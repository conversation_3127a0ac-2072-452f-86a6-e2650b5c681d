#FROM openjdk:8-jre
FROM registry.cn-hangzhou.aliyuncs.com/hyszcm/openjdk:8-jdk-alpine
#FROM alpine:latest
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories  \
    && apk add --update ttf-dejavu fontconfig  \
    && rm -rf /var/cache/apk/*  \
    && mkfontscale && mkfontdir && fc-cache
MAINTAINER Jin

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone

ARG PROJECT_DIR
COPY ${PROJECT_DIR}/target/kxss-pos-server.jar /app.jar

ENV JAVA_OPTS="-javaagent:/usr/local/skywalking-agent/skywalking-agent.jar"


ENTRYPOINT ["sh", "-c","java -Xmx2048m ${JAVA_OPTS} -Djava.security.egd=file:/dev/./urandom -Ddruid.mysql.usePingMethod=false -jar /app.jar"]
CMD ["--spring.profiles.active=dev"]
