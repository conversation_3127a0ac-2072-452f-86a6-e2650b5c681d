lamp:
  swagger:
    version: '@project.version@'
  nacos:
    ip: ${NACOS_IP:@nacos.ip@}
    port: ${NACOS_PORT:@nacos.port@}
    namespace: ${NACOS_NAMESPACE:@nacos.namespace@}
    username: ${NACOS_USERNAME:@nacos.username@}
    password: ${NACOS_PASSWORD:@nacos.password@}
  sentinel:
    dashboard: ${SENTINEL_DASHBOARD:@sentinel.dashboard@}
  seata:
    ip: ${SEATA_IP:@seata.ip@}
    port: ${SEATA_PORT:@seata.port@}
    namespace: ${SEATA_NAMESPACE:@seata.namespace@}
  liteflow:
    namespace: ${NACOS_LITEFLOW_NAMESPACE:@nacos.liteflow.namespace@}

spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: @project.artifactId@
    # 此参数一定要和 lamp-gateway-server.yml 文件中配置的 路由前缀(predicates参数) 一致！ （可以参考 system或base 服务）
    path: /pos
  profiles:
    active: @profile.active@
  cloud:
    sentinel:
      enabled: true
      filter:
        enabled: true
      eager: true  # 取消Sentinel控制台懒加载
      transport:
        dashboard: ${lamp.sentinel.dashboard}
    nacos:
      config:
        server-addr: ${lamp.nacos.ip}:${lamp.nacos.port}
        file-extension: yml
        namespace: ${lamp.nacos.namespace}
        shared-configs:
          - dataId: common.yml
            refresh: true
          - dataId: redis.yml
            refresh: false
          - dataId: database.yml
            refresh: true
          - dataId: rabbitmq.yml
            refresh: false
        enabled: true
        username: ${lamp.nacos.username}
        password: ${lamp.nacos.password}
      discovery:
        username: ${lamp.nacos.username}
        password: ${lamp.nacos.password}
        server-addr: ${lamp.nacos.ip}:${lamp.nacos.port}
        namespace: ${lamp.nacos.namespace}
        metadata: # 元数据，用于权限服务实时获取各个服务的所有接口
          management.context-path: ${server.servlet.context-path:}${spring.mvc.servlet.path:}${management.endpoints.web.base-path:}
          gray_version: ${gray_version:@gray_version@}

logging:
  file:
    path: '@logging.file.path@'
    name: ${logging.file.path}/${spring.application.name}/root.log
  config: classpath:logback-spring.xml
  level:
    druid.sql.Statement: debug

# 用于/actuator/info
info:
  name: '@project.name@'
  description: '@project.description@'
  version: '@project.version@'

liteflow:
  rule-source-ext-data-map:
    dataId: liteflow.xml
    group: DEFAULT_GROUP
    serverAddr: ${lamp.nacos.ip}:${lamp.nacos.port}
    namespace: ${lamp.nacos.namespace}
    username: ${lamp.nacos.username}
    password: ${lamp.nacos.password}
