<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 本地开发时，在bootstrap-xxx.yml中通过 logging.config=classpath:logback-spring-dev.xml 文件，表示本地的日志实时打印出来 -->
    <!-- defaults-biz-dev.xml 文件位于kxss-cloud或kxss-boot 的 kxss-public/kxss-common/src/main/resources -->
    <include resource="defaults-biz-dev.xml"/>


    <logger name="top.kx.kxss.pos.controller" additivity="true" level="${log.level.controller}">
        <appender-ref ref="CONTROLLER_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.pos.service" additivity="true" level="${log.level.service}">
        <appender-ref ref="SERVICE_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.pos.manager" additivity="true" level="${log.level.service}">
        <appender-ref ref="SERVICE_APPENDER"/>
    </logger>

    <!-- 可以在这里自定义规则 -->
</configuration>
