package top.kx.kxss.pos.service.shiftHandover;

import com.baomidou.mybatisplus.core.metadata.IPage;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.base.vo.query.shiftHandover.BaseShiftHandoverPageQuery;
import top.kx.kxss.base.vo.result.product.HandoverStockResultVO;
import top.kx.kxss.base.vo.result.shiftHandover.BaseShiftHandoverResultVO;
import top.kx.kxss.pos.vo.result.shiftHandover.ShiftHandoverResultVO;
import top.kx.kxss.pos.vo.shiftHandover.PaymentDetailVO;
import top.kx.kxss.pos.vo.shiftHandover.StatisticsVO;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 业务接口
 * 交班信息
 * </p>
 *
 * <AUTHOR>
 */
public interface ShiftHandoverService {

    BaseShiftHandoverResultVO info();

    List<PaymentDetailVO> detail(Long shiftHandoverId);


    List<StatisticsVO> statistics(Long shiftHandoverId);

    BaseShiftHandoverResultVO confirm();

    BaseShiftHandoverResultVO save(BigDecimal pettyCash);

    IPage<BaseShiftHandoverResultVO> page(PageParams<BaseShiftHandoverPageQuery> params);

    Map<String, Object> detailPage(PageParams<BaseShiftHandoverPageQuery> params);


    void detailPageExport(BaseShiftHandoverPageQuery params, HttpServletResponse response);


    List<BaseShiftHandoverResultVO> listByEmployee(DataOverviewQuery params);


    ShiftHandoverResultVO handoverDetails(Long handoverId);


    ShiftHandoverResultVO handoverPrint(Long handoverId);

    /**
     * 打印交班库存
     * @param params
     */
    Boolean printHandoverProductStock(HandoverStockResultVO params);

    Boolean checkOpenShiftHandover();

}


