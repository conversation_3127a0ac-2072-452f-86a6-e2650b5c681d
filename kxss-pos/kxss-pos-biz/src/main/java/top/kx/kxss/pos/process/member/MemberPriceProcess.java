package top.kx.kxss.pos.process.member;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.entity.thail.PosCashThail;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.vo.result.product.BaseProductResultVO;
import top.kx.kxss.base.vo.result.thail.BaseThailResultVO;
import top.kx.kxss.model.enumeration.base.ReformPriceTypeEnum;
import top.kx.kxss.model.enumeration.pos.DiscountTypeEnum;
import top.kx.kxss.model.enumeration.pos.ExtTypeEnum;
import top.kx.kxss.pos.bean.PriceCalcStepVO;
import top.kx.kxss.pos.slot.DetailCalcContext;
import top.kx.kxss.pos.slot.PriceCalcContext;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Map;

/**
 * 商品会员价计算组件
 *
 * <AUTHOR>
 */
@Component("memberPriceProcess")
@Slf4j
public class MemberPriceProcess extends NodeComponent {

    @Autowired
    private MemberInfoService memberInfoService;

    @Override
    public void process() throws Exception {
        PriceCalcContext context = this.getContextBean(PriceCalcContext.class);
        DetailCalcContext detailContext = this.getContextBean(DetailCalcContext.class);
        BigDecimal discountAmount = BigDecimal.ZERO;
        StringBuilder sb = new StringBuilder();
        //商品是否使用会员价
        if (ObjectUtil.isNotNull(context.getMemberGradeResultVO())
                && context.getMemberGradeResultVO().getIsProductMemberPrice() && CollUtil.isNotEmpty(detailContext.getProductList())) {
            Map<Long, BaseProductResultVO> productMap = detailContext.getProductMap();
            for (PosCashProduct cashProduct : detailContext.getProductList()) {
//                if (cashProduct.getIsDiscount() != null && !cashProduct.getIsDiscount()) {
//                    continue;
//                }
                if (cashProduct.getIsMemberPrice() != null && !cashProduct.getIsMemberPrice()) {
                    continue;
                }
                if (!cashProduct.getReformPriceType().equals(ReformPriceTypeEnum.NO.getCode())) {
                    continue;
                }
                BigDecimal amount = cashProduct.getAmount().subtract(cashProduct.getAssessedAmount());
                if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                if (ObjectUtil.equal(cashProduct.getDiscountType(), DiscountTypeEnum.ORIGINAL.getCode())
                        || ObjectUtil.equal(cashProduct.getDiscountType(), DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode())) {

                    if (CollUtil.isEmpty(productMap)) {
                        continue;
                    }
                    BaseProductResultVO baseProduct = productMap.get(cashProduct.getProductId());
                    if (ObjectUtil.isNull(baseProduct)) {
                        continue;
                    }
//                    //单价
//                    cashProduct.setPrice(baseProduct.getRetailPrice());
//                    //原价
//                    cashProduct.setOrginPrice(baseProduct.getRetailPrice()
//                            .multiply(new BigDecimal(cashProduct.getNum())).setScale(2, RoundingMode.HALF_UP));
                    if (cashProduct.getPrice().compareTo(baseProduct.getMemberPrice()) < 0) {
                        continue;
                    }
                    //支付价
                    cashProduct.setPrice(baseProduct.getMemberPrice());
                    cashProduct.setAmount(baseProduct.getMemberPrice()
                            .multiply(new BigDecimal(cashProduct.getNum())).setScale(2, RoundingMode.HALF_UP));
                    //优惠金额
                    cashProduct.setDiscountAmount(cashProduct.getOrginPrice().subtract(cashProduct.getAmount())
                            .setScale(2, RoundingMode.HALF_UP));
                    //是否赠送
                    cashProduct.setIsGift(cashProduct.getAmount().compareTo(BigDecimal.ZERO) == 0);
                    //优惠减免
                    cashProduct.setDiscount(cashProduct.getDiscountAmount().multiply(BigDecimal.TEN));
                    cashProduct.setDiscountType(DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode());
                    discountAmount = discountAmount.add(cashProduct.getDiscountAmount());
                    sb.append(StrUtil.isNotBlank(sb.toString()) ? "、" + baseProduct.getName() : baseProduct.getName());
                }
            }
        }

        //加入到价格步骤中
        if (discountAmount.compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal prePrice = context.getLastestPriceStep().getCurrPrice();
            BigDecimal currPrice = prePrice.subtract(discountAmount);
            context.addPriceCalcStep(PriceCalcStepVO.builder()
                    .extId(context.getMemberInfo().getId()).currPrice(currPrice)
                    .extType(ExtTypeEnum.MEMBER.getCode())
                    .prePrice(prePrice).priceType(DiscountTypeEnum.MEMBER_PRICE_DISCOUNT)
                    .priceChange(currPrice.subtract(prePrice))
                    .stepDesc(DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getDesc() + "【" + sb + "】")
                    .build());
        }
        //套餐是否使用会员价
        discountAmount = BigDecimal.ZERO;
        sb = new StringBuilder();
        if (ObjectUtil.isNotNull(context.getMemberGradeResultVO())
                && context.getMemberGradeResultVO().getIsThailMemberPrice() && CollUtil.isNotEmpty(detailContext.getThailList())) {
            Map<Long, BaseThailResultVO> productMap = detailContext.getBaseThailMap();
            for (PosCashThail cashThail : detailContext.getThailList()) {
//                if (cashThail.getIsDiscount() != null && !cashThail.getIsDiscount()) {
//                    continue;
//                }
                //不参与会员价
                if (cashThail.getIsMemberPrice() != null && !cashThail.getIsMemberPrice()) {
                    continue;
                }
                if (cashThail.getIsCheckSecurities() != null && cashThail.getIsCheckSecurities()) {
                    continue;
                }
                if (!cashThail.getReformPriceType().equals(ReformPriceTypeEnum.NO.getCode())) {
                    continue;
                }
                BigDecimal amount = cashThail.getAmount().subtract(cashThail.getAssessedAmount());
                if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                if (ObjectUtil.equal(cashThail.getDiscountType(), DiscountTypeEnum.ORIGINAL.getCode())
                        || ObjectUtil.equal(cashThail.getDiscountType(), DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode())) {
                    BaseThailResultVO baseThail = productMap.get(cashThail.getThailId());
                    //支付价
                    cashThail.setAmount(baseThail.getMemberPrice().setScale(2, RoundingMode.HALF_UP));
                    //优惠金额
                    cashThail.setDiscountAmount(cashThail.getOrginPrice().subtract(cashThail.getAmount())
                            .setScale(2, RoundingMode.HALF_UP));
                    //优惠减免
                    cashThail.setDiscount(cashThail.getDiscountAmount().multiply(BigDecimal.TEN));
                    cashThail.setDiscountType(DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode());
                    discountAmount = discountAmount.add(cashThail.getDiscountAmount());
                    sb.append(StrUtil.isNotBlank(sb.toString()) ? "、" + baseThail.getName() : baseThail.getName());
                }
            }
        }
        //加入到价格步骤中
        if (discountAmount.compareTo(BigDecimal.ZERO) != 0) {
            BigDecimal prePrice = context.getLastestPriceStep().getCurrPrice();
            BigDecimal currPrice = prePrice.subtract(discountAmount);
            context.addPriceCalcStep(PriceCalcStepVO.builder()
                    .extId(context.getMemberInfo().getId()).currPrice(currPrice)
                    .extType(ExtTypeEnum.MEMBER.getCode())
                    .prePrice(prePrice).priceType(DiscountTypeEnum.MEMBER_PRICE_DISCOUNT)
                    .priceChange(currPrice.subtract(prePrice))
                    .stepDesc(DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getDesc() + "【" + sb + "】")
                    .build());
        }
    }

    @Override
    public boolean isAccess() {
        PriceCalcContext context = this.getContextBean(PriceCalcContext.class);
        //这一步必须有，否则线程池中无法确定数据源
        context.setContextUtil(context);
        if (!context.getPosCash().getIsMemberDiscount()) {
            return false;
        }
        return ObjectUtil.isNotNull(context.getMemberId())
                && !memberInfoService.isExpire(context.getMemberInfo());
    }
}
