package top.kx.kxss.pos.process.sync;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.member.grade.MemberGrade;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.service.member.grade.MemberGradeService;
import top.kx.kxss.base.vo.result.member.grade.MemberGradeResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.pos.bean.PriceCalcQuery;
import top.kx.kxss.pos.slot.DetailCalcContext;
import top.kx.kxss.pos.slot.PriceCalcContext;

/**
 * 会员组件
 *
 * <AUTHOR>
 */
@Component("memberInfoProcess")
@Slf4j
@DS(DsConstant.BASE_TENANT)
public class MemberInfoProcess extends NodeComponent {

    @Autowired
    private MemberGradeService memberGradeService;
    @Autowired
    private MemberInfoService memberInfoService;

    @Override
    public void process() throws Exception {
        PriceCalcContext context = this.getContextBean(PriceCalcContext.class);
        DetailCalcContext detailCalcContext = this.getContextBean(DetailCalcContext.class);
        PriceCalcQuery query = this.getSlot().getRequestData();
        //查询会员信息
        MemberInfo memberInfo = memberInfoService.getMemberById(query.getMemberId());
        ArgumentAssert.notNull(memberInfo, "会员不存在");
        context.setMemberInfo(memberInfo);
        context.setMemberId(memberInfo.getId());
        detailCalcContext.setMemberInfo(memberInfo);
        //会员等级信息
        if (memberInfo.getGradeId() != null) {
            MemberGrade memberGrade = memberGradeService.getGradeById(memberInfo.getGradeId());
            ArgumentAssert.notNull(memberGrade, "会员等级不存在！");
            MemberGradeResultVO memberGradeResultVO = BeanUtil.copyProperties(memberGrade, MemberGradeResultVO.class);
            context.setMemberGradeResultVO(memberGradeResultVO);
            detailCalcContext.setMemberGradeResultVO(memberGradeResultVO);
        }
    }

    @Override
    public boolean isAccess() {
        PriceCalcContext context = this.getContextBean(PriceCalcContext.class);
        //这一步必须有，否则线程池中无法确定数据源
        context.setContextUtil(context);
        PriceCalcQuery query = this.getSlot().getRequestData();
        return ObjectUtil.isNotNull(query.getMemberId());
    }
}
