
package top.kx.kxss.pos.logical;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.R;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.cache.redis2.CacheResult;
import top.kx.basic.cache.repository.CacheOps;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.exception.BizException;
import top.kx.basic.exception.code.ExceptionCode;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.jackson.JsonUtil;
import top.kx.basic.model.cache.CacheKey;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.app.entity.cash.OpeningTableSaveVO;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.entity.thail.PosCashThail;
import top.kx.kxss.app.query.BizCacheConsumeQuery;
import top.kx.kxss.app.query.OrderAutoCancelConsumeQuery;
import top.kx.kxss.app.service.bizcache.BizCacheService;
import top.kx.kxss.app.service.cash.PosCashOperateVerifyService;
import top.kx.kxss.app.service.cash.PosCashServiceService;
import top.kx.kxss.app.service.cash.discount.PosCashDiscountDetailService;
import top.kx.kxss.app.service.cash.payment.PosCashPaymentService;
import top.kx.kxss.app.service.cash.product.PosCashProductService;
import top.kx.kxss.app.service.cash.table.PosCashTableService;
import top.kx.kxss.app.service.thail.PosCashThailService;
import top.kx.kxss.app.statemachine.PosCashStateManager;
import top.kx.kxss.app.vo.query.cash.KitchenPrintQuery;
import top.kx.kxss.base.entity.attribute.BaseAttribute;
import top.kx.kxss.base.entity.attribute.BaseAttributeSetting;
import top.kx.kxss.base.entity.biz.BaseBizLog;
import top.kx.kxss.base.entity.card.BaseCard;
import top.kx.kxss.base.entity.job.BaseJobInfo;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.outin.BaseOutin;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.service.BaseService;
import top.kx.kxss.base.entity.service.BaseServicePersonal;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.entity.warehouse.BaseWarehouse;
import top.kx.kxss.base.manager.service.BaseServicePersonalManager;
import top.kx.kxss.base.service.attribute.BaseAttributeService;
import top.kx.kxss.base.service.attribute.BaseAttributeSettingService;
import top.kx.kxss.base.service.biz.BaseBizAvailableTimeService;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.service.biz.equity.BaseBizEquityService;
import top.kx.kxss.base.service.card.BaseCardService;
import top.kx.kxss.base.service.job.BaseJobInfoService;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.service.outin.BaseOutinService;
import top.kx.kxss.base.service.payment.BasePaymentTypeService;
import top.kx.kxss.base.service.product.BaseProductService;
import top.kx.kxss.base.service.service.BaseServiceService;
import top.kx.kxss.base.service.table.BaseTableInfoService;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.service.warehouse.BaseWarehouseService;
import top.kx.kxss.base.vo.result.service.activity.BaseServiceActivityResultVO;
import top.kx.kxss.base.vo.save.outin.BaseOutinProductSaveVO;
import top.kx.kxss.base.vo.save.outin.BaseOutinStockSaveVO;
import top.kx.kxss.common.api.HelperApi;
import top.kx.kxss.common.constant.*;
import top.kx.kxss.model.constant.EchoDictType;
import top.kx.kxss.model.enumeration.BizCacheEnum;
import top.kx.kxss.model.enumeration.app.CashTableStatusEnum;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.model.enumeration.base.*;
import top.kx.kxss.model.enumeration.pos.DiscountTypeEnum;
import top.kx.kxss.model.enumeration.pos.JobTypeEnum;
import top.kx.kxss.model.enumeration.pos.LogicalType;
import top.kx.kxss.pos.bean.PriceCalcQuery;
import top.kx.kxss.pos.entity.cash.PosCashCard;
import top.kx.kxss.pos.entity.cash.PosCashProductAttribute;
import top.kx.kxss.pos.query.QueryDetailQuery;
import top.kx.kxss.pos.query.card.BindCardQuery;
import top.kx.kxss.pos.query.coupon.BindCouponQuery;
import top.kx.kxss.pos.query.member.BindMemberQuery;
import top.kx.kxss.pos.query.member.MemberVerifyQuery;
import top.kx.kxss.pos.query.order.DelOrEmptyOrderQuery;
import top.kx.kxss.pos.query.order.PosCashCreateQuery;
import top.kx.kxss.pos.query.order.ShopNumQuery;
import top.kx.kxss.pos.query.product.*;
import top.kx.kxss.pos.service.CalcPriceService;
import top.kx.kxss.pos.service.cash.PosCashCardService;
import top.kx.kxss.pos.service.cash.PosCashProductAttributeService;
import top.kx.kxss.pos.service.cash.PosCashTempService;
import top.kx.kxss.pos.service.noticedevice.WsNoticeDeviceService;
import top.kx.kxss.pos.service.order.CheckOrderService;
import top.kx.kxss.pos.service.order.impl.OrderEquityRemoveService;
import top.kx.kxss.pos.service.pay.PayService;
import top.kx.kxss.pos.service.table.TableOperateService;
import top.kx.kxss.pos.slot.*;
import top.kx.kxss.pos.vo.CashDetailResultVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 开台购物逻辑处理
 *
 * <AUTHOR>
 */
@Slf4j
@DS(DsConstant.BASE_TENANT)
public abstract class AbstractOperateLogical implements OperateLogical {
    @Autowired
    protected DistributedLock distributedLock;
    @Autowired
    protected FlowExecutor flowExecutor;
    @Autowired
    protected BaseProductService productService;
    @Autowired
    protected BaseProductService baseProductManager;
    @Autowired
    protected PosCashProductService posCashProductManager;
    @Autowired
    protected PosCashThailService posCashThailManager;
    @Autowired
    protected BaseServiceService baseServiceService;
    @Autowired
    protected MemberInfoService memberInfoManager;
    @Autowired
    protected PosCashServiceService posCashManager;
    @Autowired
    protected CheckOrderService checkOrderService;
    @Autowired
    protected PosCashTableService posCashTableManager;
    @Autowired
    protected PosCashDiscountDetailService posCashDiscountDetailManager;
    @Autowired
    protected top.kx.kxss.app.service.cash.service.PosCashServiceService posCashServiceManager;
    @Autowired
    protected PosCashStateManager cashStateManager;
    @Autowired
    protected CalcPriceService calcPriceService;
    @Autowired
    protected BaseServicePersonalManager servicePersonalManager;
    @Autowired
    protected BaseBizLogService baseBizLogManager;
    @Autowired
    protected BaseEmployeeService baseEmployeeManager;
    @Autowired
    protected PosCashOperateVerifyService posCashOperateVerifyService;
    @Autowired
    protected EchoService echoService;
    @Autowired
    protected BaseOutinService baseOutinService;
    @Autowired
    protected BaseCardService baseCardService;
    @Autowired
    protected PosCashCardService posCashCardService;
    @Autowired
    protected RabbitTemplate template;
    @Autowired
    protected BaseJobInfoService baseJobInfoService;
    @Autowired
    protected WsNoticeDeviceService wsNoticeDeviceService;
    @Autowired
    protected PosCashPaymentService posCashPaymentService;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private BizCacheService bizCacheService;
    @Autowired
    private BaseBizEquityService baseBizEquityService;
    @Autowired
    private BaseBizAvailableTimeService availableTimeService;
    @Autowired
    protected CacheOps cacheOps;
    @Autowired
    private BaseWarehouseService baseWarehouseService;
    @Autowired
    private HelperApi helperApi;
    @Autowired
    private BaseAttributeSettingService baseAttributeSettingService;
    @Autowired
    private BaseAttributeService baseAttributeService;
    @Autowired
    private PosCashProductAttributeService posCashProductAttributeService;
    @Autowired
    private TableOperateService tableOperateService;
    @Autowired
    protected BaseTableInfoService tableInfoService;
    @Autowired
    protected PosCashTempService posCashTempService;
    @Autowired
    protected PayService payService;
    @Autowired
    protected BasePaymentTypeService basePaymentTypeService;
    @Autowired
    protected OrderEquityRemoveService orderEquityRemoveService;


    /**
     * 商品数量变换
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long productChangeNum(ChangeNumQuery query) {
        boolean lock = false;
        String printObjected = IdUtil.objectId();
        String refundObjected = IdUtil.objectId();
        try {
            PosCashCreateQuery cashQuery = BeanUtil.copyProperties(query, PosCashCreateQuery.class);
            PosCash posCash = createPosCash(cashQuery);
            ArgumentAssert.notNull(posCash, "订单不存在");
            query.setPosCashId(posCash.getId());
            lock = distributedLock.lock(query.getPosCashId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            checkOrderService.checkItemPosCash(posCash);
            checkOrderService.checkOrderDiscountPosCash(posCash);
            // 选择仓库
            BaseWarehouse baseWarehouse = baseWarehouseService.getOneBySn();
            ArgumentAssert.notNull(baseWarehouse, "请设置销售仓库");
            BaseProduct baseProduct = productService.getById(query.getBizId());
            ArgumentAssert.notNull(baseProduct, "商品不存在");
            PosCashProduct posCashProduct = null;
            String numDesc = "1";
            List<BaseAttributeSetting> baseAttributeSettingList = new ArrayList<>();
            if (ObjectUtil.isNotNull(query.getAttributeSettingList())) {
                baseAttributeSettingList = baseAttributeSettingService.listByIds(query.getAttributeSettingList());
            }
            List<Long> printCashProductIds = new ArrayList<>();
            List<Long> refundCashProductIds = new ArrayList<>();
            switch (query.getType()) {
                case 1:
                    ArgumentAssert.isFalse(baseProduct.getIsAttribute() && CollUtil.isEmpty(query.getAttributeSettingList()), "请选择商品属性");
                    posCashProduct = posCashProductManager.getOne(Wraps.<PosCashProduct>lbQ()
                            .eq(PosCashProduct::getCashId, query.getPosCashId())
                            .isNull(PosCashProduct::getCashThailId)
                            .and(wrapper -> wrapper.isNull(PosCashProduct::getIsMerge)
                                    .or().eq(PosCashProduct::getIsMerge, 0))
                            .eq(PosCashProduct::getProductId, query.getBizId())
                            .eq(PosCashProduct::getId, query.getId())
                            .eq(PosCashProduct::getWarehouseId, baseWarehouse.getId())
                            .orderByDesc(PosCashProduct::getCreatedTime)
                            .last("limit 1"));
                    if (ObjectUtil.isNotNull(posCashProduct)) {
                        LocalDateTime createdTime = posCashProduct.getCreatedTime().plusMinutes(2);
                        if (ObjectUtil.isNull(query.getId()) && LocalDateTime.now().isAfter(createdTime)) {
                            posCashProduct = null;
                        }
                    }
                    if (posCashProduct != null && posCashProduct.getIsKnot() != null && posCashProduct.getIsKnot()) {
                        posCashProduct = null;
                    }
                    if (posCashProduct != null && posCashProduct.getIsGift() != null
                            && posCashProduct.getIsGift()) {
                        posCashProduct = null;
                    }
                    if (posCashProduct != null && posCashProduct.getDiscountType() != null
                            && !posCashProduct.getDiscountType().equals(DiscountTypeEnum.ORIGINAL.getCode())
                            && !posCashProduct.getDiscountType().equals(DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())
                            && !posCashProduct.getDiscountType().equals(DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode())
                    ) {
                        posCashProduct = null;
                    }
                    if (ObjectUtil.isNotNull(posCashProduct) && ObjectUtil.isNull(query.getId())) {
                        if (StrUtil.isNotBlank(posCashProduct.getReformPriceType())
                                && !posCashProduct.getReformPriceType().equals(ReformPriceTypeEnum.NO.getCode())) {
                            posCashProduct = null;
                        }
                        if (posCashProduct != null && posCashProduct.getIsKnot() != null && posCashProduct.getIsKnot()) {
                            posCashProduct = null;
                        }
                    }
                    // 有后厨打印, 则需要拆一条单
                    if (baseProduct.getIsKitchenPrint()) {
                        posCashProduct = null;
                    }
                    // 如果属性不同, 拆多条记录
                    if (ObjectUtil.isNotNull(posCashProduct) && StringUtils.isBlank(posCashProduct.getAttributeSetting()) && ObjectUtil.isNotNull(query.getAttributeSettingList())) {
                        posCashProduct = null;
                    }
                    if (ObjectUtil.isNotNull(posCashProduct) && StringUtils.isNotBlank(posCashProduct.getAttributeSetting()) && ObjectUtil.isNull(query.getAttributeSettingList())) {
                        posCashProduct = null;
                    }
                    if (ObjectUtil.isNotNull(posCashProduct) && StringUtils.isNotBlank(posCashProduct.getAttributeSetting()) && ObjectUtil.isNotNull(query.getAttributeSettingList())) {
                        List<Long> attributeSettingList = Arrays.stream(posCashProduct.getAttributeSetting().split(",")).map(Long::valueOf).sorted().collect(Collectors.toList());
                        List<Long> settingList = query.getAttributeSettingList().stream().sorted().collect(Collectors.toList());
                        if (!attributeSettingList.equals(settingList)) {
                            posCashProduct = null;
                        }
                    }
                    //新增商品数量
                    if (ObjectUtil.isNull(posCashProduct)) {
                        posCashProduct = new PosCashProduct();
                        posCashProduct.setCashId(posCash.getId());
                        posCashProduct.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                        posCashProduct.setNum(0);
                        posCashProduct.setPrice(baseProduct.getRetailPrice());
                        posCashProduct.setDiscount(BigDecimal.ZERO);
                        posCashProduct.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                        posCashProduct.setWarehouseId(baseWarehouse.getId());
                        if (CollUtil.isNotEmpty(baseAttributeSettingList)) {
                            posCashProduct.setAttributeSetting(StrUtil.join(",", query.getAttributeSettingList()));
                            posCashProduct.setAttributeSettingDesc(baseAttributeSettingList.stream().map(BaseAttributeSetting::getName).collect(Collectors.joining(",")));
                        }
                        if (StringUtils.isNotBlank(query.getRemarks()) || CollUtil.isNotEmpty(query.getTags())) {
                            posCashProduct.setRemarks(tableOperateService.setRemarks(query.getTags(), query.getRemarks(),
                                    posCashProduct.getRemarks(), EchoDictType.App.ITEM_TAGS));
                        }
                        // 设置属性金额
                        posCashProductAttributeService.setAttributeAmount(baseAttributeSettingList, posCashProduct);
                    }
                    numDesc = "+" + query.getNum();
                    productChange(baseProduct, posCashProduct, query.getNum(), posCash);

                    // 后厨打印
                    if (baseProduct.getIsKitchenPrint()) {
                        printCashProductIds.add(posCashProduct.getId());
                    }
                    break;
                case 2:
                    posCashProduct = posCashProductManager.getOne(Wraps.<PosCashProduct>lbQ()
                            .eq(PosCashProduct::getCashId, query.getPosCashId())
                            .isNull(PosCashProduct::getCashThailId)
                            .eq(PosCashProduct::getProductId, query.getBizId())
                            .eq(PosCashProduct::getId, query.getId())
                            .orderByDesc(PosCashProduct::getCreatedTime)
                            .last("limit 1"));
                    //减少商品数量
                    ArgumentAssert.notNull(posCashProduct, "请刷新后重试～");
                    // 后厨打印, 需要出退的单子
                    if (baseProduct.getIsKitchenPrint()) {
                        refundCashProductIds.add(posCashProduct.getId());
                    }
                    LocalDateTime createdTime = posCashProduct.getCreatedTime().plusMinutes(2);
                    if (LocalDateTime.now().isAfter(createdTime)) {
                        if (Arrays.asList(PosCashBillTypeEnum.COUNTER_CHECKOUT.getCode(),
                                        PosCashBillTypeEnum.REGULAR_SINGLE.getCode(), PosCashBillTypeEnum.REGULAR_SINGLE.getCode())
                                .contains(posCash.getBillType())
                                && Arrays.asList(PosCashBillStateEnum.NO_PAY.getCode(),
                                        PosCashBillStateEnum.NO_SETTLED.getCode())
                                .contains(posCash.getBillState())
                        ) {
                            ArgumentAssert.isFalse(ObjectUtil.equal(1, 1),
                                    "请刷新后重试～");
                        }
                    }
                    numDesc = "-" + query.getNum();

                    productChange(baseProduct, posCashProduct, -query.getNum(), posCash);
                    break;
                case 3:
                    // 有后厨打印, 不允许修改数量
                    ArgumentAssert.isFalse(baseProduct.getIsKitchenPrint(), "商品配置了后厨打印,不允许修改");
                    posCashProduct = posCashProductManager.getOne(Wraps.<PosCashProduct>lbQ()
                            .eq(PosCashProduct::getCashId, query.getPosCashId())
                            .isNull(PosCashProduct::getCashThailId)
                            .eq(PosCashProduct::getProductId, query.getBizId())
                            .eq(PosCashProduct::getId, query.getId())
                            .orderByDesc(PosCashProduct::getCreatedTime)
                            .last("limit 1"));
                    //直接设置商品数量
                    ArgumentAssert.notNull(posCashProduct, "请刷新后重试");
                    createdTime = posCashProduct.getCreatedTime().plusMinutes(2);
                    if (LocalDateTime.now().isAfter(createdTime)) {
                        if (Arrays.asList(PosCashBillTypeEnum.COUNTER_CHECKOUT.getCode(),
                                        PosCashBillTypeEnum.REGULAR_SINGLE.getCode(), PosCashBillTypeEnum.REGULAR_SINGLE.getCode())
                                .contains(posCash.getBillType())
                                && Arrays.asList(PosCashBillStateEnum.NO_PAY.getCode(),
                                        PosCashBillStateEnum.NO_SETTLED.getCode())
                                .contains(posCash.getBillState())
                        ) {
                            ArgumentAssert.isFalse(ObjectUtil.equal(1, 1),
                                    "请刷新后重试～");
                        }
                    }
                    int i = query.getNum() - posCashProduct.getNum();
                    // 判断是不是相同的仓库, 相同仓库, 增加和减少都是原仓库
                    if (ObjectUtil.equal(posCashProduct.getWarehouseId(), baseWarehouse.getId())) {
                        numDesc = (i > 0 ? "+" : "") + i;
                        productChange(baseProduct, posCashProduct, query.getNum() - posCashProduct.getNum(), posCash);
                        break;
                    }
                    // 不同的仓库,减少 减少原仓库, 增加的时候, 增加一条新的记录
                    if (i > 0) {
                        ArgumentAssert.isFalse(baseProduct.getIsAttribute() && CollUtil.isEmpty(query.getAttributeSettingList()), "请选择商品属性");
                        // 增加商品记录
                        posCashProduct = new PosCashProduct();
                        posCashProduct.setCashId(posCash.getId());
                        posCashProduct.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                        posCashProduct.setNum(i);
                        posCashProduct.setPrice(baseProduct.getRetailPrice());
                        posCashProduct.setDiscount(BigDecimal.ZERO);
                        posCashProduct.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                        posCashProduct.setWarehouseId(baseWarehouse.getId());
                        if (CollUtil.isNotEmpty(baseAttributeSettingList)) {
                            posCashProduct.setAttributeSetting(StrUtil.join(",", query.getAttributeSettingList()));
                            posCashProduct.setAttributeSettingDesc(baseAttributeSettingList.stream().map(BaseAttributeSetting::getName).collect(Collectors.joining(",")));
                        }
                        if (StringUtils.isNotBlank(query.getRemarks()) || CollUtil.isNotEmpty(query.getTags())) {
                            posCashProduct.setRemarks(tableOperateService.setRemarks(query.getTags(), query.getRemarks(),
                                    posCashProduct.getRemarks(), EchoDictType.App.ITEM_TAGS));
                        }
                        // 设置属性金额
                        posCashProductAttributeService.setAttributeAmount(baseAttributeSettingList, posCashProduct);
                        numDesc = "+" + i;
                        productChange(baseProduct, posCashProduct, query.getNum() - posCashProduct.getNum(), posCash);
                        break;
                    }
                    // 不同仓库减少,是减少原仓库的
                    numDesc = "-" + i;
                    productChange(baseProduct, posCashProduct, query.getNum() - posCashProduct.getNum(), posCash);
                    break;
                default:
                    ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "类型不存在");
                    break;
            }
            //新增操作日志
            baseBizLogManager.createBizLog(BaseBizLog.builder()
                    .tenantId(ContextUtil.getTenantId())
                    .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                    .description("添加/减少商品【" + baseProduct.getName() + "数量" + numDesc + "】")
                    .bizModule(BizLogModuleEnum.ADD_PRODUCT.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                    .sourceId(posCash.getId()).remarks("")
                    .sn(ContextUtil.getSn())
                    .build());
            assert posCashProduct != null;

            // 添加商品通知设备刷新订单
            if (ObjectUtil.isNotNull(posCash.getTableId())) {
                wsNoticeDeviceService.sendRefreshMsg(posCash.getTableId(), false, false);
            }

            if (CollUtil.isNotEmpty(printCashProductIds)) {
                CacheKey cacheKey = new CacheKey(RedisConstant.TENANT_PRINT_KITCHEN + ContextUtil.getTenantId() + printObjected);
                cacheKey.setExpire(Duration.ofMinutes(2));
                cacheOps.set(cacheKey, printObjected);
                template.convertAndSend(RabbitMqConstant.PRINT_KITCHEN_EXCHANGE, RabbitMqConstant.PRINT_KITCHEN_DEAD_ROUTING_KEY,
                        JSON.toJSONString(KitchenPrintQuery.builder()
                                .posCashId(posCash.getId())
                                .cashProductIds(printCashProductIds)
                                .isIntact(false)
                                .type("1")
                                .tenantId(ContextUtil.getTenantId())
                                .orgId(ContextUtil.getCurrentCompanyId())
                                .employeeId(ContextUtil.getEmployeeId())
                                .sn(ContextUtil.getSn())
                                .mqObjectId(printObjected)
                                .build()), message -> {
                            //延迟3秒钟
                            message.getMessageProperties().setExpiration((2 * 1000) + "");
                            return message;
                        });
//                template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.KITCHEN_PRINT,
//                        JsonUtil.toJson(KitchenPrintQuery.builder()
//                                .posCashId(posCash.getId())
//                                .cashProductIds(printCashProductIds)
//                                .isIntact(false)
//                                .type("1")
//                                .tenantId(ContextUtil.getTenantId())
//                                .orgId(ContextUtil.getCurrentCompanyId())
//                                .employeeId(ContextUtil.getEmployeeId())
//                                .mqObjectId(printObjected)
//                                .build()));
            }
            if (CollUtil.isNotEmpty(refundCashProductIds)) {
                CacheKey cacheKey = new CacheKey(RedisConstant.TENANT_PRINT_KITCHEN + ContextUtil.getTenantId() + refundObjected);
                cacheKey.setExpire(Duration.ofMinutes(2));
                cacheOps.set(cacheKey, refundObjected);
                template.convertAndSend(RabbitMqConstant.PRINT_KITCHEN_EXCHANGE, RabbitMqConstant.PRINT_KITCHEN_DEAD_ROUTING_KEY,
                        JSON.toJSONString(KitchenPrintQuery.builder()
                                .posCashId(posCash.getId())
                                .cashProductIds(refundCashProductIds)
                                .isIntact(false)
                                .type("2")
                                .tenantId(ContextUtil.getTenantId())
                                .orgId(ContextUtil.getCurrentCompanyId())
                                .employeeId(ContextUtil.getEmployeeId())
                                .sn(ContextUtil.getSn())
                                .mqObjectId(refundObjected)
                                .build()), message -> {
                            //延迟3秒钟
                            message.getMessageProperties().setExpiration((3 * 1000) + "");
                            return message;
                        });
//                template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.KITCHEN_PRINT,
//                        JsonUtil.toJson(KitchenPrintQuery.builder()
//                                .posCashId(posCash.getId())
//                                .cashProductIds(refundCashProductIds)
//                                .isIntact(false)
//                                .type("2")
//                                .tenantId(ContextUtil.getTenantId())
//                                .orgId(ContextUtil.getCurrentCompanyId())
//                                .employeeId(ContextUtil.getEmployeeId())
//                                .mqObjectId(refundObjected)
//                                .build()));
            }
            addCashChangeNumCache(posCash.getId());
            return posCashProduct.getId();
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getPosCashId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode());
            }
            CacheKey printKey = new CacheKey(RedisConstant.TENANT_PRINT_KITCHEN + ContextUtil.getTenantId() + printObjected);
            CacheKey refundPrintKey = new CacheKey(RedisConstant.TENANT_PRINT_KITCHEN + ContextUtil.getTenantId() + refundObjected);
            cacheOps.del(printKey);
            cacheOps.del(refundPrintKey);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long productBatchChangeNum(BatchChangeNumQuery query) {
        boolean lock = false;
        String printObjected = IdUtil.objectId();
        try {
            PosCashCreateQuery cashQuery = BeanUtil.copyProperties(query, PosCashCreateQuery.class);
            PosCash posCash = createPosCash(cashQuery);
            ArgumentAssert.notNull(posCash, "订单不存在");
            query.setPosCashId(posCash.getId());
            log.info("批量添加商品参数：{}", JSON.toJSONString(query.getDetailList()));
            lock = distributedLock.lock(query.getPosCashId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }

            BaseWarehouse baseWarehouse = baseWarehouseService.getOneBySn();
            ArgumentAssert.notNull(baseWarehouse, "请先配置销售仓库");

            List<BatchChangeNumQuery.DetailQuery> detailList = query.getDetailList();
            Map<String, Integer> detailMap = detailList.stream().collect(Collectors.toMap(detail -> detail.getBizId() +
                    (CollUtil.isNotEmpty(detail.getAttributeSettingList()) ? "-" + detail.getAttributeSettingList().stream()
                            .map(String::valueOf).collect(Collectors.joining(",")) : ""), BatchChangeNumQuery.DetailQuery::getNum));

            Map<String, BatchChangeNumQuery.DetailQuery> detailQueryMap = detailList.stream()
                    .collect(Collectors.toMap(
                            detail -> detail.getBizId() +
                                    (CollUtil.isNotEmpty(detail.getAttributeSettingList())
                                            ? "-" + detail.getAttributeSettingList().stream()
                                            .map(String::valueOf)
                                            .collect(Collectors.joining(","))
                                            : ""),
                            detail -> detail
                    ));
            List<Long> productIds = detailList.stream().map(BatchChangeNumQuery.DetailQuery::getBizId)
                    .collect(Collectors.toList());

            List<BaseAttributeSetting> baseAttributeSettingList = new ArrayList<>();
            List<BaseAttribute> baseAttributegList = new ArrayList<>();
            List<Long> attributeSettingIds = new ArrayList<>();
            for (BatchChangeNumQuery.DetailQuery productQuery : query.getDetailList()) {
                if (CollUtil.isNotEmpty(productQuery.getAttributeSettingList())) {
                    attributeSettingIds.addAll(productQuery.getAttributeSettingList());
                }
            }
            if (CollUtil.isNotEmpty(attributeSettingIds)) {
                baseAttributeSettingList = baseAttributeSettingService.listByIds(attributeSettingIds);
                List<Long> attributeIds = baseAttributeSettingList.stream().map(BaseAttributeSetting::getAttributeId).distinct().collect(Collectors.toList());
                baseAttributegList = baseAttributeService.listByIds(attributeIds);
            }

            Map<Long, BaseProduct> productMap = baseProductManager.listByIds(productIds)
                    .stream().collect(Collectors.toMap(BaseProduct::getId, Function.identity()));
            Map<Long, PosCashProduct> cashProductMap = posCashProductManager.list(Wraps.<PosCashProduct>lbQ()
                            .eq(PosCashProduct::getCashId, query.getPosCashId())
                            .isNull(PosCashProduct::getCashThailId)
                            .and(wrapper -> wrapper.isNull(PosCashProduct::getIsMerge)
                                    .or().eq(PosCashProduct::getIsMerge, 0))
                            .in(PosCashProduct::getProductId, productIds)
                            .eq(PosCashProduct::getWarehouseId, baseWarehouse.getId())
                            .orderByDesc(PosCashProduct::getCreatedTime))
                    .stream().collect(Collectors.groupingBy(PosCashProduct::getProductId, Collectors.collectingAndThen(Collectors.toList(), list ->
                    {
                        List<PosCashProduct> cashProductList = list.stream().sorted(Comparator.comparing(PosCashProduct::getCreatedTime).reversed()).collect(Collectors.toList());
                        return cashProductList.get(0);
                    })));

            List<BaseAttributeSetting> finalBaseAttributeSettingList = baseAttributeSettingList;
            List<BaseOutinProductSaveVO> baseOutinProductSaveVOList = Lists.newArrayList();
            List<String> descriptionList = Lists.newArrayList();
            List<PosCashProduct> cashProductList = detailMap.keySet().stream().map(v -> {
                Integer num = detailMap.get(v);
                BatchChangeNumQuery.DetailQuery detailQuery = detailQueryMap.get(v);
                String[] split = v.split("-");
                Long productId = Long.valueOf(split[0]);
                List<Long> attributeIds;
                if (split.length > 1) {
                    attributeIds = Arrays.stream(split[1].split(",")).map(Long::valueOf).collect(Collectors.toList());
                } else {
                    attributeIds = new ArrayList<>();
                }
                BaseProduct baseProduct = productMap.get(productId);
                ArgumentAssert.isFalse(baseProduct.getIsAttribute() && CollUtil.isEmpty(attributeIds), "请选择商品属性");
                PosCashProduct posCashProduct = new PosCashProduct();
//                if (CollUtil.isEmpty(attributeIds)) {
//                    PosCashProduct cashProduct = cashProductMap.get(productId);
//                    if (ObjectUtil.isNotNull(cashProduct)) {
//                        LocalDateTime createdTime = cashProduct.getCreatedTime().plusMinutes(2);
//                        if (!LocalDateTime.now().isAfter(createdTime)) {
//                            posCashProduct.setId(cashProduct.getId());
//                            posCashProduct.setNum(cashProduct.getNum() + num);
//                        }
//                    }
//                }

                posCashProduct.setNum(num);
                // 如果是新增一条记录
                if (Objects.isNull(posCashProduct.getId())) {
                    posCashProduct.setSn(ContextUtil.getSn());
                    posCashProduct.setWarehouseId(baseWarehouse.getId());
                }
                posCashProduct.setCashId(posCash.getId());
                posCashProduct.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                posCashProduct.setPrice(baseProduct.getRetailPrice());
                if (CollUtil.isNotEmpty(attributeIds)) {
                    posCashProduct.setAttributeSetting(StrUtil.join(",", attributeIds));
                    if (CollUtil.isNotEmpty(finalBaseAttributeSettingList)) {
                        posCashProduct.setAttributeSettingDesc(finalBaseAttributeSettingList.stream().filter(s -> attributeIds.contains(s.getId())).map(BaseAttributeSetting::getName).collect(Collectors.joining(",")));
                    }
                    posCashProductAttributeService.setAttributeAmount(finalBaseAttributeSettingList.stream().filter(s -> attributeIds.contains(s.getId())).collect(Collectors.toList()), posCashProduct);
                }
                posCashProduct.setDiscount(BigDecimal.ZERO);
                posCashProduct.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                posCashProduct.setOrginPrice(posCashProduct.getPrice().multiply(new BigDecimal(posCashProduct.getNum()))
                        .setScale(2, RoundingMode.HALF_UP));
                posCashProduct.setProductId(baseProduct.getId());
                posCashProduct.setProductName(baseProduct.getName());
                posCashProduct.setAmount(posCashProduct.getOrginPrice());
                posCashProduct.setDiscountAmount(BigDecimal.ZERO);
                posCashProduct.setIsGift(false);
                posCashProduct.setIsDiscount(baseProduct.getDiscount());
                posCashProduct.setIsAccount(baseProduct.getIsAccount());
                posCashProduct.setAssessedAmount(BigDecimal.ZERO);
                posCashProduct.setPaid(BigDecimal.ZERO);
                if (Objects.nonNull(detailQuery)) {
                    if (StringUtils.isNotBlank(detailQuery.getRemarks()) || CollUtil.isNotEmpty(detailQuery.getTags())) {
                        posCashProduct.setRemarks(tableOperateService.setRemarks(detailQuery.getTags(), detailQuery.getRemarks(),
                                posCashProduct.getRemarks(), EchoDictType.App.ITEM_TAGS));
                    }
                }
                descriptionList.add(posCashProduct.getProductName().concat("+" + num + "、"));
                return posCashProduct;
            }).collect(Collectors.toList());
            long startTime = System.currentTimeMillis();
            posCashProductManager.saveBatch(cashProductList);

            for (PosCashProduct posCashProduct : cashProductList) {
                baseOutinProductSaveVOList.add(BaseOutinProductSaveVO.builder()
                        .isGift(false)
                        .name(posCashProduct.getProductName())
                        .productId(posCashProduct.getProductId())
                        .warehouseId(baseWarehouse.getId())
                        .num(posCashProduct.getNum())
                        .attributeSetting(posCashProduct.getAttributeSetting())
                        .attributeSettingDesc(posCashProduct.getAttributeSettingDesc())
                        .desc((posCash.getType().equals(PosCashTypeEnum.START_TABLE.getCode())
                                ? posCash.getTableName() : PosCashTypeEnum.get(posCash.getType()).getDesc()
                        ).concat("-添加商品"))
                        .numType("2").price(posCashProduct.getPrice()).cashProductId(posCashProduct.getId())
                        .build());
            }

            // 添加属性值
            if (CollUtil.isNotEmpty(attributeSettingIds)) {
                List<PosCashProductAttribute> saveVOList = new ArrayList<>();
                Map<Long, BaseAttribute> baseAttributeMap = baseAttributegList.stream().collect(Collectors.toMap(BaseAttribute::getId, Function.identity()));
                Map<Long, BaseAttributeSetting> baseAttributeSettingMap = baseAttributeSettingList.stream().collect(Collectors.toMap(BaseAttributeSetting::getId, Function.identity()));
                for (PosCashProduct posCashProduct : cashProductList) {
                    if (StringUtils.isNotBlank(posCashProduct.getAttributeSetting())) {
                        List<Long> settingIds = Arrays.stream(posCashProduct.getAttributeSetting().split(",")).map(Long::valueOf).collect(Collectors.toList());
                        for (Long settingId : settingIds) {
                            saveVOList.add(PosCashProductAttribute.builder()
                                    .cashProductId(posCashProduct.getId())
                                    .productId(posCashProduct.getProductId())
                                    .attributeId(baseAttributeSettingMap.get(settingId).getAttributeId())
                                    .attributeName(baseAttributeMap.get(baseAttributeSettingMap.get(settingId).getAttributeId()).getName())
                                    .attributeSettingId(settingId)
                                    .attributeSettingName(baseAttributeSettingMap.get(settingId).getName())
                                    .changeType(baseAttributeSettingMap.get(settingId).getChangeType())
                                    .changeValue(baseAttributeSettingMap.get(settingId).getChangeValue())
                                    .createdOrgId(ContextUtil.getCurrentCompanyId())
                                    .build());
                        }
                    }
                }
                posCashProductAttributeService.saveBatch(saveVOList);
            }

            log.info("批量修改商品数量耗时：{}ms", System.currentTimeMillis() - startTime);
            String collect = String.join("", descriptionList);
            //库存
            String desc = PosCashTypeEnum.get(posCash.getType()).getDesc()
                    .concat("-")
                    .concat(PosCashBillTypeEnum.get(posCash.getBillType()).getDesc())
                    .concat("-")
                    .concat(PosCashBillStateEnum.get(posCash.getBillState()).getDesc());
            baseOutinService.saveStock(BaseOutinStockSaveVO.builder()
                    .type(OutinTypeEnum.SELL_OUT.getCode())
                    .sourceType(posCash.getOrderSource())
                    .warehouseId(baseWarehouse.getId()).code(posCash.getCode())
                    .billDate(LocalDate.now()).billState(0)
                    .orgId(ContextUtil.getCurrentCompanyId())
                    .remarks(desc).employeeId(ContextUtil.getEmployeeId())
                    .outinProductList(baseOutinProductSaveVOList)
                    .build());
            log.info("批量商品数量库存耗时：{}ms", System.currentTimeMillis() - startTime);

            // 发送ipad端
            // 配送商品 通知ipad 刷新订单
            if (ObjectUtil.isNotNull(posCash.getTableId())) {
                wsNoticeDeviceService.sendRefreshMsg(posCash.getTableId(), false, false);
            }
            //新增操作日志
            baseBizLogManager.createBizLog(BaseBizLog.builder()
                    .tenantId(ContextUtil.getTenantId())
                    .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                    .description("添加商品【" + collect + "】")
                    .bizModule(BizLogModuleEnum.ADD_PRODUCT.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                    .sourceId(posCash.getId()).remarks("")
                    .sn(ContextUtil.getSn())
                    .build());

            // 倒计时
            if (StringUtils.equals(ContextUtil.getOrderSource(), OrderSourceEnum.BOSS.getCode())) {
                rabbitTemplate.convertAndSend(RabbitMqConstant.COUNTDOWN_ORDER_EXCHANGE, RabbitMqConstant.COUNTDOWN_ORDER_DEAD_ROUTING_KEY,
                        JSON.toJSONString(OrderAutoCancelConsumeQuery.builder()
                                .tenantId(ContextUtil.getTenantId())
                                .orgId(ContextUtil.getCurrentCompanyId())
                                .memberId(ContextUtil.getMemberId())
                                .posCashId(posCash.getId())
                                .build()), message -> {
                            //5分钟自动取消
                            message.getMessageProperties().setExpiration((5 * 60000) + "");
                            return message;
                        });
            }
            CacheKey cacheKey = new CacheKey(RedisConstant.TENANT_PRINT_KITCHEN + ContextUtil.getTenantId() + printObjected);
            cacheKey.setExpire(Duration.ofMinutes(2));
            cacheOps.set(cacheKey, printObjected);
            template.convertAndSend(RabbitMqConstant.PRINT_KITCHEN_EXCHANGE, RabbitMqConstant.PRINT_KITCHEN_DEAD_ROUTING_KEY,
                    JSON.toJSONString(KitchenPrintQuery.builder()
                            .posCashId(posCash.getId())
                            .cashProductIds(cashProductList.stream().map(SuperEntity::getId).collect(Collectors.toList()))
                            .isIntact(false)
                            .type("1")
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId())
                            .employeeId(ContextUtil.getEmployeeId())
                            .sn(ContextUtil.getSn())
                            .mqObjectId(printObjected)
                            .build()), message -> {
                        //延迟3秒钟
                        message.getMessageProperties().setExpiration((2 * 1000) + "");
                        return message;
                    });
//            template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.KITCHEN_PRINT,
//                    JsonUtil.toJson(KitchenPrintQuery.builder()
//                            .posCashId(posCash.getId())
//                            .cashProductIds(cashProductList.stream().map(SuperEntity::getId).collect(Collectors.toList()))
//                            .isIntact(false)
//                            .type("1")
//                            .tenantId(ContextUtil.getTenantId())
//                            .orgId(ContextUtil.getCurrentCompanyId())
//                            .employeeId(ContextUtil.getEmployeeId())
//                            .mqObjectId(printObjected)
//                            .build()));
            addCashChangeNumCache(posCash.getId());
            return posCash.getId();
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getPosCashId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode());
            }
            CacheKey cacheKey = new CacheKey(RedisConstant.TENANT_PRINT_KITCHEN + ContextUtil.getTenantId() + printObjected);
            cacheOps.del(cacheKey);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long barCodeAddProduct(BarCodeAddProductQuery query) {
        PosCashCreateQuery cashQuery = BeanUtil.copyProperties(query, PosCashCreateQuery.class);
        PosCash posCash = createPosCash(cashQuery);
        ArgumentAssert.notNull(posCash, "订单不存在");
        query.setPosCashId(posCash.getId());
        if (query.getNum() == null || query.getNum() <= 0) {
            query.setNum(1);
        }
        BaseProduct baseProduct = baseProductManager.getOne(Wraps.<BaseProduct>lbQ()
                .eq(BaseProduct::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(BaseProduct::getBarCode, query.getBarCode()).last("limit 1"));
        ArgumentAssert.notNull(baseProduct, "商品不存在");

        BaseWarehouse baseWarehouse = baseWarehouseService.getOneBySn();
        ArgumentAssert.notNull(baseWarehouse, "请先配置销售仓库");

        PosCashProduct posCashProduct = posCashProductManager.getOne(Wraps.<PosCashProduct>lbQ()
                .eq(PosCashProduct::getCashId, query.getPosCashId())
                .isNull(PosCashProduct::getCashThailId)
                .and(wrapper -> wrapper.isNull(PosCashProduct::getIsMerge)
                        .or().eq(PosCashProduct::getIsMerge, 0))
                .eq(PosCashProduct::getProductId, baseProduct.getId())
                .eq(PosCashProduct::getWarehouseId, baseWarehouse.getId())
                .orderByDesc(PosCashProduct::getCreatedTime)
                .last("limit 1"));
        List<BaseAttributeSetting> baseAttributeSettingList = new ArrayList<>();
        if (ObjectUtil.isNotNull(query.getAttributeSettingList())) {
            baseAttributeSettingList = baseAttributeSettingService.listByIds(query.getAttributeSettingList());
        }
        if (ObjectUtil.isNotNull(posCashProduct)) {
            LocalDateTime createdTime = posCashProduct.getCreatedTime().plusMinutes(2);
            if (LocalDateTime.now().isAfter(createdTime)) {
                posCashProduct = null;
            }
        }
        if (ObjectUtil.isNotNull(posCashProduct)) {
            if (StrUtil.isNotBlank(posCashProduct.getReformPriceType())
                    && !posCashProduct.getReformPriceType().equals(ReformPriceTypeEnum.NO.getCode())) {
                posCashProduct = null;
            }
        }
        //新增商品数量
        if (ObjectUtil.isNull(posCashProduct)) {
            ArgumentAssert.isFalse(baseProduct.getIsAttribute() && CollUtil.isEmpty(query.getAttributeSettingList()), "请选择商品属性");
            posCashProduct = new PosCashProduct();
            posCashProduct.setCashId(posCash.getId());
            posCashProduct.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
            posCashProduct.setNum(0);
            if (CollUtil.isNotEmpty(baseAttributeSettingList)) {
                posCashProduct.setAttributeSetting(StrUtil.join(",", query.getAttributeSettingList()));
                posCashProduct.setAttributeSettingDesc(baseAttributeSettingList.stream().map(BaseAttributeSetting::getName).collect(Collectors.joining(",")));
            }
            posCashProduct.setPrice(baseProduct.getRetailPrice());
            posCashProduct.setDiscount(BigDecimal.ZERO);
            posCashProduct.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
            posCashProduct.setWarehouseId(baseWarehouse.getId());
        }
        productChange(baseProduct, posCashProduct, query.getNum(), posCash);
        //新增操作日志
        baseBizLogManager.createBizLog(BaseBizLog.builder()
                .tenantId(ContextUtil.getTenantId())
                .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                .description("条形码添加商品【" + baseProduct.getName() + "数量+" + query.getNum() + "】")
                .bizModule(BizLogModuleEnum.ADD_PRODUCT.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                .sourceId(posCash.getId()).remarks("")
                .sn(ContextUtil.getSn())
                .build());
        return posCashProduct.getId();

    }


    @Override
    public Long serviceChangeNum(ChangeNumQuery query) {
        ArgumentAssert.notNull(query.getServiceId(), "订单不存在");
        boolean lock = false;
        try {
            //如果是购物订单，并且收银参数中购物添加助教关闭，则不允许点
            checkAddServiceWhenShopping(query.getLogicalType());
            PosCashCreateQuery cashQuery = BeanUtil.copyProperties(query, PosCashCreateQuery.class);
            PosCash posCash = createPosCash(cashQuery);
            query.setPosCashId(posCash.getId());
            lock = distributedLock.lock(query.getPosCashId() + PosCashConstant.Event.ADD_PRODUCT.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            BaseService baseService = baseServiceService.getById(query.getServiceId());
            ArgumentAssert.notNull(baseService, "计费明细不存在");
            ArgumentAssert.notNull(posCash, "订单不存在");
            PosCashService posCashService = getPosCashService(query);
            int duration = baseService.getBillingCycle() * query.getNum();
            BaseEmployee baseEmployee = baseEmployeeManager.getById(query.getBizId());
            ArgumentAssert.notNull(baseEmployee, "服务人员不存在");
            String remarks = "";
            switch (query.getType()) {
                case 1:
                    //新增
                    if (ObjectUtil.isNull(posCashService)) {
                        // 校验忙碌是否可以点助教
                        Map<String, String> busyEmployee = helperApi.findParams(Collections.singletonList(ParameterKey.BUSY_EMPLOYEE_ORDER)).getData();
                        log.info("个性参数：{}", JsonUtil.toJson(busyEmployee));
                        if (CollUtil.isNotEmpty(busyEmployee) && StrUtil.isNotBlank(busyEmployee.get(ParameterKey.BUSY_EMPLOYEE_ORDER))
                                && StringUtils.equals(busyEmployee.get(ParameterKey.BUSY_EMPLOYEE_ORDER), "0")) {
                            List<BaseServicePersonal> servicePersonalList = servicePersonalManager.list(Wraps.<BaseServicePersonal>lbQ()
                                    .eq(SuperEntity::getDeleteFlag, 0)
                                    .eq(BaseServicePersonal::getServiceId, baseService.getId())
                                    .eq(BaseServicePersonal::getEmployeeId, query.getBizId())
                                    .eq(BaseServicePersonal::getStatus, ServiceStatus.USING.getCode()));
                            ArgumentAssert.isTrue(CollUtil.isEmpty(servicePersonalList), "当前助教正在忙碌中，请选择其他助教");
                        }
                        posCashService = new PosCashService();
                        posCashService.setCashId(posCash.getId());
                        posCashService.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                        posCashService.setPrice(baseService.getTimingCustomerPrice());
                        posCashService.setDiscount(BigDecimal.ZERO);
                        posCashService.setDuration(0);
                        posCashService.setServiceId(baseService.getId());
                        posCashService.setEmployeeId(query.getBizId());
                        posCashService.setClockType(query.getClockType());
                        posCashService.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                        posCashService.setStatus(CashTableStatusEnum.TIMING.getCode());
                        posCashService.setIsAccount(baseEmployee.getIsAccount());
                        posCashService.setReformPriceType(ReformPriceTypeEnum.NO.getCode());
                    }
                    if (posCash.getType().equals(PosCashTypeEnum.START_TABLE.getCode())) {
                        if (posCash.getBillType().equals(PosCashBillTypeEnum.COUNTER_CHECKOUT.getCode())
                                || posCash.getBillType().equals(PosCashBillTypeEnum.REGISTRATION.getCode())) {
                            posCashService.setStatus(CashTableStatusEnum.STOP.getCode());
                            posCashService.setDuration(duration);
                        }
                    }
                    serviceActivity(query.getActivityId(), posCashService);
                    if (query.getActivityId() != null) {
                        remarks = "参加服务活动【" + posCashService.getServiceActivityName() + "】";
                    }
                    serviceChange(baseService, posCashService, duration);
                    break;
                case 2:
                    ArgumentAssert.notNull(posCashService, "请刷新后重试");
                    serviceChange(baseService, posCashService, -duration);
                    break;
                case 3:
                    //直接设置商品数量
                    ArgumentAssert.notNull(posCashService, "请刷新后重试");
                    serviceChange(baseService, posCashService, duration - posCashService.getDuration());
                    break;
                default:
                    ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "类型不存在");
                    break;
            }
            if (ObjectUtil.equal(posCashService.getStatus(), CashTableStatusEnum.TIMING.getCode())) {
                posCash.setBillState(PosCashBillStateEnum.NO_SETTLED.getCode());
                posCash.setUpdatedTime(LocalDateTime.now());
                posCashManager.updateById(posCash);
            }
            //新增操作日志
            baseBizLogManager.createBizLog(BaseBizLog.builder()
                    .tenantId(ContextUtil.getTenantId())
                    .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                    .description("添加服务【" + baseService.getName()
                            + (StrUtil.isBlank(baseEmployee.getNumber()) ? "" : "-" + baseEmployee.getNumber())
                            + baseEmployee.getRealName() + "】")
                    .bizModule(BizLogModuleEnum.ADD_SERVICE.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                    .sourceId(posCash.getId()).remarks(remarks)
                    .sn(ContextUtil.getSn())
                    .build());

            // 添加服务通知设备刷新订单
            if (ObjectUtil.isNotNull(posCash.getTableId())) {
                wsNoticeDeviceService.sendRefreshMsg(posCash.getTableId(), false, false);
            }
            addCashChangeNumCache(posCash.getId());
            return posCashService.getId();
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getPosCashId() + PosCashConstant.Event.ADD_PRODUCT.getCode());
            }
        }
    }

    private void checkAddServiceWhenShopping(LogicalType logicalType) {
        if (logicalType != null && logicalType.getCode().equals(LogicalType.SHOPPING.getCode())) {
            R<Map<String, String>> helperApiParams = helperApi.findParams(Collections.singletonList(ParameterKey.ADD_SERVICE_WHEN_SHOPPING));
            if (CollUtil.isNotEmpty(helperApiParams.getData()) &&
                    StrUtil.isNotBlank(helperApiParams.getData().get(ParameterKey.ADD_SERVICE_WHEN_SHOPPING))
                    && StringUtils.equals(helperApiParams.getData().get(ParameterKey.ADD_SERVICE_WHEN_SHOPPING), "0")) {
                throw BizException.wrapTips("提示", "购物订单不支持点助教，请至 “小程序->收银参数“ 进行更改！");
            }
        }

    }

    @Override
    public Integer thailChangeNum(ThailChangeNumQuery query) {
        ArgumentAssert.notNull(query.getBizId(), "参数异常");
        boolean lock = false;
        String objected = IdUtil.objectId();
        CacheKey cacheKey = new CacheKey(RedisConstant.TENANT_PRINT_KITCHEN + ContextUtil.getTenantId() + objected);
        cacheKey.setExpire(Duration.ofMinutes(2));
        cacheOps.set(cacheKey, objected);
        try {
            PosCashCreateQuery cashQuery = BeanUtil.copyProperties(query, PosCashCreateQuery.class);
            PosCash posCash = createPosCash(cashQuery);
            query.setPosCashId(posCash.getId());
            lock = distributedLock.lock(query.getPosCashId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            OpeningTableSaveVO saveVO = OpeningTableSaveVO.builder().thailId(query.getBizId()).objectId(objected)
                    .posCashId(query.getPosCashId()).thailList(query.getThailList()).build();
            LiteflowResponse response = flowExecutor.execute2Resp(BizConstant.FLOW.ADD_THAIL_CHAIN,
                    saveVO, OpenTableContext.class, DetailCalcContext.class);
            ArgumentAssert.isFalse(!response.isSuccess(), response.getMessage());
            OpenTableContext contextBean = response.getContextBean(OpenTableContext.class);
            PosCashThail posCashThail = contextBean.getPosCashThail();
            posCash = contextBean.getPosCash();
            //新增操作日志
            baseBizLogManager.createBizLog(BaseBizLog.builder()
                    .tenantId(ContextUtil.getTenantId())
                    .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                    .description("添加套餐【" + posCashThail.getThailName() + "】")
                    .bizModule(BizLogModuleEnum.ADD_THAIL.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                    .sourceId(posCash.getId()).remarks("手动添加套餐")
                    .sn(ContextUtil.getSn())
                    .build());
            addCashChangeNumCache(posCash.getId());
            return 1;
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getPosCashId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode());
            }
            CacheKey printKey = new CacheKey(RedisConstant.TENANT_PRINT_KITCHEN + ContextUtil.getTenantId() + objected);
            cacheOps.del(printKey);
        }
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Long buyCardChangeNum(BuyCardChangeQuery query) {
        ArgumentAssert.notNull(query.getBizId(), "参数异常");
        boolean lock = false;
        try {
            PosCashCreateQuery cashQuery = BeanUtil.copyProperties(query, PosCashCreateQuery.class);
            PosCash posCash = createPosCash(cashQuery);
            query.setPosCashId(posCash.getId());
            checkOrderService.checkBuyCardPosCash(posCash);
            lock = distributedLock.lock(query.getPosCashId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            BaseCard baseCard = baseCardService.getById(query.getBizId());
            ArgumentAssert.notNull(baseCard, "权益卡不存在");
            ArgumentAssert.isTrue(baseCard.getState(), "权益卡已下架, 不允许购买");
            baseCard.setLockNum(baseCard.getLockNum() + 1);
            ArgumentAssert.isFalse(baseCard.getStockNum() - baseCard.getLockNum() < 0,
                    "库存不足");
            PosCashCard build = PosCashCard.builder()
                    .cardId(baseCard.getId())
                    .name(baseCard.getName())
                    .num(null).amount(baseCard.getPrice())
                    .assessedAmount(BigDecimal.ZERO).discountAmount(BigDecimal.ZERO)
                    .cashThailId(null).cashId(posCash.getId()).price(baseCard.getPrice())
                    .costPrice(BigDecimal.ZERO).paid(BigDecimal.ZERO)
                    .createdOrgId(ContextUtil.getCurrentCompanyId()).deleteFlag(0)
                    .discount(BigDecimal.ZERO).discountType(DiscountTypeEnum.ORIGINAL.getCode())
                    .employeeId(ContextUtil.getEmployeeId())
                    .profitPrice(baseCard.getPrice())
                    .sn(ContextUtil.getSn())
                    .num(1).build();
            posCashCardService.save(build);
            ArgumentAssert.isFalse(!baseCardService.updateById(baseCard), "操作失败！");
            //新增操作日志
            baseBizLogManager.createBizLog(BaseBizLog.builder()
                    .tenantId(ContextUtil.getTenantId())
                    .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                    .description(BizLogModuleEnum.ADD_CARD.getDesc() + "【" + baseCard.getName() + "】")
                    .bizModule(BizLogModuleEnum.ADD_CARD.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                    .sourceId(posCash.getId()).remarks("")
                    .sn(ContextUtil.getSn())
                    .build());
            addCashChangeNumCache(posCash.getId());
            return build.getId();
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getPosCashId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode());
            }
        }
    }

    @Override
    @GlobalTransactional
    public CashDetailResultVO queryDetail(QueryDetailQuery query) {
        boolean lock = false;
        try {
            PosCashCreateQuery cashQuery = BeanUtil.copyProperties(query, PosCashCreateQuery.class);
            PosCash posCash = getPosCash(cashQuery);
            if (posCash == null && !checkQueryDetail(query)) {
                return CashDetailResultVO.builder().build();
            }
            if (ObjectUtil.isNull(posCash)) {
                return CashDetailResultVO.builder().build();
            }
            if (query.getPosCashId() == null) {
                query.setPosCashId(posCash.getId());
            }
            lock = distributedLock.lock(query.getPosCashId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode(), 3000, 1, 500);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            PriceCalcQuery req = PriceCalcQuery.builder().posCash(posCash).employeeId(ContextUtil.getEmployeeId()).build();
            req.setFlowCommonContext(req);
            if (!posCash.getBillType().equals(PosCashBillTypeEnum.REGULAR_SINGLE.getCode())) {
                req.setIsStop(true);
            }
            Long tenantId = ContextUtil.getTenantId();
            //北京 510429388227506016
            try {
                if (posCash.getMemberId() != null
                        && ObjectUtil.isNotNull(tenantId)) {
                    PosCashPayment cashPayment = posCashPaymentService.getOne(Wraps.<PosCashPayment>lbQ()
                            .eq(PosCashPayment::getCashId, posCash.getId())
                            .eq(PosCashPayment::getDeleteFlag, 0)
                            .eq(PosCashPayment::getStatus, PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                            .inSql(PosCashPayment::getPayTypeId, "select id from base_payment_type where delete_flag = 0 " +
                                    "and biz_type = " + PaymentBizTypeEnum.ACCOUNT.getCode())
                            .last("limit 1"));
                    if (ObjectUtil.isNotNull(cashPayment)
                            && ObjectUtil.isNotNull(cashPayment.getMemberId())
                            && !ObjectUtil.equals(cashPayment.getMemberId(), posCash.getMemberId())) {
                        String message = "会员出问题啦！租户：%s，错误会员：%s，正确会员：%s，错误订单：%s";
                        template.convertAndSend(RabbitMqConstant.FANOUT_EXCHANGE, RabbitMqConstant.DING_DING_ALARM,
                                String.format(message, tenantId, posCash.getMemberId(), cashPayment.getMemberId(), posCash.getId()));
                        posCash.setMemberId(cashPayment.getMemberId());
                        req.setPosCash(posCash);
                    }
                }
            } catch (Exception e) {
                log.error("会员出问题啦！", e);
                throw BizException.wrapTips("提示", "订单会员补丁异常，请联系管理员");
            }
            req.setFlowCommonContext(req);
            LiteflowResponse response = flowExecutor.execute2Resp(BizConstant.FLOW.DEFAULT_PRICE_CHAIN, req, PriceCalcContext.class
                    , DetailCalcContext.class);
            ArgumentAssert.isFalse(!response.isSuccess(), response.getMessage());
            PriceCalcContext contextBean = response.getContextBean(PriceCalcContext.class);
            CashDetailResultVO cashDetailResultVO = contextBean.getCashDetailResultVO();
            cashDetailResultVO.setIsRegistration(query.getIsRegistration());

            // 返回是否临时开关灯
            cashDetailResultVO.setIsTemporaryLights(posCash.getIsTemporaryLights());

            // 返回时间
            BaseJobInfo baseJobInfo = null;
            if (posCash.getIsTemporaryLights()) {
                baseJobInfo = baseJobInfoService.getOne(Wraps.<BaseJobInfo>lbQ()
                        .eq(BaseJobInfo::getPosCashId, posCash.getId()).eq(BaseJobInfo::getTriggerStatus, 1)
                        .eq(BaseJobInfo::getJobType, JobTypeEnum.CASH_CLOSE_LIGHT_TIMING.getCode()).last(" limit 1"));

            }
            //判断是否定时 计算剩余时长
            if (ObjectUtil.isNotNull(baseJobInfo)
                    && ObjectUtil.equals(baseJobInfo.getTriggerStatus(), 1)) {
                //获取结束时间
                LocalDateTime endTime = LocalDateTime.parse(baseJobInfo.getScheduleConf(), DateTimeFormatter.ofPattern(DateUtils.CRON_FORMAT));
                //计算剩余时长
                cashDetailResultVO.setRemainingDuration(DateUtils.calDifMinutes(LocalDateTime.now(), endTime).intValue());
                if (cashDetailResultVO.getRemainingDuration() < 0) {
                    cashDetailResultVO.setRemainingDuration(0);
                }
            }

            echoService.action(cashDetailResultVO);
            // 清空一下， 数据
            CacheKey cacheKey = new CacheKey(RedisConstant.CASH_CHANGE_NUM + ContextUtil.getTenantId() + posCash.getId());
            cacheOps.del(cacheKey);
            return cashDetailResultVO;
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getPosCashId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode());
            }
        }
    }

    @Override
    @Transactional
    public Boolean bindMember(BindMemberQuery query) {
        boolean lock = false;
        try {
            if (query.getSource() != null) {
                ContextUtil.setOrderSource(query.getSource().getCode());
            }
            PosCashCreateQuery cashQuery = BeanUtil.copyProperties(query, PosCashCreateQuery.class);
            PosCash posCash = createPosCash(cashQuery);
            ArgumentAssert.notNull(posCash, "订单不存在");
            checkOrderService.checkBindMemberPosCash(posCash);
            query.setPosCashId(posCash.getId());
            lock = distributedLock.lock(query.getPosCashId() + PosCashConstant.Event.CALC_PRICE.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            ArgumentAssert.notNull(posCash, "订单不存在或此状态无法操作");
            MemberInfo memberInfo = memberInfoManager.getById(query.getMemberId());
            ArgumentAssert.notNull(memberInfo, "会员不存在");
            if (!Objects.equals(query.getSource(), OrderSourceEnum.SELF)) {
                if (StrUtil.isNotBlank(query.getMemberVerifyType())) {
                    checkOrderService.checkBindMemberVerify(MemberVerifyQuery.builder()
                            .verifyType(query.getMemberVerifyType())
                            .verifyValue(query.getMemberVerifyValue())
                            .memberInfo(memberInfo)
                            .posCash(posCash)
                            .build());
                }
            }
            posCash.setRemarks(calcPriceService.setRemarks(Lists.newArrayList(), "", posCash.getRemarks(), EchoDictType.App.ORDER_DISCOUNT_TAGS));
            posCash.setMemberId(memberInfo.getId());
            posCash.setCreatedMemberId(memberInfo.getId());
            posCash.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
            posCash.setDiscount(BigDecimal.ZERO);
            posCash.setIsRound(false);
            posCash.setRoundAmount(BigDecimal.ZERO);
            posCash.setIsAutoRound(false);
            posCash.setIsMemberDiscount(true);
            posCash.setAutoRoundAmount(BigDecimal.ZERO);
            posCash.setUpdatedBy(ContextUtil.getUserId());
            posCash.setUpdatedTime(LocalDateTime.now());
            try {
                orderEquityRemoveService.fastDiscountEquity(posCash);
            } catch (Exception ignored) {
            }
            //新增操作日志
            baseBizLogManager.createBizLog(BaseBizLog.builder()
                    .tenantId(ContextUtil.getTenantId())
                    .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                    .description("绑定会员【" + memberInfo.getName() + "/" + memberInfo.getMobile() + "】")
                    .bizModule(BizLogModuleEnum.BIND_MEMBER.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                    .sourceId(posCash.getId()).remarks("")
                    .sn(ContextUtil.getSn())
                    .build());
            template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.BIZ_CACHE,
                    JSON.toJSONString(BizCacheConsumeQuery.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId())
                            .bizCacheEnum(BizCacheEnum.MEMBER_CARD)
                            .memberId(memberInfo.getId())
                            .build()));
            // 绑定会员 通知ipad刷新订单
            if (ObjectUtil.isNotNull(posCash.getTableId())) {
                wsNoticeDeviceService.sendRefreshMsg(posCash.getTableId(), false, false);
            }
            CacheKey cacheKey = new CacheKey(RedisConstant.CASH_BIND_MEMBER + posCash.getId());
            cacheKey.setExpire(Duration.ofMinutes(1));
            cacheOps.set(cacheKey, posCash.getMemberId());
            return posCashManager.updateById(posCash);
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getPosCashId() + PosCashConstant.Event.ORDER.getCode());
            }
        }
    }

    @Override
    @GlobalTransactional
    public Boolean shopNum(ShopNumQuery query) {
        boolean lock = false;
        try {
            PosCashCreateQuery cashQuery = BeanUtil.copyProperties(query, PosCashCreateQuery.class);
            PosCash posCash = createPosCash(cashQuery);
            ArgumentAssert.notNull(posCash, "订单不存在");
            ArgumentAssert.isFalse(!ObjectUtil.equal(posCash.getType(), PosCashTypeEnum.CARD_COUPON.getCode())
                    && !ObjectUtil.equal(posCash.getType(), PosCashTypeEnum.SHOPPING.getCode()), "开台购物订单可操作");
            ArgumentAssert.isFalse(!ObjectUtil.equal(posCash.getBillType(), PosCashBillTypeEnum.REGULAR_SINGLE.getCode())
                            && !ObjectUtil.equal(posCash.getBillType(), PosCashBillTypeEnum.REGISTRATION.getCode())
                            && !ObjectUtil.equal(posCash.getBillType(), PosCashBillTypeEnum.COUNTER_CHECKOUT.getCode()),
                    "当前状态不支持此操作");
            if (ObjectUtil.equal(posCash.getBillState(), PosCashBillStateEnum.COMPLETE.getCode())) {
                throw BizException.wrap(ExceptionCode.ORDER_COMPLETE);
            }
            query.setPosCashId(posCash.getId());
            lock = distributedLock.lock(query.getPosCashId() + PosCashConstant.Event.ORDER.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            ArgumentAssert.notNull(posCash, "订单不存在或此状态无法操作");
            posCash.setShopNum(query.getShopNum());
            posCash.setUpdatedBy(ContextUtil.getUserId());
            if (StrUtil.isBlank(query.getShopNum()) && !calcPriceService.isExistItem(posCash)) {
                return posCashManager.removeById(posCash.getId());
            }
            posCash.setUpdatedTime(LocalDateTime.now());
            //新增操作日志
            baseBizLogManager.createBizLog(BaseBizLog.builder()
                    .tenantId(ContextUtil.getTenantId())
                    .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                    .description(StrUtil.isBlank(query.getShopNum()) ?
                            "移除购物号"
                            : "添加购物号【" + query.getShopNum() + "】")
                    .bizModule(BizLogModuleEnum.BIND_MEMBER.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                    .sourceId(posCash.getId()).remarks("")
                    .sn(ContextUtil.getSn())
                    .build());
            return posCashManager.updateById(posCash);
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getPosCashId() + PosCashConstant.Event.ORDER.getCode());
            }
        }
    }


    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean delMember(QueryDetailQuery query) {
        ArgumentAssert.notNull(query.getPosCashId(), "参数校验异常");
        boolean lock = false;
        try {
            lock = distributedLock.lock(query.getPosCashId() + PosCashConstant.Event.ORDER.getCode(), 0);
            PosCash posCash = posCashManager.getOne(Wraps.<PosCash>lbQ().eq(PosCash::getId, query.getPosCashId()));
            checkOrderService.checkBindMemberPosCash(posCash);
            ArgumentAssert.notNull(posCash, "订单不存在或此状态无法操作");
            ArgumentAssert.notNull(posCash.getMemberId(), "该订单未绑定会员");
            MemberInfo memberInfo = memberInfoManager.getMemberById(posCash.getMemberId());
            //去除卡和劵信息
            BindCouponQuery couponQuery = BindCouponQuery.builder()
                    .posCashId(query.getPosCashId())
                    .build();
            couponQuery.setFlowCommonContext(couponQuery);
            flowExecutor.execute2Resp(BizConstant.FLOW.REMOVE_COUPON_CHAIN, couponQuery, CouponCalcContext.class
                    , DetailCalcContext.class);

            BindCardQuery cardQuery = BindCardQuery.builder()
                    .posCashId(query.getPosCashId())
                    .build();
            cardQuery.setFlowCommonContext(cardQuery, couponQuery);
            flowExecutor.execute2Resp(BizConstant.FLOW.REMOVE_CARD_CHAIN, cardQuery,
                    DetailCalcCardPriceContext.class, DetailCalcContext.class);
            //去除会员的优惠明细
            posCashTableManager.update(Wraps.<PosCashTable>lbU()
                    .set(PosCashTable::getDiscountType, DiscountTypeEnum.ORIGINAL.getCode())
                    .set(PosCashTable::getDiscount, BigDecimal.ZERO)
                    .set(PosCashTable::getDiscountAmount, BigDecimal.ZERO)
                    .in(PosCashTable::getDiscountType, Arrays.asList
                            (DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode(),
                                    DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode()))
                    .eq(PosCashTable::getCashId, posCash.getId()));
            if (ObjectUtil.isNotNull(posCash.getTableId())) {
                posCashTableManager.update(Wraps.<PosCashTable>lbU()
                        .set(PosCashTable::getFreeAmount, BigDecimal.ZERO)
                        .set(PosCashTable::getFreeDuration, 0)
                        .eq(PosCashTable::getTableId, posCash.getTableId())
                        .eq(PosCashTable::getCashId, posCash.getId()));
            }
            //去除会员的优惠明细
            posCashServiceManager.update(Wraps.<PosCashService>lbU()
                    .set(PosCashService::getDiscountType, DiscountTypeEnum.ORIGINAL.getCode())
                    .set(PosCashService::getDiscount, BigDecimal.ZERO)
                    .set(PosCashService::getDiscountAmount, BigDecimal.ZERO)
                    .in(PosCashService::getDiscountType, Arrays.asList
                            (DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode(),
                                    DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode()))
                    .eq(PosCashService::getCashId, posCash.getId()));
            //去除会员的优惠明细
            posCashProductManager.update(Wraps.<PosCashProduct>lbU()
                    .set(PosCashProduct::getDiscountType, DiscountTypeEnum.ORIGINAL.getCode())
                    .set(PosCashProduct::getDiscount, BigDecimal.ZERO)
                    .set(PosCashProduct::getDiscountAmount, BigDecimal.ZERO)
                    .in(PosCashProduct::getDiscountType, Arrays.asList
                            (DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode(),
                                    DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode()))
                    .eq(PosCashProduct::getCashId, posCash.getId()));

            //去除会员的优惠明细
            posCashThailManager.update(Wraps.<PosCashThail>lbU()
                    .set(PosCashThail::getDiscountType, DiscountTypeEnum.ORIGINAL.getCode())
                    .set(PosCashThail::getDiscount, BigDecimal.ZERO)
                    .set(PosCashThail::getDiscountAmount, BigDecimal.ZERO)
                    .in(PosCashThail::getDiscountType, Arrays.asList
                            (DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode(),
                                    DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode()))
                    .eq(PosCashThail::getCashId, posCash.getId()));
            //去除会员已验证信息
            if (Objects.equals(posCash.getIsMemberVerified(), true)) {
                posCashOperateVerifyService.logicDeleteByCashIdAndMemberId(posCash.getId(), posCash.getMemberId());
                posCash.setIsMemberVerified(false);
            }
            posCash.setMemberId(null);
            posCash.setCreatedMemberId(null);
            posCash.setIsMemberDiscount(false);
            posCash.setUpdatedBy(ContextUtil.getUserId());
            if (!calcPriceService.isExistItem(posCash)) {
                return posCashManager.removeById(posCash.getId());
            }
            posCash.setUpdatedTime(LocalDateTime.now());
            try {
                orderEquityRemoveService.fastDiscountEquity(posCash);
            } catch (Exception ignored) {
            }
            //新增操作日志
            baseBizLogManager.createBizLog(BaseBizLog.builder()
                    .tenantId(ContextUtil.getTenantId())
                    .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                    .description("解绑会员【" + memberInfo.getName() + "/" + memberInfo.getMobile() + "】")
                    .bizModule(BizLogModuleEnum.BIND_MEMBER.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                    .sourceId(posCash.getId()).remarks("")
                    .sn(ContextUtil.getSn())
                    .build());

            // 取消绑定会员 通知ipad刷新订单
            if (ObjectUtil.isNotNull(posCash.getTableId())) {
                wsNoticeDeviceService.sendRefreshMsg(posCash.getTableId(), false, false);
            }
            CacheKey cacheKey = new CacheKey(RedisConstant.CASH_BIND_MEMBER + posCash.getId());
            cacheKey.setExpire(Duration.ofMinutes(1));
            cacheOps.set(cacheKey, memberInfo.getId());
            return posCashManager.updateById(posCash);
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getPosCashId() + PosCashConstant.Event.ORDER.getCode());
            }
        }
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean emptyCart(DelOrEmptyOrderQuery query) {
        boolean lock = false;
        try {
            lock = distributedLock.lock(query.getPosCashId() + PosCashConstant.Event.ORDER.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            PosCash posCash = posCashManager.getOne(Wraps.<PosCash>lbQ()
                    .eq(PosCash::getSn, ContextUtil.getSn())
                    .eq(PosCash::getId, query.getPosCashId()));
            checkOrderService.checkEmptyOrderPosCash(posCash);
            //两分钟内正在支付中订单不允许清空
            LocalDateTime localDateTime = LocalDateTime.now().minusMinutes(2);
            long count = posCashPaymentService.count(Wraps.<PosCashPayment>lbQ().eq(PosCashPayment::getCashId, query.getPosCashId())
                    .eq(PosCashPayment::getDeleteFlag, 0)
                    .le(PosCashPayment::getCreatedTime,
                            DateUtils.format(localDateTime, DateUtils.DEFAULT_DATE_TIME_FORMAT))
                    .inSql(PosCashPayment::getPayTypeId, "select id from base_payment_type where delete_flag = 0 " +
                            "and biz_type in (" + StrUtil.join(",",
                            Arrays.asList(PaymentBizTypeEnum.WECHAT.getCode(),
                                    PaymentBizTypeEnum.POLYMERIZATION.getCode())) + ")")
                    .eq(PosCashPayment::getStatus, PosCashPaymentStatusEnum.NO_PAY.getCode())
            );
            ArgumentAssert.isFalse(count > 0, "当前订单已发生支付，建议挂单或结账后再操作");
            PriceCalcQuery req = PriceCalcQuery.builder().posCash(posCash).isStop(true).employeeId(ContextUtil.getEmployeeId()).build();
            DetailCalcContext context = new DetailCalcContext();
            context.setPosCash(posCash);
            req.setFlowCommonContext(req);
            LiteflowResponse response = flowExecutor.execute2Resp(BizConstant.FLOW.CANCEL_ORDER, req, new PriceCalcContext(), context);
            ArgumentAssert.isFalse(!response.isSuccess(), response.getMessage());
            try {
                removeEquity(posCash);
            } catch (Exception e) {
            }
            //新增操作日志
            baseBizLogManager.createBizLog(BaseBizLog.builder()
                    .tenantId(ContextUtil.getTenantId())
                    .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                    .description(BizLogModuleEnum.EMPTY_CART.getDesc())
                    .bizModule(BizLogModuleEnum.EMPTY_CART.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                    .sourceId(posCash.getId()).remarks("")
                    .sn(ContextUtil.getSn())
                    .build());
            posCash.setUpdatedTime(LocalDateTime.now());
            return posCashManager.updateById(posCash);
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getPosCashId() + PosCashConstant.Event.ORDER.getCode());
            }
        }
    }


    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean delOrder(DelOrEmptyOrderQuery query) {
        boolean lock = false;
        String objected = IdUtil.objectId();
        try {
            lock = distributedLock.lock(query.getPosCashId() + PosCashConstant.Event.CALC_PRICE.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            PosCash posCash = posCashManager.getOne(Wraps.<PosCash>lbQ()
                    .eq(PosCash::getId, query.getPosCashId())
                    // 删除根据sn 查询订单
                    .eq(PosCash::getSn, ContextUtil.getSn()));
            checkOrderService.checkEmptyOrderPosCash(posCash);
            PriceCalcQuery req = PriceCalcQuery.builder().posCash(posCash).isStop(true).employeeId(ContextUtil.getEmployeeId()).build();
            DetailCalcContext context = new DetailCalcContext();
            context.setPosCash(posCash);
            req.setFlowCommonContext(req);
            LiteflowResponse response = flowExecutor.execute2Resp(BizConstant.FLOW.CANCEL_ORDER, req, new PriceCalcContext(), context);
            ArgumentAssert.isFalse(!response.isSuccess(), response.getMessage());
            List<PosCashProduct> cashProductList = posCashProductManager.list(Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getCashId, posCash.getId()));
            try {
                removeEquity(posCash);
            } catch (Exception e) {
            }
            //新增操作日志
            baseBizLogManager.createBizLog(BaseBizLog.builder()
                    .tenantId(ContextUtil.getTenantId())
                    .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                    .description("删除订单")
                    .bizModule(BizLogModuleEnum.DEL_ORDER.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                    .sourceId(posCash.getId()).remarks("")
                    .sn(ContextUtil.getSn())
                    .build());
            baseOutinService.update(Wraps.<BaseOutin>lbU()
                    .set(BaseOutin::getBillState, 0)
                    .eq(BaseOutin::getType, OutinTypeEnum.SELL_OUT.getCode())
                    .eq(BaseOutin::getCode, posCash.getCode())
            );
            // 打印退单
            CacheKey cacheKey = new CacheKey(RedisConstant.TENANT_PRINT_KITCHEN + ContextUtil.getTenantId() + objected);
            cacheKey.setExpire(Duration.ofMinutes(2));
            cacheOps.set(cacheKey, objected);
            if (CollUtil.isNotEmpty(cashProductList)) {
                // 需要后厨打印的商品
                template.convertAndSend(RabbitMqConstant.PRINT_KITCHEN_EXCHANGE, RabbitMqConstant.PRINT_KITCHEN_DEAD_ROUTING_KEY,
                        JSON.toJSONString(KitchenPrintQuery.builder()
                                .posCashId(posCash.getId())
                                .cashProductIds(cashProductList.stream().map(SuperEntity::getId).collect(Collectors.toList()))
                                .isIntact(false)
                                .type("2")
                                .tenantId(ContextUtil.getTenantId())
                                .orgId(ContextUtil.getCurrentCompanyId())
                                .mqObjectId(objected)
                                .employeeId(ContextUtil.getEmployeeId())
                                .sn(ContextUtil.getSn())
                                .build()), message -> {
                            //延迟2秒钟
                            message.getMessageProperties().setExpiration((2 * 1000) + "");
                            return message;
                        });
//                template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.KITCHEN_PRINT,
//                        JsonUtil.toJson(KitchenPrintQuery.builder()
//                                .posCashId(posCash.getId())
//                                .cashProductIds(cashProductList.stream().map(SuperEntity::getId).collect(Collectors.toList()))
//                                .isIntact(false)
//                                .type("2")
//                                .tenantId(ContextUtil.getTenantId())
//                                .orgId(ContextUtil.getCurrentCompanyId())
//                                .mqObjectId(objected)
//                                .employeeId(ContextUtil.getEmployeeId())
//                                .build()));
            }
            return posCashManager.removeById(posCash.getId());
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getPosCashId() + PosCashConstant.Event.CALC_PRICE.getCode());
            }
            CacheKey cacheKey = new CacheKey(RedisConstant.TENANT_PRINT_KITCHEN + ContextUtil.getTenantId() + objected);
            cacheOps.del(cacheKey);
        }
    }


    /**
     * 验证查询明细参数
     */
    protected abstract boolean checkQueryDetail(QueryDetailQuery query);

    /**
     * 商品更新
     * 库存更新
     */
    protected void productChange(BaseProduct baseProduct, PosCashProduct posCashProduct, Integer num, PosCash posCash) {
        posCashProduct.setNum(posCashProduct.getNum() + num);
        posCashProduct.setOrginPrice(posCashProduct.getPrice().multiply(new BigDecimal(posCashProduct.getNum()))
                .setScale(2, RoundingMode.HALF_UP));
        posCashProduct.setOldOrginPrice(posCashProduct.getOrginPrice());
        posCashProduct.setOldPrice(posCashProduct.getPrice());
        //锁定库存
//        BaseProductStock productStock = productStockManager.getOne(Wraps.<BaseProductStock>lbQ()
//                .eq(BaseProductStock::getProductId, baseProduct.getId()).last("limit 1"));
//        ArgumentAssert.notNull(productStock, "库存不足，请联系管理员");
//        productStock.setLockNum((productStock.getLockNum() == null ? 0 : productStock.getLockNum()) + num);
//        ArgumentAssert.isFalse(productStock.getLockNum() < 0, "库存操作异常，请联系管理员");
//        //是否支持负库存
//        if (!baseProduct.getIsNegative()) {
//            ArgumentAssert.isFalse(productStock.getNum() - productStock.getLockNum() < 0, "库存不足");
//        }
        if (Objects.isNull(posCashProduct.getWarehouseId())) {
            BaseWarehouse baseWarehouse = baseWarehouseService.getOneBySn();
            ArgumentAssert.notNull(baseWarehouse, "请先配置销售仓库");
            posCashProduct.setWarehouseId(baseWarehouse.getId());
        }

        if (posCashProduct.getNum() == 0) {
            if (posCash.getType().equals(PosCashTypeEnum.SHOPPING.getCode()) &&
                    (posCash.getBillType().equals(PosCashBillTypeEnum.COUNTER_CHECKOUT.getCode())
                            || posCash.getBillType().equals(PosCashBillTypeEnum.REGISTRATION.getCode())
                    )) {
//                ArgumentAssert.isFalse(posCashProductManager.count(Wraps.<PosCashProduct>lbQ()
//                        .isNull(PosCashProduct::getCashThailId)
//                        .eq(PosCashProduct::getDeleteFlag, 0)
//                        .eq(PosCashProduct::getCashId, posCash.getId())) == 1, "至少保留一条商品明细");
            }
        }
        if (ObjectUtil.isNull(posCashProduct.getId())) {
            posCashProduct.setProductId(baseProduct.getId());
            posCashProduct.setProductName(baseProduct.getName());
            posCashProduct.setAmount(posCashProduct.getOrginPrice());
            posCashProduct.setDiscountAmount(BigDecimal.ZERO);
            posCashProduct.setIsGift(false);
            posCashProduct.setIsDiscount(baseProduct.getDiscount());
            posCashProduct.setIsAccount(baseProduct.getIsAccount());
            posCashProduct.setSn(ContextUtil.getSn());
            posCashProductManager.save(posCashProduct);
            List<Long> attributeSettingIds = new ArrayList<>();
            if (StrUtil.isNotBlank(posCashProduct.getAttributeSetting())) {
                attributeSettingIds.addAll(Arrays.stream(posCashProduct.getAttributeSetting().split(","))
                        .map(Long::valueOf).collect(Collectors.toSet()));
            }
            // 添加属性值
            if (CollUtil.isNotEmpty(attributeSettingIds)) {
                List<PosCashProductAttribute> saveVOList = new ArrayList<>();
                List<BaseAttributeSetting> baseAttributeSettingList = baseAttributeSettingService.listByIds(attributeSettingIds);
                List<BaseAttribute> baseAttributegList = baseAttributeService.listByIds(baseAttributeSettingList.stream().map(BaseAttributeSetting::getAttributeId).distinct().collect(Collectors.toList()));
                Map<Long, BaseAttribute> baseAttributeMap = baseAttributegList.stream().collect(Collectors.toMap(BaseAttribute::getId, Function.identity()));
                Map<Long, BaseAttributeSetting> baseAttributeSettingMap = baseAttributeSettingList.stream().collect(Collectors.toMap(BaseAttributeSetting::getId, Function.identity()));
                for (Long settingId : attributeSettingIds) {
                    saveVOList.add(PosCashProductAttribute.builder()
                            .cashProductId(posCashProduct.getId())
                            .productId(posCashProduct.getProductId())
                            .attributeId(baseAttributeSettingMap.get(settingId).getAttributeId())
                            .attributeName(baseAttributeMap.get(baseAttributeSettingMap.get(settingId).getAttributeId()).getName())
                            .attributeSettingId(settingId)
                            .attributeSettingName(baseAttributeSettingMap.get(settingId).getName())
                            .changeType(baseAttributeSettingMap.get(settingId).getChangeType())
                            .changeValue(baseAttributeSettingMap.get(settingId).getChangeValue())
                            .createdOrgId(ContextUtil.getCurrentCompanyId())
                            .build());
                }
                posCashProductAttributeService.saveBatch(saveVOList);
            }
        } else {
            if (posCashProduct.getNum() == 0) {
                posCashProduct.setDeleteFlag(1);
                posCashProductManager.removeById(posCashProduct.getId());
            } else {
                posCashProduct.setUpdatedTime(LocalDateTime.now());
                posCashProductManager.updateById(posCashProduct);
            }
        }
        String desc = PosCashTypeEnum.get(posCash.getType()).getDesc()
                .concat("-")
                .concat(PosCashBillTypeEnum.get(posCash.getBillType()).getDesc())
                .concat("-")
                .concat(PosCashBillStateEnum.get(posCash.getBillState()).getDesc());
        baseOutinService.saveStock(BaseOutinStockSaveVO.builder()
                .type(OutinTypeEnum.SELL_OUT.getCode())
                .sourceType(posCash.getOrderSource())
                .warehouseId(posCashProduct.getWarehouseId()).code(posCash.getCode())
                .billDate(LocalDate.now()).billState(0)
                .orgId(ContextUtil.getCurrentCompanyId())
                .remarks(desc).employeeId(ContextUtil.getEmployeeId())
                .outinProductList(Collections.singletonList(BaseOutinProductSaveVO.builder()
                        .isGift(false)
                        .name(posCashProduct.getProductName())
                        .productId(posCashProduct.getProductId())
                        .warehouseId(posCashProduct.getWarehouseId())
                        .num(num > 0 ? num : -num)
                        .desc((posCash.getType().equals(PosCashTypeEnum.START_TABLE.getCode())
                                ? posCash.getTableName() : PosCashTypeEnum.get(posCash.getType()).getDesc()
                        ).concat(num > 0 ? "-添加商品" : "-减少商品"))
                        .numType(num > 0 ? "2" : "1")
                        .price(posCashProduct.getPrice())
                        .attributeSetting(posCashProduct.getAttributeSetting())
                        .attributeSettingDesc(posCashProduct.getAttributeSettingDesc())
                        .cashProductId(posCashProduct.getId())
                        .build()))
                .build());

        if (!calcPriceService.isExistItem(posCash)) {
            baseOutinService.update(Wraps.<BaseOutin>lbU()
                    .set(BaseOutin::getBillState, 1)
                    .eq(BaseOutin::getBillState, 0)
                    .eq(BaseOutin::getType, OutinTypeEnum.SELL_OUT.getCode())
                    .eq(BaseOutin::getCode, posCash.getCode())
            );
            if (!(posCash.getType().equals(PosCashTypeEnum.SHOPPING.getCode())
                    && (posCash.getBillType().equals(PosCashBillTypeEnum.COUNTER_CHECKOUT.getCode())
                    || posCash.getBillType().equals(PosCashBillTypeEnum.REGISTRATION.getCode())))) {
                posCashManager.removeById(posCash.getId());
            }
        }
    }


    /**
     * 服务更新
     */
    protected abstract void serviceChange(BaseService baseService, PosCashService posCashService, Integer duration);

    /**
     * 验证查询明细参数
     */
    protected abstract PosCash getPosCash(PosCashCreateQuery query);

    /**
     * 验证查询明细参数
     */
    protected abstract PosCash createPosCash(PosCashCreateQuery query);


    /**
     * 查询服务明细参数(产品经理要求每次新增)
     */
    protected abstract PosCashService getPosCashService(ChangeNumQuery query);

    /**
     * 移除卡劵权益
     */
    protected abstract Boolean removeEquity(PosCash posCash);


    private void serviceActivity(Long activityId, PosCashService posCashService) {
        if (activityId != null) {
            //验证活动信息
            List<BaseServiceActivityResultVO> list = bizCacheService.cacheList(BaseServiceActivityResultVO.class, BizCacheEnum.BASE_SERVICE_ACTIVITY);
            list = list.stream().filter(v -> v.getId().equals(activityId)).collect(Collectors.toList());
            //判断是否包含服务项目
            list.removeIf(v -> {
                if (CollUtil.isEmpty(v.getServiceList())) {
                    return true;
                }
                return !v.getServiceList().contains(posCashService.getServiceId());
            });
            PosCash posCash = posCashManager.getById(posCashService.getCashId());
            ArgumentAssert.notNull(posCash, "订单不存在");
            //验证当前时间是否可用
            list.removeIf(v -> !availableTimeService.checkTime(v));
            if (CollUtil.isNotEmpty(list)) {
                list.removeIf(v -> !baseBizEquityService.checkEquity(v.getEquityList(), Collections.singletonList(posCash.getTableId())
                        , BaseEquityTypeEnum.TABLE.getCode()));
            }
            if (list.stream().anyMatch(v -> v.getId().equals(activityId))) {
                posCashService.setServiceActivityId(activityId);
                posCashService.setServiceActivityName(list.get(0).getName());
                //执行活动逻辑，暂停并重新计时
                calcPriceService.serviceActivity(list.get(0), posCash);
            } else {
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "服务活动不满足～");
            }
        }
    }

    /**
     * 添加缓存的key
     *
     * @param cashId
     */
    private void addCashChangeNumCache(Long cashId) {
        if (Objects.isNull(cashId)) {
            return;
        }
        CacheKey cacheKey = new CacheKey(RedisConstant.CASH_CHANGE_NUM + ContextUtil.getTenantId() + cashId);
        cacheKey.setExpire(Duration.ofMinutes(2));
        CacheResult<Object> objectCacheResult = cacheOps.get(cacheKey);
        if (ObjectUtil.isNotNull(objectCacheResult)) {
            if (objectCacheResult.isNullVal() || objectCacheResult.isNull()) {
                cacheOps.set(cacheKey, true);
            }
        }
    }
}
