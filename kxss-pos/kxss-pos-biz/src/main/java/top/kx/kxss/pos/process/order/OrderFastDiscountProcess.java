package top.kx.kxss.pos.process.order;

import cn.hutool.core.util.ObjectUtil;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.base.service.discount.BaseDiscountTemplateService;
import top.kx.kxss.base.vo.result.discount.DiscountTemplateSimpleResultVO;
import top.kx.kxss.model.enumeration.pos.DiscountTypeEnum;
import top.kx.kxss.pos.process.order.sub.OrderFastDiscountCalProcess;
import top.kx.kxss.pos.process.order.sub.OrderFastReductionCalProcess;
import top.kx.kxss.pos.slot.DetailCalcContext;
import top.kx.kxss.pos.slot.PriceCalcContext;

/**
 * 快捷优惠计算处理
 *
 * <AUTHOR>
 */
@Component("orderFastDiscountProcess")
@Slf4j
public class OrderFastDiscountProcess extends NodeComponent {

    @Autowired
    private BaseDiscountTemplateService baseDiscountTemplateService;
    @Autowired
    private OrderFastDiscountCalProcess orderFastDiscountCalProcess;
    @Autowired
    private OrderFastReductionCalProcess orderFastReductionCalProcess;

    @Override
    public void process() throws Exception {
        PriceCalcContext context = this.getContextBean(PriceCalcContext.class);
        PosCash posCash = context.getPosCash();

        DiscountTemplateSimpleResultVO template = baseDiscountTemplateService.getSimpleDetail(posCash.getDiscountTemplateId());
        ArgumentAssert.notNull(template, "优惠方式不存在");
        if (template.getIsSuperpose() != null && template.getIsSuperpose()) {
            DetailCalcContext detailContext = this.getContextBean(DetailCalcContext.class);
//        //子逻辑，按照不同折扣类型计算
            if (ObjectUtil.equal(posCash.getDiscountType(), DiscountTypeEnum.FAST_DISCOUNT_DISCOUNT.getCode())) {
                orderFastDiscountCalProcess.process(context, detailContext);
            } else if (ObjectUtil.equal(posCash.getDiscountType(), DiscountTypeEnum.FAST_DISCOUNT_REDUCTION.getCode())) {
                orderFastReductionCalProcess.process(context, detailContext);
            }
        }
    }

    @Override
    public boolean isAccess() {
        PriceCalcContext context = this.getContextBean(PriceCalcContext.class);
        PosCash posCash = context.getPosCash();
        //这一步必须有，否则线程池中无法确定数据源
        context.setContextUtil(context);
        return ObjectUtil.isNotNull(posCash.getDiscountType()) &&
                (ObjectUtil.equal(posCash.getDiscountType(), DiscountTypeEnum.FAST_DISCOUNT_DISCOUNT.getCode())
                        || ObjectUtil.equal(posCash.getDiscountType(), DiscountTypeEnum.FAST_DISCOUNT_REDUCTION.getCode()));
    }
}
