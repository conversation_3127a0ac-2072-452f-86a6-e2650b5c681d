package top.kx.kxss.pos.process.fastdiscount;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.base.service.biz.BaseBizAvailableTimeService;
import top.kx.kxss.base.service.discount.BaseDiscountTemplateService;
import top.kx.kxss.base.vo.result.discount.DiscountTemplateSimpleResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.base.EquityTypeEnum;
import top.kx.kxss.model.enumeration.pos.DiscountCalTypeEnum;
import top.kx.kxss.model.enumeration.pos.DiscountTypeEnum;
import top.kx.kxss.pos.bean.CalcFastDiscountQuery;
import top.kx.kxss.pos.slot.CalcFastDiscountContext;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 数据处理
 *
 * <AUTHOR>
 */
@Component("fastDiscountProductDataProcess")
@Slf4j
@DS(DsConstant.BASE_TENANT)
public class FastDiscountProductDataProcess extends NodeComponent {

    @Autowired
    private BaseBizAvailableTimeService baseBizAvailableTimeService;
    @Autowired
    private BaseDiscountTemplateService baseDiscountTemplateService;

    @Override
    public void process() {
        Integer index = this.getLoopIndex();
        CalcFastDiscountQuery query = this.getSlot().getRequestData();
        DiscountTemplateSimpleResultVO discountTemplateVO = query.getDiscountTemplateList().get(index);
        CalcFastDiscountContext fastDiscountContext = this.getContextBean(CalcFastDiscountContext.class);
        Map<Long, DiscountTemplateSimpleResultVO> discountTemplateMap = fastDiscountContext.getDiscountTemplateMap();
        List<PosCashProduct> productList = fastDiscountContext.getProductList();
        List<Long> longList = productList.stream().map(PosCashProduct::getProductId).collect(Collectors.toList());
        Map<Long, Boolean> bizIdMatchDiscount = baseDiscountTemplateService.checkBizIdMatchDiscount(discountTemplateVO,
                longList, EquityTypeEnum.PRODUCT);
        BigDecimal discountValue = discountTemplateVO.getDiscountValue();
        for (PosCashProduct cashDetail : productList) {
            cashDetail.setFastDiscountCalcAmount(BigDecimal.ZERO);
            cashDetail.setIsPay(false);
            cashDetail.setAssessedAmount(ObjectUtil.defaultIfNull(cashDetail.getAssessedAmount(), BigDecimal.ZERO));
            cashDetail.setPaid(ObjectUtil.defaultIfNull(cashDetail.getPaid(), BigDecimal.ZERO));
            if (cashDetail.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            if (cashDetail.getAmount().compareTo(cashDetail.getPaid()) <= 0) {
                continue;
            }
            if (cashDetail.getIsDiscount() != null && !cashDetail.getIsDiscount()) {
                continue;
            }
            BigDecimal amount = cashDetail.getAmount().subtract(cashDetail.getAssessedAmount()).subtract(cashDetail.getPaid());
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            //判断权益
            if (Objects.equals(bizIdMatchDiscount.get(cashDetail.getProductId()), false)) {
                continue;
            }
            boolean isAvailable = true;
            //判断时间是不是可用
            LocalDateTime startTime = cashDetail.getCreatedTime();
            if (baseBizAvailableTimeService.checkAvailableTime(discountTemplateVO, startTime)) {
                discountTemplateMap.put(discountTemplateVO.getId(), discountTemplateVO);
                isAvailable = true;
            }
            if (!isAvailable) {
                continue;
            }
            if (discountTemplateVO.getDiscountType().equals(DiscountCalTypeEnum.REDUCTION.getCode())
                    && discountTemplateVO.getDiscountValue().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            cashDetail.setIsPay(true);
            BigDecimal tableAmount = cashDetail.getPrice().multiply(new BigDecimal(cashDetail.getNum()));
            if (discountTemplateVO.getIsSuperpose() != null
                    && discountTemplateVO.getIsSuperpose()
                    && cashDetail.getDiscountType().equals(DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                tableAmount = amount;
                if (discountTemplateVO.getDiscountType().equals(DiscountCalTypeEnum.DISCOUNT.getCode())) {
                    BigDecimal discount = discountTemplateVO.getDiscountValue()
                            .divide(BigDecimal.TEN).setScale(2, RoundingMode.HALF_UP);
                    BigDecimal bigDecimal = tableAmount.multiply(discount)
                            .setScale(2, RoundingMode.HALF_UP);
                    BigDecimal subtract = tableAmount.subtract(bigDecimal);
                    if (subtract.compareTo(tableAmount) > 0) {
                        subtract = tableAmount;
                    }
                    cashDetail.setFastDiscountAssessedAmount(subtract);
                }
            }
            if (discountTemplateVO.getDiscountType().equals(DiscountCalTypeEnum.REDUCTION.getCode())) {
                if (tableAmount.compareTo(discountTemplateVO.getDiscountValue()) >= 0) {
                    tableAmount = discountTemplateVO.getDiscountValue();
                    discountTemplateVO.setDiscountValue(BigDecimal.ZERO);
                } else {
                    discountTemplateVO.setDiscountValue(discountTemplateVO.getDiscountValue().subtract(tableAmount));
                }
            }
            if (discountTemplateVO.getTotalAmount() == null) {
                discountTemplateVO.setTotalAmount(BigDecimal.ZERO);
            }
            discountTemplateVO.setTotalAmount(discountTemplateVO.getTotalAmount().add(tableAmount));
            cashDetail.setFastDiscountCalcAmount(tableAmount);
            cashDetail.setDiscountTemplateId(discountTemplateVO.getId());
            cashDetail.setFastDiscountType(discountTemplateVO.getDiscountType());
            cashDetail.setIsFastDiscountSuperpose(discountTemplateVO.getIsSuperpose() != null && discountTemplateVO.getIsSuperpose());
            //不参与折上折 覆盖之前优惠
            if (discountTemplateVO.getIsSuperpose() == null || !discountTemplateVO.getIsSuperpose()
                    || !ObjectUtil.equal(cashDetail.getDiscountType(), DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                cashDetail.setDiscountType(ObjectUtil.equal(discountTemplateVO.getDiscountType(), DiscountCalTypeEnum.REDUCTION.getCode())
                        ? DiscountTypeEnum.FAST_DISCOUNT_REDUCTION.getCode() : DiscountTypeEnum.FAST_DISCOUNT_DISCOUNT.getCode());
                cashDetail.setDiscount(discountValue);
            } else {
                if (ObjectUtil.isNotNull(cashDetail.getDiscountTemplateId())) {
                    cashDetail.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                    cashDetail.setDiscount(BigDecimal.ZERO);
                }
            }

        }
    }

    @Override
    public boolean isAccess() {
        CalcFastDiscountContext fastDiscountContext = this.getContextBean(CalcFastDiscountContext.class);
        return CollUtil.isNotEmpty(fastDiscountContext.getProductList());
    }
}
