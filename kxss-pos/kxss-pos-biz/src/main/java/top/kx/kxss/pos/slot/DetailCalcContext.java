package top.kx.kxss.pos.slot;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import com.google.common.collect.Lists;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.PosCashPackField;
import top.kx.kxss.app.entity.cash.PosCashStop;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.entity.thail.PosCashThail;
import top.kx.kxss.app.query.flow.FlowCommonContext;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.power.BasePowerCharging;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.entity.tableCharging.setting.BaseTableChargingSetting;
import top.kx.kxss.base.vo.result.card.BaseCardResultVO;
import top.kx.kxss.base.vo.result.member.card.MemberCardResultVO;
import top.kx.kxss.base.vo.result.member.grade.MemberGradeResultVO;
import top.kx.kxss.base.vo.result.product.BaseProductResultVO;
import top.kx.kxss.base.vo.result.service.activity.BaseServiceActivityResultVO;
import top.kx.kxss.base.vo.result.thail.BaseThailResultVO;
import top.kx.kxss.pos.entity.cash.PosCashCard;
import top.kx.kxss.pos.entity.cash.PosCashPower;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "DetailCalcContext", description = "明细信息")
public class DetailCalcContext extends FlowCommonContext {
    /**
     * 台费明细
     */
    @ApiModelProperty(value = "台费明细")
    private List<PosCashTable> tableList;

    /**
     * 台费明细
     */
    @ApiModelProperty(value = "包台费明细")
    private List<PosCashTable> packFieldTableList = Lists.newArrayList();

    @ApiModelProperty(value = "台费删除明细")
    private List<Long> tableRemoveList;
    /**
     * 商品明细
     */
    @ApiModelProperty(value = "商品明细")
    private List<PosCashProduct> productList;
    /**
     * 服务明细
     */
    @ApiModelProperty(value = "服务明细")
    private List<PosCashService> serviceList;
    /**
     * 卡明细
     */
    @ApiModelProperty(value = "卡明细")
    private List<PosCashCard> buyCardList;
    /**
     * 充电明细
     */
    @ApiModelProperty(value = "充电明细")
    private List<PosCashPower> powerList;

    @ApiModelProperty(value = "订单信息", hidden = true)
    @JsonIgnore
    private PosCash posCash;

    @ApiModelProperty(value = "会员信息", hidden = true)
    @JsonIgnore
    private MemberInfo memberInfo;

    @ApiModelProperty(value = "订单信息", hidden = true)
    @JsonIgnore
    private Boolean isStop;

    @ApiModelProperty(value = "计费信息", hidden = true)
    @JsonIgnore
    private Map<Long, List<BaseTableChargingSetting>> chargingSettingMap;

    /**
     * 套餐信息
     */
    @ApiModelProperty(value = "套餐信息", hidden = true)
    @JsonIgnore
    private List<PosCashThail> thailList;
    /**
     * 商品信息
     */
    @ApiModelProperty(value = "商品信息")
    private Map<Long, BaseProductResultVO> productMap;
    /**
     * 套餐信息
     */
    @ApiModelProperty(value = "套餐信息")
    private Map<Long, BaseThailResultVO> baseThailMap;


    @ApiModelProperty(value = "支付明细")
    private List<PosCashPayment> posCashPaymentList;
    /**
     * 卡信息
     */
    @ApiModelProperty(value = "卡信息")
    private Map<Long, BaseCardResultVO> cardMap;

    @ApiModelProperty(value = "会员绑定权益卡信息")
    private MemberCardResultVO memberCardResultVO;

//    @ApiModelProperty(value = "包场信息")
//    private PosCashPackField packField;
    /**
     * 台桌信息
     */
    @ApiModelProperty(value = "台桌信息", hidden = true)
    private BaseTableInfo tableInfo;

    @ApiModelProperty(value = "包台信息")
    private List<PosCashPackField> packFieldList;

    /**
     * 套餐台桌明细
     */
    @ApiModelProperty(value = "套餐台桌明细")
    private List<PosCashTable> thailTableList;
    /**
     * 套餐商品明细
     */
    @ApiModelProperty(value = "套餐商品明细")
    private List<PosCashProduct> thailProductList;
    /**
     * 服务明细
     */
    @ApiModelProperty(value = "套餐服务明细")
    private List<PosCashService> thailServiceList;
    /**
     * 明细停止记录
     */
    @ApiModelProperty(value = "明细停止记录")
    private List<PosCashStop> detailStopList;

    @ApiModelProperty(value = "服务停止")
    private Boolean isServiceStop;

    @ApiModelProperty(value = "服务活动信息")
    private Map<Long, BaseServiceActivityResultVO> serviceActivityMap;

    /**
     * 充电配置
     */
    @ApiModelProperty(value = "充电配置")
    private BasePowerCharging powerCharging;

    @ApiModelProperty(value = "充电停止")
    private Boolean isNotPowerStop;

    @ApiModelProperty(value = "是否叠加赠金支付")
    private Boolean isGiftPay;

    /**
     * 会员等级信息
     */
    @ApiModelProperty(value = "会员等级信息", hidden = true)
    private MemberGradeResultVO memberGradeResultVO;


}
