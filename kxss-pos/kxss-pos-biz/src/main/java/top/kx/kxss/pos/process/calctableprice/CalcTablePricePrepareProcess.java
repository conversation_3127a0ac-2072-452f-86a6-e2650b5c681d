package top.kx.kxss.pos.process.calctableprice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.service.bizcache.BizCacheService;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.entity.tableCharging.BaseTableCharging;
import top.kx.kxss.base.entity.tableCharging.BaseTableChargingBeforeGrade;
import top.kx.kxss.base.entity.tableCharging.grade.BaseTableChargingSettingGrade;
import top.kx.kxss.base.entity.tableCharging.setting.BaseTableChargingSetting;
import top.kx.kxss.base.entity.tableCharging.week.BaseTableChargingSettingWeek;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.vo.result.member.grade.MemberGradeResultVO;
import top.kx.kxss.common.constant.BizConstant;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.BizCacheEnum;
import top.kx.kxss.model.enumeration.base.NoDiscountTypeEnum;
import top.kx.kxss.pos.slot.CalcTablePriceContext;

import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 计算台桌价格准备信息组件
 *
 * <AUTHOR>
 */
@Component("calcTablePricePrepareProcess")
@Slf4j
@DS(DsConstant.BASE_TENANT)
public class CalcTablePricePrepareProcess extends NodeComponent {

    @Autowired
    private BizCacheService bizCacheService;
    @Autowired
    private MemberInfoService memberInfoService;

    @Override
    public void process() throws Exception {
        CalcTablePriceContext tablePriceContext = this.getContextBean(CalcTablePriceContext.class);
        //拿到订单对应台桌的计费规则
        //消费前价格
        //1.会员等级价格
        //2.周末价格
        //匹配时间段，获取单价
        //获取计费周期
        //体验时长
        //起步价
        //封顶价
        //台桌
        tablePriceContext.setIsStop(tablePriceContext.getIsStop() != null && tablePriceContext.getIsStop());
        PosCash posCash = tablePriceContext.getPosCash();
        List<BaseTableInfo> tableInfoList = bizCacheService.cacheList(BaseTableInfo.class, BizCacheEnum.BASE_TABLE_INFO);

        List<Long> tableIds = tablePriceContext.getCashTableList().stream().map(PosCashTable::getTableId).distinct().collect(Collectors.toList());
        tableInfoList = tableInfoList.stream().filter(v -> tableIds.contains(v.getId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(tableInfoList)) {
            tableInfoList = Lists.newArrayList();
        }
        Map<Long, BaseTableInfo> tableInfoMap = tableInfoList
                .stream().collect(Collectors.toMap(BaseTableInfo::getId, k -> k));
        tablePriceContext.setTableInfoMap(tableInfoMap);
        //台桌计费
        List<String> tableTypes = tableInfoMap.values().stream().map(BaseTableInfo::getTableType).collect(Collectors.toList());
        List<BaseTableCharging> baseTableChargingList = bizCacheService.cacheList(BaseTableCharging.class, BizCacheEnum.BASE_TABLE_CHARGING);
        baseTableChargingList = baseTableChargingList.stream().filter(v -> tableTypes.contains(v.getTableType()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(baseTableChargingList)) {
            baseTableChargingList = Lists.newArrayList();
        }
        //订单是否设置会员
        Long gradeId;
        boolean isExpired = false;
        BigDecimal totalAccountAmount = BigDecimal.ZERO;
        if (posCash.getMemberId() != null) {
            MemberInfo memberInfo = memberInfoService.getById(posCash.getMemberId());
            if (ObjectUtil.isNotNull(memberInfo)) {
                memberInfo.setRechargeAmount(Optional.ofNullable(memberInfo.getRechargeAmount()).orElse(BigDecimal.ZERO));
                memberInfo.setGiftAmount(Optional.ofNullable(memberInfo.getGiftAmount()).orElse(BigDecimal.ZERO));
                totalAccountAmount = memberInfo.getRechargeAmount().add(memberInfo.getGiftAmount());
                isExpired = memberInfoService.isExpire(memberInfo);
                gradeId = memberInfo.getGradeId();
            } else {
                gradeId = BizConstant.DEFAULT_GRADE;
                posCash.setMemberId(null);
            }
        } else {
            gradeId = BizConstant.DEFAULT_GRADE;
        }
        Boolean isMemberPrice = tablePriceContext.getIsMemberPrice();
        if (isMemberPrice == null
                && ObjectUtil.isNotNull(tablePriceContext.getMemberGradeResultVO())
        ) {
            MemberGradeResultVO memberGradeResultVO = tablePriceContext.getMemberGradeResultVO();
            List<String> noDiscountType = ObjectUtil.isNotNull(memberGradeResultVO)
                    ? memberGradeResultVO.getNoDiscountRange() : Lists.newArrayList();
            //余额为零不享受会员价
            if (CollUtil.isNotEmpty(noDiscountType)
                    && noDiscountType.contains(NoDiscountTypeEnum.TABLE_PRICE.getCode())
                    && "1".equalsIgnoreCase(memberGradeResultVO.getDiscountSetting())
                    && posCash.getMemberId() != null
                    && totalAccountAmount.compareTo(BigDecimal.ZERO) == 0) {
                isMemberPrice = false;
            }
        }
        if (isMemberPrice == null) {
            isMemberPrice = true;
        }
        if(posCash.getIsMemberDiscount()!=null && !posCash.getIsMemberDiscount()){
            isMemberPrice = false;
        }
        List<Long> collect = baseTableChargingList.stream().map(BaseTableCharging::getId).collect(Collectors.toList());
        List<BaseTableChargingBeforeGrade> beforeGradeList = bizCacheService.cacheList(BaseTableChargingBeforeGrade.class, BizCacheEnum.BASE_TABLE_CHARGING_BEFORE_GRADE);
        beforeGradeList = beforeGradeList.stream().filter(v -> collect.contains(v.getChargingId()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(beforeGradeList)) {
            beforeGradeList = Lists.newArrayList();
        }
        //计费消费前几分钟计费
        Map<String, BaseTableChargingBeforeGrade> beforeGradeMap = beforeGradeList.stream().collect(Collectors.toMap(v -> v.getChargingId() + "_" +
                v.getGradeId(), Function.identity()));
        for (BaseTableCharging baseTableCharging : baseTableChargingList) {
            baseTableCharging.setConsumeCustomerPrice(baseTableCharging.getConsumeCustomerPrice() == null ? BigDecimal.ZERO : baseTableCharging.getConsumeCustomerPrice());
            baseTableCharging.setOldConsumeCustomerPrice(baseTableCharging.getConsumeCustomerPrice());
            baseTableCharging.setIsConsumeDiscount(baseTableCharging.getIsConsumeDiscount() != null && baseTableCharging.getIsConsumeDiscount());
            baseTableCharging.setConsumeDuration(baseTableCharging.getConsumeDuration() == null ? 0 : baseTableCharging.getConsumeDuration());
            if (isMemberPrice != null && !isMemberPrice) {
                continue;
            }
            if (ObjectUtil.isNotNull(posCash.getMemberId()) && !isExpired) {
                BaseTableChargingBeforeGrade beforeGrade = beforeGradeMap.get(baseTableCharging.getId() + "_" + gradeId);
                if (ObjectUtil.isNull(beforeGrade)) {
                    continue;
                }
                baseTableCharging.setConsumeCustomerPrice(beforeGrade.getCustomerPrice());
            }
        }
        Map<String, BaseTableCharging> tableChargingMap = baseTableChargingList
                .stream().collect(Collectors.toMap(BaseTableCharging::getTableType, k -> k));
        tablePriceContext.setTableChargingMap(tableChargingMap);
        //计时中的台桌 非计时中的台桌，不做重复计算
        List<Long> chargingIds = tableChargingMap.values().stream().map(BaseTableCharging::getId).collect(Collectors.toList());
        List<BaseTableChargingSetting> chargingSettings = bizCacheService.cacheList(BaseTableChargingSetting.class, BizCacheEnum.BASE_TABLE_CHARGING_SETTING);
        chargingSettings = chargingSettings.stream().filter(v -> chargingIds.contains(v.getChargingId()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(chargingSettings)) {
            chargingSettings = Lists.newArrayList();
        }
        List<Long> chargingSettingIds = chargingSettings.stream().map(BaseTableChargingSetting::getId).collect(Collectors.toList());
        //处理订单是是否绑定会员
        //更新会员等级计费规则单价信息
        Map<String, BaseTableChargingSettingGrade> settingGradeMap;
        if (CollUtil.isNotEmpty(chargingSettings)) {
            tablePriceContext.setGradeId(gradeId);
            List<BaseTableChargingSettingGrade> chargingSettingGradeList = bizCacheService
                    .cacheList(BaseTableChargingSettingGrade.class, BizCacheEnum.BASE_TABLE_CHARGING_SETTING_GRADE);

            chargingSettingGradeList = chargingSettingGradeList.stream().filter(v ->
                            chargingSettingIds.contains(v.getChargingSettingId()) && v.getGradeId().equals(gradeId))
                    .collect(Collectors.toList());
            settingGradeMap = chargingSettingGradeList
                    .stream().collect(Collectors.toMap(v -> v.getChargingId() + "_"
                            + v.getChargingSettingId() + "_"
                            + gradeId, Function.identity()));
            tablePriceContext.setSettingGradeMap(settingGradeMap);
            for (BaseTableChargingSetting chargingSetting : chargingSettings) {
                chargingSetting.setOldCustomerPrice(chargingSetting.getCustomerPrice());
                if (isMemberPrice != null && !isMemberPrice) {
                    continue;
                }
                if (ObjectUtil.isNotNull(posCash.getMemberId()) && !isExpired) {
                    BaseTableChargingSettingGrade settingGrade = settingGradeMap.get(chargingSetting.getChargingId() + "_"
                            + chargingSetting.getId() + "_"
                            + gradeId);
                    if (ObjectUtil.isNull(settingGrade)) {
                        continue;
                    }
                    chargingSetting.setCustomerPrice(settingGrade.getCustomerPrice());
//                    chargingSetting.setOldCustomerPrice(settingGrade.getCustomerPrice());
                }
            }
            //星期
            DayOfWeek dayOfWeek = posCash.getCreatedTime().getDayOfWeek();
            int week = dayOfWeek.getValue();
            List<BaseTableChargingSettingWeek> chargingSettingWeekList = bizCacheService
                    .cacheList(BaseTableChargingSettingWeek.class, BizCacheEnum.BASE_TABLE_CHARGING_SETTING_WEEK);
            chargingSettingWeekList = chargingSettingWeekList.stream().filter(v ->
                            chargingSettingIds.contains(v.getChargingSettingId())
                                    && v.getGradeId().equals(gradeId)
                                    && v.getWeek().equals(week))
                    .collect(Collectors.toList());

            Map<String, BaseTableChargingSettingWeek> settingWeekMap = chargingSettingWeekList
                    .stream().collect(Collectors.toMap(v -> v.getWeek() + "_"
                            + v.getChargingId() + "_"
                            + v.getChargingSettingId() + "_" + v.getGradeId(), Function.identity()));
            for (BaseTableChargingSetting chargingSetting : chargingSettings) {
                BaseTableChargingSettingWeek settingWeek = settingWeekMap.get(week + "_"
                        + chargingSetting.getChargingId() + "_" + chargingSetting.getId() + "_" + gradeId);
                if (ObjectUtil.isNull(settingWeek)) {
                    continue;
                }
                chargingSetting.setCustomerPrice(settingWeek.getCustomerPrice());
                chargingSetting.setOldCustomerPrice(settingWeek.getCustomerPrice());
            }
        }
        Map<Long, List<BaseTableChargingSetting>> tableChargingSettingMap = chargingSettings
                .stream().collect(Collectors.groupingBy(BaseTableChargingSetting::getChargingId));
        tablePriceContext.setChargingSettingMap(tableChargingSettingMap);
        if (CollUtil.isEmpty(tablePriceContext.getSettingGradeMap())) {
            tablePriceContext.setSettingGradeMap(MapUtil.newHashMap());
        }
        //合并订单
        for (PosCashTable v : tablePriceContext.getCashTableList()) {
            v.setIsMerge(v.getIsMerge() != null && v.getIsMerge());
            if (ObjectUtil.isNull(v.getMergeCashId()) && v.getIsMerge()) {
                v.setIsMerge(false);
            }
        }
    }

    @Override
    public boolean isAccess() {
        CalcTablePriceContext context = this.getContextBean(CalcTablePriceContext.class);
        context.setContextUtil(context);
        return ObjectUtil.isNotNull(context.getPosCash());
    }
}
