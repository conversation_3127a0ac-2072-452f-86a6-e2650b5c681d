package top.kx.kxss.pos.service.table.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.flow.LiteflowResponse;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.exception.BizException;
import top.kx.basic.exception.code.ExceptionCode;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.jackson.JsonUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.PosCashCommenter;
import top.kx.kxss.app.entity.cash.PosCashPackField;
import top.kx.kxss.app.entity.cash.PosCashStop;
import top.kx.kxss.app.entity.cash.equity.PosCashEquity;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.entity.thail.PosCashThail;
import top.kx.kxss.app.service.cash.PosCashCommenterService;
import top.kx.kxss.app.service.cash.PosCashPackFieldService;
import top.kx.kxss.app.service.cash.PosCashServiceService;
import top.kx.kxss.app.service.cash.PosCashStopService;
import top.kx.kxss.app.service.cash.equity.PosCashEquityService;
import top.kx.kxss.app.service.cash.payment.PosCashPaymentService;
import top.kx.kxss.app.service.cash.product.PosCashProductService;
import top.kx.kxss.app.service.cash.table.PosCashTableService;
import top.kx.kxss.app.service.thail.PosCashThailService;
import top.kx.kxss.app.statemachine.PosCashStateManager;
import top.kx.kxss.app.utils.DateUtils;
import top.kx.kxss.app.vo.IdQuery;
import top.kx.kxss.app.vo.query.cash.KitchenPrintQuery;
import top.kx.kxss.app.vo.query.cash.PosCashIdQuery;
import top.kx.kxss.base.entity.biz.BaseBizLog;
import top.kx.kxss.base.entity.card.BaseCard;
import top.kx.kxss.base.entity.job.BaseJobInfo;
import top.kx.kxss.base.entity.outin.BaseOutin;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.service.BaseServicePersonal;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.entity.tableCharging.BaseTableCharging;
import top.kx.kxss.base.entity.tableCharging.setting.BaseTableChargingSetting;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.service.card.BaseCardService;
import top.kx.kxss.base.service.job.BaseJobInfoService;
import top.kx.kxss.base.service.outin.BaseOutinService;
import top.kx.kxss.base.service.product.BaseProductService;
import top.kx.kxss.base.service.service.BaseServicePersonalService;
import top.kx.kxss.base.service.table.BaseTableInfoService;
import top.kx.kxss.base.service.tableCharging.BaseTableChargingService;
import top.kx.kxss.base.service.tableCharging.setting.BaseTableChargingSettingService;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.vo.query.SaveOrUpdateJobQuery;
import top.kx.kxss.base.vo.save.outin.BaseOutinProductSaveVO;
import top.kx.kxss.base.vo.save.outin.BaseOutinStockSaveVO;
import top.kx.kxss.base.vo.update.table.BaseTableInfoUpdateVO;
import top.kx.kxss.common.api.HelperApi;
import top.kx.kxss.common.constant.BizConstant;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.ParameterKey;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.model.constant.EchoDictType;
import top.kx.kxss.model.enumeration.app.CashTableStatusEnum;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.model.enumeration.base.*;
import top.kx.kxss.model.enumeration.pos.DiscountTypeEnum;
import top.kx.kxss.model.enumeration.pos.JobTypeEnum;
import top.kx.kxss.pos.bean.PaymentRevokeQuery;
import top.kx.kxss.pos.bean.PriceCalcQuery;
import top.kx.kxss.pos.entity.cash.PosCashCard;
import top.kx.kxss.pos.entity.cash.PosCashPower;
import top.kx.kxss.pos.query.order.CancelDiscountQuery;
import top.kx.kxss.pos.query.product.*;
import top.kx.kxss.pos.service.CalcPriceService;
import top.kx.kxss.pos.service.cash.PosCashCardService;
import top.kx.kxss.pos.service.cash.PosCashPowerService;
import top.kx.kxss.pos.service.noticedevice.WsNoticeDeviceService;
import top.kx.kxss.pos.service.order.CheckOrderService;
import top.kx.kxss.pos.service.order.OrderService;
import top.kx.kxss.pos.service.table.TableOperateService;
import top.kx.kxss.pos.slot.DetailCalcContext;
import top.kx.kxss.pos.slot.PriceCalcContext;
import top.kx.kxss.pos.slot.TrendsPaymentContext;
import top.kx.kxss.pos.vo.ItemRemarksResultVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service("posTableOperateService")
@DS(DsConstant.BASE_TENANT)
public class TableOperateServiceImpl implements TableOperateService {

    @Autowired
    private FlowExecutor flowExecutor;
    @Autowired
    private BaseProductService baseProductService;
    @Autowired
    private PosCashServiceService cashService;
    @Autowired
    private PosCashProductService posCashProductService;
    @Autowired
    private top.kx.kxss.app.service.cash.service.PosCashServiceService posCashServiceService;
    @Autowired
    private BaseServicePersonalService baseServicePersonalService;
    @Autowired
    private PosCashTableService posCashTableService;
    @Autowired
    private PosCashThailService posCashThailService;
    @Autowired
    private PosCashPowerService posCashPowerService;
    @Autowired
    private CalcPriceService calcPriceService;
    @Autowired
    private CheckOrderService checkOrderService;
    @Autowired
    private BaseBizLogService baseBizLogService;
    @Autowired
    private BaseJobInfoService baseJobInfoService;
    @Autowired
    private BaseTableChargingService baseTableChargingService;
    @Autowired
    private HelperApi helperApi;
    @Autowired
    private EchoService echoService;
    @Autowired
    private PosCashEquityService posCashEquityService;
    @Autowired
    private BaseOutinService baseOutinService;
    @Autowired
    private PosCashCardService posCashCardService;
    @Autowired
    private BaseCardService baseCardService;
    @Autowired
    private BaseEmployeeService baseEmployeeService;
    @Autowired
    private PosCashCommenterService posCashCommenterService;
    @Autowired
    private BaseTableInfoService baseTableInfoService;
    @Autowired
    private PosCashPackFieldService posCashPackFieldService;
    @Autowired
    private WsNoticeDeviceService wsNoticeDeviceService;
    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private PosCashPaymentService posCashPaymentService;
    @Autowired
    private PosCashStateManager cashStateManager;
    @Autowired
    private BaseTableChargingSettingService baseTableChargingSettingService;
    @Autowired
    private PosCashStopService posCashStopService;
    @Autowired
    private OrderService orderService;


    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean del(ItemDeleteQuery query) {
        boolean lock = false;
        try {
            lock = distributedLock.lock(query.getPosCashId() + PosCashConstant.Event.ORDER.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            PosCash posCash = cashService.getById(query.getPosCashId());
            checkOrderService.checkItemPosCash(posCash);
            checkOrderService.checkOrderDiscountPosCash(posCash);
            long existItemCount = calcPriceService.existItemCount(posCash);
            if (existItemCount <= 1) {
                // 判断是否存在支付记录
                List<PosCashPayment> cashPaymentList = posCashPaymentService.list(Wraps.<PosCashPayment>lbQ()
                        .eq(PosCashPayment::getCashId, posCash.getId())
                        .eq(PosCashPayment::getDeleteFlag, 0)
                        .eq(PosCashPayment::getStatus, PosCashPaymentStatusEnum.PAY_SUCCESS.getCode()));
                ArgumentAssert.isTrue(CollUtil.isEmpty(cashPaymentList), "订单已支付，请撤销支付后再试！");

            }
            switch (EquityTypeEnum.get(query.getType())) {
                //台费
                case TABLE:
                    if (ObjectUtil.isNull(posCash.getThailId())) {
                        ArgumentAssert.isFalse(posCashTableService.count(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, posCash.getId())) == 1, "至少保留一条台费明细");
                    }
                    PosCashTable posCashTable = posCashTableService.getById(query.getId());
                    ArgumentAssert.notNull(posCashTable, "请刷新后重试！");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.TIMING.getCode()), "请先停止计时再操作");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.REFUND.getCode()), "请先取消退单再操作");
                    //启用包场后，无法删除
//                    ArgumentAssert.isFalse(posCashTable.getIsPackField() != null && posCashTable.getIsPackField(),
//                            "启用包场后，无法删除");
                    posCashTableService.removeByIds(Collections.singleton(posCashTable.getId()));
                    //新增操作日志
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description("删除【" + posCashTable.getTableName() + EquityTypeEnum.get(query.getType()).getDesc()
                                    + "(" + posCashTable.getDuration() + "分钟)" + posCashTable.getAmount()
                                    .setScale(2, RoundingMode.HALF_UP).toPlainString() + "元" + "】")
                            .bizModule(BizLogModuleEnum.TABLE_ITEM_DEL.getCode()).type(BizLogTypeEnum.DELETE.getCode())
                            .sourceId(posCash.getId()).remarks("")
                            .build());
                    break;
                case SERVICE:
                    //服务
                    PosCashService posCashService = posCashServiceService.getById(query.getId());
                    ArgumentAssert.notNull(posCashService, "请刷新后重试！");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashService.getStatus(), CashTableStatusEnum.TIMING.getCode()), "请先停止后再操作");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashService.getStatus(), CashTableStatusEnum.REFUND.getCode()),
                            "退单不可操作");
                    posCashServiceService.removeById(posCashService.getId());
                    //验证当前是否在忙碌中
                    long count = posCashServiceService.count(Wraps.<PosCashService>lbQ().eq(PosCashService::getServiceId, posCashService.getServiceId())
                            .eq(PosCashService::getStatus, CashTableStatusEnum.TIMING.getCode())
                            .eq(PosCashService::getEmployeeId, posCashService.getEmployeeId()));
                    if (count == 0) {
                        baseServicePersonalService.update(Wraps.<BaseServicePersonal>lbU()
                                .set(BaseServicePersonal::getStatus, ServiceStatus.UNUSED.getCode())
                                .eq(BaseServicePersonal::getDeleteFlag, 0)
                                .eq(BaseServicePersonal::getServiceId, posCashService.getServiceId())
                                .eq(BaseServicePersonal::getEmployeeId, posCashService.getEmployeeId()));
                    }
                    //新增操作日志
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description("删除" + EquityTypeEnum.get(query.getType()).getDesc()
                                    + "【" + posCashService.getEmployeeName()
                                    + "(" + posCashService.getDuration() + "分钟)" + posCashService.getAmount()
                                    .setScale(2, RoundingMode.HALF_UP).toPlainString() + "元" + "】")
                            .bizModule(BizLogModuleEnum.ITEM_DEL.getCode()).type(BizLogTypeEnum.DELETE.getCode())
                            .sourceId(posCash.getId()).remarks("")
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                case PRODUCT:
                    //商品
                    PosCashProduct posCashProduct = posCashProductService.getById(query.getId());
                    ArgumentAssert.notNull(posCashProduct, "请刷新后重试！");
                    if (posCash.getType().equals(PosCashTypeEnum.SHOPPING.getCode()) &&
                            (posCash.getBillType().equals(PosCashBillTypeEnum.COUNTER_CHECKOUT.getCode())
                                    || posCash.getBillType().equals(PosCashBillTypeEnum.REGISTRATION.getCode())
                            )) {
//                        ArgumentAssert.isFalse(posCashProductService.count(Wraps.<PosCashProduct>lbQ()
//                                .isNull(PosCashProduct::getCashThailId)
//                                .eq(PosCashProduct::getDeleteFlag, 0)
//                                .eq(PosCashProduct::getCashId, posCash.getId())) == 1, "至少保留一条商品明细");
                    }
                    BaseProduct baseProduct = baseProductService.getById(posCashProduct.getProductId());
                    ArgumentAssert.notNull(baseProduct, "商品不存在");
                    //更新库存
                    String desc = PosCashTypeEnum.get(posCash.getType()).getDesc()
                            .concat("-")
                            .concat(PosCashBillTypeEnum.get(posCash.getBillType()).getDesc())
                            .concat("-")
                            .concat(PosCashBillStateEnum.get(posCash.getBillState()).getDesc());
                    baseOutinService.saveStock(BaseOutinStockSaveVO.builder()
                            .type(OutinTypeEnum.SELL_OUT.getCode())
                            .sourceType(posCash.getOrderSource())
                            .warehouseId(posCashProduct.getWarehouseId()).code(posCash.getCode())
                            .billDate(LocalDate.now()).billState(0)
                            .orgId(ContextUtil.getCurrentCompanyId())
                            .remarks(desc).employeeId(ContextUtil.getEmployeeId())
                            .outinProductList(Collections.singletonList(BaseOutinProductSaveVO.builder()
                                    .isGift(false)
                                    .name(posCashProduct.getProductName())
                                    .productId(posCashProduct.getProductId())
                                    .warehouseId(posCashProduct.getWarehouseId())
                                    .num(posCashProduct.getNum())
                                    .desc((posCash.getType().equals(PosCashTypeEnum.START_TABLE.getCode())
                                            ? posCash.getTableName() : PosCashTypeEnum.get(posCash.getType()).getDesc())
                                            .concat("-商品删除")
                                    ).numType("1")
                                    .price(posCashProduct.getPrice())
                                    .cashProductId(posCashProduct.getId())
                                    .build()))
                            .build());
                    posCashProductService.removeById(posCashProduct.getId());
                    // 删除的时候,需要后厨打印
                    if (baseProduct.getIsKitchenPrint()) {
                        orderService.kitchen(KitchenPrintQuery.builder()
                                .posCashId(posCash.getId())
                                .cashProductIds(Collections.singletonList(posCashProduct.getId()))
                                .isIntact(false)
                                .type("2")
                                .build());
                    }


                    //新增操作日志
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description("删除" + EquityTypeEnum.get(query.getType()).getDesc() + "【" + posCashProduct.getProductName()
                                    + "(数量" + posCashProduct.getNum() + ")" + posCashProduct.getAmount()
                                    .setScale(2, RoundingMode.HALF_UP).toPlainString() + "元" + "】")
                            .bizModule(BizLogModuleEnum.ITEM_DEL.getCode()).type(BizLogTypeEnum.DELETE.getCode())
                            .sourceId(posCash.getId()).remarks("")
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                case THAIL:
                    long count1 = posCashTableService.count(Wraps.<PosCashTable>lbQ()
                            .isNull(PosCashTable::getCashThailId)
                            .eq(PosCashTable::getDeleteFlag, 0)
                            .eq(PosCashTable::getCashId, posCash.getId()));
                    if (count1 <= 0) {
                        ArgumentAssert.isFalse(posCashThailService.count(Wraps.<PosCashThail>lbQ()
                                .eq(PosCashThail::getCashId, posCash.getId())) == 1, "至少保留一条套餐明细");

                    }
                    //套餐
                    PosCashThail posCashThail = posCashThailService.getById(query.getId());
                    ArgumentAssert.notNull(posCashThail, "请刷新后重试！");
                    ArgumentAssert.isFalse(!ObjectUtil.equal(posCashThail.getStatus(), CashTableStatusEnum.STOP.getCode())
                                    && !ObjectUtil.equal(posCashThail.getStatus(), CashTableStatusEnum.NO_START.getCode())
                            , "请先停止计时再操作");
                    String remarks = "";
                    if (ObjectUtil.equal(posCashThail.getStatus(), CashTableStatusEnum.NO_START.getCode())) {
                        if (StrUtil.isNotBlank(posCashThail.getSecuritiesNumber())) {
                            PosCashPayment cashPayment = posCashPaymentService.getOne(Wraps.<PosCashPayment>lbQ()
                                    .eq(PosCashPayment::getStatus, PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                                    .inSql(PosCashPayment::getPayTypeId, "select id from base_payment_type where delete_flag = 0 " +
                                            "and biz_type in (" + StrUtil.join(",",
                                            Arrays.asList(PaymentBizTypeEnum.MEITUAN.getCode(),
                                                    PaymentBizTypeEnum.DOUYIN.getCode())) + ")")
                                    .isNotNull(PosCashPayment::getSecuritiesNumber)
                                    .eq(PosCashPayment::getSecuritiesNumber, posCashThail.getSecuritiesNumber())
                                    .eq(PosCashPayment::getCashId, posCash.getId()).eq(PosCashPayment::getDeleteFlag, 0)
                            );
                            ArgumentAssert.notNull(cashPayment, "验劵记录不存在，请联系管理员");
                            PaymentRevokeQuery req = PaymentRevokeQuery.builder().posCash(posCash)
                                    .posCashPaymentId(cashPayment.getId()).build();
                            req.setFlowCommonContext(req);
                            LiteflowResponse response = flowExecutor.execute2Resp(BizConstant.FLOW.PAYMENT_REVOKE, req, TrendsPaymentContext.class
                                    , DetailCalcContext.class);
                            ArgumentAssert.isFalse(!response.isSuccess(), response.getMessage());
                            remarks = "已撤销验劵【" + cashPayment.getSecuritiesNumber() + "】";
                        }
                        BaseJobInfo baseJobInfo = baseJobInfoService.getOne(Wraps.<BaseJobInfo>lbQ()
                                .eq(BaseJobInfo::getPosCashId, posCash.getId()).eq(BaseJobInfo::getTriggerStatus, 1)
                                .eq(BaseJobInfo::getJobType, JobTypeEnum.TABLE_TIMING.getCode()).last(" limit 1"));
                        if (ObjectUtil.isNotNull(baseJobInfo)) {
                            //减少定时时长
                            int calcDuration = posCash.getTimingDuration() - posCashThail.getTimingDuration();
                            posCash.setTimingDuration(calcDuration);
                            baseJobInfoService.saveOrUpdateJob(SaveOrUpdateJobQuery.builder()
                                    .jobTypeEnum(JobTypeEnum.TABLE_TIMING).tenantId(ContextUtil.getTenantId())
                                    .employeeId(ContextUtil.getEmployeeId())
                                    .currentCompanyId(ContextUtil.getCurrentCompanyId()).userId(ContextUtil.getUserId())
                                    .posCash(posCash).startTime(baseJobInfo.getAddTime() == null ? LocalDateTime.now().withSecond(0).withNano(0) :
                                            baseJobInfo.getAddTime().withSecond(0).withNano(0))
                                    .desc("【" + posCash.getTableName() + "】")
                                    .duration(-posCashThail.getTimingDuration()).name("【" + posCash.getTableName() + "】")
                                    .sn(ContextUtil.getSn())
                                    .bizId(null).build());
                            //更新或新增定时
                            int closeLightRemind = 3;
                            Map<String, String> data = helperApi.findParams(Collections.singletonList(ParameterKey.CLOSE_LIGHT_REMIND)).getData();
                            if (CollUtil.isNotEmpty(data) && StrUtil.isNotBlank(data.get(ParameterKey.CLOSE_LIGHT_REMIND))) {
                                closeLightRemind = Integer.parseInt(data.get(ParameterKey.CLOSE_LIGHT_REMIND));
                            }
                            if (closeLightRemind >= 3) {
                                baseJobInfoService.saveOrUpdateJob(SaveOrUpdateJobQuery.builder()
                                        .jobTypeEnum(JobTypeEnum.VOICE_TIMING).tenantId(ContextUtil.getTenantId())
                                        .employeeId(ContextUtil.getEmployeeId())
                                        .currentCompanyId(ContextUtil.getCurrentCompanyId()).userId(ContextUtil.getUserId())
                                        .posCash(posCash).startTime(posCashThail.getEndTime() == null ? LocalDateTime.now().
                                                minusMinutes(closeLightRemind).withSecond(0).withNano(0) :
                                                posCashThail.getEndTime().minusMinutes(closeLightRemind)).desc("【" + posCash.getTableName() + "】")
                                        .duration(-posCashThail.getTimingDuration()).name("【" + posCash.getTableName() + "】")
                                        .desc(posCash.getTableName() + "倒计时".concat(closeLightRemind + "") + "分钟")
                                        .sn(ContextUtil.getSn())
                                        .bizId(null).build());
                            }
                            posCash.setUpdatedTime(LocalDateTime.now());
                            posCash.setIsTemporaryLights(false);
                            posCash.setBillState(PosCashBillStateEnum.NO_SETTLED.getCode());
                            cashService.updateById(posCash);
                        }

                    } else {
                        ArgumentAssert.isFalse(StrUtil.isNotBlank(posCashThail.getSecuritiesNumber())
                                , "已验劵，请撤销收款后重试");
                    }
                    List<PosCashProduct> productList = posCashProductService.list(Wraps.<PosCashProduct>lbQ()
                            .eq(PosCashProduct::getCashThailId, posCashThail.getId().toString()));
                    //处理库存问题
                    if (CollUtil.isNotEmpty(productList)) {
                        desc = PosCashTypeEnum.get(posCash.getType()).getDesc()
                                .concat("-")
                                .concat(PosCashBillTypeEnum.get(posCash.getBillType()).getDesc())
                                .concat("-")
                                .concat(PosCashBillStateEnum.get(posCash.getBillState()).getDesc());

                        // 仓库列表
                        List<Long> wearHouseIdList = productList.stream().map(PosCashProduct::getWarehouseId).distinct().collect(Collectors.toList());
                        //更新库存信息
                        for (Long wearHouseId : wearHouseIdList) {
                            baseOutinService.saveStock(BaseOutinStockSaveVO.builder()
                                    .type(OutinTypeEnum.SELL_OUT.getCode())
                                    .sourceType(posCash.getOrderSource())
                                    .warehouseId(wearHouseId).code(posCash.getCode())
                                    .billDate(LocalDate.now()).billState(0)
                                    .orgId(ContextUtil.getCurrentCompanyId())
                                    .remarks(desc).employeeId(ContextUtil.getEmployeeId())
                                    .outinProductList(productList.stream().filter(s -> Objects.equals(s.getWarehouseId(), wearHouseId))
                                            .map(v -> BaseOutinProductSaveVO.builder()
                                                    .isGift(false)
                                                    .name(v.getProductName())
                                                    .productId(v.getProductId())
                                                    .warehouseId(wearHouseId)
                                                    .num(v.getNum()).desc((posCash.getType().equals(PosCashTypeEnum.START_TABLE.getCode())
                                                            ? posCash.getTableName() : PosCashTypeEnum.get(posCash.getType()).getDesc())
                                                            .concat("-套餐删除")).numType("1")
                                                    .price(v.getPrice())
                                                    .cashProductId(v.getId())
                                                    .build()).collect(Collectors.toList()))
                                    .build());
                        }

                    }
                    posCashProductService.update(Wraps.<PosCashProduct>lbU()
                            .set(PosCashProduct::getDeleteFlag, 1).eq(PosCashProduct::getDeleteFlag, 0)
                            .eq(PosCashProduct::getCashId, posCash.getId().toString())
                            .eq(PosCashProduct::getCashThailId, posCashThail.getId().toString()));
                    posCashServiceService.update(Wraps.<PosCashService>lbU()
                            .set(PosCashService::getDeleteFlag, 1).eq(PosCashService::getDeleteFlag, 0)
                            .eq(PosCashService::getCashId, posCash.getId().toString())
                            .eq(PosCashService::getCashThailId, posCashThail.getId().toString()));
                    posCashTableService.update(Wraps.<PosCashTable>lbU()
                            .set(PosCashTable::getDeleteFlag, 1).eq(PosCashTable::getDeleteFlag, 0)
                            .eq(PosCashTable::getCashId, posCash.getId().toString())
                            .eq(PosCashTable::getCashThailId, posCashThail.getId().toString()));
                    posCashThailService.update(Wraps.<PosCashThail>lbU()
                            .set(PosCashThail::getDeleteFlag, 1)
                            .eq(PosCashThail::getDeleteFlag, 0)
                            .eq(PosCashThail::getCashId, posCash.getId())
                            .eq(PosCashThail::getId, posCashThail.getId()));
//                    posCashThailService.removeByIds(Collections.singleton(posCashThail.getId()));
                    //新增操作日志
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description("删除" + EquityTypeEnum.get(query.getType()).getDesc() + "【" + posCashThail.getThailName()
                                    + "(" + posCashThail.getDuration() + "分钟)" + posCashThail.getAmount()
                                    .setScale(2, RoundingMode.HALF_UP).toPlainString() + "元" + "】")
                            .bizModule(BizLogModuleEnum.ITEM_DEL.getCode()).type(BizLogTypeEnum.DELETE.getCode())
                            .sourceId(posCash.getId()).remarks(remarks)
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                case CARD:
                    checkOrderService.checkBuyCardPosCash(posCash);
                    PosCashCard posCashCard = posCashCardService.getById(query.getId());
                    ArgumentAssert.notNull(posCashCard, "卡信息不存在");
                    BaseCard baseCard = baseCardService.getById(posCashCard.getCardId());
                    ArgumentAssert.notNull(baseCard, "卡信息不存在");
                    baseCard.setLockNum(baseCard.getLockNum() - 1);
                    baseCardService.updateById(baseCard);
                    posCashCardService.removeByIds(Collections.singletonList(posCashCard.getId()));
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description("删除" + EquityTypeEnum.get(query.getType()).getDesc() + "【" + posCashCard.getName()
                                    + "(数量" + posCashCard.getNum() + ")" + posCashCard.getAmount()
                                    .setScale(2, RoundingMode.HALF_UP).toPlainString() + "元" + "】")
                            .bizModule(BizLogModuleEnum.ITEM_DEL.getCode()).type(BizLogTypeEnum.DELETE.getCode())
                            .sourceId(posCash.getId()).remarks("")
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                case POWER:
                    //充电
                    PosCashPower posCashPower = posCashPowerService.getById(query.getId());
                    ArgumentAssert.notNull(posCashPower, "请刷新后重试！");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashPower.getStatus(), CashTableStatusEnum.TIMING.getCode()), "请先停止后再操作");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashPower.getStatus(), CashTableStatusEnum.REFUND.getCode()),
                            "退单不可操作");
                    posCashPowerService.removeById(posCashPower.getId());
                    //新增操作日志
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description("删除" + posCashPower.getName()
                                    + "(" + posCashPower.getDuration() + "分钟)" + posCashPower.getAmount()
                                    .setScale(2, RoundingMode.HALF_UP).toPlainString() + "元" + "】")
                            .bizModule(BizLogModuleEnum.ITEM_DEL.getCode()).type(BizLogTypeEnum.DELETE.getCode())
                            .sourceId(posCash.getId()).remarks("")
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                default:
                    ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "不支持此操作");
                    break;
            }
            if (!calcPriceService.isExistItem(posCash)) {
                baseOutinService.update(Wraps.<BaseOutin>lbU()
                        .set(BaseOutin::getBillState, 0)
                        .eq(BaseOutin::getType, OutinTypeEnum.SELL_OUT.getCode())
                        .eq(BaseOutin::getCode, posCash.getCode())
                );
                if (!(posCash.getType().equals(PosCashTypeEnum.SHOPPING.getCode())
                        && (posCash.getBillType().equals(PosCashBillTypeEnum.COUNTER_CHECKOUT.getCode())
                        || posCash.getBillType().equals(PosCashBillTypeEnum.REGISTRATION.getCode())))) {
                    return cashService.removeById(posCash.getId());
                }
            }

            // 删除商品,通知ipad刷新订单
            if (ObjectUtil.isNotNull(posCash.getTableId())) {
                wsNoticeDeviceService.sendRefreshMsg(posCash.getTableId(), false, false);
            }
            return true;
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getPosCashId() + PosCashConstant.Event.ORDER.getCode());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean retreat(ItemRetreatQuery query) {
        PosCash posCash = cashService.getById(query.getPosCashId());
        checkOrderService.checkItemPosCash(posCash);
        checkOrderService.checkOrderDiscountPosCash(posCash);
        if (StrUtil.isBlank(query.getType())) {
            query.setType(EquityTypeEnum.TABLE.getCode());
        }
        switch (EquityTypeEnum.get(query.getType())) {
            //台费
            case TABLE:
                PosCashTable posCashTable = posCashTableService.getById(query.getId());
                if (ObjectUtil.isNull(posCash.getThailId())) {
                    ArgumentAssert.isFalse(posCashTableService.count(Wraps.<PosCashTable>lbQ()
                            .eq(PosCashTable::getCashId, posCash.getId())) == 1, "至少保留一条台费明细");
                }
                ArgumentAssert.notNull(posCashTable, "请刷新后重试！");
                ArgumentAssert.isNull(posCash.getThailId(), "当前明细属于套餐，无法操作");
                ArgumentAssert.isFalse(!ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.STOP.getCode()), "非停止状态无法操作");
                //启用包场后，无法删除
                ArgumentAssert.isFalse(posCashTable.getIsPackField() != null && posCashTable.getIsPackField(),
                        "启用包场后，无法删除");
                posCashTable.setDiscount(BigDecimal.ZERO);
                posCashTable.setDiscountRemarks(null);
                posCashTable.setDiscountAmount(BigDecimal.ZERO);
                posCashTable.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                posCashTable.setStatus(CashTableStatusEnum.REFUND.getCode());
                posCashTable.setAmount(posCashTable.getOrginPrice());
                posCashTable.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                        posCashTable.getRemarks(), EchoDictType.App.ITEM_RETREAT));
                posCashTableService.updateById(posCashTable);
                //新增操作日志
                baseBizLogService.createBizLog(BaseBizLog.builder()
                        .tenantId(ContextUtil.getTenantId())
                        .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                        .description(BizLogModuleEnum.ITEM_RETREAT.getDesc() + "【" + posCashTable.getTableName()
                                + "（" + posCashTable.getDuration() + "分钟）"
                                + posCashTable.getAmount()
                                .setScale(2, RoundingMode.HALF_UP).toPlainString() + "元" + "】")
                        .bizModule(BizLogModuleEnum.ITEM_RETREAT.getCode()).type(BizLogTypeEnum.DELETE.getCode())
                        .sourceId(posCash.getId()).remarks("")
                        .businessAuthId(null)
                        .build());
                break;
            case SERVICE:
                //服务
                PosCashService posCashService = posCashServiceService.getById(query.getId());
                ArgumentAssert.notNull(posCashService, "请刷新后重试！");
                ArgumentAssert.isNull(posCashService.getCashThailId(), "当前属于套餐，无法操作");
                ArgumentAssert.isFalse(!ObjectUtil.equal(posCashService.getStatus(), CashTableStatusEnum.STOP.getCode()), "非停止状态无法操作");
                posCashService.setDiscount(BigDecimal.ZERO);
                posCashService.setDiscountRemarks(null);
                posCashService.setDiscountAmount(BigDecimal.ZERO);
                posCashService.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                posCashService.setStatus(CashTableStatusEnum.REFUND.getCode());
                posCashService.setAmount(posCashService.getOrginPrice());
                posCashService.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                        posCashService.getRemarks(), EchoDictType.App.ITEM_RETREAT));
                posCashServiceService.updateById(posCashService);
                //新增操作日志
                baseBizLogService.createBizLog(BaseBizLog.builder()
                        .tenantId(ContextUtil.getTenantId())
                        .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                        .description(BizLogModuleEnum.ITEM_RETREAT.getDesc() + "【" + posCashService.getEmployeeName()
                                + "（" + posCashService.getDuration() + "分钟）"
                                + posCashService.getAmount()
                                .setScale(2, RoundingMode.HALF_UP).toPlainString() + "元" + "】")
                        .bizModule(BizLogModuleEnum.ITEM_RETREAT.getCode()).type(BizLogTypeEnum.DELETE.getCode())
                        .sourceId(posCash.getId()).remarks("")
                        .build());
                break;
            default:
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "不支持此操作");
                break;
        }
        return true;
    }

    @Override
    public Boolean cancelRetreat(ItemRemarkQuery query) {
        PosCash posCash = cashService.getById(query.getPosCashId());
        checkOrderService.checkItemPosCash(posCash);
        checkOrderService.checkOrderDiscountPosCash(posCash);
        switch (EquityTypeEnum.get(query.getType())) {
            //台费
            case TABLE:
                PosCashTable posCashTable = posCashTableService.getById(query.getId());
                ArgumentAssert.notNull(posCashTable, "请刷新后重试！");
                ArgumentAssert.isNull(posCash.getThailId(), "当前明细属于套餐，无法操作");
                ArgumentAssert.isFalse(!ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.REFUND.getCode()),
                        "非退单状态无法操作");
                posCashTable.setDiscount(BigDecimal.ZERO);
                posCashTable.setDiscountRemarks(null);
                posCashTable.setDiscountAmount(BigDecimal.ZERO);
                posCashTable.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                posCashTable.setStatus(CashTableStatusEnum.STOP.getCode());
                posCashTable.setAmount(posCashTable.getOrginPrice());
                posCashTable.setRemarks(setRemarks(Lists.newArrayList(), "",
                        posCashTable.getRemarks(), EchoDictType.App.ITEM_RETREAT));
                posCashTableService.updateById(posCashTable);
                //新增操作日志
                baseBizLogService.createBizLog(BaseBizLog.builder()
                        .tenantId(ContextUtil.getTenantId())
                        .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                        .description(BizLogModuleEnum.CANCEL_ITEM_RETREAT.getDesc() + "【" + posCashTable.getTableName()
                                + "（" + posCashTable.getDuration() + "分钟) 】")
                        .bizModule(BizLogModuleEnum.CANCEL_ITEM_RETREAT.getCode()).type(BizLogTypeEnum.DELETE.getCode())
                        .sourceId(posCash.getId()).remarks("")
                        .build());
                break;
            case SERVICE:
                //服务
                PosCashService posCashService = posCashServiceService.getById(query.getId());
                ArgumentAssert.notNull(posCashService, "请刷新后重试！");
                ArgumentAssert.isNull(posCashService.getCashThailId(), "当前属于套餐，无法操作");
                ArgumentAssert.isFalse(!ObjectUtil.equal(posCashService.getStatus(), CashTableStatusEnum.REFUND.getCode()),
                        "非退单状态无法操作");
                posCashService.setDiscount(BigDecimal.ZERO);
                posCashService.setDiscountRemarks(null);
                posCashService.setDiscountAmount(BigDecimal.ZERO);
                posCashService.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                posCashService.setStatus(CashTableStatusEnum.STOP.getCode());
                posCashService.setAmount(posCashService.getOrginPrice());
                posCashService.setRemarks(setRemarks(Lists.newArrayList(), "",
                        posCashService.getRemarks(), EchoDictType.App.ITEM_RETREAT));
                posCashServiceService.updateById(posCashService);
                //新增操作日志
                baseBizLogService.createBizLog(BaseBizLog.builder()
                        .tenantId(ContextUtil.getTenantId())
                        .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                        .description(BizLogModuleEnum.CANCEL_ITEM_RETREAT.getDesc() + "【" + posCashService.getEmployeeName()
                                + "（" + posCashService.getDuration() + "分钟） 】")
                        .bizModule(BizLogModuleEnum.CANCEL_ITEM_RETREAT.getCode()).type(BizLogTypeEnum.DELETE.getCode())
                        .sourceId(posCash.getId()).remarks("")
                        .build());
                break;
            default:
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "不支持此操作");
                break;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean productReturn(ProductReturnQuery query) {
        boolean lock = false;
        try {
            lock = distributedLock.lock(query.getPosCashId() + PosCashConstant.Event.ORDER.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            PosCash posCash = cashService.getById(query.getPosCashId());
            checkOrderService.checkItemPosCash(posCash);
            checkOrderService.checkOrderDiscountPosCash(posCash);
            if (CollUtil.isEmpty(query.getTags()) && StrUtil.isBlank(query.getRemarks())) {
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "标签备注至少输入一项");
            }
            PosCashProduct posCashProduct = posCashProductService.getById(query.getId());
            ArgumentAssert.notNull(posCashProduct, "请刷新后重试！");
            ArgumentAssert.isNull(posCashProduct.getCashThailId(), "当前属于套餐，无法操作");
            BaseProduct baseProduct = baseProductService.getById(posCashProduct.getProductId());
            ArgumentAssert.notNull(baseProduct, "商品不存在！");
            LocalDateTime createdTime = posCashProduct.getCreatedTime().plusMinutes(2);
            if (LocalDateTime.now().isAfter(createdTime)) {
                ArgumentAssert.isFalse(posCashProduct.getNum() < query.getNum(),
                        "退货的数量不能超过实际数据");
                posCashProduct.setReturnNum(posCashProduct.getReturnNum() == null ? 0 : posCashProduct.getReturnNum());
                posCashProduct.setReturnNum(posCashProduct.getReturnNum() + query.getNum());
                posCashProduct.setNum(posCashProduct.getNum() - query.getNum());
                ArgumentAssert.isFalse(posCashProduct.getNum() < 0,
                        "当前状态无法操作");
                posCashProduct.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                        posCashProduct.getRemarks(), EchoDictType.App.PRODUCT_ITEM_RETREAT));
                //新增退货库存记录
                baseOutinService.productReturn(posCashProduct, query.getNum(), posCash);
                if (posCashProduct.getNum() == 0) {
                    baseOutinService.update(Wraps.<BaseOutin>lbU()
                            .set(BaseOutin::getBillState, 1)
                            .eq(BaseOutin::getBillState, 0)
                            .eq(BaseOutin::getType, OutinTypeEnum.SELL_OUT.getCode())
                            .eq(BaseOutin::getCode, posCash.getCode()));
                    posCashProductService.removeById(posCashProduct.getId());
                } else {
                    posCashProductService.updateById(posCashProduct);
                }
                //新增操作日志
                String desc = setRemarksDesc(query.getTags(), query.getRemarks(),
                        posCashProduct.getRemarks(), EchoDictType.App.PRODUCT_ITEM_RETREAT);
                baseBizLogService.createBizLog(BaseBizLog.builder()
                        .tenantId(ContextUtil.getTenantId())
                        .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                        .description(BizLogModuleEnum.PRODUCT_RETURN.getDesc() + "【" + baseProduct.getName()
                                + "（-" + query.getNum() + "）" + "】")
                        .bizModule(BizLogModuleEnum.PRODUCT_RETURN.getCode())
                        .type(BizLogTypeEnum.DELETE.getCode())
                        .sourceId(posCash.getId()).remarks(desc)
                        .build());
            } else {
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "当前状态无法操作，请稍后再试");
            }
//            }
            // 商品退货刷新订单
            if (ObjectUtil.isNotNull(posCash.getTableId())) {
                wsNoticeDeviceService.sendRefreshMsg(posCash.getTableId(), false, false);
            }
            return true;
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getPosCashId() + PosCashConstant.Event.ORDER.getCode());
            }
        }
    }

    @Override
    public Boolean productGift(ItemGiftQuery query) {
        boolean lock = false;
        try {
            lock = distributedLock.lock(query.getId() + "_" + PosCashConstant.Event.ORDER.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            PosCash posCash = cashService.getById(query.getPosCashId());
            checkOrderService.checkItemPosCash(posCash);
            checkOrderService.checkOrderDiscountPosCash(posCash);
            if (CollUtil.isEmpty(query.getTags()) && StrUtil.isBlank(query.getRemarks())) {
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "标签备注至少输入一项");
            }
            PosCashProduct posCashProduct = posCashProductService.getById(query.getId());
            ArgumentAssert.notNull(posCashProduct, "请刷新后重试！");
            ArgumentAssert.isNull(posCashProduct.getCashThailId(), "当前属于套餐，无法操作");
            BaseProduct baseProduct = baseProductService.getById(posCashProduct.getProductId());
            ArgumentAssert.notNull(baseProduct, "商品不存在！");
            List<String> discountTypeList = Arrays.asList(DiscountTypeEnum.ORIGINAL.getCode(), DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode(),
                    DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode());
            if (!discountTypeList.contains(posCashProduct.getDiscountType())) {
                throw new BizException("已参与优惠，无法赠送");
            }
            if (posCashProduct.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                throw new BizException("金额为零，无法赠送");
            }
            ArgumentAssert.isFalse(posCashProduct.getNum() < query.getNum(),
                    "赠送数量不能超过实际数据");
            posCashProduct.setNum(posCashProduct.getNum() - query.getNum());
            posCashProduct.setOrginPrice(posCashProduct.getPrice().multiply(new BigDecimal(posCashProduct.getNum()))
                    .setScale(2, RoundingMode.HALF_UP));
            posCashProduct.setDiscount(posCashProduct.getOrginPrice());
            posCashProduct.setOldOrginPrice(posCashProduct.getOrginPrice());
            posCashProduct.setOldPrice(posCashProduct.getPrice());
            ArgumentAssert.isFalse(posCashProduct.getNum() < 0,
                    "当前状态无法操作");
            PosCashProduct cashProduct = BeanUtil.copyProperties(posCashProduct, PosCashProduct.class);
            cashProduct.setNum(query.getNum());
            cashProduct.setRemarks("");
            cashProduct.setIsGift(true);
            cashProduct.setId(null);
            cashProduct.setGiftSourceId(posCashProduct.getId());
            cashProduct.setOrginPrice(posCashProduct.getPrice().multiply(new BigDecimal(posCashProduct.getNum()))
                    .setScale(2, RoundingMode.HALF_UP));
            cashProduct.setAmount(BigDecimal.ZERO);
            cashProduct.setDiscount(cashProduct.getOrginPrice());
            cashProduct.setDiscountAmount(cashProduct.getOrginPrice());
            cashProduct.setOldOrginPrice(posCashProduct.getOrginPrice());
            cashProduct.setOldPrice(posCashProduct.getPrice());
            cashProduct.setDiscountType(DiscountTypeEnum.PRODUCT_GIFT.getCode());
            cashProduct.setGiftAuthId(query.getBusinessAuthId());
            cashProduct.setGiftEmployeeId(ContextUtil.getEmployeeId());
            cashProduct.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                    cashProduct.getRemarks(), EchoDictType.App.PRODUCT_ITEM_GIFT));
            if (posCashProduct.getNum() == 0) {
                baseOutinService.update(Wraps.<BaseOutin>lbU()
                        .set(BaseOutin::getBillState, 1)
                        .eq(BaseOutin::getBillState, 0)
                        .eq(BaseOutin::getType, OutinTypeEnum.SELL_OUT.getCode())
                        .eq(BaseOutin::getCode, posCash.getCode()));
                posCashProductService.removeById(posCashProduct.getId());
            } else {
                posCashProductService.updateById(posCashProduct);
            }
            posCashProductService.save(cashProduct);
            //新增操作日志
            String desc = setRemarksDesc(query.getTags(), query.getRemarks(),
                    posCashProduct.getRemarks(), EchoDictType.App.PRODUCT_ITEM_GIFT);
            baseBizLogService.createBizLog(BaseBizLog.builder()
                    .tenantId(ContextUtil.getTenantId())
                    .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                    .description(BizLogModuleEnum.PRODUCT_GIFT.getDesc() + "【" + cashProduct.getProductName()
                            + "（赠送数量" + query.getNum() + "）" + "】")
                    .bizModule(BizLogModuleEnum.PRODUCT_GIFT.getCode())
                    .type(BizLogTypeEnum.CREATED.getCode())
                    .sourceId(posCash.getId()).remarks(desc)
                    .businessAuthId(query.getBusinessAuthId())
                    .build());
            // 商品赠送刷新订单
            if (ObjectUtil.isNotNull(posCash.getTableId())) {
                wsNoticeDeviceService.sendRefreshMsg(posCash.getTableId(), false, false);
            }
            return true;
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getPosCashId() + "_" + PosCashConstant.Event.ORDER.getCode());
            }
        }
    }

    @Override
    public Boolean cancelGift(ItemCancelGiftQuery query) {
        boolean lock = false;
        try {
            lock = distributedLock.lock(query.getId() + "_" + PosCashConstant.Event.ORDER.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            PosCash posCash = cashService.getById(query.getPosCashId());
            checkOrderService.checkItemPosCash(posCash);
            checkOrderService.checkOrderDiscountPosCash(posCash);
            PosCashProduct posCashProduct = posCashProductService.getById(query.getId());
            ArgumentAssert.notNull(posCashProduct, "请刷新后重试！");
            ArgumentAssert.isNull(posCashProduct.getCashThailId(), "当前属于套餐，无法操作");
            if (posCashProduct.getIsGift() == null || !posCashProduct.getIsGift()) {
                throw new BizException("非赠送，无法操作");
            }
            posCashProduct.setOrginPrice(posCashProduct.getPrice().multiply(new BigDecimal(posCashProduct.getNum()))
                    .setScale(2, RoundingMode.HALF_UP));
            posCashProduct.setDiscount(BigDecimal.ZERO);
            posCashProduct.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
            posCashProduct.setDiscountAmount(BigDecimal.ZERO);
            posCashProduct.setOldOrginPrice(posCashProduct.getOrginPrice());
            posCashProduct.setOldPrice(posCashProduct.getPrice());
            posCashProduct.setRemarks(setRemarks(Lists.newArrayList(), "",
                    posCashProduct.getRemarks(), EchoDictType.App.PRODUCT_ITEM_GIFT));
            posCashProduct.setIsGift(false);
            posCashProduct.setGiftSourceId(null);
            posCashProduct.setGiftEmployeeId(null);
            posCashProduct.setGiftAuthId(null);
            posCashProductService.updateById(posCashProduct);
            baseBizLogService.createBizLog(BaseBizLog.builder()
                    .tenantId(ContextUtil.getTenantId())
                    .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                    .description("取消赠送【" + posCashProduct.getProductName()
                            + "（-数量" + posCashProduct.getNum() + "）" + "】")
                    .bizModule(BizLogModuleEnum.PRODUCT_GIFT.getCode())
                    .type(BizLogTypeEnum.CREATED.getCode())
                    .sourceId(posCash.getId()).remarks("")
                    .build());
            // 商品赠送刷新订单
            if (ObjectUtil.isNotNull(posCash.getTableId())) {
                wsNoticeDeviceService.sendRefreshMsg(posCash.getTableId(), false, false);
            }
            return true;
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getPosCashId() + "_" + PosCashConstant.Event.ORDER.getCode());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remark(ItemRemarkQuery query) {
        boolean lock = false;
        try {
            lock = distributedLock.lock(query.getPosCashId() + PosCashConstant.Event.ORDER.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            PosCash posCash = cashService.getById(query.getPosCashId());
            checkOrderService.checkRemarkPosCash(posCash);
            switch (EquityTypeEnum.get(query.getType())) {
                //台费
                case TABLE:
                    PosCashTable posCashTable = posCashTableService.getById(query.getId());
                    ArgumentAssert.notNull(posCashTable, "请刷新后重试！");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.TIMING.getCode()),
                            "请先停止计时再操作");
                    posCashTable.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashTable.getRemarks(), EchoDictType.App.ITEM_TAGS));
                    posCashTableService.updateById(posCashTable);
                    String desc = setRemarksDesc(query.getTags(), query.getRemarks(),
                            posCashTable.getRemarks(), EchoDictType.App.ITEM_TAGS);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashTable.getTableName() + "添加" + BizLogModuleEnum.ITEM_REMARKS.getDesc())
                            .bizModule(BizLogModuleEnum.ITEM_REMARKS.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks(desc)
                            .build());
                    break;
                case SERVICE:
                    //服务
                    PosCashService posCashService = posCashServiceService.getById(query.getId());
                    ArgumentAssert.notNull(posCashService, "请刷新后重试！");
                    posCashService.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashService.getRemarks(), EchoDictType.App.ITEM_TAGS));
                    posCashServiceService.updateById(posCashService);
                    desc = setRemarksDesc(query.getTags(), query.getRemarks(),
                            posCashService.getRemarks(), EchoDictType.App.ITEM_TAGS);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashService.getEmployeeName() + "添加" + BizLogModuleEnum.ITEM_REMARKS.getDesc())
                            .bizModule(BizLogModuleEnum.ITEM_REMARKS.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks(desc)
                            .build());
                    break;
                case PRODUCT:
                    //商品
                    PosCashProduct posCashProduct = posCashProductService.getById(query.getId());
                    ArgumentAssert.notNull(posCashProduct, "请刷新后重试！");
                    posCashProduct.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashProduct.getRemarks(), EchoDictType.App.ITEM_TAGS));
                    posCashProductService.updateById(posCashProduct);
                    //新增操作日志
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashProduct.getProductName() + "添加"
                                    + BizLogModuleEnum.ITEM_REMARKS.getDesc())
                            .bizModule(BizLogModuleEnum.ITEM_REMARKS.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashProduct.getRemarks(), EchoDictType.App.ITEM_TAGS))
                            .build());
                    break;
                case CARD:
                    //商品
                    PosCashCard posCashCard = posCashCardService.getById(query.getId());
                    ArgumentAssert.notNull(posCashCard, "请刷新后重试！");
                    posCashCard.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashCard.getRemarks(), EchoDictType.App.ITEM_TAGS));
                    posCashCardService.updateById(posCashCard);
                    //新增操作日志
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashCard.getName() + "添加"
                                    + BizLogModuleEnum.ITEM_REMARKS.getDesc())
                            .bizModule(BizLogModuleEnum.ITEM_REMARKS.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashCard.getRemarks(), EchoDictType.App.ITEM_TAGS))
                            .build());
                    break;
                case POWER:
                    //商品
                    PosCashPower posCashPower = posCashPowerService.getById(query.getId());
                    ArgumentAssert.notNull(posCashPower, "请刷新后重试！");
                    posCashPower.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashPower.getRemarks(), EchoDictType.App.ITEM_TAGS));
                    posCashPowerService.updateById(posCashPower);
                    //新增操作日志
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashPower.getName() + "添加"
                                    + BizLogModuleEnum.ITEM_REMARKS.getDesc())
                            .bizModule(BizLogModuleEnum.ITEM_REMARKS.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashPower.getRemarks(), EchoDictType.App.ITEM_TAGS))
                            .build());
                    break;
                default:
                    ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "不支持此操作");
                    break;
            }
            return true;
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getPosCashId() + PosCashConstant.Event.ORDER.getCode());
            }
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean discount(ItemDiscountQuery query) {
        boolean lock = false;
        try {
            lock = distributedLock.lock(query.getId() + PosCashConstant.Event.ORDER.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            if (!ObjectUtil.equal(query.getDiscountType(), DiscountTypeEnum.HAND_DISCOUNT.getCode())
                    && !ObjectUtil.equal(query.getDiscountType(), DiscountTypeEnum.HAND_REDUCTION.getCode())) {
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "优惠类型错误");
            }
            if (ObjectUtil.equal(query.getDiscountType(), DiscountTypeEnum.HAND_DISCOUNT.getCode())) {
                ArgumentAssert.isFalse(query.getDiscount().compareTo(new BigDecimal("9.9")) > 0
                        || query.getDiscount().compareTo(new BigDecimal("1")) < 0, "请输入1-9.9折扣值");
                query.setDiscount(query.getDiscount());
            }
            PosCash posCash = cashService.getById(query.getPosCashId());
            checkOrderService.checkItemPosCash(posCash);
            checkOrderService.checkOrderDiscountPosCash(posCash);
            ArgumentAssert.isFalse(posCashEquityService.count(Wraps.<PosCashEquity>lbQ()
                    .eq(PosCashEquity::getCashId, posCash.getId())) > 0, "请先取消优惠劵再试");

            switch (EquityTypeEnum.get(query.getType())) {
                //台费
                case TABLE:
                    PosCashTable posCashTable = posCashTableService.getById(query.getId());
                    ArgumentAssert.notNull(posCashTable, "请刷新后重试！");
                    if (posCashTable.getAmount()
                            .subtract(posCashTable.getAssessedAmount()).compareTo(BigDecimal.ZERO) <= 0
                            || posCashTable.getOrginPrice().compareTo(BigDecimal.ZERO) == 0) {
                        ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "金额为零，无需优惠");
                    }

                    ArgumentAssert.isNull(posCashTable.getCashThailId(), "已享受套餐优惠，无法操作");
                    ArgumentAssert.isFalse(!ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.STOP.getCode()), "请先停止计时再操作");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.REFUND.getCode()), "当前状态无法操作");
                    if (ObjectUtil.equal(query.getDiscountType(), DiscountTypeEnum.HAND_REDUCTION.getCode())) {
                        ArgumentAssert.isFalse(query.getDiscount().compareTo(posCashTable.getAmount()) > 0, "减免金额不能大于可支付金额");
                    }
                    if (StrUtil.isNotBlank(posCashTable.getDiscountType()) && !ObjectUtil.equal(query.getDiscountType(),
                            DiscountTypeEnum.ORIGINAL.getCode())) {
                        if (posCashTable.getDiscountType().equals(DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode())
                                || posCashTable.getDiscountType().equals(DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                            posCashTable.setDiscountAmount(BigDecimal.ZERO);
                        }
                    }
                    posCashTable.setDiscount(query.getDiscount());
                    posCashTable.setDiscountType(query.getDiscountType());
                    posCashTable.setDiscountRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashTable.getRemarks(), EchoDictType.App.ITEM_DISCOUNT_TAGS));
                    posCashTableService.updateById(posCashTable);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashTable.getTableName() + EquityTypeEnum.get(query.getType()).getDesc()
                                    + BizLogModuleEnum.ITEM_DISCOUNT.getDesc() + "【"
                                    + DiscountTypeEnum.get(query.getDiscountType()).getDesc()
                                    + (ObjectUtil.equal(query.getDiscountType(), DiscountTypeEnum.HAND_DISCOUNT.getCode())
                                    ? query.getDiscount().toPlainString() + "折" : "-" + query.getDiscount()
                                    .setScale(2, RoundingMode.HALF_UP).toPlainString() + "元") + "】")
                            .bizModule(BizLogModuleEnum.ITEM_DISCOUNT.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashTable.getRemarks(), EchoDictType.App.ITEM_DISCOUNT_TAGS))
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                case SERVICE:
                    //服务
                    PosCashService posCashService = posCashServiceService.getById(query.getId());
                    ArgumentAssert.notNull(posCashService, "请刷新后重试！");
                    if (posCashService.getAmount()
                            .subtract(posCashService.getAssessedAmount()).compareTo(BigDecimal.ZERO) <= 0
                            || posCashService.getOrginPrice().compareTo(BigDecimal.ZERO) == 0) {
                        ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "金额为零，无需优惠");
                    }
                    ArgumentAssert.isNull(posCashService.getCashThailId(), "已享受套餐优惠，无法操作");
                    ArgumentAssert.isFalse(!ObjectUtil.equal(posCashService.getStatus(), CashTableStatusEnum.STOP.getCode()), "请先停止计时再操作");
                    if (ObjectUtil.equal(query.getDiscountType(), DiscountTypeEnum.HAND_REDUCTION.getCode())) {
                        ArgumentAssert.isFalse(query.getDiscount().compareTo(posCashService.getAmount()) > 0, "减免金额不能大于可支付金额");
                    }
                    if (StrUtil.isNotBlank(posCashService.getDiscountType())) {
                        if (posCashService.getDiscountType().equals(DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode())
                                || posCashService.getDiscountType().equals(DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                            posCashService.setDiscountAmount(BigDecimal.ZERO);
                        }
                    }
                    posCashService.setDiscount(query.getDiscount());
                    posCashService.setDiscountType(query.getDiscountType());
                    posCashService.setDiscountRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashService.getRemarks(), EchoDictType.App.ITEM_DISCOUNT_TAGS));
                    posCashServiceService.updateById(posCashService);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashService.getEmployeeName()
                                    + BizLogModuleEnum.ITEM_DISCOUNT.getDesc() + "【"
                                    + DiscountTypeEnum.get(query.getDiscountType()).getDesc()
                                    + (ObjectUtil.equal(query.getDiscountType(), DiscountTypeEnum.HAND_DISCOUNT.getCode())
                                    ? query.getDiscount().toPlainString() + "折" : "-" + query.getDiscount()
                                    .setScale(2, RoundingMode.HALF_UP).toPlainString() + "元") + "】")
                            .bizModule(BizLogModuleEnum.ITEM_DISCOUNT.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashService.getRemarks(), EchoDictType.App.ITEM_DISCOUNT_TAGS))
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                case PRODUCT:
                    //商品
                    PosCashProduct posCashProduct = posCashProductService.getById(query.getId());
                    ArgumentAssert.notNull(posCashProduct, "请刷新后重试！");
                    if (posCashProduct.getAmount()
                            .subtract(posCashProduct.getAssessedAmount()).compareTo(BigDecimal.ZERO) <= 0
                            || posCashProduct.getOrginPrice().compareTo(BigDecimal.ZERO) == 0) {
                        ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "金额为零，无需优惠");
                    }
                    ArgumentAssert.isNull(posCashProduct.getCashThailId(), "已享受套餐优惠，无法操作");
                    if (ObjectUtil.equal(query.getDiscountType(), DiscountTypeEnum.HAND_REDUCTION.getCode())) {
                        ArgumentAssert.isFalse(query.getDiscount().compareTo(posCashProduct.getAmount()) > 0, "减免金额不能大于可支付金额");
                    }
                    if (StrUtil.isNotBlank(posCashProduct.getDiscountType())) {
                        if (posCashProduct.getDiscountType().equals(DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode())
                                || posCashProduct.getDiscountType().equals(DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                            posCashProduct.setDiscountAmount(BigDecimal.ZERO);
                        }
                    }
                    posCashProduct.setDiscount(query.getDiscount());
                    posCashProduct.setDiscountType(query.getDiscountType());
                    posCashProduct.setDiscountRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashProduct.getRemarks(), EchoDictType.App.ITEM_DISCOUNT_TAGS));
                    posCashProductService.updateById(posCashProduct);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashProduct.getProductName()
                                    + BizLogModuleEnum.ITEM_DISCOUNT.getDesc() + "【"
                                    + DiscountTypeEnum.get(query.getDiscountType()).getDesc()
                                    + (ObjectUtil.equal(query.getDiscountType(), DiscountTypeEnum.HAND_DISCOUNT.getCode())
                                    ? query.getDiscount().toPlainString() + "折" : "-" + query.getDiscount()
                                    .setScale(2, RoundingMode.HALF_UP).toPlainString() + "元") + "】")
                            .bizModule(BizLogModuleEnum.ITEM_DISCOUNT.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashProduct.getRemarks(), EchoDictType.App.ITEM_DISCOUNT_TAGS))
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                case CARD:
                    //商品
                    PosCashCard posCashCard = posCashCardService.getById(query.getId());
                    ArgumentAssert.notNull(posCashCard, "请刷新后重试！");
                    if (posCashCard.getAmount()
                            .subtract(posCashCard.getAssessedAmount()).compareTo(BigDecimal.ZERO) <= 0
                            || posCashCard.getOrginPrice().compareTo(BigDecimal.ZERO) == 0) {
                        ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "金额为零，无需优惠");
                    }
                    ArgumentAssert.isNull(posCashCard.getCashThailId(), "已享受套餐优惠，无法操作");
                    if (ObjectUtil.equal(query.getDiscountType(), DiscountTypeEnum.HAND_REDUCTION.getCode())) {
                        ArgumentAssert.isFalse(query.getDiscount().compareTo(posCashCard.getAmount()) > 0, "减免金额不能大于可支付金额");
                    }
                    if (StrUtil.isNotBlank(posCashCard.getDiscountType())) {
                        if (posCashCard.getDiscountType().equals(DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode())
                                || posCashCard.getDiscountType().equals(DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                            posCashCard.setDiscountAmount(BigDecimal.ZERO);
                        }
                    }
                    posCashCard.setDiscount(query.getDiscount());
                    posCashCard.setDiscountType(query.getDiscountType());
                    posCashCard.setDiscountRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashCard.getRemarks(), EchoDictType.App.ITEM_DISCOUNT_TAGS));
                    posCashCardService.updateById(posCashCard);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashCard.getName()
                                    + BizLogModuleEnum.ITEM_DISCOUNT.getDesc() + "【"
                                    + DiscountTypeEnum.get(query.getDiscountType()).getDesc()
                                    + (ObjectUtil.equal(query.getDiscountType(), DiscountTypeEnum.HAND_DISCOUNT.getCode())
                                    ? query.getDiscount().toPlainString() + "折" : "-" + query.getDiscount()
                                    .setScale(2, RoundingMode.HALF_UP).toPlainString() + "元") + "】")
                            .bizModule(BizLogModuleEnum.ITEM_DISCOUNT.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashCard.getRemarks(), EchoDictType.App.ITEM_DISCOUNT_TAGS))
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                case THAIL:
                    //商品
                    PosCashThail posCashThail = posCashThailService.getById(query.getId());
                    ArgumentAssert.notNull(posCashThail, "请刷新后重试！");
                    if (posCashThail.getAmount()
                            .subtract(posCashThail.getAssessedAmount()).compareTo(BigDecimal.ZERO) <= 0
                            || posCashThail.getOrginPrice().compareTo(BigDecimal.ZERO) == 0) {
                        ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "金额为零，无需优惠");
                    }
                    if (ObjectUtil.equal(query.getDiscountType(), DiscountTypeEnum.HAND_REDUCTION.getCode())) {
                        ArgumentAssert.isFalse(query.getDiscount().compareTo(posCashThail.getAmount()) > 0, "减免金额不能大于可支付金额");
                    }
                    if (StrUtil.isNotBlank(posCashThail.getDiscountType())) {
                        if (posCashThail.getDiscountType().equals(DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode())
                                || posCashThail.getDiscountType().equals(DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                            posCashThail.setDiscountAmount(BigDecimal.ZERO);
                        }
                    }
                    posCashThail.setDiscount(query.getDiscount());
                    posCashThail.setDiscountType(query.getDiscountType());
                    posCashThail.setDiscountRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashThail.getRemarks(), EchoDictType.App.ITEM_DISCOUNT_TAGS));
                    posCashThailService.updateById(posCashThail);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashThail.getThailName()
                                    + BizLogModuleEnum.ITEM_DISCOUNT.getDesc() + "【"
                                    + DiscountTypeEnum.get(query.getDiscountType()).getDesc()
                                    + (ObjectUtil.equal(query.getDiscountType(), DiscountTypeEnum.HAND_DISCOUNT.getCode())
                                    ? query.getDiscount().toPlainString() + "折" : "-" + query.getDiscount()
                                    .setScale(2, RoundingMode.HALF_UP).toPlainString() + "元") + "】")
                            .bizModule(BizLogModuleEnum.ITEM_DISCOUNT.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashThail.getRemarks(), EchoDictType.App.ITEM_DISCOUNT_TAGS))
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                case POWER:
                    //服务
                    PosCashPower posCashPower = posCashPowerService.getById(query.getId());
                    ArgumentAssert.notNull(posCashPower, "请刷新后重试！");
                    if (posCashPower.getAmount()
                            .subtract(posCashPower.getAssessedAmount()).compareTo(BigDecimal.ZERO) <= 0
                            || posCashPower.getOrginPrice().compareTo(BigDecimal.ZERO) == 0) {
                        ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "金额为零，无需优惠");
                    }
                    ArgumentAssert.isFalse(!ObjectUtil.equal(posCashPower.getStatus(), CashTableStatusEnum.STOP.getCode()), "请先停止计时再操作");
                    if (ObjectUtil.equal(query.getDiscountType(), DiscountTypeEnum.HAND_REDUCTION.getCode())) {
                        ArgumentAssert.isFalse(query.getDiscount().compareTo(posCashPower.getAmount()) > 0, "减免金额不能大于可支付金额");
                    }
                    if (StrUtil.isNotBlank(posCashPower.getDiscountType())) {
                        if (posCashPower.getDiscountType().equals(DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode())
                                || posCashPower.getDiscountType().equals(DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                            posCashPower.setDiscountAmount(BigDecimal.ZERO);
                        }
                    }
                    posCashPower.setDiscount(query.getDiscount());
                    posCashPower.setDiscountType(query.getDiscountType());
                    posCashPower.setDiscountRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashPower.getRemarks(), EchoDictType.App.ITEM_DISCOUNT_TAGS));
                    posCashPowerService.updateById(posCashPower);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashPower.getName()
                                    + BizLogModuleEnum.ITEM_DISCOUNT.getDesc() + "【"
                                    + DiscountTypeEnum.get(query.getDiscountType()).getDesc()
                                    + (ObjectUtil.equal(query.getDiscountType(), DiscountTypeEnum.HAND_DISCOUNT.getCode())
                                    ? query.getDiscount().toPlainString() + "折" : "-" + query.getDiscount()
                                    .setScale(2, RoundingMode.HALF_UP).toPlainString() + "元") + "】")
                            .bizModule(BizLogModuleEnum.ITEM_DISCOUNT.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashPower.getRemarks(), EchoDictType.App.ITEM_DISCOUNT_TAGS))
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                default:
                    ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "不支持此操作");
                    break;
            }
            posCash.setRoundAmount(BigDecimal.ZERO);
            posCash.setIsRound(false);
            posCash.setIsAutoRound(false);
            posCash.setAutoRoundAmount(BigDecimal.ZERO);
            posCash.setUpdatedTime(LocalDateTime.now());
            boolean b = cashService.updateById(posCash);

            // 单品优惠 通知设备刷新订单
            if (ObjectUtil.isNotNull(posCash.getTableId())) {
                wsNoticeDeviceService.sendRefreshMsg(posCash.getTableId(), false, false);
            }
            return b;
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getId() + PosCashConstant.Event.ORDER.getCode());
            }
        }

    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean duration(ItemDurationUpdateQuery query) {
        PosCash posCash = cashService.getById(query.getPosCashId());
        checkOrderService.checkItemPosCash(posCash);
        checkOrderService.checkOrderDiscountPosCash(posCash);
        Integer duration = 0;
        String desc = "";
        ArgumentAssert.isFalse(posCashEquityService.count(Wraps.<PosCashEquity>lbQ()
                .eq(PosCashEquity::getCashId, posCash.getId())) > 0, "请先取消优惠劵再试");
        switch (EquityTypeEnum.get(query.getType())) {
            //台费
            case TABLE:
                PosCashTable posCashTable = posCashTableService.getById(query.getId());
                ArgumentAssert.notNull(posCashTable, "请刷新后重试！");
                ArgumentAssert.isNull(posCashTable.getCashThailId(), "已享受套餐优惠，不支持此操作");
                ArgumentAssert.isFalse(posCashTable.getFreeDuration() != null
                        && posCashTable.getFreeDuration() > 0, "已享受免打优惠，不支持此操作");
                if (posCashTable.getIsPackField() != null && posCashTable.getIsPackField()) {
                    //判断金额是否为零，为零无法修改
                    ArgumentAssert.isFalse(posCashTable.getAmount().compareTo(BigDecimal.ZERO) == 0,
                            "启用包场后，金额为零无法修改时长");
                    if (posCashTable.getIsPackField() != null && posCashTable.getIsPackField()) {
                        ArgumentAssert.isFalse(ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.TIMING.getCode()),
                                "此记录为包台，请停止计时后再试");
                    }
//                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.TIMING.getCode()),
//                            "启用包场后，请停止后再试");
                }
                if (posCashTable.getBillingLevel() != null && posCashTable.getBillingLevel()
                        .equals(BillingLevelEnum.ONE.getCode())) {
                    //判断金额是否为零，为零无法修改
//                    ArgumentAssert.isFalse(posCashTable.getAmount().compareTo(BigDecimal.ZERO) == 0,
//                            "金额为零无法修改时长");
                    ArgumentAssert.isFalse(!ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.TIMING.getCode()),
                            "首轮计时为固定价格，不支持修改");
                }
//                ArgumentAssert.isFalse(!isUpdateDuration, "已享受暂停服务，不支持此操作");
                duration = posCashTable.getDuration();
                posCashTable.setIsModifyDuration(true);
                posCashTable.setDuration(query.getDuration());
                posCashTable.setStartTime(posCashTable.getEndTime()
                        .withSecond(0).withNano(0).minusMinutes(query.getDuration()));
                //重新计算价格
                calcPriceService.refreshTable(posCashTable);
                posCashTableService.updateById(posCashTable);
                desc = posCashTable.getTableName() + EquityTypeEnum.get(query.getType()).getDesc();
                break;
            case SERVICE:
                checkServiceDuration(query, posCash);
                //服务
                PosCashService posCashService = posCashServiceService.getById(query.getId());
                ArgumentAssert.notNull(posCashService, "请刷新后重试！");
                ArgumentAssert.isNull(posCashService.getCashThailId(), "已享受套餐优惠，不支持此操作");
                duration = posCashService.getDuration();
                posCashService.setDuration(query.getDuration());
                posCashService.setStartTime(posCashService.getEndTime()
                        .withSecond(0).withNano(0).minusMinutes(query.getDuration()));
//                posCashService.setEndTime(LocalDateTime.now()
//                        .withSecond(0).withNano(0));
                //重新计算价格
                calcPriceService.refreshService(posCashService);
                posCashServiceService.updateById(posCashService);
                desc = posCashService.getEmployeeName();
                break;
            default:
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "不支持此操作");
                break;
        }

        // 发送消息通知IPAD设备,刷新订单-修改时长
        if (ObjectUtil.isNotNull(posCash.getTableId())) {
            wsNoticeDeviceService.sendRefreshMsg(posCash.getTableId(), false, false);
        }

        //新增操作日志
        baseBizLogService.createBizLog(BaseBizLog.builder()
                .tenantId(ContextUtil.getTenantId())
                .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                .description("修改时长【" + desc + "，" + duration + "分钟->" + query.getDuration() + "分钟】")
                .bizModule(BizLogModuleEnum.MODIFY_DURATION.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                .sourceId(posCash.getId()).remarks("")
                .businessAuthId(query.getBusinessAuthId())
                .build());
        return true;
    }

    private void checkServiceDuration(ItemDurationUpdateQuery query, PosCash posCash) {
        // 只有开台的订单才需要检验
        if (!StringUtils.equals(posCash.getType(), PosCashTypeEnum.START_TABLE.getCode())) {
            return;
        }
        boolean serviceMoreThanTable;
        Map<String, String> serviceMoreThanTableDuration = helperApi.findParams(Collections.singletonList(ParameterKey.SERVICE_MORE_THAN_TABLE_DURATION)).getData();
        log.info("个性参数：{}", JsonUtil.toJson(serviceMoreThanTableDuration));

        serviceMoreThanTable = CollUtil.isEmpty(serviceMoreThanTableDuration) || StrUtil.isBlank(serviceMoreThanTableDuration.get(ParameterKey.SERVICE_MORE_THAN_TABLE_DURATION))
                || StringUtils.equals(serviceMoreThanTableDuration.get(ParameterKey.SERVICE_MORE_THAN_TABLE_DURATION), "1");
        if (serviceMoreThanTable) {
            return;
        }
        int totalDuration = 0;
        // 只需要校验台桌时长和套餐时长就可以了
        if (Objects.nonNull(posCash.getTableDuration())) {
            totalDuration += posCash.getTableDuration();
        }
        if (Objects.nonNull(posCash.getThailDuration())) {
            totalDuration += posCash.getThailDuration();
        }
        if (totalDuration < query.getDuration()) {
            throw BizException.wrapTips("提示", "服务时长不允许超过台桌计费时长，如有需要，请至 “小程序->收银参数“ 进行更改！");
        }
    }


    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean setTiming(ItemSetTimingQuery query) {
        PosCash posCash = cashService.getById(query.getPosCashId());
        checkOrderService.checkItemPosCashNoOpera(posCash);
        long count = posCashThailService.count(Wraps.<PosCashThail>lbQ().eq(PosCashThail::getCashId, posCash.getId())
                .eq(PosCashThail::getStatus, CashTableStatusEnum.TIMING.getCode()));
        ArgumentAssert.isFalse(count > 0, "套餐进行中无法设置定时");
        count = posCashTableService.count(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, posCash.getId())
                .isNull(PosCashTable::getCashThailId)
                .eq(PosCashTable::getStatus, CashTableStatusEnum.TIMING.getCode()));
        ArgumentAssert.isFalse(count <= 0, "台桌非计时中，无法设置定时");
        if (posCash.getIsPackField() != null && posCash.getIsPackField()) {
            PosCashPackField packField = posCashPackFieldService.getOne(Wraps.<PosCashPackField>lbQ()
                    .eq(PosCashPackField::getCashId, posCash.getId())
                    .eq(PosCashPackField::getTableId, posCash.getTableId()).eq(PosCashPackField::getDeleteFlag, 0)
                    .orderByDesc(PosCashPackField::getCreatedTime).last("limit 1"));
            ArgumentAssert.notNull(packField, "包台信息不存在");
            long countPack = posCashTableService.count(Wraps.<PosCashTable>lbQ()
                    .eq(PosCashTable::getCashId, posCash.getId())
                    .eq(PosCashTable::getDeleteFlag, 0)
                    .eq(PosCashTable::getIsPackField, true)
                    .eq(PosCashTable::getStatus, CashTableStatusEnum.TIMING.getCode())
                    .eq(PosCashTable::getTableId, posCash.getTableId())
                    .isNull(PosCashTable::getCashThailId)
            );
            ArgumentAssert.isFalse(countPack > 0,
                    "当前台桌正在包台中，不支持重新定时");
        }
        timingDuration(posCash, query);
        //新增操作日志
        baseBizLogService.createBizLog(BaseBizLog.builder()
                .tenantId(ContextUtil.getTenantId())
                .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                .description(BizLogModuleEnum.SET_TIMING.getDesc() + "【+" + query.getDuration().toString() + "分钟】")
                .bizModule(BizLogModuleEnum.SET_TIMING.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                .sourceId(posCash.getId()).remarks("")
                .build());
        return cashService.updateById(posCash);
    }

    @Override
    public void timingDuration(PosCash posCash, ItemSetTimingQuery query) {

        //设置定时
        Integer timingDuration = posCash.getTimingDuration() == null ? 0 : posCash.getTimingDuration();
        posCash.setTimingDuration(timingDuration + query.getDuration());
        //总时间
        int calcDuration = query.getDuration();
        if (ObjectUtil.equal(1, query.getType())) {
            List<PosCashTable> cashTableList = posCashTableService.list(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, posCash.getId())
                    .isNull(PosCashTable::getCashThailId)
                    .eq(PosCashTable::getTableId, posCash.getTableId()));
            cashTableList.forEach(v -> v.setDuration(v.getDuration() == null ? 0 : v.getDuration()));
            int duration = cashTableList.stream().mapToInt(PosCashTable::getDuration).sum();
            ArgumentAssert.isFalse(duration > query.getDuration(),
                    "请设置大于总时长的时间");
            query.setDuration(query.getDuration() - duration);
            posCash.setTimingDuration(query.getDuration());
            calcDuration = query.getDuration() - timingDuration;
        }
        PosCashTable posCashTable = posCashTableService.getOne(Wraps.<PosCashTable>lbQ()
                .isNull(PosCashTable::getCashThailId)
                .eq(PosCashTable::getTableId, posCash.getTableId())
                .eq(PosCashTable::getCashId, posCash.getId())
                .eq(PosCashTable::getStatus, CashTableStatusEnum.TIMING.getCode())
                .orderByDesc(PosCashTable::getEndTime).last("limit 1"));
        //更新或新增定时
        baseJobInfoService.saveOrUpdateJob(SaveOrUpdateJobQuery.builder()
                .jobTypeEnum(JobTypeEnum.TABLE_TIMING).tenantId(ContextUtil.getTenantId())
                .employeeId(ContextUtil.getEmployeeId())
                .currentCompanyId(ContextUtil.getCurrentCompanyId()).userId(ContextUtil.getUserId())
                .posCash(posCash).startTime(posCashTable == null || posCashTable.getEndTime() == null ? LocalDateTime.now().withSecond(0).withNano(0) :
                        posCashTable.getEndTime().withSecond(0).withNano(0))
                .desc("【" + posCash.getTableName() + "】")
                .duration(calcDuration).name("【" + posCash.getTableName() + "】")
                .sn(ContextUtil.getSn())
                .bizId(null).build());
        //更新或新增定时
        int closeLightRemind = 3;
        Map<String, String> data = helperApi.findParams(Collections.singletonList(ParameterKey.CLOSE_LIGHT_REMIND)).getData();
        if (CollUtil.isNotEmpty(data) && StrUtil.isNotBlank(data.get(ParameterKey.CLOSE_LIGHT_REMIND))) {
            closeLightRemind = Integer.parseInt(data.get(ParameterKey.CLOSE_LIGHT_REMIND));
        }
        if (closeLightRemind >= 3) {
            baseJobInfoService.saveOrUpdateJob(SaveOrUpdateJobQuery.builder()
                    .jobTypeEnum(JobTypeEnum.VOICE_TIMING).tenantId(ContextUtil.getTenantId())
                    .employeeId(ContextUtil.getEmployeeId())
                    .currentCompanyId(ContextUtil.getCurrentCompanyId()).userId(ContextUtil.getUserId())
                    .posCash(posCash).startTime(posCashTable == null || posCashTable.getEndTime() == null ? LocalDateTime.now().
                            minusMinutes(closeLightRemind).withSecond(0).withNano(0) :
                            posCashTable.getEndTime().minusMinutes(closeLightRemind)).desc("【" + posCash.getTableName() + "】")
                    .duration(calcDuration).name("【" + posCash.getTableName() + "】")
                    .desc(posCash.getTableName() + "倒计时".concat(closeLightRemind + "") + "分钟")
                    .sn(ContextUtil.getSn())
                    .bizId(null).build());
        }
        posCash.setUpdatedTime(LocalDateTime.now());
        posCash.setIsTemporaryLights(false);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean setServiceTiming(SetServiceTimingQuery query) {
        PosCash posCash = cashService.getById(query.getPosCashId());
        checkOrderService.checkItemPosCashNoOpera(posCash);
        PosCashService posCashService = posCashServiceService.getOne(Wraps.<PosCashService>lbQ().eq(PosCashService::getCashId, posCash.getId())
                .isNull(PosCashService::getCashThailId)
                .eq(PosCashService::getId, query.getId())
                .eq(PosCashService::getStatus, CashTableStatusEnum.TIMING.getCode()).last("limit 1"));
        ArgumentAssert.notNull(posCashService, "服务非计时中，无法设置定时");
        //设置定时
        Integer timingDuration = posCashService.getTimingDuration() == null ? 0 : posCashService.getTimingDuration();
        //增加定时
        posCashService.setTimingDuration(timingDuration + query.getDuration());
        int calcDuration = query.getDuration();
        if (ObjectUtil.equal(1, query.getType())) {
            int duration = posCashService.getDuration();
            ArgumentAssert.isFalse(duration > query.getDuration(),
                    "请设置大于总时长的时间");
            query.setDuration(query.getDuration() - duration);
            posCashService.setTimingDuration(query.getDuration());
            calcDuration = query.getDuration() - timingDuration;
        }
        String timingDesc = posCashService.getEmployeeName() + BizLogModuleEnum.SET_TIMING.getDesc() + "【+" + query.getDuration().toString() + "分钟】";
        //新增操作日志
        baseBizLogService.createBizLog(BaseBizLog.builder()
                .tenantId(ContextUtil.getTenantId())
                .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                .description(timingDesc)
                .bizModule(BizLogModuleEnum.SET_TIMING.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                .sourceId(posCash.getId()).remarks("")
                .build());
        //更新或新增定时
        baseJobInfoService.saveOrUpdateJob(SaveOrUpdateJobQuery.builder()
                .jobTypeEnum(JobTypeEnum.SERVICE_TIMING).tenantId(ContextUtil.getTenantId())
                .employeeId(ContextUtil.getEmployeeId())
                .currentCompanyId(ContextUtil.getCurrentCompanyId()).userId(ContextUtil.getUserId())
                .posCash(posCash).startTime(posCashService.getEndTime()
                        .withSecond(0).withNano(0)).desc("【" + posCashService.getEmployeeName() + "】")
                .duration(calcDuration).name("【" + posCashService.getEmployeeName() + "】")
                .sn(ContextUtil.getSn())
                .bizId(posCashService.getId()).build());
        return posCashServiceService.updateById(posCashService);
    }


    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean stopTiming(ItemStopTimingQuery query) {
        PosCash posCash = cashService.getById(query.getPosCashId());
        checkOrderService.checkItemPosCash(posCash);
        String desc = "";
        switch (EquityTypeEnum.get(query.getType())) {
            //台费 停止计时
            case TABLE:
                PosCashTable posCashTable = posCashTableService.getById(query.getId());
                ArgumentAssert.notNull(posCashTable, "请刷新后重试！");
                ArgumentAssert.isFalse(!ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.TIMING.getCode()), "请先停止计时后再操作");
                posCashTable.setEndTime(LocalDateTime.now().withSecond(0).withNano(0));
                long startMinutes = DateUtils.calDifMinutes(posCashTable.getStartTime(), posCashTable.getEndTime());
                posCashTable.setDuration(Integer.valueOf(startMinutes + ""));
                calcPriceService.refreshTable(posCashTable);
                posCashTable.setStatus(CashTableStatusEnum.STOP.getCode());
                posCashTable.setUpdatedTime(LocalDateTime.now());
                posCashTableService.updateById(posCashTable);
                posCashTableService.checkTableStop(posCash);
                desc = posCashTable.getTableName() + EquityTypeEnum.get(query.getType()).getDesc();
                desc = desc.concat("(手动关灯)");
                break;
            case SERVICE:
                //服务 停止计时
                PosCashService posCashService = posCashServiceService.getById(query.getId());
                ArgumentAssert.notNull(posCashService, "请刷新后重试！");
                ArgumentAssert.isFalse(!ObjectUtil.equal(posCashService.getStatus(), CashTableStatusEnum.TIMING.getCode()), "请先停止计时后再操作");
                posCashService.setDuration(null);
                posCashService.setEndTime(LocalDateTime.now());
                calcPriceService.refreshService(posCashService);
                posCashService.setStatus(CashTableStatusEnum.STOP.getCode());
                posCashServiceService.updateById(posCashService);
                //验证是否在计时中
                calcPriceService.checkServicePersonalStatus(posCashService);
                calcPriceService.serviceActivityStop(posCashService, posCash);
                desc = posCashService.getEmployeeName();
                //停止定时
                baseJobInfoService.saveOrUpdateJob(SaveOrUpdateJobQuery.builder()
                        .currentCompanyId(ContextUtil.getCurrentCompanyId())
                        .tenantId(ContextUtil.getTenantId()).type(2)
                        .bizId(posCashService.getId()).jobTypeEnum(JobTypeEnum.SERVICE_TIMING)
                        .sn(ContextUtil.getSn())
                        .build());
                break;
            case POWER:
                //充电 停止计时
                PosCashPower posCashPower = posCashPowerService.getById(query.getId());
                ArgumentAssert.notNull(posCashPower, "请刷新后重试！");
                ArgumentAssert.isFalse(!ObjectUtil.equal(posCashPower.getStatus(), CashTableStatusEnum.TIMING.getCode()), "请先停止计时后再操作");
                posCashPower.setDuration(null);
                posCashPower.setEndTime(LocalDateTime.now().withSecond(0).withNano(0));
                posCashPowerService.calcPrice(posCashPower);
                posCashPower.setStatus(CashTableStatusEnum.STOP.getCode());
                posCashPowerService.updateById(posCashPower);
                desc = posCashPower.getName();
                //充电停止
                posCashPowerService.sendStop(posCashPower);
                break;
            default:
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "不支持此操作");
                break;
        }
        long count = posCashTableService.count(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, posCash.getId())
                .eq(PosCashTable::getStatus, CashTableStatusEnum.TIMING.getCode()));
        //查询此服务是否在计时中
        long count2 = posCashServiceService.count(Wraps.<PosCashService>lbQ().eq(PosCashService::getCashId, posCash.getId())
                .eq(PosCashService::getStatus, CashTableStatusEnum.TIMING.getCode()));
        long count1 = posCashThailService.count(Wraps.<PosCashThail>lbQ()
                .eq(PosCashThail::getCashId, posCash.getId())
                .eq(PosCashThail::getStatus, CashTableStatusEnum.TIMING.getCode()));
        long powerCount = posCashPowerService.count(Wraps.<PosCashPower>lbQ()
                .eq(PosCashPower::getCashId, posCash.getId())
                .eq(PosCashPower::getStatus, CashTableStatusEnum.TIMING.getCode()));
        if (count == 0) {
            if (count1 == 0) {
                posCash.setTimingDuration(0);
                //停止定时
                baseJobInfoService.saveOrUpdateJob(SaveOrUpdateJobQuery.builder()
                        .currentCompanyId(ContextUtil.getCurrentCompanyId())
                        .tenantId(ContextUtil.getTenantId()).type(1)
                        .posCash(posCash).jobTypeEnum(JobTypeEnum.TABLE_TIMING)
                        .sn(ContextUtil.getSn())
                        .build());
                //停止定时
                baseJobInfoService.saveOrUpdateJob(SaveOrUpdateJobQuery.builder()
                        .currentCompanyId(ContextUtil.getCurrentCompanyId())
                        .tenantId(ContextUtil.getTenantId()).type(1)
                        .posCash(posCash).jobTypeEnum(JobTypeEnum.VOICE_TIMING)
                        .sn(ContextUtil.getSn())
                        .build());
            }
        }
        if (count == 0 && count1 == 0 && count2 == 0
                && powerCount == 0) {
            posCash.setBillState(PosCashBillStateEnum.NO_PAY.getCode());
            posCash.setUpdatedBy(ContextUtil.getUserId());
        }
        posCash.setUpdatedTime(LocalDateTime.now());
        cashService.updateById(posCash);
        //新增操作日志
        baseBizLogService.createBizLog(BaseBizLog.builder()
                .tenantId(ContextUtil.getTenantId())
                .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                .description("停止计时【" + desc + "】")
                .bizModule(BizLogModuleEnum.ITEM_STOP_TIMING.getCode())
                .type(BizLogTypeEnum.UPDATED.getCode())
                .sourceId(posCash.getId()).remarks("")
                .build());

        // 停止计时
        if (ObjectUtil.isNotNull(posCash.getTableId())) {
            wsNoticeDeviceService.sendRefreshMsg(posCash.getTableId(), false, false);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean continueTiming(ItemStopTimingQuery query) {
        PosCash posCash = cashService.getById(query.getPosCashId());
        checkOrderService.checkItemPosCashNoOpera(posCash);
        checkOrderService.checkOrderDiscountPosCash(posCash);
        switch (EquityTypeEnum.get(query.getType())) {
            //台费 继续计时
            case TABLE:
                PosCashTable posCashTable = posCashTableService.getById(query.getId());
                ArgumentAssert.notNull(posCashTable, "请刷新后重试！");
                ArgumentAssert.isFalse(ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.TIMING.getCode()), "正在计时中...");
                ArgumentAssert.isFalse(ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.REFUND.getCode()), "当前状态无法操作");
                ArgumentAssert.isFalse(!ObjectUtil.equal(posCashTable.getTableId(), posCash.getTableId()), "仅支持操作当前台桌");
                //查询此台桌是否在计时中
                long count = posCashTableService.count(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getTableId, posCashTable.getTableId())
                        .eq(PosCashTable::getCashId, posCashTable.getCashId()).eq(PosCashTable::getStatus, CashTableStatusEnum.TIMING.getCode()));
                ArgumentAssert.isFalse(count > 0, "此台桌存在计时，请勿重复操作");
                PosCashTable cashTable = PosCashTable.builder()
                        .status(CashTableStatusEnum.TIMING.getCode())
                        .duration(null).startTime(LocalDateTime.now()).cashId(posCash.getId())
                        .discount(BigDecimal.ZERO).price(posCashTable.getPrice())
                        .chargingSettingId(posCashTable.getChargingSettingId())
                        .discountType(DiscountTypeEnum.ORIGINAL.getCode())
                        .cycle(posCashTable.getCycle()).amount(BigDecimal.ZERO)
                        .discountAmount(BigDecimal.ZERO).tableName(posCashTable.getTableName())
                        .orginPrice(BigDecimal.ZERO)
                        .cashId(posCashTable.getCashId()).tableId(posCashTable.getTableId())
                        .build();
                cashTable.setStatus(CashTableStatusEnum.TIMING.getCode());
                cashTable.setDuration(null);
                cashTable.setStartTime(LocalDateTime.now().withSecond(0).withNano(0));
                cashTable.setId(null);
                cashTable.setSn(ContextUtil.getSn());
                posCashTableService.save(cashTable);
                posCashTableService.checkTableUsed(posCash);
                //新增操作日志
                baseBizLogService.createBizLog(BaseBizLog.builder()
                        .tenantId(ContextUtil.getTenantId())
                        .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                        .description(posCashTable.getTableName() + EquityTypeEnum.get(query.getType()).getDesc() + "继续计时")
                        .bizModule(BizLogModuleEnum.CONTINUE_TIMING.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                        .sourceId(posCash.getId()).remarks("")
                        .build());
                break;
            case SERVICE:
                //服务 继续计时
                PosCashService posCashService = posCashServiceService.getById(query.getId());
                ArgumentAssert.notNull(posCashService, "请刷新后重试！");
                ArgumentAssert.isFalse(ObjectUtil.equal(posCashService.getStatus(), CashTableStatusEnum.TIMING.getCode()), "正在计时中...");
                ArgumentAssert.isFalse(ObjectUtil.equal(posCashService.getStatus(), CashTableStatusEnum.REFUND.getCode()), "当前状态无法操作");
                //查询此台桌是否在计时中
                count = posCashServiceService.count(Wraps.<PosCashService>lbQ().eq(PosCashService::getServiceId, posCashService.getServiceId())
                        .eq(PosCashService::getEmployeeId, posCashService.getEmployeeId())
                        .eq(PosCashService::getCashId, posCashService.getCashId()).eq(PosCashService::getStatus, CashTableStatusEnum.TIMING.getCode()));
                ArgumentAssert.isFalse(count > 0, "服务人员存在计时，请勿重复操作");
                PosCashService cashService = PosCashService.builder()
                        .status(CashTableStatusEnum.TIMING.getCode())
                        .duration(null).startTime(LocalDateTime.now()).orginPrice(BigDecimal.ZERO)
                        .discount(BigDecimal.ZERO).price(posCashService.getPrice())
                        .discountType(DiscountTypeEnum.ORIGINAL.getCode())
                        .cycle(posCashService.getCycle()).amount(BigDecimal.ZERO).cashId(posCash.getId())
                        .discountAmount(BigDecimal.ZERO).employeeName(posCashService.getEmployeeName())
                        .employeeId(posCashService.getEmployeeId()).serviceId(posCashService.getServiceId())
                        .num(1).build();
                cashService.setId(null);
                cashService.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                cashService.setSn(ContextUtil.getSn());
                posCashServiceService.save(cashService);
                //验证是否在计时中
                calcPriceService.checkServicePersonalStatus(cashService);
                //新增操作日志
                baseBizLogService.createBizLog(BaseBizLog.builder()
                        .tenantId(ContextUtil.getTenantId())
                        .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                        .description(posCashService.getEmployeeName() + "继续计时")
                        .bizModule(BizLogModuleEnum.CONTINUE_TIMING.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                        .sourceId(posCash.getId()).remarks("")
                        .build());
                break;
            default:
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "不支持此操作");
                break;
        }
        posCash.setBillState(PosCashBillStateEnum.NO_SETTLED.getCode());
        posCash.setUpdatedBy(ContextUtil.getUserId());
        posCash.setUpdatedTime(LocalDateTime.now());
        return cashService.updateById(posCash);
    }

    @Override
    public Boolean cancelDiscount(CancelDiscountQuery query) {
        PosCash posCash = cashService.getById(query.getPosCashId());
        checkOrderService.checkItemPosCash(posCash);
        BigDecimal discount = BigDecimal.ZERO;
        String discountType = "";
        switch (EquityTypeEnum.get(query.getType())) {
            //台费
            case TABLE:
                PosCashTable posCashTable = posCashTableService.getById(query.getId());
                ArgumentAssert.notNull(posCashTable, "请刷新后重试！");
                ArgumentAssert.isNull(posCashTable.getCashThailId(), "已享受套餐优惠，无法操作");
                if (!ObjectUtil.equal(posCashTable.getDiscountType(), DiscountTypeEnum.HAND_DISCOUNT.getCode())
                        && !ObjectUtil.equal(posCashTable.getDiscountType(), DiscountTypeEnum.HAND_REDUCTION.getCode())) {
                    ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "优惠不存在，无法取消");
                }
                discount = posCashTable.getDiscount();
                discountType = posCashTable.getDiscountType();
                posCashTable.setDiscount(BigDecimal.ZERO);
                posCashTable.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                posCashTable.setDiscountRemarks(setRemarks(Lists.newArrayList(), "",
                        posCashTable.getRemarks(), EchoDictType.App.ITEM_DISCOUNT_TAGS));
                posCashTableService.updateById(posCashTable);
                break;
            case SERVICE:
                //服务
                PosCashService posCashService = posCashServiceService.getById(query.getId());
                ArgumentAssert.notNull(posCashService, "请刷新后重试！");
                ArgumentAssert.isNull(posCashService.getCashThailId(), "已享受套餐优惠，无法操作");
                if (!ObjectUtil.equal(posCashService.getDiscountType(), DiscountTypeEnum.HAND_DISCOUNT.getCode())
                        && !ObjectUtil.equal(posCashService.getDiscountType(), DiscountTypeEnum.HAND_REDUCTION.getCode())) {
                    ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "优惠不存在，无法取消");
                }
                discount = posCashService.getDiscount();
                discountType = posCashService.getDiscountType();
                posCashService.setDiscount(BigDecimal.ZERO);
                posCashService.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                posCashService.setDiscountRemarks(setRemarks(Lists.newArrayList(), "",
                        posCashService.getRemarks(), EchoDictType.App.ITEM_DISCOUNT_TAGS));
                posCashServiceService.updateById(posCashService);
                break;
            case PRODUCT:
                //商品

                PosCashProduct posCashProduct = posCashProductService.getById(query.getId());
                ArgumentAssert.notNull(posCashProduct, "请刷新后重试！");
                if (!ObjectUtil.equal(posCashProduct.getDiscountType(), DiscountTypeEnum.HAND_DISCOUNT.getCode())
                        && !ObjectUtil.equal(posCashProduct.getDiscountType(), DiscountTypeEnum.HAND_REDUCTION.getCode())) {
                    ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "优惠不存在，无法取消");
                }
                discount = posCashProduct.getDiscount();
                discountType = posCashProduct.getDiscountType();
                posCashProduct.setDiscount(BigDecimal.ZERO);
                posCashProduct.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                posCashProduct.setDiscountRemarks(setRemarks(Lists.newArrayList(), "",
                        posCashProduct.getRemarks(), EchoDictType.App.ITEM_DISCOUNT_TAGS));
                posCashProductService.updateById(posCashProduct);
                break;
            case CARD:
                //卡
                PosCashCard posCashCard = posCashCardService.getById(query.getId());
                ArgumentAssert.notNull(posCashCard, "请刷新后重试！");
                if (!ObjectUtil.equal(posCashCard.getDiscountType(), DiscountTypeEnum.HAND_DISCOUNT.getCode())
                        && !ObjectUtil.equal(posCashCard.getDiscountType(), DiscountTypeEnum.HAND_REDUCTION.getCode())) {
                    ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "优惠不存在，无法取消");
                }
                discount = posCashCard.getDiscount();
                discountType = posCashCard.getDiscountType();
                posCashCard.setDiscount(BigDecimal.ZERO);
                posCashCard.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                posCashCard.setDiscountRemarks(setRemarks(Lists.newArrayList(), "",
                        posCashCard.getRemarks(), EchoDictType.App.ITEM_DISCOUNT_TAGS));
                posCashCardService.updateById(posCashCard);
                break;
            case THAIL:
                PosCashThail posCashThail = posCashThailService.getById(query.getId());
                ArgumentAssert.notNull(posCashThail, "请刷新后重试！");
                if (!ObjectUtil.equal(posCashThail.getDiscountType(), DiscountTypeEnum.HAND_DISCOUNT.getCode())
                        && !ObjectUtil.equal(posCashThail.getDiscountType(), DiscountTypeEnum.HAND_REDUCTION.getCode())) {
                    ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "优惠不存在，无法取消");
                }
                discount = posCashThail.getDiscount();
                discountType = posCashThail.getDiscountType();
                posCashThail.setDiscount(BigDecimal.ZERO);
                posCashThail.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                posCashThail.setDiscountRemarks(setRemarks(Lists.newArrayList(), "",
                        posCashThail.getRemarks(), EchoDictType.App.ITEM_DISCOUNT_TAGS));
                posCashThailService.updateById(posCashThail);
                break;
            case POWER:
                PosCashPower posCashPower = posCashPowerService.getById(query.getId());
                ArgumentAssert.notNull(posCashPower, "请刷新后重试！");
                if (!ObjectUtil.equal(posCashPower.getDiscountType(), DiscountTypeEnum.HAND_DISCOUNT.getCode())
                        && !ObjectUtil.equal(posCashPower.getDiscountType(), DiscountTypeEnum.HAND_REDUCTION.getCode())) {
                    ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "优惠不存在，无法取消");
                }
                discount = posCashPower.getDiscount();
                discountType = posCashPower.getDiscountType();
                posCashPower.setDiscount(BigDecimal.ZERO);
                posCashPower.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                posCashPower.setDiscountRemarks(setRemarks(Lists.newArrayList(), "",
                        posCashPower.getRemarks(), EchoDictType.App.ITEM_DISCOUNT_TAGS));
                posCashPowerService.updateById(posCashPower);
                break;
            default:
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "不支持此操作");
                break;
        }
        //新增操作日志
        baseBizLogService.createBizLog(BaseBizLog.builder()
                .tenantId(ContextUtil.getTenantId())
                .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                .description(BizLogModuleEnum.CANCEL_ITEM_DISCOUNT.getDesc() + "【"
                        + (ObjectUtil.equal(discountType, DiscountTypeEnum.HAND_DISCOUNT.getCode())
                        ? discount.toPlainString() + "折" : discount
                        .setScale(2, RoundingMode.HALF_UP).toPlainString() + "元") + "】")
                .bizModule(BizLogModuleEnum.CANCEL_ITEM_DISCOUNT.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                .sourceId(posCash.getId()).remarks("")
                .build());
        posCash.setRoundAmount(BigDecimal.ZERO);
        posCash.setIsRound(false);
        posCash.setIsAutoRound(false);
        posCash.setAutoRoundAmount(BigDecimal.ZERO);
        posCash.setUpdatedTime(LocalDateTime.now());
        return cashService.updateById(posCash);
    }

    @Override
    @GlobalTransactional
    public Boolean cancelTiming(PosCashIdQuery query) {
        PosCash posCash = cashService.getById(query.getPosCashId());
        checkOrderService.checkStopPosCash(posCash);
        checkOrderService.checkItemPosCashNoOpera(posCash);
        long count = posCashThailService.count(Wraps.<PosCashThail>lbQ().eq(PosCashThail::getCashId, query.getPosCashId())
                .eq(PosCashThail::getStatus, CashTableStatusEnum.TIMING.getCode()));
        ArgumentAssert.isFalse(count > 0, "已参与套餐，不能取消定时");
        if (posCash.getIsPackField() != null && posCash.getIsPackField()) {
            PosCashPackField packField = posCashPackFieldService.getOne(Wraps.<PosCashPackField>lbQ()
                    .eq(PosCashPackField::getCashId, posCash.getId())
                    .eq(PosCashPackField::getTableId, posCash.getTableId()).eq(PosCashPackField::getDeleteFlag, 0)
                    .orderByDesc(PosCashPackField::getCreatedTime).last("limit 1"));
            ArgumentAssert.notNull(packField, "包台信息不存在");
            long countPack = posCashTableService.count(Wraps.<PosCashTable>lbQ()
                    .eq(PosCashTable::getCashId, posCash.getId())
                    .eq(PosCashTable::getDeleteFlag, 0)
                    .eq(PosCashTable::getIsPackField, true)
                    .eq(PosCashTable::getStatus, CashTableStatusEnum.TIMING.getCode())
                    .eq(PosCashTable::getTableId, posCash.getTableId())
                    .isNull(PosCashTable::getCashThailId));
            ArgumentAssert.isFalse(countPack > 0, "启用包台，无法取消定时");
        }
        //停止定时
        baseJobInfoService.saveOrUpdateJob(SaveOrUpdateJobQuery.builder()
                .currentCompanyId(ContextUtil.getCurrentCompanyId())
                .tenantId(ContextUtil.getTenantId()).type(1)
                .posCash(posCash).jobTypeEnum(JobTypeEnum.TABLE_TIMING)
                .sn(ContextUtil.getSn())
                .build());
        baseJobInfoService.saveOrUpdateJob(SaveOrUpdateJobQuery.builder()
                .currentCompanyId(ContextUtil.getCurrentCompanyId())
                .tenantId(ContextUtil.getTenantId()).type(1)
                .sn(ContextUtil.getSn())
                .posCash(posCash).jobTypeEnum(JobTypeEnum.VOICE_TIMING)
                .build());
        //新增操作日志
        baseBizLogService.createBizLog(BaseBizLog.builder()
                .tenantId(ContextUtil.getTenantId())
                .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                .description(BizLogModuleEnum.CANCEL_TIMING.getDesc() + "【+" + posCash.getTimingDuration() + "分钟】")
                .bizModule(BizLogModuleEnum.CANCEL_TIMING.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                .sourceId(posCash.getId()).remarks("")
                .build());
        posCash.setTimingDuration(0);
        posCash.setUpdatedTime(LocalDateTime.now());
        posCash.setIsTemporaryLights(false);
        return cashService.updateById(posCash);
    }

    @Override
    @GlobalTransactional
    public Boolean cancelServiceTiming(IdQuery query) {
        PosCashService posCashService = posCashServiceService.getOne(Wraps.<PosCashService>lbQ()
                .isNull(PosCashService::getCashThailId)
                .eq(PosCashService::getId, query.getId()));
        ArgumentAssert.notNull(posCashService, "记录不存在，刷新后重试");
        ArgumentAssert.isFalse(!ObjectUtil.equal(posCashService.getStatus(), CashTableStatusEnum.TIMING.getCode()),
                "非计时中，无法取消定时");
        PosCash posCash = cashService.getById(posCashService.getCashId());
        checkOrderService.checkStopPosCash(posCash);
        checkOrderService.checkItemPosCashNoOpera(posCash);
        //停止定时
        baseJobInfoService.saveOrUpdateJob(SaveOrUpdateJobQuery.builder()
                .currentCompanyId(ContextUtil.getCurrentCompanyId())
                .tenantId(ContextUtil.getTenantId()).type(2)
                .sn(ContextUtil.getSn())
                .bizId(posCashService.getId()).jobTypeEnum(JobTypeEnum.SERVICE_TIMING)
                .build());
        //新增操作日志
        baseBizLogService.createBizLog(BaseBizLog.builder()
                .tenantId(ContextUtil.getTenantId())
                .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                .description(posCashService.getEmployeeName() + BizLogModuleEnum.CANCEL_TIMING.getDesc()
                        + "【" + posCashService.getTimingDuration() + "分钟】")
                .bizModule(BizLogModuleEnum.CANCEL_TIMING.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                .sourceId(posCash.getId()).remarks("")
                .build());
        posCashService.setTimingDuration(0);
        return posCashServiceService.updateById(posCashService);
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public Boolean orderContinueTiming(PosCashIdQuery query) {
        PosCash posCash = cashService.getById(query.getPosCashId());
        checkOrderService.checkItemPosCashNoOpera(posCash);
        checkOrderService.checkOrderDiscountPosCash(posCash);
        ArgumentAssert.isFalse(ObjectUtil.equal(posCash.getBillType(), PosCashBillTypeEnum.COUNTER_CHECKOUT.getCode()),
                "反结账无法开灯继续");
        long count = posCashThailService.count(Wraps.<PosCashThail>lbQ().eq(PosCashThail::getCashId, query.getPosCashId())
                .eq(PosCashThail::getStatus, CashTableStatusEnum.TIMING.getCode()));
        ArgumentAssert.isFalse(count > 0, "套餐存在计时，无法操作");
        count = posCashEquityService.count(Wraps.<PosCashEquity>lbQ().eq(PosCashEquity::getCashId, query.getPosCashId())
                .eq(PosCashEquity::getDeleteFlag, 0));
        ArgumentAssert.isFalse(count > 0, "存在卡劵优惠，请移除后重试");

        //查询此台桌是否在计时中
        count = posCashTableService.count(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, query.getPosCashId())
                .eq(PosCashTable::getDeleteFlag, 0)
                .isNull(PosCashTable::getCashThailId)
                .eq(PosCashTable::getStatus, CashTableStatusEnum.TIMING.getCode()));
        ArgumentAssert.isFalse(count > 0, "台桌存在计时，请勿重复操作");
        orderContinue(posCash);
        //新增操作日志
        baseBizLogService.createBizLog(BaseBizLog.builder()
                .tenantId(ContextUtil.getTenantId())
                .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                .description(BizLogModuleEnum.ORDER_CONTINUE_TIMING.getDesc())
                .bizModule(BizLogModuleEnum.ORDER_CONTINUE_TIMING.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                .sourceId(posCash.getId()).remarks("")
                .build());
        boolean b = cashService.updateById(posCash);

        // 开灯继续
        if (ObjectUtil.isNotNull(posCash.getTableId())) {
            wsNoticeDeviceService.sendRefreshMsg(posCash.getTableId(), false, false);
        }

        return b;
    }

    @Override
    public void orderContinue(PosCash posCash) {
        //套餐 继续计时
        //获取最后一个台费明细
        PosCashTable posCashTable = posCashTableService.getOne(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, posCash.getId())
                .isNull(PosCashTable::getCashThailId)
                .eq(PosCashTable::getTableId, posCash.getTableId())
                .orderByDesc(PosCashTable::getCreatedTime).last(" limit 1"));

        if (ObjectUtil.isNull(posCashTable) && ObjectUtil.isNotNull(posCash.getThailId())) {
            posCashTable = posCashTableService.getOne(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, posCash.getId())
                    .isNotNull(PosCashTable::getCashThailId)
                    .orderByDesc(PosCashTable::getCreatedTime)
                    .last(" limit 1"));
        } else if (ObjectUtil.isNull(posCashTable) && ObjectUtil.isNull(posCash.getThailId())) {
            posCashTable = posCashTableService.getOne(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, posCash.getId())
                    .isNull(PosCashTable::getCashThailId)
                    .eq(PosCashTable::getTableId, posCash.getTableId())
                    .orderByDesc(PosCashTable::getCreatedTime)
                    .last(" limit 1"));
        }
        if (posCashTable != null) {
            ArgumentAssert.isFalse(ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.TIMING.getCode()), "正在计时中...");
        } else {
            posCashTable = PosCashTable.builder()
                    .status(CashTableStatusEnum.TIMING.getCode())
                    .duration(0).cashId(posCash.getId())
                    .discount(BigDecimal.ZERO).price(BigDecimal.ZERO)
                    .chargingSettingId(0L)
                    .discountType(DiscountTypeEnum.ORIGINAL.getCode())
                    .cycle("").amount(BigDecimal.ZERO)
                    .discountAmount(BigDecimal.ZERO).tableName(posCash.getTableName())
                    .orginPrice(BigDecimal.ZERO).startTime(LocalDateTime.now())
                    .cashId(posCash.getId()).tableId(posCash.getTableId())
                    .build();
        }
        ArgumentAssert.isFalse(ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.REFUND.getCode()), "当前状态无法操作");
        PosCashTable cashTable;
        //总开台时长
        int sumDuration;
        BaseTableInfo tableInfo = baseTableInfoService.getById(posCashTable.getTableId());
//        if (posCash.getIsPackField() != null && posCash.getIsPackField()) {
//
//            PosCashPackField packField = posCashPackFieldService.getOne(Wraps.<PosCashPackField>lbQ().eq(PosCashPackField::getCashId, posCash.getId())
//                    .eq(PosCashPackField::getTableId, posCashTable.getTableId())
//                    .eq(PosCashPackField::getDeleteFlag, 0));
//            ArgumentAssert.notNull(packField, "包台信息异常！");
//            PackFieldSaveVO packFieldSaveVO = BeanUtil.copyProperties(packField, PackFieldSaveVO.class);
//            packFieldSaveVO.setIsPackField(true);
//            sumDuration = posCashTableService.listObjs(Wraps.<PosCashTable>lbQ()
//                    .select(PosCashTable::getDuration)
//                    .eq(PosCashTable::getCashId, posCash.getId())
//                    .eq(PosCashTable::getDeleteFlag, 0)
//                    .eq(PosCashTable::getIsPackField, true)
//                    .ne(PosCashTable::getStatus, CashTableStatusEnum.REFUND.getCode())
//                    .eq(PosCashTable::getTableId, posCashTable.getTableId())
//                    .isNull(PosCashTable::getCashThailId), Convert::toInt).stream().mapToInt(Convert::toInt).sum();
//            cashTable = posCashTableService.getPackField(posCash, tableInfo, packFieldSaveVO);
//            if (sumDuration >= packField.getPackFieldDuration()) {
//                cashTable.setIsPackField(false);
//            }
//            //定时
//            if (sumDuration < packField.getPackFieldDuration()) {
//                //更新或新增定时
//                posCash.setTimingDuration(packField.getPackFieldDuration() - sumDuration);
//                baseJobInfoService.saveOrUpdateJob(SaveOrUpdateJobQuery.builder()
//                        .employeeId(ContextUtil.getEmployeeId())
//                        .jobTypeEnum(JobTypeEnum.TABLE_TIMING).tenantId(ContextUtil.getTenantId())
//                        .currentCompanyId(ContextUtil.getCurrentCompanyId()).userId(ContextUtil.getUserId())
//                        .posCash(posCash).startTime(LocalDateTime.now()).desc("【" + posCash.getTableName() + "】")
//                        .duration(posCash.getTimingDuration()).name("【" + posCash.getTableName() + "】")
//                        .sn(ContextUtil.getSn())
//                        .bizId(null).build());
//            }
//            //更新或新增定时
//            int closeLightRemind = 3;
//            Map<String, String> data = helperApi.findParams(Collections.singletonList(ParameterKey.CLOSE_LIGHT_REMIND)).getData();
//            if (CollUtil.isNotEmpty(data) && StrUtil.isNotBlank(data.get(ParameterKey.CLOSE_LIGHT_REMIND))) {
//                closeLightRemind = Integer.parseInt(data.get(ParameterKey.CLOSE_LIGHT_REMIND));
//            }
//            //语音
//            if (sumDuration < packField.getPackFieldDuration() - closeLightRemind) {
//                if (closeLightRemind >= 3) {
//                    baseJobInfoService.saveOrUpdateJob(SaveOrUpdateJobQuery.builder()
//                            .employeeId(ContextUtil.getEmployeeId())
//                            .jobTypeEnum(JobTypeEnum.VOICE_TIMING).tenantId(ContextUtil.getTenantId())
//                            .currentCompanyId(ContextUtil.getCurrentCompanyId()).userId(ContextUtil.getUserId())
//                            .posCash(posCash).startTime(LocalDateTime.now())
//                            .duration(posCash.getTimingDuration() - closeLightRemind).name("【" + posCash.getTableName() + "】")
//                            .desc(posCash.getTableName() + "倒计时".concat(closeLightRemind + "") + "分钟")
//                            .sn(ContextUtil.getSn())
//                            .bizId(null).build());
//                }
//            }
//
//        } else {
        //如果最新计时为第一轮计时
        //添加暂停记录
        if (posCashTable.getBillingLevel() != null
                && posCashTable.getBillingLevel().equals(BillingLevelEnum.ONE.getCode())) {
            cashTable = BeanUtil.copyProperties(posCashTable, PosCashTable.class);
            cashTable.setStatus(CashTableStatusEnum.TIMING.getCode());
            cashTable.setChargingSettingId(0L);
            PosCashStop cashStop = PosCashStop.builder().createdOrgId(ContextUtil.getCurrentCompanyId()).startTime(posCashTable.getEndTime()
                            .plusSeconds(1).withNano(0)).endTime(LocalDateTime.now().withSecond(0).withNano(0))
                    .cashId(posCash.getId()).sourceId(cashTable.getId()).bizType(BaseEquityTypeEnum.TABLE.getCode())
                    .build();
            Long startMinutes = DateUtils.calDifMinutes(cashStop.getStartTime(), cashStop.getEndTime());
            cashStop.setDuration(Integer.valueOf(startMinutes + ""));
            cashTable.setEndTime(LocalDateTime.now().withNano(0));
            ArgumentAssert.isFalse(!posCashStopService.save(cashStop), "暂停记录添加失败！");
        } else {
            cashTable = PosCashTable.builder()
                    .status(CashTableStatusEnum.TIMING.getCode())
                    .duration(0).cashId(posCash.getId()).billingLevel(posCashTable.getBillingLevel())
                    .discount(BigDecimal.ZERO).price(posCashTable.getPrice())
                    .chargingSettingId(posCashTable.getChargingSettingId())
                    .discountType(DiscountTypeEnum.ORIGINAL.getCode())
                    .cycle(posCashTable.getCycle()).amount(BigDecimal.ZERO)
                    .discountAmount(BigDecimal.ZERO).tableName(posCashTable.getTableName())
                    .orginPrice(BigDecimal.ZERO).startTime(LocalDateTime.now())
                    .cashId(posCashTable.getCashId()).tableId(posCashTable.getTableId())
                    .build();
        }
        //总开台时长
        sumDuration = posCashTableService.listObjs(Wraps.<PosCashTable>lbQ()
                .select(PosCashTable::getDuration)
                .eq(PosCashTable::getCashId, posCash.getId())
                .eq(PosCashTable::getDeleteFlag, 0)
                .ne(PosCashTable::getIsPackField, true)
                .ne(PosCashTable::getStatus, CashTableStatusEnum.REFUND.getCode())
                .eq(PosCashTable::getTableId, posCashTable.getTableId())
                .isNull(PosCashTable::getCashThailId), Convert::toInt).stream().mapToInt(Convert::toInt).sum();
        if (ObjectUtil.isNull(posCashTable.getCashThailId())
                && posCashTable.getChargingSettingId() != null && posCashTable.getChargingSettingId() != 0) {
            //判断是否满足一个周期如果不满足则重新计算价格
            BaseTableCharging tableCharging = baseTableChargingService.getOne(Wraps.<BaseTableCharging>lbQ().inSql(BaseTableCharging::getId,
                    "select charging_id from base_table_charging_setting where id ="
                            + posCashTable.getChargingSettingId()).last(" limit 1"));
            ArgumentAssert.notNull(tableCharging, "计费不存在，请联系管理员");
            tableCharging.setContinuingRules(ContinuingRulesEnum.MINUTE.getCode());
            if (StrUtil.isNotBlank(tableCharging.getContinuingRules())
                    && ObjectUtil.equal(tableCharging.getContinuingRules(), ContinuingRulesEnum.MINUTE.getCode())) {
                //是否体验
                //剩余时长
                int remainingDuration = sumDuration % tableCharging.getPeriod();
                if (ObjectUtil.isNotNull(posCashTable.getCalcType())) {
                    Integer calcDuration = posCashTable.getCalcDuration();
                    if (posCashTable.getDuration() < calcDuration) {
                        cashTable.setCalcDuration(calcDuration - posCashTable.getDuration());
                        posCashTable.setCalcDuration(posCashTable.getDuration());
                    } else {
                        posCashTable.setCalcDuration(remainingDuration);
                        cashTable.setCalcDuration(tableCharging.getPeriod() - remainingDuration);
                    }
                } else {
                    posCashTable.setCalcDuration(remainingDuration);
                    cashTable.setCalcDuration(tableCharging.getPeriod() - remainingDuration);
                }
                posCashTable.setCalcType(1);
                posCashTableService.updateById(posCashTable);
                //新的一条
                cashTable.setCalcType(1);
            }
        }
//        }
        //停止临时灯
        baseJobInfoService.stop(posCash, JobTypeEnum.CASH_CLOSE_LIGHT_TIMING);
        calcPriceService.checkServiceActivity(posCash, cashTable);
        cashTable.setSn(ContextUtil.getSn());
        cashTable.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
        cashTable.setDiscount(BigDecimal.ZERO);
        cashTable.setDiscountAmount(BigDecimal.ZERO);
        cashTable.setMemberCardId(null);
        cashTable.setCouponId(null);
        posCashTableService.saveOrUpdate(cashTable);
//        posCashTableService.checkTableUsed(posCash);
        if (tableInfo != null) {
            tableInfo.setTableStatus(TableStatus.USING.getCode());
            BaseTableInfoUpdateVO build = BaseTableInfoUpdateVO.builder()
                    .tableStatus(TableStatus.USING.getCode())
                    .id(tableInfo.getId())
                    .build();
            baseTableInfoService.updateById(build);
            //产品经理强制要求继续计时 开灯
            cashStateManager.handleBooleanEvent(posCash.getId(), PosCashConstant.Event.OPEN_LIGHT, posCash, tableInfo);
        }
        posCash.setBillState(PosCashBillStateEnum.NO_SETTLED.getCode());
        posCash.setUpdatedBy(ContextUtil.getUserId());
        posCash.setUpdatedTime(LocalDateTime.now());
        posCash.setIsTemporaryLights(false);
    }


    @Override
    public Boolean commenter(ItemCommenterQuery query) {
        PosCash posCash = cashService.getById(query.getPosCashId());
        checkOrderService.checkCommenterPosCash(posCash);
        String desc = "";
        List<PosCashCommenter> commenterList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(query.getCommenterList())) {
            Map<Long, BaseEmployee> employeeMap = baseEmployeeService.list(Wraps.<BaseEmployee>lbQ()
                            .in(BaseEmployee::getId, query.getCommenterList()))
                    .stream().collect(Collectors.toMap(BaseEmployee::getId, k -> k));
            commenterList = query.getCommenterList().stream().map(v -> PosCashCommenter.builder()
                    .cashId(posCash.getId())
                    .createdOrgId(ContextUtil.getCurrentCompanyId())
                    .type(query.getType())
                    .sourceId(query.getId()).employeeId(v)
                    .build()).collect(Collectors.toList());
            desc = employeeMap.values().stream().map(BaseEmployee::getRealName).collect(Collectors.joining(","));
        }
        switch (EquityTypeEnum.get(query.getType())) {
            //台费
            case PRODUCT:
                PosCashProduct posCashProduct = posCashProductService.getById(query.getId());
                ArgumentAssert.notNull(posCashProduct, "商品明细不存在！");
                ArgumentAssert.isNull(posCashProduct.getCashThailId(), "当前明细属于套餐，无法操作");
                break;
            case SERVICE:
                //服务
                PosCashService posCashService = posCashServiceService.getById(query.getId());
                ArgumentAssert.notNull(posCashService, "服务明细不存在！");
                ArgumentAssert.isNull(posCashService.getCashThailId(), "当前属于套餐，无法操作");
                break;
            //购卡
            case CARD:
                PosCashCard posCashCard = posCashCardService.getById(query.getId());
                ArgumentAssert.notNull(posCashCard, "卡明细不存在！");
                break;
            default:
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "不支持此操作");
                break;
        }
        posCashCommenterService.remove(Wraps.<PosCashCommenter>lbQ()
                .eq(PosCashCommenter::getCashId, posCash.getId())
                .eq(PosCashCommenter::getSourceId, query.getId())
                .eq(PosCashCommenter::getType, query.getType()));
        if (CollUtil.isNotEmpty(commenterList)) {
            posCashCommenterService.saveBatch(commenterList);
        }
        //新增操作日志
        baseBizLogService.createBizLog(BaseBizLog.builder()
                .tenantId(ContextUtil.getTenantId())
                .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                .description(BizLogModuleEnum.SETTING_COMMENTER.getDesc())
                .bizModule(BizLogModuleEnum.SETTING_COMMENTER.getCode())
                .type(BizLogTypeEnum.UPDATED.getCode())
                .sourceId(posCash.getId()).remarks(StrUtil.isBlank(desc) ? "设置为空" : desc)
                .build());
        return true;
    }

    @Override
    @GlobalTransactional
    public Boolean serviceTurn(ServiceTurnQuery query) {
        PosCash posCash = cashService.getById(query.getPosCashId());
        checkOrderService.checkItemPosCash(posCash);
        PosCashService posCashService = posCashServiceService.getById(query.getId());
        ArgumentAssert.notNull(posCashService, "服务明细不存在！");
        ArgumentAssert.isNull(posCashService.getCashThailId(), "当前属于套餐，无法操作");
        PosCash mergePosCash = cashService.getById(query.getMergePosCashId());
        checkOrderService.checkItemPosCash(mergePosCash);
        posCashService.setCashId(mergePosCash.getId());
        posCashService.setUpdatedTime(LocalDateTime.now());
        //新增操作日志
        baseBizLogService.createBizLog(BaseBizLog.builder()
                .tenantId(ContextUtil.getTenantId())
                .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                .description(BizLogModuleEnum.SERVICE_TURN.getDesc() + "到" + mergePosCash.getTableName())
                .bizModule(BizLogModuleEnum.SERVICE_TURN.getCode())
                .type(BizLogTypeEnum.UPDATED.getCode())
                .sourceId(posCash.getId()).remarks("")
                .build());
        boolean b = posCashServiceService.updateById(posCashService);

        // 服务换桌,通知ipad刷新
        if (ObjectUtil.isNotNull(mergePosCash.getTableId())) {
            wsNoticeDeviceService.sendRefreshMsg(mergePosCash.getTableId(), false, false);
        }
        return b;
    }

    @Override
    @GlobalTransactional
    public Boolean knot(KnotRemarkQuery query) {
        PosCash posCash = cashService.getById(query.getPosCashId());
        checkOrderService.checkKnot(posCash);
        if (CollUtil.isEmpty(query.getTags()) && StrUtil.isBlank(query.getRemarks())) {
            ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "标签备注至少输入一项");
        }
        PriceCalcQuery req = PriceCalcQuery.builder().posCash(posCash).isStop(true).employeeId(ContextUtil.getEmployeeId())
                .isServiceStop(false).build();
        req.setFlowCommonContext(req);
        LiteflowResponse response = flowExecutor.execute2Resp(BizConstant.FLOW.DEFAULT_PRICE_CHAIN, req, PriceCalcContext.class
                , DetailCalcContext.class);
        ArgumentAssert.isFalse(!response.isSuccess(), response.getMessage());
        DetailCalcContext detailCalcContext = response.getContextBean(DetailCalcContext.class);
        PriceCalcContext contextBean = response.getContextBean(PriceCalcContext.class);
        List<PosCashTable> tableList = detailCalcContext.getTableList();
        List<PosCashProduct> productList = detailCalcContext.getProductList();
        List<PosCashService> serviceList = detailCalcContext.getServiceList();
        //台桌
        List<PosCashTable> cashTableList = tableList.stream().filter(v -> {
            if (StrUtil.isBlank(v.getRemarks())) {
                return true;
            }

            if (StrUtil.isNotBlank(v.getRemarks())) {
                ItemRemarksResultVO itemRemarksResultVO = JSON.parseObject(v.getRemarks(), ItemRemarksResultVO.class);
                return ObjectUtil.isNull(itemRemarksResultVO.getKnotTags())
                        && StrUtil.isBlank(itemRemarksResultVO.getKnotRemarks());
            }
            return true;
        }).collect(Collectors.toList());
        for (PosCashTable posCashTable : cashTableList) {
            posCashTable.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                    posCashTable.getRemarks(), EchoDictType.App.KNOT_TAGS));
        }
        //商品
        List<PosCashProduct> cashProductList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(productList)) {
            cashProductList = productList.stream().filter(v -> {
                if (StrUtil.isBlank(v.getRemarks())) {
                    return true;
                }

                if (StrUtil.isNotBlank(v.getRemarks())) {
                    ItemRemarksResultVO itemRemarksResultVO = JSON.parseObject(v.getRemarks(), ItemRemarksResultVO.class);
                    return ObjectUtil.isNull(itemRemarksResultVO.getKnotTags())
                            && StrUtil.isBlank(itemRemarksResultVO.getKnotRemarks());
                }
                return true;
            }).collect(Collectors.toList());
            for (PosCashProduct posCashProduct : cashProductList) {
                posCashProduct.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                        posCashProduct.getRemarks(), EchoDictType.App.KNOT_TAGS));
                posCashProduct.setIsKnot(true);
            }
        }

        //服务
        List<PosCashService> cashServiceList = Lists.newArrayList();
        List<PosCashService> newCashServiceList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(serviceList)) {
            cashServiceList = serviceList.stream().filter(v -> {
                if (StrUtil.isBlank(v.getRemarks())) {
                    return true;
                }

                if (StrUtil.isNotBlank(v.getRemarks())) {
                    ItemRemarksResultVO itemRemarksResultVO = JSON.parseObject(v.getRemarks(), ItemRemarksResultVO.class);
                    return ObjectUtil.isNull(itemRemarksResultVO.getKnotTags())
                            && StrUtil.isBlank(itemRemarksResultVO.getKnotRemarks());
                }
                return true;
            }).collect(Collectors.toList());
            for (PosCashService posCashService : cashServiceList) {
                posCashService.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                        posCashService.getRemarks(), EchoDictType.App.KNOT_TAGS));
                String status = posCashService.getStatus();
                posCashService.setStatus(CashTableStatusEnum.STOP.getCode());
                posCashService.setIsKnot(true);
                calcPriceService.refreshService(posCashService);
                if (!status.equals(CashTableStatusEnum.TIMING.getCode())) {
                    continue;
                }
                PosCashService cashService1 = BeanUtil.copyProperties(posCashService, PosCashService.class);
                cashService1.setStartTime(LocalDateTime.now().withSecond(0).withNano(0));
                cashService1.setEndTime(LocalDateTime.now());
                cashService1.setCreatedTime(LocalDateTime.now());
                cashService1.setUpdatedTime(LocalDateTime.now());
                cashService1.setDuration(0);
                cashService1.setStatus(CashTableStatusEnum.TIMING.getCode());
                cashService1.setRemarks("");
                cashService1.setId(null);
                cashService1.setReformPriceType(ReformPriceTypeEnum.NO.getCode());
                cashService1.setReformPrice(BigDecimal.ZERO);
                cashService1.setDiscount(BigDecimal.ZERO);
                cashService1.setDiscountAmount(BigDecimal.ZERO);
                cashService1.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                newCashServiceList.add(cashService1);
            }
        }
        List<PosCashThail> thailList = detailCalcContext.getThailList();
        List<PosCashThail> cashthailList = thailList.stream().filter(v -> {
            if (StrUtil.isBlank(v.getRemarks())) {
                return true;
            }
            if (StrUtil.isNotBlank(v.getRemarks())) {
                ItemRemarksResultVO itemRemarksResultVO = JSON.parseObject(v.getRemarks(), ItemRemarksResultVO.class);
                return ObjectUtil.isNull(itemRemarksResultVO.getKnotTags())
                        && StrUtil.isBlank(itemRemarksResultVO.getKnotRemarks());
            }
            return true;
        }).collect(Collectors.toList());
        for (PosCashThail posCashThail : cashthailList) {
            posCashThail.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                    posCashThail.getRemarks(), EchoDictType.App.KNOT_TAGS));
        }
        PosCashTable posCashTable = cashTableList.stream().filter(v ->
                        ObjectUtil.isNotNull(v.getId()) && ObjectUtil.isNotNull(v.getTableId())
                                && ObjectUtil.equal(v.getTableId(), posCash.getTableId()))
                .max(Comparator.comparing(PosCashTable::getCreatedTime))
                .orElseGet(() -> posCashTableService.getOne(Wraps.<PosCashTable>lbQ()
                        .eq(PosCashTable::getCashId, posCash.getId())
                        .eq(PosCashTable::getTableId, posCash.getTableId())
                        .orderByDesc(PosCashTable::getEndTime).last(" limit 1"))
                );
        if (CollUtil.isNotEmpty(cashTableList)) {
            posCashTableService.updateBatchById(cashTableList);
        }
        if (CollUtil.isNotEmpty(cashProductList)) {
            posCashProductService.updateBatchById(cashProductList);
        }
        if (CollUtil.isNotEmpty(cashServiceList)) {
            posCashServiceService.updateBatchById(cashServiceList);
        }
        if (CollUtil.isNotEmpty(newCashServiceList)) {
            posCashServiceService.saveBatch(newCashServiceList);
        }
        if (CollUtil.isNotEmpty(cashthailList)) {
            posCashThailService.updateBatchById(cashthailList);
        }
        int sum = tableList.stream().mapToInt(PosCashTable::getDuration).sum();
        QueryWrap<BaseTableChargingSetting> queryWrap = new QueryWrap<>();
        queryWrap.lambda().eq(BaseTableChargingSetting::getId, posCashTable.getChargingSettingId())
                .last("limit 1");
        BaseTableChargingSetting baseTableChargingSetting = baseTableChargingSettingService.getOne(queryWrap);
        BaseTableCharging baseTableCharging = null;
        if (baseTableChargingSetting == null) {
            BaseTableInfo byId = baseTableInfoService.getById(posCashTable.getTableId());
            QueryWrap<BaseTableCharging> wrap = new QueryWrap<>();
            wrap.lambda().eq(BaseTableCharging::getTableType, byId.getTableType())
                    .last("limit 1");
            BaseTableCharging one = baseTableChargingService.getOne(wrap);
            baseTableCharging = BaseTableCharging.builder()
                    .period(one.getPeriod())
                    .build();
        } else {
            baseTableCharging = baseTableChargingService.findByWrap(Wraps.<BaseTableCharging>lbQ()
                    .eq(BaseTableCharging::getId, baseTableChargingSetting.getChargingId()).last("limit 1"));
        }
        if (ObjectUtil.isNull(baseTableCharging)) {
            baseTableCharging = BaseTableCharging.builder()
                    .period(1).skipFee(1).overtime(1)
                    .build();
        }
        Integer calcDuration = calcPriceService.calcDuration(posCashTable, sum, baseTableCharging);
        posCashTable.setId(null);
        posCashTable.setServiceActivityId(null);
        posCashTable.setServiceActivityName(null);
        posCashTable.setServiceActivityDiscountValue(BigDecimal.ZERO);
        posCashTable.setStartTime(LocalDateTime.now().withSecond(0).withNano(0));
        posCashTable.setEndTime(LocalDateTime.now().withSecond(0).withNano(0));
        posCashTable.setCreatedTime(LocalDateTime.now());
        posCashTable.setDuration(0);
        posCashTable.setCashThailId(null);
        posCashTable.setUpdatedTime(LocalDateTime.now().withSecond(0).withNano(0));
        posCashTable.setDiscountAmount(BigDecimal.ZERO);
        posCashTable.setAmount(BigDecimal.ZERO);
        posCashTable.setDiscount(BigDecimal.ZERO);
        posCashTable.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
        posCashTable.setFreeDuration(0);
        posCashTable.setAssessedAmount(BigDecimal.ZERO);
        posCashTable.setDiscountTemplateId(null);
        posCashTable.setIsPackField(false);
        posCashTable.setFreeAmount(BigDecimal.ZERO);
        posCashTable.setIsTurn(false);
        posCashTable.setDeductDuration(0);
        posCashTable.setBillingMode(0);
        posCashTable.setPaid(BigDecimal.ZERO);
        posCashTable.setStatus(CashTableStatusEnum.TIMING.getCode());
        posCashTable.setBillingLevel(BillingLevelEnum.TWO.getCode());
        posCashTable.setIsSplit(false);
        posCashTable.setExperienceDuration(0);
        posCashTable.setCouponId(null);
        posCashTable.setMemberCardId(null);
        posCashTable.setIsCardEquity(false);
        posCashTable.setCalcDuration(0);
        posCashTable.setCalcType(0);
        posCashTable.setIsKnot(true);
        if (calcDuration > 0) {
            posCashTable.setCalcDuration(calcDuration);
            posCashTable.setCalcType(1);
        }
        //判断服务活动
        calcPriceService.checkServiceActivity(posCash, posCashTable);
        posCashTable.setSn(ContextUtil.getSn());
        //新增操作日志
        baseBizLogService.createBizLog(BaseBizLog.builder()
                .tenantId(ContextUtil.getTenantId())
                .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                .description(BizLogModuleEnum.KNOT.getDesc())
                .bizModule(BizLogModuleEnum.KNOT.getCode())
                .type(BizLogTypeEnum.UPDATED.getCode())
                .sourceId(posCash.getId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                        (CollUtil.isNotEmpty(cashTableList) ? cashTableList.get(0).getRemarks() : null)
                        , EchoDictType.App.KNOT_TAGS))
                .build());
        PosCash cash = contextBean.getPosCash();
        cash.setBillState(PosCashBillStateEnum.NO_SETTLED.getCode());
        cashService.updateById(cash);
        BaseTableInfo tableInfo = contextBean.getTableInfo();
        tableInfo.setTableStatus(TableStatus.USING.getCode());
        cashStateManager.handleBooleanEvent(posCash.getId(), PosCashConstant.Event.OPEN_LIGHT, posCash, tableInfo);
        baseTableInfoService.updateById(tableInfo);
        return posCashTableService.save(posCashTable);
    }


    @Override
    public Boolean modifyRemark(ModifyKnotRemarkQuery query) {
        ArgumentAssert.notBlank(query.getType(), "类型不能为空");
        if (CollUtil.isEmpty(query.getTags()) && StrUtil.isBlank(query.getRemarks())) {
            ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "标签备注至少输入一项");
        }
        switch (query.getType()) {
            case "PRODUCT_GIFT":
                PosCashProduct posCashProduct = posCashProductService.getById(query.getId());
                ArgumentAssert.notNull(posCashProduct, "商品明细不存在！");
                posCashProduct.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                        posCashProduct.getRemarks(), EchoDictType.App.PRODUCT_ITEM_GIFT));
                posCashProductService.updateById(posCashProduct);
                //新增操作日志
                baseBizLogService.createBizLog(BaseBizLog.builder()
                        .tenantId(ContextUtil.getTenantId())
                        .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                        .description(BizLogModuleEnum.UPDATE_PRODUCT_GIFT_REMARKS.getDesc())
                        .bizModule(BizLogModuleEnum.UPDATE_PRODUCT_GIFT_REMARKS.getCode())
                        .type(BizLogTypeEnum.UPDATED.getCode())
                        .sourceId(posCashProduct.getCashId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                posCashProduct.getRemarks(), EchoDictType.App.PRODUCT_ITEM_GIFT))
                        .build());
                break;
            default:
                ArgumentAssert.isFalse(true, "不支持此操作");
                break;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean modifyKnotRemark(ModifyKnotRemarkQuery query) {
        boolean lock = false;
        try {
            lock = distributedLock.lock(query.getId() + PosCashConstant.Event.ORDER.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            PosCashTable posCashTable = posCashTableService.getById(query.getId());
            ArgumentAssert.notNull(posCashTable, "台费明细不存在！");
            if (CollUtil.isEmpty(query.getTags()) && StrUtil.isBlank(query.getRemarks())) {
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "标签备注至少输入一项");
            }
            posCashTable.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                    posCashTable.getRemarks(), EchoDictType.App.KNOT_TAGS));
            //新增操作日志
            baseBizLogService.createBizLog(BaseBizLog.builder()
                    .tenantId(ContextUtil.getTenantId())
                    .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                    .description(BizLogModuleEnum.MODIFY_KNOT.getDesc())
                    .bizModule(BizLogModuleEnum.MODIFY_KNOT.getCode())
                    .type(BizLogTypeEnum.UPDATED.getCode())
                    .sourceId(posCashTable.getCashId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                            posCashTable.getRemarks(), EchoDictType.App.MODIFY_KNOT_TAGS))
                    .build());
            return posCashTableService.updateById(posCashTable);
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getId() + PosCashConstant.Event.ORDER.getCode());
            }
        }
    }

    @Override
    @Transactional
    public Boolean modifyChangePriceRemark(ModifyChangePriceQuery query) {
        boolean lock = false;
        try {
            lock = distributedLock.lock(query.getId() + PosCashConstant.Event.ORDER.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            if (CollUtil.isEmpty(query.getTags()) && StrUtil.isBlank(query.getRemarks())) {
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "标签备注至少输入一项");
            }
            switch (EquityTypeEnum.get(query.getType())) {
                //台费
                case TABLE:
                    PosCashTable posCashTable = posCashTableService.getById(query.getId());
                    ArgumentAssert.notNull(posCashTable, "台费明细不存在！");
                    ArgumentAssert.isNull(posCashTable.getCashThailId(), "当前明细属于套餐，无法操作");
                    posCashTable.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashTable.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS));
                    posCashTableService.updateById(posCashTable);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description("台费改价备注变更")
                            .bizModule(BizLogModuleEnum.MODIFY_CHANGE_PRICE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCashTable.getCashId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashTable.getRemarks(), EchoDictType.App.MODIFY_CHANGE_PRICE_TAGS))
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                //商品
                case PRODUCT:
                    PosCashProduct posCashProduct = posCashProductService.getById(query.getId());
                    ArgumentAssert.notNull(posCashProduct, "商品明细不存在！");
                    ArgumentAssert.isNull(posCashProduct.getCashThailId(), "当前明细属于套餐，无法操作");
                    posCashProduct.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashProduct.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS));
                    posCashProductService.updateById(posCashProduct);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashProduct.getProductName() + "改价备注变更")
                            .bizModule(BizLogModuleEnum.MODIFY_CHANGE_PRICE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCashProduct.getCashId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashProduct.getRemarks(), EchoDictType.App.MODIFY_CHANGE_PRICE_TAGS))
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                //服务
                case SERVICE:
                    PosCashService posCashService = posCashServiceService.getById(query.getId());
                    ArgumentAssert.notNull(posCashService, "服务明细不存在！");
                    ArgumentAssert.isNull(posCashService.getCashThailId(), "当前明细属于套餐，无法操作");
                    posCashService.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashService.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS));
                    posCashServiceService.updateById(posCashService);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashService.getEmployeeName() + "改价备注变更")
                            .bizModule(BizLogModuleEnum.CHANGE_PRICE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCashService.getCashId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashService.getRemarks(), EchoDictType.App.MODIFY_CHANGE_PRICE_TAGS))
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                //套餐
                case THAIL:
                    PosCashThail posCashThail = posCashThailService.getById(query.getId());
                    ArgumentAssert.notNull(posCashThail, "套餐不存在！");
                    posCashThail.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashThail.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS));
                    posCashThailService.updateById(posCashThail);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashThail.getThailName() + "改价备注变更")
                            .bizModule(BizLogModuleEnum.MODIFY_CHANGE_PRICE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCashThail.getCashId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashThail.getRemarks(), EchoDictType.App.MODIFY_CHANGE_PRICE_TAGS))
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                //购卡
                case CARD:
                    PosCashCard posCashCard = posCashCardService.getById(query.getId());
                    ArgumentAssert.notNull(posCashCard, "套餐不存在！");
                    posCashCard.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashCard.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS));
                    posCashCardService.updateById(posCashCard);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashCard.getName() + "改价备注变更")
                            .bizModule(BizLogModuleEnum.MODIFY_CHANGE_PRICE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCashCard.getCashId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashCard.getRemarks(), EchoDictType.App.MODIFY_CHANGE_PRICE_TAGS))
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                default:
                    ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "不支持此操作");
                    break;
            }
            return true;
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getId() + PosCashConstant.Event.ORDER.getCode());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changePrice(ChangePriceQuery query) {
        boolean lock = false;
        try {
            lock = distributedLock.lock(query.getId() + PosCashConstant.Event.ORDER.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            PosCash posCash = cashService.getById(query.getPosCashId());
            checkOrderService.checkChangePrice(posCash);
            switch (EquityTypeEnum.get(query.getType())) {
                //台费
                case TABLE:
                    PosCashTable posCashTable = posCashTableService.getById(query.getId());
                    ArgumentAssert.notNull(posCashTable, "台费明细不存在！");
                    ArgumentAssert.isNull(posCashTable.getCashThailId(), "当前明细属于套餐，无法操作");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.TIMING.getCode()), "请先停止计时再操作");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.REFUND.getCode()), "请先取消退单再操作");
                    checkOrderService.checkDiscountType(posCashTable.getDiscountType());
                    posCashTable.setMemberCardId(null);
                    posCashTable.setCouponId(null);
                    posCashTable.setCardDeductAmount(BigDecimal.ZERO);
                    posCashTable.setDeductDuration(0);
                    posCashTable.setDiscountAmount(BigDecimal.ZERO);
                    posCashTable.setDiscount(BigDecimal.ZERO);
                    posCashTable.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                    posCashTable.setReformPrice(query.getReformPrice());
                    posCashTable.setReformPriceType(query.getReformPriceType());
                    posCashTable.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashTable.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS));
                    posCashTableService.updateById(posCashTable);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description("台费 改"
                                    + ReformPriceTypeEnum.get(query.getReformPriceType()).getDesc() + "【"
                                    + (ObjectUtil.equal(query.getReformPriceType(), ReformPriceTypeEnum.SINGLE.getCode())
                                    ? posCashTable.getPrice().toPlainString() : posCashTable.getOrginPrice().toPlainString()) + "元>>>"
                                    + query.getReformPrice().setScale(2, RoundingMode.HALF_UP).toPlainString() + "】")
                            .bizModule(BizLogModuleEnum.CHANGE_PRICE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashTable.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS))
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                //商品
                case PRODUCT:
                    PosCashProduct posCashProduct = posCashProductService.getById(query.getId());
                    ArgumentAssert.notNull(posCashProduct, "商品明细不存在！");
                    ArgumentAssert.isNull(posCashProduct.getCashThailId(), "当前明细属于套餐，无法操作");
                    checkOrderService.checkDiscountType(posCashProduct.getDiscountType());
                    posCashProduct.setMemberCardId(null);
                    posCashProduct.setCouponId(null);
                    posCashProduct.setCardDeductAmount(BigDecimal.ZERO);
                    posCashProduct.setDeductNum(0);
                    posCashProduct.setDiscountAmount(BigDecimal.ZERO);
                    posCashProduct.setDiscount(BigDecimal.ZERO);
                    posCashProduct.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                    posCashProduct.setReformPrice(query.getReformPrice());
                    posCashProduct.setReformPriceType(query.getReformPriceType());
                    posCashProduct.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashProduct.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS));
                    posCashProductService.updateById(posCashProduct);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashProduct.getProductName() + " 改"
                                    + ReformPriceTypeEnum.get(query.getReformPriceType()).getDesc() + "【"
                                    + (ObjectUtil.equal(query.getReformPriceType(), ReformPriceTypeEnum.SINGLE.getCode())
                                    ? posCashProduct.getPrice().toPlainString() : posCashProduct.getOrginPrice().toPlainString()) + "元>>>"
                                    + query.getReformPrice().setScale(2, RoundingMode.HALF_UP).toPlainString() + "】")
                            .bizModule(BizLogModuleEnum.CHANGE_PRICE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashProduct.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS))
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                //服务
                case SERVICE:
                    PosCashService posCashService = posCashServiceService.getById(query.getId());
                    ArgumentAssert.notNull(posCashService, "服务明细不存在！");
                    ArgumentAssert.isNull(posCashService.getCashThailId(), "当前明细属于套餐，无法操作");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashService.getStatus(), CashTableStatusEnum.REFUND.getCode()), "请先取消退单再操作");
                    checkOrderService.checkDiscountType(posCashService.getDiscountType());
                    posCashService.setMemberCardId(null);
                    posCashService.setCouponId(null);
                    posCashService.setCardDeductAmount(BigDecimal.ZERO);
                    posCashService.setDeductDuration(0);
                    posCashService.setDiscountAmount(BigDecimal.ZERO);
                    posCashService.setDiscount(BigDecimal.ZERO);
                    posCashService.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                    posCashService.setReformPrice(query.getReformPrice());
                    posCashService.setReformPriceType(query.getReformPriceType());
                    posCashService.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashService.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS));
                    posCashServiceService.updateById(posCashService);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashService.getEmployeeName() + " 改"
                                    + ReformPriceTypeEnum.get(query.getReformPriceType()).getDesc() + "【"
                                    + (ObjectUtil.equal(query.getReformPriceType(), ReformPriceTypeEnum.SINGLE.getCode())
                                    ? posCashService.getPrice().toPlainString() : posCashService.getOrginPrice().toPlainString()) + "元>>>"
                                    + query.getReformPrice().setScale(2, RoundingMode.HALF_UP).toPlainString() + "】")
                            .bizModule(BizLogModuleEnum.CHANGE_PRICE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashService.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS))
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                //套餐
                case THAIL:
                    PosCashThail posCashThail = posCashThailService.getById(query.getId());
                    ArgumentAssert.notNull(posCashThail, "套餐不存在！");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashThail.getStatus(), CashTableStatusEnum.REFUND.getCode()),
                            "请先取消退单再操作");
                    checkOrderService.checkDiscountType(posCashThail.getDiscountType());
                    posCashThail.setDiscountAmount(BigDecimal.ZERO);
                    posCashThail.setDiscount(BigDecimal.ZERO);
                    posCashThail.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                    posCashThail.setReformPrice(query.getReformPrice());
                    posCashThail.setReformPriceType(query.getReformPriceType());
                    posCashThail.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashThail.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS));
                    posCashThailService.updateById(posCashThail);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashThail.getThailName() + " 改"
                                    + ReformPriceTypeEnum.get(query.getReformPriceType()).getDesc() + "【"
                                    + (ObjectUtil.equal(query.getReformPriceType(), ReformPriceTypeEnum.SINGLE.getCode())
                                    ? posCashThail.getPrice().toPlainString() : posCashThail.getOrginPrice().toPlainString()) + "元>>>"
                                    + query.getReformPrice().setScale(2, RoundingMode.HALF_UP).toPlainString() + "】")
                            .bizModule(BizLogModuleEnum.CHANGE_PRICE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashThail.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS))
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                //购卡
                case CARD:
                    PosCashCard posCashCard = posCashCardService.getById(query.getId());
                    ArgumentAssert.notNull(posCashCard, "套餐不存在！");
                    checkOrderService.checkDiscountType(posCashCard.getDiscountType());
                    posCashCard.setDiscountAmount(BigDecimal.ZERO);
                    posCashCard.setDiscount(BigDecimal.ZERO);
                    posCashCard.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                    posCashCard.setReformPrice(query.getReformPrice());
                    posCashCard.setReformPriceType(query.getReformPriceType());
                    posCashCard.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashCard.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS));
                    posCashCardService.updateById(posCashCard);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashCard.getName() + " 改"
                                    + ReformPriceTypeEnum.get(query.getReformPriceType()).getDesc() + "【"
                                    + (ObjectUtil.equal(query.getReformPriceType(), ReformPriceTypeEnum.SINGLE.getCode())
                                    ? posCashCard.getPrice().toPlainString() : posCashCard.getOrginPrice().toPlainString()) + "元>>>"
                                    + query.getReformPrice().setScale(2, RoundingMode.HALF_UP).toPlainString() + "】")
                            .bizModule(BizLogModuleEnum.CHANGE_PRICE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashCard.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS))
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                //充电
                case POWER:
                    PosCashPower posCashPower = posCashPowerService.getById(query.getId());
                    ArgumentAssert.notNull(posCashPower, "充电记录不存在！");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashPower.getStatus(), CashTableStatusEnum.REFUND.getCode()),
                            "请先取消退单再操作");
                    checkOrderService.checkDiscountType(posCashPower.getDiscountType());
                    posCashPower.setDiscountAmount(BigDecimal.ZERO);
                    posCashPower.setDiscount(BigDecimal.ZERO);
                    posCashPower.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                    posCashPower.setReformPrice(query.getReformPrice());
                    posCashPower.setReformPriceType(query.getReformPriceType());
                    posCashPower.setRemarks(setRemarks(query.getTags(), query.getRemarks(),
                            posCashPower.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS));
                    posCashPowerService.updateById(posCashPower);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashPower.getName() + " 改"
                                    + ReformPriceTypeEnum.get(query.getReformPriceType()).getDesc() + "【"
                                    + (ObjectUtil.equal(query.getReformPriceType(), ReformPriceTypeEnum.SINGLE.getCode())
                                    ? posCashPower.getPrice().toPlainString() : posCashPower.getOrginPrice().toPlainString()) + "元>>>"
                                    + query.getReformPrice().setScale(2, RoundingMode.HALF_UP).toPlainString() + "】")
                            .bizModule(BizLogModuleEnum.CHANGE_PRICE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks(setRemarksDesc(query.getTags(), query.getRemarks(),
                                    posCashPower.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS))
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                default:
                    ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "不支持此操作");
                    break;
            }
            return true;
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getId() + PosCashConstant.Event.ORDER.getCode());
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelChangePrice(CancelChangePriceQuery query) {
        boolean lock = false;
        try {
            lock = distributedLock.lock(query.getId() + PosCashConstant.Event.ORDER.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            PosCash posCash = cashService.getById(query.getPosCashId());
            checkOrderService.checkChangePrice(posCash);
            switch (EquityTypeEnum.get(query.getType())) {
                //台费
                case TABLE:
                    PosCashTable posCashTable = posCashTableService.getById(query.getId());
                    ArgumentAssert.notNull(posCashTable, "台费明细不存在！");
                    ArgumentAssert.isNull(posCashTable.getCashThailId(), "当前明细属于套餐，无法操作");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashTable.getReformPriceType(), ReformPriceTypeEnum.NO.getCode()),
                            "不存在改价，无需操作哦～");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.TIMING.getCode()), "请先停止计时再操作");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashTable.getStatus(), CashTableStatusEnum.REFUND.getCode()), "请先取消退单再操作");
                    BigDecimal reformPrice = posCashTable.getReformPrice();
                    String reformPriceType = posCashTable.getReformPriceType();
                    posCashTable.setReformPrice(BigDecimal.ZERO);
                    posCashTable.setReformPriceType(ReformPriceTypeEnum.NO.getCode());
                    posCashTable.setRemarks(setRemarks(Lists.newArrayList(), "",
                            posCashTable.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS));
                    posCashTable.setCycle(posCashTable.getCycle().replaceAll("（起步价）", "")
                            .replaceAll("（封顶价）", ""));
                    posCashTableService.updateById(posCashTable);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description("台费 取消改"
                                    + ReformPriceTypeEnum.get(reformPriceType).getDesc() + "【"
                                    + reformPrice.setScale(2, RoundingMode.HALF_UP).toPlainString() + "】")
                            .bizModule(BizLogModuleEnum.CHANGE_PRICE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks("")
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                //商品
                case PRODUCT:
                    PosCashProduct posCashProduct = posCashProductService.getById(query.getId());
                    ArgumentAssert.notNull(posCashProduct, "台费明细不存在！");
                    ArgumentAssert.isNull(posCashProduct.getCashThailId(), "当前明细属于套餐，无法操作");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashProduct.getReformPriceType(), ReformPriceTypeEnum.NO.getCode()),
                            "不存在改价，无需操作哦～");
                    reformPrice = posCashProduct.getReformPrice();
                    reformPriceType = posCashProduct.getReformPriceType();
                    posCashProduct.setReformPrice(BigDecimal.ZERO);
                    posCashProduct.setReformPriceType(ReformPriceTypeEnum.NO.getCode());
                    posCashProduct.setRemarks(setRemarks(Lists.newArrayList(), "",
                            posCashProduct.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS));
                    posCashProductService.updateById(posCashProduct);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashProduct.getProductName() + " 取消改"
                                    + ReformPriceTypeEnum.get(reformPriceType).getDesc() + "【"
                                    + reformPrice.setScale(2, RoundingMode.HALF_UP).toPlainString() + "】")
                            .bizModule(BizLogModuleEnum.CHANGE_PRICE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks("")
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                //服务
                case SERVICE:
                    PosCashService posCashService = posCashServiceService.getById(query.getId());
                    ArgumentAssert.notNull(posCashService, "服务明细不存在！");
                    ArgumentAssert.isNull(posCashService.getCashThailId(), "当前明细属于套餐，无法操作");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashService.getReformPriceType(), ReformPriceTypeEnum.NO.getCode()),
                            "不存在改价，无需操作哦～");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashService.getStatus(), CashTableStatusEnum.REFUND.getCode()), "请先取消退单再操作");
                    reformPrice = posCashService.getReformPrice();
                    reformPriceType = posCashService.getReformPriceType();
                    posCashService.setReformPrice(BigDecimal.ZERO);
                    posCashService.setReformPriceType(ReformPriceTypeEnum.NO.getCode());
                    posCashService.setRemarks(setRemarks(Lists.newArrayList(), "",
                            posCashService.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS));
                    posCashServiceService.updateById(posCashService);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashService.getEmployeeName() + " 取消改"
                                    + ReformPriceTypeEnum.get(reformPriceType).getDesc() + "【"
                                    + reformPrice.setScale(2, RoundingMode.HALF_UP).toPlainString() + "】")
                            .bizModule(BizLogModuleEnum.CHANGE_PRICE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks("")
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                //套餐
                case THAIL:
                    PosCashThail posCashThail = posCashThailService.getById(query.getId());
                    ArgumentAssert.notNull(posCashThail, "服务明细不存在！");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashThail.getReformPriceType(), ReformPriceTypeEnum.NO.getCode()),
                            "不存在改价，无需操作哦～");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashThail.getStatus(), CashTableStatusEnum.REFUND.getCode()), "请先取消退单再操作");
                    reformPrice = posCashThail.getReformPrice();
                    reformPriceType = posCashThail.getReformPriceType();
                    posCashThail.setReformPrice(BigDecimal.ZERO);
                    posCashThail.setReformPriceType(ReformPriceTypeEnum.NO.getCode());
                    posCashThail.setRemarks(setRemarks(Lists.newArrayList(), "",
                            posCashThail.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS));
                    posCashThailService.updateById(posCashThail);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashThail.getThailName() + " 取消改"
                                    + ReformPriceTypeEnum.get(reformPriceType).getDesc() + "【"
                                    + reformPrice.setScale(2, RoundingMode.HALF_UP).toPlainString() + "】")
                            .bizModule(BizLogModuleEnum.CHANGE_PRICE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks("")
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                //购卡
                case CARD:
                    PosCashCard posCashCard = posCashCardService.getById(query.getId());
                    ArgumentAssert.notNull(posCashCard, "服务明细不存在！");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashCard.getReformPriceType(), ReformPriceTypeEnum.NO.getCode()),
                            "不存在改价，无需操作哦～");
                    reformPrice = posCashCard.getReformPrice();
                    reformPriceType = posCashCard.getReformPriceType();
                    posCashCard.setReformPrice(BigDecimal.ZERO);
                    posCashCard.setReformPriceType(ReformPriceTypeEnum.NO.getCode());
                    posCashCard.setRemarks(setRemarks(Lists.newArrayList(), "",
                            posCashCard.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS));
                    posCashCardService.updateById(posCashCard);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashCard.getName() + " 取消改"
                                    + ReformPriceTypeEnum.get(reformPriceType).getDesc() + "【"
                                    + reformPrice.setScale(2, RoundingMode.HALF_UP).toPlainString() + "】")
                            .bizModule(BizLogModuleEnum.CHANGE_PRICE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks("")
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                //充电
                case POWER:
                    PosCashPower posCashPower = posCashPowerService.getById(query.getId());
                    ArgumentAssert.notNull(posCashPower, "服务明细不存在！");
                    ArgumentAssert.isNull(posCashPower.getCashThailId(), "当前明细属于套餐，无法操作");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashPower.getReformPriceType(), ReformPriceTypeEnum.NO.getCode()),
                            "不存在改价，无需操作哦～");
                    ArgumentAssert.isFalse(ObjectUtil.equal(posCashPower.getStatus(), CashTableStatusEnum.REFUND.getCode()), "请先取消退单再操作");
                    reformPrice = posCashPower.getReformPrice();
                    reformPriceType = posCashPower.getReformPriceType();
                    posCashPower.setReformPrice(BigDecimal.ZERO);
                    posCashPower.setReformPriceType(ReformPriceTypeEnum.NO.getCode());
                    posCashPower.setRemarks(setRemarks(Lists.newArrayList(), "",
                            posCashPower.getRemarks(), EchoDictType.App.CHANGE_PRICE_TAGS));
                    posCashPowerService.updateById(posCashPower);
                    baseBizLogService.createBizLog(BaseBizLog.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                            .description(posCashPower.getName() + " 取消改"
                                    + ReformPriceTypeEnum.get(reformPriceType).getDesc() + "【"
                                    + reformPrice.setScale(2, RoundingMode.HALF_UP).toPlainString() + "】")
                            .bizModule(BizLogModuleEnum.CHANGE_PRICE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                            .sourceId(posCash.getId()).remarks("")
                            .businessAuthId(query.getBusinessAuthId())
                            .build());
                    break;
                default:
                    ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "不支持此操作");
                    break;
            }
            return true;
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getId() + PosCashConstant.Event.ORDER.getCode());
            }
        }
    }

    @Override
    public String setRemarks(List<String> tags, String remarks, String remark, String key) {
        ItemRemarksResultVO itemRemarksResultVO = new ItemRemarksResultVO();
        //获取原始备注
        if (StrUtil.isNotBlank(remark)) {
            itemRemarksResultVO = JSON.parseObject(remark, ItemRemarksResultVO.class);
        }
        String itemTags = tags == null ? "" : String.join(",", tags);
        //保存新的备注
        if (ObjectUtil.equal(key, EchoDictType.App.ITEM_TAGS)) {
            itemRemarksResultVO.setItemTags(itemTags);
            itemRemarksResultVO.setRemarks(remarks);
        } else if (ObjectUtil.equal(key, EchoDictType.App.ITEM_DISCOUNT_TAGS)) {
            itemRemarksResultVO.setDiscountTags(itemTags);
            itemRemarksResultVO.setDiscountRemarks(remarks);
        } else if (ObjectUtil.equal(key, EchoDictType.App.ITEM_RETREAT)) {
            itemRemarksResultVO.setRetreatTags(itemTags);
            itemRemarksResultVO.setRetreatRemarks(remarks);
        } else if (ObjectUtil.equal(key, EchoDictType.App.PRODUCT_ITEM_RETREAT)) {
            itemRemarksResultVO.setProductReturnTags(itemTags);
            itemRemarksResultVO.setProductReturnRemarks(remarks);
        } else if (ObjectUtil.equal(key, EchoDictType.App.KNOT_TAGS)) {
            itemRemarksResultVO.setKnotTags(itemTags);
            itemRemarksResultVO.setKnotRemarks(remarks);
        } else if (ObjectUtil.equal(key, EchoDictType.App.CHANGE_PRICE_TAGS)) {
            itemRemarksResultVO.setChangePriceTags(itemTags);
            itemRemarksResultVO.setChangePriceRemarks(remarks);
        } else if (ObjectUtil.equal(key, EchoDictType.App.PRODUCT_ITEM_GIFT)) {
            itemRemarksResultVO.setProductGiftTags(itemTags);
            itemRemarksResultVO.setProductGiftRemarks(remarks);
        }
        return JSONUtil.toJsonStr(itemRemarksResultVO);
    }

    @Override
    public String setRemarksDesc(List<String> tags, String remarks, String remark, String key) {
        ItemRemarksResultVO itemRemarksResultVO = new ItemRemarksResultVO();
        //获取原始备注
        if (StrUtil.isNotBlank(remark)) {
            itemRemarksResultVO = JSON.parseObject(remark, ItemRemarksResultVO.class);
        }
        String itemTags = tags == null ? "" : String.join(",", tags);
        //保存新的备注
        if (ObjectUtil.equal(key, EchoDictType.App.ITEM_TAGS)) {
            itemRemarksResultVO.setItemTags(itemTags);
            itemRemarksResultVO.setRemarks(remarks);
        } else if (ObjectUtil.equal(key, EchoDictType.App.ITEM_DISCOUNT_TAGS)) {
            itemRemarksResultVO.setDiscountTags(itemTags);
            itemRemarksResultVO.setDiscountRemarks(remarks);
        } else if (ObjectUtil.equal(key, EchoDictType.App.ITEM_RETREAT)) {
            itemRemarksResultVO.setRetreatTags(itemTags);
            itemRemarksResultVO.setRetreatRemarks(remarks);
        } else if (ObjectUtil.equal(key, EchoDictType.App.PRODUCT_ITEM_RETREAT)) {
            itemRemarksResultVO.setProductReturnTags(itemTags);
            itemRemarksResultVO.setProductReturnRemarks(remarks);
        } else if (ObjectUtil.equal(key, EchoDictType.App.KNOT_TAGS)) {
            itemRemarksResultVO.setKnotTags(itemTags);
            itemRemarksResultVO.setKnotRemarks(remarks);
        } else if (ObjectUtil.equal(key, EchoDictType.App.CHANGE_PRICE_TAGS)) {
            itemRemarksResultVO.setChangePriceTags(itemTags);
            itemRemarksResultVO.setChangePriceRemarks(remarks);
        } else if (ObjectUtil.equal(key, EchoDictType.App.PRODUCT_ITEM_GIFT)) {
            itemRemarksResultVO.setProductGiftTags(itemTags);
            itemRemarksResultVO.setProductGiftRemarks(remarks);
        }
        echoService.action(itemRemarksResultVO);
        String tagDesc = null;
        if (ObjectUtil.equal(key, EchoDictType.App.ITEM_TAGS)) {
            if (itemRemarksResultVO.getEchoMap().get("itemTags") != null) {
                tagDesc = itemRemarksResultVO.getEchoMap().get("itemTags").toString();
            }
        } else if (ObjectUtil.equal(key, EchoDictType.App.ITEM_DISCOUNT_TAGS)) {
            if (itemRemarksResultVO.getEchoMap().get("discountTags") != null) {
                tagDesc = itemRemarksResultVO.getEchoMap().get("discountTags").toString();
            }
        } else if (ObjectUtil.equal(key, EchoDictType.App.ITEM_RETREAT)) {
            if (itemRemarksResultVO.getEchoMap().get("retreatTags") != null) {
                tagDesc = itemRemarksResultVO.getEchoMap().get("retreatTags").toString();
            }
        } else if (ObjectUtil.equal(key, EchoDictType.App.PRODUCT_ITEM_RETREAT)) {
            if (itemRemarksResultVO.getEchoMap().get("productReturnTags") != null) {
                tagDesc = itemRemarksResultVO.getEchoMap().get("productReturnTags").toString();
            }
        } else if (ObjectUtil.equal(key, EchoDictType.App.KNOT_TAGS)) {
            if (itemRemarksResultVO.getEchoMap().get("knotTags") != null) {
                tagDesc = itemRemarksResultVO.getEchoMap().get("knotTags").toString();
            }
        } else if (ObjectUtil.equal(key, EchoDictType.App.CHANGE_PRICE_TAGS)) {
            if (itemRemarksResultVO.getEchoMap().get("changePriceTags") != null) {
                tagDesc = itemRemarksResultVO.getEchoMap().get("changePriceTags").toString();
            }
        } else if (ObjectUtil.equal(key, EchoDictType.App.PRODUCT_ITEM_GIFT)) {
            if (itemRemarksResultVO.getEchoMap().get("productGiftTags") != null) {
                tagDesc = itemRemarksResultVO.getEchoMap().get("productGiftTags").toString();
            }
        }
        if (StrUtil.isBlank(remarks)) {
            return tagDesc;
        }
        if (StrUtil.isBlank(tagDesc)) {
            return remarks;
        }
        return tagDesc.concat(",").concat(remarks);
    }
}

