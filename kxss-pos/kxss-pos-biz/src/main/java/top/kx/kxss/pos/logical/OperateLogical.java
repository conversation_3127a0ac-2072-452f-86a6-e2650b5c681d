package top.kx.kxss.pos.logical;

import top.kx.kxss.app.entity.cash.OpeningTableSaveVO;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.pos.query.QueryDetailQuery;
import top.kx.kxss.pos.query.member.BindMemberQuery;
import top.kx.kxss.pos.query.order.DelOrEmptyOrderQuery;
import top.kx.kxss.pos.query.order.ShopNumQuery;
import top.kx.kxss.pos.query.order.temp.TempOpenTableQuery;
import top.kx.kxss.pos.query.product.*;
import top.kx.kxss.pos.vo.CashDetailResultVO;
import top.kx.kxss.pos.vo.payment.PrepayWithRequestPaymentVO;

/**
 * 逻辑处理
 */
public interface OperateLogical {

    /**
     * 开台
     */
    PosCash openTable(OpeningTableSaveVO saveVO);

    /**
     * 开台明细
     *
     * @param query
     * @return
     */
    CashDetailResultVO queryDetail(QueryDetailQuery query);

    /**
     * 商品变换数量
     *
     * @param query
     * @return
     */
    Long productChangeNum(ChangeNumQuery query);

    /**
     * 商品变换数量
     *
     * @param query
     * @return
     */
    Long serviceChangeNum(ChangeNumQuery query);

    /**
     * 套餐变换数量
     *
     * @param query
     * @return
     */
    Integer thailChangeNum(ThailChangeNumQuery query);


    /**
     * 绑定会员
     *
     * @param query
     * @return
     */
    Boolean bindMember(BindMemberQuery query);

    /**
     * 移除会员
     *
     * @param query
     * @return
     */
    Boolean delMember(QueryDetailQuery query);

    /**
     * 输入购物号
     *
     * @param query
     * @return
     */
    Boolean shopNum(ShopNumQuery query);


    /**
     * 清空购物车
     *
     * @param query
     * @return
     */
    Boolean emptyCart(DelOrEmptyOrderQuery query);

    /**
     * 删除整单
     *
     * @param query
     * @return
     */
    Boolean delOrder(DelOrEmptyOrderQuery query);

    Long barCodeAddProduct(BarCodeAddProductQuery query);

    Long buyCardChangeNum(BuyCardChangeQuery query);

    Long productBatchChangeNum(BatchChangeNumQuery query);

    PrepayWithRequestPaymentVO tempOpenTable(TempOpenTableQuery query);
}
