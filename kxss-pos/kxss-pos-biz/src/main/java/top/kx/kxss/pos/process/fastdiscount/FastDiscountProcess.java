package top.kx.kxss.pos.process.fastdiscount;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.base.entity.discount.BaseDiscountTemplate;
import top.kx.kxss.base.service.discount.BaseDiscountTemplateService;
import top.kx.kxss.model.enumeration.pos.DiscountTypeEnum;
import top.kx.kxss.model.enumeration.pos.ExtTypeEnum;
import top.kx.kxss.pos.bean.PriceCalcStepVO;
import top.kx.kxss.pos.slot.DetailCalcContext;
import top.kx.kxss.pos.slot.PriceCalcContext;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 快捷折扣计算组件
 *
 * <AUTHOR>
 */
@Component("fastDiscountProcess")
@Slf4j
public class FastDiscountProcess extends NodeComponent {

    @Autowired
    private BaseDiscountTemplateService discountTemplateService;

    @Override
    public void process() throws Exception {
        PriceCalcContext context = this.getContextBean(PriceCalcContext.class);
        DetailCalcContext detailContext = this.getContextBean(DetailCalcContext.class);
        context.setContextUtil(context);
        BigDecimal discountAmount = BigDecimal.ZERO;
        BigDecimal disocunt = BigDecimal.ZERO;
        Long discountTemplateId = null;
        //台费折扣
        if (CollUtil.isNotEmpty(detailContext.getTableList())) {
            for (PosCashTable posCashTable : detailContext.getTableList()) {
                //是否叠加会员优惠
                if (discountTemplateService.checkFastDiscount(posCashTable.getDiscountTemplateId()
                        , posCashTable.getFastDiscountType())) {
                    boolean isFastDiscountSuperpose = posCashTable.getIsFastDiscountSuperpose() != null && posCashTable.getIsFastDiscountSuperpose();
                    discountTemplateId = posCashTable.getDiscountTemplateId();
                    posCashTable.setFastDiscountCalcAmount(ObjectUtil.defaultIfNull(posCashTable.getFastDiscountCalcAmount(), BigDecimal.ZERO));
                    posCashTable.setFastDiscountAssessedAmount(ObjectUtil.defaultIfNull(posCashTable.getFastDiscountAssessedAmount(), BigDecimal.ZERO));
                    if (isFastDiscountSuperpose && ObjectUtil.equal(posCashTable.getDiscountType(),
                            DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
//                        posCashTable.setAssessedAmount(posCashTable.getAssessedAmount().add(posCashTable.getFastDiscountAssessedAmount()));
//                        discountAmount = discountAmount.add(posCashTable.getFastDiscountAssessedAmount());
                        posCashTable.setDiscountTemplateId(0L);
                        continue;
                    }
                    //台桌折扣
                    BigDecimal tableDiscount = posCashTable.getDiscount()
                            .divide(BigDecimal.TEN).setScale(2, RoundingMode.HALF_UP);
                    //支付价
                    posCashTable.setFreeAmount(posCashTable.getFreeAmount() == null ? BigDecimal.ZERO : posCashTable.getFreeAmount());
                    BigDecimal bigDecimal = posCashTable.getFastDiscountCalcAmount().multiply(tableDiscount)
                            .setScale(2, RoundingMode.HALF_UP);
                    BigDecimal discountPrice = posCashTable.getFastDiscountCalcAmount().subtract(bigDecimal);
                    posCashTable.setAmount(posCashTable.getOrginPrice().subtract(discountPrice)
                            .setScale(2, RoundingMode.HALF_UP));
                    //优惠金额
                    posCashTable.setDiscountAmount(posCashTable.getOrginPrice().subtract(posCashTable.getAmount())
                            .setScale(2, RoundingMode.HALF_UP));
                    posCashTable.setDiscountType(DiscountTypeEnum.FAST_DISCOUNT_DISCOUNT.getCode());
                    posCashTable.setDiscountDesc(posCashTable.getDiscount().stripTrailingZeros().toPlainString() + "折");
                    discountTemplateId = posCashTable.getDiscountTemplateId();
                    disocunt = tableDiscount;
                    discountAmount = discountAmount.add(discountPrice);
                }
            }
        }

        //商品折扣
        if (CollUtil.isNotEmpty(detailContext.getProductList())) {
            for (PosCashProduct cashDetail : detailContext.getProductList()) {
                //是否叠加会员优惠
                if (discountTemplateService.checkFastDiscount(cashDetail.getDiscountTemplateId()
                        , cashDetail.getFastDiscountType())) {
                    boolean isFastDiscountSuperpose = cashDetail.getIsFastDiscountSuperpose() != null && cashDetail.getIsFastDiscountSuperpose();
                    discountTemplateId = cashDetail.getDiscountTemplateId();
                    cashDetail.setFastDiscountCalcAmount(ObjectUtil.defaultIfNull(cashDetail.getFastDiscountCalcAmount(), BigDecimal.ZERO));
                    cashDetail.setFastDiscountAssessedAmount(ObjectUtil.defaultIfNull(cashDetail.getFastDiscountAssessedAmount(), BigDecimal.ZERO));
                    if (isFastDiscountSuperpose && ObjectUtil.equal(cashDetail.getDiscountType(),
                            DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
//                        cashDetail.setAssessedAmount(cashDetail.getAssessedAmount().add(cashDetail.getFastDiscountAssessedAmount()));
//                        discountAmount = discountAmount.add(cashDetail.getFastDiscountAssessedAmount());
                        cashDetail.setDiscountTemplateId(0L);
                        continue;
                    }
                    //台桌折扣
                    BigDecimal tableDiscount = cashDetail.getDiscount()
                            .divide(BigDecimal.TEN).setScale(2, RoundingMode.HALF_UP);
                    //支付价
                    BigDecimal bigDecimal = cashDetail.getFastDiscountCalcAmount().multiply(tableDiscount)
                            .setScale(2, RoundingMode.HALF_UP);
                    BigDecimal discountPrice = cashDetail.getFastDiscountCalcAmount().subtract(bigDecimal);
                    cashDetail.setAmount(cashDetail.getOrginPrice().subtract(discountPrice)
                            .setScale(2, RoundingMode.HALF_UP));
                    //优惠金额
                    cashDetail.setDiscountAmount(cashDetail.getOrginPrice().subtract(cashDetail.getAmount())
                            .setScale(2, RoundingMode.HALF_UP));
                    cashDetail.setDiscountType(DiscountTypeEnum.FAST_DISCOUNT_DISCOUNT.getCode());
                    cashDetail.setDiscountDesc(cashDetail.getDiscount().stripTrailingZeros().toPlainString() + "折");
                    discountTemplateId = cashDetail.getDiscountTemplateId();
                    disocunt = tableDiscount;
                    discountAmount = discountAmount.add(discountPrice);
                }
            }
        }
        //服务折扣
        if (CollUtil.isNotEmpty(detailContext.getServiceList())) {
            for (PosCashService cashDetail : detailContext.getServiceList()) {
                //是否叠加会员优惠
                if (discountTemplateService.checkFastDiscount(cashDetail.getDiscountTemplateId()
                        , cashDetail.getFastDiscountType())) {
                    boolean isFastDiscountSuperpose = cashDetail.getIsFastDiscountSuperpose() != null && cashDetail.getIsFastDiscountSuperpose();
                    discountTemplateId = cashDetail.getDiscountTemplateId();
                    cashDetail.setFastDiscountCalcAmount(ObjectUtil.defaultIfNull(cashDetail.getFastDiscountCalcAmount(), BigDecimal.ZERO));
                    cashDetail.setFastDiscountAssessedAmount(ObjectUtil.defaultIfNull(cashDetail.getFastDiscountAssessedAmount(), BigDecimal.ZERO));
                    if (isFastDiscountSuperpose && ObjectUtil.equal(cashDetail.getDiscountType(),
                            DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
//                        cashDetail.setAssessedAmount(cashDetail.getAssessedAmount().add(cashDetail.getFastDiscountAssessedAmount()));
//                        discountAmount = discountAmount.add(cashDetail.getFastDiscountAssessedAmount());
                        cashDetail.setDiscountTemplateId(0L);
                        continue;
                    }
                    //台桌折扣
                    BigDecimal tableDiscount = cashDetail.getDiscount()
                            .divide(BigDecimal.TEN).setScale(2, RoundingMode.HALF_UP);
                    //支付价
                    BigDecimal bigDecimal = cashDetail.getFastDiscountCalcAmount().multiply(tableDiscount)
                            .setScale(2, RoundingMode.HALF_UP);
                    BigDecimal discountPrice = cashDetail.getFastDiscountCalcAmount().subtract(bigDecimal);
                    cashDetail.setAmount(cashDetail.getOrginPrice().subtract(discountPrice)
                            .setScale(2, RoundingMode.HALF_UP));
                    //优惠金额
                    cashDetail.setDiscountAmount(cashDetail.getOrginPrice().subtract(cashDetail.getAmount())
                            .setScale(2, RoundingMode.HALF_UP));
                    cashDetail.setDiscountType(DiscountTypeEnum.FAST_DISCOUNT_DISCOUNT.getCode());
                    cashDetail.setDiscountDesc(cashDetail.getDiscount().stripTrailingZeros().toPlainString() + "折");
                    discountTemplateId = cashDetail.getDiscountTemplateId();
                    disocunt = tableDiscount;
                    discountAmount = discountAmount.add(discountPrice);
                }
            }
        }

        //加入到价格步骤中
        if (discountAmount.compareTo(BigDecimal.ZERO) != 0 && discountTemplateId != null) {
            ArgumentAssert.notNull(discountTemplateId, "快捷优惠异常");
            BaseDiscountTemplate byId = discountTemplateService.findById(discountTemplateId);
            if (ObjectUtil.isNull(byId)) {
                byId = discountTemplateService.getById(discountTemplateId);
            }
            context.getCashDetailResultVO().setDiscountTemplateId(discountTemplateId);
            BigDecimal prePrice = context.getLastestPriceStep().getCurrPrice();
            BigDecimal currPrice = prePrice.subtract(discountAmount);
            context.addPriceCalcStep(PriceCalcStepVO.builder()
                    .extId(discountTemplateId).currPrice(currPrice)
                    .extType(ExtTypeEnum.FAST_DISCOUNT.getCode())
                    .isGiftPay(byId.getIsGiftPay())
                    .prePrice(prePrice).priceType(DiscountTypeEnum.FAST_DISCOUNT_DISCOUNT)
                    .priceChange(currPrice.subtract(prePrice))
                    .stepDesc(ObjectUtil.isNotNull(byId) ? byId.getName()
                            : DiscountTypeEnum.FAST_DISCOUNT_DISCOUNT.getDesc() + "【" + disocunt.multiply(BigDecimal.TEN).stripTrailingZeros().toPlainString() + "    折】")
                    .build());
        }
    }

}
