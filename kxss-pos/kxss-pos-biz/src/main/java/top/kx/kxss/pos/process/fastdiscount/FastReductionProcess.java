package top.kx.kxss.pos.process.fastdiscount;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.base.entity.discount.BaseDiscountTemplate;
import top.kx.kxss.base.service.discount.BaseDiscountTemplateService;
import top.kx.kxss.model.enumeration.pos.DiscountTypeEnum;
import top.kx.kxss.model.enumeration.pos.ExtTypeEnum;
import top.kx.kxss.pos.bean.PriceCalcStepVO;
import top.kx.kxss.pos.slot.DetailCalcContext;
import top.kx.kxss.pos.slot.PriceCalcContext;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 快捷减免计算组件
 *
 * <AUTHOR>
 */
@Component("fastReductionProcess")
@Slf4j
public class FastReductionProcess extends NodeComponent {

    @Autowired
    private BaseDiscountTemplateService discountTemplateService;

    @Override
    public void process() throws Exception {
        PriceCalcContext context = this.getContextBean(PriceCalcContext.class);
        DetailCalcContext detailContext = this.getContextBean(DetailCalcContext.class);
        context.setContextUtil(context);
        BigDecimal discountAmount = BigDecimal.ZERO;
        BigDecimal disocunt = BigDecimal.ZERO;
        Long discountTemplateId = null;
        //台费减免
        if (CollUtil.isNotEmpty(detailContext.getTableList())) {
            for (PosCashTable cashDetail : detailContext.getTableList()) {
                if (discountTemplateService.checkFastReduction(cashDetail.getDiscountTemplateId()
                        , cashDetail.getFastDiscountType())) {
                    boolean isFastDiscountSuperpose = cashDetail.getIsFastDiscountSuperpose() != null && cashDetail.getIsFastDiscountSuperpose();
                    discountTemplateId = cashDetail.getDiscountTemplateId();
                    //台桌减免
                    BigDecimal tableDiscount = cashDetail.getFastDiscountCalcAmount();
                    if (isFastDiscountSuperpose && ObjectUtil.equal(cashDetail.getDiscountType(),
                            DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
//                        cashDetail.setAssessedAmount(cashDetail.getAssessedAmount().add(cashDetail.getFastDiscountCalcAmount()));
//                        discountAmount = discountAmount.add(cashDetail.getFastDiscountCalcAmount());
                        cashDetail.setDiscountTemplateId(0L);
                        continue;
                    }
                    //支付价
                    cashDetail.setFreeAmount(cashDetail.getFreeAmount() == null ? BigDecimal.ZERO : cashDetail.getFreeAmount());
                    cashDetail.setAmount(cashDetail.getOrginPrice().subtract(cashDetail.getFreeAmount()).subtract(tableDiscount)
                            .setScale(2, RoundingMode.HALF_UP));
                    //优惠金额
                    cashDetail.setDiscountAmount(cashDetail.getOrginPrice().subtract(cashDetail.getAmount())
                            .setScale(2, RoundingMode.HALF_UP));
                    cashDetail.setDiscountDesc("-" + tableDiscount.toPlainString() + "元");
                    cashDetail.setDiscountType(DiscountTypeEnum.FAST_DISCOUNT_REDUCTION.getCode());
                    discountAmount = discountAmount.add(cashDetail.getDiscountAmount())
                            .setScale(2, RoundingMode.HALF_UP);
                    discountTemplateId = cashDetail.getDiscountTemplateId();
                    disocunt = tableDiscount;
                }
            }
        }

        //商品减免
        if (CollUtil.isNotEmpty(detailContext.getProductList())) {
            for (PosCashProduct cashDetail : detailContext.getProductList()) {
                if (discountTemplateService.checkFastReduction(cashDetail.getDiscountTemplateId()
                        , cashDetail.getFastDiscountType())) {
                    boolean isFastDiscountSuperpose = cashDetail.getIsFastDiscountSuperpose() != null && cashDetail.getIsFastDiscountSuperpose();
                    discountTemplateId = cashDetail.getDiscountTemplateId();
                    //台桌减免
                    BigDecimal tableDiscount = cashDetail.getFastDiscountCalcAmount();
                    if (isFastDiscountSuperpose && ObjectUtil.equal(cashDetail.getDiscountType(),
                            DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
//                        cashDetail.setAssessedAmount(cashDetail.getAssessedAmount().add(cashDetail.getFastDiscountCalcAmount()));
//                        discountAmount = discountAmount.add(cashDetail.getFastDiscountCalcAmount());
                        cashDetail.setDiscountTemplateId(0L);
                        continue;
                    }
                    //支付价
                    cashDetail.setAmount(cashDetail.getOrginPrice().subtract(tableDiscount)
                            .setScale(2, RoundingMode.HALF_UP));
                    //优惠金额
                    cashDetail.setDiscountAmount(cashDetail.getOrginPrice().subtract(cashDetail.getAmount())
                            .setScale(2, RoundingMode.HALF_UP));
                    cashDetail.setDiscountDesc("-" + tableDiscount.toPlainString() + "元");
                    cashDetail.setDiscountType(DiscountTypeEnum.FAST_DISCOUNT_REDUCTION.getCode());
                    discountAmount = discountAmount.add(cashDetail.getDiscountAmount())
                            .setScale(2, RoundingMode.HALF_UP);
                    discountTemplateId = cashDetail.getDiscountTemplateId();
                    disocunt = tableDiscount;
                }
            }
        }
        //服务减免
        if (CollUtil.isNotEmpty(detailContext.getServiceList())) {
            for (PosCashService cashDetail : detailContext.getServiceList()) {
                if (discountTemplateService.checkFastReduction(cashDetail.getDiscountTemplateId()
                        , cashDetail.getFastDiscountType())) {
                    boolean isFastDiscountSuperpose = cashDetail.getIsFastDiscountSuperpose() != null && cashDetail.getIsFastDiscountSuperpose();
                    discountTemplateId = cashDetail.getDiscountTemplateId();
                    //台桌减免
                    BigDecimal tableDiscount = cashDetail.getFastDiscountCalcAmount();
                    if (isFastDiscountSuperpose && ObjectUtil.equal(cashDetail.getDiscountType(),
                            DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
//                        cashDetail.setAssessedAmount(cashDetail.getAssessedAmount().add(cashDetail.getFastDiscountCalcAmount()));
//                        discountAmount = discountAmount.add(cashDetail.getFastDiscountCalcAmount());
                        cashDetail.setDiscountTemplateId(0L);
                        continue;
                    }
                    //支付价
                    cashDetail.setAmount(cashDetail.getOrginPrice().subtract(tableDiscount)
                            .setScale(2, RoundingMode.HALF_UP));
                    //优惠金额
                    cashDetail.setDiscountAmount(cashDetail.getOrginPrice().subtract(cashDetail.getAmount())
                            .setScale(2, RoundingMode.HALF_UP));
                    cashDetail.setDiscountDesc("-" + tableDiscount.toPlainString() + "元");
                    cashDetail.setDiscountType(DiscountTypeEnum.FAST_DISCOUNT_REDUCTION.getCode());
                    discountAmount = discountAmount.add(cashDetail.getDiscountAmount())
                            .setScale(2, RoundingMode.HALF_UP);
                    discountTemplateId = cashDetail.getDiscountTemplateId();
                    disocunt = tableDiscount;
                }
            }
        }

        //加入到价格步骤中
        if (discountAmount.compareTo(BigDecimal.ZERO) != 0) {
            ArgumentAssert.notNull(discountTemplateId, "快捷优惠异常");
            BaseDiscountTemplate byId = discountTemplateService.findById(discountTemplateId);
            if (ObjectUtil.isNull(byId)) {
                byId = discountTemplateService.getById(discountTemplateId);
            }
            context.getCashDetailResultVO().setDiscountTemplateId(discountTemplateId);
            BigDecimal prePrice = context.getLastestPriceStep().getCurrPrice();
            BigDecimal currPrice = prePrice.subtract(discountAmount);
            context.addPriceCalcStep(PriceCalcStepVO.builder()
                    .extId(discountTemplateId).currPrice(currPrice)
                    .isGiftPay(byId.getIsGiftPay())
                    .extType(ExtTypeEnum.FAST_DISCOUNT.getCode())
                    .prePrice(prePrice).priceType(DiscountTypeEnum.FAST_DISCOUNT_REDUCTION)
                    .priceChange(currPrice.subtract(prePrice))
                    .stepDesc(ObjectUtil.isNotNull(byId) ? byId.getName()
                            : DiscountTypeEnum.FAST_DISCOUNT_REDUCTION.getDesc() + "【减免" + disocunt.setScale(2, RoundingMode.HALF_UP).toPlainString() + "元】")
                    .build());
        }
    }

}
