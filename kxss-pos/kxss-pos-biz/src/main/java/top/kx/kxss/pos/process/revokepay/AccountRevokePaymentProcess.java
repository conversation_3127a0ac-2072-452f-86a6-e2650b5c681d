package top.kx.kxss.pos.process.revokepay;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.entity.cash.refund.PosCashRefundPayment;
import top.kx.kxss.base.entity.member.MemberBalanceChange;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.manager.member.MemberInfoManager;
import top.kx.kxss.base.service.member.grade.MemberConsumeLimitService;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.model.enumeration.base.PaymentBizTypeEnum;
import top.kx.kxss.model.enumeration.base.PaymentTypeEnum;
import top.kx.kxss.pos.slot.TrendsPaymentContext;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;

/**
 * 撤销账户支付
 *
 * <AUTHOR>
 */
@Component("accountRevokePaymentProcess")
@DS(DsConstant.BASE_TENANT)
@Slf4j
public class AccountRevokePaymentProcess extends NodeComponent {

    @Autowired
    private MemberInfoManager memberInfoManager;

    @Override
    public void process() throws Exception {
        TrendsPaymentContext context = this.getContextBean(TrendsPaymentContext.class);
        PosCashPayment posCashPayment = context.getPosCashPayment();
        PosCash posCash = context.getPosCash();
        MemberInfo memberInfo = memberInfoManager.getById(posCash.getMemberId());
        MemberBalanceChange build = MemberBalanceChange.builder()
                .type(PaymentTypeEnum.REFUND.getCode())
                .remarks("撤销收款")
                .lastBalance(memberInfo.getRechargeAmount().add(memberInfo.getGiftAmount()))
                .createdOrgId(ContextUtil.getCurrentCompanyId())
                .employeeId(posCash.getCreatedEmp())
                .giftChangeType(1).rechargeChangeType(1)
                .sourceId(posCash.getId()).paymentId(posCashPayment.getId())
                .lastGiftAmount(memberInfo.getGiftAmount().setScale(2, RoundingMode.HALF_UP))
                .lastRechargeAmount(memberInfo.getRechargeAmount().setScale(2, RoundingMode.HALF_UP))
                .memberId(memberInfo.getId())
                .build();
        memberInfo.setRechargeAmount(memberInfo.getRechargeAmount().add(posCashPayment.getRechargeAmount()));
        memberInfo.setGiftAmount(memberInfo.getGiftAmount().add(posCashPayment.getGiftAmount()));
        context.setMemberInfo(memberInfo);
        //支付金额
        BigDecimal amount = posCashPayment.getAmount();
        //已付款金额
        BigDecimal paid = posCash.getPaid() == null ? BigDecimal.ZERO : posCash.getPaid();
        //未付款金额
        BigDecimal unpaid = posCash.getUnpaid() == null ? BigDecimal.ZERO : posCash.getUnpaid();
        posCashPayment.setRefundGiftAmount(posCashPayment.getGiftAmount());
        posCashPayment.setRefundAmount(posCashPayment.getAmount());
        posCash.setPaid(paid.subtract(amount));
        posCash.setUnpaid(unpaid.add(amount));
        build.setGiftChangeAmount(posCashPayment.getGiftAmount().setScale(2, RoundingMode.HALF_UP));
        build.setRechargeChangeAmount(posCashPayment.getRechargeAmount().setScale(2, RoundingMode.HALF_UP));
        build.setRechargeAmount(memberInfo.getRechargeAmount());
        build.setGiftAmount(memberInfo.getGiftAmount());
        build.setAccountBalance(memberInfo.getRechargeAmount().add(memberInfo.getGiftAmount()));
        build.setExplain("+" + build.getRechargeChangeAmount().toPlainString() + "(本金)" +
                " +" + build.getGiftChangeAmount().toPlainString() + "(赠金)");
        context.setBalanceChange(build);
        PosCashRefundPayment refundPayment = BeanUtil.copyProperties(posCashPayment, PosCashRefundPayment.class);
        refundPayment.setRechargeAmount(posCashPayment.getRechargeAmount());
        refundPayment.setGiftAmount(posCashPayment.getGiftAmount());
        refundPayment.setAmount(posCashPayment.getAmount());
        refundPayment.setId(null);
        refundPayment.setCreatedTime(LocalDateTime.now());
        refundPayment.setUpdatedTime(LocalDateTime.now());
        refundPayment.setCashId(posCash.getId());
        refundPayment.setPayTime(LocalDateTime.now());
        refundPayment.setStatus(PosCashPaymentStatusEnum.PAY_SUCCESS.getCode());
        refundPayment.setCashPaymentId(posCashPayment.getId());
        context.setRefundPayment(refundPayment);
    }

    @Override
    public boolean isAccess() {
        TrendsPaymentContext context = this.getContextBean(TrendsPaymentContext.class);
        if (ObjectUtil.isNotNull(context.getPayType())
                && ObjectUtil.equal(context.getPayType().getBizType(), PaymentBizTypeEnum.ACCOUNT.getCode())) {
            //这一步必须有，否则线程池中无法确定数据源
            context.setContextUtil(context);
            return true;
        } else {
            return false;
        }
    }
}
