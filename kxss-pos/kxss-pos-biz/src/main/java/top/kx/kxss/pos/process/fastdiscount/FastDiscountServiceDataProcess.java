package top.kx.kxss.pos.process.fastdiscount;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.base.service.biz.BaseBizAvailableTimeService;
import top.kx.kxss.base.service.discount.BaseDiscountTemplateService;
import top.kx.kxss.base.vo.result.discount.DiscountTemplateSimpleResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.base.EquityTypeEnum;
import top.kx.kxss.model.enumeration.pos.DiscountCalTypeEnum;
import top.kx.kxss.model.enumeration.pos.DiscountTypeEnum;
import top.kx.kxss.pos.bean.CalcFastDiscountQuery;
import top.kx.kxss.pos.service.CalcPriceService;
import top.kx.kxss.pos.slot.CalcFastDiscountContext;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 数据处理
 *
 * <AUTHOR>
 */
@Component("fastDiscountServiceDataProcess")
@Slf4j
@DS(DsConstant.BASE_TENANT)
public class FastDiscountServiceDataProcess extends NodeComponent {


    @Autowired
    private BaseBizAvailableTimeService baseBizAvailableTimeService;
    @Autowired
    private BaseDiscountTemplateService baseDiscountTemplateService;
    @Autowired
    private CalcPriceService calcPriceService;

    @Override
    public void process() {
        Integer index = this.getLoopIndex();
        CalcFastDiscountQuery query = this.getSlot().getRequestData();
        DiscountTemplateSimpleResultVO discountTemplateVO = query.getDiscountTemplateList().get(index);
        CalcFastDiscountContext fastDiscountContext = this.getContextBean(CalcFastDiscountContext.class);
        Map<Long, DiscountTemplateSimpleResultVO> discountTemplateMap = fastDiscountContext.getDiscountTemplateMap();
        List<PosCashService> tableList = fastDiscountContext.getServiceList();
        List<Long> longList = tableList.stream().map(PosCashService::getServiceId).collect(Collectors.toList());
        Map<Long, Boolean> bizIdMatchDiscount = baseDiscountTemplateService.checkBizIdMatchDiscount(discountTemplateVO,
                longList, EquityTypeEnum.SERVICE);
        BigDecimal discountValue = discountTemplateVO.getDiscountValue();
        for (PosCashService cashDetail : tableList) {
            cashDetail.setFastDiscountCalcAmount(BigDecimal.ZERO);
            cashDetail.setIsPay(false);
            cashDetail.setAssessedAmount(ObjectUtil.defaultIfNull(cashDetail.getAssessedAmount(), BigDecimal.ZERO));
            cashDetail.setPaid(ObjectUtil.defaultIfNull(cashDetail.getPaid(), BigDecimal.ZERO));
            if (cashDetail.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            if (cashDetail.getAmount().compareTo(cashDetail.getPaid()) <= 0) {
                continue;
            }
            if (cashDetail.getIsDiscount() != null && !cashDetail.getIsDiscount()) {
                continue;
            }
            BigDecimal amount = cashDetail.getAmount().subtract(cashDetail.getAssessedAmount()).subtract(cashDetail.getPaid());
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }
            //判断权益
            if (Objects.equals(bizIdMatchDiscount.get(cashDetail.getServiceId()), false)) {
                continue;
            }
            boolean isAvailable = false;
            //判断时间是不是可用
            LocalDateTime startTime = cashDetail.getStartTime();
            if (baseBizAvailableTimeService.checkAvailableTime(discountTemplateVO, startTime, cashDetail.getEndTime())) {
                discountTemplateMap.put(discountTemplateVO.getId(), discountTemplateVO);
                isAvailable = true;
            }
//            LocalDateTime endTime = cashDetail.getEndTime();
//            if (!isAvailable && baseBizAvailableTimeService.checkAvailableTime(discountTemplateVO, endTime)) {
//                discountTemplateMap.put(discountTemplateVO.getId(), discountTemplateVO);
//                isAvailable = true;
//            }
            if (!isAvailable) {
                continue;
            }
            if (discountTemplateVO.getDiscountType().equals(DiscountCalTypeEnum.REDUCTION.getCode())
                    && discountTemplateVO.getDiscountValue().compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            cashDetail.setIsPay(true);
            //计算出可以参与折扣的时长
            Integer endTimeDuration = baseBizAvailableTimeService.getEndTimeDuration(discountTemplateVO.getAvailableTimeList(), cashDetail.getStartTime(),
                    cashDetail.getEndTime(), cashDetail.getDuration());
            BigDecimal tableAmount = amount;
            if (!Objects.equals(endTimeDuration, cashDetail.getDuration())) {
                //算出需要折扣的金额
                PosCashService cashService = BeanUtil.copyProperties(cashDetail, PosCashService.class);
                cashService.setDuration(endTimeDuration);
                calcPriceService.refreshNewServiceByMin(cashService);
                if (discountTemplateVO.getIsSuperpose() != null
                        && discountTemplateVO.getIsSuperpose()
                        && cashDetail.getDiscountType().equals(DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                    if (cashService.getAmount().compareTo(tableAmount) <= 0) {
                        tableAmount = cashService.getAmount();
                    }
                    if (discountTemplateVO.getDiscountType().equals(DiscountCalTypeEnum.DISCOUNT.getCode())) {

                        BigDecimal discount = discountTemplateVO.getDiscountValue()
                                .divide(BigDecimal.TEN).setScale(2, RoundingMode.HALF_UP);
                        BigDecimal bigDecimal = tableAmount.multiply(discount)
                                .setScale(2, RoundingMode.HALF_UP);
                        BigDecimal subtract = tableAmount.subtract(bigDecimal);
                        if (subtract.compareTo(tableAmount) > 0) {
                            subtract = tableAmount;
                        }
                        cashDetail.setFastDiscountAssessedAmount(subtract);
                        tableAmount = amount;
                    }
                } else {
                    tableAmount = cashService.getAmount();
                }
            } else {
                tableAmount = cashDetail.getOrginPrice();
                if (discountTemplateVO.getIsSuperpose() != null
                        && discountTemplateVO.getIsSuperpose()
                        && cashDetail.getDiscountType().equals(DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                    tableAmount = cashDetail.getAmount();
                    cashDetail.setFastDiscountAssessedAmount(tableAmount);
                }
            }
            if (discountTemplateVO.getDiscountType().equals(DiscountCalTypeEnum.REDUCTION.getCode())) {
                if (tableAmount.compareTo(discountTemplateVO.getDiscountValue()) >= 0) {
                    tableAmount = discountTemplateVO.getDiscountValue();
                    discountTemplateVO.setDiscountValue(BigDecimal.ZERO);
                } else {
                    discountTemplateVO.setDiscountValue(discountTemplateVO.getDiscountValue().subtract(tableAmount));
                }
            }
            if (discountTemplateVO.getTotalAmount() == null) {
                discountTemplateVO.setTotalAmount(BigDecimal.ZERO);
            }
            discountTemplateVO.setTotalAmount(discountTemplateVO.getTotalAmount().add(tableAmount));
            cashDetail.setFastDiscountCalcAmount(tableAmount);
            cashDetail.setFastDiscountType(discountTemplateVO.getDiscountType());
            cashDetail.setDiscountTemplateId(discountTemplateVO.getId());
            cashDetail.setIsFastDiscountSuperpose(discountTemplateVO.getIsSuperpose() != null && discountTemplateVO.getIsSuperpose());
            //不参与折上折 覆盖之前优惠
            if (discountTemplateVO.getIsSuperpose() == null || !discountTemplateVO.getIsSuperpose()
                    || !ObjectUtil.equal(cashDetail.getDiscountType(), DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                cashDetail.setDiscountType(ObjectUtil.equal(discountTemplateVO.getDiscountType(), DiscountCalTypeEnum.REDUCTION.getCode())
                        ? DiscountTypeEnum.FAST_DISCOUNT_REDUCTION.getCode() : DiscountTypeEnum.FAST_DISCOUNT_DISCOUNT.getCode());
                cashDetail.setDiscount(discountValue);
            } else {
                if (ObjectUtil.isNotNull(cashDetail.getDiscountTemplateId())) {
                    cashDetail.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                    cashDetail.setDiscount(BigDecimal.ZERO);
                }
            }
        }
    }

    @Override
    public boolean isAccess() {
        CalcFastDiscountContext fastDiscountContext = this.getContextBean(CalcFastDiscountContext.class);
        return CollUtil.isNotEmpty(fastDiscountContext.getServiceList());
    }
}
