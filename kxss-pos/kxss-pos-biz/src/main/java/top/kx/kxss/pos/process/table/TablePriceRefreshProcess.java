package top.kx.kxss.pos.process.table;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.yomahub.liteflow.core.FlowExecutor;
import com.yomahub.liteflow.core.NodeComponent;
import com.yomahub.liteflow.flow.LiteflowResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.PosCashStop;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.entity.thail.PosCashThail;
import top.kx.kxss.app.service.thail.PosCashThailService;
import top.kx.kxss.base.entity.tableCharging.setting.BaseTableChargingSetting;
import top.kx.kxss.common.constant.BizConstant;
import top.kx.kxss.model.enumeration.app.CashTableStatusEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashTypeEnum;
import top.kx.kxss.model.enumeration.pos.DiscountTypeEnum;
import top.kx.kxss.pos.slot.CalcTablePriceContext;
import top.kx.kxss.pos.slot.DetailCalcContext;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 台桌刷新价格
 *
 * <AUTHOR>
 */
@Component("tablePriceRefreshProcess")
@Slf4j
public class TablePriceRefreshProcess extends NodeComponent {

    @Autowired
    private FlowExecutor flowExecutor;
    @Autowired
    private PosCashThailService posCashThailService;


    @Override
    public void process() throws Exception {
        DetailCalcContext detailCalcContext = this.getContextBean(DetailCalcContext.class);
        //这一步必须有，否则线程池中无法确定数据源
        //查询订单明细信息
        PosCash posCash = detailCalcContext.getPosCash();
        if (CollUtil.isEmpty(detailCalcContext.getTableList())
                && CollUtil.isEmpty(detailCalcContext.getThailTableList())
                && CollUtil.isEmpty(detailCalcContext.getPackFieldTableList())
                && ObjectUtil.equal(posCash.getType(), PosCashTypeEnum.START_TABLE.getCode())
                && ObjectUtil.isNull(posCash.getThailId())
                && ObjectUtil.equal(posCash.getBillState(), PosCashBillStateEnum.NO_SETTLED.getCode())
        ) {
            //有套餐无需补明细
            long count = posCashThailService.count(Wraps.<PosCashThail>lbQ()
                    .eq(PosCashThail::getDeleteFlag, 0)
                    .eq(PosCashThail::getCashId, posCash.getId()));
            if (count > 0) {
                return;
            }
            PosCashTable cashTable = PosCashTable.builder().build();
            cashTable.setTableId(posCash.getTableId());
            cashTable.setStartTime(posCash.getCreatedTime().withSecond(0).withNano(0));
            cashTable.setCreatedTime(posCash.getCreatedTime());
            cashTable.setUpdatedTime(LocalDateTime.now());
            cashTable.setTableName(posCash.getTableName());
            cashTable.setChargingSettingId(BizConstant.DEFAULT_ID);
            cashTable.setIsPackField(false);

            cashTable.setEndTime(LocalDateTime.now());
            cashTable.setCashId(posCash.getId());
            cashTable.setCalcDuration(0);
            cashTable.setCalcAmount(BigDecimal.ZERO);
            cashTable.setCalcType(null);
            cashTable.setCalcPrice(BigDecimal.ZERO);
            cashTable.setIsAccount(null);
            cashTable.setIsTableGiftPay(null);
            cashTable.setIsDiscount(null);
            cashTable.setStatus(CashTableStatusEnum.TIMING.getCode());
            cashTable.setAmount(BigDecimal.ZERO);
            cashTable.setDiscountAmount(BigDecimal.ZERO);
            cashTable.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
            cashTable.setAssessedAmount(BigDecimal.ZERO);
            cashTable.setPaid(BigDecimal.ZERO);
            cashTable.setDiscount(BigDecimal.ZERO);
            cashTable.setOrginPrice(BigDecimal.ZERO);
            cashTable.setPrice(BigDecimal.ZERO);
            cashTable.setCycle(null);
            if (cashTable.getDuration() == null || cashTable.getDuration() == 0) {
                long startMinutes = DateUtils.calDifMinutes(cashTable.getStartTime(), cashTable.getEndTime());
                cashTable.setDuration(Integer.valueOf(startMinutes + ""));
            }
            if (StrUtil.isBlank(cashTable.getCycle())) {
                cashTable.setCycle("0.00元/分钟");
            }
            detailCalcContext.getTableList().add(cashTable);
        }
        if (CollUtil.isEmpty(detailCalcContext.getTableList())) {
            return;
        }
        Map<Long, List<BaseTableChargingSetting>> chargingSetting = MapUtil.newHashMap();
        try {
            CalcTablePriceContext context = new CalcTablePriceContext();
            context.setFlowCommonContext(context);
            context.setPosCash(posCash);
            context.setMemberGradeResultVO(detailCalcContext.getMemberGradeResultVO());
            context.setCashTableList(detailCalcContext.getTableList());
            context.setDetailStopDurationMap(MapUtil.newHashMap());
            //暂停记录
            if (CollUtil.isNotEmpty(detailCalcContext.getDetailStopList())) {
                context.setDetailStopDurationMap(detailCalcContext.getDetailStopList().stream()
                        .collect(Collectors.groupingBy(v -> v.getBizType()
                                + "_" + v.getSourceId(), Collectors.summingInt(PosCashStop::getDuration))));
            }
            context.setIsStop(detailCalcContext.getIsStop());
            context.setMemberId(posCash.getMemberId());
            LiteflowResponse response = flowExecutor.execute2Resp(BizConstant.FLOW.CALC_TABLE_PRICE, detailCalcContext, context);
            ArgumentAssert.isFalse(!response.isSuccess(), response.getMessage());
            CalcTablePriceContext contextBean = response.getContextBean(CalcTablePriceContext.class);
            List<PosCashTable> resultTableList = contextBean.getResultTableList();
            detailCalcContext.setTableList(resultTableList);
            detailCalcContext.setTableRemoveList(contextBean.getTableRemoveList());
            detailCalcContext.setServiceActivityMap(contextBean.getServiceActivityMap());
//            detailCalcContext.setTableList(calcPriceService.refreshTable(removeList, detailCalcContext.getPosCash(),
//                    detailCalcContext.getTableList(), detailCalcContext.getIsStop(), chargingSetting, detailCalcContext.getMemberInfo()));
        } catch (Exception e) {
            log.error("订单台桌计算价格异常", e);
            detailCalcContext.setTableList(Lists.newArrayList());
        }
        detailCalcContext.setChargingSettingMap(chargingSetting);
    }

    @Override
    public boolean isAccess() {
        DetailCalcContext context = this.getContextBean(DetailCalcContext.class);
        //这一步必须有，否则线程池中无法确定数据源
        context.setContextUtil(context);
        if (CollUtil.isEmpty(context.getTableList())) {
            context.setTableList(Lists.newArrayList());
        }
        return ObjectUtil.isNotNull(context.getPosCash());
    }
}
