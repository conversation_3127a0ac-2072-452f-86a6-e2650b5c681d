package top.kx.kxss.pos.process;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import com.yomahub.liteflow.core.NodeComponent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.entity.thail.PosCashThail;
import top.kx.kxss.app.manager.cash.payment.PosCashPaymentManager;
import top.kx.kxss.app.service.bizcache.BizCacheService;
import top.kx.kxss.base.entity.member.MemberBalanceChange;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.member.coupon.MemberCoupon;
import top.kx.kxss.base.service.member.MemberBalanceChangeService;
import top.kx.kxss.base.service.member.card.MemberCardService;
import top.kx.kxss.base.service.member.coupon.MemberCouponService;
import top.kx.kxss.base.vo.result.member.card.MemberCardResultVO;
import top.kx.kxss.base.vo.result.member.grade.MemberGradeResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.BizCacheEnum;
import top.kx.kxss.model.enumeration.app.CashTableStatusEnum;
import top.kx.kxss.model.enumeration.base.*;
import top.kx.kxss.model.enumeration.pos.DiscountTypeEnum;
import top.kx.kxss.pos.bean.PriceCalcStepVO;
import top.kx.kxss.pos.entity.cash.PosCashCard;
import top.kx.kxss.pos.service.member.MemberService;
import top.kx.kxss.pos.slot.DetailCalcContext;
import top.kx.kxss.pos.slot.PriceCalcContext;
import top.kx.kxss.pos.vo.member.MemberResultVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 价格初始化计算组件
 *
 * <AUTHOR>
 */
@Component("priceStepOriginalProcess")
@DS(DsConstant.BASE_TENANT)
@Slf4j
public class PriceStepOriginalProcess extends NodeComponent {

    @Autowired
    private BizCacheService bizCacheService;
    @Autowired
    private MemberCouponService memberCouponService;
    @Autowired
    private PosCashPaymentManager posCashPaymentManager;
    @Autowired
    private MemberService memberService;
    @Autowired
    private MemberCardService memberCardService;
    @Autowired
    private MemberBalanceChangeService memberBalanceChangeService;

    @Override
    public void process() throws Exception {
        PriceCalcContext context = this.getContextBean(PriceCalcContext.class);
        DetailCalcContext detailContext = this.getContextBean(DetailCalcContext.class);
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal discountTotalAmount = BigDecimal.ZERO;
        BigDecimal discountAmount = BigDecimal.ZERO;
        MemberGradeResultVO memberGradeResultVO = context.getMemberGradeResultVO();
        List<String> noDiscountType = ObjectUtil.isNotNull(memberGradeResultVO)
                ? memberGradeResultVO.getNoDiscountRange() : Lists.newArrayList();
        PosCash posCash = detailContext.getPosCash();
        Boolean isMemberPrice = posCash.getIsMemberDiscount() != null
                && posCash.getIsMemberDiscount();
        //会员信息
        //余额为零时不享受折扣
        boolean isZero = false;
        BigDecimal accountBalance = BigDecimal.ZERO;
        if (ObjectUtil.isNotNull(context.getMemberInfo())) {
            MemberInfo memberInfo = context.getMemberInfo();
            MemberResultVO memberVO = memberService.getMemberVO(memberInfo);
            accountBalance = memberVO.getAccountBalance();
            List<BigDecimal> decimalList = posCashPaymentManager.listObjs(Wraps.<PosCashPayment>lbQ()
                    .select(PosCashPayment::getAmount)
                    .inSql(PosCashPayment::getPayTypeId, "select id from base_payment_type where delete_flag = 0 " +
                            "and biz_type = " + PaymentBizTypeEnum.ACCOUNT.getCode())
                    .eq(PosCashPayment::getCashId, context.getPosCash().getId()), Convert::toBigDecimal);
            if (CollUtil.isNotEmpty(decimalList)) {
                accountBalance = accountBalance.add(decimalList.stream().reduce(BigDecimal.ZERO, BigDecimal::add))
                        .setScale(2, RoundingMode.HALF_UP);
            }
            if (accountBalance.compareTo(BigDecimal.ZERO) <= 0) {
                isZero = true;
            }
            List<MemberCardResultVO> list = bizCacheService.cacheList(MemberCardResultVO.class, BizCacheEnum.MEMBER_CARD, memberInfo.getId());
            memberVO.setCardNum(CollUtil.isNotEmpty(list) ? list.size() : 0);
            context.getCashDetailResultVO().setIsCard(CollUtil.isNotEmpty(list));
            memberVO.setCardUsedNum(0);
            if (CollUtil.isNotEmpty(list)) {
                List<MemberCardResultVO> memberCardResultVOList = list.stream().filter(v -> !v.getType().equals(CardTypeEnum.STORED.getCode())).collect(Collectors.toList());
                memberCardResultVOList.removeIf(v -> !Arrays.asList(CashCardStatusEnum.IN_USE.getCode(), CashCardStatusEnum.NO_ACTIVATE.getCode())
                        .contains(v.getStatus()) || !memberCardService.availableTime(v));
                memberVO.setCardUsedNum(CollUtil.isNotEmpty(memberCardResultVOList) ? memberCardResultVOList.size() : 0);
            }
            if (CollUtil.isNotEmpty(list)) {
                List<MemberCardResultVO> memberCardResultVOList = list.stream().filter(v -> v.getType().equals(CardTypeEnum.STORED.getCode()))
                        .collect(Collectors.toList());
                memberCardResultVOList.removeIf(v -> !Arrays.asList(CashCardStatusEnum.IN_USE.getCode(), CashCardStatusEnum.NO_ACTIVATE.getCode())
                        .contains(v.getStatus()) || !memberCardService.availableTime(v));
                memberVO.setStoredCardUsedNum(CollUtil.isNotEmpty(memberCardResultVOList) ? memberCardResultVOList.size() : 0);
            }
            long count = memberCouponService.count(Wraps.<MemberCoupon>lbQ()
                    .eq(MemberCoupon::getDeleteFlag, 0)
                    .in(MemberCoupon::getStatus, Arrays.asList(CouponStatusEnum.RUNNING.getCode()
                            , CouponStatusEnum.LOCK.getCode()))
                    .eq(MemberCoupon::getMemberId, memberInfo.getId()));
            context.getCashDetailResultVO().setIsCoupon(count > 0);
            memberVO.setCouponNum(Integer.parseInt(count + ""));
            memberVO.setType(memberInfo.getType());
            memberVO.setIsPassword(StrUtil.isNotBlank(memberInfo.getPassword())
                    || StrUtil.isNotBlank(memberInfo.getFingerprint()));
            if (ObjectUtil.isNotNull(posCash)) {
                MemberBalanceChange memberBalanceChange = memberBalanceChangeService.getOne(Wraps.<MemberBalanceChange>lbQ()
                        .eq(MemberBalanceChange::getDeleteFlag, 0)
                        .eq(MemberBalanceChange::getMemberId, memberInfo.getId())
                        .eq(MemberBalanceChange::getSourceId, posCash.getId())
                        .orderByDesc(SuperEntity::getCreatedTime)
                        .last("limit 1"));
                if (ObjectUtil.isNotNull(memberBalanceChange)) {
                    memberVO.setAccountBalanceSnapshot(memberBalanceChange.getAccountBalance());
                } else {
                    memberVO.setAccountBalanceSnapshot(memberVO.getAccountBalance());
                }
            }

            context.getCashDetailResultVO().setMemberVO(memberVO);
        }
        if (CollUtil.isNotEmpty(detailContext.getProductList())) {
            BigDecimal productAmount = detailContext.getProductList().stream()
                    .filter(v -> v.getIsAccount() != null && v.getIsAccount())
                    .map(PosCashProduct::getOrginPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            totalAmount = totalAmount.add(productAmount);
            discountTotalAmount = discountTotalAmount.add(detailContext.getProductList()
                    .stream()
                    .filter(v -> v.getIsAccount() != null && v.getIsAccount()).map(PosCashProduct::getOrginPrice)
                    .filter(ObjectUtil::isNotNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            if (ObjectUtil.isNotNull(memberGradeResultVO)
                    && CollUtil.isNotEmpty(memberGradeResultVO.getDiscountTypes()) &&
                    ObjectUtil.isNotNull(memberGradeResultVO.getProductDiscount()) &&
                    memberGradeResultVO.getDiscountTypes().contains(EquityTypeEnum.PRODUCT.getCode())) {
                for (PosCashProduct v : detailContext.getProductList()) {
                    if (v.getIsDiscount() == null || !v.getIsDiscount()) {
                        continue;
                    }
                    BigDecimal amount = v.getOrginPrice().multiply(memberGradeResultVO.getProductDiscount())
                            .divide(BigDecimal.valueOf(10), 2, RoundingMode.HALF_UP);
                    if (v.getIsAccount() == null || !v.getIsAccount()) {
                        continue;
                    }
                    discountAmount = discountAmount.add(v.getOrginPrice().subtract(amount));
                }
            }
            if (isZero
                    && ObjectUtil.isNotNull(memberGradeResultVO)
                    && StrUtil.isNotBlank(memberGradeResultVO.getDiscountSetting())
                    && ObjectUtil.equal(memberGradeResultVO.getDiscountSetting(), "1")
                    && CollUtil.isNotEmpty(noDiscountType)
            ) {
                detailContext.getProductList().forEach(v -> {
                            if (noDiscountType.contains(NoDiscountTypeEnum.PRODUCT.getCode())) {
                                v.setIsDiscount(false);
                                if (ObjectUtil.equal(v.getDiscountType(), DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                                    v.setDiscountAmount(BigDecimal.ZERO);
                                    v.setDiscount(BigDecimal.ZERO);
                                    v.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                                }
                            }
                            if (noDiscountType.contains(NoDiscountTypeEnum.PRODUCT_PRICE.getCode())
                                    || !isMemberPrice) {
                                v.setIsMemberPrice(false);
                                if (ObjectUtil.equal(v.getDiscountType(), DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode())) {
                                    v.setDiscountAmount(BigDecimal.ZERO);
                                    v.setDiscount(BigDecimal.ZERO);
                                    v.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                                }
                            }
                        }
                );
            }
        }
        if (CollUtil.isNotEmpty(detailContext.getTableList())) {
            BigDecimal tableAmount = detailContext.getTableList()
                    .stream()
                    .filter(v -> !ObjectUtil.equal(v.getStatus(), CashTableStatusEnum.REFUND.getCode())
                            && v.getIsAccount() != null && v.getIsAccount())
                    .collect(Collectors.toList()).stream().map(PosCashTable::getOrginPrice)
                    .filter(ObjectUtil::isNotNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            totalAmount = totalAmount.add(tableAmount);
            discountTotalAmount = discountTotalAmount.add(detailContext.getTableList()
                    .stream().filter(v -> !ObjectUtil.equal(v.getStatus(), CashTableStatusEnum.REFUND.getCode()))
                    .collect(Collectors.toList()).stream()
                    .filter(v -> v.getIsAccount() != null && v.getIsAccount()).map(PosCashTable::getOrginPrice)
                    .filter(ObjectUtil::isNotNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            if (ObjectUtil.isNotNull(memberGradeResultVO)
                    && CollUtil.isNotEmpty(memberGradeResultVO.getDiscountTypes()) &&
                    ObjectUtil.isNotNull(memberGradeResultVO.getTableDiscount()) &&
                    memberGradeResultVO.getDiscountTypes().contains(EquityTypeEnum.TABLE.getCode())) {
                for (PosCashTable v : detailContext.getTableList()) {
                    if (v.getIsDiscount() == null || !v.getIsDiscount()) {
                        continue;
                    }
                    BigDecimal amount = v.getOrginPrice().multiply(memberGradeResultVO.getTableDiscount())
                            .divide(BigDecimal.valueOf(10), 2, RoundingMode.HALF_UP);
                    if (v.getIsAccount() == null || !v.getIsAccount()) {
                        continue;
                    }
                    discountAmount = discountAmount.add(v.getOrginPrice().subtract(amount));
                }
            }
            if (isZero
                    && ObjectUtil.isNotNull(memberGradeResultVO)
                    && StrUtil.isNotBlank(memberGradeResultVO.getDiscountSetting())
                    && ObjectUtil.equal(memberGradeResultVO.getDiscountSetting(), "1")
                    && CollUtil.isNotEmpty(noDiscountType)
                    && noDiscountType.contains(NoDiscountTypeEnum.TABLE.getCode())
            ) {
                detailContext.getTableList().forEach(v -> {
                            v.setIsDiscount(false);
                            if (ObjectUtil.equal(v.getDiscountType(), DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                                v.setDiscountAmount(BigDecimal.ZERO);
                                v.setDiscount(BigDecimal.ZERO);
                                v.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                            }
                        }
                );
            }
        }
        if (CollUtil.isNotEmpty(detailContext.getServiceList())) {
            BigDecimal serviceAmount = detailContext.getServiceList()
                    .stream()
                    .filter(v -> !ObjectUtil.equal(v.getStatus(), CashTableStatusEnum.REFUND.getCode())
                            && v.getIsAccount() != null && v.getIsAccount())
                    .collect(Collectors.toList()).stream().map(PosCashService::getOrginPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            totalAmount = totalAmount.add(serviceAmount);
            discountTotalAmount = discountTotalAmount.add(detailContext.getServiceList()
                    .stream().filter(v -> !ObjectUtil.equal(v.getStatus(), CashTableStatusEnum.REFUND.getCode()))
                    .collect(Collectors.toList()).stream()
                    .filter(v -> v.getIsAccount() != null && v.getIsAccount()).map(PosCashService::getOrginPrice)
                    .filter(ObjectUtil::isNotNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            if (ObjectUtil.isNotNull(memberGradeResultVO)
                    && CollUtil.isNotEmpty(memberGradeResultVO.getDiscountTypes()) &&
                    ObjectUtil.isNotNull(memberGradeResultVO.getServiceDiscount()) &&
                    memberGradeResultVO.getDiscountTypes().contains(EquityTypeEnum.SERVICE.getCode())) {
                for (PosCashService v : detailContext.getServiceList()) {
                    if (v.getIsDiscount() == null || !v.getIsDiscount()) {
                        continue;
                    }
                    BigDecimal amount = v.getOrginPrice().multiply(memberGradeResultVO.getServiceDiscount())
                            .divide(BigDecimal.valueOf(10), 2, RoundingMode.HALF_UP);
                    if (v.getIsAccount() == null || !v.getIsAccount()) {
                        continue;
                    }
                    discountAmount = discountAmount.add(v.getOrginPrice().subtract(amount));
                }
            }
            if (isZero
                    && ObjectUtil.isNotNull(memberGradeResultVO)
                    && StrUtil.isNotBlank(memberGradeResultVO.getDiscountSetting())
                    && ObjectUtil.equal(memberGradeResultVO.getDiscountSetting(), "1")
                    && CollUtil.isNotEmpty(noDiscountType)
                    && noDiscountType.contains(NoDiscountTypeEnum.SERVICE.getCode())
            ) {
                detailContext.getServiceList().forEach(v -> {
                            v.setIsDiscount(false);
                            if (ObjectUtil.equal(v.getDiscountType(), DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                                v.setDiscountAmount(BigDecimal.ZERO);
                                v.setDiscount(BigDecimal.ZERO);
                                v.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                            }
                        }
                );
            }
        }
        if (CollUtil.isNotEmpty(detailContext.getThailList())) {
            BigDecimal thailAmount = detailContext.getThailList().stream()
                    .filter(v -> v.getIsAccount() != null && v.getIsAccount())
                    .map(PosCashThail::getOrginPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            totalAmount = totalAmount.add(thailAmount);
            discountTotalAmount = discountTotalAmount.add(detailContext.getThailList()
                    .stream()
                    .filter(v -> v.getIsAccount() != null && v.getIsAccount()).map(PosCashThail::getOrginPrice)
                    .filter(ObjectUtil::isNotNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            if (ObjectUtil.isNotNull(memberGradeResultVO)
                    && CollUtil.isNotEmpty(memberGradeResultVO.getDiscountTypes()) &&
                    ObjectUtil.isNotNull(memberGradeResultVO.getProductDiscount()) &&
                    memberGradeResultVO.getDiscountTypes().contains(EquityTypeEnum.THAIL.getCode())) {
                for (PosCashThail v : detailContext.getThailList()) {
                    if (v.getIsDiscount() == null || !v.getIsDiscount()) {
                        continue;
                    }
                    BigDecimal amount = v.getOrginPrice().multiply(memberGradeResultVO.getThailDiscount())
                            .divide(BigDecimal.valueOf(10), 2, RoundingMode.HALF_UP);
                    if (v.getIsAccount() == null || !v.getIsAccount()) {
                        continue;
                    }
                    discountAmount = discountAmount.add(v.getOrginPrice().subtract(amount));
                }
            }
            if (isZero
                    && ObjectUtil.isNotNull(memberGradeResultVO)
                    && StrUtil.isNotBlank(memberGradeResultVO.getDiscountSetting())
                    && ObjectUtil.equal(memberGradeResultVO.getDiscountSetting(), "1")
                    && CollUtil.isNotEmpty(noDiscountType)
            ) {
                detailContext.getThailList().forEach(v -> {
                            if (noDiscountType.contains(NoDiscountTypeEnum.THAIL.getCode())) {
                                v.setIsDiscount(false);
                                if (ObjectUtil.equal(v.getDiscountType(), DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                                    v.setDiscountAmount(BigDecimal.ZERO);
                                    v.setDiscount(BigDecimal.ZERO);
                                    v.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                                }
                            }
                            if (noDiscountType.contains(NoDiscountTypeEnum.THAIL_PRICE.getCode())
                                    || !isMemberPrice) {
                                v.setIsMemberPrice(false);
                                if (ObjectUtil.equal(v.getDiscountType(), DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode())) {
                                    v.setDiscountAmount(BigDecimal.ZERO);
                                    v.setDiscount(BigDecimal.ZERO);
                                    v.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                                }
                            }
                        }
                );
            }
        }
        if (CollUtil.isNotEmpty(detailContext.getBuyCardList())) {
            totalAmount = totalAmount.add(detailContext.getBuyCardList().stream().map(PosCashCard::getOrginPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            discountTotalAmount = discountTotalAmount.add(detailContext.getBuyCardList()
                    .stream()
                    .map(PosCashCard::getOrginPrice)
                    .filter(ObjectUtil::isNotNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            if (isZero
                    && ObjectUtil.isNotNull(memberGradeResultVO)
                    && StrUtil.isNotBlank(memberGradeResultVO.getDiscountSetting())
                    && ObjectUtil.equal(memberGradeResultVO.getDiscountSetting(), "1")
                    && CollUtil.isNotEmpty(noDiscountType)
                    && noDiscountType.contains(NoDiscountTypeEnum.CARD.getCode())
            ) {
                detailContext.getBuyCardList().forEach(v -> {
                            if (ObjectUtil.equal(v.getDiscountType(), DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode())) {
                                v.setDiscountAmount(BigDecimal.ZERO);
                                v.setDiscount(BigDecimal.ZERO);
                                v.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                            }
                        }
                );
            }
        }
        Boolean isAccount = null;
        if (ObjectUtil.isNotNull(memberGradeResultVO)
                && StrUtil.isNotBlank(memberGradeResultVO.getDiscountSetting())
                && ObjectUtil.equal(memberGradeResultVO.getDiscountSetting(), "2")) {
            isAccount = accountBalance.compareTo(totalAmount) < 0;
        }
        if (ObjectUtil.isNotNull(memberGradeResultVO)
                && StrUtil.isNotBlank(memberGradeResultVO.getDiscountSetting())
                && ObjectUtil.equal(memberGradeResultVO.getDiscountSetting(), "3")) {
            BigDecimal subtract = discountTotalAmount.subtract(discountAmount);
            isAccount = accountBalance.compareTo(subtract) < 0;
        }
        if (isAccount != null && isAccount
                && ObjectUtil.isNotNull(memberGradeResultVO)
                && CollUtil.isNotEmpty(noDiscountType)
                && CollUtil.isNotEmpty(detailContext.getProductList())
        ) {
            detailContext.getProductList().forEach(v -> {
                        if (noDiscountType.contains(NoDiscountTypeEnum.PRODUCT.getCode())) {
                            v.setIsDiscount(false);
                            if (ObjectUtil.equal(v.getDiscountType(), DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                                v.setDiscountAmount(BigDecimal.ZERO);
                                v.setDiscount(BigDecimal.ZERO);
                                v.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                            }
                        }
                        if (noDiscountType.contains(NoDiscountTypeEnum.PRODUCT_PRICE.getCode())
                                || !isMemberPrice) {
                            v.setIsMemberPrice(false);
                            if (ObjectUtil.equal(v.getDiscountType(), DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode())) {
                                v.setDiscountAmount(BigDecimal.ZERO);
                                v.setDiscount(BigDecimal.ZERO);
                                v.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                            }
                        }
                    }
            );
        }
        if (isAccount != null && isAccount
                && ObjectUtil.isNotNull(memberGradeResultVO)
                && CollUtil.isNotEmpty(noDiscountType)
                && CollUtil.isNotEmpty(detailContext.getTableList())
                && noDiscountType.contains(NoDiscountTypeEnum.TABLE.getCode())) {
            detailContext.getTableList().forEach(v -> {
                        v.setIsDiscount(false);
                        if (ObjectUtil.equal(v.getDiscountType(), DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                            v.setDiscountAmount(BigDecimal.ZERO);
                            v.setDiscount(BigDecimal.ZERO);
                            v.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                        }
                    }
            );
        }
        if (isAccount != null && isAccount
                && ObjectUtil.isNotNull(memberGradeResultVO)
                && CollUtil.isNotEmpty(noDiscountType)
                && CollUtil.isNotEmpty(detailContext.getServiceList())
                && noDiscountType.contains(NoDiscountTypeEnum.SERVICE.getCode())) {
            detailContext.getServiceList().forEach(v -> {
                        v.setIsDiscount(false);
                        if (ObjectUtil.equal(v.getDiscountType(), DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                            v.setDiscountAmount(BigDecimal.ZERO);
                            v.setDiscount(BigDecimal.ZERO);
                            v.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                        }
                    }
            );
        }

        if (isAccount != null && isAccount
                && ObjectUtil.isNotNull(memberGradeResultVO)
                && CollUtil.isNotEmpty(noDiscountType)
                && CollUtil.isNotEmpty(detailContext.getBuyCardList())
                && noDiscountType.contains(NoDiscountTypeEnum.CARD.getCode())) {
            detailContext.getBuyCardList().forEach(v -> {
                        if (ObjectUtil.equal(v.getDiscountType(), DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                            v.setDiscountAmount(BigDecimal.ZERO);
                            v.setDiscount(BigDecimal.ZERO);
                            v.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                        }
                    }
            );
        }
        if ((isAccount != null && isAccount
                && ObjectUtil.isNotNull(memberGradeResultVO)
                && CollUtil.isNotEmpty(noDiscountType)
                && CollUtil.isNotEmpty(detailContext.getThailList()))) {
            detailContext.getThailList().forEach(v -> {
                        if (noDiscountType.contains(NoDiscountTypeEnum.THAIL.getCode())) {
                            v.setIsDiscount(false);
                            if (ObjectUtil.equal(v.getDiscountType(), DiscountTypeEnum.MEMBER_GRADE_DISCOUNT.getCode())) {
                                v.setDiscountAmount(BigDecimal.ZERO);
                                v.setDiscount(BigDecimal.ZERO);
                                v.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                            }
                        }
                        if (noDiscountType.contains(NoDiscountTypeEnum.THAIL_PRICE.getCode())
                                || !isMemberPrice) {
                            v.setIsMemberPrice(false);
                            if (ObjectUtil.equal(v.getDiscountType(), DiscountTypeEnum.MEMBER_PRICE_DISCOUNT.getCode())) {
                                v.setDiscountAmount(BigDecimal.ZERO);
                                v.setDiscount(BigDecimal.ZERO);
                                v.setDiscountType(DiscountTypeEnum.ORIGINAL.getCode());
                            }
                        }
                    }
            );

        }
//        if (CollUtil.isNotEmpty(detailContext.getThailList())) {
//            totalAmount = totalAmount.add(detailContext.getThailList().stream().map(PosCashThail::getOrginPrice)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add));
//        }
        //加入到价格步骤中
        context.addPriceCalcStep(PriceCalcStepVO.builder()
                .extId(null).currPrice(totalAmount)
                .prePrice(BigDecimal.ZERO).priceType(DiscountTypeEnum.ORIGINAL)
                .priceChange(totalAmount).stepDesc(DiscountTypeEnum.ORIGINAL.getDesc())
                .build());
    }

    @Override
    public boolean isAccess() {
        PriceCalcContext context = this.getContextBean(PriceCalcContext.class);
        //这一步必须有，否则线程池中无法确定数据源
        context.setContextUtil(context);
        return true;
    }
}
