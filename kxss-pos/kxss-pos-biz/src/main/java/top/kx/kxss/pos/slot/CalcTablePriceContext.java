package top.kx.kxss.pos.slot;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.query.flow.FlowCommonContext;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.entity.tableCharging.BaseTableCharging;
import top.kx.kxss.base.entity.tableCharging.grade.BaseTableChargingSettingGrade;
import top.kx.kxss.base.entity.tableCharging.setting.BaseTableChargingSetting;
import top.kx.kxss.base.vo.result.member.grade.MemberGradeResultVO;
import top.kx.kxss.base.vo.result.service.activity.BaseServiceActivityResultVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ApiModel(value = "CalcTablePriceContext", description = "计算台桌信息参数")
public class CalcTablePriceContext extends FlowCommonContext {


    private List<PosCashTable> cashTableList;

    private PosCash posCash;

    private Map<Long, List<BaseTableChargingSetting>> chargingSettingMap;

    private Map<String, Integer> detailStopDurationMap;

    private Map<String, BaseTableCharging> tableChargingMap;

    private Map<Long, BaseTableInfo> tableInfoMap;

    private Map<String, BaseTableChargingSettingGrade> settingGradeMap;


    @ApiModelProperty(value = "是否停止计时", hidden = true)
    private Boolean isStop;

    @ApiModelProperty(value = "计时中台桌信息", hidden = true)
    private List<PosCashTable> timingTableList;

    @ApiModelProperty(value = "停止中台桌信息", hidden = true)
    private List<PosCashTable> stopTableList;

    @ApiModelProperty(value = "消费前数据信息", hidden = true)
    private List<PosCashTable> consumeBeforeTableList;

    @ApiModelProperty(value = "合并的台桌信息", hidden = true)
    private List<PosCashTable> mergeTableList;

    @ApiModelProperty(value = "拆单的台桌信息", hidden = true)
    private List<PosCashTable> spilitTableList;

    @ApiModelProperty(value = "结果台桌信息", hidden = true)
    private List<PosCashTable> resultTableList;

    @ApiModelProperty(value = "台费删除明细")
    private List<Long> tableRemoveList;

    @ApiModelProperty(value = "总时长")
    private Integer sumDuration;

    @ApiModelProperty(value = "会员等级")
    private Long gradeId;

    @ApiModelProperty(value = "是否使用会员价")
    private Boolean isMemberPrice;

    @ApiModelProperty(value = "服务活动信息")
    private Map<Long, BaseServiceActivityResultVO> serviceActivityMap;
    /**
     * 会员等级信息
     */
    @ApiModelProperty(value = "会员等级信息", hidden = true)
    private MemberGradeResultVO memberGradeResultVO;
}
