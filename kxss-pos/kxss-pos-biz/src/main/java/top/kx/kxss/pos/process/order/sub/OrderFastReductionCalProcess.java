package top.kx.kxss.pos.process.order.sub;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.base.entity.discount.BaseDiscountTemplate;
import top.kx.kxss.base.service.discount.BaseDiscountTemplateService;
import top.kx.kxss.model.enumeration.app.CashTableStatusEnum;
import top.kx.kxss.model.enumeration.pos.DiscountTypeEnum;
import top.kx.kxss.model.enumeration.pos.ExtTypeEnum;
import top.kx.kxss.pos.bean.PriceCalcStepVO;
import top.kx.kxss.pos.slot.DetailCalcContext;
import top.kx.kxss.pos.slot.PriceCalcContext;
import top.kx.kxss.pos.vo.OrderRemarksResultVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 快捷优惠-减免计算
 *
 * <AUTHOR>
 */
@Component("orderFastReductionCalProcess")
@Slf4j
public class OrderFastReductionCalProcess {
    @Autowired
    private BaseDiscountTemplateService discountTemplateService;
    public void process(PriceCalcContext context, DetailCalcContext detailContext) throws Exception {
        context.setContextUtil(context);
        PosCash posCash = context.getPosCash();
        //计算商品中不参与折扣
        // 减免比例
        BigDecimal discountAmount = BigDecimal.ZERO;
        BigDecimal roundAmount = posCash.getDiscount();
        //台桌
        if (CollUtil.isNotEmpty(detailContext.getTableList())) {
            List<PosCashTable> cashTableList = detailContext.getTableList().stream()
                    .filter(v -> !ObjectUtil.equal(v.getStatus(), CashTableStatusEnum.REFUND.getCode()))
                    .collect(Collectors.toList());
            for (PosCashTable posCashTable : cashTableList) {
                if (posCashTable.getAmount().compareTo(posCashTable.getPaid()) <= 0) {
                    continue;
                }
                if (posCashTable.getIsDiscount() != null && !posCashTable.getIsDiscount()) {
                    continue;
                }
                if (ObjectUtil.equal(posCashTable.getIsExcludeDiscount(), true)) {
                    continue;
                }
                if (ObjectUtil.isNull(posCashTable.getDiscountTemplateId())
                        || !ObjectUtil.equal(posCashTable.getDiscountTemplateId(), 0L)) {
                    continue;
                }
                if (roundAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                BigDecimal amount = posCashTable.getAmount().subtract(posCashTable.getAssessedAmount()).setScale(2, RoundingMode.HALF_UP);
                if (amount.compareTo(roundAmount) > 0) {
                    posCashTable.setAssessedAmount(posCashTable.getAssessedAmount()
                            .add(roundAmount));
                    discountAmount = discountAmount.add(roundAmount);
                    roundAmount = BigDecimal.ZERO;
                } else {
                    posCashTable.setAssessedAmount(posCashTable.getAssessedAmount()
                            .add(amount));
                    discountAmount = discountAmount.add(amount);
                    roundAmount = roundAmount.subtract(amount);
                }
            }
        }
        //商品
        if (CollUtil.isNotEmpty(detailContext.getProductList())) {
            for (PosCashProduct posCashProduct : detailContext.getProductList()) {
                if (posCashProduct.getAmount().compareTo(posCashProduct.getPaid()) <= 0) {
                    continue;
                }
                //不参与折扣
                if (posCashProduct.getIsDiscount() == null || !posCashProduct.getIsDiscount()) {
                    continue;
                }
                if (ObjectUtil.equal(posCashProduct.getIsExcludeDiscount(), true)) {
                    continue;
                }
                if (ObjectUtil.isNull(posCashProduct.getDiscountTemplateId())
                        || !ObjectUtil.equal(posCashProduct.getDiscountTemplateId(), 0L)) {
                    continue;
                }
                if (roundAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                BigDecimal amount = posCashProduct.getAmount().subtract(posCashProduct.getAssessedAmount()).setScale(2, RoundingMode.HALF_UP);
                if (amount.compareTo(roundAmount) > 0) {
                    posCashProduct.setAssessedAmount(posCashProduct.getAssessedAmount()
                            .add(roundAmount));
                    discountAmount = discountAmount.add(roundAmount);
                    roundAmount = BigDecimal.ZERO;
                } else {
                    posCashProduct.setAssessedAmount(posCashProduct.getAssessedAmount()
                            .add(amount));
                    discountAmount = discountAmount.add(amount);
                    roundAmount = roundAmount.subtract(amount);
                }

            }
        }
        //服务
        if (CollUtil.isNotEmpty(detailContext.getServiceList())) {
            List<PosCashService> cashServiceList = detailContext.getServiceList().stream()
                    .filter(v -> !ObjectUtil.equal(v.getStatus(), CashTableStatusEnum.REFUND.getCode()))
                    .collect(Collectors.toList());
            for (PosCashService posCashService : cashServiceList) {
                if (posCashService.getAmount().compareTo(posCashService.getPaid()) <= 0) {
                    continue;
                }
                //不参与折扣
                if (posCashService.getIsDiscount() == null || !posCashService.getIsDiscount()) {
                    continue;
                }
                if (ObjectUtil.equal(posCashService.getIsExcludeDiscount(), true)) {
                    continue;
                }
                if (ObjectUtil.isNull(posCashService.getDiscountTemplateId())
                        || !ObjectUtil.equal(posCashService.getDiscountTemplateId(), 0L)) {
                    continue;
                }
                if (roundAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    continue;
                }
                BigDecimal amount = posCashService.getAmount().subtract(posCashService.getAssessedAmount()).setScale(2, RoundingMode.HALF_UP);
                if (amount.compareTo(roundAmount) > 0) {
                    posCashService.setAssessedAmount(posCashService.getAssessedAmount()
                            .add(roundAmount));
                    discountAmount = discountAmount.add(roundAmount);
                    roundAmount = BigDecimal.ZERO;
                } else {
                    roundAmount = roundAmount.subtract(amount);
                    posCashService.setAssessedAmount(posCashService.getAssessedAmount()
                            .add(amount));
                    discountAmount = discountAmount.add(amount);
                    roundAmount = roundAmount.subtract(amount);
                }
            }
        }

        //加入到价格步骤中
        //获取原始备注
        OrderRemarksResultVO remarksResultVO = null;
        if (StrUtil.isNotBlank(posCash.getRemarks())) {
            remarksResultVO = JSON.parseObject(posCash.getRemarks(), OrderRemarksResultVO.class);
        }
        if (discountAmount.compareTo(BigDecimal.ZERO) != 0) {
            BaseDiscountTemplate byId = discountTemplateService.findById(posCash.getDiscountTemplateId());
            context.getCashDetailResultVO().setDiscountTemplateId(byId.getId());
            BigDecimal prePrice = context.getLastestPriceStep().getCurrPrice();
            BigDecimal currPrice = prePrice.subtract(discountAmount);
            context.addPriceCalcStep(PriceCalcStepVO.builder()
                    .extId(posCash.getId()).currPrice(currPrice)
                    .extType(ExtTypeEnum.ORDER.getCode())
                    .isGiftPay(byId == null || byId.getIsGiftPay() == null || byId.getIsGiftPay())
                    .prePrice(prePrice).priceType(DiscountTypeEnum.FAST_DISCOUNT)
                    .priceChange(currPrice.subtract(prePrice))
                    .stepDesc(remarksResultVO != null
                            ? remarksResultVO.getDiscountRemarks()
                            : DiscountTypeEnum.FAST_DISCOUNT.getDesc())
                    .build());
        }
    }

}
