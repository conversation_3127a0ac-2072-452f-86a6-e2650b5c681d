package top.kx.kxss.pos.service.shiftHandover.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.manager.cash.PosCashManager;
import top.kx.kxss.app.mapper.cash.PosCashMapper;
import top.kx.kxss.app.vo.result.cash.AmountResultVO;
import top.kx.kxss.base.entity.biz.BaseBizLog;
import top.kx.kxss.base.entity.payment.BasePaymentType;
import top.kx.kxss.base.entity.shiftHandover.BaseShiftHandover;
import top.kx.kxss.base.manager.shiftHandover.BaseShiftHandoverManager;
import top.kx.kxss.base.service.payment.BasePaymentTypeService;
import top.kx.kxss.base.service.shiftHandover.BaseShiftHandoverService;
import top.kx.kxss.base.vo.query.shiftHandover.BaseShiftHandoverPageQuery;
import top.kx.kxss.base.vo.result.product.HandoverStockResultVO;
import top.kx.kxss.base.vo.result.shiftHandover.BaseShiftHandoverResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.model.enumeration.base.*;
import top.kx.kxss.model.enumeration.pos.ShiftHandoverStatusEnum;
import top.kx.kxss.model.enumeration.system.PrintTemplateTypeEnum;
import top.kx.kxss.pos.entity.shiftHandover.BaseShiftHandoverDetails;
import top.kx.kxss.pos.entity.shiftHandover.BaseShiftHandoverStatistics;
import top.kx.kxss.pos.service.order.PrintOrderService;
import top.kx.kxss.pos.service.shiftHandover.BaseShiftHandoverDetailsService;
import top.kx.kxss.pos.service.shiftHandover.BaseShiftHandoverStatisticsService;
import top.kx.kxss.pos.service.shiftHandover.ShiftHandoverService;
import top.kx.kxss.pos.vo.CommonNameValueResultVO;
import top.kx.kxss.pos.vo.print.PrintVO;
import top.kx.kxss.pos.vo.result.shiftHandover.BaseShiftHandoverDetailsResultVO;
import top.kx.kxss.pos.vo.result.shiftHandover.BaseShiftHandoverStatisticsResultVO;
import top.kx.kxss.pos.vo.result.shiftHandover.ShiftHandoverResultVO;
import top.kx.kxss.pos.vo.shiftHandover.PaymentDetailVO;
import top.kx.kxss.pos.vo.shiftHandover.StatisticsVO;
import top.kx.kxss.wxapp.api.custom.CustomApi;
import top.kx.kxss.wxapp.vo.query.common.OrgIdListQuery;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.ColumnVO;
import top.kx.kxss.wxapp.vo.result.statistics.SensitiveVO;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static top.kx.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT;

/**
 * <p>
 * 业务实现类
 * 交班信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-10-20 16:08:17
 * @create [2023-10-20 16:08:17] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
public class ShiftHandoverServiceImpl implements ShiftHandoverService {
    @Autowired
    private BaseShiftHandoverService baseShiftHandoverService;
    @Autowired
    private BasePaymentTypeService basePaymentTypeService;
    @Autowired
    private BaseShiftHandoverManager baseShiftHandoverManager;
    @Autowired
    private PosCashMapper posCashMapper;
    @Autowired
    private PosCashManager posCashManager;
    @Autowired
    private EchoService echoService;
    @Autowired
    private CustomApi customersApi;
    @Autowired
    private PrintOrderService printOrderService;
    @Autowired
    private BaseShiftHandoverDetailsService baseShiftHandoverDetailsService;
    @Autowired
    private BaseShiftHandoverStatisticsService baseShiftHandoverStatisticsService;


    @Override
    public BaseShiftHandoverResultVO info() {
        BaseShiftHandover baseShiftHandover = baseShiftHandoverService.getSuperManager().getOne(Wraps.<BaseShiftHandover>lbQ()
                .eq(BaseShiftHandover::getStatus, ShiftHandoverStatusEnum.NO_COMPLETE.getCode())
                .eq(BaseShiftHandover::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(BaseShiftHandover::getEmployeeId, ContextUtil.getEmployeeId())
                .orderByDesc(BaseShiftHandover::getCreatedTime).last("limit 1"));
        if (ObjectUtil.isNull(baseShiftHandover)) {
            return null;
        }
        baseShiftHandover.setEmployeeId(ContextUtil.getEmployeeId());
        baseShiftHandover.setEndTime(LocalDateTime.now());
        BaseShiftHandoverResultVO baseShiftHandoverResultVO = BeanUtil.copyProperties(baseShiftHandover, BaseShiftHandoverResultVO.class);
        echoService.action(baseShiftHandoverResultVO);
        return baseShiftHandoverResultVO;
    }

    @Override
    public List<PaymentDetailVO> detail(Long shiftHandoverId) {
        BaseShiftHandover baseShiftHandover = baseShiftHandoverService.getSuperManager()
                .getOne(Wraps.<BaseShiftHandover>lbQ()
                        .eq(BaseShiftHandover::getStatus, ShiftHandoverStatusEnum.NO_COMPLETE.getCode())
                        .eq(BaseShiftHandover::getId, shiftHandoverId)
                        .eq(BaseShiftHandover::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                        .eq(BaseShiftHandover::getEmployeeId, ContextUtil.getEmployeeId())
                        .orderByDesc(BaseShiftHandover::getCreatedTime).last("limit 1"));
        ArgumentAssert.notNull(baseShiftHandover, "您还未开班，请先开班");
//        Long tenantId = ContextUtil.getTenantId();
        // 强制刷新五A 的交班人
//        if (ObjectUtil.equal(tenantId, 554208621236562947L)) {
//            posCashMapper.forceRefreshCompleteEmp(ContextUtil.getEmployeeId(), baseShiftHandover.getStartTime(), ContextUtil.getCurrentCompanyId());
//        } else {
//            //posCashMapper.refreshCompleteEmp(ContextUtil.getEmployeeId(), baseShiftHandover.getStartTime(), ContextUtil.getCurrentCompanyId());
//        }
        String startTime = DateUtils.format(baseShiftHandover.getStartTime(), DEFAULT_DATE_TIME_FORMAT);
        String endTime = DateUtils.format(LocalDateTime.now(), DEFAULT_DATE_TIME_FORMAT);
        List<PaymentDetailVO> detailVOList = Lists.newArrayList();
        QueryWrapper<PosCash> wrapper = new QueryWrapper<>();
        wrapper.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(), PosCashBillStateEnum.PART_REFUND.getCode()))
                .notIn("p.bill_type",
                        Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                                , PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .eq("p.delete_flag", 0)
                .between("p.complete_time", startTime, endTime)
                .eq("p.org_id", ContextUtil.getCurrentCompanyId())
                .eq("t.status", PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                .eq("t.delete_flag", 0)
                .eq("p.complete_emp", ContextUtil.getEmployeeId())
                .notIn("bpt.biz_type", "1", "8");
        List<AmountResultVO> amountResultVOList = posCashMapper.selectByPayType(wrapper);
        //本班次收款, 不包含余额和储值卡支付
        PaymentDetailVO paymentDetailVO = PaymentDetailVO.builder()
                .color("#ff0000").name("本班次收款(元)").value(BigDecimal.ZERO.toPlainString()).build();
        if (CollUtil.isNotEmpty(amountResultVOList)) {
            BigDecimal bigDecimal = amountResultVOList.stream().map(AmountResultVO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
            paymentDetailVO.setValue(bigDecimal.toPlainString());
        }
        detailVOList.add(paymentDetailVO);
        wrapper = new QueryWrapper<>();
        wrapper.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(), PosCashBillStateEnum.PART_REFUND.getCode()))
                .notIn("p.bill_type",
                        Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                                , PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .eq("p.delete_flag", 0)
                .between("p.complete_time", startTime, endTime)
                .eq("t.status", PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                .eq("t.delete_flag", 0)
                .eq("p.complete_emp", ContextUtil.getEmployeeId())
                .eq("p.org_id", ContextUtil.getCurrentCompanyId());
        amountResultVOList = posCashMapper.selectByPayType(wrapper);
        BigDecimal surplusCash = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(amountResultVOList)) {
            List<String> payTypeId = basePaymentTypeService.getSuperManager().listObjs(Wraps.<BasePaymentType>lbQ()
                    .select(BasePaymentType::getId)
                    .eq(BasePaymentType::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                    .eq(BasePaymentType::getBizType, PaymentBizTypeEnum.CASH.getCode()), Convert::toStr);
            if (CollUtil.isNotEmpty(payTypeId)) {
                surplusCash = amountResultVOList.stream().filter(v -> payTypeId.contains(v.getField()))
                        .map(AmountResultVO::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            //根据支付类型进行分组
            Map<String, AmountResultVO> voMap = amountResultVOList.stream().collect(
                    Collectors.toMap(AmountResultVO::getField, k -> k));
            for (AmountResultVO value : voMap.values()) {
                value.setField(value.getName());
            }
            detailVOList.addAll(voMap.keySet().stream().map(v -> PaymentDetailVO.builder()
                    .name(voMap.get(v).getName()).value(voMap.get(v).getAmount()
                            .setScale(2, RoundingMode.HALF_UP).toPlainString())
                    .build()).collect(Collectors.toList()));
        }
        //备用金
        detailVOList.add(PaymentDetailVO.builder().name("备用金(元)").value(baseShiftHandover.getPettyCash()
                .setScale(2, RoundingMode.HALF_UP).toPlainString()).build());
        //应有现金
        detailVOList.add(PaymentDetailVO.builder().name("应有现金(元)").value(baseShiftHandover.getPettyCash()
                .add(surplusCash).setScale(2, RoundingMode.HALF_UP).toPlainString()).color("#0000ff").build());
        return detailVOList;
    }

    @Override
    public List<StatisticsVO> statistics(Long shiftHandoverId) {
        List<StatisticsVO> returnList = Lists.newArrayList();
        BaseShiftHandover baseShiftHandover = baseShiftHandoverService
                .getOne(Wraps.<BaseShiftHandover>lbQ()
                        .eq(BaseShiftHandover::getStatus, ShiftHandoverStatusEnum.NO_COMPLETE.getCode())
                        .eq(BaseShiftHandover::getId, shiftHandoverId)
                        .eq(BaseShiftHandover::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                        .orderByDesc(BaseShiftHandover::getCreatedTime).last("limit 1"));
        ArgumentAssert.notNull(baseShiftHandover, "您还未开班，请先开班");
//        Long tenantId = ContextUtil.getTenantId();
        // 强制刷新五A 的交班人
//        if (ObjectUtil.equal(tenantId, 554208621236562947L)) {
//            posCashMapper.forceRefreshCompleteEmp(ContextUtil.getEmployeeId(), baseShiftHandover.getStartTime(), ContextUtil.getCurrentCompanyId());
//        } else {
//            // posCashMapper.refreshCompleteEmp(ContextUtil.getEmployeeId(), baseShiftHandover.getStartTime(), ContextUtil.getCurrentCompanyId());
//        }
        String startTime = DateUtils.format(baseShiftHandover.getStartTime(), DEFAULT_DATE_TIME_FORMAT);
        String endTime = DateUtils.format(LocalDateTime.now(), DEFAULT_DATE_TIME_FORMAT);
        Map<String, StatisticsVO> map = MapUtil.newHashMap();
        //台桌统计
        StatisticsVO statisticsVO = StatisticsVO.builder().name("台桌开台统计").type(ShiftHandoverDetailsTypeEnum.OPEN_TABLE.getCode()).total("0").build();
        QueryWrapper<PosCash> wrapper = new QueryWrapper<>();
        wrapper.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(), PosCashBillStateEnum.PART_REFUND.getCode()))
                .notIn("p.bill_type",
                        Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                                , PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .eq("p.delete_flag", 0)
                .eq("t.status", PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                .eq("t.delete_flag", 0)
                .eq("p.type_", PosCashTypeEnum.START_TABLE.getCode())
                .between("p.complete_time", startTime, endTime)
                .eq("p.complete_emp", ContextUtil.getEmployeeId())
                .eq("p.org_id", ContextUtil.getCurrentCompanyId());

        List<AmountResultVO> amountResultVOList = posCashMapper.selectByPayType(wrapper);
        if (CollUtil.isNotEmpty(amountResultVOList)) {
            //根据支付类型进行分组
            Map<String, AmountResultVO> voMap = amountResultVOList.stream().collect(
                    Collectors.toMap(AmountResultVO::getField, k -> k));
            for (AmountResultVO value : voMap.values()) {
                value.setField(value.getName());
            }
            statisticsVO.setTotal(voMap.values().stream().map(AmountResultVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP).toPlainString());
            statisticsVO.setDataList(voMap.keySet().stream().map(v -> CommonNameValueResultVO.builder()
                    .name(voMap.get(v).getName()).value(voMap.get(v).getAmount()
                            .setScale(2, RoundingMode.HALF_UP).toPlainString())
                    .build()).collect(Collectors.toList()));
        }
        returnList.add(statisticsVO);
        //购物统计
        wrapper = new QueryWrapper<>();
        statisticsVO = StatisticsVO.builder().name("直接购物统计").type(ShiftHandoverDetailsTypeEnum.SHOPPING.getCode()).total("0").build();
        wrapper.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(), PosCashBillStateEnum.PART_REFUND.getCode()))
                .notIn("p.bill_type",
                        Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                                , PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .eq("p.delete_flag", 0)
                .eq("p.type_", PosCashTypeEnum.SHOPPING.getCode())
                .eq("t.status", PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                .eq("t.delete_flag", 0)
                .between("p.complete_time", startTime, endTime)
                .eq("p.complete_emp", ContextUtil.getEmployeeId())
                .eq("p.org_id", ContextUtil.getCurrentCompanyId());

        amountResultVOList = posCashMapper.selectByPayType(wrapper);
        if (CollUtil.isNotEmpty(amountResultVOList)) {
            //根据支付类型进行分组
            Map<String, AmountResultVO> voMap = amountResultVOList.stream().collect(
                    Collectors.toMap(AmountResultVO::getField, k -> k));
            for (AmountResultVO value : voMap.values()) {
                value.setField(value.getName());
            }
            statisticsVO.setTotal(voMap.values().stream().map(AmountResultVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP).toPlainString());
            statisticsVO.setDataList(voMap.keySet().stream().map(v -> CommonNameValueResultVO.builder()
                    .name(voMap.get(v).getName()).value(voMap.get(v).getAmount()
                            .setScale(2, RoundingMode.HALF_UP).toPlainString())
                    .build()).collect(Collectors.toList()));
        }
        returnList.add(statisticsVO);
        //充值统计
        statisticsVO = StatisticsVO.builder().name("会员充值").type(ShiftHandoverDetailsTypeEnum.MEMBER_RECHARGE.getCode()).total("0").build();
        wrapper = new QueryWrapper<>();
        wrapper.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(), PosCashBillStateEnum.REFUNDED.getCode()))
                .notIn("p.bill_type",
                        Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                                , PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .eq("p.delete_flag", 0)
                .eq("p.type_", PosCashTypeEnum.RECHARGE.getCode())
                .eq("t.status", PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                .eq("t.delete_flag", 0)
                .between("p.complete_time", startTime, endTime)
                .eq("p.complete_emp", ContextUtil.getEmployeeId())
                .eq("p.org_id", ContextUtil.getCurrentCompanyId());
        amountResultVOList = posCashMapper.selectByPayType(wrapper);
        if (CollUtil.isNotEmpty(amountResultVOList)) {
            //根据支付类型进行分组
            Map<String, AmountResultVO> voMap = amountResultVOList.stream().collect(
                    Collectors.toMap(AmountResultVO::getField, k -> k));
            for (AmountResultVO value : voMap.values()) {
                value.setField(value.getName());
            }
            statisticsVO.setTotal(voMap.values().stream().map(AmountResultVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP).toPlainString());
            List<CommonNameValueResultVO> dataList = voMap.keySet().stream().map(v -> CommonNameValueResultVO.builder()
                    .name(voMap.get(v).getName()).value(voMap.get(v).getPayment()
                            .setScale(2, RoundingMode.HALF_UP).toPlainString())
                    .build()).collect(Collectors.toList());

            List<CommonNameValueResultVO> refundList = voMap.keySet().stream().filter(c -> ObjectUtil.isNotNull(voMap.get(c).getRefundAmount()) && voMap.get(c).getRefundAmount().compareTo(BigDecimal.ZERO) != 0).map(v -> CommonNameValueResultVO.builder()
                    .name(voMap.get(v).getName()).value(BigDecimal.ZERO.subtract(voMap.get(v).getRefundAmount()
                            .setScale(2, RoundingMode.HALF_UP)).toPlainString())
                    .build()).collect(Collectors.toList());
            dataList.addAll(refundList);
            statisticsVO.setDataList(dataList);
        }
        returnList.add(statisticsVO);
        //退款合计
        statisticsVO = StatisticsVO.builder().name("消费退款合计").type(ShiftHandoverDetailsTypeEnum.REFUND.getCode()).total("0").build();
        wrapper = new QueryWrapper<>();
        wrapper.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.PART_REFUND.getCode(), PosCashBillStateEnum.REFUNDED.getCode()))
                .eq("p.bill_type", PosCashBillTypeEnum.REGULAR_SINGLE.getCode())
                .eq("p.delete_flag", 0)
                .ne("p.type_", PosCashTypeEnum.RECHARGE.getCode())
                .between("p.complete_time", startTime, endTime)
                .eq("p.complete_emp", ContextUtil.getEmployeeId())
                .eq("t.status", PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                .eq("t.delete_flag", 0)
                .eq("p.org_id", ContextUtil.getCurrentCompanyId());

        amountResultVOList = posCashMapper.selectByPayType(wrapper);
        if (CollUtil.isNotEmpty(amountResultVOList)) {
            //根据支付类型进行分组
            Map<String, AmountResultVO> voMap = amountResultVOList.stream().collect(
                    Collectors.toMap(AmountResultVO::getField, k -> k));
            for (AmountResultVO value : voMap.values()) {
                value.setField(value.getName());
            }
            statisticsVO.setTotal(BigDecimal.ZERO.subtract(voMap.values().stream().map(AmountResultVO::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                    .setScale(2, RoundingMode.HALF_UP)).toPlainString());

            statisticsVO.setDataList(voMap.keySet().stream().map(v -> CommonNameValueResultVO.builder()
                    .name(voMap.get(v).getName()).value(BigDecimal.ZERO.subtract(voMap.get(v).getRefundAmount()
                            .setScale(2, RoundingMode.HALF_UP)).toPlainString())
                    .build()).collect(Collectors.toList()));
        }
        returnList.add(statisticsVO);
        //订单合计
        statisticsVO = StatisticsVO.builder().name("订单合计").type(ShiftHandoverDetailsTypeEnum.ORDERS.getCode()).desc("笔").total("0").build();
        long unCompleteCount = posCashManager.count(Wraps.<PosCash>lbQ().notIn(PosCash::getBillType, Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                        , PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .ne(PosCash::getBillState, PosCashBillStateEnum.COMPLETE.getCode())
                .between(PosCash::getCreatedTime, startTime, endTime)
                .eq(PosCash::getCompleteEmp, ContextUtil.getEmployeeId())
                .eq(PosCash::getCreatedOrgId, ContextUtil.getCurrentCompanyId()));
        long completeCount = posCashManager.count(Wraps.<PosCash>lbQ().notIn(PosCash::getBillType, Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                        , PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .eq(PosCash::getBillState, PosCashBillStateEnum.COMPLETE.getCode())
                .between(PosCash::getCompleteTime, startTime, endTime)
                .eq(PosCash::getCompleteEmp, ContextUtil.getEmployeeId())
                .eq(PosCash::getCreatedOrgId, ContextUtil.getCurrentCompanyId()));
        statisticsVO.setDataList(Arrays.asList(CommonNameValueResultVO.builder()
                        .name("未结账订单").value("" + unCompleteCount)
                        .build(),
                CommonNameValueResultVO.builder()
                        .name("已完成订单")
                        .value("" + completeCount).build()));
        statisticsVO.setTotal((unCompleteCount + completeCount) + "");
        returnList.add(statisticsVO);
        //敏感操作
        statisticsVO = StatisticsVO.builder().name("敏感操作").type(ShiftHandoverDetailsTypeEnum.SENSITIVE.getCode()).desc("次").total("0").build();
        List<String> stringList = Arrays.stream(NoticeTypeEnum.values()).sequential().map(NoticeTypeEnum::getCode).collect(Collectors.toList());
        stringList.removeIf(v -> StringUtils.equalsAny(v,
                NoticeTypeEnum.UPDATE_MEMBER_GRADE.getCode(),
                NoticeTypeEnum.UPDATE_MEMBER_MOBILE.getCode(),
                NoticeTypeEnum.MEMBER_CHANGE.getCode()));

        QueryWrap<BaseBizLog> wrap = new QueryWrap();
        wrap.in("b.biz_module", stringList);
        wrap.eq("b.delete_flag", 0);
        wrap.eq("p.delete_flag", 0);
        wrap.eq("b.created_org_id", ContextUtil.getCurrentCompanyId());
        wrap.between("b.opearte_time", startTime, endTime);
        wrap.eq("p.complete_emp", ContextUtil.getEmployeeId());
        List<SensitiveVO> valueVOList = posCashMapper.sensitiveList(wrap);
        if (CollUtil.isEmpty(valueVOList)) {
            valueVOList = Lists.newArrayList();
        }
        wrap = new QueryWrap();
        wrap.in("b.biz_module", Arrays.asList(NoticeTypeEnum.UPDATE_MEMBER_GRADE.getCode(),
                NoticeTypeEnum.UPDATE_MEMBER_MOBILE.getCode()
        ));
        wrap.eq("b.delete_flag", 0);
        wrap.eq("p.delete_flag", 0);
        wrap.eq("b.created_org_id", ContextUtil.getCurrentCompanyId());
        wrap.between("b.opearte_time", startTime,
                endTime);
        wrap.eq("b.employee_id", ContextUtil.getEmployeeId());
        List<SensitiveVO> valueVOList1 = posCashMapper.sensitiveMemberList(wrap);
        if (CollUtil.isNotEmpty(valueVOList1)) {
            valueVOList.addAll(valueVOList1);
        }
        wrap = new QueryWrap();
        wrap.eq("b.biz_module", NoticeTypeEnum.MEMBER_CHANGE.getCode());
        wrap.eq("b.delete_flag", 0);
        wrap.eq("p.delete_flag", 0);
        wrap.eq("b.created_org_id", ContextUtil.getCurrentCompanyId());
        wrap.between("b.opearte_time", startTime,
                endTime);
        wrap.eq("p.operate_emp", ContextUtil.getEmployeeId());
        List<SensitiveVO> valueVOList2 = posCashMapper.sensitiveMemberBalanceList(wrap);
        if (CollUtil.isNotEmpty(valueVOList2)) {
            valueVOList.addAll(valueVOList2);
        }
        if (CollUtil.isNotEmpty(valueVOList)) {
            valueVOList.forEach(v -> v.setName(NoticeTypeEnum.get(v.getName()).getDesc()));
            statisticsVO.setTotal("" + valueVOList.stream().mapToLong(v -> Long.parseLong(v.getValue())).sum());
            statisticsVO.setDataList(valueVOList.stream().map(v ->
                    BeanUtil.copyProperties(v, CommonNameValueResultVO.class)
            ).collect(Collectors.toList()));
        }
        returnList.add(statisticsVO);
        return returnList;
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public BaseShiftHandoverResultVO confirm() {
        BaseShiftHandover baseShiftHandover = baseShiftHandoverService.getSuperManager().getOne(Wraps.<BaseShiftHandover>lbQ()
                .eq(BaseShiftHandover::getStatus, ShiftHandoverStatusEnum.NO_COMPLETE.getCode())
                .eq(BaseShiftHandover::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(BaseShiftHandover::getEmployeeId, ContextUtil.getEmployeeId())
                .orderByDesc(BaseShiftHandover::getCreatedTime).last("limit 1"));
        ArgumentAssert.notNull(baseShiftHandover, "您还未开班，请先开班");
//        Long tenantId = ContextUtil.getTenantId();
//        // 强制刷新五A 的交班人
//        if (ObjectUtil.equal(tenantId, 554208621236562947L)) {
//            posCashMapper.forceRefreshCompleteEmp(ContextUtil.getEmployeeId(), baseShiftHandover.getStartTime(), ContextUtil.getCurrentCompanyId());
//        } else {
//            //posCashMapper.refreshCompleteEmp(ContextUtil.getEmployeeId(), baseShiftHandover.getStartTime(), ContextUtil.getCurrentCompanyId());
//        }
        baseShiftHandover.setEmployeeId(ContextUtil.getEmployeeId());
        baseShiftHandover.setEndTime(LocalDateTime.now());
        String startTime = DateUtils.format(baseShiftHandover.getStartTime(), DEFAULT_DATE_TIME_FORMAT);
        String endTime = DateUtils.format(baseShiftHandover.getEndTime(), DEFAULT_DATE_TIME_FORMAT);
        QueryWrapper<PosCash> wrapper = new QueryWrapper<>();
        wrapper.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(), PosCashBillStateEnum.PART_REFUND.getCode()))
                .notIn("p.bill_type",
                        Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                                , PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .eq("p.delete_flag", 0)
                .between("p.complete_time", startTime, endTime)
                .eq("p.org_id", ContextUtil.getCurrentCompanyId())
                .eq("t.status", PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                .eq("t.delete_flag", 0)
                .eq("p.complete_emp", ContextUtil.getEmployeeId())
                .notIn("bpt.biz_type", "1", "8");
        //本班次收款, 不包含余额和储值卡支付
        List<AmountResultVO> amountResultVOList = posCashMapper.selectByPayType(wrapper);
        if (CollUtil.isNotEmpty(amountResultVOList)) {
            baseShiftHandover.setPayment(amountResultVOList.stream().map(AmountResultVO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
        }
        wrapper = new QueryWrapper<>();
        wrapper.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(), PosCashBillStateEnum.PART_REFUND.getCode()))
                .notIn("p.bill_type",
                        Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                                , PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .eq("p.delete_flag", 0)
                .between("p.complete_time", startTime, endTime)
                .eq("p.org_id", ContextUtil.getCurrentCompanyId())
                .eq("t.status", PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                .eq("t.delete_flag", 0)
                .eq("p.complete_emp", ContextUtil.getEmployeeId())
                .eq("bpt.biz_type", "4");
        amountResultVOList = posCashMapper.selectByPayType(wrapper);
        if (CollUtil.isNotEmpty(amountResultVOList)) {
            baseShiftHandover.setSurplusCash(baseShiftHandover.getPettyCash().add(amountResultVOList.stream().map(AmountResultVO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add)).setScale(2, RoundingMode.HALF_UP));
        }
        baseShiftHandover.setStatus(ShiftHandoverStatusEnum.COMPLETE.getCode());
        baseShiftHandover.setEndSn(ContextUtil.getSn());

        // 交班详情
        List<PaymentDetailVO> detailVOList = detail(baseShiftHandover.getId());
        // 交班统计
        List<StatisticsVO> statistics = statistics(baseShiftHandover.getId());

        baseShiftHandoverManager.updateById(baseShiftHandover);

        // 添加交班记录
        List<BaseShiftHandoverStatistics> statisticsSaveList = new ArrayList<>();
        PaymentDetailVO paymentDetailVO = detailVOList.get(0);

        statisticsSaveList.add(BaseShiftHandoverStatistics.builder()
                .handoverId(baseShiftHandover.getId())
                .name(paymentDetailVO.getName())
                .type(ShiftHandoverDetailsTypeEnum.PAY_IN.getCode())
                .value(paymentDetailVO.getValue())
                .unit("")
                .createdOrgId(ContextUtil.getCurrentCompanyId())
                .build());
        List<BaseShiftHandoverDetails> detailsList = new ArrayList<>();
        // 记录
        detailVOList.remove(0);
        List<BaseShiftHandoverDetails> details = BeanPlusUtil.toBeanList(detailVOList, BaseShiftHandoverDetails.class);
        details.forEach(d -> {
            d.setHandoverId(baseShiftHandover.getId());
            d.setType(ShiftHandoverDetailsTypeEnum.PAY_IN.getCode());
            d.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
            if (new BigDecimal(d.getValue()).compareTo(BigDecimal.ZERO) >= 0) {
                d.setPositive("0");
            } else {
                d.setPositive("1");
            }
        });
        detailsList.addAll(details);

        // 交班详情统计
        for (StatisticsVO statistic : statistics) {
            statisticsSaveList.add(BaseShiftHandoverStatistics.builder()
                    .handoverId(baseShiftHandover.getId())
                    .name(statistic.getName())
                    .type(statistic.getType())
                    .value(statistic.getTotal())
                    .unit(statistic.getDesc())
                    .createdOrgId(ContextUtil.getCurrentCompanyId())
                    .build());

            if (CollUtil.isNotEmpty(statistic.getDataList())) {
                List<BaseShiftHandoverDetails> handoverDetails = BeanPlusUtil.toBeanList(statistic.getDataList(), BaseShiftHandoverDetails.class);
                handoverDetails.forEach(d -> {
                    d.setHandoverId(baseShiftHandover.getId());
                    d.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                    d.setType(statistic.getType());
                    if (new BigDecimal(d.getValue()).compareTo(BigDecimal.ZERO) >= 0) {
                        d.setPositive("0");
                    } else {
                        d.setPositive("1");
                    }
                });
                detailsList.addAll(handoverDetails);
            }
        }
        if (CollUtil.isNotEmpty(statisticsSaveList)) {
            baseShiftHandoverStatisticsService.saveBatch(statisticsSaveList);
        }
        if (CollUtil.isNotEmpty(detailsList)) {
            baseShiftHandoverDetailsService.saveBatch(detailsList);
        }
        try {
            handoverPrint(baseShiftHandover.getId());
        } catch (Exception e) {
            log.error("交班打印失败", e);
        }
        return BeanUtil.copyProperties(baseShiftHandover, BaseShiftHandoverResultVO.class);
    }

    @Override
    public BaseShiftHandoverResultVO save(BigDecimal pettyCash) {
        BaseShiftHandover baseShiftHandover = baseShiftHandoverService.getOne(Wraps.<BaseShiftHandover>lbQ()
                .eq(BaseShiftHandover::getStatus, ShiftHandoverStatusEnum.NO_COMPLETE.getCode())
                .eq(BaseShiftHandover::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(BaseShiftHandover::getEmployeeId, ContextUtil.getEmployeeId())
                .orderByDesc(BaseShiftHandover::getCreatedTime).last("limit 1"));
        if (baseShiftHandover != null) {
            BaseShiftHandoverResultVO baseShiftHandoverResultVO = BeanUtil.copyProperties(baseShiftHandover, BaseShiftHandoverResultVO.class);
            echoService.action(baseShiftHandoverResultVO);
            return baseShiftHandoverResultVO;
        }
        baseShiftHandover = BaseShiftHandover.builder()
                .createdOrgId(ContextUtil.getCurrentCompanyId())
                .startTime(LocalDateTime.now()).payment(BigDecimal.ZERO)
                .pettyCash(pettyCash).surplusCash(BigDecimal.ZERO)
                .status(ShiftHandoverStatusEnum.NO_COMPLETE.getCode())
                .employeeId(ContextUtil.getEmployeeId())
                .startSn(ContextUtil.getSn())
                .endSn(null)
                .build();
        baseShiftHandoverManager.save(baseShiftHandover);
        BaseShiftHandoverResultVO baseShiftHandoverResultVO = BeanUtil.copyProperties(baseShiftHandover, BaseShiftHandoverResultVO.class);
        echoService.action(baseShiftHandoverResultVO);
        return baseShiftHandoverResultVO;
    }

    @Override
    public IPage<BaseShiftHandoverResultVO> page(PageParams<BaseShiftHandoverPageQuery> params) {
        params.setSort("");
        params.setSort("");
        IPage<BaseShiftHandover> page = params.buildPage(BaseShiftHandover.class);
        BaseShiftHandoverPageQuery model = params.getModel();
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            R<DataOverviewQuery> storeTime = customersApi.getStoreTime(model);
            DataOverviewQuery storeTimeData = storeTime.getData();
            model.setStartDate(storeTimeData.getStartDate());
            model.setEndDate(storeTimeData.getEndDate());
        }
        LbQueryWrap<BaseShiftHandover> wrap = Wraps.lbQ();
        wrap.eq(SuperEntity::getDeleteFlag, 0);
        wrap.in(CollUtil.isNotEmpty(model.getOrgIdList()), BaseShiftHandover::getCreatedOrgId, model.getOrgIdList());
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            wrap.and(wrapper -> wrapper.between(StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate()),
                            BaseShiftHandover::getEndTime, model.getStartDate(), model.getEndDate())
                    .or().isNull(BaseShiftHandover::getEndTime));
        }
        if (StringUtils.isNotBlank(model.getKeyword())) {
            wrap.inSql(BaseShiftHandover::getEmployeeId, "select id from base_employee " +
                    "where delete_flag = 0 and real_name like '%" + model.getKeyword() + "%'");
        }
        wrap.orderByAsc(BaseShiftHandover::getStatus).orderByDesc(BaseShiftHandover::getEndTime);
        IPage<BaseShiftHandover> handoverIPage = baseShiftHandoverService.page(page, wrap);
        IPage<BaseShiftHandoverResultVO> beanPage = BeanPlusUtil.toBeanPage(handoverIPage, BaseShiftHandoverResultVO.class);
//        for (BaseShiftHandoverResultVO record : beanPage.getRecords()) {
//            // 反结账操作记录
//            CounterCheckoutResultVO counterCheckoutResultVO = posCashMapper.selectCounterCheckout(Wraps.<PosCash>lbQ()
//                    .eq(PosCash::getDeleteFlag, 0)
//                    .isNotNull(PosCash::getCounterCheckoutTime)
//                    .ne(PosCash::getCompleteEmp, ContextUtil.getEmployeeId())
//                    .between(PosCash::getCompleteTime, record.getStartTime(), record.getEndTime())
//                    .eq(PosCash::getOrgId, ContextUtil.getCurrentCompanyId()));
//            if (ObjectUtil.isNotNull(counterCheckoutResultVO)) {
//                record.setExceptionDesc(String.format("被反结账笔数：%s笔；最后一次反结账时间：%s", counterCheckoutResultVO.getNum(), counterCheckoutResultVO.getCounterCheckoutTime()));
//            }
//        }

        echoService.action(beanPage);
        return beanPage;
    }

    @Override
    public Map<String, Object> detailPage(PageParams<BaseShiftHandoverPageQuery> params) {
        initOrgIdList(params.getModel());
        IPage<BaseShiftHandoverResultVO> shiftHandoverResultVOIPage = page(params);
        IPage<Map> pageList = BeanPlusUtil.toBeanPage(shiftHandoverResultVOIPage, Map.class);
        List<Map> resultVOList = Lists.newArrayList();

        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("endTime").label("交班时间").width(250).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("startTime").label("开班时间").width(250).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("employee").label("收银员").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("status").label("交班状态").width(180).emptyString("-").fixed(false).build()
        );
        List<BaseShiftHandoverResultVO> resultVOS = shiftHandoverResultVOIPage.getRecords();
        if (CollUtil.isEmpty(resultVOS)) {
            pageList.setRecords(resultVOList);
            Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
            objectMap.put("columnList", columnVOList);
            return objectMap;
        }


        List<Long> handoverIdList = resultVOS.stream().map(BaseShiftHandoverResultVO::getId).collect(Collectors.toList());
        List<BaseShiftHandoverStatistics> statisticsList = baseShiftHandoverStatisticsService.list(Wraps.<BaseShiftHandoverStatistics>lbQ().eq(BaseShiftHandoverStatistics::getDeleteFlag, 0)
                .in(BaseShiftHandoverStatistics::getHandoverId, handoverIdList));
        // 查询详情
        List<BaseShiftHandoverDetails> detailsList = baseShiftHandoverDetailsService.list(Wraps.<BaseShiftHandoverDetails>lbQ().eq(BaseShiftHandoverDetails::getDeleteFlag, 0)
                .in(BaseShiftHandoverDetails::getHandoverId, handoverIdList));

        List<String> statisticsColumnList = statisticsList.stream().map(s -> "statistics_".concat(s.getType()).concat("_").concat(s.getName())).distinct().collect(Collectors.toList());
        // 详情表头
        List<String> detailsColumnList = detailsList.stream().map(s -> "details_".concat(s.getType()).concat("_").concat(s.getPositive()).concat("_").concat(s.getName())).distinct().collect(Collectors.toList());


        Map<String, String> statisticsValueMap = statisticsList.stream()
                .collect(Collectors.toMap(
                        obj -> "statistics_".concat(obj.getType()).concat("_").concat(obj.getName()).concat("_").concat(obj.getHandoverId().toString()),
                        BaseShiftHandoverStatistics::getValue,
                        (existing, replacement) -> existing
                ));

        Map<String, String> detailsValueMap = detailsList.stream()
                .collect(Collectors.toMap(
                        obj -> "details_".concat(obj.getType()).concat("_").concat(obj.getPositive()).concat("_").concat(obj.getName()).concat("_").concat(obj.getHandoverId().toString()),
                        BaseShiftHandoverDetails::getValue,
                        (existing, replacement) -> existing
                ));

        detailPageColum(statisticsList, detailsList, statisticsColumnList, columnVOList, detailsColumnList);

        columnVOList.add(ColumnVO.builder().name("org").label("门店").width(180).emptyString("-").fixed(false).build());

        // 设置数据
        detailPageResult(resultVOS, statisticsColumnList, statisticsValueMap, detailsColumnList, detailsValueMap, resultVOList);

        pageList.setRecords(resultVOList);
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public void detailPageExport(BaseShiftHandoverPageQuery model, HttpServletResponse response) {
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && !model.getStartDate().contains(":")) {
            model.setStartDate(model.getStartDate() + " 00:00:00");
        }
        if (StringUtils.isNotBlank(model.getEndDate()) && !model.getEndDate().contains(":")) {
            model.setEndDate(model.getEndDate() + " 23:59:59");
        }
        LbQueryWrap<BaseShiftHandover> wrap = Wraps.lbQ();
        wrap.eq(SuperEntity::getDeleteFlag, 0);
        wrap.in(CollUtil.isNotEmpty(model.getOrgIdList()), BaseShiftHandover::getCreatedOrgId, model.getOrgIdList());
        wrap.between(StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate()),
                BaseShiftHandover::getEndTime, model.getStartDate(), model.getEndDate());
        if (StringUtils.isNotBlank(model.getKeyword())) {
            wrap.inSql(BaseShiftHandover::getEmployeeId, "select id from base_employee " +
                    "where delete_flag = 0 and real_name like '%" + model.getKeyword() + "%'");
        }
        wrap.orderByAsc(BaseShiftHandover::getStatus).orderByDesc(BaseShiftHandover::getEndTime);
        List<BaseShiftHandover> handoverIPage = baseShiftHandoverService.list(wrap);
        List<BaseShiftHandoverResultVO> resultVOS = BeanPlusUtil.toBeanList(handoverIPage, BaseShiftHandoverResultVO.class);
        echoService.action(resultVOS);
        List<Map> resultVOList = Lists.newArrayList();

        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("startTime").label("开班时间").width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("endTime").label("交班时间").width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("employee").label("收银员").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("status").label("交班状态").width(180).emptyString("-").fixed(false).build()
        );
        if (CollUtil.isEmpty(resultVOS)) {
            export(response, new ArrayList<>(), columnVOList, "交班记录导出");
            return;
        }

        List<Long> handoverIdList = resultVOS.stream().map(BaseShiftHandoverResultVO::getId).collect(Collectors.toList());
        List<BaseShiftHandoverStatistics> statisticsList = baseShiftHandoverStatisticsService.list(Wraps.<BaseShiftHandoverStatistics>lbQ().eq(BaseShiftHandoverStatistics::getDeleteFlag, 0)
                .in(BaseShiftHandoverStatistics::getHandoverId, handoverIdList));
        // 查询详情
        List<BaseShiftHandoverDetails> detailsList = baseShiftHandoverDetailsService.list(Wraps.<BaseShiftHandoverDetails>lbQ().eq(BaseShiftHandoverDetails::getDeleteFlag, 0)
                .in(BaseShiftHandoverDetails::getHandoverId, handoverIdList));

        List<String> statisticsColumnList = statisticsList.stream().map(s -> "statistics_".concat(s.getType()).concat("_").concat(s.getName())).distinct().collect(Collectors.toList());
        // 详情表头
        List<String> detailsColumnList = detailsList.stream().map(s -> "details_".concat(s.getType()).concat("_").concat(s.getPositive()).concat("_").concat(s.getName())).distinct().collect(Collectors.toList());


        Map<String, String> statisticsValueMap = statisticsList.stream()
                .collect(Collectors.toMap(
                        obj -> "statistics_".concat(obj.getType()).concat("_").concat(obj.getName()).concat("_").concat(obj.getHandoverId().toString()),
                        BaseShiftHandoverStatistics::getValue,
                        (existing, replacement) -> existing
                ));

        Map<String, String> detailsValueMap = detailsList.stream()
                .collect(Collectors.toMap(
                        obj -> "details_".concat(obj.getType()).concat("_").concat(obj.getPositive()).concat("_").concat(obj.getName()).concat("_").concat(obj.getHandoverId().toString()),
                        BaseShiftHandoverDetails::getValue,
                        (existing, replacement) -> existing
                ));

        detailPageColum(statisticsList, detailsList, statisticsColumnList, columnVOList, detailsColumnList);

        columnVOList.add(ColumnVO.builder().name("org").label("门店").width(180).emptyString("-").fixed(false).build());

        // 设置数据
        detailPageResult(resultVOS, statisticsColumnList, statisticsValueMap,
                detailsColumnList, detailsValueMap, resultVOList);
        export(response, resultVOList, columnVOList, "交班记录导出");
    }

    private static void detailPageResult(List<BaseShiftHandoverResultVO> resultVOS, List<String> statisticsColumnList, Map<String, String> statisticsValueMap, List<String> detailsColumnList, Map<String, String> detailsValueMap, List<Map> resultVOList) {
        for (BaseShiftHandoverResultVO resultVO : resultVOS) {
            Map<String, Object> result = BeanPlusUtil.beanToMap(resultVO);
            if (Objects.isNull(resultVO.getEndTime())) {
                result.put("endTime", "-");
            }
            // 收银员和交班状态
            result.put("employee", Objects.nonNull(resultVO.getEchoMap().get("employeeId")) ? resultVO.getEchoMap().get("employeeId") : "-");
            result.put("status", Objects.nonNull(resultVO.getEchoMap().get("status")) ? resultVO.getEchoMap().get("status") : "-");

            // 暂时产品不要返回全部
            for (String statisticsColumn : statisticsColumnList) {
                if (statisticsColumn.startsWith("statistics_1")) {
                    result.put(statisticsColumn, StringUtils.isBlank(statisticsValueMap.get(statisticsColumn.concat("_").concat(resultVO.getId().toString()))) ? "-" : statisticsValueMap.get(statisticsColumn.concat("_").concat(resultVO.getId().toString())));
                    List<String> detailsColumns = detailsColumnList.stream().filter(s -> s.startsWith("details_".concat(statisticsColumn.split("_")[1]))).collect(Collectors.toList());
                    for (String detailColumn : detailsColumns) {
                        result.put(detailColumn, StringUtils.isBlank(detailsValueMap.get(detailColumn.concat("_").concat(resultVO.getId().toString()))) ? "-" : detailsValueMap.get(detailColumn.concat("_").concat(resultVO.getId().toString())));
                    }
                }
            }

            result.put("org", Objects.nonNull(resultVO.getEchoMap().get("createdOrgId")) ? resultVO.getEchoMap().get("createdOrgId") : "-");

//            for (String statisticsColumn : statisticsColumnList) {
//                result.put(statisticsColumn, StringUtils.isBlank(statisticsValueMap.get(statisticsColumn.concat("_").concat(resultVO.getId().toString()))) ? "-" : statisticsValueMap.get(statisticsColumn.concat("_").concat(resultVO.getId().toString())));
//                // 详情
//                for (String detailColumn : detailsColumnList) {
//                    result.put(detailColumn, StringUtils.isBlank(detailsValueMap.get(detailColumn.concat("_").concat(resultVO.getId().toString()))) ? "-" : detailsValueMap.get(detailColumn.concat("_").concat(resultVO.getId().toString())));
//                }
//            }
            resultVOList.add(result);
        }
    }

    private static void detailPageColum(List<BaseShiftHandoverStatistics> statisticsList, List<BaseShiftHandoverDetails> detailsList, List<String> statisticsColumnList, List<ColumnVO> columnVOList, List<String> detailsColumnList) {
        Map<String, String> statisticsColumnMap = statisticsList.stream()
                .collect(Collectors.toMap(
                        obj -> "statistics_".concat(obj.getType()).concat("_").concat(obj.getName()),
                        BaseShiftHandoverStatistics::getName,
                        (existing, replacement) -> existing
                ));
        Map<String, String> detailsColumnMap = new HashMap<>();
        if (CollUtil.isNotEmpty(detailsList)) {
            detailsColumnMap = detailsList.stream()
                    .collect(Collectors.toMap(
                            obj -> "details_".concat(obj.getType()).concat("_").concat(obj.getPositive()).concat("_").concat(obj.getName()),
                            BaseShiftHandoverDetails::getName,
                            (existing, replacement) -> existing
                    ));
        }
        // 暂时产品不要返回全部
        for (String statisticsColumn : statisticsColumnList) {
            // 暂时只 展示 type = 0的数据, 并且 去除 备用金(元) 和 应有现金(元)
            if (statisticsColumn.startsWith("statistics_1")) {
                columnVOList.add(ColumnVO.builder().name(statisticsColumn).label(statisticsColumnMap.get(statisticsColumn)).width(180).emptyString("-").fixed(false).build());
                List<String> detailsColumns = detailsColumnList.stream().filter(s -> s.startsWith("details_".concat(statisticsColumn.split("_")[1]))).collect(Collectors.toList());
                for (String detailColumn : detailsColumns) {
                    // 判断是否是 备用金(元) 和 应有现金(元)
                    if (!StringUtils.equals(detailsColumnMap.get(detailColumn), "备用金(元)") && !StringUtils.equals(detailsColumnMap.get(detailColumn), "应有现金(元)")) {
                        columnVOList.add(ColumnVO.builder().name(detailColumn).label(detailsColumnMap.get(detailColumn)).width(180).emptyString("-").fixed(false).build());
                    }
                }
            }
        }


        // 设置动态表头
//        if (CollUtil.isNotEmpty(statisticsList)) {
//            for (String statisticsColumn : statisticsColumnList) {
//                columnVOList.add(ColumnVO.builder().name(statisticsColumn).label(statisticsColumnMap.get(statisticsColumn)).width(180).emptyString("-").fixed(false).build());
//                // 遍历符合的
//                List<String> detailsColumns = detailsColumnList.stream().filter(s -> s.startsWith("details_".concat(statisticsColumn.split("_")[1]))).collect(Collectors.toList());
//                for (String detailColumn : detailsColumns) {
//                    columnVOList.add(ColumnVO.builder().name(detailColumn).label(detailsColumnMap.get(detailColumn)).width(180).emptyString("-").fixed(false).build());
//                }
//            }
//        }
    }

    @Override
    public List<BaseShiftHandoverResultVO> listByEmployee(DataOverviewQuery params) {
        if (StringUtils.isNotBlank(params.getStartDate())) {
            params.setStartDate(params.getStartDate() + " 00:00:00");
        }
        if (StringUtils.isNotBlank(params.getEndDate())) {
            params.setEndDate(params.getEndDate() + " 23:59:59");
        }
        LbQueryWrap<BaseShiftHandover> wrap = Wraps.lbQ();
        wrap.eq(SuperEntity::getDeleteFlag, 0);
        wrap.eq(BaseShiftHandover::getEmployeeId, ContextUtil.getEmployeeId());
        wrap.between(StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate()),
                BaseShiftHandover::getEndTime, params.getStartDate(), params.getEndDate());
        wrap.orderByDesc(BaseShiftHandover::getEndTime);
        List<BaseShiftHandover> list = baseShiftHandoverService.list(wrap);
        List<BaseShiftHandoverResultVO> beanList = BeanPlusUtil.toBeanList(list, BaseShiftHandoverResultVO.class);
        echoService.action(beanList);
        return beanList;
    }

    @Override
    public ShiftHandoverResultVO handoverDetails(Long handoverId) {
        BaseShiftHandover handover = baseShiftHandoverService.getById(handoverId);
        if (Objects.isNull(handover)) {
            return null;
        }
        ShiftHandoverResultVO resultVO = BeanPlusUtil.toBean(handover, ShiftHandoverResultVO.class);
        // 现金支付
        if (Objects.isNull(resultVO.getPettyCash())) {
            resultVO.setPettyCash(BigDecimal.ZERO);
        }
        if (Objects.isNull(resultVO.getSurplusCash())) {
            resultVO.setSurplusCash(BigDecimal.ZERO);
        }
        resultVO.setCashPayment(resultVO.getSurplusCash().subtract(resultVO.getPettyCash()));

        List<BaseShiftHandoverStatistics> statisticsList = baseShiftHandoverStatisticsService.list(Wraps.<BaseShiftHandoverStatistics>lbQ().eq(BaseShiftHandoverStatistics::getDeleteFlag, 0).eq(BaseShiftHandoverStatistics::getHandoverId, handoverId));
        if (CollUtil.isEmpty(statisticsList)) {
            return resultVO;
        }
        List<BaseShiftHandoverDetails> detailsList = baseShiftHandoverDetailsService.list(Wraps.<BaseShiftHandoverDetails>lbQ().eq(BaseShiftHandoverDetails::getDeleteFlag, 0).eq(BaseShiftHandoverDetails::getHandoverId, handoverId));
        List<BaseShiftHandoverStatisticsResultVO> statisticsResultVOS = BeanPlusUtil.toBeanList(statisticsList, BaseShiftHandoverStatisticsResultVO.class);

        if (CollUtil.isNotEmpty(detailsList)) {
            List<BaseShiftHandoverDetailsResultVO> detailsResultList = BeanPlusUtil.toBeanList(detailsList, BaseShiftHandoverDetailsResultVO.class);
            Map<String, List<BaseShiftHandoverDetailsResultVO>> detailsMap = detailsResultList.stream().collect(Collectors.groupingBy(BaseShiftHandoverDetailsResultVO::getType));
            statisticsResultVOS.forEach(s -> {
                s.setDetailsResultVOList(detailsMap.get(s.getType()));
            });
            // 余额支付 = 所有的收入明细 - payment
            resultVO.setBalancePayment(detailsMap.get("1").stream().map(s -> new BigDecimal(s.getValue())).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(resultVO.getPayment()));
        }
        resultVO.setStatisticsResultVOList(statisticsResultVOS);
        echoService.action(resultVO);
        return resultVO;
    }

    @Override
    public ShiftHandoverResultVO handoverPrint(Long handoverId) {
        ShiftHandoverResultVO shiftHandoverResultVO = handoverDetails(handoverId);
        ArgumentAssert.isTrue(StringUtils.equals(shiftHandoverResultVO.getStatus(), "20"), "非交班状态不允许打印");
        PrintVO printVO = new PrintVO();
        printVO.setTypeEnum(PrintTemplateTypeEnum.SHIFT_HANDOVER);
        printVO.setShiftHandoverResultVO(shiftHandoverResultVO);
        printVO.setEmployeeId(ContextUtil.getEmployeeId());
        printOrderService.print(printVO);
        return shiftHandoverResultVO;
    }

    @Override
    public Boolean printHandoverProductStock(HandoverStockResultVO handoverStock) {
        PrintTemplateTypeEnum templateTypeEnum = PrintTemplateTypeEnum.HANDOVER_STOCK;
        PrintVO printVO = new PrintVO();
        printVO.setTypeEnum(templateTypeEnum);
        printVO.setHandoverStockResultVO(handoverStock);
        printVO.setEmployeeId(ContextUtil.getEmployeeId());
        return printOrderService.print(printVO);
    }

    @Override
    public Boolean checkOpenShiftHandover() {
        return baseShiftHandoverService.checkOpenShiftHandover();
    }

    public void initOrgIdList(OrgIdListQuery params) {
        if (Objects.isNull(params)) {
            params = new OrgIdListQuery();
        }
        if (CollUtil.isEmpty(params.getOrgIdList())) {
            params.setOrgIdList(Collections.singletonList(ContextUtil.getCurrentCompanyId()));
        }
    }

    private static void export(HttpServletResponse response, List<Map> resultVOS, List<ColumnVO> columnVOList, String fileName) {
        // 1.获取ExcelWriter对象
        ExcelWriter writer = ExcelUtil.getBigWriter();
        // 2.写出表头
        for (ColumnVO columnVO : columnVOList) {
            // 自定义标题别名
            writer.addHeaderAlias(columnVO.getName(), columnVO.getLabel());
        }

        // ...
        // 3.定义表头单元格样式(可选)
        StyleSet style = writer.getStyleSet();
        CellStyle headCellStyle = style.getHeadCellStyle();
        // 自动换行
        headCellStyle.setWrapText(true);
        // 水平居中
        headCellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 上下居中
        headCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置表头字体大小
        Font headFont = writer.createFont();
        headFont.setFontName("宋体");
        // 设置字体大小为15磅
        headFont.setFontHeightInPoints((short) 15);
        headCellStyle.setFont(headFont);
        // 4.定义内容单元格样式(可选)
        CellStyle cellStyle = style.getCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        // 设置字体大小
        Font font = writer.createFont();
        font.setFontName("宋体");
        cellStyle.setFont(font);
        // 5.其他设置(可选)
        // 只写出设置别名的属性
        writer.setOnlyAlias(true);
        // 冻结行
        writer.setFreezePane(1);
        // 6.写入数据 设置列宽
        writer.write(resultVOS);
        writer.autoSizeColumnAll();
        // 7.开启筛选(可选)
        //CellRangeAddress filterRange = CellRangeAddress.valueOf("B1:D1");
        //writer.getSheet().setAutoFilter(filterRange);
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            // 8.导出
            response.setCharacterEncoding(CharsetUtil.UTF_8);
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()) + ".xlsx");
            response.setContentType("application/vnd.ms-excel;" + CharsetUtil.UTF_8);
            writer.flush(outputStream);
//            writer.close();
        } catch (Exception e) {
            log.warn("批量导出出错：{}", e.getMessage());
        } finally {
            writer.close();
        }
    }
}


