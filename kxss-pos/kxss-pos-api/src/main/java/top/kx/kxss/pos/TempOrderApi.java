package top.kx.kxss.pos;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.app.query.PayQueryOrder;
import top.kx.kxss.app.vo.query.cash.RechargeQuery;
import top.kx.kxss.app.vo.query.cash.ShoppingCreatedQuery;
import top.kx.kxss.pos.entity.cash.PosCashTemp;
import top.kx.kxss.pos.query.order.temp.TempOpenTableQuery;
import top.kx.kxss.pos.vo.order.temp.TempOpenTableResultVO;
import top.kx.kxss.pos.vo.payment.PrepayWithRequestPaymentVO;
import top.kx.kxss.pos.vo.save.reward.RewardCreateOrderSaveVO;
import top.kx.kxss.pos.vo.save.sms.SmsRechargeSaveVO;
import top.kx.kxss.wxapp.vo.result.payment.PrepayPaymentResultVO;
import top.kx.kxss.wxapp.vo.result.shopping.ShoppingCreatedResultVO;

/**
 * 临时订单操作
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/order/temp")
public interface TempOrderApi {

    @ApiOperation(value = "开台", notes = "开台")
    @PostMapping("/open")
    R<TempOpenTableResultVO> open(@RequestBody @Validated TempOpenTableQuery query);

    @GetMapping("/getInfoById")
    PosCashTemp getInfoById(@RequestParam Long id);

    @PostMapping("/payment")
    Boolean payment(@RequestParam Long id);

    @ApiOperation(value = "充值", notes = "充值")
    @PostMapping("/recharge")
    R<PrepayWithRequestPaymentVO> recharge(@RequestBody @Validated RechargeQuery query);

    @ApiOperation(value = "购物", notes = "购物")
    @PostMapping("/shopping")
    ShoppingCreatedResultVO shopping(@RequestBody @Validated ShoppingCreatedQuery query);

    @ApiOperation(value = "打赏", notes = "打赏")
    @PostMapping("/rewardOrder")
    ShoppingCreatedResultVO rewardOrder(@RequestBody @Validated RewardCreateOrderSaveVO model);


    @ApiOperation(value = "短信充值", notes = "短信充值")
    @PostMapping("/smsRecharge")
    PrepayPaymentResultVO smsRecharge(@RequestBody @Validated SmsRechargeSaveVO model);


    @ApiOperation(value = "更新", notes = "更新")
    @PostMapping("/modify")
    boolean updateById(@RequestBody PosCashTemp posCashTemp);

    @ApiOperation(value = "查询订单", notes = "查询订单")
    @PostMapping("/queryOrder")
    R<JSONObject> queryOrder(@RequestBody @Validated PayQueryOrder query);

}
