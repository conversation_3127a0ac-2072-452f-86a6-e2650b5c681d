package top.kx.kxss.pos;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.pos.query.product.BatchChangeNumQuery;
import top.kx.kxss.pos.query.product.ChangeNumQuery;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/detail/operate")
public interface ProductOperateApi {

    @ApiOperation(value = "批量添加商品", notes = "批量添加商品")
    @PostMapping("/batchChangeNum")
    R<Long> batchChangeNum(@RequestBody BatchChangeNumQuery query);

}
