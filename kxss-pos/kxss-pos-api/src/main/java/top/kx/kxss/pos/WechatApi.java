package top.kx.kxss.pos;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import top.kx.basic.constant.Constants;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 推送
 *
 * <AUTHOR>
 */
@FeignClient(value = "external-service",
        url = "${" + Constants.PROJECT_PREFIX + ".wechat.remoteHost}",
        path = "/api/pos/wechat")
public interface WechatApi {


    @GetMapping("/sign")
    @ApiOperation(value = "sign", notes = "验证token信息")
    void get(@RequestParam(required = false) String signature,
             @RequestParam(required = false) String timestamp,
             @RequestParam(required = false) String nonce,
             @RequestParam(required = false) String echostr,
             HttpServletResponse response) throws IOException;

    /**
     * 当设置完微信公众号的接口后，微信会把用户发送的消息，扫码事件等推送过来
     *
     * @param signature    微信加密签名，signature结合了开发者填写的 token 参数和请求中的 timestamp 参数、nonce参数。
     * @param encType      加密类型（暂未启用加密消息）
     * @param msgSignature 加密的消息
     * @param timestamp    时间戳
     * @param nonce        随机数
     * @throws IOException
     */
    @PostMapping(value = "/sign", produces = "text/xml; charset=UTF-8")
    String post(@RequestBody String requestBody, @RequestParam("signature") String signature,
                @RequestParam(name = "encrypt_type", required = false) String encType,
                @RequestParam(name = "msg_signature", required = false) String msgSignature,
                @RequestParam("timestamp") String timestamp, @RequestParam("nonce") String nonce);

}
