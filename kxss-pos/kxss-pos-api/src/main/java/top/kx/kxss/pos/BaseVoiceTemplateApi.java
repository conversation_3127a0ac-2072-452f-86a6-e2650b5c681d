package top.kx.kxss.pos;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.pos.entity.voice.BaseVoiceTemplate;

/**
 * 推送
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/baseVoiceTemplate")
public interface BaseVoiceTemplateApi {


    @PostMapping("/saveEntity")
    R<Boolean> saveEntity(@RequestBody BaseVoiceTemplate build);
}
