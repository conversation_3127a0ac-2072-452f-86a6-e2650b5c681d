package top.kx.kxss.pos;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.app.query.PayAppletQuery;
import top.kx.kxss.app.query.PayQueryOrder;
import top.kx.kxss.pos.vo.payment.PrepayWithRequestPaymentVO;

/**
 * 整单操作
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/pay")
public interface PosPayApi {

    @ApiOperation(value = "小程序支付", notes = "小程序支付")
    @PostMapping("/applet")
    R<PrepayWithRequestPaymentVO> applet(@RequestBody @Validated PayAppletQuery query);

    @ApiOperation(value = "查询订单", notes = "查询订单")
    @PostMapping("/queryOrder")
    R<String> queryOrder(@RequestBody @Validated PayQueryOrder query);

    @ApiOperation(value = "取消支付", notes = "取消支付")
    @PostMapping("/cancel")
    R<Boolean> cancel(@RequestBody @Validated PayQueryOrder query);
}
