package top.kx.kxss.pos;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.pos.query.thail.ThailItemQuery;
import top.kx.kxss.pos.query.thail.ThailQuery;
import top.kx.kxss.pos.vo.thail.ThailDetailsResultVO;
import top.kx.kxss.pos.vo.thail.ThailResultVO;

import java.util.List;

/**
 * 开台
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/thail")
public interface PosThailApi {


    @PostMapping("/list")
    @ResponseBody
    R<List<ThailResultVO>> thailList(@RequestBody @Validated ThailQuery query);

    @PostMapping("/itemDetail")
    @ResponseBody
    R<List<ThailDetailsResultVO>> itemDetail(@RequestBody @Validated ThailItemQuery query);
}
