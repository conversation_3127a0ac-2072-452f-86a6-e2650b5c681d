package top.kx.kxss.pos;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.app.vo.member.MemberIdQuery;
import top.kx.kxss.base.vo.result.member.coupon.MemberCouponResultVO;
import top.kx.kxss.pos.query.member.BindMemberQuery;
import top.kx.kxss.pos.query.member.MemberEquityQuery;

import java.util.List;

/**
 * 开台
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/member")
public interface PosMemberApi {


    @ApiOperation(value = "会员优惠劵列表", notes = "会员优惠劵列表")
    @PostMapping("/couponList")
    R<List<MemberCouponResultVO>> couponList(@RequestBody @Validated MemberIdQuery query);
    @ApiOperation(value = "会员优惠劵列表", notes = "会员优惠劵列表")
    @PostMapping("/couponAllList")
    R<List<MemberCouponResultVO>> couponAllList(@RequestBody @Validated MemberIdQuery query);

    @ApiOperation(value = "会员可使用优惠劵列表", notes = "会员可使用优惠劵列表")
    @PostMapping("/couponUsedList")
    R<List<MemberCouponResultVO>> couponUsedList(@RequestBody @Validated MemberEquityQuery query);

    @ApiOperation(value = "绑定会员", notes = "绑定会员")
    @PostMapping("/bindMember")
    R<Boolean> bindMember(@RequestBody @Validated BindMemberQuery query);
}
