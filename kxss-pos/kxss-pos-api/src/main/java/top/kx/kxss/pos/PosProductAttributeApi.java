package top.kx.kxss.pos;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.pos.vo.save.cash.PosCashProductAttributeSaveVO;

import java.util.List;

/**
 * 任务
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}")
public interface PosProductAttributeApi {

    @PostMapping("/posCashProductAttribute/saveBatch")
    R<Boolean> saveBatch(@RequestBody List<PosCashProductAttributeSaveVO> attributeSaveVOList);

}
