package top.kx.kxss.pos;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.constant.Constants;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.query.OrderIsChangeQuery;
import top.kx.kxss.app.vo.query.cash.OrderCompleteQuery;
import top.kx.kxss.app.vo.query.cash.PosCashIdQuery;
import top.kx.kxss.base.vo.query.CashIdQueryVO;
import top.kx.kxss.base.vo.query.biz.BaseBizLogPageQuery;
import top.kx.kxss.base.vo.result.biz.BaseBizLogResultVO;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.member.card.MemberCardResultVO;
import top.kx.kxss.pos.entity.cash.PosCashPaymentTransaction;
import top.kx.kxss.pos.query.card.BindCardQuery;
import top.kx.kxss.pos.query.coupon.BindCouponQuery;
import top.kx.kxss.pos.query.order.CancelOrderQuery;
import top.kx.kxss.pos.query.order.OrderPageQuery;
import top.kx.kxss.pos.query.order.OrderQuery;
import top.kx.kxss.pos.query.order.SensitiveOrderQuery;
import top.kx.kxss.pos.query.payment.PaymentQuery;
import top.kx.kxss.pos.vo.CashDetailResultVO;
import top.kx.kxss.pos.vo.order.OrderResultVO;
import top.kx.kxss.pos.vo.recharge.RechargePageResultVO;
import top.kx.kxss.pos.vo.save.cash.PosCashPaymentTransactionSaveVO;

import java.util.List;

/**
 * 整单操作
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/order")
public interface PosOrderApi {


    @ApiOperation(value = "停止", notes = "停止")
    @PostMapping("/operate/stopping")
    R<PosCash> stopping(@RequestBody @Validated PosCashIdQuery query);

    @ApiOperation(value = "订单详情", notes = "订单详情")
    @PostMapping("/detail")
    R<CashDetailResultVO> detail(@RequestBody @Validated PosCashIdQuery query);

    @ApiOperation(value = "订单支付", notes = "订单支付")
    @PostMapping("/operate/payment")
    R<PosCash> payment(@RequestBody @Validated PaymentQuery query);

    @ApiOperation(value = "绑定优惠劵", notes = "绑定优惠劵")
    @PostMapping("/operate/bindCoupon")
    R<Boolean> bindCoupon(@RequestBody @Validated BindCouponQuery query);

    @ApiOperation(value = "移除优惠劵", notes = "移除优惠劵")
    @PostMapping("/operate/delCoupon")
    R<Boolean> delCoupon(@RequestBody @Validated PosCashIdQuery query);

    @ApiOperation(value = "账户明细（分页）", notes = "账户明细（分页）")
    @PostMapping(value = "/rechargePage")
    R<Page<RechargePageResultVO>> rechargePage(@RequestBody PageParams<OrderPageQuery> params);

    @ApiOperation(value = "订单列表（分页）", notes = "订单列表（分页）")
    @PostMapping(value = "/orgPage")
    R<Page<OrderResultVO>> page(@RequestBody PageParams<OrderPageQuery> params);

    @ApiOperation(value = "订单列表", notes = "订单列表")
    @PostMapping("/queryList")
    R<Page<OrderResultVO>> queryList(@RequestBody PageParams<OrderQuery> query);


    @ApiOperation(value = "敏感订单列表", notes = "敏感订单列表")
    @PostMapping("/sensitivePage")
    R<Page<OrderResultVO>> sensitivePage(@RequestBody PageParams<SensitiveOrderQuery> query);


    @ApiOperation(value = "订单相关日志（分页）", notes = "订单相关日志（分页）")
    @PostMapping(value = "/bizLogPage")
    R<Page<BaseBizLogResultVO>> bizLogPage(@RequestBody PageParams<BaseBizLogPageQuery> params);

    @ApiOperation(value = "呼叫服务员", notes = "呼叫服务员")
    @PostMapping(value = "/callOut")
    R<Boolean> callOut(PosCashIdQuery query);

    @ApiOperation(value = "呼叫服务员", notes = "呼叫服务员")
    @PostMapping(value = "/callOut")
    R<List<MemberInfoResultVO>> memberListByCashId(PosCashIdQuery query);

    @ApiOperation(value = "订单相关会员权益卡", notes = "订单相关会员权益卡")
    @PostMapping("/operate/cardList")
    R<List<MemberCardResultVO>> cardList(@RequestBody @Validated PosCashIdQuery query);

    @ApiOperation(value = "绑定权益卡", notes = "绑定权益卡")
    @PostMapping("/operate/bindCard")
    @WebLog("绑定权益卡")
    R<Boolean> bindCard(@RequestBody @Validated BindCardQuery query);

    @ApiOperation(value = "移除权益卡", notes = "移除权益卡")
    @PostMapping("/operate/removeCard")
    @WebLog("移除权益卡")
    R<Boolean> removeCard(@RequestBody @Validated BindCardQuery query);


    @ApiOperation(value = "订单是否变化", notes = "订单是否变化")
    @PostMapping("/operate/isChange")
    @WebLog("订单是否变化")
    R<Boolean> isChange(@RequestBody @Validated OrderIsChangeQuery query);

    @ApiOperation(value = "订单是否变化", notes = "订单是否变化")
    @PostMapping("/getById")
    R<PosCash> getById(@RequestParam Long cashId);


    @ApiOperation(value = "取消购物订单", notes = "取消boss端购物订单")
    @PostMapping(value = "/cancelShopping")
    R<PosCash> cancelShopping(@RequestParam Long cashId);


    @PostMapping("/operate/cancelOrder")
    R<Boolean> cancelOrder(@RequestBody @Validated CancelOrderQuery query);

    @PostMapping("/operate/cancel")
    R<Boolean> cancel(@RequestBody @Validated CancelOrderQuery query);

    @PostMapping("/operate/getIds")
    R<List<Long>> getIds(@RequestBody @Validated CashIdQueryVO query);

    @ApiOperation(value = "订单完成", notes = "订单完成")
    @PostMapping(value = "/operate/complete")
    R<PosCash> complete(@RequestBody @Validated OrderCompleteQuery query);

    @ApiOperation(value = "支付流水保存", notes = "支付流水保存")
    @PostMapping(value = "/operate/savePaymentTransaction")
    R<PosCashPaymentTransaction> savePaymentTransaction(@RequestBody @Validated PosCashPaymentTransactionSaveVO query);
}
