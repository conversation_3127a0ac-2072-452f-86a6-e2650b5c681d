package top.kx.kxss.pos;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;

/**
 * 短信
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/sms")
public interface SmsSendApi {


    @PostMapping(value = "/sendCode")
    R<String> sendCode(@RequestParam(required = false) String mobile);

    @PostMapping(value = "/sendCodeByMobile")
    R<String> sendCodeByMobile(@RequestParam String mobile);
}
