package top.kx.kxss.pos;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.constant.Constants;
import top.kx.kxss.app.query.DealOrderPaymentQuery;
import top.kx.kxss.system.entity.subscription.order.SubscriptionOrder;
import top.kx.kxss.system.vo.query.UpdateCashPaymentQuery;
import top.kx.kxss.system.vo.query.UpdateRefundPaymentQuery;
import top.kx.kxss.system.vo.result.subscription.order.SubscriptionOrderResultVO;

/**
 * 整单操作
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/subscription/order")
public interface SubscriptionOrderApi {

    @ApiOperation(value = "更新支付记录", notes = "更新支付记录")
    @PostMapping("/updatePayment")
    R<Boolean> updatePayment(@RequestBody @Validated UpdateCashPaymentQuery query);

    @ApiOperation(value = "更新退款记录", notes = "更新退款记录")
    @PostMapping("/updateRefundPayment")
    R<Boolean> updateRefundPayment(UpdateRefundPaymentQuery refundPaymentId);

    @ApiOperation(value = "根据订单ID查询订单信息", notes = "根据订单ID查询订单信息")
    @PostMapping("/getByOrderId")
    R<SubscriptionOrder> getByOrderId(@RequestParam Long orderId);

    @ApiOperation(value = "支付成功", notes = "支付成功")
    @PostMapping("/paySuccess")
    R<Boolean> payment(@RequestBody DealOrderPaymentQuery query);

    @PostMapping("/page")
    @ApiOperation(value = "租户续费订单", notes = "租户续费订单")
    R<Page<SubscriptionOrderResultVO>> page(@RequestBody PageParams<SubscriptionOrderResultVO> pageParams);
}
