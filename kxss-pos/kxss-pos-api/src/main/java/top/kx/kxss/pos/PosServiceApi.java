package top.kx.kxss.pos;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.constant.Constants;
import top.kx.kxss.pos.query.product.ChangeNumQuery;
import top.kx.kxss.pos.query.product.ProductQuery;
import top.kx.kxss.pos.query.service.ServicePersonalQuery;
import top.kx.kxss.pos.vo.CommonNameResultVO;
import top.kx.kxss.pos.vo.service.PlatformServicePersonalResultVO;
import top.kx.kxss.pos.vo.service.ServiceInfoResultVO;
import top.kx.kxss.pos.vo.service.ServiceResultVO;

import java.util.List;

/**
 * 任务
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/service")
public interface PosServiceApi {

    @PostMapping("/serviceInfoList")
    R<List<ServiceInfoResultVO>> serviceInfoList();

    @PostMapping("/serviceTypeList")
    R<List<ServiceInfoResultVO>> serviceTypeList();

    @ApiOperation(value = "查询服务类型", notes = "查询服务类型")
    @PostMapping("/queryCategory")
    R<List<CommonNameResultVO>> serviceCategory();

    @ApiOperation(value = "服务列表", notes = "服务列表")
    @PostMapping("/queryService")
    R<List<CommonNameResultVO>> service();

    @ApiOperation(value = "服务列表", notes = "服务列表")
    @PostMapping("/queryList")
    R<Page<ServiceResultVO>> serviceList(@RequestBody PageParams<ProductQuery> query);

    @ApiOperation(value = "服务列表", notes = "服务列表")
    @PostMapping("/platformServiceList")
    R<Page<PlatformServicePersonalResultVO>> platformServiceList(@RequestBody PageParams<ServicePersonalQuery> query);

    @ApiOperation(value = "批量添加服务", notes = "批量添加服务")
    @PostMapping("/batchChangeNum")
    R<List<Long>> batchChangeNum(@RequestBody List<ChangeNumQuery> query);

    @ApiOperation(value = "根据员工ID查询信息", notes = "根据员工ID查询信息")
    @GetMapping("/getEmployeeInfo/{employeeId}")
    R<PlatformServicePersonalResultVO> getEmployeeInfo(@PathVariable Long employeeId);

    @ApiOperation(value = "服务列表", notes = "服务列表")
    @PostMapping("/rewardServiceList")
    R<Page<PlatformServicePersonalResultVO>> rewardServiceList(@RequestBody PageParams<ServicePersonalQuery> query);
}
