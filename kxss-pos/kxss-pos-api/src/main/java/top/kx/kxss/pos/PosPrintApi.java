package top.kx.kxss.pos;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.pos.vo.print.PrintVO;

/**
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.pos-server:kxss-pos-server}",
        path = "/print/order")
public interface PosPrintApi {

    @PostMapping("/ipad")
    R<String> print(@RequestBody PrintVO printVO);

}
