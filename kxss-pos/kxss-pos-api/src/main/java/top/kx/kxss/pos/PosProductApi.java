package top.kx.kxss.pos;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.pos.query.product.ProductQuery;
import top.kx.kxss.pos.vo.CommonNameResultVO;
import top.kx.kxss.pos.vo.product.ProductInfoResultVO;

import java.util.List;

/**
 * 任务
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/product")
public interface PosProductApi {

    @PostMapping("/productInfoList")
    R<List<ProductInfoResultVO>> productInfoList(@RequestBody ProductQuery query);

    @ApiOperation(value = "查询商品类型", notes = "查询商品类型")
    @PostMapping("/queryCategory")
    R<List<CommonNameResultVO>> queryCategory();

    @ApiOperation(value = "简易信息列表", notes = "简易信息列表")
    @PostMapping("/simpleList")
    R<List<CommonNameResultVO>> simpleList();


}
