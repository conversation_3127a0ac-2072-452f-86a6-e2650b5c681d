package top.kx.kxss.pos;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.app.query.DealOrderPaymentQuery;
import top.kx.kxss.system.vo.query.UpdateCashPaymentQuery;
import top.kx.kxss.system.vo.query.UpdateRefundPaymentQuery;
import top.kx.kxss.system.entity.deal.DealOrder;
import top.kx.kxss.system.entity.deal.DealOrderPayment;

/**
 * 整单操作
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/deal/recharge")
public interface DealOrderApi {

    @ApiOperation(value = "更新支付记录", notes = "更新支付记录")
    @PostMapping("/updatePayment")
    R<Boolean> updatePayment(@RequestBody @Validated UpdateCashPaymentQuery query);

    @ApiOperation(value = "查询支付记录", notes = "查询支付记录")
    @GetMapping("/paymentById")
    R<DealOrderPayment> paymentById(@RequestParam Long id);

    @ApiOperation(value = "更新退款记录", notes = "更新退款记录")
    @PostMapping("/updateRefundPayment")
    R<Boolean> updateRefundPayment(UpdateRefundPaymentQuery refundPaymentId);

    @ApiOperation(value = "根据订单ID查询订单信息", notes = "根据订单ID查询订单信息")
    @PostMapping("/getByOrderId")
    R<DealOrder> getByOrderId(@RequestParam Long orderId);

    @ApiOperation(value = "支付成功", notes = "支付成功")
    @PostMapping("/paySuccess")
    R<Boolean> payment(@RequestBody DealOrderPaymentQuery query);

}
