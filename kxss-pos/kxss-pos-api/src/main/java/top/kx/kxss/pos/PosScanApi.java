package top.kx.kxss.pos;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.pos.query.SceneQuery;
import top.kx.kxss.system.vo.result.scan.DefScanResultVO;

/**
 * 开台
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/scan")
public interface PosScanApi {

    @PostMapping(value = "/findByScene")
    @ResponseBody
    @ApiOperation(value = "根据scene获取信息", notes = "根据scene获取信息")
    R<DefScanResultVO> findByScene(@RequestBody @Validated SceneQuery query);


    @PostMapping(value = "/sure")
    @ResponseBody
    @ApiOperation(value = "确认", notes = "确认")
    R<Boolean> sure(@RequestBody @Validated SceneQuery query);

    @PostMapping(value = "/cancel")
    @ResponseBody
    @ApiOperation(value = "取消", notes = "取消")
    R<Boolean> cancel(@RequestBody @Validated SceneQuery query);

}
