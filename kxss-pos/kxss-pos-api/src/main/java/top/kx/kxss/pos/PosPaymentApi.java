package top.kx.kxss.pos;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.query.BizPaymentQuery;
import top.kx.kxss.app.vo.query.cash.PosCashIdQuery;
import top.kx.kxss.pos.query.payment.PayAmountQuery;
import top.kx.kxss.pos.query.payment.RevokePaymentQuery;
import top.kx.kxss.system.vo.query.UpdateCashPaymentQuery;
import top.kx.kxss.system.vo.query.UpdateRefundPaymentQuery;
import top.kx.kxss.pos.vo.order.PayResultVO;
import top.kx.kxss.pos.vo.payment.PayAmountResultVO;
import top.kx.kxss.pos.vo.payment.PrepayWithRequestPaymentVO;

/**
 * 整单操作
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/payment")
public interface PosPaymentApi {


    @ApiOperation(value = "支付", notes = "支付")
    @PostMapping
    R<PosCash> payment(@RequestBody @Validated BizPaymentQuery query);

    @ApiOperation(value = "wx支付", notes = "wx支付")
    @PostMapping("wxPay")
    R<PosCash> wxPayment(@RequestBody @Validated BizPaymentQuery query);

    @ApiOperation(value = "acc支付", notes = "acc支付")
    @PostMapping("accPay")
    R<PosCash> accPayment(@RequestBody @Validated BizPaymentQuery query);

    @ApiOperation(value = "撤销支付", notes = "撤销支付")
    @PostMapping("/revokePayment")
    R<Boolean> revokePayment(@RequestBody @Validated RevokePaymentQuery query);

    @ApiOperation(value = "拉起聚合支付", notes = "拉起聚合支付")
    @PostMapping("/unifiedOrder")
    R<PrepayWithRequestPaymentVO> unifiedOrder(@RequestBody @Validated BizPaymentQuery query);

    @ApiOperation(value = "查询订单支付信息", notes = "查询订单支付信息")
    @PostMapping("/payResult")
    R<Boolean> payResult(@RequestBody @Validated PosCashIdQuery query);

    @ApiOperation(value = "订单支付成功", notes = "订单支付成功")
    @PostMapping("/paySuccess")
    R<Boolean> paySuccess(@RequestBody @Validated PayResultVO payResultVO);

    @ApiOperation(value = "订单支付失败", notes = "订单支付失败")
    @PostMapping("/payFail")
    R<Boolean> payFail(@RequestBody @Validated PayResultVO payResultVO);

    @ApiOperation(value = "获取支付方式对应支付金额", notes = "获取支付方式对应的支付金额")
    @PostMapping("/payAmount")
    R<PayAmountResultVO> payAmount(@RequestBody @Validated PayAmountQuery query);

    @ApiOperation(value = "更新支付记录", notes = "更新支付记录")
    @PostMapping("/updateCashPayment")
    R<Boolean> updateCashPayment(@RequestBody @Validated UpdateCashPaymentQuery query);

    @ApiOperation(value = "查询支付记录", notes = "查询支付记录")
    @GetMapping("/cashPaymentById")
    R<PosCashPayment> cashPaymentById(@RequestParam Long id);

    @ApiOperation(value = "更新退款记录", notes = "更新退款记录")
    @PostMapping("/updateRefundPayment")
    R<Boolean> updateRefundPayment(UpdateRefundPaymentQuery refundPaymentId);
}
