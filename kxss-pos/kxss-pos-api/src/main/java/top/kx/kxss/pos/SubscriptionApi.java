package top.kx.kxss.pos;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.pos.vo.payment.PrepayWithRequestPaymentVO;
import top.kx.kxss.system.vo.result.subscription.SubscriptionTenantTemplateResultVO;
import top.kx.kxss.system.vo.result.subscription.market.MarketTenantTemplateResultVO;

import java.util.List;

/**
 * 整单操作
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/subscription")
public interface SubscriptionApi {

    @ApiOperation(value = "获取门店订阅版本信息", notes = "获取门店订阅版本信息")
    @PostMapping(value = "/info")
    R<MarketTenantTemplateResultVO> info();

    @ApiOperation(value = "获取门店订阅生效版本列表", notes = "获取门店订阅生效版本列表")
    @PostMapping(value = "/templateList")
    R<List<SubscriptionTenantTemplateResultVO>> templateList();

    @ApiOperation(value = "获取门店订阅增值生效版本列表", notes = "获取门店订阅增值生效版本列表")
    @PostMapping(value = "/valueAddedList")
    R<List<SubscriptionTenantTemplateResultVO>> valueAddedList();

    @ApiOperation(value = "模板立即续费", notes = "模板立即续费")
    @PostMapping(value = "/order/renewImmediately")
    R<PrepayWithRequestPaymentVO> renewImmediately();

    @ApiOperation(value = "根据模板立即续费", notes = "根据模板立即续费")
    @PostMapping(value = "/order/renewImmediately/{tenantTmpId}")
    R<PrepayWithRequestPaymentVO> renewImmediatelyByTmpId(@PathVariable Long tenantTmpId);

    @ApiOperation(value = "取消支付", notes = "取消支付")
    @PostMapping(value = "/order/cancelPay/{orderId}")
    R<Boolean> cancelPay(@PathVariable Long orderId);

    @ApiOperation(value = "继续支付", notes = "继续支付")
    @PostMapping(value = "/order/continuePay/{orderId}")
    R<PrepayWithRequestPaymentVO> continuePay(@PathVariable Long orderId);
}
