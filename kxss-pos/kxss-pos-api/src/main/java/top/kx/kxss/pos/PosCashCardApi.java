package top.kx.kxss.pos;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.ResponseBody;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.pos.vo.result.cash.PosCashCardResultVO;

import java.util.List;

/**
 * 开台
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/posCashCard")
public interface PosCashCardApi {


    @PostMapping("/queryListByCashId")
    @ResponseBody
    R<List<PosCashCardResultVO>> queryListByCashId(@RequestBody List<Long> query);

}
