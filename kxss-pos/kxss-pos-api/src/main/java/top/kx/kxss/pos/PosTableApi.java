package top.kx.kxss.pos;

import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.app.entity.cash.OpeningTableSaveVO;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.base.vo.result.common.BaseDictResultVO;
import top.kx.kxss.pos.query.QueryDetailQuery;
import top.kx.kxss.pos.query.table.TableIdGroupBuyQuery;
import top.kx.kxss.pos.query.table.TableInfoQuery;
import top.kx.kxss.pos.vo.CashDetailResultVO;
import top.kx.kxss.pos.vo.CommonNameResultVO;
import top.kx.kxss.pos.vo.HomePriceResultVO;
import top.kx.kxss.pos.vo.table.TableGroupBuyResultVO;
import top.kx.kxss.pos.vo.table.TableInfoResultVO;

import java.util.List;

/**
 * 开台
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/table")
public interface PosTableApi {


    @RequestMapping(value = "/type", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "台桌类型", notes = "台桌类型")
    R<List<BaseDictResultVO>> type();

    @RequestMapping(value = "/open", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "开台", notes = "开台")
    R<PosCash> openTable(@RequestBody @Validated OpeningTableSaveVO saveVO);

    @PostMapping("/queryDetail")
    @ResponseBody
    @ApiOperation(value = "开台明细", notes = "开台明细")
    R<CashDetailResultVO> queryDetail(@RequestBody @Validated QueryDetailQuery query);

    @RequestMapping(value = "/simpleList", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "台桌简易列表", notes = "台桌简易列表")
    R<List<CommonNameResultVO>> simpleList();

    @RequestMapping(value = "/list", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "台桌列表", notes = "台桌列表")
    R<List<TableInfoResultVO>> list(@RequestBody TableInfoQuery query);


    @RequestMapping(value = "/refreshPrice", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "刷新价格", notes = "刷新价格")
    R<List<HomePriceResultVO>> refreshPrice(@RequestBody TableInfoQuery query);

    @RequestMapping(value = "/bossList", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "台桌列表(boss)", notes = "台桌列表(boss)")
    R<List<TableInfoResultVO>> bossList(@RequestBody TableInfoQuery query);

    @RequestMapping(value = "/verifyGroupBuy", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "验证券码和台桌", notes = "验券验证")
    R<TableGroupBuyResultVO> verifyGroupBuy(TableIdGroupBuyQuery query);
}
