package top.kx.kxss.pos;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.pos.push.PushNoticeDeviceMsgBean;
import top.kx.kxss.pos.push.VerticalScreenMsgBean;
import top.kx.kxss.pos.query.ws.BindDevicePushQuery;

/**
 * 推送
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/ws")
public interface WsPushApi {


    @PostMapping("/bindDevicePush")
    R<Boolean> bindDevicePush(@RequestBody @Validated BindDevicePushQuery query);

    /**
     * @deprecated 自  2025-06-20 起弃用,请使用 {@link #sendVerticalScreenMsg(VerticalScreenMsgBean)}
     * 原因：新接口支持更灵活的消息结构，老接口仅为兼容性保留
     * @param query
     * @return
     */
    @Deprecated
    @PostMapping("/sendRefreshMsg")
    R<Boolean> sendRefreshMsg(@RequestBody @Validated PushNoticeDeviceMsgBean query);

    /**
     * 新的智慧屏推送接口
     * @param query
     * @return
     */
    @PostMapping("/sendVerticalScreenMsg")
    R<Boolean> sendVerticalScreenMsg(@RequestBody @Validated VerticalScreenMsgBean query);
}
