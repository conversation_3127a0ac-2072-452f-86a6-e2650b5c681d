package top.kx.kxss.pos;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.base.vo.ExecutorParamVO;

/**
 * 任务
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pos-server}",
        path = "/job")
public interface PosJobApi {

    @PostMapping("/stop")
    R<Boolean> stop(@RequestBody @Validated ExecutorParamVO executorParamVO);

    @PostMapping("/serviceStop")
    R<Boolean> serviceStop(@RequestBody @Validated ExecutorParamVO executorParamVO);

    @PostMapping("/voice")
    R<Boolean> voice(@RequestBody @Validated ExecutorParamVO executorParamVO);

    @PostMapping("/closeLight")
    R<Boolean> closeLight(@RequestBody @Validated ExecutorParamVO executorParamVO);

    @PostMapping("/paymentJob")
    R<Boolean> paymentJob(@RequestBody @Validated ExecutorParamVO executorParamVO);

    @PostMapping("/inventoryAlert")
    R<Boolean> inventoryAlert(@RequestBody @Validated ExecutorParamVO executorParamVO);

    /**
     * 订单临时关灯
     */
    @PostMapping("/cashCloseLight")
    R<Boolean> cashCloseLight(@RequestBody @Validated ExecutorParamVO executorParamVO);

    /**
     * 订单自动挂单
     */
    @PostMapping("/autoRegistration")
    R<Boolean> autoRegistration(@RequestBody @Validated ExecutorParamVO executorParamVO);

    /**
     * 定时发券
     */
    @PostMapping("/grantCoupon")
    R<Boolean> grantCoupon(@RequestBody @Validated ExecutorParamVO executorParamVO);

}
