package top.kx.kxss.pos.entity.cash;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 支付流水
 * </p>
 *
 * <AUTHOR>
 * @date 2025-02-05 15:17:57
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("pos_cash_payment_transaction")
public class PosCashPaymentTransaction extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 分类: 1-账单结算,2-充值收入
     */
    @TableField(value = "type_", condition = LIKE)
    private String type;
    /**
     * 支付类型ID
     */
    @TableField(value = "pay_type_id", condition = EQUAL)
    private Long payTypeId;

    /**
     * 支付类型
     */
    @TableField(value = "pay_type", condition = LIKE)
    private String payType;

    /**
     * 支付详情
     */
    @TableField(value = "pay_type_detail", condition = LIKE)
    private String payTypeDetail;

    /**
     * 团购券类型
     */
    @TableField(value = "securities_type", condition = LIKE)
    private String securitiesType;

    /**
     * 券码
     */
    @TableField(value = "securities_number", condition = LIKE)
    private String securitiesNumber;

    /**
     * 状态 1 支付成功 2 退款成功
     */
    @TableField(value = "status", condition = LIKE)
    private String status;
    /**
     * 主表id
     */
    @TableField(value = "cash_id", condition = EQUAL)
    private Long cashId;
    /**
     * 支付记录ID
     */
    @TableField(value = "payment_id", condition = EQUAL)
    private Long paymentId;
    /**
     * 订单号
     */
    @TableField(value = "code_", condition = LIKE)
    private String code;
    /**
     * 金额
     */
    @TableField(value = "amount", condition = EQUAL)
    private BigDecimal amount;
    /**
     * 商户手续费
     */
    @TableField(value = "fee_amount", condition = EQUAL)
    private BigDecimal feeAmount;
    /**
     * 支付时间
     */
    @TableField(value = "pay_time", condition = EQUAL)
    private LocalDateTime payTime;
    /**
     * 三方订单ID
     */
    @TableField(value = "order_id", condition = LIKE)
    private String orderId;

    /**
     * 备注
     */
    @TableField(value = "order_source", condition = LIKE)
    private String orderSource;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 员工ID
     */
    @TableField(value = "employee_id", condition = EQUAL)
    private Long employeeId;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;



}
