package top.kx.kxss.pos.entity.cash;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;
import top.kx.basic.base.entity.Entity;


/**
 * <p>
 * 实体类
 * 商品订单属性
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-12 14:48:50
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("pos_cash_product_attribute")
public class PosCashProductAttribute extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 商品订单id
     */
    @TableField(value = "cash_product_id", condition = EQUAL)
    private Long cashProductId;
    /**
     * 商品id
     */
    @TableField(value = "product_id", condition = EQUAL)
    private Long productId;
    /**
     * 属性id
     */
    @TableField(value = "attribute_id", condition = EQUAL)
    private Long attributeId;
    /**
     * 属性
     */
    @TableField(value = "attribute_name", condition = LIKE)
    private String attributeName;
    /**
     * 属性设置id
     */
    @TableField(value = "attribute_setting_id", condition = EQUAL)
    private Long attributeSettingId;
    /**
     * 属性设置
     */
    @TableField(value = "attribute_setting_name", condition = LIKE)
    private String attributeSettingName;
    /**
     * 价格变动类型:1-不加价,2-固定加价,3-比例加价,4-固件减价,5-比例减价
     */
    @TableField(value = "change_type", condition = LIKE)
    private String changeType;
    /**
     * 价格变动值
     */
    @TableField(value = "change_value", condition = EQUAL)
    private BigDecimal changeValue;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;


    public static final String CASH_PRODUCT_ID = "cash_product_id";
    public static final String PRODUCT_ID = "product_id";
    public static final String ATTRIBUTE_ID = "attribute_id";
    public static final String ATTRIBUTE_NAME = "attribute_name";
    public static final String ATTRIBUTE_SETTING_ID = "attribute_setting_id";
    public static final String ATTRIBUTE_SETTING_NAME = "attribute_setting_name";
    public static final String CHANGE_TYPE = "change_type";
    public static final String CHANGE_VALUE = "change_value";
    public static final String CREATED_ORG_ID = "created_org_id";
    public static final String DELETE_FLAG = "delete_flag";

}
