package top.kx.kxss.pos.entity.voice;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 语音模板
 * </p>
 *
 * <AUTHOR>
 * @date 2024-06-18 16:42:02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_voice_template")
public class BaseVoiceTemplate extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 类型:1-结账语音
     */
    @TableField(value = "type", condition = LIKE)
    private String type;
    /**
     * 模板内容
     */
    @TableField(value = "content", condition = LIKE)
    private String content;


    @TableField(value = "state", condition = EQUAL)
    private Boolean state;

    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = LIKE)
    private Long createdOrgId;

}
