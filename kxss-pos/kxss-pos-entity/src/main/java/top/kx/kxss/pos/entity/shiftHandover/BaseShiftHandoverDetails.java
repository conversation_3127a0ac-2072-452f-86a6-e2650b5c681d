package top.kx.kxss.pos.entity.shiftHandover;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;
import top.kx.basic.base.entity.Entity;


/**
 * <p>
 * 实体类
 * 交班详情
 * </p>
 *
 * <AUTHOR>
 * @date 2024-06-21 11:49:10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_shift_handover_details")
public class BaseShiftHandoverDetails extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 交班记录id
     */
    @TableField(value = "handover_id", condition = EQUAL)
    private Long handoverId;
    /**
     * 类型:1-收款,2-台桌开台,3-直接购物,4-会员充值,5-退款,6-订单,7-敏感操作
     */
    @TableField(value = "type_", condition = LIKE)
    private String type;

    /**
     * 0-正数,1-负数
     */
    @TableField(value = "positive", condition = LIKE)
    private String positive;
    /**
     * 名称
     */
    @TableField(value = "name", condition = LIKE)
    private String name;
    /**
     * 值
     */
    @TableField(value = "value", condition = LIKE)
    private String value;
    /**
     * 单位
     */
    @TableField(value = "unit", condition = LIKE)
    private String unit;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;
    /**
     * 删除标识 0 未删除 1 已删除
     */
    @TableField(value = "delete_flag", condition = EQUAL)
    private Integer deleteFlag;


    public static final String HANDOVER_ID = "handover_id";
    public static final String TYPE_ = "type_";
    public static final String NAME = "name";
    public static final String VALUE = "value";
    public static final String UNIT = "unit";
    public static final String CREATED_ORG_ID = "created_org_id";
    public static final String DELETE_FLAG = "delete_flag";

}
