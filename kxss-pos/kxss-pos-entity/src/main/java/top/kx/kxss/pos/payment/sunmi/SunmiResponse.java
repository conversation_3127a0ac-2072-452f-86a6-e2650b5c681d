package top.kx.kxss.pos.payment.sunmi;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/9/20 20:15
 */
@NoArgsConstructor
@Data
@Builder
@AllArgsConstructor
public class SunmiResponse {

    @ApiModelProperty(value = "")
    private boolean success;
    @ApiModelProperty(value = "")
    private ResponseVO response;
    @ApiModelProperty(value = "")
    private String message;

    @NoArgsConstructor
    @Data
    public static class ResponseVO {
        @ApiModelProperty(value = "")
        private String batchNum;
        @ApiModelProperty(value = "")
        private String accountType;
        @ApiModelProperty(value = "")
        private String operatorld;
        @ApiModelProperty(value = "")
        private String appType;
        @ApiModelProperty(value = "")
        private String referenceNum;
        @ApiModelProperty(value = "")
        private String orderld;
        @ApiModelProperty(value = "")
        private int amount1;
        @ApiModelProperty(value = "")
        private String acquirer;
        @ApiModelProperty(value = "")
        private String platformld;
        @ApiModelProperty(value = "")
        private String platform;
        @ApiModelProperty(value = "")
        private String misld;
        @ApiModelProperty(value = "")
        private String terminalld;
        @ApiModelProperty(value = "")
        private String transDate;
        @ApiModelProperty(value = "")
        private String version;
        @ApiModelProperty(value = "")
        private String issuer;
        @ApiModelProperty(value = "")
        private String cardNum;
        @ApiModelProperty(value = "")
        private String model;
        @ApiModelProperty(value = "")
        private String transTime;
        @ApiModelProperty(value = "")
        private int amount;
        @ApiModelProperty(value = "")
        private int amount2;
        @ApiModelProperty(value = "")
        private String businessld;
        @ApiModelProperty(value = "")
        private int amount3;
        @ApiModelProperty(value = "")
        private String merchantld;
        @ApiModelProperty(value = "")
        private String cardType;
        @ApiModelProperty(value = "")
        private String resultMsg;
        @ApiModelProperty(value = "")
        private String transType;
        @ApiModelProperty(value = "")
        private String resultCode;
        @ApiModelProperty(value = "")
        private String voucherNum;
    }
}
