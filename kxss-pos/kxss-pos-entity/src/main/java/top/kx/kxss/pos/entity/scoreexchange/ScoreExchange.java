package top.kx.kxss.pos.entity.scoreexchange;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;
import top.kx.basic.base.entity.Entity;


/**
 * <p>
 * 实体类
 * 积分兑换
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-18 19:14:08
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("score_exchange")
public class ScoreExchange extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 会员ID
     */
    @TableField(value = "member_id", condition = EQUAL)
    private Long memberId;
    /**
     * 消耗积分
     */
    @TableField(value = "score", condition = EQUAL)
    private Integer score;
    /**
     * 剩余积分
     */
    @TableField(value = "surplus_score", condition = EQUAL)
    private Integer surplusScore;
    /**
     * 兑换类型:1-商品
     */
    @TableField(value = "biz_type", condition = LIKE)
    private String bizType;
    /**
     * 兑换物品id
     */
    @TableField(value = "biz_id", condition = EQUAL)
    private Long bizId;
    /**
     * 兑换来源: 0-web 1-pos  2app
     */
    @TableField(value = "source_type", condition = LIKE)
    private String sourceType;

    /**
     * 兑换数量
     */
    @TableField(value = "quantity", condition = EQUAL)
    private Integer quantity;

    /**
     * 仓库ID
     */
    @TableField(value = "warehouse_id", condition = EQUAL)
    private Long warehouseId;

    /**
     * 兑换物品名称
     */
    @TableField(value = "biz_name", condition = LIKE)
    private String bizName;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;
    /**
     * 删除标识 0 未删除 1 已删除
     */
    @TableField(value = "delete_flag", condition = EQUAL)
    private Integer deleteFlag;


    public static final String MEMBER_ID = "member_id";
    public static final String SCORE = "score";
    public static final String SURPLUS_SCORE = "surplus_score";
    public static final String BIZ_TYPE = "biz_type";
    public static final String BIZ_ID = "biz_id";
    public static final String BIZ_NAME = "biz_name";
    public static final String REMARKS = "remarks";
    public static final String CREATED_ORG_ID = "created_org_id";
    public static final String DELETE_FLAG = "delete_flag";

}
