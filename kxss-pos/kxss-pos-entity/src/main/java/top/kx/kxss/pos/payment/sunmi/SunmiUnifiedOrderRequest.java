package top.kx.kxss.pos.payment.sunmi;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 商米统一支付请求
 * <AUTHOR>
 * @date 2023/9/22 20:25
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel(value = "SunmiUnifiedOrderRequest", description = "商米统一下单")
public class SunmiUnifiedOrderRequest {

    @ApiModelProperty(value = "商户号（商户进件成功商米创建商户的商户号)")
    private String merchantNo;

    @ApiModelProperty(value = "交易金额格式：精确到分 最低1分钱")
    private String totalAmount;

    @ApiModelProperty(value = "商户订单号")
    private String orderId;

    @ApiModelProperty(value = "交易场景：\n" +
            "BC-付款码支付（B扫C）\n" +
            "JSAPI-公众号支付【微信公众号】【支付宝生活号】\n" +
            "MINI-小程序支付【微信小程序】【支付宝小程序】")
    private String payScene;
    @ApiModelProperty(value = "交易类型：\n" +
            "WX-微信\n" +
            "ALI-支付宝\n" +
            "UNION 银联支付")
    private String payType;

    @ApiModelProperty(value = "支付结果通知地址")
    private String notifyUrl;
    @ApiModelProperty(value = "业务流水号")
    private String businessId;

    @ApiModelProperty(value = "终端IP：正确的用户端IP")
    private String terminalIp;

    @ApiModelProperty(value = "订单描述【如不填写，默认为商户名称】")
    private String orderTitle;

    @ApiModelProperty(value = "订单描述【如不填写，默认为商户名称】")
    private String orderDetail;

    @ApiModelProperty(value = "交易有效时间（如不填写，默认值为3，单位分钟，取值区间3~120)")
    private String expireTime;

    @ApiModelProperty(value = "jsapi/公众号，商户公众号AppID、小程序AppID  \n" +
            "（公众号和小程序支付必填）")
    private String miniAppId;

    @ApiModelProperty(value = "jsapi/公众号，支付宝userId、微信openId【用户标识】\n" +
            "（公众号和小程序支付必填）")
    private String userId;

    @ApiModelProperty(value = "B扫C，支付授权码【扫码支付使用】，B扫C交易时必传")
    private String authCode;

    @ApiModelProperty(value = "B扫C，应用包名，B扫C交易时必传")
    private String packageName;

    @ApiModelProperty(value = "B扫C，应用版本号，B扫C交易时必传")
    private String appVersion;

    @ApiModelProperty(value = "B扫C，经度,121.615459404，B扫C交易时必传")
    private String longitude;

    @ApiModelProperty(value = "B扫C，维度,31.4056441552，B扫C交易时必传")
    private String latitude;

    @ApiModelProperty(value = "终端号")
    private String terminalNo;



}
