package top.kx.kxss.pos.entity.scorerecords;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;
import top.kx.basic.base.entity.Entity;


/**
 * <p>
 * 实体类
 * 积分记录详情
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-16 15:15:40
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("score_records_details")
public class ScoreRecordsDetails extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 积分记录id
     */
    @TableField(value = "score_id", condition = EQUAL)
    private Long scoreId;
    /**
     * 关联单据id(pos_cash)
     */
    @TableField(value = "cash_id", condition = EQUAL)
    private Long cashId;
    /**
     * 关联单据号(pos_cash)
     */
    @TableField(value = "cash_code", condition = LIKE)
    private String cashCode;
    /**
     * 会员ID
     */
    @TableField(value = "member_id", condition = EQUAL)
    private Long memberId;


    @TableField(value = "exchange_id", condition = EQUAL)
    private Long exchangeId;

    /**
     * 变动积分
     */
    @TableField(value = "score", condition = EQUAL)
    private Integer score;
    /**
     * 金额
     */
    @TableField(value = "amount", condition = EQUAL)
    private BigDecimal amount;
    /**
     * 是否增加积分
     */
    @TableField(value = "is_add", condition = EQUAL)
    private Boolean isAdd;
    /**
     * 类型 TABLE 台费 PRODUCT 商品 SERVICE 服务 RECHARGE 储存(充值) THAIL 套餐 BUY_CARD 卡券 ADJUST 调整 EXCHANGE 兑换
     */
    @TableField(value = "type_", condition = LIKE)
    private String type;
    /**
     * 记录生成时的,积分生成设置规则
     */
    @TableField(value = "explain_", condition = LIKE)
    private String explain;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;
    /**
     * 删除标识 0 未删除 1 已删除
     */
    @TableField(value = "delete_flag", condition = EQUAL)
    private Integer deleteFlag;


    public static final String SCORE_ID = "score_id";
    public static final String CASH_ID = "cash_id";
    public static final String MEMBER_ID = "member_id";
    public static final String SCORE = "score";
    public static final String AMOUNT = "amount";
    public static final String IS_ADD = "is_add";
    public static final String TYPE_ = "type_";
    public static final String EXPLAIN_ = "explain_";
    public static final String REMARKS = "remarks";
    public static final String CREATED_ORG_ID = "created_org_id";
    public static final String DELETE_FLAG = "delete_flag";

}
