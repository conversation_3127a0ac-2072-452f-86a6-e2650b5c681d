package top.kx.kxss.pos.entity.cash;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;
import top.kx.basic.base.entity.Entity;


/**
 * <p>
 * 实体类
 * 订单支付抵用券
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-28 18:32:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("pos_cash_payment_coupon")
public class PosCashPaymentCoupon extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 券id
     */
    @TableField(value = "coupon_id", condition = EQUAL)
    private Long couponId;
    /**
     * 券数量
     */
    @TableField(value = "num", condition = EQUAL)
    private Integer num;
    /**
     * 面值/商品原价
     */
    @TableField(value = "original_price", condition = EQUAL)
    private BigDecimal originalPrice;
    /**
     * 收入金额
     */
    @TableField(value = "amount", condition = EQUAL)
    private BigDecimal amount;
    /**
     * 订单ID
     */
    @TableField(value = "cash_id", condition = EQUAL)
    private Long cashId;
    /**
     * 券名称
     */
    @TableField(value = "coupon_name", condition = LIKE)
    private String couponName;
    /**
     * 支付方式ID
     */
    @TableField(value = "pay_type_id", condition = EQUAL)
    private Long payTypeId;
    /**
     * 支付记录ID
     */
    @TableField(value = "cash_payment_id", condition = EQUAL)
    private Long cashPaymentId;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;


    public static final String COUPON_ID = "coupon_id";
    public static final String NUM = "num";
    public static final String ORIGINAL_PRICE = "original_price";
    public static final String AMOUNT = "amount";
    public static final String CASH_ID = "cash_id";
    public static final String COUPON_NAME = "coupon_name";
    public static final String PAY_TYPE_ID = "pay_type_id";
    public static final String CASH_PAYMENT_ID = "cash_payment_id";
    public static final String CREATED_ORG_ID = "created_org_id";

}
