package top.kx.kxss.pos.payment.sunmi;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/9/20 20:15
 */
@NoArgsConstructor
@Data
@Builder
@AllArgsConstructor
public class SunmiPayResponse<T> {

    @ApiModelProperty(value = "结果集")
    private Map<String, Object> data;

    @ApiModelProperty(value = "编码")
    private int code;

    @ApiModelProperty(value = "错误信息")
    private String msg;

}
