package top.kx.kxss.pos.entity.cash;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 充电计时费用
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-27 15:50:43
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("pos_cash_power")
public class PosCashPower extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 结算ID
     */
    @TableField(value = "cash_id", condition = EQUAL)
    private Long cashId;
    /**
     * 名称
     */
    @TableField(value = "name", condition = EQUAL)
    private String name;
    /**
     * 台桌主键
     */
    @TableField(value = "table_id", condition = EQUAL)
    private Long tableId;
    /**
     * 台桌名称
     */
    @TableField(value = "table_name", condition = LIKE)
    private String tableName;
    /**
     * 充电类型
     */
    @TableField(value = "power_type", condition = EQUAL)
    private String powerType;
    /**
     * 时长
     */
    @TableField(value = "duration", condition = EQUAL)
    private Integer duration;
    /**
     * 开始时间
     */
    @TableField(value = "start_time", condition = EQUAL)
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    @TableField(value = "end_time", condition = EQUAL)
    private LocalDateTime endTime;
    /**
     * 原始价格
     */
    @TableField(value = "orgin_price", condition = EQUAL)
    private BigDecimal orginPrice;
    /**
     * 单价
     */
    @TableField(value = "price", condition = EQUAL)
    private BigDecimal price;
    /**
     * 金额
     */
    @TableField(value = "amount", condition = EQUAL)
    private BigDecimal amount;
    /**
     * 体验时长
     */
    @TableField(value = "overtime", condition = EQUAL)
    private Integer overtime;
    /**
     * 计费周期
     */
    @TableField(value = "period", condition = EQUAL)
    private Integer period;
    /**
     * 计费单价
     */
    @TableField(value = "billing_price", condition = EQUAL)
    private BigDecimal billingPrice;
    /**
     * 计费周期描述
     */
    @TableField(value = "cycle", condition = LIKE)
    private String cycle;
    /**
     * 优惠类型: 1-打折 2-减免 3-赠送 4-卡权益-打折卡 5-卡权益-次卡 6-卡权益-时长卡
     */
    @TableField(value = "type", condition = LIKE)
    private String type;
    /**
     * 优惠数值
     */
    @TableField(value = "discount", condition = EQUAL)
    private BigDecimal discount;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 状态 0 计时中  1：停止计时
     */
    @TableField(value = "status", condition = LIKE)
    private String status;
    /**
     * 使用优惠券ID
     */
    @TableField(value = "coupon_id", condition = EQUAL)
    private Long couponId;
    /**
     * 是否合并
     */
    @TableField(value = "is_merge", condition = EQUAL)
    private Boolean isMerge;
    /**
     * 是否转台
     */
    @TableField(value = "is_turn", condition = EQUAL)
    private Boolean isTurn;
    /**
     * 抵扣的时长
     */
    @TableField(value = "deduct_duration", condition = EQUAL)
    private Integer deductDuration;
    /**
     * 单品折扣/优惠备注
     */
    @TableField(value = "discount_remarks", condition = LIKE)
    private String discountRemarks;
    /**
     * 优惠类型
     */
    @TableField(value = "discount_type", condition = LIKE)
    private String discountType;
    /**
     * 优惠金额
     */
    @TableField(value = "discount_amount", condition = EQUAL)
    private BigDecimal discountAmount;
    /**
     * 订单套餐ID
     */
    @TableField(value = "cash_thail_id", condition = LIKE)
    private String cashThailId;
    /**
     * 整单分摊金额
     */
    @TableField(value = "assessed_amount", condition = EQUAL)
    private BigDecimal assessedAmount;
    /**
     * 已付金额
     */
    @TableField(value = "paid", condition = EQUAL)
    private BigDecimal paid;
    /**
     * 是否参与折扣 0 不参与 1 参与
     */
    @TableField(value = "is_discount", condition = EQUAL)
    private Boolean isDiscount;
    /**
     * 是否允许账户支付 0 不允许 1 允许
     */
    @TableField(value = "is_account", condition = EQUAL)
    private Boolean isAccount;
    /**
     * 合并订单ID
     */
    @TableField(value = "merge_cash_id", condition = EQUAL)
    private Long mergeCashId;
    /**
     * 是否拆单
     */
    @TableField(value = "is_split", condition = EQUAL)
    private Boolean isSplit;
    /**
     * 拆单ID
     */
    @TableField(value = "split_cash_id", condition = EQUAL)
    private Long splitCashId;
    /**
     * 会员卡ID
     */
    @TableField(value = "member_card_id", condition = EQUAL)
    private Long memberCardId;
    /**
     * 卡抵扣金额
     */
    @TableField(value = "card_deduct_amount", condition = EQUAL)
    private BigDecimal cardDeductAmount;
    /**
     * 储值卡ID
     */
    @TableField(value = "stored_card_id", condition = EQUAL)
    private Long storedCardId;
    /**
     * 快捷优惠ID
     */
    @TableField(value = "discount_template_id", condition = EQUAL)
    private Long discountTemplateId;
    /**
     * 折扣描述
     */
    @TableField(value = "discount_desc", condition = LIKE)
    private String discountDesc;
    /**
     * 改价类型
     */
    @TableField(value = "reform_price_type", condition = LIKE)
    private String reformPriceType;
    /**
     * 改价价格
     */
    @TableField(value = "reform_price", condition = EQUAL)
    private BigDecimal reformPrice;
    /**
     * 设备码
     */
    @TableField(value = "sn", condition = LIKE)
    private String sn;
    /**
     * 是否修改时长
     */
    @TableField(value = "is_modify_duration", condition = EQUAL)
    private Boolean isModifyDuration;
    /**
     * 退款金额
     */
    @TableField(value = "refund_amount", condition = EQUAL)
    private BigDecimal refundAmount;
    /**
     * 暂停时长
     */
    @TableField(value = "stop_duration", condition = EQUAL)
    private Integer stopDuration;

    /**
     * 犹豫暂停时长
     */
    @TableField(value = "charging_stop_duration", condition = EQUAL)
    private Integer chargingStopDuration;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;
    /**
     * 周期数
     */
    @TableField(value = "cycle_num", condition = EQUAL)
    private Integer cycleNum;

    /**
     * 体验时长
     */
    @TableField(exist = false)
    private Integer experienceDuration;

    /**
     * 原始单价
     */
    @TableField(value = "old_price", condition = EQUAL)
    private BigDecimal oldPrice;

    /**
     * 原始原价
     */
    @TableField(value = "old_orgin_Price", condition = EQUAL)
    private BigDecimal oldOrginPrice;

    /**
     * 是否支付
     */
    @TableField(exist = false)
    private Boolean isPay = false;

    /**
     * 是否支付
     */
    @TableField(exist = false)
    private Boolean isGiftPay = false;

    /**
     * 拆单ID
     */
    @TableField(exist = false)
    private Long cashPowerId;

    /**
     * 拆单时长
     */
    @TableField(exist = false)
    private Integer splitValue;


}
