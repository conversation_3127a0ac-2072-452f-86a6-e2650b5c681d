package top.kx.kxss.pos.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.member.grade.MemberGrade;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 表单查询方法返回值VO
 * 设备SN实体
 * </p>
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@Builder
@ApiModel(value = "AccountDeductQuery", description = "设备SN实体")
public class AccountDeductQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "会员等级")
    private MemberGrade memberGrade;

    @ApiModelProperty(value = "会员等级")
    private MemberInfo memberInfo;

    @ApiModelProperty(value = "消耗金额")
    private BigDecimal amount;

}
