package top.kx.kxss.pos.entity.cash;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import java.math.BigDecimal;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 订单退款明细
 * </p>
 *
 * <AUTHOR>
 * @date 2025-02-25 14:09:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("pos_cash_refund_detail")
public class PosCashRefundDetail extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @TableField(value = "name", condition = LIKE)
    private String name;
    /**
     * 业务明细类型
     */
    @TableField(value = "biz_type", condition = LIKE)
    private String bizType;
    /**
     * 明细ID
     */
    @TableField(value = "source_id", condition = EQUAL)
    private Long sourceId;
    /**
     * 退款金额
     */
    @TableField(value = "amount", condition = EQUAL)
    private BigDecimal amount;
    /**
     * 数量
     */
    @TableField(value = "num", condition = EQUAL)
    private Integer num;
    /**
     * 订单ID
     */
    @TableField(value = "cash_id", condition = EQUAL)
    private Long cashId;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = EQUAL)
    private String remarks;


}
