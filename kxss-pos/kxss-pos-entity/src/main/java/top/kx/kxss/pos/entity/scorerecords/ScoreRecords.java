package top.kx.kxss.pos.entity.scorerecords;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;
import top.kx.basic.base.entity.Entity;


/**
 * <p>
 * 实体类
 * 积分记录
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-16 15:13:55
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("score_records")
public class ScoreRecords extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableField(value = "code", condition = LIKE)
    private String code;
    /**
     * 关联单据id(pos_cash)
     */
    @TableField(value = "cash_id", condition = EQUAL)
    private Long cashId;
    /**
     * 关联单据号(pos_cash)
     */
    @TableField(value = "cash_code", condition = LIKE)
    private String cashCode;
    /**
     * 兑换记录id
     */
    @TableField(value = "exchange_id", condition = EQUAL)
    private Long exchangeId;
    /**
     * 兑换描述(例如:冰红茶*3)
     */
    @TableField(value = "exchange_describe", condition = LIKE)
    private String exchangeDescribe;
    /**
     * 会员ID
     */
    @TableField(value = "member_id", condition = EQUAL)
    private Long memberId;

    @TableField(value = "employee_id", condition = EQUAL)
    private Long employeeId;
    /**
     * 变动积分
     */
    @TableField(value = "score", condition = EQUAL)
    private Integer score;
    /**
     * 当前积分
     */
    @TableField(value = "account_score", condition = EQUAL)
    private Integer accountScore;
    /**
     * 是否增加积分
     */
    @TableField(value = "is_add", condition = EQUAL)
    private Boolean isAdd;
    /**
     * 分类 0开台 1购物 2卡券 3充值 4调整 5兑换
     */
    @TableField(value = "type_", condition = LIKE)
    private String type;
    /**
     * 来源: 0-web 1-pos  2app
     */
    @TableField(value = "source_type", condition = LIKE)
    private String sourceType;
    /**
     * 状态: 1初始 2已完成 3结算失败
     */
    @TableField(value = "status", condition = LIKE)
    private String status;
    /**
     * 单据总金额
     */
    @TableField(value = "amount", condition = EQUAL)
    private BigDecimal amount;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;


    public static final String CODE = "code";
    public static final String CASH_ID = "cash_id";
    public static final String EXCHANGE_ID = "exchange_id";
    public static final String MEMBER_ID = "member_id";
    public static final String SCORE = "score";
    public static final String ACCOUNT_SCORE = "account_score";
    public static final String IS_ADD = "is_add";
    public static final String TYPE_ = "type_";
    public static final String AMOUNT = "amount";
    public static final String REMARKS = "remarks";
    public static final String CREATED_ORG_ID = "created_org_id";

}
