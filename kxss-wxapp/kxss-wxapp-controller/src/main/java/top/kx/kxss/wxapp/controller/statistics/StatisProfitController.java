package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.wxapp.service.statistics.StatisProfitService;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.StatisProfitResultVO;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/profit")
@AllArgsConstructor
@Api(value = "利润统计相关API", tags = "利润统计相关API")
public class StatisProfitController {

    @Autowired
    private StatisProfitService statisProfitService;


    @ApiOperation(value = "统计信息", notes = "统计信息")
    @PostMapping
    public R<StatisProfitResultVO> statistics(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(statisProfitService.statistics(query));
    }
}
