package top.kx.kxss.wxapp.controller.statistics;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.base.vo.result.member.MemberInfoExportVO;
import top.kx.kxss.wxapp.service.statistics.StatisCashierPerformanceService;
import top.kx.kxss.wxapp.service.statistics.StatisTimeRangeService;
import top.kx.kxss.wxapp.vo.query.statistics.CashierPerformanceQuery;
import top.kx.kxss.wxapp.vo.query.statistics.TimeRangeQuery;
import top.kx.kxss.wxapp.vo.result.statistics.StatisCashierPerformanceResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.StatisTimeRangeResultVO;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/cashierPerformance")
@AllArgsConstructor
@Api(value = "收银员业绩相关API", tags = "收银员业绩相关API")
public class StatisCashierPerformanceController {

    @Autowired
    private StatisCashierPerformanceService statisCashierPerformanceService;


    @ApiOperation(value = "统计", notes = "统计")
    @PostMapping("overview")
    public R<StatisCashierPerformanceResultVO> overview(@RequestBody @Validated CashierPerformanceQuery query) {
        return R.success(statisCashierPerformanceService.overview(query));
    }

    @ApiOperation(value = "收银员业绩列表-分页", notes = "收银员业绩列表-分页")
    @PostMapping("page")
    public R<IPage<StatisCashierPerformanceResultVO>> page(@RequestBody @Validated PageParams<CashierPerformanceQuery> query) {
        return R.success(statisCashierPerformanceService.page(query));
    }

    @ApiOperation(value = "收银员业绩列表", notes = "收银员业绩列表")
    @PostMapping("list")
    public R<List<StatisCashierPerformanceResultVO>> list(@RequestBody @Validated CashierPerformanceQuery query) {
        return R.success(statisCashierPerformanceService.list(query));
    }

    @ApiOperation(value = "收银员业绩-导出", notes = "收银员业绩-导出")
    @RequestMapping(value = "/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void export(@RequestBody @Validated CashierPerformanceQuery query, HttpServletResponse response) {
        List<StatisCashierPerformanceResultVO> list = statisCashierPerformanceService.list(query);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=Cashier Performance.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, StatisCashierPerformanceResultVO.class)
                    .sheet("sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }

}
