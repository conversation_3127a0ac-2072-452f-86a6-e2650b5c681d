package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.base.vo.query.member.card.MemberCardPageQuery;
import top.kx.kxss.base.vo.query.member.coupon.MemberCouponStatisPageQuery;
import top.kx.kxss.wxapp.service.statistics.StatisCardService;
import top.kx.kxss.wxapp.service.statistics.StatisCouponService;
import top.kx.kxss.wxapp.vo.query.statistics.BuyCardStatisQuery;
import top.kx.kxss.wxapp.vo.result.statistics.StatisCardOverviewResultVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/coupon")
@AllArgsConstructor
@Api(value = "优惠券统计相关API", tags = "优惠券统计相关API")
public class StatisCouponController {

    @Autowired
    private StatisCouponService statisCouponService;



    // 优惠劵名称 发放时间 发放方式 使用状态 会员名称  会员等级, 会员编码 手机号 核销时间 、适用范围

    @ApiOperation(value = "会员券", notes = "会员券")
    @PostMapping("/member")
    public R<Map<String, Object>> memberCoupon(@RequestBody PageParams<MemberCouponStatisPageQuery> params) {
        return R.success(statisCouponService.memberCoupon(params));
    }

    @ApiOperation(value = "会员券-导出", notes = "会员券-导出")
    @RequestMapping(value = "/member/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void memberCouponExport(@RequestBody MemberCouponStatisPageQuery params, HttpServletResponse response) {
        statisCouponService.memberCouponExport(params, response);
    }


}
