package top.kx.kxss.wxapp.controller.reward;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.base.vo.query.reward.RewardOrderPageQuery;
import top.kx.kxss.base.vo.result.reward.RewardOrderResultVO;
import top.kx.kxss.model.enumeration.pos.RewardOrderStatusEnum;
import top.kx.kxss.reward.RewardOrderApi;
import top.kx.kxss.wxapp.service.statistics.CustomService;

/**
 * 打赏API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/reward/emp")
@Api(value = "RewardEmpController", tags = "员工相关打赏API")
public class RewardEmpController {

    private final RewardOrderApi rewardOrderApi;
    private final CustomService customService;

    @ApiOperation(value = "员工对应打赏记录", notes = "员工对应打赏记录")
    @PostMapping("/page")
    public R<Page<RewardOrderResultVO>> orderPage(@RequestBody PageParams<RewardOrderPageQuery> params) {
        if (params.getModel() == null) {
            params.setModel(new RewardOrderPageQuery());
        }
        params.setOrder("descending");
        params.setSort("payTime");
        params.getModel().setStatus(RewardOrderStatusEnum.COMPLETE.getCode());
        params.getModel().setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        customService.storeTime(params.getModel());
        return rewardOrderApi.page(params);
    }

    @ApiOperation(value = "全部打赏记录", notes = "全部打赏记录")
    @PostMapping("/pageAll")
    public R<Page<RewardOrderResultVO>> pageAll(@RequestBody PageParams<RewardOrderPageQuery> params) {
        if (params.getModel() == null) {
            params.setModel(new RewardOrderPageQuery());
        }
        params.setOrder("descending");
        params.setSort("payTime");
        params.getModel().setStatus(RewardOrderStatusEnum.COMPLETE.getCode());
        params.getModel().setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        customService.storeTime(params.getModel());
        return rewardOrderApi.page(params);
    }

    @ApiOperation(value = "打赏总数", notes = "全部打赏记录")
    @PostMapping("/total")
    public R<JSONObject> total(@RequestBody RewardOrderPageQuery query) {
        customService.storeTime(query);
        return rewardOrderApi.total(query);
    }

}


