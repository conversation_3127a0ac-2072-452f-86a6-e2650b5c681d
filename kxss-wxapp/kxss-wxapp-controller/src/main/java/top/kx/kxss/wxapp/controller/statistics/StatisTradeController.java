package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.base.vo.result.member.MemberBalanceChangeResultVO;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.wxapp.service.statistics.StatisTradeService;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.*;

import java.util.List;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/trade")
@AllArgsConstructor
@Api(value = "交易数据汇总相关API", tags = "交易数据汇总相关API")
public class StatisTradeController {

    @Autowired
    private StatisTradeService statisTradeService;


    @ApiOperation(value = "概览", notes = "概览")
    @PostMapping("overview")
    public R<StatisTradeResultVO> overview(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(statisTradeService.overview(query));
    }

    @ApiOperation(value = "关键数据指标", notes = "关键数据指标")
    @PostMapping("crucial")
    public R<List<TradeCrucialResultVO>> crucial(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(statisTradeService.crucial(query));
    }

    @ApiOperation(value = "台桌类型", notes = "台桌类型")
    @PostMapping("tableType")
    public R<List<TradeTableTypeResultVO>> tableType(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(statisTradeService.tableType(query));
    }

    @ApiOperation(value = "经营效率", notes = "经营效率")
    @PostMapping("efficiency")
    public R<TradeEfficiencyResultVO> efficiency(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(statisTradeService.efficiency(query));
    }

    @ApiOperation(value = "商品概览", notes = "商品概览")
    @PostMapping("productOverview")
    public R<TradeProductResultVO> productOverview(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(statisTradeService.productOverview(query));
    }

    @ApiOperation(value = "商品畅销", notes = "商品畅销")
    @PostMapping("productPopular")
    public R<List<TradeProductPopularResultVO>> productPopular(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(statisTradeService.productPopular(query));
    }

    @ApiOperation(value = "商品库存", notes = "商品库存")
    @PostMapping("productStock")
    public R<TradeProductStockResultVO> productStock() {
        return R.success(statisTradeService.productStock());
    }

    @ApiOperation(value = "会员概览", notes = "会员概览")
    @PostMapping("memberOverview")
    public R<TradeMemberResultVO> memberOverview(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(statisTradeService.memberOverview(query));
    }


    @ApiOperation(value = "会员新增列表", notes = "会员新增列表")
    @PostMapping("newMemberList")
    public R<List<MemberInfoResultVO>> newMemberList(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(statisTradeService.newMemberList(query));
    }

    @ApiOperation(value = "会员删除列表", notes = "会员删除列表")
    @PostMapping("removeMemberList")
    public R<List<MemberInfoResultVO>> removeMemberList(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(statisTradeService.removeMemberList(query));
    }

    @ApiOperation(value = "会员余额调整列表", notes = "会员余额调整列表")
    @PostMapping("memberAmountChangeList")
    public R<List<MemberBalanceChangeResultVO>> memberAmountChangeList(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(statisTradeService.memberAmountChangeList(query));
    }

    @ApiOperation(value = "会员充值和消费", notes = "会员充值和消费")
    @PostMapping("memberRechargeConsume")
    public R<StatisTradeMemberResultVO> memberRechargeConsume(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(statisTradeService.memberRechargeConsume(query));
    }

    @ApiOperation(value = "会员等级", notes = "会员等级")
    @PostMapping("memberGrade")
    public R<List<TradeMemberGradeResultVO>> memberGrade() {
        return R.success(statisTradeService.memberGrade());
    }

}
