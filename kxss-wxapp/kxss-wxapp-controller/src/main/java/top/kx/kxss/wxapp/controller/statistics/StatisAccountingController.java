package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.wxapp.service.statistics.StatisAccountingService;
import top.kx.kxss.wxapp.service.statistics.StatisCashService;
import top.kx.kxss.wxapp.vo.query.statistics.AccountingMonthOverviewQuery;
import top.kx.kxss.wxapp.vo.query.statistics.CashStatsQuery;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.AccountingExpendOverviewResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.AccountingMonthOverviewResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.AccountingOverviewResultVO;
import top.kx.kxss.wxapp.vo.result.thail.StatisThailResultVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 笔记本收支统计 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/accounting")
@AllArgsConstructor
@Api(value = "记账本收支统计相关API", tags = "记账本收支统计相关API")
public class StatisAccountingController {

    @Autowired
    private StatisAccountingService statisAccountingService;


    @ApiOperation(value = "概览", notes = "概览")
    @PostMapping("/overview")
    public R<AccountingOverviewResultVO> overview(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(statisAccountingService.overview(query));
    }

    @ApiOperation(value = "营业能力月支出对比", notes = "营业能力月支出对比")
    @PostMapping("/incomeExpend")
    public R<List<AccountingMonthOverviewResultVO>> incomeExpend(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(statisAccountingService.incomeExpend(query));
    }

    @ApiOperation(value = "月支出排行", notes = "月支出排行")
    @PostMapping("/expendRise")
    public R<List<AccountingExpendOverviewResultVO>> expendRise(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(statisAccountingService.expendRise(query));
    }

    @ApiOperation(value = "月支出类型占比", notes = "月支出类型占比")
    @PostMapping("/expendOverview")
    public R<List<AccountingExpendOverviewResultVO>> expendOverview(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(statisAccountingService.expendOverview(query));
    }



}
