package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.wxapp.service.statistics.TurnoverService;
import top.kx.kxss.wxapp.vo.query.statistics.OverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.*;

import java.util.List;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/turnover")
@AllArgsConstructor
@Api(value = "营业额统计相关API", tags = "营业额相关API")
public class TurnoverController {
    @Autowired
    private TurnoverService turnoverService;

    @ApiOperation(value = "今日昨日统计", notes = "今日昨日统计")
    @PostMapping("/yestAndtoday")
    public R<TurnoverResultVO> yestAndtoday() {
        return R.success(turnoverService.yestAndtoday());
    }


    @ApiOperation(value = "营业概览", notes = "营业概览")
    @PostMapping("/overview")
    public R<OverviewResultVO> overview(@RequestBody @Validated OverviewQuery query) {
        return R.success(turnoverService.overview(query));
    }

    @ApiOperation(value = "营业构成(订单来源)", notes = "营业构成(订单来源)")
    @PostMapping("/constitute")
    public R<ChartResultVO> constitute(@RequestBody @Validated OverviewQuery query) {
        query.setSource("1");
        return R.success(turnoverService.constitute(query));
    }

    @ApiOperation(value = "营业构成(销售类型)", notes = "营业构成(销售类型)")
    @PostMapping("/saleType")
    public R<ChartResultVO> saleType(@RequestBody @Validated OverviewQuery query) {
        query.setSource("2");
        return R.success(turnoverService.constitute(query));
    }

    @ApiOperation(value = "营业构成(优惠类型)", notes = "营业构成(优惠类型)")
    @PostMapping("/discountType")
    public R<ChartResultVO> discountType(@RequestBody @Validated OverviewQuery query) {
        query.setSource("3");
        return R.success(turnoverService.constitute(query));
    }

    @ApiOperation(value = "营业构成(台桌类型)", notes = "营业构成(台桌类型)")
    @PostMapping("/tableType")
    public R<List<TableOverviewResultVO>> tableType(@RequestBody @Validated OverviewQuery query) {
        return R.success(turnoverService.tableType(query));
    }

    @ApiOperation(value = "营业构成(台桌区域)", notes = "营业构成(台桌区域)")
    @PostMapping("/tableArea")
    public R<List<TableOverviewResultVO>> tableArea(@RequestBody @Validated OverviewQuery query) {
        return R.success(turnoverService.tableArea(query));
    }

    @ApiOperation(value = "营业构成(套餐)", notes = "营业构成(套餐)")
    @PostMapping("/thail")
    public R<List<ThailOverviewResultVO>> thail(@RequestBody @Validated OverviewQuery query) {
        return R.success(turnoverService.thailType(query));
    }

    @ApiOperation(value = "营业构成(团购)", notes = "营业构成(tuangou )")
    @PostMapping("/groupBuyType")
    public R<List<GroupBuyOverviewResultVO>> groupBuyType(@RequestBody @Validated OverviewQuery query) {
        return R.success(turnoverService.groupBuyType(query));
    }

}
