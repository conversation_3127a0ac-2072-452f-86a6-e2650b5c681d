package top.kx.kxss.wxapp.controller.notice;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.member.card.MemberCard;
import top.kx.kxss.base.entity.member.coupon.MemberCoupon;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.service.member.card.MemberCardService;
import top.kx.kxss.base.service.member.coupon.MemberCouponService;
import top.kx.kxss.base.service.outin.BaseOutinService;
import top.kx.kxss.base.service.product.BaseProductService;
import top.kx.kxss.model.enumeration.base.CashCardStatusEnum;
import top.kx.kxss.model.enumeration.base.CouponStatusEnum;
import top.kx.kxss.msg.entity.ExtendNotice;
import top.kx.kxss.msg.enumeration.NoticeRemindModeEnum;
import top.kx.kxss.msg.service.ExtendNoticeService;
import top.kx.kxss.msg.vo.result.ExtendNoticeResultVO;
import top.kx.kxss.msg.vo.result.NoticeMsgResultVO;
import top.kx.kxss.notice.api.NoticeApi;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 收银通知
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-19 14:43:55
 * @create [2023-09-19 14:43:55] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/notice")
@Api(value = "Notice", tags = "收银通知API")
public class NoticeController {

    @Autowired
    private ExtendNoticeService extendNoticeService;
    @Autowired
    private BaseOutinService baseOutinService;
    @Autowired
    private BaseProductService baseProductService;
    @Autowired
    private MemberInfoService memberInfoService;
    @Autowired
    private EchoService echoService;
    @Autowired
    private MemberCouponService memberCouponService;
    @Autowired
    private MemberCardService memberCardService;
    @Autowired
    private NoticeApi noticeApi;


    @ApiOperation(value = "分页查询通知记录", notes = "分页查询通知记录")
    @PostMapping("/page")
    @WebLog(value = "'分页查询通知记录:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<ExtendNoticeResultVO>> queryList(@RequestBody PageParams<ExtendNotice> params) {
        params.setSort("");
        params.setOrder("");
        IPage<ExtendNotice> noticeList = params.buildPage(ExtendNotice.class);
        extendNoticeService.page(noticeList, Wraps.<ExtendNotice>lbQ()
                .eq(ExtendNotice::getBizType, params.getModel().getBizType())
                .eq(ExtendNotice::getRemindMode, NoticeRemindModeEnum.NOTICE.getValue())
                .eq(ExtendNotice::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(ExtendNotice::getRecipientId, ContextUtil.getEmployeeId())
                .inSql(StrUtil.isNotBlank(params.getModel().getTemplateCode()), ExtendNotice::getMsgId,
                        "select id from extend_msg where delete_flag =0 and template_code ='"
                                + params.getModel().getTemplateCode() + "'")
                .orderByDesc(ExtendNotice::getCreatedTime));
        IPage<ExtendNoticeResultVO> beanPage = BeanPlusUtil.toBeanPage(noticeList, ExtendNoticeResultVO.class);
        echoService.action(beanPage);
        return R.success(beanPage);
    }

    @ApiOperation(value = "未读数", notes = "未读数")
    @PostMapping("/count")
    @WebLog(value = "未读数")
    public R<Long> count() {
        long count = extendNoticeService.count(Wraps.<ExtendNotice>lbQ()
                .eq(ExtendNotice::getRemindMode, NoticeRemindModeEnum.NOTICE.getValue())
                .eq(ExtendNotice::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(ExtendNotice::getRecipientId, ContextUtil.getEmployeeId())
                .eq(ExtendNotice::getIsRead, false)
        );
        return R.success(count);
    }

    @ApiOperation(value = "未读数", notes = "未读数")
    @PostMapping("/countCode")
    @WebLog(value = "未读数")
    public R<Map<String, Object>> countCode(@RequestBody List<String> templateCodeList) {
        QueryWrap<ExtendNotice> wrap = new QueryWrap<>();
        wrap.eq("n.remind_mode", NoticeRemindModeEnum.NOTICE.getValue());
        wrap.eq("n.created_org_id", ContextUtil.getCurrentCompanyId());
        wrap.eq("n.recipient_id", ContextUtil.getEmployeeId());
        wrap.in("m.template_code", templateCodeList);
        wrap.eq("n.is_read", false);
        wrap.groupBy("m.template_code");
        Map<String, Object> map = MapUtil.newHashMap();
        List<NoticeMsgResultVO> managerMap = extendNoticeService.getMapData(wrap);
        for (String string : templateCodeList) {
            Long count = 0L;
            if (CollUtil.isNotEmpty(managerMap)) {
                List<NoticeMsgResultVO> mapList = managerMap.stream().filter(v -> v.getTemplateCode().equals(string)).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(mapList)) {
                    count = mapList.get(0).getCount();
                }
            }
            map.put(string, count);
        }
        // 待审核的数量
        map.put("OUTIN_NO_REVIEWED", baseOutinService.noReviewedCount());
        //低库存预警数量
        QueryWrap<BaseProduct> queryWrapper = new QueryWrap<>();
        queryWrapper.eq("p.delete_flag", 0);
        queryWrapper.eq("p.created_org_id", ContextUtil.getCurrentCompanyId());
        queryWrapper.isNotNull("p.min_stock");
        queryWrapper.apply("p.min_stock >= (s.num - s.lock_num)");
        map.put("MIN_PRODUCT_STOCK", baseProductService.selectStockCount(queryWrapper));
        //高库存预警数量
        queryWrapper = new QueryWrap<>();
        queryWrapper.eq("p.delete_flag", 0);
        queryWrapper.eq("p.created_org_id", ContextUtil.getCurrentCompanyId());
        queryWrapper.isNotNull("p.max_stock");
        queryWrapper.apply("p.max_stock <= (s.num - s.lock_num)");
        map.put("MAX_PRODUCT_STOCK", baseProductService.selectStockCount(queryWrapper));
        //过期的会员
        LbQueryWrap<MemberInfo> eq = Wraps.<MemberInfo>lbQ()
                .eq(MemberInfo::getCreatedOrgId, ContextUtil.getCurrentCompanyId());
        String today = LocalDate.now().toString();
        eq.isNotNull(MemberInfo::getValidityDate);
        eq.lt(MemberInfo::getValidityDate, today);
        eq.eq(MemberInfo::getDeleteFlag, 0);
        map.put("EXPIRED_MEMBER", memberInfoService.count(eq));
        //即将过期的会员
        eq = Wraps.<MemberInfo>lbQ()
                .eq(MemberInfo::getCreatedOrgId, ContextUtil.getCurrentCompanyId());
        eq.isNotNull(MemberInfo::getValidityDate);
        String tomorrow = LocalDate.now().plusDays(6).toString();
        eq.between(MemberInfo::getValidityDate, today, tomorrow);
        eq.eq(MemberInfo::getDeleteFlag, 0);
        map.put("SOON_EXPIRED_MEMBER", memberInfoService.count(eq));
        //近7天过生日的会员
        eq = Wraps.<MemberInfo>lbQ()
                .eq(MemberInfo::getCreatedOrgId, ContextUtil.getCurrentCompanyId());
        eq.eq(MemberInfo::getDeleteFlag, 0);
        eq.isNotNull(MemberInfo::getBirth);
        String sql = "DATE_FORMAT(STR_TO_DATE(birth, '%Y-%m-%d'), '%m-%d') >= DATE_FORMAT(CURDATE(), '%m-%d') " +
                "  AND " +
                "  DATE_FORMAT(STR_TO_DATE(birth, '%Y-%m-%d'), '%m-%d') <= DATE_FORMAT(DATE_ADD(CURDATE(), INTERVAL 7 DAY), '%m-%d')";
        eq.apply(sql);
        map.put("SOON_BIRTH_MEMBER", memberInfoService.count(eq));
        //账户为0的会员
        eq = Wraps.<MemberInfo>lbQ()
                .eq(MemberInfo::getCreatedOrgId, ContextUtil.getCurrentCompanyId());
        eq.eq(MemberInfo::getDeleteFlag, 0);
        List<Long> collect = Lists.newArrayList();
        List<Long> collect1 = memberCouponService.list(Wraps.<MemberCoupon>lbQ().eq(MemberCoupon::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                        .in(MemberCoupon::getStatus, Arrays.asList(CouponStatusEnum.LOCK.getCode(),
                                CouponStatusEnum.RUNNING.getCode()
                        ))
                        .eq(MemberCoupon::getDeleteFlag, 0))
                .stream().map(MemberCoupon::getMemberId).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect1)) {
            collect.addAll(collect1);
        }
        collect1 = memberCardService.list(Wraps.<MemberCard>lbQ().eq(MemberCard::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                        .in(MemberCard::getStatus, Arrays.asList(CashCardStatusEnum.NO_ACTIVATE.getCode(),
                                CashCardStatusEnum.IN_USE.getCode()
                        ))
                        .eq(MemberCard::getDeleteFlag, 0))
                .stream().map(MemberCard::getMemberId).distinct().collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect1)) {
            collect.addAll(collect1);
        }
        eq.notIn(CollUtil.isNotEmpty(collect), MemberInfo::getId, collect.stream().distinct().collect(Collectors.toList()));
        eq.apply(" IFNULL(recharge_amount,0) + IFNULL(gift_amount,0) <= 0");
        map.put("ACCOUNT_EXPIRE_MEMBER", memberInfoService.count(eq));
        return R.success(map);
    }


    /**
     * 标记消息为已读
     *
     * @param noticeIds 主表id
     * @return 是否成功
     */
    @ApiOperation(value = "标记消息为已读", notes = "标记消息为已读")
    @PostMapping(value = "/mark")
    public R<Boolean> mark(@RequestBody List<Long> noticeIds) {
        return noticeApi.mark(noticeIds);
    }

    /**
     * 全部已读
     *
     * @return 是否成功
     */
    @ApiOperation(value = "全部已读", notes = "全部已读")
    @PostMapping(value = "/markAll")
    public R<Boolean> markAll() {
        return noticeApi.markAll();
    }

    /**
     * 删除消息中心
     *
     * @param receiveIds 接收id
     * @return 删除结果
     */
    @ApiOperation(value = "删除我的消息", notes = "根据id物理删除我的消息")
    @DeleteMapping("/deleteMyNotice")
    @WebLog("删除我的消息")
    public R<Boolean> deleteMyMsg(@RequestBody List<Long> receiveIds) {
        return noticeApi.deleteMyMsg(receiveIds);
    }

    public static void main(String[] args) {
        System.out.println(String.join("','", Arrays.asList("POS_MSG", "POS_MSG11")));
    }
}


