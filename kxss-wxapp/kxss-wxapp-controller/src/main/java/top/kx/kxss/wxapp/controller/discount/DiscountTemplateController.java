package top.kx.kxss.wxapp.controller.discount;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.discount.BaseDiscountTemplate;
import top.kx.kxss.base.service.discount.BaseDiscountTemplateService;
import top.kx.kxss.base.vo.BaseStateVO;
import top.kx.kxss.base.vo.query.discount.BaseDiscountTemplatePageQuery;
import top.kx.kxss.base.vo.result.discount.BaseDiscountTemplateResultVO;
import top.kx.kxss.base.vo.save.discount.BaseDiscountTemplateSaveVO;
import top.kx.kxss.base.vo.update.discount.BaseDiscountTemplateUpdateVO;
import top.kx.kxss.discount.DiscountTemplateApi;

import java.util.List;

/**
 * 优惠方式相关API Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/discount/template")
@AllArgsConstructor
@Api(value = "/wxapp/discount/template", tags = "优惠方式相关API")
public class DiscountTemplateController {
    @Autowired
    private DiscountTemplateApi discountTemplateApi;

    @Autowired
    private BaseDiscountTemplateService bookstoreTemplateService;

    @ApiOperation(value = "分页", notes = "分页")
    @PostMapping("/page")
    public R<Page<BaseDiscountTemplateResultVO>> page(@RequestBody @Validated PageParams<BaseDiscountTemplatePageQuery> params) {
        params.setOrder("");
        params.setSort("");
        return discountTemplateApi.page(params);
    }

    @ApiOperation(value = "详情", notes = "详情")
    @GetMapping("/detail")
    public R<BaseDiscountTemplateResultVO> detail(@RequestParam Long id) {
        //return discountTemplateApi.detail(id); 线上通过feign调用, 数据丢失
        return R.success(bookstoreTemplateService.getDetail(id));
    }

    @ApiOperation(value = "保存", notes = "保存")
    @PostMapping()
    public R<BaseDiscountTemplateResultVO> save(@RequestBody @Validated BaseDiscountTemplateSaveVO saveVO) {
        //return discountTemplateApi.save(saveVO);
        BaseDiscountTemplate baseDiscountTemplate = bookstoreTemplateService.saveTemplate(saveVO);
        return R.success(BeanPlusUtil.copyProperties(baseDiscountTemplate, BaseDiscountTemplateResultVO.class));
    }

    @ApiOperation(value = "更新", notes = "更新")
    @PutMapping()
    public R<BaseDiscountTemplateResultVO> update(@RequestBody @Validated BaseDiscountTemplateUpdateVO updateVO) {
        //return discountTemplateApi.update(updateVO);
        BaseDiscountTemplate baseDiscountTemplate = bookstoreTemplateService.updateTemplate(updateVO);
        return R.success(BeanPlusUtil.copyProperties(baseDiscountTemplate, BaseDiscountTemplateResultVO.class));
    }

    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping()
    public R<Boolean> delete(@RequestBody List<Long> idList) {
        return discountTemplateApi.delete(idList);
    }

    /**
     * 启用/停用
     *
     * @param model
     * @return
     */
    @ApiOperation(value = "启用/停用", notes = "启用/停用")
    @PostMapping("/state")
    public R<Boolean> state(@RequestBody BaseStateVO model) {
        return discountTemplateApi.state(model);
    }

}
