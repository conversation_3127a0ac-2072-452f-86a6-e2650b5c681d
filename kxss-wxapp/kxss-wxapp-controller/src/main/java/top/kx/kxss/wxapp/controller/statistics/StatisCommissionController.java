package top.kx.kxss.wxapp.controller.statistics;

import com.alibaba.excel.EasyExcel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.app.service.cash.PosCashServiceService;
import top.kx.kxss.wxapp.service.statistics.StatisCommissionService;
import top.kx.kxss.wxapp.vo.query.statistics.*;
import top.kx.kxss.wxapp.vo.result.statistics.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 提成 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/commission")
@AllArgsConstructor
@Api(value = "提成相关统计", tags = "提成相关统计")
public class StatisCommissionController {

    @Autowired
    private StatisCommissionService statisCommissionService;
    @Resource
    private PosCashServiceService posCashService;


    @ApiOperation(value = "员工提成统计", notes = "员工提成统计")
    @PostMapping("/employeeList")
    public R<List<EmployeeCommissionResultVO>> employeeList(@RequestBody @Validated CommissionStatsQuery params) {
        return R.success(statisCommissionService.employeeCommissionList(params));
    }

    @ApiOperation(value = "员工商品提成列表", notes = "员工商品提成")
    @PostMapping("/productCommission")
    public R<List<EmployeeCommissionResultVO>> productCommission(@RequestBody @Validated CommissionStatsQuery params) {
        return R.success(statisCommissionService.productCommission(params));
    }

    @ApiOperation(value = "员工充值提成列表", notes = "员工充值提成")
    @PostMapping("/rechargeCommission")
    public R<List<EmployeeCommissionResultVO>> rechargeCommission(@RequestBody @Validated CommissionStatsQuery params) {
        return R.success(statisCommissionService.rechargeCommission(params));
    }

    @ApiOperation(value = "员工提成统计-导出", notes = "员工提成统计-导出")
    @RequestMapping(value = "/employeeList/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void employeeListExport(@RequestBody @Validated CommissionStatsQuery params, HttpServletResponse response) {
        List<EmployeeCommissionResultVO> resultVOList = statisCommissionService.employeeCommissionList(params);
        resultVOList.forEach(s-> {
            s.setServiceCycleAchievementHour(posCashService.serviceDurationDesc(s.getServiceCycleAchievementHour()));
            s.setServiceDurationAchievementHour(posCashService.serviceDurationDesc(s.getServiceDurationAchievementHour()));
        });
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=EmployeeCommission.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, EmployeeCommissionResultVO.class)
                    .sheet("sheet1")
                    .doWrite(resultVOList);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "员工薪资", notes = "员工薪资")
    @PostMapping("/employeeSalary")
    public R<List<EmployeeSalaryResultVO>> employeeSalary(@RequestBody @Validated CommissionStatsQuery params) {
        return R.success(statisCommissionService.employeeSalary(params));
    }

    @ApiOperation(value = "员工薪资-分页", notes = "员工薪资-分页")
    @PostMapping("/employeeSalary/page")
    public R<Map<String, Object>> employeeSalaryPage(@RequestBody @Validated PageParams<CommissionStatsQuery> params) {
        return R.success(statisCommissionService.employeeSalaryPage(params));
    }


    @ApiOperation(value = "员工薪资导出", notes = "员工薪资导出 - 动态三级表头")
    @RequestMapping(value = "/employeeSalary/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void employeeSalaryExport(@RequestBody @Validated CommissionStatsQuery params, HttpServletResponse response) {
        statisCommissionService.employeeSalaryExport(params, response);
    }


//    @ApiOperation(value = "商品提成详情", notes = "商品提成详情")
//    @PostMapping("/productCommission/details")
//    public R<Map<String, Object>> productCommissionDetails(@RequestBody PageParams<InventoryFlowQuery> params) {
//        return R.success(statisCommissionService.productCommissionDetails(params));
//    }
//
//    @ApiOperation(value = "商品提成详情统计", notes = "商品提成详情统计")
//    @PostMapping("/productCommission/details/sum")
//    public R<ProductCommissionDetailResultVO> productCommissionDetailsSum(@RequestBody InventoryFlowQuery params) {
//        return R.success(statisCommissionService.productCommissionDetailsSum(params));
//    }
//
//    @ApiOperation(value = "商品提成详情-导出", notes = "商品提成详情-导出")
//    @RequestMapping(value = "/productCommission/details/export", method = RequestMethod.POST, produces = "application/octet-stream")
//    public void productCommissionDetailsExport(@RequestBody @Validated InventoryFlowQuery query, HttpServletResponse response) {
//        List<ProductCommissionDetailResultVO> list = statisCommissionService.productCommissionDetailsList(query);
//        response.setContentType("application/vnd.ms-excel");
//        response.setCharacterEncoding("utf8");
//        response.setHeader("Content-disposition", "attachment;filename=ProductCommission.xlsx");
//        try (ServletOutputStream outputStream = response.getOutputStream()) {
//            EasyExcel.write(outputStream, ProductCommissionDetailResultVO.class)
//                    .sheet("sheet1")
//                    .doWrite(list);
//        } catch (IOException e) {
//            log.error("导出失败", e);
//            throw new RuntimeException(e);
//        }
//    }

//    @ApiOperation(value = "会员充值提成详情", notes = "会员充值提成详情")
//    @PostMapping("/rechargeCommission/details")
//    public R<Map<String, Object>> rechargeCommissionDetails(@RequestBody PageParams<MemberDetailsQuery> params) {
//        return R.success(statisCommissionService.rechargeCommissionDetails(params));
//    }
//
//    @ApiOperation(value = "会员充值提成统计", notes = "会员充值提成统计")
//    @PostMapping("/rechargeCommission/details/sum")
//    public R<RechargeCommissionDetailResultVO> rechargeCommissionDetailsSum(@RequestBody MemberDetailsQuery params) {
//        return R.success(statisCommissionService.rechargeCommissionDetailsSum(params));
//    }
//
//    @ApiOperation(value = "会员充值提成-导出", notes = "会员充值提成-导出")
//    @RequestMapping(value = "/rechargeCommission/details/export", method = RequestMethod.POST, produces = "application/octet-stream")
//    public void rechargeCommissionDetailsExport(@RequestBody @Validated MemberDetailsQuery query, HttpServletResponse response) {
//        List<RechargeCommissionDetailResultVO> list = statisCommissionService.rechargeCommissionDetailsList(query);
//        response.setContentType("application/vnd.ms-excel");
//        response.setCharacterEncoding("utf8");
//        response.setHeader("Content-disposition", "attachment;filename=RechargeCommission.xlsx");
//        try (ServletOutputStream outputStream = response.getOutputStream()) {
//            EasyExcel.write(outputStream, RechargeCommissionDetailResultVO.class)
//                    .sheet("sheet1")
//                    .doWrite(list);
//        } catch (IOException e) {
//            log.error("导出失败", e);
//            throw new RuntimeException(e);
//        }
//    }

//    @ApiOperation(value = "服务提成明细", notes = "服务提成明细")
//    @PostMapping("/serviceCommission/details")
//    public R<Map<String, Object>> serviceCommissionDetails(@RequestBody PageParams<ServiceDetailsQuery> params) {
//        return R.success(statisCommissionService.serviceCommissionDetails(params));
//    }
//
//    @ApiOperation(value = "服务提成明细-统计", notes = "服务提成明细-统计")
//    @PostMapping("/serviceCommission/details/sum")
//    public R<ServiceCommissionDetailResultVO> serviceCommissionDetailsSum(@RequestBody ServiceDetailsQuery params) {
//        return R.success(statisCommissionService.serviceCommissionDetailsSum(params));
//    }
//
//    @ApiOperation(value = "服务提成明细-导出", notes = "服务提成明细-导出")
//    @RequestMapping(value = "/serviceCommission/details/export", method = RequestMethod.POST, produces = "application/octet-stream")
//    public void serviceCommissionDetailsExport(@RequestBody @Validated ServiceDetailsQuery query, HttpServletResponse response) {
//        List<ServiceCommissionDetailResultVO> list = statisCommissionService.serviceCommissionDetailsList(query);
//        list.forEach(s-> {
//            s.setServiceDurationHourAchievement(posCashService.serviceDurationDesc(s.getServiceDurationHourAchievement()));
//            s.setServiceCycleHourAchievement(posCashService.serviceDurationDesc(s.getServiceCycleHourAchievement()));
//        });
//        response.setContentType("application/vnd.ms-excel");
//        response.setCharacterEncoding("utf8");
//        response.setHeader("Content-disposition", "attachment;filename=ServiceCommission.xlsx");
//        try (ServletOutputStream outputStream = response.getOutputStream()) {
//            EasyExcel.write(outputStream, ServiceCommissionDetailResultVO.class)
//                    .sheet("sheet1")
//                    .doWrite(list);
//        } catch (IOException e) {
//            log.error("导出失败", e);
//            throw new RuntimeException(e);
//        }
//    }
//
//    @ApiOperation(value = "提成统计汇总", notes = "提成统计汇总")
//    @PostMapping("sum")
//    public R<StatisCommissionResultVO> commissionSum(@RequestBody @Validated DataOverviewQuery params) {
//        return R.success(statisCommissionService.commissionSum(params));
//    }


    @ApiOperation(value = "门店营业提成", notes = "门店营业提成")
    @PostMapping("/storeTurnover")
    public R<List<StoreTurnoverCommissionResultVO>> storeTurnover(@RequestBody @Validated ServiceStoreTurnoverQuery params) {
        return R.success(statisCommissionService.storeTurnover(params));
    }

    @ApiOperation(value = "门店营业提成-导出", notes = "门店营业提成-导出")
    @RequestMapping(value = "/storeTurnover/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void storeTurnoverExport(@RequestBody @Validated ServiceStoreTurnoverQuery query, HttpServletResponse response) {
        List<StoreTurnoverCommissionResultVO> list = statisCommissionService.storeTurnover(query);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=StoreTurnover.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, StoreTurnoverCommissionResultVO.class)
                    .sheet("sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }

}
