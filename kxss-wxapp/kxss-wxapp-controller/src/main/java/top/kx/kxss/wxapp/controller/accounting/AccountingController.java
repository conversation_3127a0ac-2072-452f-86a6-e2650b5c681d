package top.kx.kxss.wxapp.controller.accounting;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.base.service.accounting.BaseAccountingDateService;
import top.kx.kxss.base.vo.query.accounting.AccountingQuery;
import top.kx.kxss.base.vo.query.accounting.BaseAccountingDatePageQuery;
import top.kx.kxss.base.vo.result.accounting.*;
import top.kx.kxss.base.vo.save.accounting.BaseAccountingSaveVO;
import top.kx.kxss.base.vo.update.accounting.BaseAccountingUpdateVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * 轮播图
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/accounting")
@Api(value = "记账相关API", tags = "记账相关API")
public class AccountingController {

    @Autowired
    private BaseAccountingDateService baseAccountingDateService;


    @ApiOperation(value = "分页查询记账记录", notes = "分页查询记账记录")
    @PostMapping("/page")
    @WebLog(value = "'分页查询记账记录:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<BaseAccountingResultVO>> page(@RequestBody @Validated PageParams<BaseAccountingDatePageQuery> params) {
        return R.success(baseAccountingDateService.pageList(params));
    }

    @ApiOperation(value = "记账记录统计", notes = "记账记录统计")
    @PostMapping("/statistics")
    public R<Map<String,Object>> statistics(@RequestBody @Validated BaseAccountingDatePageQuery query) {
        return R.success(baseAccountingDateService.statistics(query));
    }

    @ApiOperation(value = "新增记账", notes = "新增记账")
    @PostMapping("/save")
    @WebLog("新增记账")
    public R<Boolean> save(@RequestBody @Validated BaseAccountingSaveVO model) {
        return R.success(baseAccountingDateService.saveAccounting(model));
    }

    @ApiOperation(value = "修改记账", notes = "修改记账")
    @PutMapping("/update")
    @WebLog("修改记账")
    public R<Boolean> update(@RequestBody @Validated BaseAccountingUpdateVO model) {
        return R.success(baseAccountingDateService.updateAccounting(model));
    }
    @ApiOperation(value = "查看详情", notes = "查看详情")
    @GetMapping("/detail/{id}")
    @WebLog("查看详情")
    public R<BaseAccountingVO> detail(@PathVariable Long id) {
        return R.success(baseAccountingDateService.detail(id));
    }


    @ApiOperation(value = "删除记账", notes = "删除记账")
    @DeleteMapping("/del/{id}")
    @WebLog("删除记账")
    public R<Boolean> del(@PathVariable Long id) {
        return R.success(baseAccountingDateService.del(id));
    }

    /**
     * 记账日历
     */
    @ApiOperation(value = "记账日历", notes = "记账日历")
    @PostMapping("/calendar")
    public R<List<AccountingCalenderResultVO>> calendar(@RequestBody @Validated AccountingQuery params) {
        return R.success(baseAccountingDateService.calendar(params));
    }

    @ApiOperation(value = "记账日历-总计", notes = "记账日历-总计")
    @PostMapping("/calendar/sum")
    public R<AccountingCalenderSumResultVO> calendarSum(@RequestBody @Validated AccountingQuery params) {
        return R.success(baseAccountingDateService.calendarSum(params));
    }


    @ApiOperation(value = "记账日历-详情", notes = "记账日历-详情")
    @PostMapping("/calendarDetail/page")
    public R<IPage<BaseAccountingInfoResultVO>> calendarDetail(@RequestBody @Validated PageParams<AccountingQuery> params) {
        AccountingQuery model = params.getModel();
        if (StringUtils.isBlank(model.getStartDate()) && StringUtils.isBlank(model.getEndDate())) {
            return R.success(new Page<>());
        }
        return R.success(baseAccountingDateService.calendarDetail(params));
    }

    @ApiOperation(value = "记账日历-详情", notes = "记账日历-详情")
    @PostMapping("/calendarDetail/list")
    public R<List<BaseAccountingInfoResultVO>> calendarDetailList(@RequestBody @Validated AccountingQuery model) {
        if (StringUtils.isBlank(model.getStartDate()) && StringUtils.isBlank(model.getEndDate())) {
            return R.success(new ArrayList<>());
        }
        return R.success(baseAccountingDateService.calendarDetailList(model));
    }


}


