package top.kx.kxss.wxapp.controller.statistics;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.app.vo.query.cash.PosCashIdQuery;
import top.kx.kxss.app.vo.result.cash.AmountResultVO;
import top.kx.kxss.app.vo.result.cash.StatisOrderResultVO;
import top.kx.kxss.model.enumeration.base.PosCashBillTypeEnum;
import top.kx.kxss.model.enumeration.pos.OrderQueryTypeEnum;
import top.kx.kxss.pos.PosOrderApi;
import top.kx.kxss.pos.query.order.OrderPageQuery;
import top.kx.kxss.pos.query.order.OrderQuery;
import top.kx.kxss.pos.query.order.SensitiveOrderQuery;
import top.kx.kxss.pos.vo.CashDetailResultVO;
import top.kx.kxss.wxapp.service.statistics.CustomService;
import top.kx.kxss.wxapp.service.statistics.StatisOrderService;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.query.statistics.OverviewQuery;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/order")
@AllArgsConstructor
@Api(value = "订单统计相关API", tags = "订单统计相关API")
public class StatisOrderController {
    @Autowired
    private StatisOrderService statisOrderService;
    @Autowired
    private PosOrderApi posOrderApi;
    @Autowired
    private CustomService customService;


    @ApiOperation(value = "概览", notes = "概览")
    @PostMapping("/overview")
    public R<AmountResultVO> overview(@RequestBody @Validated OverviewQuery query) {
        return R.success(statisOrderService.overview(query));
    }

    @ApiOperation(value = "订单列表（分页）", notes = "订单列表（分页）")
    @PostMapping(value = "/page")
    public R<Page<top.kx.kxss.pos.vo.order.OrderResultVO>> page(@RequestBody PageParams<OrderQuery> query) {
        DataOverviewQuery model = DataOverviewQuery.builder()
                .startDate(DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_TIME_START_FORMAT))
                .endDate(DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_TIME_START_FORMAT))
                .build();
        if (StrUtil.isNotBlank(query.getModel().getStartDateTime())) {
            model.setStartDate(query.getModel().getStartDateTime());
        }
        if (StrUtil.isNotBlank(query.getModel().getEndDateTime())) {
            model.setEndDate(query.getModel().getEndDateTime());
        }

        if (query.getModel().getStartDate() != null) {
            model.setStartDate(DateUtils.format(query.getModel().getStartDate(),
                    DateUtils.DEFAULT_DATE_FORMAT));
        }
        if (query.getModel().getEndDate() != null) {
            model.setEndDate(DateUtils.format(query.getModel().getEndDate(),
                    DateUtils.DEFAULT_DATE_FORMAT));
        }
        customService.storeTime(model);
        DateTimeFormatter dft = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        query.getModel().setStartTime(LocalDateTime.parse(model.getStartDate(), dft));
        query.getModel().setEndTime(LocalDateTime.parse(model.getEndDate(), dft));
        if (StrUtil.isBlank(query.getModel().getOrderQueryType())) {
            query.getModel().setOrderQueryType(OrderQueryTypeEnum.COMPLETE_TIME.getCode());
        }
        query.getModel().setBillTypes(Arrays.asList(PosCashBillTypeEnum.REGULAR_SINGLE.getCode()
                , PosCashBillTypeEnum.CANCELLATION.getCode(), PosCashBillTypeEnum.REGISTRATION.getCode()
                , PosCashBillTypeEnum.COUNTER_CHECKOUT.getCode()));
        return posOrderApi.queryList(query);
    }

    @ApiOperation(value = "敏感操作订单", notes = "敏感操作订单")
    @PostMapping(value = "/sensitivePage")
    public R<Page<top.kx.kxss.pos.vo.order.OrderResultVO>> sensitivePage(@RequestBody @Validated  PageParams<SensitiveOrderQuery> query) {
        return posOrderApi.sensitivePage(query);

    }

    @ApiOperation(value = "订单列表（分页）--NEW", notes = "订单列表（分页）--NEW")
    @PostMapping(value = "/pageNew")
    public R<Page<top.kx.kxss.pos.vo.order.OrderResultVO>> pageNew(@RequestBody PageParams<OrderPageQuery> params) {
        return posOrderApi.page(params);
    }

    @ApiOperation(value = "订单详情--NEW", notes = "订单详情--NEW")
    @PostMapping(value = "/detail")
    public R<CashDetailResultVO> detail(@RequestBody @Validated PosCashIdQuery query) {
        return posOrderApi.detail(query);
    }

    @ApiOperation(value = "订单详情", notes = "订单详情")
    @PostMapping(value = "/detail/{posCashId}")
    public R<StatisOrderResultVO> detail(@PathVariable Long posCashId) {
        return R.success(statisOrderService.detail(posCashId));
    }

}
