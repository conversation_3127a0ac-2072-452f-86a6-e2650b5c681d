package top.kx.kxss.wxapp.controller.component;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.wxapp.service.component.CodeManagerService;
import top.kx.kxss.wxapp.vo.query.component.CodeCommitQuery;


/**
 * <p>
 * 微信服务商通知接口
 * </p>
 *
 * <AUTHOR>
 */

@Api(value = "/wxapp/code/manager", tags = "微信服务商通知接口")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/code/manager")
public class CodeManagerController {

    @Autowired
    private CodeManagerService codeManagerService;

    @ApiOperation(value = "上传代码并提交体验版", notes = "上传代码并提交体验版")
    @PostMapping(value = "/commit")
    public R<Boolean> commit(@RequestBody @Validated CodeCommitQuery query) {
        return R.success(codeManagerService.commit(query));
    }

}
