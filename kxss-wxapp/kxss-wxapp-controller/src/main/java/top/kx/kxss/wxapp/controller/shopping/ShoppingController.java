package top.kx.kxss.wxapp.controller.shopping;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.app.vo.query.cash.ShoppingCheckStockQuery;
import top.kx.kxss.app.vo.query.cash.ShoppingCreatedQuery;
import top.kx.kxss.wxapp.service.shopping.WxShoppingService;
import top.kx.kxss.wxapp.vo.result.shopping.ShoppingCreatedResultVO;

/**
 * 充值相关API Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/shopping")
@AllArgsConstructor
@Api(value = "购物相关API", tags = "购物相关API")
public class ShoppingController {

    private final WxShoppingService wxShoppingService;


    @ApiOperation(value = "购物校验库存", notes = "购物校验库存")
    @PostMapping("/checkStock")
    public R<Boolean> checkStock(@RequestBody @Validated ShoppingCheckStockQuery query) {
        return R.success(wxShoppingService.checkStock(query));
    }

    @ApiOperation(value = "购物创建(小程序)", notes = "购物创建（小程序）")
    @PostMapping("/save/applet")
    public R<ShoppingCreatedResultVO> saveApplet(@RequestBody @Validated ShoppingCreatedQuery query) {
        return R.success(wxShoppingService.saveApplet(query));
    }

    @ApiOperation(value = "购物创建", notes = "购物创建")
    @PostMapping
    public R<ShoppingCreatedResultVO> save(@RequestBody @Validated ShoppingCreatedQuery query) {
        log.info("[购物创建] 当前设备:{},当前台桌:{}", ContextUtil.getSn()
                , query.getPosCashId());
        return R.success(wxShoppingService.save(query));
    }
}
