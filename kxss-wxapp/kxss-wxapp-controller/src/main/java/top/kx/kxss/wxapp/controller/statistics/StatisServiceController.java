package top.kx.kxss.wxapp.controller.statistics;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.wxapp.service.cash.WxPosCashServiceService;
import top.kx.kxss.wxapp.service.statistics.StatisServiceService;
import top.kx.kxss.wxapp.vo.query.cash.WxPosCashServicePageQuery;
import top.kx.kxss.wxapp.vo.query.statistics.OverviewQuery;
import top.kx.kxss.wxapp.vo.query.statistics.PerformanceMemberQuery;
import top.kx.kxss.wxapp.vo.query.statistics.StatisServiceQuery;
import top.kx.kxss.wxapp.vo.query.statistics.StatisServiceRankingQuery;
import top.kx.kxss.wxapp.vo.result.cash.WxPosCashServiceResultVO;
import top.kx.kxss.wxapp.vo.result.cash.WxPosCashServiceTotalResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/service")
@AllArgsConstructor
@Api(value = "服务统计相关API", tags = "服务统计相关API")
public class StatisServiceController {
    @Autowired
    private StatisServiceService serviceService;

    @Autowired
    private WxPosCashServiceService wxPosCashServiceService;

    @ApiOperation(value = "服务收入统计", notes = "服务收入统计")
    @PostMapping
    public R<List<StatisServiceResultVO>> statistics(@RequestBody @Validated StatisServiceQuery query) {
        return R.success(serviceService.statistics(query));
    }

    @ApiOperation(value = "服务收入统计", notes = "服务收入统计")
    @PostMapping("/listByService")
    public R<List<StatisServiceResultVO>> listByService(@RequestBody @Validated StatisServiceQuery query) {
        return R.success(serviceService.listByService(query));
    }

    @ApiOperation(value = "服务人员统计", notes = "服务人员统计")
    @PostMapping("/listByServicePerson")
    public R<List<StatisServiceResultVO>> listByServicePerson(@RequestBody @Validated StatisServiceQuery query) {
        return R.success(serviceService.listByServicePerson(query));
    }

    @ApiOperation(value = "服务收入统计-有表头", notes = "服务收入统计-有表头")
    @PostMapping("/list")
    public R<Map<String, Object>> statisticsList(@RequestBody PageParams<StatisServiceQuery> query) {
        return R.success(serviceService.statisticsList(query));
    }

    @ApiOperation(value = "服务收入统计-合计", notes = "服务收入统计-合计")
    @PostMapping("/list/sum")
    public R<StatiscsServiceListResultVO> statisticsListSum(@RequestBody StatisServiceQuery query) {
        return R.success(serviceService.statisticsListSum(query));
    }

    @ApiOperation(value = "服务收入统计-导出", notes = "服务收入统计-导出")
    @RequestMapping(value = "/list/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void statisticsListExport(@RequestBody StatisServiceQuery query, HttpServletResponse response) {
        serviceService.statisticsListExport(query, response);
    }

    @ApiOperation(value = "服务时长统计", notes = "服务时长统计")
    @PostMapping("/duration")
    public R<StripChartResultVO> duration(@RequestBody @Validated OverviewQuery query) {
        return R.success(serviceService.duration(query));
    }

    @ApiOperation(value = "服务时长统计榜单", notes = "服务时长统计榜单")
    @PostMapping("/durationRanking")
    public R<List<StatisServiceRankingResultVO>> durationRanking(@RequestBody @Validated StatisServiceRankingQuery query) {
        return R.success(serviceService.durationRanking(query));
    }
    @ApiOperation(value = "服务客户统计榜单", notes = "服务客户统计榜单")
    @PostMapping("/memberRanking")
    public R<IPage<StatisPerformanceMemberResultVO>> memberRanking(@RequestBody @Validated PageParams<PerformanceMemberQuery> query) {
        return R.success(serviceService.memberRanking(query));
    }


    @ApiOperation(value = "服务项目时长统计", notes = "服务项目时长统计")
    @PostMapping("/serviceDuration")
    public R<ChartResultVO> serviceDuration(@RequestBody @Validated OverviewQuery query) {
        return R.success(serviceService.serviceDuration(query));
    }

    @ApiOperation(value = "服务次数统计", notes = "服务次数统计")
    @PostMapping("/serviceTimes")
    public R<ChartResultVO> serviceNum(@RequestBody @Validated OverviewQuery query) {
        return R.success(serviceService.serviceNum(query));
    }

    @ApiOperation(value = "服务订单列表（分页）", notes = "服务订单列表（分页）")
    @PostMapping(value = "/order/list")
    public R<IPage<WxPosCashServiceResultVO>> servicePage(@RequestBody PageParams<WxPosCashServicePageQuery> params) {
        return R.success(wxPosCashServiceService.page(params));
    }

    @ApiOperation(value = "服务订单统计", notes = "服务订单统计")
    @PostMapping(value = "/order/total")
    public R<WxPosCashServiceTotalResultVO> servicePage(@RequestBody WxPosCashServicePageQuery params) {
        return R.success(wxPosCashServiceService.total(params));
    }


}
