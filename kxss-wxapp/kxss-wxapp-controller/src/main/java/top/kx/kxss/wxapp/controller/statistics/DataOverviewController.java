package top.kx.kxss.wxapp.controller.statistics;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.wxapp.service.statistics.DataOverviewService;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/data")
@AllArgsConstructor
@Api(value = "数据概览相关API", tags = "数据概览相关API")
public class DataOverviewController {

    @Resource
    private DataOverviewService dataOverviewService;

    @ApiOperation(value = "营业款", notes = "营业款")
    @PostMapping("/trade")
    public R<List<ChartResultVO.NameValueResultVO>> trade(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(dataOverviewService.trade(query));
    }

    @ApiOperation(value = "支付方式明细", notes = "支付方式明细")
    @PostMapping("/paymentTypeList")
    public R<List<PaymentTypeResultVO>> paymentTypeList(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(dataOverviewService.paymentTypeList(query));
    }

    @ApiOperation(value = "实际收入", notes = "实际收入")
    @PostMapping("/payment")
    public R<String> payment(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(dataOverviewService.payment(query));
    }

    @ApiOperation(value = "实际收入(new)", notes = "实际收入(new)")
    @PostMapping("/pay")
    public R<Map<String, String>> pay(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(dataOverviewService.pay(query));
    }

    @ApiOperation(value = "营业数据", notes = "营业数据")
    @PostMapping("/tradeData")
    public R<JSONObject> tradeData(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(dataOverviewService.tradeData(query));
    }

    // 支付方式分组
    @ApiOperation(value = "支付方式分组", notes = "支付方式分组")
    @PostMapping("/payType")
    public R<List<PayTypeResultVO>> payType(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(dataOverviewService.payType(query));
    }

    @ApiOperation(value = "销售收入", notes = "销售收入")
    @PostMapping("/revenue")
    public R<List<ChartResultVO.NameValueResultVO>> revenue(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(dataOverviewService.revenue(query));
    }

    @ApiOperation(value = "优惠明细", notes = "优惠明细")
    @PostMapping("/profit")
    public R<List<SensitiveVO>> profit(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(dataOverviewService.profit(query));
    }

    @ApiOperation(value = "其他信息", notes = "其他信息")
    @PostMapping("/other")
    public R<List<ChartResultVO.NameValueResultVO>> other(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(dataOverviewService.other(query));
    }

    @ApiOperation(value = "订单信息", notes = "订单信息")
    @PostMapping("/order")
    public R<List<ChartResultVO.NameValueResultVO>> order(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(dataOverviewService.order(query));
    }

    @ApiOperation(value = "敏感操作", notes = "敏感操作")
    @PostMapping("/sensitive")
    public R<List<SensitiveVO>> sensitive(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(dataOverviewService.sensitive(query));
    }

    @ApiOperation(value = "套餐统计", notes = "套餐统计")
    @PostMapping("/thail")
    public R<List<ChartResultVO.NameValueResultVO>> thail(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(dataOverviewService.thail(query));
    }

    @ApiOperation(value = "会员信息", notes = "会员信息")
    @PostMapping("/member")
    public R<MemberStatisResultVO> member(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(dataOverviewService.member(query));
    }

    @ApiOperation(value = "新客信息", notes = "新客信息")
    @PostMapping("/newCustomers")
    public R<NewCustomersStatisResultVO> newCustomers(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(dataOverviewService.newCustomers(query));
    }
}
