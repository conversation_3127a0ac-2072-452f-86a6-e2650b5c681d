package top.kx.kxss.wxapp.controller.commenter;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.app.cash.PosCashCommenterApi;
import top.kx.kxss.app.vo.save.cash.PosCashCommenterSaveVO;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/posCashCommenter")
@AllArgsConstructor
@Api(value = "添加提成人", tags = "添加提成人")
public class PosCashCommenterController {

    @Autowired
    private PosCashCommenterApi posCashCommenterApi;


    @ApiOperation(value = "批量新增提成人", notes = "建议每次只新增一次类型")
    @PostMapping(value = "/batchSave")
    public R<Boolean> batchSave(@RequestBody @Validated List<PosCashCommenterSaveVO> params) {
        return posCashCommenterApi.batchSave(params);
    }

}
