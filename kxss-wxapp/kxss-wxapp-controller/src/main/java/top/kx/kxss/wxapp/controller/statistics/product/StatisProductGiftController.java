package top.kx.kxss.wxapp.controller.statistics.product;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.wxapp.service.statistics.product.StatisProductGiftService;
import top.kx.kxss.wxapp.vo.query.statistics.product.StatisProductGiftPageQuery;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/productGift")
@AllArgsConstructor
@Api(value = "商品赠送明细相关API", tags = "商品赠送明细相关API")
public class StatisProductGiftController {

    @Autowired
    private StatisProductGiftService statisProductGiftService;


    @ApiOperation(value = "明细统计", notes = "明细统计")
    @PostMapping("/pageList")
    public R<Map<String, Object>> pageList(@RequestBody PageParams<StatisProductGiftPageQuery> params) {
        return R.success(statisProductGiftService.pageList(params));
    }

    @ApiOperation(value = "总数", notes = "总数")
    @PostMapping("/sum")
    public R<Map<String, Object>> pageSum(@RequestBody StatisProductGiftPageQuery query) {
        return R.success(statisProductGiftService.pageSum(query));
    }

    @ApiOperation(value = "商品赠送明细-导出", notes = "商品赠送明细-导出")
    @RequestMapping(value = "/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void export(@RequestBody StatisProductGiftPageQuery params, HttpServletResponse response) {
        statisProductGiftService.export(params, response);
    }


}
