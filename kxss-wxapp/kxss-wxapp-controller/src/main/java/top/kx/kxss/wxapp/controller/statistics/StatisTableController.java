package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.wxapp.service.statistics.StatisTableService;
import top.kx.kxss.wxapp.vo.query.statistics.OverviewQuery;
import top.kx.kxss.wxapp.vo.query.statistics.StatisTableQuery;
import top.kx.kxss.wxapp.vo.result.statistics.ChartResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.StatisAreaResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.StatisTableResultVO;
import top.kx.kxss.wxapp.vo.result.table.TableAreaResultVO;

import java.util.List;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/table")
@AllArgsConstructor
@Api(value = "台桌统计相关API", tags = "台桌统计相关API")
public class StatisTableController {

    @Autowired
    private StatisTableService statisTableService;

    @ApiOperation(value = "概览", notes = "概览")
    @PostMapping("/overview")
    public R<List<TableAreaResultVO>> overview(@RequestBody @Validated StatisTableQuery query) {
        return R.success(statisTableService.tableList(query));
    }

    @ApiOperation(value = "台桌统计", notes = "台桌统计")
    @PostMapping
    public R<List<StatisTableResultVO>> statistics(@RequestBody @Validated OverviewQuery query) {
        return R.success(statisTableService.statistics(query));
    }

    @ApiOperation(value = "台桌类型统计", notes = "台桌类型统计")
    @PostMapping("/type")
    public R<List<StatisTableResultVO>> statisticsType(@RequestBody @Validated OverviewQuery query) {
        return R.success(statisTableService.statisticsType(query));
    }

    @ApiOperation(value = "台桌区域统计", notes = "台桌区域统计")
    @PostMapping("/area")
    public R<List<ChartResultVO.NameValueResultVO>> statisticsArea(@RequestBody @Validated OverviewQuery query) {
        return R.success(statisTableService.statisticsArea(query));
    }

    @ApiOperation(value = "台桌区域统计--NEW", notes = "台桌区域统计--NEW")
    @PostMapping("/newArea")
    public R<ChartResultVO> statisticsNewArea(@RequestBody @Validated OverviewQuery query) {
        return R.success(statisTableService.statisticsNewArea(query));
    }

    @ApiOperation(value = "台桌欢迎度统计", notes = "台桌欢迎度统计")
    @PostMapping("/welcome")
    public R<List<StatisTableResultVO>> welcome(@RequestBody @Validated OverviewQuery query) {
        return R.success(statisTableService.welcome(query));
    }

    @ApiOperation(value = "台桌区域统计", notes = "台桌区域统计")
    @PostMapping("/tableArea")
    public R<List<StatisAreaResultVO>> tableArea(@RequestBody @Validated OverviewQuery query) {
        return R.success(statisTableService.tableArea(query));
    }

    @ApiOperation(value = "台桌次数(按类型统计)", notes = "台桌次数(按类型统计)")
    @PostMapping("/timesByType")
    public R<List<StatisTableResultVO>> timesByType(@RequestBody @Validated OverviewQuery query) {
        return R.success(statisTableService.timesByType(query));
    }

    @ApiOperation(value = "台桌次数(按台桌统计)", notes = "台桌次数(按台桌统计)")
    @PostMapping("/times")
    public R<List<StatisTableResultVO>> times(@RequestBody @Validated OverviewQuery query) {
        return R.success(statisTableService.times(query));
    }
}
