package top.kx.kxss.wxapp.controller.payment;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.app.query.PayQueryOrder;
import top.kx.kxss.app.query.payment.RefundQuery;
import top.kx.kxss.model.enumeration.base.OrderSourceEnum;
import top.kx.kxss.pos.TempOrderApi;
import top.kx.kxss.pos.vo.payment.PaymentResultVO;
import top.kx.kxss.wxapp.service.payment.PaymentService;
import top.kx.kxss.wxapp.vo.query.payment.PaymentQuery;

/**
 * <p>
 * 前端控制器
 * 支付类型
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-19 14:43:55
 * @create [2023-09-19 14:43:55] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/payment")
@Api(value = "payment", tags = "支付")
public class PaymentController {

    @Autowired
    private PaymentService paymentService;
    @Autowired
    private TempOrderApi tempOrderApi;

    @ApiOperation(value = "直接支付", notes = "直接支付")
    @PostMapping
    @WebLog("直接支付")
    public R<PaymentResultVO> pay(@RequestBody @Validated PaymentQuery query) {
        query.setOrderSourceEnum(StrUtil.isNotBlank(ContextUtil.getOrderSource())
                ? OrderSourceEnum.get(ContextUtil.getOrderSource()) : OrderSourceEnum.POS);
        return R.success(paymentService.pay(query));
    }

//    @ApiOperation(value = "订单支付失败", notes = "订单支付失败")
//    @PostMapping("/payFail")
//    @WebLog("订单支付失败")
//    public R<Boolean> payFail(@RequestBody @Validated PayFailResultVO payResultVO) {
//        payResultVO.setOrderSourceEnum(ObjectUtil.isNotNull(ContextUtil.getOrderSource())
//                ? OrderSourceEnum.get(ContextUtil.getOrderSource()) : OrderSourceEnum.POS);
//        return R.success(paymentService.payFail(payResultVO));
//    }

    @ApiOperation(value = "取消支付", notes = "取消支付")
    @PostMapping("/payFail")
    @WebLog("取消支付")
    public R<Boolean> cancel(@RequestBody @Validated PayQueryOrder query) {
        return R.success(paymentService.cancel(query));
    }

    @ApiOperation(value = "查询聚合支付是否成功", notes = "查询聚合支付是否成功")
    @PostMapping("/queryOrder")
    @WebLog("查询聚合支付是否成功")
    public R<String> queryOrder(@RequestBody @Validated PayQueryOrder query) {
        return R.success(paymentService.queryOrder(query));
    }

    @ApiOperation(value = "查询聚合支付是否成功", notes = "查询聚合支付是否成功")
    @PostMapping("/queryTempOrder")
    @WebLog("查询聚合支付是否成功")
    public R<JSONObject> queryTempOrder(@RequestBody @Validated PayQueryOrder query) {
        return tempOrderApi.queryOrder(query);
    }

    @ApiOperation(value = "整单退款", notes = "整单退款")
    @PostMapping("/refund")
    @WebLog("整单退款")
    public R<String> refund(@RequestBody @Validated RefundQuery query) {
        return R.success(paymentService.refund(query));
    }
}


