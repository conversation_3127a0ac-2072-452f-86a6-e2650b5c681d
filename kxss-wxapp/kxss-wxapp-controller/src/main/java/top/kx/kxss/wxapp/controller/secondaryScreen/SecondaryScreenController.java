package top.kx.kxss.wxapp.controller.secondaryScreen;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.secondaryScreen.BaseSecondaryScreen;
import top.kx.kxss.base.service.secondaryScreen.BaseSecondaryScreenService;
import top.kx.kxss.base.vo.query.secondaryScreen.BaseSecondaryScreenPageQuery;
import top.kx.kxss.base.vo.result.secondaryScreen.BaseSecondaryScreenResultVO;
import top.kx.kxss.base.vo.save.secondaryScreen.BaseSecondaryScreenSaveVO;
import top.kx.kxss.base.vo.update.secondaryScreen.BaseSecondaryScreenUpdateVO;
import top.kx.kxss.file.api.FileApi;
import top.kx.kxss.file.vo.result.FileResultVO;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * 副屏广告
 * </p>
 *
 * <AUTHOR>
 * @date 2023-10-10 11:36:15
 * @create [2023-10-10 11:36:15] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/secondaryScreen")
@Api(value = "SecondaryScreen", tags = "副屏广告")
public class SecondaryScreenController extends SuperController<BaseSecondaryScreenService, Long, BaseSecondaryScreen, BaseSecondaryScreenSaveVO,
        BaseSecondaryScreenUpdateVO, BaseSecondaryScreenPageQuery, BaseSecondaryScreenResultVO> {
    private final EchoService echoService;

    @Resource
    private FileApi fileApi;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Override
    public R<BaseSecondaryScreen> handlerSave(BaseSecondaryScreenSaveVO model) {
        model.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return super.handlerSave(model);
    }

    @Override
    public QueryWrap<BaseSecondaryScreen> handlerWrapper(BaseSecondaryScreen model, PageParams<BaseSecondaryScreenPageQuery> params) {
        model.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return super.handlerWrapper(model, params);
    }

    @Override
    public R<List<BaseSecondaryScreenResultVO>> query(BaseSecondaryScreenPageQuery data) {
        data.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return super.query(data);
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
    public R<BaseSecondaryScreen> add(@RequestPart(value = "file") MultipartFile file) {
        R<FileResultVO> secondaryScreen = fileApi.upload(file, "SECONDARY_SCREEN", null, null, null);
        if (secondaryScreen.getCode() != 0) {
            ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "上传失败");
        }
        BaseSecondaryScreenSaveVO model = BaseSecondaryScreenSaveVO.builder().build();
        model.setPicId(secondaryScreen.getData().getId());
        model.setPicUrl(secondaryScreen.getData().getUrl());
        model.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return save(model);
    }

    @Override
    public R<BaseSecondaryScreen> handlerUpdate(BaseSecondaryScreenUpdateVO baseSecondaryScreenUpdateVO) {
        return super.handlerUpdate(baseSecondaryScreenUpdateVO);
    }
}


