package top.kx.kxss.wxapp.controller.statistics;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.pos.query.report.ReportPaymentDetailsQuery;
import top.kx.kxss.wxapp.service.statistics.StatisticsPaymentService;
import top.kx.kxss.wxapp.vo.query.statistics.CashierPerformanceQuery;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.query.statistics.OverviewQuery;
import top.kx.kxss.wxapp.vo.query.statistics.PaymentOverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/payment")
@AllArgsConstructor
@Api(value = "收款统计相关API", tags = "收款相关API")
public class StatisticsPaymentController {

    @Autowired
    private StatisticsPaymentService paymentService;


    @ApiOperation(value = "收款概览", notes = "收款概览")
    @PostMapping("/overview")
    public R<PayOverviewResultVO> overview(@RequestBody @Validated OverviewQuery query) {
        return R.success(paymentService.overview(query));
    }

    @ApiOperation(value = "收款构成", notes = "收款构成")
    @PostMapping("/constitute")
    public R<ChartResultVO> constitute(@RequestBody @Validated OverviewQuery query) {
        //收款构成
        query.setSource("1");
        return R.success(paymentService.constitute(query));
    }
    @ApiOperation(value = "营业收入", notes = "收款构成")
    @PostMapping("/paid")
    public R<ChartResultVO> paid(@RequestBody @Validated OverviewQuery query) {
        //营业收入
        query.setSource("2");
        return R.success(paymentService.constitute(query));
    }
    @ApiOperation(value = "充值构成", notes = "充值构成")
    @PostMapping("/recharge")
    public R<ChartResultVO> recharge(@RequestBody @Validated OverviewQuery query) {
        //充值构成
        query.setSource("3");
        return R.success(paymentService.constitute(query));
    }


    @ApiOperation(value = "支付方式明细", notes = "支付方式明细")
    @PostMapping("/cashPayment/details")
    public R<IPage<PaymentDetailsResultVO>> paymentDetails(@RequestBody @Validated PageParams<ReportPaymentDetailsQuery> params) {
        return R.success(paymentService.paymentDetails(params));
    }

    @ApiOperation(value = "支付方式明细概览", notes = "支付方式明细概览")
    @PostMapping("/cashPayment/details/overview")
    public R<PaymentDetailsOverviewResultVO> paymentDetailsOverview(@RequestBody ReportPaymentDetailsQuery params) {
        return R.success(paymentService.paymentDetailsOverview(params));
    }

    // 收款统计
    @ApiOperation(value = "收款统计", notes = "收款统计")
    @PostMapping("/receivables")
    public R<PaymentReceivablesResultVO> receivables(@RequestBody @Validated DataOverviewQuery params) {
        return R.success(paymentService.receivables(params));
    }

    // 根据收款方式合计
    @ApiOperation(value = "支付方式收款统计", notes = "支付方式收款统计")
    @PostMapping("/receivables/payType")
    public R<List<PaymentReceivablesDetailsResultVO>> payType(@RequestBody @Validated PaymentOverviewQuery params) {
        return R.success(paymentService.payType(params));
    }
    @ApiOperation(value = "支付方式收款统计-导出", notes = "支付方式收款统计-导出")
    @RequestMapping(value = "/receivables/payType/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void export(@RequestBody @Validated PaymentOverviewQuery query, HttpServletResponse response) {
        List<PaymentReceivablesDetailsResultVO> list = paymentService.payType(query);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=PaymentType.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, PaymentReceivablesDetailsResultVO.class)
                    .sheet("sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }

    // 未完成的订单
    @ApiOperation(value = "未完成订单", notes = "未完成订单")
    @PostMapping("/unfinishedOrder")
    public R<PaymentUnfinishedResultVO> unfinishedOrder(@RequestBody @Validated PaymentOverviewQuery params) {
        return R.success(paymentService.unfinishedOrder(params));
    }



}
