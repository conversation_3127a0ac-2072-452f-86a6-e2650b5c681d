package top.kx.kxss.wxapp.controller.statistics;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.service.cash.PosCashServiceService;
import top.kx.kxss.wxapp.service.statistics.StatisPerformanceService;
import top.kx.kxss.wxapp.vo.query.statistics.*;
import top.kx.kxss.wxapp.vo.result.statistics.*;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 业绩统计 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/performance")
@AllArgsConstructor
@Api(value = "业绩统计相关API", tags = "业绩统计相关API")
public class StatisPerformanceController {
    @Autowired
    private StatisPerformanceService performanceService;
    @Resource
    private PosCashServiceService posCashService;

    @Deprecated
    @ApiOperation(value = "业绩排行榜", notes = "业绩排行榜")
    @PostMapping
    public R<IPage<StatisPerformanceResultVO>> performance(@RequestBody @Validated PageParams<PerformanceQuery> query) {
        return R.success(performanceService.performance(query));
    }

    @Deprecated
    @ApiOperation(value = "服务时长排行榜", notes = "服务时长排行榜")
    @PostMapping("/duration")
    public R<IPage<StatisPerformanceResultVO>> duration(@RequestBody @Validated PageParams<PerformanceQuery> query) {
        return R.success(performanceService.duration(query));
    }

    @Deprecated
    @ApiOperation(value = "个人业绩", notes = "个人业绩")
    @PostMapping("/listByEmployeeId")
    public R<IPage<StatisPerformanceInfoResultVO>> listByEmployeeId(@RequestBody @Validated PageParams<DataOverviewQuery> query) {
        return R.success(performanceService.listByEmployeeId(query));
    }

    @Deprecated
    @ApiOperation(value = "客户服务排行榜", notes = "客户服务排行榜")
    @PostMapping("/member")
    public R<IPage<StatisPerformanceMemberResultVO>> member(@RequestBody @Validated PageParams<PerformanceMemberQuery> query) {
        return R.success(performanceService.member(query));
    }

    @Deprecated
    @ApiOperation(value = "概览", notes = "概览")
    @PostMapping("/overview")
    public R<Map<String, String>> overview(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(performanceService.overview(query));
    }

    @Deprecated
    @ApiOperation(value = "充值明细", notes = "充值明细")
    @PostMapping("/rechargeList")
    public R<List<StatisPerformanceInfoResultVO>> rechargeList(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(performanceService.rechargeList(query));
    }

    @Deprecated
    @ApiOperation(value = "服务明细", notes = "服务明细")
    @PostMapping("/serviceList")
    public R<List<StatisPerformanceInfoResultVO>> serviceList(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(performanceService.serviceList(query));
    }

    @ApiOperation(value = "员工提成统计", notes = "员工提成统计 - 从base_performance表获取数据")
    @PostMapping("/employeeList")
    public R<List<EmployeeCommissionResultVO>> employeeCommissionList(@RequestBody @Validated CommissionStatsQuery params) {
        return R.success(performanceService.employeeCommissionList(params));
    }

    @ApiOperation(value = "员工商品提成列表", notes = "员工商品提成")
    @PostMapping("/productCommission")
    public R<List<EmployeeCommissionResultVO>> productCommission(@RequestBody @Validated CommissionStatsQuery params) {
        return R.success(performanceService.productCommission(params));
    }

    @ApiOperation(value = "员工充值提成列表", notes = "员工充值提成")
    @PostMapping("/rechargeCommission")
    public R<List<EmployeeCommissionResultVO>> rechargeCommission(@RequestBody @Validated CommissionStatsQuery params) {
        return R.success(performanceService.rechargeCommission(params));
    }

    @ApiOperation(value = "员工提成统计-导出", notes = "员工提成统计-导出")
    @RequestMapping(value = "/employeeList/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void employeeListExport(@RequestBody @Validated CommissionStatsQuery params, HttpServletResponse response) {
        List<EmployeeCommissionResultVO> resultVOList = performanceService.employeeCommissionList(params);
        resultVOList.forEach(s-> {
            s.setServiceCycleAchievementHour(posCashService.serviceDurationDesc(s.getServiceCycleAchievementHour()));
            s.setServiceDurationAchievementHour(posCashService.serviceDurationDesc(s.getServiceDurationAchievementHour()));
        });
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=EmployeeCommission.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, EmployeeCommissionResultVO.class)
                    .sheet("sheet1")
                    .doWrite(resultVOList);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "会员充值提成详情", notes = "会员充值提成详情")
    @PostMapping("/rechargeCommission/details")
    public R<Map<String, Object>> rechargeCommissionDetails(@RequestBody @Validated PageParams<CommissionDetailsQuery> params) {
        return R.success(performanceService.rechargeCommissionDetails(params));
    }

    @ApiOperation(value = "会员充值提成统计", notes = "会员充值提成统计")
    @PostMapping("/rechargeCommission/details/sum")
    public R<RechargeCommissionDetailResultVO> rechargeCommissionDetailsSum(@RequestBody @Validated CommissionDetailsQuery params) {
        return R.success(performanceService.rechargeCommissionDetailsSum(params));
    }

    @ApiOperation(value = "会员充值提成-导出", notes = "会员充值提成-导出")
    @RequestMapping(value = "/rechargeCommission/details/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void rechargeCommissionDetailsExport(@RequestBody @Validated CommissionDetailsQuery query, HttpServletResponse response) {
        List<RechargeCommissionDetailResultVO> list = performanceService.rechargeCommissionDetailsList(query);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=RechargeCommission.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, RechargeCommissionDetailResultVO.class)
                    .sheet("sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }


    @ApiOperation(value = "服务提成明细", notes = "服务提成明细")
    @PostMapping("/serviceCommission/details")
    public R<Map<String, Object>> serviceCommissionDetails(@RequestBody PageParams<CommissionDetailsQuery> params) {
        return R.success(performanceService.serviceCommissionDetails(params));
    }

    @ApiOperation(value = "服务提成明细-统计", notes = "服务提成明细-统计")
    @PostMapping("/serviceCommission/details/sum")
    public R<ServiceCommissionDetailResultVO> serviceCommissionDetailsSum(@RequestBody CommissionDetailsQuery params) {
        return R.success(performanceService.serviceCommissionDetailsSum(params));
    }

    @ApiOperation(value = "服务提成明细-导出", notes = "服务提成明细-导出")
    @RequestMapping(value = "/serviceCommission/details/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void serviceCommissionDetailsExport(@RequestBody @Validated CommissionDetailsQuery query, HttpServletResponse response) {
        List<ServiceCommissionDetailResultVO> list = performanceService.serviceCommissionDetailsList(query);
        list.forEach(s-> {
            s.setServiceDurationHourAchievement(posCashService.serviceDurationDesc(s.getServiceDurationHourAchievement()));
            s.setServiceCycleHourAchievement(posCashService.serviceDurationDesc(s.getServiceCycleHourAchievement()));
        });
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=ServiceCommission.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, ServiceCommissionDetailResultVO.class)
                    .sheet("sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }


    @ApiOperation(value = "商品提成详情", notes = "商品提成详情")
    @PostMapping("/productCommission/details")
    public R<Map<String, Object>> productCommissionDetails(@RequestBody PageParams<CommissionDetailsQuery> params) {
        return R.success(performanceService.productCommissionDetails(params));
    }

    @ApiOperation(value = "商品提成详情统计", notes = "商品提成详情统计")
    @PostMapping("/productCommission/details/sum")
    public R<ProductCommissionDetailResultVO> productCommissionDetailsSum(@RequestBody CommissionDetailsQuery params) {
        return R.success(performanceService.productCommissionDetailsSum(params));
    }

    @ApiOperation(value = "商品提成详情-导出", notes = "商品提成详情-导出")
    @RequestMapping(value = "/productCommission/details/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void productCommissionDetailsExport(@RequestBody @Validated CommissionDetailsQuery query, HttpServletResponse response) {
        List<ProductCommissionDetailResultVO> list = performanceService.productCommissionDetailsList(query);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=ProductCommission.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, ProductCommissionDetailResultVO.class)
                    .sheet("sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "整单提成", notes = "整单提成")
    @PostMapping("/wholeOrderCommission")
    public R<Map<String, Object>> wholeOrderCommission(@RequestBody @Validated PageParams<CommissionDetailsQuery> params) {
        return R.success(performanceService.wholeOrderCommission(params));
    }

    @ApiOperation(value = "整单提成统计", notes = "整单提成统计")
    @PostMapping("/wholeOrderCommission/sum")
    public R<WholeOrderCommissionResultVO> wholeOrderCommission(@RequestBody @Validated CommissionDetailsQuery params) {
        return R.success(performanceService.wholeOrderCommissionSum(params));
    }

    @ApiOperation(value = "整单提成-导出", notes = "整单提成-导出")
    @RequestMapping(value = "/wholeOrderCommission/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void wholeOrderCommissionExport(@RequestBody @Validated CommissionDetailsQuery query, HttpServletResponse response) {
        List<WholeOrderCommissionResultVO> list = performanceService.wholeOrderCommissionList(query);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=WholeOrderCommission.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, WholeOrderCommissionResultVO.class)
                    .sheet("sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "员工薪资", notes = "员工薪资")
    @PostMapping("/employeeSalary")
    public R<Map<String, Object>> employeeSalary(@RequestBody @Validated EmployeeSalaryQuery params) {
        return R.success(performanceService.employeeSalary(params));
    }

    @ApiOperation(value = "员工薪资统计", notes = "员工薪资统计")
    @PostMapping("/employeeSalary/sum")
    public R<Map<String, Object>> employeeSalarySum(@RequestBody @Validated EmployeeSalaryQuery params) {
        return R.success(performanceService.employeeSalarySum(params));
    }


    @ApiOperation(value = "员工薪资导出", notes = "员工薪资导出 - 动态三级表头")
    @RequestMapping(value = "/employeeSalary/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void employeeSalaryExport(@RequestBody @Validated EmployeeSalaryQuery params, HttpServletResponse response) {
        performanceService.employeeSalaryExport(params, response);
    }

    @ApiOperation(value = "月度提成统计", notes = "按月份统计提成人数和提成金额")
    @PostMapping("/monthlyCommissionStatistics")
    public R<Map<String, Object>> monthlyCommissionStatistics(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(performanceService.monthlyCommissionStatistics(query));
    }

    @ApiOperation(value = "月度提成统计", notes = "按月份统计提成人数和提成金额")
    @PostMapping("/monthlyCommissionStatistics/sum")
    public R<MonthlyCommissionStatisticsResultVO> monthlyCommissionStatisticsSum(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(performanceService.monthlyCommissionStatisticsSum(query));
    }

    @ApiOperation(value = "提成统计汇总", notes = "提成统计汇总")
    @PostMapping("sum")
    public R<StatisCommissionResultVO> commissionSum(@RequestBody @Validated DataOverviewQuery params) {
        return R.success(performanceService.commissionSum(params));
    }

}
