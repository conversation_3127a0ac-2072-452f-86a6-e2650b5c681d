package top.kx.kxss.wxapp.controller.polymerize;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.app.service.cash.payment.PosCashPaymentService;
import top.kx.kxss.pay.PayOrderApi;
import top.kx.kxss.pay.vo.query.ReconciliationPageQuery;
import top.kx.kxss.pay.vo.query.ReconciliationQuery;
import top.kx.kxss.pay.vo.result.PayOrderResultVO;
import top.kx.kxss.pay.vo.result.ReconciliationResultVO;
import top.kx.kxss.wxapp.vo.query.payment.PaymentTypeQuery;
import top.kx.kxss.wxapp.vo.result.payment.PaymentTypeStatisticsResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.PaymentDetailsResultVO;

@Slf4j
@RestController
@RequestMapping("/polymerize")
@AllArgsConstructor
@Api(value = "聚合支付对账", tags = "聚合支付对账相关接口")
public class PolymerizeController {

    @Autowired
    private PayOrderApi payOrderApi;

    @Autowired
    private PosCashPaymentService posCashPaymentService;


    @ApiOperation(value = "流水日或月统计", notes = "聚合支付按日,按月统计流水")
    @PostMapping("/statistics")
    public R<Page<ReconciliationResultVO>> polymerizeStatistics(@RequestBody PageParams<ReconciliationPageQuery> params) {
        params.getModel().setMchNoList(posCashPaymentService.getMchNoListByCurOrg());
        return payOrderApi.reconciliationStatistics(params);
    }

    @ApiOperation(value = "流水统计", notes = "聚合支付流水统计")
    @PostMapping("/sum")
    public R<ReconciliationResultVO> polymerizeSum(@RequestBody ReconciliationQuery params) {
        params.setMchNoList(posCashPaymentService.getMchNoListByCurOrg());
        return payOrderApi.reconciliation(params);
    }

    @ApiOperation(value = "流水列表", notes = "聚合支付流水列表")
    @PostMapping("/page")
    public R<Page<PayOrderResultVO>> polymerizePage(@RequestBody PageParams<ReconciliationQuery> params) {
        params.getModel().setMchNoList(posCashPaymentService.getMchNoListByCurOrg());
        return payOrderApi.reconciliationPage(params);
    }

    /**
     * 根据字符方式查询支统计支付金额
     * @param params
     * @return
     */
    @ApiOperation(value = "支付方式统计", notes = "聚合支付方式统计-指定时间内的支付方式金额汇总-包含手续费")
    @PostMapping("/cashPayment/statistics")
    public R<PaymentTypeStatisticsResultVO> statisticsPolymerize(@RequestBody PaymentTypeQuery params) {
        return R.success(posCashPaymentService.statisticsPolymerize(params));
    }

    @ApiOperation(value = "支付方式明细", notes = "聚合支付方式明细-指定时间内的支付记录")
    @PostMapping("/cashPayment/page")
    public R<IPage<PaymentDetailsResultVO>> cashPaymentPage(@RequestBody @Validated PageParams<PaymentTypeQuery> params) {
        return R.success(posCashPaymentService.polymerizeCashPaymentPage(params));
    }


    @ApiOperation(value = "订单导出", notes = "聚合订单导出-异步")
    @PostMapping("/cashPayment/export")
    public R<Boolean> cashPaymentExport(@RequestBody @Validated PaymentTypeQuery params) {
        // 需要记录第三方流水号
        return R.success(posCashPaymentService.cashPaymentExport(params));
    }



}
