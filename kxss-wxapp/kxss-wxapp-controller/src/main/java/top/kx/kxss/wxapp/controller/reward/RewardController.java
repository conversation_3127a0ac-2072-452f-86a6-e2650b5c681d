package top.kx.kxss.wxapp.controller.reward;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.base.vo.query.reward.RewardEmployeeIdQuery;
import top.kx.kxss.base.vo.query.reward.RewardMemberIdQuery;
import top.kx.kxss.base.vo.query.reward.RewardOrderPageQuery;
import top.kx.kxss.base.vo.result.reward.*;
import top.kx.kxss.model.enumeration.pos.RewardOrderStatusEnum;
import top.kx.kxss.pos.TempOrderApi;
import top.kx.kxss.pos.vo.save.reward.RewardCreateOrderSaveVO;
import top.kx.kxss.reward.RewardOrderApi;
import top.kx.kxss.wxapp.service.reward.RewardService;
import top.kx.kxss.wxapp.vo.result.shopping.ShoppingCreatedResultVO;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 打赏API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/reward")
@Api(value = "RewardController", tags = "打赏API")
public class RewardController {

    private final TempOrderApi tempOrderApi;
    private final RewardService rewardService;
    private final RewardOrderApi rewardOrderApi;

    @ApiOperation(value = "创建打赏订单", notes = "创建打赏订单")
    @PostMapping("/createOrder")
    public R<ShoppingCreatedResultVO> createOrder(@RequestBody @Validated RewardCreateOrderSaveVO model) {
        ArgumentAssert.isFalse(model.getNum() < 1, "打赏数量不能小于1！");
        return R.success(tempOrderApi.rewardOrder(model));
    }

    @ApiOperation(value = "获取打赏列表", notes = "获取打赏列表")
    @PostMapping("/list")
    public R<List<RewardInfoResultVO>> rewardList() {
        return R.success(rewardService.rewardList());
    }

    @ApiOperation(value = "打赏记录", notes = "打赏记录")
    @PostMapping("/orderPage")
    public R<Page<RewardOrderResultVO>> orderPage(@RequestBody PageParams<RewardOrderPageQuery> params) {
        if (params.getModel() == null) {
            params.setModel(new RewardOrderPageQuery());
        }
        String format = DateUtils.format(LocalDate.now().minusMonths(6),
                DateUtils.DEFAULT_DATE_FORMAT);
        LocalDateTime startTime = DateUtils.getStartTime(format);
        LocalDateTime endTime = DateUtils.getEndTime(LocalDate.now().toString());
        params.setOrder("descending");
        params.setSort("payTime");
        params.getModel().setMemberId(Optional.of(ContextUtil.getMemberId()).orElse(-1L));
        params.getModel().setStatus(RewardOrderStatusEnum.COMPLETE.getCode());
        params.getModel().setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        params.getModel().setStartDate(DateUtils.format(startTime, DateUtils.DEFAULT_DATE_TIME_FORMAT));
        params.getModel().setEndDate(DateUtils.format(endTime, DateUtils.DEFAULT_DATE_TIME_FORMAT));
        return rewardOrderApi.page(params);
    }

    @ApiOperation(value = "打赏会员信息", notes = "打赏会员信息")
    @GetMapping("/memberRank")
    public R<RewardOrderMemerRankResultVO> memberRank() {
        String format = DateUtils.format(LocalDate.now().minusDays(30),
                DateUtils.DEFAULT_DATE_FORMAT);
        LocalDateTime startTime = DateUtils.getStartTime(format);
        LocalDateTime endTime = DateUtils.getEndTime(LocalDate.now().toString());
        RewardMemberIdQuery build = new RewardMemberIdQuery();
        build.setMemberId(Optional.of(ContextUtil.getMemberId()).orElse(-1L));
        build.setOrgId(ContextUtil.getCurrentCompanyId());
        build.setStartDate(DateUtils.format(startTime, DateUtils.DEFAULT_DATE_TIME_FORMAT));
        build.setEndDate(DateUtils.format(endTime, DateUtils.DEFAULT_DATE_TIME_FORMAT));
        return rewardOrderApi.memberRank(build);
    }

    @ApiOperation(value = "打赏会员排名（top7）", notes = "打赏会员排名（top7）")
    @GetMapping("/rankList")
    public R<List<RewardOrderRankResultVO>> rankList() {
        String format = DateUtils.format(LocalDate.now().minusDays(30),
                DateUtils.DEFAULT_DATE_FORMAT);
        LocalDateTime startTime = DateUtils.getStartTime(format);
        LocalDateTime endTime = DateUtils.getEndTime(LocalDate.now().toString());
        RewardMemberIdQuery build = new RewardMemberIdQuery();
        build.setMemberId(Optional.of(ContextUtil.getMemberId()).orElse(-1L));
        build.setOrgId(ContextUtil.getCurrentCompanyId());
        build.setStartDate(DateUtils.format(startTime, DateUtils.DEFAULT_DATE_TIME_FORMAT));
        build.setEndDate(DateUtils.format(endTime, DateUtils.DEFAULT_DATE_TIME_FORMAT));
        return rewardOrderApi.rankList(build);
    }

    @ApiOperation(value = "打赏员工排名（top3）", notes = "打赏员工排名（top3）")
    @GetMapping("/empRankList")
    public R<List<RewardEmployeeRankResultVO>> empRankList() {
        String format = DateUtils.format(LocalDate.now().minusDays(30),
                DateUtils.DEFAULT_DATE_FORMAT);
        LocalDateTime startTime = DateUtils.getStartTime(format);
        LocalDateTime endTime = DateUtils.getEndTime(LocalDate.now().toString());
        RewardEmployeeIdQuery build = new RewardEmployeeIdQuery();
        build.setEmployeeId(ContextUtil.getEmployeeId());
        build.setOrgId(ContextUtil.getCurrentCompanyId());
        build.setStartDate(DateUtils.format(startTime, DateUtils.DEFAULT_DATE_TIME_FORMAT));
        build.setEndDate(DateUtils.format(endTime, DateUtils.DEFAULT_DATE_TIME_FORMAT));
        return rewardOrderApi.empRankList(build);
    }


}


