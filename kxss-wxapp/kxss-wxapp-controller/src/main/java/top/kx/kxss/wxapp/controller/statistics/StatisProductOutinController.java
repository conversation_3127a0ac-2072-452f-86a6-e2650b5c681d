package top.kx.kxss.wxapp.controller.statistics;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.wxapp.service.statistics.StatisProductOutinService;
import top.kx.kxss.wxapp.vo.query.statistics.ProductOutinQuery;
import top.kx.kxss.wxapp.vo.query.statistics.ProductStockOutinQuery;
import top.kx.kxss.wxapp.vo.result.statistics.*;

import javax.servlet.http.HttpServletResponse;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/outin")
@AllArgsConstructor
@Api(value = "商品库存统计相关API", tags = "商品库存统计相关API")
public class StatisProductOutinController {
    @Autowired
    private StatisProductOutinService productOutinService;


    @ApiOperation(value = "商品入库统计", notes = "商品入库统计")
    @PostMapping("/storage")
    public R<IPage<ProductOutinStorageResultVO>> storage(@RequestBody @Validated PageParams<ProductOutinQuery> query) {
        return R.success(productOutinService.storage(query));
    }

    @ApiOperation(value = "商品入库统计-导出", notes = "商品入库统计-导出")
    @RequestMapping(value = "/storage/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void storageExport(@RequestBody @Validated ProductOutinQuery query, HttpServletResponse response) {
        productOutinService.storageExport(query, response);
    }

    @ApiOperation(value = "商品入库合计", notes = "商品入库合计")
    @PostMapping("/storageSum")
    public R<OutinStorageResultVO> storageSum(@RequestBody @Validated ProductOutinQuery query) {
        return R.success(productOutinService.storageSum(query));
    }

    @ApiOperation(value = "商品出库统计", notes = "商品出库统计")
    @PostMapping("/out")
    public R<IPage<ProductOutinOutResultVO>> out(@RequestBody @Validated PageParams<ProductOutinQuery> query) {
        return R.success(productOutinService.out(query));
    }


    @ApiOperation(value = "商品出库统计-导出", notes = "商品出库统计-导出")
    @RequestMapping(value = "/out/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void outExport(@RequestBody @Validated ProductOutinQuery query, HttpServletResponse response) {
        productOutinService.outExport(query, response);
    }

    @ApiOperation(value = "商品出库合计", notes = "商品出库合计")
    @PostMapping("/outSum")
    public R<OutinOutNumResultVO> outSum(@RequestBody @Validated ProductOutinQuery query) {
        return R.success(productOutinService.outSum(query));
    }

    @ApiOperation(value = "商品流水统计", notes = "商品流水统计")
    @PostMapping("/flowing")
    public R<IPage<ProductOutinFlowingResultVO>> flowing(@RequestBody @Validated PageParams<ProductOutinQuery> query) {
        return R.success(productOutinService.flowing(query));
    }

    @ApiOperation(value = "商品流水统计-导出", notes = "商品流水统计-导出")
    @RequestMapping(value = "/flowing/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void flowingExport(@RequestBody @Validated ProductOutinQuery query, HttpServletResponse response) {
        productOutinService.flowingExport(query, response);
    }


    @ApiOperation(value = "商品库存统计", notes = "商品库存统计")
    @PostMapping
    public R<IPage<ProductOutinResultVO>> outin(@RequestBody @Validated PageParams<ProductStockOutinQuery> query) {
        return R.success(productOutinService.outin(query));
    }

    @ApiOperation(value = "商品库存统计-导出", notes = "商品库存统计-导出")
    @RequestMapping(value = "/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void outinExport(@RequestBody @Validated ProductStockOutinQuery query, HttpServletResponse response) {
        productOutinService.outinExport(query, response);
    }

}
