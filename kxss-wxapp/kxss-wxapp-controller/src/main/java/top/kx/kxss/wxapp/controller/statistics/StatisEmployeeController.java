package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.wxapp.service.statistics.StatisEmployeeService;
import top.kx.kxss.wxapp.vo.query.statistics.OverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.StatisEmployeeResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.StatisShiftHandoverResultVO;

import java.util.List;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/employee")
@AllArgsConstructor
@Api(value = "收银员统计相关API", tags = "收银员统计相关API")
public class StatisEmployeeController {
    @Autowired
    private StatisEmployeeService statisEmployeeService;

    @ApiOperation(value = "服务收入统计", notes = "服务收入统计")
    @PostMapping
    public R<List<StatisEmployeeResultVO>> statistics(@RequestBody @Validated OverviewQuery query) {
        return R.success(statisEmployeeService.statistics(query));
    }

    @ApiOperation(value = "交接班", notes = "交接班")
    @PostMapping("/shiftHandover")
    public R<List<StatisShiftHandoverResultVO>> statisShiftHandover(@RequestBody @Validated OverviewQuery query) {
        return R.success(statisEmployeeService.statisShiftHandover(query));
    }

}
