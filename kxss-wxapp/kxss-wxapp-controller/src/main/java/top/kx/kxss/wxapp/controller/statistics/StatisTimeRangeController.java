package top.kx.kxss.wxapp.controller.statistics;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.wxapp.service.statistics.StatisTimeRangeService;
import top.kx.kxss.wxapp.vo.query.statistics.TimeRangeQuery;
import top.kx.kxss.wxapp.vo.result.statistics.StatisTimeRangeExportResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.StatisTimeRangeResultVO;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/timeRange")
@AllArgsConstructor
@Api(value = "时段统计相关API", tags = "时段统计相关API")
public class StatisTimeRangeController {

    @Autowired
    private StatisTimeRangeService statisTimeRangeService;


    @ApiOperation(value = "统计", notes = "统计-柱状图")
    @PostMapping("overview")
    public R<List<StatisTimeRangeResultVO>> overview(@RequestBody @Validated TimeRangeQuery query) {
        return R.success(statisTimeRangeService.overview(query));
    }

    @ApiOperation(value = "时段统计-分页", notes = "时段统计分页")
    @PostMapping("page")
    public R<IPage<StatisTimeRangeResultVO>> page(@RequestBody @Validated PageParams<TimeRangeQuery> query) {
        return R.success(statisTimeRangeService.page(query));
    }

    @ApiOperation(value = "时段统计列表", notes = "时段统计列表")
    @PostMapping("list")
    public R<List<StatisTimeRangeExportResultVO>> list(@RequestBody @Validated TimeRangeQuery query) {
        return R.success(statisTimeRangeService.list(query));
    }

    @ApiOperation(value = "时段统计-导出", notes = "时段统计-导出")
    @RequestMapping(value = "/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void export(@RequestBody @Validated TimeRangeQuery query, HttpServletResponse response) {
        List<StatisTimeRangeExportResultVO> list = statisTimeRangeService.list(query);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=Time Range.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, StatisTimeRangeExportResultVO.class)
                    .sheet("sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }

}
