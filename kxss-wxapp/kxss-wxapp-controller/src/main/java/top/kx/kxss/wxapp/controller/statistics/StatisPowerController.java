package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.wxapp.service.statistics.StatisPowerService;
import top.kx.kxss.wxapp.vo.query.statistics.OverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.StatisAreaResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.StatisPowerResultVO;

import java.util.List;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/power")
@AllArgsConstructor
@Api(value = "充电统计相关API", tags = "充电统计相关API")
public class StatisPowerController {

    @Autowired
    private StatisPowerService statisPowerService;


    @ApiOperation(value = "台桌统计", notes = "台桌统计")
    @PostMapping
    public R<List<StatisPowerResultVO>> statistics(@RequestBody @Validated OverviewQuery query) {
        return R.success(statisPowerService.statistics(query));
    }

    @ApiOperation(value = "台桌类型统计", notes = "台桌类型统计")
    @PostMapping("/type")
    public R<List<StatisPowerResultVO>> statisticsType(@RequestBody @Validated OverviewQuery query) {
        return R.success(statisPowerService.statisticsType(query));
    }


    @ApiOperation(value = "台桌区域统计", notes = "台桌区域统计")
    @PostMapping("/tableArea")
    public R<List<StatisAreaResultVO>> tableArea(@RequestBody @Validated OverviewQuery query) {
        return R.success(statisPowerService.tableArea(query));
    }

}
