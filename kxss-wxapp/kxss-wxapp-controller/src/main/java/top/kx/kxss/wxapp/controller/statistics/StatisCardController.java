package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.base.vo.query.member.card.MemberCardPageQuery;
import top.kx.kxss.wxapp.service.statistics.StatisCardService;
import top.kx.kxss.wxapp.vo.query.statistics.BuyCardStatisQuery;
import top.kx.kxss.wxapp.vo.query.statistics.CashStatsQuery;
import top.kx.kxss.wxapp.vo.query.statistics.MemberCardQuery;
import top.kx.kxss.wxapp.vo.result.statistics.StatisCardOverviewResultVO;
import top.kx.kxss.wxapp.vo.result.table.TableAreaResultVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/card")
@AllArgsConstructor
@Api(value = "购卡统计相关API", tags = "购卡统计相关API")
public class StatisCardController {

    @Autowired
    private StatisCardService statisCardService;

    @ApiOperation(value = "概览", notes = "概览")
    @PostMapping("/overview")
    public R<List<StatisCardOverviewResultVO>> overview(@RequestBody @Validated BuyCardStatisQuery query) {
        return R.success(statisCardService.overview(query));
    }


    @ApiOperation(value = "权益卡", notes = "权益卡")
    @PostMapping("/member")
    public R<Map<String, Object>> memberCard(@RequestBody PageParams<MemberCardPageQuery> params) {
        return R.success(statisCardService.memberCard(params));
    }

    @ApiOperation(value = "权益卡-导出", notes = "权益卡-导出")
    @RequestMapping(value = "/member/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void memberCardExport(@RequestBody MemberCardPageQuery params, HttpServletResponse response) {
        statisCardService.memberCardExport(params, response);
    }


}
