package top.kx.kxss.wxapp.controller.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.pos.PosServiceApi;
import top.kx.kxss.pos.query.product.ChangeNumQuery;
import top.kx.kxss.pos.query.product.ProductQuery;
import top.kx.kxss.pos.query.service.ServicePersonalQuery;
import top.kx.kxss.pos.vo.service.PlatformServicePersonalResultVO;
import top.kx.kxss.pos.vo.service.ServiceInfoResultVO;
import top.kx.kxss.pos.vo.service.ServiceResultVO;

import java.util.List;

/**
 * 前端控制器
 * 服务相关API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/service")
@Api(value = "/wxapp/service", tags = "服务相关API")
public class ServiceController {

    @Autowired
    private PosServiceApi posServiceApi;

    @ApiOperation(value = "服务列表", notes = "服务列表")
    @PostMapping("/queryList")
    public R<Page<ServiceResultVO>> serviceList(@RequestBody PageParams<ProductQuery> query) {
        return posServiceApi.serviceList(query);
    }

    @ApiOperation(value = "根据服务获取人员列表（服务项目树形）", notes = "服务项目树形")
    @PostMapping("/serviceInfoList")
    public R<List<ServiceInfoResultVO>> serviceInfoList() {
        return posServiceApi.serviceInfoList();
    }

    @ApiOperation(value = "根据服务获取人员列表（服务项目树形）", notes = "服务项目树形")
    @PostMapping("/serviceTypeList")
    public R<List<ServiceInfoResultVO>> serviceTypeList() {
        return posServiceApi.serviceTypeList();
    }

    @ApiOperation(value = "平台服务列表", notes = "平台服务列表")
    @PostMapping("/platformServiceList")
    public R<Page<PlatformServicePersonalResultVO>> platformServiceList(@RequestBody PageParams<ServicePersonalQuery> query) {
        return posServiceApi.platformServiceList(query);
    }

    @ApiOperation(value = "平台服务列表", notes = "平台服务列表")
    @PostMapping("/rewardServiceList")
    public R<Page<PlatformServicePersonalResultVO>> rewardServiceList(@RequestBody PageParams<ServicePersonalQuery> query) {
        return posServiceApi.rewardServiceList(query);
    }

    @ApiOperation(value = "根据员工ID查询信息", notes = "根据员工ID查询信息")
    @GetMapping("/getEmployeeInfo/{employeeId}")
    public R<PlatformServicePersonalResultVO> getEmployeeInfo(@PathVariable Long employeeId) {
        return posServiceApi.getEmployeeInfo(employeeId);
    }

    @ApiOperation(value = "批量服务选择", notes = "批量服务选择")
    @PostMapping("/batchChangeNum")
    public R<List<Long>> batchChangeNum(@RequestBody @Validated List<ChangeNumQuery> query) {
        return posServiceApi.batchChangeNum(query);
    }
}


