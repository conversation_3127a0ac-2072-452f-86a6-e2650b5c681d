package top.kx.kxss.wxapp.controller.tenant;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.wxapp.service.org.WxOrgService;
import top.kx.kxss.wxapp.vo.query.org.OrgPageQuery;
import top.kx.kxss.wxapp.vo.result.org.CommonOrgResultVO;

import java.util.Map;

/**
 * 门店相关API Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/tenant")
@AllArgsConstructor
@Api(value = "商户相关API", tags = "商户相关API")
public class TenantController {

    private final WxOrgService orgService;

    @ApiOperation(value = "查询商户信息（分页）", notes = "查询常用门店（分页）")
    @PostMapping(value = "/page")
    @WebLog(value = "'分页列表查询:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<CommonOrgResultVO>> tenantPage(@RequestBody PageParams<OrgPageQuery> params) {
        return R.success(orgService.tenantPage(params));
    }

    @ApiOperation(value = "选择商户门店", notes = "选择商户门店")
    @PostMapping(value = "/select")
    public R<Map<String, Object>> select(@RequestParam Long tenantId, @RequestParam Long orgId,
                                         @RequestParam(required = false) Long memberId) {
        log.info("选择商户门店tenantId:{}, orgId:{}, memberId:{}", tenantId, orgId, memberId);
        return R.success(orgService.select(tenantId, orgId, memberId));
    }

}
