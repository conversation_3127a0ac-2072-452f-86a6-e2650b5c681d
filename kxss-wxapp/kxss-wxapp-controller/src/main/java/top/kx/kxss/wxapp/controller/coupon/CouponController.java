package top.kx.kxss.wxapp.controller.coupon;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.app.cash.member.MemberApi;
import top.kx.kxss.app.query.CouponInfoQuery;
import top.kx.kxss.app.query.GrantCouponQuery;
import top.kx.kxss.base.entity.coupon.BaseCouponInfo;
import top.kx.kxss.base.entity.coupon.BaseCouponRange;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.member.grade.MemberGrade;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.service.BaseService;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.service.coupon.BaseCouponInfoService;
import top.kx.kxss.base.service.coupon.BaseCouponRangeService;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.service.member.coupon.MemberGradeCouponService;
import top.kx.kxss.base.service.member.grade.MemberGradeService;
import top.kx.kxss.base.service.product.BaseProductService;
import top.kx.kxss.base.service.service.BaseServiceService;
import top.kx.kxss.base.service.table.BaseTableInfoService;
import top.kx.kxss.base.vo.result.coupon.BaseCouponInfoResultVO;
import top.kx.kxss.base.vo.result.coupon.BaseCouponRangeResultVO;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.product.BaseProductResultVO;
import top.kx.kxss.base.vo.result.service.BaseServiceResultVO;
import top.kx.kxss.base.vo.result.table.BaseTableInfoResultVO;
import top.kx.kxss.base.vo.save.member.MemberInfoSaveVO;
import top.kx.kxss.model.enumeration.base.CouponStatusEnum;
import top.kx.kxss.model.enumeration.base.MemberSourceEnum;
import top.kx.kxss.model.enumeration.base.UsableRangeEnum;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.service.tenant.DefUserService;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 订单相关API Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/coupon")
@AllArgsConstructor
@Api(value = "优惠劵相关API", tags = "优惠劵相关API")
public class CouponController {

    @Autowired
    private BaseCouponInfoService baseCouponInfoService;
    @Autowired
    private EchoService echoService;
    @Autowired
    private BaseCouponRangeService baseCouponRangeService;
    @Autowired
    private BaseProductService baseProductService;
    @Autowired
    private BaseServiceService baseServiceService;
    @Autowired
    private BaseTableInfoService baseTableInfoService;
    @Autowired
    private MemberGradeCouponService memberGradeCouponService;
    @Autowired
    private MemberApi memberApi;
    @Autowired
    private MemberGradeService memberGradeService;
    @Autowired
    private DefUserService defUserService;
    @Autowired
    private MemberInfoService memberInfoService;

    /**
     * 不鉴权
     *
     * @return
     */
    @ApiOperation(value = "获取优惠券", notes = "获取优惠券")
    @PostMapping("/detail")
    public R<BaseCouponInfoResultVO> detail(@RequestParam("tenantId") Long tenantId, @RequestParam("orgId") Long orgId, @RequestParam("couponId") Long couponId) {
        ContextUtil.setTenantBasePoolName(tenantId);
        ContextUtil.setTenantId(tenantId);
        ContextUtil.setCurrentCompanyId(orgId);
        BaseCouponInfo baseCouponInfo = baseCouponInfoService.getById(couponId);
        ArgumentAssert.notNull(baseCouponInfo, "优惠券不存在");
        ArgumentAssert.isFalse(StringUtils.equals(baseCouponInfo.getCouponStatus(), CouponStatusEnum.STOP.getCode()), "优惠券已停止发放");
        ArgumentAssert.isFalse(StringUtils.equals(baseCouponInfo.getCouponStatus(), CouponStatusEnum.OVER.getCode()), "优惠券已结束发放");
        BaseCouponInfoResultVO resultVO = BeanPlusUtil.toBean(baseCouponInfo, BaseCouponInfoResultVO.class);
        resultVO.setValidityDesc(baseCouponInfoService.validityDesc(baseCouponInfo));
        // 获取优惠劵使用范围
        List<BaseCouponRange> ranges = baseCouponRangeService.list(Wraps.<BaseCouponRange>lbQ().in(BaseCouponRange::getCouponId, couponId));
        if (CollUtil.isNotEmpty(ranges)) {
            List<BaseCouponRangeResultVO> rangeResultVOList = BeanPlusUtil.toBeanList(ranges, BaseCouponRangeResultVO.class);
            //优惠劵使用范围 商品和服务
            List<Long> productIds = rangeResultVOList.stream().filter(vo -> ObjectUtil.equal(vo.getType(), UsableRangeEnum.PRODUCT.getCode())).map(BaseCouponRangeResultVO::getSourceId).collect(Collectors.toList());
            List<Long> serviceIds = rangeResultVOList.stream().filter(vo -> ObjectUtil.equal(vo.getType(), UsableRangeEnum.SERVICE.getCode())).map(BaseCouponRangeResultVO::getSourceId).collect(Collectors.toList());
            List<Long> tableIds = rangeResultVOList.stream().filter(vo -> ObjectUtil.equal(vo.getType(), UsableRangeEnum.TABLE.getCode())).map(BaseCouponRangeResultVO::getSourceId).collect(Collectors.toList());
            //返回信息
            Map<Long, BaseProduct> productMap = CollUtil.isNotEmpty(productIds) ? baseProductService.list(Wraps.<BaseProduct>lbQ().in(BaseProduct::getId, productIds))
                    .stream().collect(Collectors.toMap(BaseProduct::getId, k -> k)) : new HashMap<>();
            Map<Long, BaseService> serviceMap = CollUtil.isNotEmpty(serviceIds) ? baseServiceService.list(Wraps.<BaseService>lbQ().in(BaseService::getId, serviceIds))
                    .stream().collect(Collectors.toMap(BaseService::getId, k -> k)) : new HashMap<>();
            Map<Long, BaseTableInfo> tableMap = CollUtil.isNotEmpty(tableIds) ? baseTableInfoService.list(Wraps.<BaseTableInfo>lbQ().in(BaseTableInfo::getId, tableIds))
                    .stream().collect(Collectors.toMap(BaseTableInfo::getId, k -> k)) : new HashMap<>();
            for (BaseCouponRangeResultVO baseCouponRangeResultVO : rangeResultVOList) {
                if (ObjectUtil.equal(baseCouponRangeResultVO.getType(), UsableRangeEnum.PRODUCT.getCode())) {
                    BaseProduct baseProduct = productMap.get(baseCouponRangeResultVO.getSourceId());
                    baseCouponRangeResultVO.setProductVO(baseProduct == null ? null : BeanUtil.copyProperties(baseProduct, BaseProductResultVO.class));
                }
                if (ObjectUtil.equal(baseCouponRangeResultVO.getType(), UsableRangeEnum.SERVICE.getCode())) {
                    BaseService baseService = serviceMap.get(baseCouponRangeResultVO.getSourceId());
                    baseCouponRangeResultVO.setServiceVO(baseService == null ? null : BeanUtil.copyProperties(baseService, BaseServiceResultVO.class));
                }

                if (ObjectUtil.equal(baseCouponRangeResultVO.getType(), UsableRangeEnum.TABLE.getCode())) {
                    BaseTableInfo baseTableInfo = tableMap.get(baseCouponRangeResultVO.getSourceId());
                    baseCouponRangeResultVO.setTableVO(baseTableInfo == null ? null : BeanUtil.copyProperties(baseTableInfo, BaseTableInfoResultVO.class));
                }
            }
            resultVO.setProductList(rangeResultVOList.stream().filter(baseCouponRange -> ObjectUtil.equal(baseCouponRange.getType(), UsableRangeEnum.PRODUCT.getCode())).collect(Collectors.toList()));
            resultVO.setServiceList(rangeResultVOList.stream().filter(baseCouponRange -> ObjectUtil.equal(baseCouponRange.getType(), UsableRangeEnum.SERVICE.getCode())).collect(Collectors.toList()));
            resultVO.setTableList(rangeResultVOList.stream().filter(baseCouponRange -> ObjectUtil.equal(baseCouponRange.getType(), UsableRangeEnum.TABLE.getCode())).collect(Collectors.toList()));
        }
        echoService.action(resultVO);
        return R.success(resultVO);
    }

    @ApiOperation(value = "领取优惠券", notes = "领取优惠券")
    @PostMapping("/receive")
    public R<Long> receive(@RequestParam("tenantId") Long tenantId, @RequestParam("orgId") Long orgId, @RequestParam Long couponId) {
        ArgumentAssert.isTrue(Objects.nonNull(couponId), "请选择需要领取的优惠券");

        MemberInfo member = memberInfoService.getMemberById(ContextUtil.getMemberId());
        ArgumentAssert.notNull(member, "用户异常，请重新登录");
        ContextUtil.setTenantBasePoolName(tenantId);
        ContextUtil.setTenantId(tenantId);
        ContextUtil.setCurrentCompanyId(orgId);
        // 判断会员是否存在
        MemberInfoResultVO memberInfo = memberInfoService.getMemberByPhoneNoAuth(member.getMobile(), null);
        GrantCouponQuery query = new GrantCouponQuery();
        query.setIsAll(false);
        List<CouponInfoQuery> couponList = new ArrayList<>();
        couponList.add(new CouponInfoQuery(couponId, 1));
        query.setCouponList(couponList);
        if (ObjectUtil.isNull(memberInfo)) {
            // 查询会员级别
            List<MemberGrade> gradeList = memberGradeService.list(Wraps.<MemberGrade>lbQ().eq(SuperEntity::getDeleteFlag, 0)
                    .eq(MemberGrade::getIsScan, true));
            ArgumentAssert.notEmpty(gradeList, "该门店暂不支持自主注册会员，请联系门店了解详情");
            // 添加会员等级
            MemberGrade memberGrade = gradeList.get(0);
            // 新增会员
            DefUser defUser = defUserService.getById(ContextUtil.getUserId());
            MemberInfoSaveVO memberInfoSaveVO = MemberInfoSaveVO.builder()
                    .gradeId(memberGrade.getId())
                    .isLock(false)
                    .mobile(defUser.getMobile())
                    .name(defUser.getNickName())
                    .sex(defUser.getSex())
                    .source(MemberSourceEnum.SELF.getCode())
                    .build();
            MemberInfoResultVO memberInfoResultVO = memberInfoService.addMember(memberInfoSaveVO);
            query.setMemberList(Collections.singletonList(memberInfoResultVO.getId()));
        } else {
            query.setMemberList(Collections.singletonList(memberInfo.getId()));
        }
        ArgumentAssert.isTrue(memberGradeCouponService.checkReceivedNum(query.getMemberList().get(0), couponId, 1), "优惠券领取已达上限");
        query.setIsHandGrand(false);
        Boolean b = memberGradeCouponService.grantCoupon(query);
        ArgumentAssert.isFalse(!b, "领取失败");
        return R.success(query.getMemberList().get(0));


//        return R.success(baseCouponInfoService.receive(couponId));
    }

}
