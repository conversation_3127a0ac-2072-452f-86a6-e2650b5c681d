package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.wxapp.service.statistics.StatisticsRechargeService;
import top.kx.kxss.wxapp.vo.query.statistics.OverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.ChartResultVO;

import java.util.List;

/**
 * 报表
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/recharge")
@AllArgsConstructor
@Api(value = "充值报表相关api", tags = "充值报表相关api")
public class StatisticsRechargeController {

    @Autowired
    private StatisticsRechargeService statisticsRechargeService;

    @ApiOperation(value = "信息概览", notes = "信息概览")
    @PostMapping
    public R<ChartResultVO> overview(@RequestBody @Validated OverviewQuery query) {
        return R.success(statisticsRechargeService.overview(query));
    }

}
