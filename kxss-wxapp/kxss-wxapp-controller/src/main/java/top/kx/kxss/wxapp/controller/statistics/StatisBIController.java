package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.wxapp.service.statistics.StatisBIService;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.StatisSalesBIResultVO;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/bi")
@AllArgsConstructor
@Api(value = "BI大屏相关API", tags = "BI大屏相关API")
public class StatisBIController {

    @Autowired
    private StatisBIService statisBIService;


    @ApiOperation(value = "销量看板(不含充值)", notes = "销量看板(不含充值)")
    @PostMapping("sales")
    public R<StatisSalesBIResultVO> sales(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(statisBIService.sales(query));
    }

}
