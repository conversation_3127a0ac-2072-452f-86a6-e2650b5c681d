package top.kx.kxss.wxapp.controller.component;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.kxss.wxapp.service.component.ComponentService;
import top.kx.kxss.wxapp.vo.query.component.AuthQuery;


/**
 * <p>
 * 微信服务商通知接口
 * </p>
 *
 * <AUTHOR>
 */

@Api(value = "/wxapp/component", tags = "微信服务商通知接口")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/component")
public class ComponentController {

    @Autowired
    private ComponentService componentService;

    @ApiOperation(value = "微信回调", notes = "微信回调")
    @RequestMapping(value = "/notify",
            method = RequestMethod.POST)
    public R<Boolean> tickNotify(@RequestParam("timestamp") String timestamp,
                                 @RequestParam("nonce") String nonce,
                                 @RequestParam("msg_signature") String msgSignature,
                                 @RequestBody String postData) {
        log.info("授权事件接收URL,验证票据timestamp,{},nonce,{},msg_signature,{},postData,{}"
                , timestamp, nonce, msgSignature, postData);
        return R.success(componentService.tickNotify(timestamp, nonce, msgSignature, postData));
    }

    @ApiOperation(value = "用户小程序回调", notes = "用户小程序回调")
    @PostMapping(value = "/{appId}/callback")
    public R<Boolean> callback(@PathVariable String appId,
                               @RequestParam("timestamp") String timestamp,
                               @RequestParam("nonce") String nonce,
                               @RequestParam("msg_signature") String msgSignature,
                               @RequestBody String postData) {
        log.info("授权事件接收URL,验证票据timestamp,{},nonce,{},msg_signature,{},postData,{}"
                , timestamp, nonce, msgSignature, postData);
        return R.success(componentService.callback(appId, timestamp, nonce, msgSignature, postData));
    }

    @ApiOperation(value = "授权链接(PC)", notes = "授权链接(PC)")
    @PostMapping(value = "/authUrl")
    public R<String> authUrl() {
        return R.success(componentService.authUrl());
    }

    @ApiOperation(value = "授权链接（h5）", notes = "授权链接")
    @PostMapping(value = "/authH5Url")
    public R<String> authH5Url(@RequestBody @Validated AuthQuery query) {
        return R.success(componentService.authH5Url(query));
    }

    @ApiOperation(value = "授权回调", notes = "授权回调")
    @GetMapping(value = "/authNotify")
    public R<String> authNotify(@RequestParam String auth_code, @RequestParam String expires_in) {
        return R.success(componentService.authNotify(auth_code, expires_in));
    }
}
