package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.wxapp.service.statistics.StatisCashService;
import top.kx.kxss.wxapp.service.statistics.StatisMonthCashService;
import top.kx.kxss.wxapp.vo.query.statistics.CashStatsQuery;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/cash")
@AllArgsConstructor
@Api(value = "订单统计相关API", tags = "订单统计相关API")
public class StatisCashController {

    @Autowired
    private StatisCashService statisCashService;
    @Autowired
    private StatisMonthCashService statisMonthCashService;


    @ApiOperation(value = "月统计-新", notes = "月统计-新")
    @PostMapping("/month/stats/new")
    public R<Map<String, Object>> monthStatsNew(@RequestBody @Validated PageParams<CashStatsQuery> params) {
        return R.success(statisCashService.monthStats(params));
    }

    @ApiOperation(value = "月统计-合计-新", notes = "月统计-合计-新")
    @PostMapping("/month/stats/new/sum")
    public R<Map<String, Object>> monthStatsNewSum(@RequestBody @Validated CashStatsQuery params) {
        return R.success(statisCashService.monthStatsSum(params));
    }

    @ApiOperation(value = "月统计-导出-新", notes = "月统计-导出-新")
    @RequestMapping(value = "/month/stats/new/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void monthStatsNewExport(@RequestBody @Validated CashStatsQuery params, HttpServletResponse response) {
        statisCashService.monthStatsExport(params, response);
    }


    @ApiOperation(value = "月统计", notes = "月统计")
    @PostMapping("/month/stats")
    public R<Map<String, Object>> monthStats(@RequestBody @Validated PageParams<CashStatsQuery> params) {
        return R.success(statisMonthCashService.monthStats(params));
    }

    @ApiOperation(value = "月统计-合计", notes = "月统计-合计")
    @PostMapping("/month/stats/sum")
    public R<Map<String, Object>> monthStatsSum(@RequestBody @Validated CashStatsQuery params) {
        return R.success(statisMonthCashService.monthStatsSum(params));
    }

    @ApiOperation(value = "月统计-导出", notes = "月统计-导出")
    @RequestMapping(value = "/month/stats/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void monthStatsExport(@RequestBody @Validated CashStatsQuery params, HttpServletResponse response) {
        statisMonthCashService.monthStatsExport(params, response);
    }

}
