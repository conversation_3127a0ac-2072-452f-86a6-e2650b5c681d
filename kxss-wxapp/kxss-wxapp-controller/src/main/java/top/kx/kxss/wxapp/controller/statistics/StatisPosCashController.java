package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.wxapp.service.statistics.StatisPosCashService;
import top.kx.kxss.wxapp.vo.query.statistics.PosCashDetailsQuery;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 订单明细统计 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics")
@AllArgsConstructor
@Api(value = "订单明细统计相关API", tags = "订单明细统计相关API")
public class StatisPosCashController {

    @Autowired
    private StatisPosCashService statisPosCashService;

    @ApiOperation(value = "订单明细表", notes = "订单明细表")
    @PostMapping("/posCash/details/new")
    public R<Map<String, Object>> posCashDetails(@RequestBody PageParams<PosCashDetailsQuery> params) {
        return R.success(statisPosCashService.posCashDetails(params));
    }

    @ApiOperation(value = "订单明细表-合计", notes = "订单明细表-合计")
    @PostMapping("/posCash/details/new/sum")
    public R<Map<String, Object>> posCashDetailsSum(@RequestBody PosCashDetailsQuery params) {
        return R.success(statisPosCashService.posCashDetailsSum(params));
    }

    @ApiOperation(value = "订单明细表-导出", notes = "订单明细表-导出")
    @RequestMapping(value = "/posCash/details/new/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void posCashDetailsExport(@RequestBody PosCashDetailsQuery params, HttpServletResponse response) {
        statisPosCashService.posCashDetailsExport(params, response);
    }
}
