package top.kx.kxss.wxapp.controller.statistics;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.wxapp.service.statistics.RealTimeOverviewService;
import top.kx.kxss.wxapp.vo.result.statistics.ChartResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.RealTimeOverviewResultVO;

import javax.annotation.Resource;

/**
 * 实时数据概览 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/realTime")
@AllArgsConstructor
@Api(value = "实时数据概览相关API", tags = "实时数据概览相关API")
public class RealTimeOverviewController {

    @Resource
    private RealTimeOverviewService realTimeOverviewService;

    @ApiOperation(value = "概览", notes = "概览")
    @PostMapping("/overview")
    public R<RealTimeOverviewResultVO> overview() {
        return R.success(realTimeOverviewService.overview());
    }

    @ApiOperation(value = "待结概览", notes = "待结概览")
    @PostMapping("/unsettle/overview")
    public R<RealTimeOverviewResultVO> unsettleOverview() {
        return R.success(realTimeOverviewService.unsettleOverview());
    }

    @ApiOperation(value = "台桌概览", notes = "台桌概览")
    @PostMapping("/table")
    public R<JSONObject> table() {
        return R.success(realTimeOverviewService.table());
    }

}
