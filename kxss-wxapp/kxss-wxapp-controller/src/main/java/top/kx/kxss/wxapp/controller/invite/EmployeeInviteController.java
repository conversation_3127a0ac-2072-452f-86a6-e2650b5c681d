package top.kx.kxss.wxapp.controller.invite;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.vo.result.user.invite.BaseEmployeeInviteBatchDetailResultVO;
import top.kx.kxss.base.vo.result.user.invite.BaseEmployeeInviteBatchResultVO;
import top.kx.kxss.base.vo.result.user.invite.InviteQuery;
import top.kx.kxss.base.vo.result.user.invite.InviteSmsQuery;
import top.kx.kxss.base.vo.save.user.invite.BaseEmployeeInviteBatchDetailSaveVO;
import top.kx.kxss.base.vo.save.user.invite.BaseEmployeeInviteBatchSaveVO;
import top.kx.kxss.model.enumeration.pos.SmsCodeTypeEnum;
import top.kx.kxss.wxapp.service.invite.EmployeeInviteBatchService;
import top.kx.kxss.base.service.sms.SmsService;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * 员工邀请批次
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-18 18:11:02
 * @create [2025-06-18 18:11:02] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/invite")
@Api(value = "invite", tags = "员工邀请相关API")
public class EmployeeInviteController {


    @Autowired
    private EmployeeInviteBatchService employeeInviteBatchService;

    @Autowired
    private SmsService smsService;

    /**
     * 保存批次
     */
    @ApiOperation(value = "保存批次", notes = "保存批次")
    @PostMapping("/addBatch")
    @WebLog("保存批次")
    public R<String> addBatch(@RequestBody @Validated BaseEmployeeInviteBatchSaveVO saveVO) {
        return R.success(employeeInviteBatchService.addBatch(saveVO));
    }

    /**
     * 根据批次号获取信息
     */
    @ApiOperation(value = "根据批次号获取信息", notes = "根据批次号获取信息")
    @GetMapping("/getInfo/{batchCode}")
    public R<BaseEmployeeInviteBatchResultVO> getInfo(@PathVariable String batchCode) {
        return R.success(employeeInviteBatchService.getInfo(batchCode));
    }

    /**
     * 根据批次号获取信息
     */
    @ApiOperation(value = "根据批次号获取信息", notes = "根据批次号获取信息")
    @PostMapping("/getInfo")
    public R<BaseEmployeeInviteBatchResultVO> getInfo(@RequestBody @Validated InviteQuery query) {
        try {
            ContextUtil.setTenantBasePoolName(query.getTenantId());
            ContextUtil.setTenantId(query.getTenantId());
            ContextUtil.setCurrentCompanyId(query.getOrgId());
            return R.success(employeeInviteBatchService.getInfo(query.getBatchCode()));
        } finally {
            ContextUtil.remove();
        }
    }

    /**
     * 获取最新的批次号
     */
    @ApiOperation(value = "获取最新的批次号", notes = "获取最新的批次号")
    @GetMapping("/getInfo")
    public R<BaseEmployeeInviteBatchResultVO> getInfo() {
        return R.success(employeeInviteBatchService.getInfo());
    }

    /**
     * 根据批次号获取填写人列表
     */
    @ApiOperation(value = "根据批次号获取填写人列表", notes = "根据批次号获取填写人列表")
    @GetMapping("/detailList/{batchCode}")
    public R<List<BaseEmployeeInviteBatchDetailResultVO>> detailList(@PathVariable String batchCode) {
        return R.success(employeeInviteBatchService.detailList(batchCode));
    }

    /**
     * 所有待审核列表
     */
    @ApiOperation(value = "所有待审核列表（不包含已加入）", notes = "所有待审核列表（不包含已加入）")
    @GetMapping("/detailList")
    public R<List<BaseEmployeeInviteBatchDetailResultVO>> detailList() {
        return R.success(employeeInviteBatchService.detailList());
    }


    /**
     * 获取所有历史填写人信息
     */
    @ApiOperation(value = "获取所有历史填写人信息", notes = "获取所有历史填写人信息")
    @GetMapping("/historyDetailList")
    public R<List<BaseEmployeeInviteBatchDetailResultVO>> historyDetailList() {
        return R.success(employeeInviteBatchService.historyDetailList());
    }

    /**
     * 校验手机号
     */
    @ApiOperation(value = "校验手机号", notes = "校验手机号")
    @PostMapping("/checkMobile")
    public R<Boolean> checkMobile(@RequestBody @Validated InviteSmsQuery query) {
        if (StrUtil.isBlank(query.getBatchCode())) {
            ArgumentAssert.isFalse(true, "批次号不能为空");
        }
        try {
            ContextUtil.setTenantId(query.getTenantId());
            ContextUtil.setTenantBasePoolName(query.getTenantId());
            ContextUtil.setCurrentCompanyId(query.getOrgId());
            return R.success(employeeInviteBatchService.checkMobile(query));
        } finally {
            ContextUtil.remove();
        }
    }

    /**
     * 立即加入
     */
    @ApiOperation(value = "立即加入", notes = "立即加入")
    @PostMapping("/joinNow")
    public R<Boolean> joinNow(@RequestBody @Validated InviteSmsQuery query) {
        if (StrUtil.isBlank(query.getBatchCode())) {
            ArgumentAssert.isFalse(true, "批次号不能为空");
        }
        try {
            ContextUtil.setTenantId(query.getTenantId());
            ContextUtil.setTenantBasePoolName(query.getTenantId());
            ContextUtil.setCurrentCompanyId(query.getOrgId());
            return R.success(employeeInviteBatchService.joinNow(query));
        } finally {
            ContextUtil.remove();
        }
    }

    /**
     * 发送短信
     */
    @ApiOperation(value = "发送短信", notes = "发送短信")
    @PostMapping("/smsCode")
    public R<String> smsCode(@RequestBody @Validated InviteSmsQuery query) {
        try {
            ContextUtil.setTenantId(query.getTenantId());
            ContextUtil.setTenantBasePoolName(query.getTenantId());
            ContextUtil.setCurrentCompanyId(query.getOrgId());
            return R.success(smsService.sendByMobile(query.getMobile(), SmsCodeTypeEnum.APPLY_EMPLOYEE.getCode()));
        } finally {
            ContextUtil.remove();
        }
    }

    /**
     * 保存申请
     */
    @ApiOperation(value = "保存申请", notes = "保存申请")
    @PostMapping("/apply")
    public R<Boolean> apply(@RequestBody @Validated BaseEmployeeInviteBatchDetailSaveVO saveVO) {
        if (StrUtil.isBlank(saveVO.getBatchCode())
                && ObjectUtil.isNull(saveVO.getBatchId())) {
            ArgumentAssert.isFalse(true, "请传入批次号");
        }
        try {
            ContextUtil.setTenantId(saveVO.getTenantId());
            ContextUtil.setTenantBasePoolName(saveVO.getTenantId());
            ContextUtil.setCurrentCompanyId(saveVO.getOrgId());
            return R.success(employeeInviteBatchService.apply(saveVO));
        } finally {
            ContextUtil.remove();
        }
    }

    /**
     * 同意加入
     */
    @ApiOperation(value = "同意加入", notes = "同意加入")
    @PostMapping("/agree/{detailId}")
    public R<Boolean> agree(@PathVariable Long detailId) {
        return R.success(employeeInviteBatchService.agree(detailId));
    }

    /**
     * 同意加入
     */
    @ApiOperation(value = "批量同意加入", notes = "批量同意加入")
    @PostMapping("/agree")
    public R<Boolean> agree(@RequestBody List<Long> detailIds) {
        return R.success(employeeInviteBatchService.agree(detailIds));
    }


    /**
     * 拒绝加入
     */
    @ApiOperation(value = "批量拒绝加入", notes = "批量拒绝加入")
    @PostMapping("/refuse")
    public R<Boolean> refuse(@RequestBody List<Long> detailIds) {
        return R.success(employeeInviteBatchService.refuse(detailIds));
    }

    /**
     * 拒绝加入
     */
    @ApiOperation(value = "拒绝加入", notes = "拒绝加入")
    @PostMapping("/refuse/{detailId}")
    public R<Boolean> refuse(@PathVariable Long detailId) {
        return R.success(employeeInviteBatchService.refuse(detailId));
    }


    /**
     * 删除
     */
    @ApiOperation(value = "删除", notes = "删除")
    @DeleteMapping("/deleteByIds")
    public R<Boolean> deleteByIds(@RequestBody List<Long> ids) {
        return R.success(employeeInviteBatchService.deleteByIds(ids));
    }
}


