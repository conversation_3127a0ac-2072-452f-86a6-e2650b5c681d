package top.kx.kxss.wxapp.controller.member.deposit;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.wxapp.service.member.WxMemberDepositService;
import top.kx.kxss.wxapp.vo.result.deposit.MemberDepositRuleResultVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2023/8/27 11:26
 * @description TODO
 * @Version 1.0-SNAPSHOT
 */
@Slf4j
@RestController
@RequestMapping("/deposit")
@AllArgsConstructor
@Api(value = "储值相关API", tags = "储值相关API")
public class MemberDepositController {

    @Autowired
    private WxMemberDepositService depositService;

    @ApiOperation(value = "储值列表",notes = "储值列表",nickname = "lixuecheng")
    @PostMapping("/list")
    public R<List<MemberDepositRuleResultVO>> list(){
        return R.success(depositService.list());
    }

}
