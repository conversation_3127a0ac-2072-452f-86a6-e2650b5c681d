package top.kx.kxss.wxapp.controller.member.card;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.base.entity.member.card.MemberCard;
import top.kx.kxss.base.vo.query.member.card.MemberCardPageQuery;
import top.kx.kxss.base.vo.result.member.card.MemberCardResultVO;
import top.kx.kxss.base.vo.update.member.card.MemberCardUpdateVO;
import top.kx.kxss.member.MemberCardApi;

/**
 * 前端控制器
 * 会员相关API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/memberCard")
@Api(value = "/wxapp/memberCard", tags = "会员卡相关API")
public class MemberCardController {

    @Autowired
    private MemberCardApi memberCardApi;

    @ApiOperation(value = "关键字搜索会员卡(分页)", notes = "关键字搜索会员卡(分页)")
    @PostMapping("/pageList")
    public R<Page<MemberCardResultVO>> pageList(@RequestBody
                                                PageParams<MemberCardPageQuery> query) {
        return memberCardApi.pageList(query);
    }

    @ApiOperation(value = "会员卡详情", notes = "会员卡详情")
    @GetMapping("/detail")
    public R<MemberCardResultVO> detail(@RequestParam Long id) {
        return memberCardApi.detail(id);
    }


    @ApiOperation(value = "会员卡详情", notes = "会员卡详情")
    @PostMapping("/modify")
    public R<MemberCard> modify(@RequestBody @Validated MemberCardUpdateVO model) {
        return memberCardApi.modify(model);
    }


}


