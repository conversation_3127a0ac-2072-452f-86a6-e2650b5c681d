package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.wxapp.service.statistics.StatisReconciliationService;
import top.kx.kxss.wxapp.vo.query.statistics.ReconciliationCashQuery;
import top.kx.kxss.wxapp.vo.query.statistics.ReconciliationPaymentQuery;

import javax.servlet.http.HttpServletResponse;
import java.util.Map;

/**
 * 对账报表 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/reconciliation")
@AllArgsConstructor
@Api(value = "对账报表统计相关API", tags = "对账报表统计相关API")
public class StatisReconciliationController {

    @Autowired
    private StatisReconciliationService reconciliationService;

    @ApiOperation(value = "支付明细统计", notes = "支付明细统计")
    @PostMapping("/payment")
    public R<Map<String, Object>> paymentStatistics(@RequestBody @Validated PageParams<ReconciliationPaymentQuery> params) {
        return R.success(reconciliationService.paymentStatistics(params));
    }

    @ApiOperation(value = "支付明细统计-导出", notes = "支付明细统计-导出")
    @RequestMapping(value = "/payment/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void paymentStatisticsExport(@RequestBody @Validated ReconciliationPaymentQuery params, HttpServletResponse response) {
        reconciliationService.paymentStatisticsExport(params, response);
    }

    @ApiOperation(value = "订单明细统计", notes = "订单明细统计")
    @PostMapping("/cashDetail")
    public R<Map<String, Object>> cashDetail(@RequestBody @Validated PageParams<ReconciliationCashQuery> params) {
        return R.success(reconciliationService.cashDetail(params));
    }

    @ApiOperation(value = "订单明细统计-导出", notes = "订单明细统计-导出")
    @RequestMapping(value = "/cashDetail/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void cashDetailExport(@RequestBody @Validated ReconciliationCashQuery params, HttpServletResponse response) {
        reconciliationService.cashDetailExport(params, response);
    }
}
