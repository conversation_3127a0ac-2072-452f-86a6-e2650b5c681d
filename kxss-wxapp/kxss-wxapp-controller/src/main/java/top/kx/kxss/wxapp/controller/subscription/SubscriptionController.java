package top.kx.kxss.wxapp.controller.subscription;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.pos.SubscriptionApi;
import top.kx.kxss.pos.vo.payment.PrepayWithRequestPaymentVO;
import top.kx.kxss.system.subscription.SysSubscriptionOrderApi;
import top.kx.kxss.system.vo.result.subscription.SubscriptionTenantTemplateResultVO;
import top.kx.kxss.system.vo.result.subscription.market.MarketTenantTemplateResultVO;
import top.kx.kxss.system.vo.result.subscription.order.SubscriptionOrderResultVO;

import java.util.List;

/**
 * 前端控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/subscription")
@Api(value = "/wxapp/subscription", tags = "许可证相关API")
public class SubscriptionController {

    @Autowired
    private SubscriptionApi subscriptionApi;
    @Autowired
    private SysSubscriptionOrderApi sysSubscriptionOrderApi;


    @ApiOperation(value = "获取门店订阅版本信息", notes = "获取门店订阅版本信息")
    @PostMapping(value = "/info")
    public R<MarketTenantTemplateResultVO> info() {
        return subscriptionApi.info();
    }


    @ApiOperation(value = "获取门店订阅生效版本列表", notes = "获取门店订阅生效版本列表")
    @PostMapping(value = "/templateList")
    public R<List<SubscriptionTenantTemplateResultVO>> templateList() {
        return subscriptionApi.templateList();
    }

    @ApiOperation(value = "获取门店订阅增值生效版本列表", notes = "获取门店订阅增值生效版本列表")
    @PostMapping(value = "/valueAddedList")
    public R<List<SubscriptionTenantTemplateResultVO>> valueAddedList() {
        return subscriptionApi.valueAddedList();
    }

    @ApiOperation(value = "模板立即续费", notes = "模板立即续费")
    @PostMapping(value = "/order/renewImmediately")
    public R<PrepayWithRequestPaymentVO> renewImmediately() {
        return subscriptionApi.renewImmediately();
    }

    @ApiOperation(value = "根据模板立即续费", notes = "根据模板立即续费")
    @PostMapping(value = "/order/renewImmediately/{tenantTmpId}")
    public R<PrepayWithRequestPaymentVO> renewImmediatelyByTmpId(@PathVariable Long tenantTmpId) {
        return subscriptionApi.renewImmediatelyByTmpId(tenantTmpId);
    }

    @ApiOperation(value = "取消支付", notes = "取消支付")
    @PostMapping(value = "/order/cancelPay/{orderId}")
    public R<Boolean> cancelPay(@PathVariable Long orderId) {
        return subscriptionApi.cancelPay(orderId);
    }

    @ApiOperation(value = "继续支付", notes = "继续支付")
    @PostMapping(value = "/order/continuePay/{orderId}")
    public R<PrepayWithRequestPaymentVO> continuePay(@PathVariable Long orderId) {
        return subscriptionApi.continuePay(orderId);
    }

    @PostMapping("/page")
    @ApiOperation(value = "租户续费订单", notes = "租户续费订单")
    public R<Page<SubscriptionOrderResultVO>> page(@RequestBody PageParams<SubscriptionOrderResultVO> pageParams) {
        pageParams.getModel().setTenantId(ContextUtil.getTenantId());
        pageParams.getModel().setOrgId(ContextUtil.getCurrentCompanyId());
        pageParams.setSort("createdTime");
        pageParams.setOrder("descending");
        return sysSubscriptionOrderApi.page(pageParams);
    }
}


