package top.kx.kxss.wxapp.controller.statistics;

import com.alibaba.excel.EasyExcel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.base.vo.query.member.card.MemberCardPageQuery;
import top.kx.kxss.wxapp.service.statistics.StatisticsService;
import top.kx.kxss.wxapp.vo.query.statistics.*;
import top.kx.kxss.wxapp.vo.result.statistics.ProductCommissionDetailResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.ProductOutboundDetailsResultVO;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 报表
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics")
@AllArgsConstructor
@Api(value = "报表相关api", tags = "报表相关api")
public class StatisticsController {

    @Autowired
    private StatisticsService statisticsService;


    @ApiOperation(value = "服务明细查询", notes = "服务明细查询")
    @PostMapping("/service/details")
    public R<Map<String, Object>> serviceDetails(@RequestBody PageParams<ServiceDetailsQuery> params) {
        return R.success(statisticsService.serviceDetails(params));
    }

    @ApiOperation(value = "服务明细查询统计", notes = "服务明细查询统计")
    @PostMapping("/service/details/sum")
    public R<Map<String, Object>> serviceDetailsSum(@RequestBody ServiceDetailsQuery params) {
        return R.success(statisticsService.serviceDetailsSum(params));
    }

    @ApiOperation(value = "服务明细查询-导出", notes = "服务明细查询-导出")
    @RequestMapping(value = "/service/details/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void cashDetailExport(@RequestBody ServiceDetailsQuery params, HttpServletResponse response) {
        statisticsService.serviceDetailsExport(params, response);
    }

    @ApiOperation(value = "服务统计查询", notes = "服务统计查询")
    @PostMapping("/serviceStatistics")
    public R<Map<String, Object>> serviceStatistics(@RequestBody PageParams<ServiceDetailsQuery> params) {
        return R.success(statisticsService.serviceStatistics(params));
    }

    @ApiOperation(value = "服务统计查询统计", notes = "服务统计查询统计")
    @PostMapping("/serviceStatistics/sum")
    public R<Map<String, Object>> serviceStatisticsSum(@RequestBody ServiceDetailsQuery params) {
        return R.success(statisticsService.serviceStatisticsSum(params));
    }


    @ApiOperation(value = "服务统计查询统计-导出", notes = "服务统计查询统计-导出")
    @RequestMapping(value = "/serviceStatistics/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void serviceStatisticsExport(@RequestBody ServiceDetailsQuery params, HttpServletResponse response) {
        statisticsService.serviceStatisticsExport(params, response);
    }

    @ApiOperation(value = "服务汇总查询", notes = "服务汇总查询")
    @PostMapping("/serviceStatistics/summary")
    public R<Map<String, Object>> serviceStatisticsSummary(@RequestBody PageParams<ServiceDetailsQuery> params) {
        return R.success(statisticsService.serviceStatisticsSummary(params));
    }

    @ApiOperation(value = "服务汇总查询统计", notes = "服务汇总查询统计")
    @PostMapping("/serviceStatistics/summary/sum")
    public R<Map<String, Object>> serviceStatisticsSummarySum(@RequestBody ServiceDetailsQuery params) {
        return R.success(statisticsService.serviceStatisticsSummarySum(params));
    }

    @ApiOperation(value = "服务汇总查询统计-导出", notes = "服务汇总查询统计-导出")
    @RequestMapping(value = "/serviceStatistics/summary/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void serviceStatisticsSummaryExport(@RequestBody ServiceDetailsQuery params, HttpServletResponse response) {
        statisticsService.serviceStatisticsSummaryExport(params, response);
    }

    @ApiOperation(value = "服务金额查询", notes = "服务金额查询")
    @PostMapping("/service/amount")
    public R<Map<String, Object>> serviceAmount(@RequestBody @Validated PageParams<ServiceAmountQuery> params) {
        return R.success(statisticsService.serviceAmount(params));
    }

    @ApiOperation(value = "服务金额查询统计", notes = "服务金额查询统计")
    @PostMapping("/service/amount/sum")
    public R<Map<String, Object>> serviceAmountSum(@RequestBody @Validated ServiceAmountQuery params) {
        return R.success(statisticsService.serviceAmountSum(params));
    }


    @ApiOperation(value = "服务金额查询统计-导出", notes = "服务金额查询统计-导出")
    @RequestMapping(value = "/service/amount/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void serviceAmountExport(@RequestBody @Validated ServiceAmountQuery params, HttpServletResponse response) {
        statisticsService.serviceAmountExport(params, response);
    }



    @ApiOperation(value = "支付方式明细", notes = "支付方式明细")
    @PostMapping("/payment/details")
    public R<Map<String, Object>> paymentDetails(@RequestBody PageParams<PaymentDetailsQuery> params) {
        return R.success(statisticsService.paymentDetails(params));
    }

    @ApiOperation(value = "支付方式明细统计", notes = "支付方式明细统计")
    @PostMapping("/payment/details/sum")
    public R<Map<String, Object>> paymentDetailsSum(@RequestBody PaymentDetailsQuery params) {
        return R.success(statisticsService.paymentDetailsSum(params));
    }


    @ApiOperation(value = "支付方式明细-导出", notes = "支付方式明细-导出")
    @RequestMapping(value = "/payment/details/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void paymentDetailsExport(@RequestBody PaymentDetailsQuery params, HttpServletResponse response) {
        statisticsService.paymentDetailsExport(params, response);
    }


    @ApiOperation(value = "会员充值表", notes = "会员充值表")
    @PostMapping("/member/recharge/details")
    public R<Map<String, Object>> memberRechargeDetails(@RequestBody PageParams<MemberDetailsQuery> params) {
        return R.success(statisticsService.memberRechargeDetails(params));
    }

    @ApiOperation(value = "会员充值表统计", notes = "会员充值表统计")
    @PostMapping("/member/recharge/details/sum")
    public R<Map<String, Object>> memberRechargeDetailsSum(@RequestBody MemberDetailsQuery params) {
        return R.success(statisticsService.memberRechargeDetailsSum(params));
    }


    @ApiOperation(value = "会员充值表-导出", notes = "会员充值表-导出")
    @RequestMapping(value = "/member/recharge/details/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void memberRechargeDetailsExport(@RequestBody MemberDetailsQuery params, HttpServletResponse response) {
        statisticsService.memberRechargeDetailsExport(params, response);
    }


    @ApiOperation(value = "会员消费", notes = "会员消费")
    @PostMapping("/member/consume")
    public R<Map<String, Object>> memberConsume(@RequestBody PageParams<MemberDetailsQuery> params) {
        return R.success(statisticsService.memberConsume(params));
    }

    @ApiOperation(value = "会员消费统计", notes = "会员消费统计")
    @PostMapping("/member/consume/sum")
    public R<Map<String, Object>> memberConsumeSum(@RequestBody MemberDetailsQuery params) {
        return R.success(statisticsService.memberConsumeSum(params));
    }

    @ApiOperation(value = "会员消费-导出", notes = "会员消费-导出")
    @RequestMapping(value = "/member/consume/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void memberConsumeExport(@RequestBody MemberDetailsQuery params, HttpServletResponse response) {
        statisticsService.memberConsumeExport(params, response);
    }


    @ApiOperation(value = "会员充值统计", notes = "会员充值统计")
    @PostMapping("/member/rechargeStatistics")
    public R<Map<String, Object>> memberRecharge(@RequestBody PageParams<MemberDetailsQuery> params) {
        return R.success(statisticsService.memberRecharge(params));
    }

    @ApiOperation(value = "会员充值统计统计", notes = "会员充值统计统计")
    @PostMapping("/member/rechargeStatistics/sum")
    public R<Map<String, Object>> memberRechargeSum(@RequestBody MemberDetailsQuery params) {
        return R.success(statisticsService.memberRechargeSum(params));
    }

    @ApiOperation(value = "会员充值统计-导出", notes = "会员充值统计-导出")
    @RequestMapping(value = "/member/rechargeStatistics/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void memberRechargeExport(@RequestBody MemberDetailsQuery params, HttpServletResponse response) {
        statisticsService.memberRechargeExport(params, response);
    }
    @ApiOperation(value = "库存出入库流水", notes = "库存出入库流水")
    @PostMapping("/inventoryFlow")
    public R<Map<String, Object>> inventoryFlow(@RequestBody PageParams<InventoryFlowQuery> params) {
        return R.success(statisticsService.inventoryFlow(params));
    }

    @ApiOperation(value = "库存出入库流水统计", notes = "库存出入库流水统计")
    @PostMapping("/inventoryFlow/sum")
    public R<Map<String, Object>> inventoryFlowSum(@RequestBody InventoryFlowQuery params) {
        return R.success(statisticsService.inventoryFlowSum(params));
    }

    @ApiOperation(value = "库存出入库流水-导出", notes = "库存出入库流水-导出")
    @RequestMapping(value = "/inventoryFlow/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void inventoryFlowExport(@RequestBody InventoryFlowQuery params, HttpServletResponse response) {
        statisticsService.inventoryFlowExport(params, response);
    }

    @ApiOperation(value = "库存出库流水详情", notes = "库存出库流水详情")
    @PostMapping("/productOutbound/details")
    public R<Map<String, Object>> productOutboundDetails(@RequestBody PageParams<InventoryFlowQuery> params) {
        return R.success(statisticsService.productOutboundDetails(params));
    }

    @ApiOperation(value = "库存出库流水详情统计", notes = "库存出库流水详情统计")
    @PostMapping("/productOutbound/details/sum")
    public R<ProductOutboundDetailsResultVO> productOutboundDetailsSum(@RequestBody InventoryFlowQuery params) {
        return R.success(statisticsService.productOutboundDetailsSum(params));
    }

    @ApiOperation(value = "库存出库流水详情-导出", notes = "库存出库流水详情-导出")
    @RequestMapping(value = "/productOutbound/details/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void productOutboundDetailsExport(@RequestBody InventoryFlowQuery params, HttpServletResponse response) {
        List<ProductOutboundDetailsResultVO> resultVOList = statisticsService.productOutboundDetailsExport(params, response);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=ProductCommission.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, ProductOutboundDetailsResultVO.class)
                    .sheet("sheet1")
                    .doWrite(resultVOList);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }


    @ApiOperation(value = "库存入库流水详情", notes = "库存入库流水详情")
    @PostMapping("/productInventory/details")
    public R<Map<String, Object>> productInventoryDetails(@RequestBody PageParams<InventoryFlowQuery> params) {
        return R.success(statisticsService.productInventoryDetails(params));
    }

    @ApiOperation(value = "库存入库流水详情统计", notes = "库存入库流水详情统计")
    @PostMapping("/productInventory/details/sum")
    public R<Map<String, Object>> productInventoryDetailsSum(@RequestBody InventoryFlowQuery params) {
        return R.success(statisticsService.productInventoryDetailsSum(params));
    }

    @ApiOperation(value = "库存入库流水详情-导出", notes = "库存入库流水详情-导出")
    @RequestMapping(value = "/productInventory/details/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void productInventoryDetailsExport(@RequestBody InventoryFlowQuery params, HttpServletResponse response) {
        statisticsService.productInventoryDetailsExport(params, response);
    }


    @ApiOperation(value = "商品实时库存", notes = "商品实时库存")
    @PostMapping("/realTimeInventoryOfGoods")
    public R<Map<String, Object>> realTimeInventoryOfGoods(@RequestBody PageParams<RealTimeInventoryOfGoodsQuery> params) {
        return R.success(statisticsService.realTimeInventoryOfGoods(params));
    }

    @ApiOperation(value = "商品实时库存统计", notes = "商品实时库存统计")
    @PostMapping("/realTimeInventoryOfGoods/sum")
    public R<Map<String, Object>> realTimeInventoryOfGoodsSum(@RequestBody RealTimeInventoryOfGoodsQuery params) {
        return R.success(statisticsService.realTimeInventoryOfGoodsSum(params));
    }

    @ApiOperation(value = "商品实时库存-导出", notes = "商品实时库存-导出")
    @RequestMapping(value = "/realTimeInventoryOfGoods/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void realTimeInventoryOfGoodsExport(@RequestBody RealTimeInventoryOfGoodsQuery params, HttpServletResponse response) {
        statisticsService.realTimeInventoryOfGoodsExport(params, response);
    }


    @ApiOperation(value = "订单明细表", notes = "订单明细表")
    @PostMapping("/posCash/details")
    public R<Map<String, Object>> posCashDetails(@RequestBody PageParams<PosCashDetailsQuery> params) {
        return R.success(statisticsService.posCashDetails(params));
    }

    @ApiOperation(value = "pos订单明细表", notes = "订单明细表")
    @PostMapping("/posCash/order/details")
    public R<Map<String, Object>> posCashOrderDetails(@RequestBody PageParams<PosCashDetailsQuery> params) {
        return R.success(statisticsService.posCashOrderDetails(params));
    }

    @ApiOperation(value = "订单明细表-合计", notes = "订单明细表-合计")
    @PostMapping("/posCash/details/sum")
    public R<Map<String, Object>> posCashDetailsSum(@RequestBody PosCashDetailsQuery params) {
        return R.success(statisticsService.posCashDetailsSum(params));
    }

    @ApiOperation(value = "订单明细表-导出", notes = "订单明细表-导出")
    @RequestMapping(value = "/posCash/details/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void posCashDetailsExport(@RequestBody PosCashDetailsQuery params, HttpServletResponse response) {
        statisticsService.posCashDetailsExport(params, response);
    }


    @ApiOperation(value = "订单提成统计", notes = "订单整单提成人")
    @PostMapping("/posCash/commenter")
    public R<Map<String, Object>> posCashCommenter(@RequestBody PageParams<PosCashDetailsQuery> params) {
        return R.success(statisticsService.posCashCommenter(params));
    }

    @ApiOperation(value = "订单提成统计-合计", notes = "订单整单提成人-合计")
    @PostMapping("/posCash/commenter/sum")
    public R<Map<String, Object>> posCashCommenterSum(@RequestBody PosCashDetailsQuery params) {
        return R.success(statisticsService.posCashCommenterSum(params));
    }

    @ApiOperation(value = "订单提成统计-导出", notes = "订单整单提成人-导出")
    @RequestMapping(value = "/posCash/commenter/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void posCashCommenterExport(@RequestBody PosCashDetailsQuery params, HttpServletResponse response) {
        statisticsService.posCashCommenterExport(params, response);
    }


    @ApiOperation(value = "订单优惠方式统计", notes = "订单优惠方式统计")
    @PostMapping("/posCash/discountTypeStatistics")
    public R<Map<String, Object>> posCashDiscountTypeStatistics(@RequestBody PageParams<PosCashDetailsQuery> params) {
        return R.success(statisticsService.posCashDiscountTypeStatistics(params));
    }

    @ApiOperation(value = "订单优惠方式统计-合计", notes = "订单优惠方式统计-合计")
    @PostMapping("/posCash/discountTypeStatistics/sum")
    public R<Map<String, Object>> posCashDiscountTypeStatisticsSum(@RequestBody PosCashDetailsQuery params) {
        return R.success(statisticsService.posCashDiscountTypeStatisticsSum(params));
    }

    @ApiOperation(value = "订单优惠方式统计-导出", notes = "订单优惠方式统计-导出")
    @RequestMapping(value = "/posCash/discountTypeStatistics/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void posCashDiscountTypeStatisticsExport(@RequestBody PosCashDetailsQuery params, HttpServletResponse response) {
        statisticsService.posCashDiscountTypeStatisticsExport(params, response);
    }


    @ApiOperation(value = "订单优惠方式明细", notes = "订单优惠方式明细")
    @PostMapping("/posCash/discountType/details")
    public R<Map<String, Object>> posCashDiscountTypeDetails(@RequestBody PageParams<PosCashDetailsQuery> params) {
        return R.success(statisticsService.posCashDiscountTypeDetails(params));
    }

    @ApiOperation(value = "订单优惠方式明细-合计", notes = "订单优惠方式明细-合计")
    @PostMapping("/posCash/discountType/details/sum")
    public R<Map<String, Object>> posCashDiscountTypeDetailsSum(@RequestBody PosCashDetailsQuery params) {
        return R.success(statisticsService.posCashDiscountTypeDetailsSum(params));
    }

    @ApiOperation(value = "订单优惠方式明细-导出", notes = "订单优惠方式明细-导出")
    @RequestMapping(value = "/posCash/discountType/details/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void posCashDiscountTypeDetailsExport(@RequestBody PosCashDetailsQuery params, HttpServletResponse response) {
        statisticsService.posCashDiscountTypeDetailsExport(params, response);
    }

    @ApiOperation(value = "套餐报表", notes = "套餐报表")
    @PostMapping("/thail/details")
    public R<Map<String, Object>> thailDetails(@RequestBody @Validated PageParams<ThailDetailsQuery> params) {
        return R.success(statisticsService.thailDetails(params));
    }

    @ApiOperation(value = "套餐报表-导出", notes = "套餐报表-导出")
    @RequestMapping(value = "/thail/details/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void thailDetailsExport(@RequestBody @Validated ThailDetailsQuery params, HttpServletResponse response) {
        statisticsService.thailDetailsExport(params, response);
    }

    @ApiOperation(value = "用户会员卡", notes = "用户会员卡")
    @PostMapping("/memberCard/details")
    public R<Map<String, Object>> memberCardDetails(@RequestBody @Validated PageParams<MemberCardPageQuery> params) {
        return R.success(statisticsService.memberCardDetails(params));
    }

    @ApiOperation(value = "用户会员卡-导出", notes = "用户会员卡-导出")
    @RequestMapping(value = "/memberCard/details/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void memberCardDetailsExport(@RequestBody @Validated MemberCardPageQuery params, HttpServletResponse response) {
        statisticsService.memberCardDetailsExport(params, response);
    }

    @ApiOperation(value = "优惠活动", notes = "优惠活动")
    @PostMapping("/posCash/discountType/activity")
    public R<Map<String, Object>> posCashDiscountTypeActivity(@RequestBody PageParams<PosCashDetailsQuery> params) {
        return R.success(statisticsService.posCashDiscountTypeActivity(params));
    }

    @ApiOperation(value = "优惠活动-合计", notes = "优惠活动-合计")
    @PostMapping("/posCash/discountType/activity/sum")
    public R<Map<String, Object>> posCashDiscountTypeActivitySum(@RequestBody PosCashDetailsQuery params) {
        return R.success(statisticsService.posCashDiscountTypeActivitySum(params));
    }

    @ApiOperation(value = "优惠活动-导出", notes = "优惠活动-导出")
    @RequestMapping(value = "/posCash/discountType/activity/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void posCashDiscountTypeActivityExport(@RequestBody PosCashDetailsQuery params, HttpServletResponse response) {
        statisticsService.posCashDiscountTypeActivityExport(params, response);
    }


    @ApiOperation(value = "出入库统计", notes = "出入库统计")
    @PostMapping("/product/stats")
    public R<Map<String, Object>> productStats(@RequestBody PageParams<ProductStatsQuery> params) {
        return R.success(statisticsService.productStats(params));
    }

    @ApiOperation(value = "出入库统计-合计", notes = "出入库统计-合计")
    @PostMapping("/product/stats/sum")
    public R<Map<String, Object>> productStatsSum(@RequestBody ProductStatsQuery params) {
        return R.success(statisticsService.productStatsSum(params));
    }

    @ApiOperation(value = "出入库统计-导出", notes = "出入库统计-导出")
    @RequestMapping(value = "/product/stats/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void productStatsExport(@RequestBody ProductStatsQuery params, HttpServletResponse response) {
        statisticsService.productStatsExport(params, response);
    }

}
