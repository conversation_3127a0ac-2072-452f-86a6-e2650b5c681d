package top.kx.kxss.wxapp.controller.order;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayWithRequestPaymentResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.google.common.collect.Lists;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.app.cash.member.MemberApi;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.query.BizPaymentQuery;
import top.kx.kxss.app.query.OrderIsChangeQuery;
import top.kx.kxss.app.service.cash.PosCashServiceService;
import top.kx.kxss.app.vo.query.cash.PosCashIdQuery;
import top.kx.kxss.app.vo.query.cash.PosCashMemberQuery;
import top.kx.kxss.base.entity.payment.BasePaymentType;
import top.kx.kxss.base.service.payment.BasePaymentTypeService;
import top.kx.kxss.base.vo.query.biz.BaseBizLogPageQuery;
import top.kx.kxss.base.vo.result.biz.BaseBizLogResultVO;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.member.card.MemberCardResultVO;
import top.kx.kxss.model.enumeration.base.OrderSourceEnum;
import top.kx.kxss.model.enumeration.base.PaymentBizTypeEnum;
import top.kx.kxss.model.enumeration.pos.LogicalType;
import top.kx.kxss.pos.PosMemberApi;
import top.kx.kxss.pos.PosOrderApi;
import top.kx.kxss.pos.PosPaymentApi;
import top.kx.kxss.pos.query.card.BindCardQuery;
import top.kx.kxss.pos.query.coupon.BindCouponQuery;
import top.kx.kxss.pos.query.member.BindMemberQuery;
import top.kx.kxss.pos.query.order.CancelOrderQuery;
import top.kx.kxss.pos.query.order.OrderPageQuery;
import top.kx.kxss.pos.vo.order.PayResultVO;
import top.kx.kxss.pos.vo.payment.PaymentTypeResultVO;
import top.kx.kxss.pos.vo.payment.PrepayWithRequestPaymentVO;
import top.kx.kxss.pos.vo.recharge.RechargePageResultVO;
import top.kx.kxss.wxapp.service.order.WxOrderService;
import top.kx.kxss.wxapp.vo.query.order.WxPaymentQuery;
import top.kx.kxss.wxapp.vo.result.order.OrderResultVO;
import top.kx.kxss.wxapp.vo.result.org.StoreResultVO;

import java.util.Arrays;
import java.util.List;

/**
 * 订单相关API Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/order")
@AllArgsConstructor
@Api(value = "订单相关API", tags = "订单相关API")
public class OrderController {
    private final WxOrderService orderService;
    private final PosOrderApi posOrderApi;
    private final PosPaymentApi posPaymentApi;
    private final MemberApi memberApi;
    private final BasePaymentTypeService basePaymentTypeService;
    private final EchoService echoService;
    private final PosCashServiceService posCashServiceService;
    private final PosMemberApi posMemberApi;


    @ApiOperation(value = "订单列表（分页）", notes = "订单列表（分页）")
    @PostMapping(value = "/orgPage")
    public R<IPage<OrderResultVO>> page(@RequestBody PageParams<OrderPageQuery> params) {
        return R.success(orderService.page(params));
    }

    @ApiOperation(value = "账户明细（分页）", notes = "账户明细（分页）")
    @PostMapping(value = "/rechargePage")
    public R<Page<RechargePageResultVO>> rechargePage(@RequestBody PageParams<OrderPageQuery> params) {
        params.getModel().setMemberId(ContextUtil.getMemberId());
        return posOrderApi.rechargePage(params);
    }

    @ApiOperation(value = "根据订单获取门店", notes = "根据订单获取门店")
    @PostMapping("/storeInfo")
    public R<StoreResultVO> storeInfo(@RequestBody PosCashIdQuery query) {
        return R.success(orderService.getStoreInfo(query));
    }

    @ApiOperation(value = "绑定优惠劵", notes = "绑定优惠劵")
    @PostMapping("/bindCoupon")
    public R<Boolean> bindCoupon(@RequestBody @Validated BindCouponQuery query) {
        return posOrderApi.bindCoupon(query);
    }

    @ApiOperation(value = "移除优惠劵", notes = "移除优惠劵")
    @PostMapping("/delCoupon")
    public R<Boolean> delCoupon(@RequestBody @Validated PosCashIdQuery query) {
        return posOrderApi.delCoupon(query);
    }

    @ApiOperation(value = "关灯结账", notes = "关灯结账")
    @PostMapping(value = "/checkout")
    public R<PrepayWithRequestPaymentResponse> checkout(@RequestParam Long posCashId) {
        return R.success(orderService.checkout(posCashId));
    }

    @ApiOperation(value = "支付方式列表", notes = "支付方式列表")
    @PostMapping("/payTypeList")
    public R<List<PaymentTypeResultVO>> wxList() {
        List<BasePaymentType> typeList = basePaymentTypeService.list(Wraps.<BasePaymentType>lbQ()
                .eq(BasePaymentType::getIsApplet, 1)
                .in(BasePaymentType::getBizType, Arrays.asList(PaymentBizTypeEnum.ACCOUNT.getCode(),
                        PaymentBizTypeEnum.POLYMERIZATION.getCode(), PaymentBizTypeEnum.WECHAT.getCode()))
                .eq(BasePaymentType::getCreatedOrgId, ContextUtil.getCurrentCompanyId()));
        List<PaymentTypeResultVO> paymentTypeList = BeanUtil.copyToList(typeList, PaymentTypeResultVO.class);
        echoService.action(paymentTypeList);
        return R.success(paymentTypeList);
    }

    @ApiOperation(value = "充值支付方式列表", notes = "充值支付方式列表")
    @PostMapping("/rechargePayTypeList")
    public R<List<PaymentTypeResultVO>> rechargePayTypeList() {
        List<BasePaymentType> typeList = basePaymentTypeService.list(Wraps.<BasePaymentType>lbQ()
                .eq(BasePaymentType::getIsApplet, 1)
                .in(BasePaymentType::getBizType, Arrays.asList(PaymentBizTypeEnum.POLYMERIZATION.getCode(),
                        PaymentBizTypeEnum.WECHAT.getCode()))
                .eq(BasePaymentType::getCreatedOrgId, ContextUtil.getCurrentCompanyId()));
        List<PaymentTypeResultVO> paymentTypeList = BeanUtil.copyToList(typeList, PaymentTypeResultVO.class);
        echoService.action(paymentTypeList);
        return R.success(paymentTypeList);
    }

    @ApiOperation(value = "订单支付", notes = "订单支付")
    @PostMapping("/payment")
    public R<PosCash> payment(@RequestBody @Validated WxPaymentQuery query) {
        return orderService.payment(query);
    }

    @ApiOperation(value = "pos订单支付", notes = "pos订单支付")
    @PostMapping("/posPayment")
    public R<PosCash> posPayment(@RequestBody @Validated BizPaymentQuery query) {
        return orderService.posPayment(query);
    }

    @ApiOperation(value = "余额支付", notes = "余额支付")
    @PostMapping(value = "/balance_checkout")
    public R<PosCash> balanceCheckout(@RequestParam Long posCashId) {
        return orderService.balanceCheckout(posCashId);
    }

    @ApiOperation(value = "拉起聚合支付", notes = "拉起聚合支付")
    @PostMapping("/unifiedOrder")
    @WebLog("拉起聚合支付")
    public R<PrepayWithRequestPaymentVO> unifiedOrder(@RequestBody @Validated BizPaymentQuery query) {
        query.setOrderSourceEnum(OrderSourceEnum.SELF);
        return posPaymentApi.unifiedOrder(query);
    }

    @ApiOperation(value = "订单支付成功", notes = "订单支付成功")
    @PostMapping("/paySuccess")
    @WebLog("查询订单支付信息")
    public R<Boolean> paySuccess(@RequestBody @Validated PayResultVO payResultVO) {
        payResultVO.setOrderSourceEnum(OrderSourceEnum.SELF);
        return posPaymentApi.paySuccess(payResultVO);
    }

    @ApiOperation(value = "订单支付失败", notes = "订单支付失败")
    @PostMapping("/payFail")
    @WebLog("查询订单支付信息")
    public R<Boolean> payFail(@RequestBody @Validated PayResultVO payResultVO) {
        payResultVO.setOrderSourceEnum(OrderSourceEnum.SELF);
        return posPaymentApi.payFail(payResultVO);
    }

    @ApiOperation(value = "查询订单支付信息", notes = "查询订单支付信息")
    @PostMapping("/payResult")
    @WebLog("查询订单支付信息")
    public R<Boolean> payResult(@RequestBody @Validated PosCashIdQuery query) {
        return posPaymentApi.payResult(query);
    }

    @ApiOperation(value = "订单相关日志（分页）", notes = "订单相关日志（分页）")
    @PostMapping(value = "/bizLogPage")
    public R<Page<BaseBizLogResultVO>> bizLogPage(@RequestBody PageParams<BaseBizLogPageQuery> params) {
        return posOrderApi.bizLogPage(params);
    }

    @ApiOperation(value = "呼叫服务员", notes = "呼叫服务员")
    @PostMapping(value = "/callOut")
    public R<Boolean> callOut(@RequestBody @Validated PosCashIdQuery query) {
        return posOrderApi.callOut(query);
    }

    @ApiOperation(value = "获取订单对应手机号的会员列表", notes = "获取订单对应手机号的会员列表")
    @PostMapping(value = "/memberListByCashId")
    public R<List<MemberInfoResultVO>> memberListByCashId(@RequestBody @Validated PosCashIdQuery query) {
        PosCash posCash = posCashServiceService.getById(query.getPosCashId());
        if (ObjectUtil.isNull(posCash)) {
            return R.success(Lists.newArrayList());
        }
        R<MemberInfoResultVO> memberInfo = memberApi.getMemberInfo(ContextUtil.getMemberId());
        if (memberInfo.getIsSuccess() && ObjectUtil.isNotNull(memberInfo.getData())) {
            return memberApi.memberListByMobile(memberInfo.getData().getMobile());
        }
        return R.success(Lists.newArrayList());
    }

    @ApiOperation(value = "绑定会员", notes = "绑定会员")
    @PostMapping("/bindMember")
    public R<Boolean> bindMember(@RequestBody @Validated PosCashMemberQuery query) {
        BindMemberQuery build = BindMemberQuery.builder()
                .memberId(query.getMemberId())
                .posCashId(query.getPosCashId())
                .build();
        build.setSource(OrderSourceEnum.SELF);
        build.setLogicalType(LogicalType.OPEN_TABLE);
        if (StrUtil.isNotBlank(ContextUtil.getOrderSource())) {
            build.setSource(OrderSourceEnum.get(ContextUtil.getOrderSource()));
        }
        return posMemberApi.bindMember(build);
    }

    @ApiOperation(value = "订单相关会员权益卡", notes = "订单相关会员权益卡")
    @PostMapping("/cardList")
    public R<List<MemberCardResultVO>> cardList(@RequestBody @Validated PosCashIdQuery query) {
        return posOrderApi.cardList(query);
    }

    @ApiOperation(value = "绑定权益卡", notes = "绑定权益卡")
    @PostMapping("/bindCard")
    @WebLog("绑定权益卡")
    public R<Boolean> bindCard(@RequestBody @Validated BindCardQuery query) {
        return posOrderApi.bindCard(query);
    }

    @ApiOperation(value = "移除权益卡", notes = "移除权益卡")
    @PostMapping("/removeCard")
    @WebLog("移除权益卡")
    public R<Boolean> removeCard(@RequestBody @Validated BindCardQuery query) {
        return posOrderApi.removeCard(query);
    }

    @ApiOperation(value = "订单是否变化", notes = "订单是否变化")
    @PostMapping("/isChange")
    @WebLog("订单是否变化")
    public R<Boolean> isChange(@RequestBody @Validated OrderIsChangeQuery query) {
        return posOrderApi.isChange(query);
    }


    @ApiOperation(value = "取消购物订单", notes = "取消boss端购物订单")
    @PostMapping(value = "/cancelShopping")
    public R<PosCash> cancelShopping(@RequestParam Long cashId) {
        return posOrderApi.cancelShopping(cashId);
    }


    @ApiOperation(value = "用户自动取消", notes = "用户自动取消")
    @PostMapping(value = "/cancelOrder")
    public R<Boolean> cancelOrder(@RequestBody @Validated CancelOrderQuery query) {
        query.setRemarks("用户取消支付");
        return posOrderApi.cancel(query);
    }


}
