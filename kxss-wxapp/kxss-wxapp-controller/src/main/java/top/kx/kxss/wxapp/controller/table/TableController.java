package top.kx.kxss.wxapp.controller.table;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.cash.MqttApi;
import top.kx.kxss.app.entity.cash.OpeningTableSaveVO;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.vo.query.cash.OrderCompleteQuery;
import top.kx.kxss.app.vo.query.cash.PosCashIdQuery;
import top.kx.kxss.base.entity.thail.BaseThailDetails;
import top.kx.kxss.base.service.thail.BaseThailDetailsService;
import top.kx.kxss.base.vo.query.table.TableBatchQrCodeQuery;
import top.kx.kxss.base.vo.result.common.BaseDictResultVO;
import top.kx.kxss.file.vo.result.FileResultVO;
import top.kx.kxss.model.enumeration.base.EquityTypeEnum;
import top.kx.kxss.model.enumeration.base.OrderSourceEnum;
import top.kx.kxss.model.enumeration.pos.FinishTypeEnum;
import top.kx.kxss.model.enumeration.pos.LogicalType;
import top.kx.kxss.pos.PosOrderApi;
import top.kx.kxss.pos.PosTableApi;
import top.kx.kxss.pos.TempOrderApi;
import top.kx.kxss.pos.query.order.temp.TempOpenTableQuery;
import top.kx.kxss.pos.query.table.TableIdGroupBuyQuery;
import top.kx.kxss.pos.query.table.TableInfoQuery;
import top.kx.kxss.pos.vo.CashDetailResultVO;
import top.kx.kxss.pos.vo.order.temp.TempOpenTableResultVO;
import top.kx.kxss.pos.vo.table.TableGroupBuyResultVO;
import top.kx.kxss.pos.vo.table.TableInfoResultVO;
import top.kx.kxss.wxapp.service.table.BegunService;
import top.kx.kxss.wxapp.vo.result.table.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 台桌相关API Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/table")
@AllArgsConstructor
@Api(value = "台桌相关API", tags = "台桌相关API")
public class TableController {

    private final BegunService begunService;

    @Autowired
    private PosTableApi posTableApi;
    @Autowired
    private PosOrderApi posOrderApi;
    @Autowired
    private TempOrderApi tempOrderApi;
    @Autowired
    private MqttApi mqttApi;
    @Autowired
    private BaseThailDetailsService baseThailDetailsService;


    @RequestMapping(value = "/type", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "台桌类型（新）", notes = "台桌类型（新）")
    public R<List<BaseDictResultVO>> type() {
        return posTableApi.type();
    }

    @ApiOperation(value = "根据门店查询台桌列表", notes = "根据门店查询台桌列表")
    @PostMapping(value = "/tableList")
    @ApiIgnore
    public R<List<TableAreaResultVO>> tableList() {
        return R.success(begunService.tableList(ContextUtil.getCurrentCompanyId()));
    }

    @ApiOperation(value = "根据门店查询台桌列表（类型）", notes = "根据门店查询台桌列表（类型）")
    @PostMapping(value = "/tableTypeList")
    public R<List<TableTypeResultVO>> tableTypeList() {
        return R.success(begunService.tableTypeList(ContextUtil.getCurrentCompanyId()));
    }

    @ApiOperation(value = "根据门店查询台桌列表（新）", notes = "根据门店查询台桌列表（新）")
    @PostMapping(value = "/list")
    @ApiIgnore
    public R<List<TableResultVO>> list(@RequestBody TableInfoQuery query) {
        return R.success(begunService.list(query));
    }

    @ApiOperation(value = "查询台桌列表分页（新）", notes = "查询台桌列表分页（新）")
    @PostMapping(value = "/page")
    public R<IPage<TableResultVO>> page(@RequestBody PageParams<TableInfoQuery> params) {
        return R.success(begunService.page(params));
    }

    @ApiOperation(value = "订单未完成数", notes = "订单未完成数")
    @PostMapping(value = "/unCompleteCount")
    public R<Map<String, Long>> unCompleteCount() {
        return R.success(begunService.unCompleteCount());
    }

    @ApiOperation(value = "开台信息", notes = "开台信息")
    @PostMapping(value = "/begunInfo")
    public R<BegunResultVO> begunInfo(@RequestParam Long tableId) {
        ArgumentAssert.notNull(tableId, "参数异常");
        return R.success(begunService.begunInfo(tableId));
    }

    @ApiOperation(value = "立即开台（新）", notes = "立即开台（新）")
    @PostMapping(value = "/opening")
    public R<PosCash> opening(@RequestBody @Validated OpeningTableSaveVO saveVO) {
        saveVO.setOrderSource(ContextUtil.getOrderSource());
        if (StrUtil.isBlank(saveVO.getOrderSource())) {
            saveVO.setOrderSource(OrderSourceEnum.SELF.getCode());
        }
        //会员信息
//        MemberInfo memberInfo = memberInfoService.getCustmoerMember();
//        ArgumentAssert.notNull(memberInfo, "会员不存在！");
//        saveVO.setMemberId(memberInfo.getId());
        saveVO.setLogicalType(LogicalType.OPEN_TABLE);
        if (ObjectUtil.isNotNull(saveVO.getThailId())) {
            long count = baseThailDetailsService.count(Wraps.<BaseThailDetails>lbQ()
                    .ne(BaseThailDetails::getType, EquityTypeEnum.TABLE.getCode())
                    .eq(BaseThailDetails::getThailId, saveVO.getThailId())
            );
            ArgumentAssert.isFalse(count > 0, "不支持此套餐");
        }
        return posTableApi.openTable(saveVO);
    }

    @ApiOperation(value = "立即开台（boss）", notes = "立即开台（boss）")
    @PostMapping(value = "/bossOpen")
    public R<PosCash> bossOpen(@RequestBody @Validated OpeningTableSaveVO saveVO) {
        if (StrUtil.isBlank(saveVO.getOrderSource())) {
            saveVO.setOrderSource(OrderSourceEnum.BOSS.getCode());
        }
        if (StrUtil.isNotBlank(saveVO.getSecuritiesNumber()) && "0".equals(saveVO.getSecuritiesNumber())) {
            saveVO.setSecuritiesNumber("");
        }
        return posTableApi.openTable(saveVO);
    }

    @ApiOperation(value = "开台明细（新）", notes = "开台明细（新）")
    @PostMapping(value = "/queryDetail")
    public R<CashDetailResultVO> queryDetail(@RequestParam Long posCashId) {
        PosCashIdQuery build = PosCashIdQuery.builder().posCashId(posCashId).build();
        return posOrderApi.detail(build);
    }

    @ApiOperation(value = "停止计时", notes = "停止计时")
    @PostMapping(value = "/stop")
    public R<PosCash> stop(@RequestParam Long posCashId) {
        PosCashIdQuery build = PosCashIdQuery.builder().posCashId(posCashId).build();
        R<PosCash> stopping = posOrderApi.stopping(build);
        if (stopping.getData() != null) {
            if (stopping.getData().getUnpaid().compareTo(BigDecimal.ZERO) <= 0) {
                if (stopping.getData().getPayment().compareTo(stopping.getData().getPaid()) == 0) {
                    R<PosCash> complete = posOrderApi.complete(OrderCompleteQuery.builder()
                            .finishType(FinishTypeEnum.FINISH.getCode()).posCashId(posCashId).build());
                    stopping.getData().setBillState(complete.getData().getBillState());
                }
            }
        }
        return stopping;
    }

    @ApiOperation(value = "获取台桌二维码", notes = "生成台桌二维码")
    @PostMapping("/getQrCode")
    public R<FileResultVO> getQrCode(@RequestParam Long tableId, @RequestParam String clientId) {
        if (StrUtil.isNotBlank(clientId)) {
            ContextUtil.setClientId(clientId);
        }
        return R.success(begunService.getQrCode(tableId));
    }

    @ApiOperation(value = "批量获取台桌二维码", notes = "批量获取台桌二维码")
    @PostMapping("/batchQrCode")
    public R<List<FileResultVO>> batchQrCode(@RequestBody List<Long> tableIds, @RequestParam String clientId) {
        if (StrUtil.isNotBlank(clientId)) {
            ContextUtil.setClientId(clientId);
        }
        return R.success(begunService.batchQrCode(tableIds));
    }

    @ApiOperation(value = "批量获取台桌二维码链接", notes = "批量获取台桌二维码链接")
    @PostMapping("/batchQrCodeLink")
    public JSONObject batchQrCodeLink(@RequestBody TableBatchQrCodeQuery query) {
        return begunService.batchQrCodeLink(query);
    }


    @ApiOperation(value = "根据scene获取参数信息", notes = "根据scene获取参数信息")
    @PostMapping("/scene")
    public R<QrCodeResultVO> getQrCodeInfo(@RequestParam String scene) {
        return R.success(begunService.qrCodeInfo(scene));
    }

    @RequestMapping(value = "/queryList", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "台桌概览列表", notes = "台桌概览列表")
    public R<List<TableInfoResultVO>> queryList(@RequestBody TableInfoQuery query) {
        return posTableApi.bossList(query);
    }

    /**
     * 验券验证
     * 入参: 台桌ID和去哪券码
     * 出参: 是否直接开台, 否的话,返回权益详情
     * 1.判断是抖音券码还是美团券码
     * 2.判断有没有绑定 本系统的团购套餐
     * 如果没有绑定团购套餐,给出提示
     * 3.查询团购套餐(分三种情况)
     * 1.纯台费,直接返回
     * 2.有台费和有权益, 将权益返回
     * 3.无台费, 给提示,无发开台
     */
    @RequestMapping(value = "/verifyGroupBuy", method = RequestMethod.POST)
    @ResponseBody
    @ApiOperation(value = "验证券码和台桌", notes = "验券验证")
    public R<TableGroupBuyResultVO> verifyGroupBuy(@RequestBody @Validated TableIdGroupBuyQuery query) {
        return posTableApi.verifyGroupBuy(query);
    }

    @ApiOperation(value = "立即开台", notes = "立即开台")
    @PostMapping(value = "/open")
    public R<TempOpenTableResultVO> open(@RequestBody @Validated TempOpenTableQuery query) {
        query.setOrderSource(ContextUtil.getOrderSource());
        if (StrUtil.isBlank(query.getOrderSource())) {
            query.setOrderSource(OrderSourceEnum.SELF.getCode());
        }
        if (ObjectUtil.isNull(query.getThailId())) {
            OpeningTableSaveVO saveVO = OpeningTableSaveVO.builder()
                    .tableId(query.getTableId()).orderSource(query.getOrderSource())
                    .build();
            R<PosCash> posCashR = posTableApi.openTable(saveVO);
            if (!posCashR.getIsSuccess() || ObjectUtil.isNull(posCashR.getData())) {
                return R.fail("开台失败");
            }
            PosCash data = posCashR.getData();
            return R.success(TempOpenTableResultVO.builder()
                    .cashId(data.getId()).isSuccess(true)
                    .build());
        }
        return tempOrderApi.open(query);
    }
}
