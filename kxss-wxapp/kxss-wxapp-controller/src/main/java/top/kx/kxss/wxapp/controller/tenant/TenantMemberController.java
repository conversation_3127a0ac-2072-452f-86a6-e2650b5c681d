package top.kx.kxss.wxapp.controller.tenant;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.wxapp.service.org.WxOrgService;
import top.kx.kxss.wxapp.vo.query.org.OrgPageQuery;
import top.kx.kxss.wxapp.vo.query.org.OrgUnBindQuery;
import top.kx.kxss.wxapp.vo.result.org.CommonMemberOrgResultVO;
import top.kx.kxss.wxapp.vo.result.org.MemberOrgResultVO;

import java.util.List;
import java.util.Map;

/**
 * 门店相关API Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/tenantMember")
@AllArgsConstructor
@Api(value = "商户相关API", tags = "商户相关API")
public class TenantMemberController {

    private final WxOrgService orgService;

    @ApiOperation(value = "查询商户信息（分页）", notes = "查询常用门店（分页）")
    @PostMapping(value = "/page")
    @WebLog(value = "'分页列表查询:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<CommonMemberOrgResultVO>> tenantMemberPage(@RequestBody PageParams<OrgPageQuery> params) {
        return R.success(orgService.tenantMemberPage(params));
    }

    @PostMapping(value = "/pageList")
    @WebLog(value = "'分页列表查询:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<MemberOrgResultVO>> pageList(@RequestBody PageParams<OrgPageQuery> params) {
        return R.success(orgService.pageList(params));
    }

    @ApiOperation(value = "查询商户信息（无分页）", notes = "查询常用门店（无分页）")
    @PostMapping(value = "/query")
    public R<List<MemberOrgResultVO>> query(@RequestBody OrgPageQuery query) {
        return R.success(orgService.query(query));
    }


    @ApiOperation(value = "选择会员门店", notes = "选择会员门店")
    @PostMapping(value = "/select")
    public R<Map<String, Object>> memberSelect(@RequestParam Long tenantId, @RequestParam Long orgId,
                                               @RequestParam Long memberId) {
        return R.success(orgService.memberSelect(tenantId, orgId, memberId));
    }

    @ApiOperation(value = "解绑", notes = "解绑")
    @PostMapping(value = "/unbind")
    public R<Boolean> unbind(@RequestBody @Validated OrgUnBindQuery query) {
        return R.success(orgService.unbind(query));
    }

}
