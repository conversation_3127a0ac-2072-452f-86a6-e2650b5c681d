package top.kx.kxss.wxapp.controller.statistics;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.app.vo.member.MemberIdQuery;
import top.kx.kxss.base.vo.NameValueVO;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.wxapp.service.statistics.StatisMemberService;
import top.kx.kxss.wxapp.service.statistics.StatisRechargeService;
import top.kx.kxss.wxapp.vo.query.statistics.ConsumeQuery;
import top.kx.kxss.wxapp.vo.query.statistics.MemberConsumeQuery;
import top.kx.kxss.wxapp.vo.query.statistics.RechargeQuery;
import top.kx.kxss.wxapp.vo.result.statistics.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/member")
@AllArgsConstructor
@Api(value = "会员统计相关API", tags = "会员统计相关API")
public class StatisMemberController {

    @Autowired
    private StatisMemberService statisMemberService;

    @Autowired
    private StatisRechargeService statisRechargeService;

    @ApiOperation(value = "概览", notes = "概览")
    @PostMapping("/overview")
    public R<Map<String, Object>> overview() {
        return R.success(statisMemberService.overview());
    }

    @ApiOperation(value = "会员等级统计", notes = "会员等级统计")
    @PostMapping("/grade")
    public R<ChartResultVO> grade() {
        return R.success(statisMemberService.grade());
    }

    @ApiOperation(value = "会员性别统计", notes = "会员性别统计")
    @PostMapping("/sex")
    public R<Map<String, Object>> sex() {
        return R.success(statisMemberService.sex());
    }

    @ApiOperation(value = "会员消费次数统计", notes = "会员消费次数统计")
    @PostMapping("/consumeTimes")
    public R<List<StatisMemberResultVO>> consumeTimes() {
        return R.success(statisMemberService.consumeTimes());
    }

    @ApiOperation(value = "会员消费金额统计", notes = "会员消费金额统计")
    @PostMapping("/consumeAmountTop")
    public R<List<StatisMemberConsumeAmountResultVO>> consumeAmountTop() {
        return R.success(statisMemberService.consumeAmountTop());
    }

    @ApiOperation(value = "会员充值金额统计", notes = "会员充值金额统计")
    @PostMapping("/recharge")
    public R<List<StatisMemberResultVO>> recharge() {
        return R.success(statisMemberService.recharge());
    }

    @ApiOperation(value = "会员消费金额统计", notes = "会员消费金额统计")
    @PostMapping("/consumeAmount")
    public R<StripChartResultVO> consumeAmount() {
        return R.success(statisMemberService.consumeAmount());
    }

    @ApiOperation(value = "会员人物画像", notes = "会员人物画像")
    @PostMapping("/portrait")
    public R<List<NameValueVO>> portrait(@RequestBody @Validated MemberIdQuery query) {
        return R.success(statisMemberService.portrait(query));
    }

    @ApiOperation(value = "充值明细", notes = "充值明细")
    @PostMapping("/rechargePage")
    public R<IPage<StatisRechargeResultVO>> recharge(@RequestBody @Validated PageParams<RechargeQuery> params) {
        return statisRechargeService.recharge(params);
    }

    @ApiOperation(value = "充值明细-导出", notes = "充值明细-导出")
    @RequestMapping(value = "/rechargePage/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void rechargeExport(@RequestBody @Validated RechargeQuery params, HttpServletResponse response) {
        statisRechargeService.rechargeExport(params, response);
    }

    @ApiOperation(value = "消费明细", notes = "消费明细")
    @PostMapping("/consumePage")
    public R<Map<String, Object>> consume(@RequestBody PageParams<ConsumeQuery> params) {
        return statisRechargeService.consume(params);
    }

    @ApiOperation(value = "消费明细-统计", notes = "消费明细-统计")
    @PostMapping("/consumePage/sum")
    public R<Map<String, Object>> consumeSum(@RequestBody ConsumeQuery params) {
        return R.success(statisRechargeService.consumeSum(params));
    }

    @ApiOperation(value = "消费明细-导出", notes = "消费明细-导出")
    @RequestMapping(value = "/consumePage/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void consumeExport(@RequestBody ConsumeQuery params, HttpServletResponse response) {
        statisRechargeService.consumeExport(params, response);
    }

    @ApiOperation(value = "余额查询", notes = "余额查询")
    @PostMapping("/balancePage")
    public R<Map<String, Object>> balance(@RequestBody PageParams<MemberConsumeQuery> params) {
        return statisRechargeService.balance(params);
    }

    @ApiOperation(value = "余额查询求和", notes = "余额查询求和")
    @PostMapping("/balancePage/sum")
    public R<StatisBalanceResultVO> balanceSum(@RequestBody MemberConsumeQuery params) {
        return R.success(statisRechargeService.balanceSum(params));
    }

    @ApiOperation(value = "余额查询-导出", notes = "余额查询-导出")
    @RequestMapping(value = "/balancePage/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void balanceExport(@RequestBody MemberConsumeQuery params, HttpServletResponse response) {
        statisRechargeService.balanceExport(params, response);
    }
}
