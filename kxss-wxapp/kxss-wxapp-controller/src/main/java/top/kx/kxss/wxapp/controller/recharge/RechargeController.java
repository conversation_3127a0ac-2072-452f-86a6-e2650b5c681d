package top.kx.kxss.wxapp.controller.recharge;

import cn.hutool.core.util.ObjectUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.app.vo.query.cash.RechargeQuery;
import top.kx.kxss.pos.vo.payment.PrepayWithRequestPaymentVO;
import top.kx.kxss.wxapp.service.recharge.WXRechargeService;
import top.kx.kxss.wxapp.vo.result.recharge.RechargeResultVO;

/**
 * 充值相关API Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/recharge")
@AllArgsConstructor
@Api(value = "充值相关API", tags = "充值相关API")
public class RechargeController {

    private final WXRechargeService rechargeService;

    @ApiOperation(value = "充值信息", notes = "充值信息")
    @PostMapping(value = "/info")
    public R<RechargeResultVO> rechargeInfo(Long memberId) {
        Long orgId =ContextUtil.getCurrentCompanyId();
        return R.success(rechargeService.rechargeInfo(orgId,memberId));
    }

//    @ApiOperation(value = "充值", notes = "充值")
//    @PostMapping
//    public R<RechargePayResultVO> save(@RequestBody @Validated RechargeQuery query) {
//        query.setOrgId(ObjectUtil.isNull(query.getOrgId()) ? ContextUtil.getCurrentCompanyId() : query.getOrgId());
//        return R.success(rechargeService.save(query));
//    }

    @ApiOperation(value = "充值", notes = "充值")
    @PostMapping
    public R<PrepayWithRequestPaymentVO> save(@RequestBody @Validated RechargeQuery query) {
        query.setOrgId(ObjectUtil.isNull(query.getOrgId()) ? ContextUtil.getCurrentCompanyId() : query.getOrgId());
        return rechargeService.save(query);
    }

    @ApiOperation(value = "充值失败", notes = "充值失败")
    @PostMapping(value = "/error")
    public R<Boolean> error(@RequestParam Long orderId) {
        return rechargeService.rechargeError(orderId);
    }

    @ApiOperation(value = "充值成功", notes = "充值成功")
    @PostMapping(value = "/ok")
    public R<Boolean> ok(@RequestParam Long orderId) {
        return rechargeService.rechargeOk(orderId);
    }
}
