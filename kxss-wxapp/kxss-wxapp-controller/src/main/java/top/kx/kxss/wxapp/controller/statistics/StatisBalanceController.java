package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.wxapp.service.statistics.StatisBalanceService;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.BalanceChartResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.BalanceOverviewResultVO;

import java.util.List;

/**
 * 收支结余
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/balance")
@AllArgsConstructor
@Api(value = "收支结余相关API", tags = "收支结余相关API")
public class StatisBalanceController {

    @Autowired
    private StatisBalanceService statisBalanceService;

    @ApiOperation(value = "收支概览", notes = "收支概览")
    @PostMapping("/overview")
    public R<BalanceOverviewResultVO> overview(@RequestBody DataOverviewQuery query) {
        return R.success(statisBalanceService.overview(query));
    }

    @ApiOperation(value = "收支统计", notes = "收支统计")
    @PostMapping("/chart")
    public R<List<BalanceChartResultVO>> chart(@RequestBody DataOverviewQuery query) {
        return R.success(statisBalanceService.chart(query));
    }


}
