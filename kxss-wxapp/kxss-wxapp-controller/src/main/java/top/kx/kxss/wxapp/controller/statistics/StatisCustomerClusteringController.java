package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.wxapp.service.statistics.StatisCustomerClusteringService;
import top.kx.kxss.wxapp.vo.result.statistics.CustomerClusteringVO;

import java.util.List;

/**
 * 客户分群
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/customer/clustering")
@AllArgsConstructor
@Api(value = "客户分群相关API", tags = "客户分群相关API")
public class StatisCustomerClusteringController {

    @Autowired
    private StatisCustomerClusteringService customerClusteringService;

    @ApiOperation(value = "消费活跃度", notes = "消费活跃度")
    @PostMapping
    public R<List<CustomerClusteringVO>> clustering() {
        return R.success(customerClusteringService.clustering());
    }

    @ApiOperation(value = "会员生日", notes = "会员生日")
    @PostMapping("/birth")
    public R<List<CustomerClusteringVO>> birth() {
        return R.success(customerClusteringService.birth());
    }

}
