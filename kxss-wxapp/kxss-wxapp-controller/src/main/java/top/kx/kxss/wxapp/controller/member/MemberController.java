package top.kx.kxss.wxapp.controller.member;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.cash.member.MemberApi;
import top.kx.kxss.app.query.BalanceChangesQuery;
import top.kx.kxss.app.query.GrantCouponQuery;
import top.kx.kxss.app.vo.member.MemberIdQuery;
import top.kx.kxss.app.vo.query.cash.payment.PosCashPaymentPageQuery;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.vo.query.member.MemberBalanceChangePageQuery;
import top.kx.kxss.base.vo.query.member.MemberInfoPageQuery;
import top.kx.kxss.base.vo.result.coupon.BaseCouponInfoResultVO;
import top.kx.kxss.base.vo.result.member.MemberBalanceChangeResultVO;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.member.coupon.MemberCouponResultVO;
import top.kx.kxss.base.vo.save.member.MemberInfoSaveVO;
import top.kx.kxss.base.vo.update.member.MemberInfoUpdateVO;
import top.kx.kxss.member.MemberBalanceChangeApi;
import top.kx.kxss.model.enumeration.base.OrderSourceEnum;
import top.kx.kxss.model.enumeration.base.PaymentTypeEnum;
import top.kx.kxss.pos.PosMemberApi;
import top.kx.kxss.pos.query.member.BindMemberQuery;
import top.kx.kxss.pos.query.member.MemberEquityQuery;
import top.kx.kxss.pos.vo.member.MemberGradeResultVO;
import top.kx.kxss.system.vo.update.tenant.CheckPasswordVO;
import top.kx.kxss.system.vo.update.tenant.DefUserPasswordResetVO;
import top.kx.kxss.system.vo.update.tenant.DefUserPasswordUpdateVO;
import top.kx.kxss.wxapp.service.member.WxMemberService;
import top.kx.kxss.wxapp.vo.result.member.MemberTableResultVO;
import top.kx.kxss.wxapp.vo.update.member.MemberUpdateVO;

import java.util.List;

/**
 * 订单相关API Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/member")
@AllArgsConstructor
@Api(value = "会员相关API", tags = "会员相关API")
public class MemberController {
    private final WxMemberService wxMemberService;
    private final MemberInfoService memberInfoService;
    @Autowired
    private PosMemberApi posMemberApi;
    @Autowired
    private MemberApi memberApi;
    @Autowired
    private MemberBalanceChangeApi memberBalanceChangeApi;

    @ApiOperation(value = "用户信息", notes = "用户信息")
    @PostMapping(value = "/info")
    public R<MemberTableResultVO> info() {
        return R.success(wxMemberService.info());
    }

    @ApiOperation(value = "修改用户信息", notes = "修改用户信息")
    @PostMapping(value = "/update")
    public R<Boolean> update(@RequestBody @Validated MemberUpdateVO updateVO) {
        return R.success(wxMemberService.update(updateVO));
    }


    @ApiOperation(value = "会员优惠劵列表", notes = "会员优惠劵列表")
    @PostMapping("/couponList")
    public R<List<MemberCouponResultVO>> couponList(@RequestBody MemberIdQuery query) {
        if (query != null && ObjectUtil.isNull(query.getMemberId())) {
            MemberInfo userId = memberInfoService.getCustmoerMember();
            ArgumentAssert.notNull(userId, "用户异常");
            query.setMemberId(userId.getId());
        }
        return posMemberApi.couponList(query);
    }

    @ApiOperation(value = "会员可使用优惠劵列表", notes = "会员可使用优惠劵列表")
    @PostMapping("/couponUsedList")
    public R<List<MemberCouponResultVO>> couponUsedList(@RequestBody MemberEquityQuery query) {
        ArgumentAssert.notNull(query.getPosCashId(), "订单不为空");
        MemberInfo userId = memberInfoService.getCustmoerMember();
        ArgumentAssert.notNull(userId, "用户异常");
        query.setMemberId(userId.getId());
        return posMemberApi.couponUsedList(query);
    }

    @ApiOperation(value = "会员等级列表", notes = "会员等级列表")
    @PostMapping("/gradeList")
    public R<List<MemberGradeResultVO>> gradeList() {
        return R.success(wxMemberService.gradeList());
    }


    @ApiOperation(value = "会员新增", notes = "会员新增")
    @PostMapping
    public R<MemberInfoResultVO> save(@RequestBody @Validated MemberInfoSaveVO saveVO) {
        return memberApi.save(saveVO);
    }

    @ApiOperation(value = "会员修改", notes = "会员修改")
    @PutMapping
    public R<MemberInfoResultVO> update(@RequestBody @Validated MemberInfoUpdateVO saveVO) {
        return memberApi.update(saveVO);
    }

    @ApiOperation(value = "会员分页列表", notes = "会员分页列表")
    @PostMapping("/page")
    public R<Page<MemberInfoResultVO>> page(@RequestBody @Validated PageParams<MemberInfoPageQuery> pageParams) {
        return memberApi.page(pageParams);
    }

    @ApiOperation(value = "会员分页列表(即将过期7天)", notes = "会员分页列表(即将过期7天)")
    @PostMapping("/soonExpirePage")
    public R<Page<MemberInfoResultVO>> soonExpirePage(@RequestBody @Validated PageParams<MemberInfoPageQuery> pageParams) {
        //七天内即将过期的会员
        pageParams.getModel().setValidDateType("3");
        pageParams.getModel().setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return memberApi.page(pageParams);
    }

    @ApiOperation(value = "会员分页列表(已过期)", notes = "会员分页列表(已过期)")
    @PostMapping("/expirePage")
    public R<Page<MemberInfoResultVO>> expirePage(@RequestBody @Validated PageParams<MemberInfoPageQuery> pageParams) {
        //已过期的会员
        pageParams.getModel().setValidDateType("0");
        pageParams.getModel().setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return memberApi.page(pageParams);
    }

    @ApiOperation(value = "会员分页列表(账户余额为0的会员)", notes = "会员分页列表(账户余额为0的会员)")
    @PostMapping("/accountExpirePage")
    public R<Page<MemberInfoResultVO>> accountExpirePage(@RequestBody @Validated PageParams<MemberInfoPageQuery> pageParams) {
        //已过期的会员
        pageParams.getModel().setIsAccountExpire(true);
        pageParams.getModel().setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return memberApi.page(pageParams);
    }


    @ApiOperation(value = "会员列表（无分页）", notes = "会员列表（无分页）")
    @PostMapping("/query")
    public R<List<MemberInfoResultVO>> query(@RequestBody @Validated MemberInfoPageQuery query) {
        return memberApi.query(query);
    }

    @ApiOperation(value = "分页查询订单交易记录", notes = "分页查询订单交易记录")
    @PostMapping("/paymentPage")
    @WebLog(value = "'分页查询订单交易记录:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<Page<PosCashPaymentResultVO>> paymentPage(@RequestBody PageParams<PosCashPaymentPageQuery> params) {
        return memberApi.paymentPage(params);
    }

    @ApiOperation(value = "查询会员优惠劵", notes = "查询会员优惠劵")
    @GetMapping("/couponList")
    public R<List<BaseCouponInfoResultVO>> couponList(@RequestParam Long memberId, @RequestParam(required = false) String status) {
        return memberApi.couponList(memberId, status);
    }


    /**
     * 修改密码
     *
     * @param data 修改实体
     * @return 是否成功
     */
    @ApiOperation(value = "修改密码", notes = "修改密码")
    @PutMapping("/password")
    public R<Boolean> updatePassword(@RequestBody @Validated DefUserPasswordUpdateVO data) {
        return memberApi.updatePassword(data);
    }


    /**
     * 重置密码
     *
     * @param data 修改实体
     * @return 是否成功
     */
    @ApiOperation(value = "重置密码", notes = "重置密码")
    @PutMapping("/resetPassword")
    public R<Boolean> resetPassword(@RequestBody @Validated DefUserPasswordResetVO data) {
        return memberApi.resetPassword(data);
    }

    /**
     * 校验密码
     *
     * @param data 修改实体
     * @return 是否成功
     */
    @ApiOperation(value = "校验密码", notes = "校验密码")
    @PutMapping("/checkPassword")
    public R<Boolean> checkPassword(@RequestBody @Validated CheckPasswordVO data) {
        return memberApi.checkPassword(data);
    }

    /**
     * 余额变动
     */
    @ApiOperation(value = "余额变动", notes = "余额变动")
    @PostMapping("/balanceChanges")
    public R<Boolean> balanceChanges(@RequestBody @Validated BalanceChangesQuery query) {
        return memberApi.balanceChanges(query);
    }

    /**
     * 绑定会员
     */
    @ApiOperation(value = "绑定会员", notes = "绑定会员")
    @PostMapping("/bindMember")
    public R<Boolean> bindMember(@RequestBody @Validated BindMemberQuery data) {
        data.setSource(OrderSourceEnum.SELF);
        if (StrUtil.isNotBlank(ContextUtil.getOrderSource())) {
            data.setSource(OrderSourceEnum.get(ContextUtil.getOrderSource()));
        }
        return posMemberApi.bindMember(data);
    }

    @ApiOperation(value = "手动发放优惠劵", notes = "手动发放优惠劵")
    @PostMapping("/grantCoupon")
    public R<Boolean> grantCoupon(@RequestBody @Validated GrantCouponQuery query) {
        return memberApi.grantCoupon(query);
    }

    @ApiOperation(value = "会员余额变动记录", notes = "会员余额变动记录")
    @PostMapping("/balanceChange/page")
    public R<Page<MemberBalanceChangeResultVO>> balanceChangePage(@RequestBody PageParams<MemberBalanceChangePageQuery> params) {
        params.getModel().setMemberId(ContextUtil.getMemberId());
        if (params.getModel().getMemberId() == null) {
            params.getModel().setMemberId(-1L);
        }
        switch (params.getModel().getType()) {
            case "10":
                params.getModel().setTypeList(CollUtil.newArrayList(PaymentTypeEnum.RECHARGE.getCode()));
                break;
            case "20":
                params.getModel().setTypeList(CollUtil.newArrayList(PaymentTypeEnum.REFUND.getCode(),
                        PaymentTypeEnum.CONSUMPTION.getCode()));
                break;
            default:
                params.getModel().setTypeList(CollUtil.newArrayList(PaymentTypeEnum.REFUND.getCode(),
                        PaymentTypeEnum.RECHARGE.getCode(), PaymentTypeEnum.CONSUMPTION.getCode()));
                break;
        }
        params.getModel().setType("");
        return memberBalanceChangeApi.pageList(params);
    }
}
