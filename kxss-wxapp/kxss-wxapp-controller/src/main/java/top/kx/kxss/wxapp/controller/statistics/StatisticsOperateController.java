package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.wxapp.service.statistics.StatisticsOperateService;
import top.kx.kxss.wxapp.vo.query.statistics.OperateQuery;
import top.kx.kxss.wxapp.vo.result.statistics.LineResultVO;

/**
 * 运营分析 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/operate")
@AllArgsConstructor
@Api(value = "运营分析相关API", tags = "运营分析相关API")
public class StatisticsOperateController {
    @Autowired
    private StatisticsOperateService statisticsOperateService;

    @ApiOperation(value = "业绩排行榜", notes = "业绩排行榜")
    @PostMapping
    public R<LineResultVO> analysis(@RequestBody @Validated OperateQuery query) {
        return R.success(statisticsOperateService.analysis(query));
    }
}
