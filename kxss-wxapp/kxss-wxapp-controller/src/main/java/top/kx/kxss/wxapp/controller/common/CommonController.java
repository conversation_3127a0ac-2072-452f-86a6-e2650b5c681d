package top.kx.kxss.wxapp.controller.common;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.exception.BizException;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.vo.result.common.BaseDictResultVO;
import top.kx.kxss.model.enumeration.base.EquityTypeEnum;
import top.kx.kxss.pos.PosProductApi;
import top.kx.kxss.pos.PosServiceApi;
import top.kx.kxss.pos.PosTableApi;
import top.kx.kxss.pos.vo.CommonNameResultVO;
import top.kx.kxss.wxapp.vo.query.common.CommonListQuery;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/common")
@AllArgsConstructor
@Api(value = "通用信息", tags = "通用信息相关API")
public class CommonController {

    private final PosServiceApi posServiceApi;
    private final PosTableApi posTableApi;
    private final PosProductApi posProductApi;

    @ApiOperation(value = "类型列表", notes = "类型列表")
    @PostMapping(value = "/categoryList")
    public R<List<CommonNameResultVO>> categoryList(@RequestBody @Validated CommonListQuery query) {
        switch (EquityTypeEnum.get(query.getBizType())) {
            case SERVICE:
                return posServiceApi.serviceCategory();
            case TABLE:
                return R.success(handleTableTypeResult(posTableApi.type()));
            case PRODUCT:
                return posProductApi.queryCategory();
            default:
                throw new BizException("类型错误");
        }
    }

    @ApiOperation(value = "业务项列表", notes = "业务项列表")
    @PostMapping(value = "/bizItemList")
    public R<List<CommonNameResultVO>> bizItemList(@RequestBody @Validated CommonListQuery query) {
        switch (EquityTypeEnum.get(query.getBizType())) {
            case SERVICE:
                return posServiceApi.service();
            case TABLE:
                return posTableApi.simpleList();
            case PRODUCT:
                return posProductApi.simpleList();
            default:
                throw new BizException("类型错误");
        }
    }

    private List<CommonNameResultVO> handleTableTypeResult(R<List<BaseDictResultVO>> resultR) {
        ArgumentAssert.isFalse(!resultR.getIsSuccess(), resultR.getMsg());
        List<BaseDictResultVO> data = resultR.getData();
        if (CollUtil.isEmpty(data)) {
            return new ArrayList<>();
        }
        return data.stream().map(v -> BeanUtil.copyProperties(v, CommonNameResultVO.class)).collect(Collectors.toList());
    }


}
