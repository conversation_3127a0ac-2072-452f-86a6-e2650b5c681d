package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.wxapp.service.statistics.StatisProductService;
import top.kx.kxss.wxapp.vo.query.statistics.OverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.ChartResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.ProductResultVO;

import java.util.List;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/product")
@AllArgsConstructor
@Api(value = "商品统计相关API", tags = "商品统计相关API")
public class StatisProductController {
    @Autowired
    private StatisProductService productService;
    @ApiOperation(value = "商品统计", notes = "商品统计")
    @PostMapping
    public R<List<ProductResultVO>> statistics(@RequestBody @Validated OverviewQuery query) {
        return R.success(productService.statistics(query));
    }

    @ApiOperation(value = "商品类型统计", notes = "商品类型统计")
    @PostMapping("/type")
    public R<ChartResultVO> statisticsType(@RequestBody @Validated OverviewQuery query) {
        return R.success(productService.statisticsType(query));
    }
}
