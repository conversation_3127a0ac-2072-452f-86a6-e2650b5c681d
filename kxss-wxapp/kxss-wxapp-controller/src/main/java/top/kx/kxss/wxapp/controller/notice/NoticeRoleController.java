package top.kx.kxss.wxapp.controller.notice;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.base.entity.notice.BaseNoticeRole;
import top.kx.kxss.base.vo.result.notice.NoticeRoleResultVO;
import top.kx.kxss.base.vo.save.notice.BaseNoticeRoleSaveVO;
import top.kx.kxss.notice.api.NoticeRoleApi;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * 收银通知权限配置
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-19 14:43:55
 * @create [2023-09-19 14:43:55] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/noticeRole")
@Api(value = "noticeRole", tags = "收银通知权限配置API")
public class NoticeRoleController {

    @Autowired
    private NoticeRoleApi noticeRoleApi;


    @ApiOperation(value = "查询权限通知配置", notes = "查询权限通知配置")
    @PostMapping("/queryList")
    public R<List<NoticeRoleResultVO>> queryList() {
        return noticeRoleApi.queryList();
    }

    @ApiOperation(value = "保存", notes = "保存")
    @PostMapping("/add")
    public R<BaseNoticeRole> add(@RequestBody @Validated BaseNoticeRoleSaveVO model) {
        return noticeRoleApi.add(model);
    }


}


