package top.kx.kxss.wxapp.controller.applet;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.system.api.AppletNoticeTmplApi;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * 小程序服务通知模版
 * </p>
 *
 * <AUTHOR>
 * @date 2023-10-25 15:39:53
 * @create [2023-10-25 15:39:53] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/appletNoticeTmpl")
@Api(value = "AppletNoticeTmpl", tags = "小程序服务通知模版")
public class AppletNoticeTmplController {

    @Autowired
    private AppletNoticeTmplApi appletNoticeTmplApi;

    /**
     * 获取模版id
     */
    @ApiOperation(value = "获取模版id", notes = "获取模版id")
    @PostMapping("/tmpList")
    public R<List<String>> tmpList() {
        return appletNoticeTmplApi.tmpList();
    }
}


