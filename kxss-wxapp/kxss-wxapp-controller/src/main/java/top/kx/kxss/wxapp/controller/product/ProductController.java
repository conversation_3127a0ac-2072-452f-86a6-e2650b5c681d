package top.kx.kxss.wxapp.controller.product;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.kxss.pos.PosProductApi;
import top.kx.kxss.pos.ProductOperateApi;
import top.kx.kxss.pos.query.product.BatchChangeNumQuery;
import top.kx.kxss.pos.query.product.ProductQuery;
import top.kx.kxss.pos.vo.product.ProductInfoResultVO;

import java.util.List;

/**
 * 前端控制器
 * 服务相关API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/product")
@Api(value = "/wxapp/product", tags = "商品相关API")
public class ProductController {

    @Autowired
    private PosProductApi posProductApi;

    @Autowired
    private ProductOperateApi productOperateApi;


    @ApiOperation(value = "根据分类查询商品信息（树形）", notes = "根据分类查询商品信息（树形）")
    @PostMapping("/productInfoList")
    public R<List<ProductInfoResultVO>> productInfoList() {
        return posProductApi.productInfoList(ProductQuery.builder().build());
    }

    @ApiOperation(value = "商品数量变换批量添加", notes = "商品数量变换(批量)")
    @PostMapping("/batchChangeNum")
    @WebLog("商品数量变换批量")
    public R<Long> batchChangeNum(@RequestBody @Validated BatchChangeNumQuery query) {
        return productOperateApi.batchChangeNum(query);
    }

}


