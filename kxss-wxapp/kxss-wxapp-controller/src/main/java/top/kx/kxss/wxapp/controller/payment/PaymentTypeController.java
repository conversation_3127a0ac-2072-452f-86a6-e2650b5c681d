package top.kx.kxss.wxapp.controller.payment;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.base.annotation.OperationLog;
import top.kx.kxss.base.entity.payment.BasePaymentType;
import top.kx.kxss.base.service.payment.BasePaymentTypeService;
import top.kx.kxss.base.vo.query.payment.BasePaymentTypePageQuery;
import top.kx.kxss.base.vo.result.payment.BasePaymentTypeResultVO;
import top.kx.kxss.base.vo.save.payment.BasePaymentTypeSaveVO;
import top.kx.kxss.base.vo.update.payment.BasePaymentTypeUpdateVO;
import top.kx.kxss.model.enumeration.base.BizLogOperationTypeEnum;
import top.kx.kxss.model.enumeration.base.SnapshotBizModuleEnum;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * 支付类型
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-19 14:43:55
 * @create [2023-09-19 14:43:55] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/paymentType")
@Api(value = "paymentType", tags = "支付类型")
public class PaymentTypeController extends SuperController<BasePaymentTypeService, Long, BasePaymentType, BasePaymentTypeSaveVO,
        BasePaymentTypeUpdateVO, BasePaymentTypePageQuery, BasePaymentTypeResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Override
    public R<BasePaymentTypeResultVO> getDetail(Long aLong) {
        return success(superService.getDetail(aLong));
    }

    @Override
    public QueryWrap<BasePaymentType> handlerWrapper(BasePaymentType model, PageParams<BasePaymentTypePageQuery> params) {
        params.setSort("sortValue");
        params.setOrder("ascending");
        model.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return super.handlerWrapper(model, params);
    }

    @OperationLog(
            module = SnapshotBizModuleEnum.PAYMENT_TYPE,
            type = BizLogOperationTypeEnum.CREATE,
            descSplitField = "name"
    )
    @Override
    public R<BasePaymentType> save(BasePaymentTypeSaveVO basePaymentTypeSaveVO) {
        return super.save(basePaymentTypeSaveVO);
    }


    @OperationLog(
            module = SnapshotBizModuleEnum.PAYMENT_TYPE,
            type = BizLogOperationTypeEnum.UPDATE,
            source = {"#basePaymentTypeUpdateVO.id"},
            descSplitField = "name"
    )
    @Override
    public R<BasePaymentType> update(BasePaymentTypeUpdateVO basePaymentTypeUpdateVO) {
        return super.update(basePaymentTypeUpdateVO);
    }

    @OperationLog(
            module = SnapshotBizModuleEnum.PAYMENT_TYPE,
            type = BizLogOperationTypeEnum.DELETE,
            source = {"#longs"},
            descSplitField = "name"
    )
    @Override
    public R<Boolean> delete(List<Long> longs) {
        return super.delete(longs);
    }

    @Override
    public R<IPage<BasePaymentTypeResultVO>> page(PageParams<BasePaymentTypePageQuery> params) {
        params.setSort("sortValue");
        params.setOrder("ascending");
        params.getModel().setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return super.page(params);
    }

    @Override
    public R<List<BasePaymentTypeResultVO>> query(BasePaymentTypePageQuery data) {
        data.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return super.query(data);
    }
}


