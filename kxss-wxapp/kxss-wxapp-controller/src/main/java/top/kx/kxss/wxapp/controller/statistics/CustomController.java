package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.wxapp.service.statistics.CustomService;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.query.statistics.OverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.CustomResultVO;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/custom")
@AllArgsConstructor
@Api(value = "首页统计相关API", tags = "首页统计相关API")
public class CustomController {

    @Autowired
    private CustomService customService;

    @ApiOperation(value = "获取表达查询条件vo", notes = "获取表达查询条件vo-统一处理接口")
    @PostMapping("/getStoreTime")
    public R<DataOverviewQuery> getStoreTime(@RequestBody @Validated DataOverviewQuery query) {
        customService.storeTime(query);
        return R.success(query);
    }

    @ApiOperation(value = "概览", notes = "概览")
    @PostMapping("/time")
    public R<DataOverviewQuery> time(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(customService.time(query));
    }
    @ApiOperation(value = "概览", notes = "概览")
    @PostMapping("/overview")
    public R<CustomResultVO> overview(@RequestBody @Validated OverviewQuery query) {
        return R.success(customService.overview(query));
    }
}
