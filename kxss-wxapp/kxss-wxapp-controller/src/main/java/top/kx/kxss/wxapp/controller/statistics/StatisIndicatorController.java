package top.kx.kxss.wxapp.controller.statistics;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.base.vo.result.member.MemberBalanceChangeResultVO;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.wxapp.service.statistics.StatisIndicatorService;
import top.kx.kxss.wxapp.service.statistics.StatisTradeService;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.query.statistics.IndicatorQuery;
import top.kx.kxss.wxapp.vo.result.statistics.*;

import java.util.List;

/**
 * 营业额 Controller
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/statistics/indicator")
@AllArgsConstructor
@Api(value = "指标相关API", tags = "指标相关API")
public class StatisIndicatorController {

    @Autowired
    private StatisIndicatorService statisIndicatorService;


    @ApiOperation(value = "实时指标", notes = "实时指标")
    @PostMapping("realTime")
    public R<List<StatisRealTimeIndicatorResultVO>> realTime() {
        return R.success(statisIndicatorService.realTime());
    }

    /**
     * 收款金额相关指标
     * @param query
     * @return
     */
    @ApiOperation(value = "收款指标")
    @PostMapping("payment")
    public R<List<StatisIndicatorResultVO>> payment(@RequestBody @Validated IndicatorQuery query) {
        return R.success(statisIndicatorService.payment(query));
    }

    @ApiOperation(value = "营业相关指标", notes = "营业相关指标")
    @PostMapping("trade")
    public R<List<StatisIndicatorResultVO>> trade(@RequestBody @Validated IndicatorQuery query) {
        return R.success(statisIndicatorService.trade(query));
    }

    @ApiOperation(value = "台桌相关指标", notes = "台桌相关指标")
    @PostMapping("table")
    public R<List<StatisIndicatorResultVO>> table(@RequestBody @Validated IndicatorQuery query) {
        return R.success(statisIndicatorService.table(query));
    }

    @ApiOperation(value = "服务相关指标", notes = "服务相关指标")
    @PostMapping("service")
    public R<List<StatisIndicatorResultVO>> service(@RequestBody @Validated IndicatorQuery query) {
        return R.success(statisIndicatorService.service(query));
    }

    @ApiOperation(value = "商品相关指标", notes = "商品相关指标")
    @PostMapping("product")
    public R<List<StatisIndicatorResultVO>> product(@RequestBody @Validated IndicatorQuery query) {
        return R.success(statisIndicatorService.product(query));
    }

    @ApiOperation(value = "会员相关指标", notes = "会员相关指标")
    @PostMapping("member")
    public R<List<StatisIndicatorResultVO>> member(@RequestBody @Validated IndicatorQuery query) {
        return R.success(statisIndicatorService.member(query));
    }

}
