package top.kx.kxss.wxapp.vo.result.statistics;

import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.constant.EchoDictType;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 收支统计信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-07-07 13:50:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ColumnWidth(20)
@ExcelIgnoreUnannotated
@ApiModel(value = "ProductOutboundDetailsResultVO", description = "服务提成")
public class ProductOutboundDetailsResultVO implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "明细ID")
    private Long id;

    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.PRODUCT_UNIT)
    @ApiModelProperty(value = "单位key")
    private String measuringUnitKey;

    @ApiModelProperty(value = "商品ID")
    private Long productId;

    @ApiModelProperty(value = "备注")
    private String remarks;

    /**
     * 金额
     */
    @ExcelProperty(value = "出库时间", index = 0)
    @ApiModelProperty(value = "出库时间")
    private LocalDateTime createdTime;
    /**
     * 名称
     */
    @ExcelProperty(value = "结账时间", index = 1)
    @ApiModelProperty(value = "结账时间")
    private LocalDateTime completeTime;
    /**
     * 数量
     */
    @ExcelProperty(value = "单号", index = 2)
    @ApiModelProperty(value = "单号")
    private String code;

    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS,dictType = EchoDictType.Base.OUTIN_TYPE)
    @ApiModelProperty(value = "出库类型")
    private String type;

    @ExcelProperty(value = "出库类型", index = 3)
    @ApiModelProperty(value = "出库类型")
    private String typeName;

    @ApiModelProperty(value = "分类")
    @Echo(api = EchoApi.PROCUCT_CATEGORY_CLASS)
    private Long categoryId;

    @ExcelProperty(value = "分类", index = 4)
    @ApiModelProperty(value = "分类")
    private String categoryName;

    @ExcelProperty(value = "商品名称", index = 5)
    @ApiModelProperty(value = "商品名称")
    private String name;

    @ExcelProperty(value = "单位", index = 6)
    @ApiModelProperty(value = "单位")
    private String measuringUnit;

    @ExcelProperty(value = "数量", index = 7)
    @ApiModelProperty(value = "数量")
    private Integer num;

    @ExcelProperty(value = "销售单价", index = 8)
    @ApiModelProperty(value = "销售单价")
    private BigDecimal price;

    @ExcelProperty(value = "销售金额", index = 9)
    @ApiModelProperty(value = "销售金额")
    private BigDecimal originPrice;

    @ExcelProperty(value = "优惠金额", index = 10)
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmount;

    @ExcelProperty(value = "销售收入", index = 11)
    @ApiModelProperty(value = "销售收入")
    private BigDecimal totalPrice;

    @ExcelProperty(value = "成本单价", index = 12)
    @ApiModelProperty(value = "成本单价")
    private BigDecimal costPrice;

    @ExcelProperty(value = "成本金额", index = 13)
    @ApiModelProperty(value = "成本金额")
    private BigDecimal totalCostPrice;

    @ExcelProperty(value = "利润", index = 14)
    @ApiModelProperty(value = "利润")
    private BigDecimal profitPrice;

    @ApiModelProperty(value = "提成员工")
    private String beneficiaryEmpIdStr;

    @ApiModelProperty(value = "提成员工")
    @Echo(api = EchoApi.EMPLOYEE_CONTAIN_REMOVE_CLASS)
    private List<Long> beneficiaryEmpIdList;

    @ExcelProperty(value = "提成员工", index = 15)
    @ApiModelProperty(value = "提成员工")
    private String beneficiaryEmp;

    @ApiModelProperty(value = "操作员工")
    @Echo(api = EchoApi.EMPLOYEE_CONTAIN_REMOVE_CLASS)
    private Long createdEmpId;

    @ExcelProperty(value = "操作员工", index = 16)
    @ApiModelProperty(value = "操作员工")
    private String createdEmp;

    @ApiModelProperty(value = "门店id")
    @Echo(api = EchoApi.ORG_ID_CLASS)
    private Long orgId;

    @ExcelProperty(value = "门店", index = 17)
    @ApiModelProperty(value = "门店")
    private String org;

    @ApiModelProperty(value = "仓库id")
    @Echo(api = EchoApi.WAREHOUSE_CLASS)
    private Long warehouseId;

    @ExcelProperty(value = "仓库", index = 18)
    @ApiModelProperty(value = "仓库")
    private String warehouseName;

    @ExcelProperty(value = "台桌名称", index = 19)
    @ApiModelProperty(value = "台桌名称")
    private String tableName;

    @ExcelProperty(value = "备注", index = 20)
    @ApiModelProperty(value = "备注")
    private String itemRemark;
}
