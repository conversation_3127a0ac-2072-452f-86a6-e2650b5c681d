package top.kx.kxss.wxapp.vo.query.statistics;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "PosCashDetailsQuery", description = "")
public class PosCashDetailsQuery extends DataOverviewQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "类型 0开台  1购物    2卡券    3充值")
    private String type;

    @ApiModelProperty(value = "类型 0开台  1购物    2卡券    3充值")
    private List<String> typeList;

    @ApiModelProperty(value = "单据状态")
    private List<String> billStateList;

    @ApiModelProperty(value = "单据类型")
    private List<String> billTypeList;

    @ApiModelProperty(value = "订单编号")
    private String keyword;

    /**
     * 兼容web 端
     */
    @ApiModelProperty(value = "订单编号")
    private String code;

    @ApiModelProperty(value = "单号")
    private String mirrorCode;

    @ApiModelProperty(value = "收退类型")
    private String mirrorType;


    @ApiModelProperty(value = "员工id")
    private Long employeeId;


    @ApiModelProperty(value = "提成人")
    private Long commenter;

    @ApiModelProperty(value = "会员名称")
    private String memberName;

    @ApiModelProperty(value = "订单来源")
    private String orderSource;


    @ApiModelProperty(value = "台桌名称")
    private String tableName;

    @ApiModelProperty(value = "台桌类型")
    private String tableType;

    @ApiModelProperty(value = "台桌区域")
    private String tableArea;


    @ApiModelProperty(value = "完成结束时间")
    private String completeTime_ed;

    @ApiModelProperty(value = "完成开始时间")
    private String completeTime_st;

    @ApiModelProperty(value = "创建人")
    private Long createdEmp;

    @ApiModelProperty(value = "订单日期-开始")
    private String startBillDate;

    @ApiModelProperty(value = "订单日期-结束")
    private String endBillDate;

    /**
     * 挂单开始时间
     */
    @ApiModelProperty(value = "挂单开始时间")
    private String registrationStartTime;

    /**
     * 挂单结束时间
     */
    @ApiModelProperty(value = "挂单结束时间")
    private String registrationEndTime;




}
