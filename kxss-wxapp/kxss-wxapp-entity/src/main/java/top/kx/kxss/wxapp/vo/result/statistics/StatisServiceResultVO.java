package top.kx.kxss.wxapp.vo.result.statistics;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 表单查询方法返回值VO
 * 服务统计
 * </p>
 *
 * <AUTHOR>
 * @date 2023-07-07 13:50:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@Builder
@ApiModel(value = "StatisServiceResultVO", description = "服务统计")
public class StatisServiceResultVO implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 工号
     */
    @ApiModelProperty(value = "工号")
    private String number;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "名称")
    private String empName;

    @ApiModelProperty(value = "名称")
    private String fieldName;

    /**
     * 服务项目名称
     */
    @ApiModelProperty(value = "服务项目名称")
    private String serviceName;

    /**
     * 计费规则
     */
    @ApiModelProperty(value = "计费规则")
    private String cycle;
    /**
     * 点钟方式
     */
    @ApiModelProperty(value = "点钟方式")
    private String clockType;

    /**
     * 营业额
     */
    @ApiModelProperty(value = "营业额")
    private BigDecimal amount;

    /**
     * 营业收入
     */
    @ApiModelProperty(value = "营业收入")
    private BigDecimal payment;

    /**
     * 优惠金额
     */
    @ApiModelProperty(value = "优惠金额")
    private BigDecimal discountAmount;

    /**
     * 开台次数
     */
    @ApiModelProperty(value = "开台次数")
    private Integer num;

    /**
     * 服务时长
     */
    @ApiModelProperty(value = "服务时长")
    private Integer duration;
    /**
     * 计费时长描述
     */
    @ApiModelProperty(value = "服务时长描述")
    private String durationDesc;

    /**
     * 计费时长描述
     */
    @ApiModelProperty(value = "计费时长描述")
    private String chargingDurationDesc;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序", hidden = true)
    @JsonIgnore
    private Integer orderNum;

    /**
     * 计费时长(分钟)
     */
    @ApiModelProperty(value = "计费时长(分钟)")
    private Integer chargingDuration;

    /**
     * 计费次数
     */
    @ApiModelProperty(value = "计费次数")
    private Integer cycleNum;

    @ApiModelProperty(value = "门店")
    private String org;

    @ApiModelProperty(value = "是否套餐")
    private Boolean isThail;

    @ApiModelProperty(value = "组名")
    private String groupName;

    @ApiModelProperty(value = "组名")
    private Long groupId;


}
