<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.wxapp.service.mapper.StatisticsMapper">
<!--
    代码生成器 by 2024-03-21 10:21:10
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <select id="serviceDetailsPage" resultType="java.util.Map">
        select
        pcs.id  as id,
            substr(pc.CODE, 16) AS 'pcCode',
        pc.created_time AS 'createTime',
        pc.complete_time AS 'completeTime',
        pcs.employee_name AS 'employeeName',
        be.name AS 'name',
        be.group_id AS 'groupId',
        pc.org_id AS 'orgId',
        pc.table_id AS 'tableId',
        pcs.service_id AS 'serviceId',
        pcs.clock_type AS 'clockType',
        pcs.created_org_id AS 'createdOrgId',
        pcs.remarks AS 'remarks',
        ROUND(IFNULL(pcs.amount, 0 )
        -IFNULL(pcs.assessed_amount, 0 ), 2) AS 'amount',
        ROUND(IFNULL(pcs.refund_amount, 0 ), 2) AS 'refundAmount',
        ROUND(IFNULL(pcs.amount, 0 )
        -IFNULL(pcs.assessed_amount, 0 )
        -IFNULL(pcs.refund_amount, 0 ), 2) AS 'income',
        pcs.duration AS 'duration',
        IFNULL(
        CASE
        WHEN pcs.cycle IS NULL
        OR pcs.cycle = '' THEN
        0
        WHEN instr( pcs.cycle, '元/' ) <![CDATA[ <= ]]> 0 THEN 0 WHEN instr( pcs.cycle, '元/小时' ) > 0 THEN
        (  IFNULL( pcs.cycle_num, 0 ) * 60 )
        WHEN instr( pcs.cycle, '元/小时' ) <![CDATA[ <= ]]> 0 AND instr( pcs.cycle, '小时' ) > 0 THEN
        (
        IFNULL( pcs.cycle_num, 0 ) * 60 * SUBSTRING_INDEX( SUBSTRING_INDEX( pcs.cycle, '/', -1 ), '小时',  1 ))
        WHEN instr( pcs.cycle, '元/分钟' ) > 0 THEN
        IFNULL( pcs.cycle_num, 0 )
        WHEN instr( pcs.cycle, '元/分钟' ) <![CDATA[ <= ]]> 0 AND instr( pcs.cycle, '分钟' ) > 0 THEN
        (
        IFNULL( pcs.cycle_num, 0 ) * SUBSTRING_INDEX( SUBSTRING_INDEX( pcs.cycle, '/', -1 ), '分钟',  1 )) ELSE 0
        END,
        0
        ) as chargingDuration,
        pcs.cycle AS 'cycle'
        from pos_cash_service pcs
                 LEFT JOIN pos_cash pc ON pcs.cash_id = pc.id
                 left join base_employee be on pcs.employee_id = be.id
        where pcs.delete_flag = 0
          AND pc.delete_flag = 0
          AND pc.bill_state in ('2','5')
          and pc.bill_type in ('0', '3', '4')
          and pcs.status in ('0', '1')
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(pcs.employee_name, #{model.keyword}) or instr(be.name, #{model.keyword}) or instr(pc.code, #{model.keyword}))
        </if>
        <if test="model.employeeName != null and model.employeeName != ''">
            and (instr(be.name, #{model.employeeName}) or instr(be.real_name, #{model.employeeName}))
        </if>
        <if test="model.tableName != null and model.tableName != ''">
            and instr(pc.table_name, #{model.tableName})
        </if>
        <if test="model.serviceId != null">
            and pcs.service_id = #{model.serviceId}
        </if>
        <if test="model.employeeId != null">
            and pcs.employee_id = #{model.employeeId}
        </if>
        <if test="model.commenter != null">
            and pcs.employee_id = #{model.commenter}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.groupIds != null and model.groupIds.size() > 0">
            and be.group_id IN
            <foreach item="groupId" collection="model.groupIds" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        <if test="model.orderSource != null and model.orderSource != ''">
            and pcs.clock_type = #{model.orderSource}
        </if>
        <if test="model.id != null">
            and pcs.service_id = #{model.id}
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        ORDER BY pcs.end_time DESC
    </select>

    <select id="serviceDetailsSum" resultType="java.util.Map">
        select
        ROUND(IFNULL(SUM(IFNULL(pcs.amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.assessed_amount, 0 )), 0 ), 2) as amount,
        ROUND(IFNULL(SUM(IFNULL(pcs.refund_amount, 0 )), 0 ), 2) as refundAmount,
        ROUND(IFNULL(SUM(IFNULL(pcs.amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.assessed_amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.refund_amount, 0 )), 0 ), 2) as income,
        IFNULL(
        sum(CASE
        WHEN pcs.cycle IS NULL
        OR pcs.cycle = '' THEN
        0
        WHEN instr( pcs.cycle, '元/' ) <![CDATA[ <= ]]> 0 THEN 0 WHEN instr( pcs.cycle, '元/小时' ) > 0 THEN
        (  IFNULL( pcs.cycle_num, 0 ) * 60 )
        WHEN instr( pcs.cycle, '元/小时' ) <![CDATA[ <= ]]> 0 AND instr( pcs.cycle, '小时' ) > 0 THEN
        (
        IFNULL( pcs.cycle_num, 0 ) * 60 * SUBSTRING_INDEX( SUBSTRING_INDEX( pcs.cycle, '/', -1 ), '小时',  1 ))
        WHEN instr( pcs.cycle, '元/分钟' ) > 0 THEN
        IFNULL( pcs.cycle_num, 0 )
        WHEN instr( pcs.cycle, '元/分钟' ) <![CDATA[ <= ]]> 0 AND instr( pcs.cycle, '分钟' ) > 0 THEN
        (
        IFNULL( pcs.cycle_num, 0 ) * SUBSTRING_INDEX( SUBSTRING_INDEX( pcs.cycle, '/', -1 ), '分钟',  1 )) ELSE 0
        END),
        0
        ) as chargingDuration,
        SUM(pcs.duration) AS 'duration'
        from pos_cash_service pcs
        LEFT JOIN pos_cash pc ON pcs.cash_id = pc.id
        left join base_employee be on pcs.employee_id = be.id
        LEFT JOIN base_table_info bti ON pc.table_id = bti.id
        LEFT JOIN base_service bs ON pcs.service_id = bs.id
        where pcs.delete_flag = 0
        AND pc.delete_flag = 0
        AND pc.bill_state in ('2','5')
        and pc.bill_type in ('0', '3', '4')
        and pcs.status in ('0', '1')
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(pcs.employee_name, #{model.keyword}) or instr(be.name, #{model.keyword}) or instr(pc.code, #{model.keyword}))
        </if>
        <if test="model.employeeName != null and model.employeeName != ''">
            and (instr(be.name, #{model.employeeName}) or instr(be.real_name, #{model.employeeName}))
        </if>
        <if test="model.tableName != null and model.tableName != ''">
            and instr(pc.table_name, #{model.tableName})
        </if>
        <if test="model.serviceId != null">
            and bs.id = #{model.serviceId}
        </if>
        <if test="model.employeeId != null">
            and pcs.employee_id = #{model.employeeId}
        </if>
        <if test="model.commenter != null">
            and pcs.employee_id = #{model.commenter}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.groupIds != null and model.groupIds.size() > 0">
            and be.group_id IN
            <foreach item="groupId" collection="model.groupIds" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        <if test="model.orderSource != null and model.orderSource != ''">
            and pcs.clock_type = #{model.orderSource}
        </if>
        <if test="model.id != null">
            and bs.id = #{model.id}
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
    </select>

    <select id="serviceDetailsExport" resultType="java.util.Map">
        select
        pcs.id  as id,
        substr(pc.CODE, 16) AS 'pcCode',
        pc.created_time AS 'createTime',
        pc.complete_time AS 'completeTime',
        IFNULL(pcs.employee_name, '-') AS 'employeeName',
        IFNULL(be.name, '-') AS 'name',
        be.group_id AS 'groupId',
        pc.org_id AS 'orgId',
        pc.table_id AS 'tableId',
        pcs.service_id AS 'serviceId',
        pcs.clock_type AS 'clockType',
        pcs.created_org_id AS 'createdOrgId',
        pcs.remarks AS 'remarks',
        ROUND(IFNULL(pcs.amount, 0 )
        -IFNULL(pcs.assessed_amount, 0 ), 2) as amount,
        ROUND(IFNULL(pcs.refund_amount, 0 ), 2) as refundAmount,
        ROUND(IFNULL(pcs.amount, 0 )
        -IFNULL(pcs.assessed_amount, 0 )
        -IFNULL(pcs.refund_amount, 0 ), 2) as income,
        pcs.duration AS 'duration',
        IFNULL(
        CASE
        WHEN pcs.cycle IS NULL
        OR pcs.cycle = '' THEN
        0
        WHEN instr( pcs.cycle, '元/' ) <![CDATA[ <= ]]> 0 THEN 0 WHEN instr( pcs.cycle, '元/小时' ) > 0 THEN
        (  IFNULL( pcs.cycle_num, 0 ) * 60 )
        WHEN instr( pcs.cycle, '元/小时' ) <![CDATA[ <= ]]> 0 AND instr( pcs.cycle, '小时' ) > 0 THEN
        (
        IFNULL( pcs.cycle_num, 0 ) * 60 * SUBSTRING_INDEX( SUBSTRING_INDEX( pcs.cycle, '/', -1 ), '小时',  1 ))
        WHEN instr( pcs.cycle, '元/分钟' ) > 0 THEN
        IFNULL( pcs.cycle_num, 0 )
        WHEN instr( pcs.cycle, '元/分钟' ) <![CDATA[ <= ]]> 0 AND instr( pcs.cycle, '分钟' ) > 0 THEN
        (
        IFNULL( pcs.cycle_num, 0 ) * SUBSTRING_INDEX( SUBSTRING_INDEX( pcs.cycle, '/', -1 ), '分钟',  1 )) ELSE 0
        END,
        0
        ) as chargingDuration,
        pcs.cycle AS 'cycle'
        from pos_cash_service pcs
                 LEFT JOIN pos_cash pc ON pcs.cash_id = pc.id
                 left join base_employee be on pcs.employee_id = be.id
        where pcs.delete_flag = 0
          AND pc.delete_flag = 0
          AND pc.bill_state in ('2','5')
          and pc.bill_type in ('0', '3', '4')
          and pcs.status in ('0', '1')
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(pcs.employee_name, #{model.keyword}) or instr(be.name, #{model.keyword}) or instr(pc.code, #{model.keyword}))
        </if>
        <if test="model.employeeName != null and model.employeeName != ''">
            and (instr(be.name, #{model.employeeName}) or instr(be.real_name, #{model.employeeName}))
        </if>
        <if test="model.tableName != null and model.tableName != ''">
            and instr(pc.table_name, #{model.tableName})
        </if>
        <if test="model.serviceId != null">
            and pcs.service_id = #{model.serviceId}
        </if>
        <if test="model.employeeId != null">
            and pcs.employee_id = #{model.employeeId}
        </if>
        <if test="model.commenter != null">
            and pcs.employee_id = #{model.commenter}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.groupIds != null and model.groupIds.size() > 0">
            and be.group_id IN
            <foreach item="groupId" collection="model.groupIds" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        <if test="model.orderSource != null and model.orderSource != ''">
            and pcs.clock_type = #{model.orderSource}
        </if>
        <if test="model.id != null">
            and pcs.service_id = #{model.id}
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        ORDER BY pcs.end_time DESC
    </select>

    <select id="serviceStatistics" resultType="java.util.Map">
        select
        be.real_name AS 'employeeName',
        be.name AS 'name',
        be.group_id AS 'groupId',
        pc.org_id AS 'orgId',
        pc.table_id AS 'tableId',
        pcs.service_id AS 'serviceId',
        pcs.clock_type AS 'clockType',
        pcs.created_org_id AS 'createdOrgId',
        sum(pcs.duration) AS 'duration',
        IFNULL(
        sum(CASE
        WHEN pcs.cycle IS NULL
        OR pcs.cycle = '' THEN
        0
        WHEN instr( pcs.cycle, '元/' ) <![CDATA[ <= ]]> 0 THEN 0 WHEN instr( pcs.cycle, '元/小时' ) > 0 THEN
        ( IFNULL( pcs.cycle_num, 0 ) * 60 )
        WHEN instr( pcs.cycle, '元/小时' ) <![CDATA[ <= ]]> 0 AND instr( pcs.cycle, '小时' ) > 0 THEN
        (
        IFNULL( pcs.cycle_num, 0 ) * 60 * SUBSTRING_INDEX( SUBSTRING_INDEX( pcs.cycle, '/', -1 ), '小时', 1 ))
        WHEN instr( pcs.cycle, '元/分钟' ) > 0 THEN
        IFNULL( pcs.cycle_num, 0 )
        WHEN instr( pcs.cycle, '元/分钟' ) <![CDATA[ <= ]]> 0 AND instr( pcs.cycle, '分钟' ) > 0 THEN
        (
        IFNULL( pcs.cycle_num, 0 ) * SUBSTRING_INDEX( SUBSTRING_INDEX( pcs.cycle, '/', -1 ), '分钟', 1 )) ELSE 0
        END),
        0
        ) as chargingDuration,
        ROUND(IFNULL(SUM(IFNULL(pcs.amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.assessed_amount, 0 )), 0 ), 2) as amount,
        ROUND(IFNULL(SUM(IFNULL(pcs.refund_amount, 0 )), 0 ), 2) as refundAmount,
        ROUND(IFNULL(SUM(IFNULL(pcs.amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.assessed_amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.refund_amount, 0 )), 0 ), 2) as income,
        count(*) AS 'num'
        from pos_cash_service pcs
        LEFT JOIN pos_cash pc ON pcs.cash_id = pc.id
        LEFT JOIN base_employee be on pcs.employee_id = be.id
            ${ew.customSqlSegment}
    </select>

    <select id="serviceStatisticsSum" resultType="java.util.Map">
        select  sum(pcs.duration) AS 'duration',
        IFNULL(
        sum(CASE
        WHEN pcs.cycle IS NULL
        OR pcs.cycle = '' THEN
        0
        WHEN instr( pcs.cycle, '元/' ) <![CDATA[ <= ]]> 0 THEN 0 WHEN instr( pcs.cycle, '元/小时' ) > 0 THEN
        (  IFNULL( pcs.cycle_num, 0 ) * 60 )
        WHEN instr( pcs.cycle, '元/小时' ) <![CDATA[ <= ]]> 0 AND instr( pcs.cycle, '小时' ) > 0 THEN
        (
        IFNULL( pcs.cycle_num, 0 ) * 60 * SUBSTRING_INDEX( SUBSTRING_INDEX( pcs.cycle, '/', -1 ), '小时',  1 ))
        WHEN instr( pcs.cycle, '元/分钟' ) > 0 THEN
        IFNULL( pcs.cycle_num, 0 )
        WHEN instr( pcs.cycle, '元/分钟' ) <![CDATA[ <= ]]> 0 AND instr( pcs.cycle, '分钟' ) > 0 THEN
        (
        IFNULL( pcs.cycle_num, 0 ) * SUBSTRING_INDEX( SUBSTRING_INDEX( pcs.cycle, '/', -1 ), '分钟',  1 )) ELSE 0
        END),
        0
        ) as chargingDuration,
        ROUND(IFNULL(SUM(IFNULL(pcs.amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.assessed_amount, 0 )), 0 ), 2) as amount,
        ROUND(IFNULL(SUM(IFNULL(pcs.refund_amount, 0 )), 0 ), 2) as refundAmount,
        ROUND(IFNULL(SUM(IFNULL(pcs.amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.assessed_amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.refund_amount, 0 )), 0 ), 2) as income,
        count(*) AS 'num'
        from pos_cash_service pcs
        LEFT JOIN pos_cash pc ON pcs.cash_id = pc.id
        LEFT JOIN base_service bs ON pcs.service_id = bs.id
            ${ew.customSqlSegment}
    </select>

    <select id="serviceStatisticsExport" resultType="java.util.Map">
        select
        IFNULL(be.real_name, '-') AS 'employeeName',
        IFNULL(be.name, '-') AS 'name',
        be.group_id AS 'groupId',
        pc.org_id AS 'orgId',
        pc.table_id AS 'tableId',
        pcs.service_id AS 'serviceId',
        pcs.clock_type AS 'clockType',
        pcs.created_org_id AS 'createdOrgId',
        sum(ifnull(pcs.duration, 0)) AS 'duration',
        IFNULL(
        sum(CASE
        WHEN pcs.cycle IS NULL
        OR pcs.cycle = '' THEN
        0
        WHEN instr( pcs.cycle, '元/' ) <![CDATA[ <= ]]> 0 THEN 0 WHEN instr( pcs.cycle, '元/小时' ) > 0 THEN
        (  IFNULL( pcs.cycle_num, 0 ) * 60 )
        WHEN instr( pcs.cycle, '元/小时' ) <![CDATA[ <= ]]> 0 AND instr( pcs.cycle, '小时' ) > 0 THEN
        (
        IFNULL( pcs.cycle_num, 0 ) * 60 * SUBSTRING_INDEX( SUBSTRING_INDEX( pcs.cycle, '/', -1 ), '小时',  1 ))
        WHEN instr( pcs.cycle, '元/分钟' ) > 0 THEN
        IFNULL( pcs.cycle_num, 0 )
        WHEN instr( pcs.cycle, '元/分钟' ) <![CDATA[ <= ]]> 0 AND instr( pcs.cycle, '分钟' ) > 0 THEN
        (
        IFNULL( pcs.cycle_num, 0 ) * SUBSTRING_INDEX( SUBSTRING_INDEX( pcs.cycle, '/', -1 ), '分钟',  1 )) ELSE 0
        END),
        0
        ) as chargingDuration,
        ROUND(IFNULL(SUM(IFNULL(pcs.amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.assessed_amount, 0 )), 0 ), 2) as amount,
        ROUND(IFNULL(SUM(IFNULL(pcs.refund_amount, 0 )), 0 ), 2) as refundAmount,
        ROUND(IFNULL(SUM(IFNULL(pcs.amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.assessed_amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.refund_amount, 0 )), 0 ), 2) as income,
        count(*) AS 'num'
        from pos_cash_service pcs
        LEFT JOIN pos_cash pc ON pcs.cash_id = pc.id
        LEFT JOIN base_employee be on pcs.employee_id = be.id
            ${ew.customSqlSegment}
    </select>

    <select id="serviceAmount" resultType="java.util.Map">
        select pcs.employee_name AS 'employeeName',
        IFNULL(SUM(IFNULL(pcs.amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.assessed_amount, 0 ))
        -IFNULL(SUM(IFNULL(pcs.refund_amount, 0 )), 0 ), 0 ) as amount,
                count(*) AS 'num'
        from pos_cash_service pcs
                 LEFT JOIN pos_cash pc ON pcs.cash_id = pc.id
        where pcs.delete_flag = 0
          AND pc.delete_flag = 0
          AND pc.bill_state = '2'
          and pc.bill_type in ('0', '3', '4')
        <if test="model.employeeName != null and model.employeeName != ''">
            and instr(pcs.employee_name, #{model.employeeName})
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        GROUP BY pcs.employee_id
    </select>

    <select id="serviceAmountSum" resultType="java.util.Map">
        select
        IFNULL(SUM(IFNULL(pcs.amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.assessed_amount, 0 ))
        -IFNULL(SUM(IFNULL(pcs.refund_amount, 0 )), 0 ), 0 ) as amount,
        count(*) AS 'num'
        from pos_cash_service pcs
        LEFT JOIN pos_cash pc ON pcs.cash_id = pc.id
        where pcs.delete_flag = 0
        AND pc.delete_flag = 0
        AND pc.bill_state = '2'
        and pc.bill_type in ('0', '3', '4')
        <if test="model.employeeName != null and model.employeeName != ''">
            and instr(pcs.employee_name, #{model.employeeName})
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
    </select>

    <select id="serviceAmountExport" resultType="java.util.Map">
        select ifnull(pcs.employee_name, '-') AS 'employeeName',
        IFNULL(SUM(IFNULL(pcs.amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.assessed_amount, 0 ))
        -IFNULL(SUM(IFNULL(pcs.refund_amount, 0 )), 0 ), 0 ) as amount,
        count(*) AS 'num'
        from pos_cash_service pcs
        LEFT JOIN pos_cash pc ON pcs.cash_id = pc.id
        where pcs.delete_flag = 0
        AND pc.delete_flag = 0
        AND pc.bill_state = '2'
        and pc.bill_type in ('0', '3', '4')
        <if test="model.employeeName != null and model.employeeName != ''">
            and instr(pcs.employee_name, #{model.employeeName})
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        GROUP BY pcs.employee_id
    </select>

    <!-- 服务汇总查询 - 去掉completeTime字段，按员工和服务项目汇总 -->
    <select id="serviceStatisticsSummary" resultType="java.util.Map">
        select pcs.employee_id AS 'employeeId',
        be.name AS 'name',
        be.group_id AS 'groupId',
        pc.org_id AS 'orgId',
        pc.table_id AS 'tableId',
        pcs.service_id AS 'serviceId',
        pcs.clock_type AS 'clockType',
        pcs.created_org_id AS 'createdOrgId',
        sum(pcs.duration) AS 'duration',
        IFNULL(
        sum(CASE
        WHEN pcs.cycle IS NULL
        OR pcs.cycle = '' THEN
        0
        WHEN instr( pcs.cycle, '元/' ) <![CDATA[ <= ]]> 0 THEN 0 WHEN instr( pcs.cycle, '元/小时' ) > 0 THEN
        ( IFNULL( pcs.cycle_num, 0 ) * 60 )
        WHEN instr( pcs.cycle, '元/小时' ) <![CDATA[ <= ]]> 0 AND instr( pcs.cycle, '小时' ) > 0 THEN
        (
        IFNULL( pcs.cycle_num, 0 ) * 60 * SUBSTRING_INDEX( SUBSTRING_INDEX( pcs.cycle, '/', -1 ), '小时', 1 ))
        WHEN instr( pcs.cycle, '元/分钟' ) > 0 THEN
        IFNULL( pcs.cycle_num, 0 )
        WHEN instr( pcs.cycle, '元/分钟' ) <![CDATA[ <= ]]> 0 AND instr( pcs.cycle, '分钟' ) > 0 THEN
        (
        IFNULL( pcs.cycle_num, 0 ) * SUBSTRING_INDEX( SUBSTRING_INDEX( pcs.cycle, '/', -1 ), '分钟', 1 )) ELSE 0
        END),
        0
        ) as chargingDuration,
        ROUND(IFNULL(SUM(IFNULL(pcs.amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.assessed_amount, 0 )), 0 ), 2) as amount,
        ROUND(IFNULL(SUM(IFNULL(pcs.refund_amount, 0 )), 0 ), 2) as refundAmount,
        ROUND(IFNULL(SUM(IFNULL(pcs.amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.assessed_amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.refund_amount, 0 )), 0 ), 2) as income,
        count(*) AS 'num'
        from pos_cash_service pcs
        LEFT JOIN pos_cash pc ON pcs.cash_id = pc.id
        LEFT JOIN base_employee be on pcs.employee_id = be.id
        where pcs.delete_flag = 0
        AND pc.delete_flag = 0
        AND pc.bill_state IN ('2', '5')
        and pc.bill_type in ('0', '3', '4')
        and pcs.status in ('0', '1')
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(pcs.employee_name, #{model.keyword}) or instr(be.name, #{model.keyword}))
        </if>
        <if test="model.id != null">
            and pcs.service_id = #{model.id}
        </if>
        <if test="model.serviceId != null">
            and pcs.service_id = #{model.serviceId}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pcs.created_org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.groupIds != null and model.groupIds.size() > 0">
            and be.group_id IN
            <foreach item="groupId" collection="model.groupIds" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        GROUP BY pcs.employee_id, pcs.service_id, pc.org_id
        ORDER BY pcs.employee_name
    </select>

    <select id="serviceStatisticsSummarySum" resultType="java.util.Map">
        select  sum(pcs.duration) AS 'duration',
        IFNULL(
        sum(CASE
        WHEN pcs.cycle IS NULL
        OR pcs.cycle = '' THEN
        0
        WHEN instr( pcs.cycle, '元/' ) <![CDATA[ <= ]]> 0 THEN 0 WHEN instr( pcs.cycle, '元/小时' ) > 0 THEN
        (  IFNULL( pcs.cycle_num, 0 ) * 60 )
        WHEN instr( pcs.cycle, '元/小时' ) <![CDATA[ <= ]]> 0 AND instr( pcs.cycle, '小时' ) > 0 THEN
        (
        IFNULL( pcs.cycle_num, 0 ) * 60 * SUBSTRING_INDEX( SUBSTRING_INDEX( pcs.cycle, '/', -1 ), '小时',  1 ))
        WHEN instr( pcs.cycle, '元/分钟' ) > 0 THEN
        IFNULL( pcs.cycle_num, 0 )
        WHEN instr( pcs.cycle, '元/分钟' ) <![CDATA[ <= ]]> 0 AND instr( pcs.cycle, '分钟' ) > 0 THEN
        (
        IFNULL( pcs.cycle_num, 0 ) * SUBSTRING_INDEX( SUBSTRING_INDEX( pcs.cycle, '/', -1 ), '分钟',  1 )) ELSE 0
        END),
        0
        ) as chargingDuration,
        ROUND(IFNULL(SUM(IFNULL(pcs.amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.assessed_amount, 0 )), 0 ), 2) as amount,
        ROUND(IFNULL(SUM(IFNULL(pcs.refund_amount, 0 )), 0 ), 2) as refundAmount,
        ROUND(IFNULL(SUM(IFNULL(pcs.amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.assessed_amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.refund_amount, 0 )), 0 ), 2) as income,
        count(*) AS 'num'
        from pos_cash_service pcs
        LEFT JOIN pos_cash pc ON pcs.cash_id = pc.id
        LEFT JOIN base_service bs ON pcs.service_id = bs.id
        LEFT JOIN base_dict bd ON pcs.clock_type = bd.key_ AND bd.parent_key = 'CLOCK_TYPE'
        where pcs.delete_flag = 0
        AND pc.delete_flag = 0
        AND pc.bill_state IN ('2', '5')
        and pc.bill_type in ('0', '3', '4')
        and pcs.status in ('0', '1')
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(pcs.employee_name, #{model.keyword}) or instr(be.name, #{model.keyword}))
        </if>
        <if test="model.id != null">
            and bs.id = #{model.id}
        </if>
        <if test="model.serviceId != null">
            and bs.id = #{model.serviceId}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.groupIds != null and model.groupIds.size() > 0">
            and be.group_id IN
            <foreach item="groupId" collection="model.groupIds" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and bd.created_org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
    </select>

    <select id="serviceStatisticsSummaryExport" resultType="java.util.Map">
        select pcs.employee_id AS 'employeeId',
        IFNULL(be.name, '-') AS 'name',
        be.group_id AS 'groupId',
        pc.org_id AS 'orgId',
        pc.table_id AS 'tableId',
        pcs.service_id AS 'serviceId',
        pcs.clock_type AS 'clockType',
        pcs.created_org_id AS 'createdOrgId',
        sum(ifnull(pcs.duration, 0)) AS 'duration',
        IFNULL(
        sum(CASE
        WHEN pcs.cycle IS NULL
        OR pcs.cycle = '' THEN
        0
        WHEN instr( pcs.cycle, '元/' ) <![CDATA[ <= ]]> 0 THEN 0 WHEN instr( pcs.cycle, '元/小时' ) > 0 THEN
        (  IFNULL( pcs.cycle_num, 0 ) * 60 )
        WHEN instr( pcs.cycle, '元/小时' ) <![CDATA[ <= ]]> 0 AND instr( pcs.cycle, '小时' ) > 0 THEN
        (
        IFNULL( pcs.cycle_num, 0 ) * 60 * SUBSTRING_INDEX( SUBSTRING_INDEX( pcs.cycle, '/', -1 ), '小时',  1 ))
        WHEN instr( pcs.cycle, '元/分钟' ) > 0 THEN
        IFNULL( pcs.cycle_num, 0 )
        WHEN instr( pcs.cycle, '元/分钟' ) <![CDATA[ <= ]]> 0 AND instr( pcs.cycle, '分钟' ) > 0 THEN
        (
        IFNULL( pcs.cycle_num, 0 ) * SUBSTRING_INDEX( SUBSTRING_INDEX( pcs.cycle, '/', -1 ), '分钟',  1 )) ELSE 0
        END),
        0
        ) as chargingDuration,
        ROUND(IFNULL(SUM(IFNULL(pcs.amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.assessed_amount, 0 )), 0 ), 2) as amount,
        ROUND(IFNULL(SUM(IFNULL(pcs.refund_amount, 0 )), 0 ), 2) as refundAmount,
        ROUND(IFNULL(SUM(IFNULL(pcs.amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.assessed_amount, 0 )), 0 )
        -IFNULL(SUM(IFNULL(pcs.refund_amount, 0 )), 0 ), 2) as income,
        count(*) AS 'num'
        from pos_cash_service pcs
        LEFT JOIN pos_cash pc ON pcs.cash_id = pc.id
        LEFT JOIN base_employee be on pcs.employee_id = be.id
        where pcs.delete_flag = 0
        AND pc.delete_flag = 0
        AND pc.bill_state IN ('2', '5')
        and pc.bill_type in ('0', '3', '4')
        and pcs.status in ('0', '1')
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(pcs.employee_name, #{model.keyword}) or instr(be.name, #{model.keyword}))
        </if>
        <if test="model.id != null">
            and pcs.service_id = #{model.id}
        </if>
        <if test="model.serviceId != null">
            and pcs.service_id = #{model.serviceId}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pcs.created_org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.groupIds != null and model.groupIds.size() > 0">
            and be.group_id IN
            <foreach item="groupId" collection="model.groupIds" open="(" separator="," close=")">
                #{groupId}
            </foreach>
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        GROUP BY pcs.employee_id, pcs.service_id, pc.org_id
        ORDER BY pcs.employee_name
    </select>

    <select id="paymentDetails" resultType="java.util.Map">
        select pc.CODE AS 'code',
        pc.created_time AS 'createdTime',
        pc.complete_time AS 'completeTime',
        pcp.pay_time AS 'payTime',
        pcp.pay_name AS 'payName',
        pcp.amount-IFNULL(pcp.refund_amount,0)-IFNULL(pcp.change_amount,0) AS 'paidIn',
        pcp.amount-IFNULL(pcp.change_amount,0) AS 'amount',
        pcp.order_id AS 'orderId',
        IFNULL(pcp.refund_amount,0) as 'refundAmount',
        IFNULL(pcp.change_amount,0) as 'changeAmount',
        IF(bpt.biz_type = 1, pcp.recharge_amount, '-') AS 'rechargeAmount',
        IF(bpt.biz_type = 1, pcp.gift_amount, '-') AS 'giftAmount',
        bo.name AS 'org'
        from pos_cash pc
        LEFT JOIN pos_cash_payment pcp ON pc.id = pcp.cash_id
        LEFT JOIN base_payment_type bpt ON bpt.id = pcp.pay_type_id
        LEFT JOIN base_org bo on pc.org_id = bo.id
        where pc.delete_flag = 0
        AND pc.bill_type IN ('0', '3', '4')
        AND pc.bill_state IN ('2', '5', '6')
        AND pcp.STATUS = 2
        AND pcp.delete_flag = 0
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.completeTime_st != null and model.completeTime_st != ''">
            and pc.complete_time >= #{model.completeTime_st}
        </if>
        <if test="model.completeTime_ed != null and model.completeTime_ed != ''">
            and pc.complete_time &lt;= #{model.completeTime_ed}
        </if>
        <if test="model.payTime_st != null and model.payTime_st != ''">
            and pcp.pay_time >= #{model.payTime_st}
        </if>
        <if test="model.payTime_ed != null and model.payTime_ed != ''">
            and pcp.pay_time &lt;= #{model.payTime_ed}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.payTypeIds != null and model.payTypeIds.size() > 0">
            and bpt.id IN
            <foreach item="payTypeId" collection="model.payTypeIds" open="(" separator="," close=")">
                #{payTypeId}
            </foreach>
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and instr(pc.code, #{model.keyword})
        </if>
        <if test="model.payTypeId != null">
            and pcp.pay_type_id = #{model.payTypeId}
        </if>
        ORDER BY pc.complete_time DESC
    </select>

    <select id="paymentDetailsSum" resultType="java.util.Map">
        select
        ROUND(sum(pcp.amount)-SUM(IFNULL(pcp.change_amount,0)), 2) AS 'amount',
        ROUND(sum(pcp.amount) -SUM(IFNULL(pcp.refund_amount,0))
                  -SUM(IFNULL(pcp.change_amount,0)), 2) AS 'paidIn',
        ROUND(SUM(IFNULL(pcp.refund_amount,0)), 2) as 'refundAmount',
        ROUND(sum(IF(bpt.biz_type = 1, pcp.recharge_amount, 0)), 2) AS 'rechargeAmount',
        ROUND(sum(IF(bpt.biz_type = 1, pcp.gift_amount, 0)), 2) AS 'giftAmount'
        from pos_cash pc
        LEFT JOIN pos_cash_payment pcp ON pc.id = pcp.cash_id
        LEFT JOIN base_payment_type bpt ON bpt.id = pcp.pay_type_id
        where pc.delete_flag = 0
        AND pc.bill_type IN ('0', '3', '4')
        AND pc.bill_state IN ('2', '5', '6')
        AND pcp.STATUS = 2
        AND pcp.delete_flag = 0
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.completeTime_st != null and model.completeTime_st != ''">
            and pc.complete_time >= #{model.completeTime_st}
        </if>
        <if test="model.completeTime_ed != null and model.completeTime_ed != ''">
            and pc.complete_time &lt;= #{model.completeTime_ed}
        </if>
        <if test="model.payTime_st != null and model.payTime_st != ''">
            and pcp.pay_time >= #{model.payTime_st}
        </if>
        <if test="model.payTime_ed != null and model.payTime_ed != ''">
            and pcp.pay_time &lt;= #{model.payTime_ed}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.payTypeIds != null and model.payTypeIds.size() > 0">
            and pcp.pay_type_id IN
            <foreach item="payTypeId" collection="model.payTypeIds" open="(" separator="," close=")">
                #{payTypeId}
            </foreach>
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and instr(pc.code, #{model.keyword})
        </if>
        <if test="model.payTypeId != null">
            and pcp.pay_type_id = #{model.payTypeId}
        </if>
    </select>

    <select id="paymentDetailsExport" resultType="java.util.Map">
        select pc.CODE AS 'code',
        pc.created_time AS 'createdTime',
        pc.complete_time AS 'completeTime',
        pcp.pay_time AS 'payTime',
        ifnull(pcp.pay_name, '-') AS 'payName',
        ifnull(pcp.amount, 0.00)- IFNULL(pcp.change_amount,0) AS 'amount',
        ifnull(pcp.amount, 0.00) - IFNULL(pcp.refund_amount,0)- IFNULL(pcp.change_amount,0) AS 'paidIn',
        IFNULL(pcp.order_id, '-') AS 'orderId',
        IFNULL(pcp.refund_amount,0) AS 'refundAmount',
        IF(bpt.biz_type = 1, pcp.recharge_amount, '-') AS 'rechargeAmount',
        IF(bpt.biz_type = 1, pcp.gift_amount, '-') AS 'giftAmount',
        IFNULL(bo.name, '-') AS org
        from pos_cash pc
        LEFT JOIN pos_cash_payment pcp ON pc.id = pcp.cash_id
        LEFT JOIN base_payment_type bpt ON bpt.id = pcp.pay_type_id
        LEFT JOIN base_org bo on pc.org_id = bo.id
        where pc.delete_flag = 0
        AND pc.bill_type IN ('0', '3', '4')
        AND pc.bill_state IN ('2', '5', '6')
        AND pcp.STATUS = 2
        AND pcp.delete_flag = 0
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.completeTime_st != null and model.completeTime_st != ''">
            and pc.complete_time >= #{model.completeTime_st}
        </if>
        <if test="model.completeTime_ed != null and model.completeTime_ed != ''">
            and pc.complete_time &lt;= #{model.completeTime_ed}
        </if>
        <if test="model.payTime_st != null and model.payTime_st != ''">
            and pcp.pay_time >= #{model.payTime_st}
        </if>
        <if test="model.payTime_ed != null and model.payTime_ed != ''">
            and pcp.pay_time &lt;= #{model.payTime_ed}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.payTypeIds != null and model.payTypeIds.size() > 0">
            and pcp.pay_type_id IN
            <foreach item="payTypeId" collection="model.payTypeIds" open="(" separator="," close=")">
                #{payTypeId}
            </foreach>
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and instr(pc.code, #{model.keyword})
        </if>
        <if test="model.payTypeId != null">
            and pcp.pay_type_id = #{model.payTypeId}
        </if>
        ORDER BY pc.complete_time DESC
    </select>

    <select id="memberRechargeDetails" resultType="java.util.Map">
        select mi.code AS 'id',
                mi.NAME AS 'name',
                CONCAT(SUBSTRING(mi.mobile, 1, 3), '****',
                SUBSTRING(mi.mobile, 8)) AS 'mobile',
                mg.NAME AS 'gradeName',
                pc.complete_time AS 'completeTime',
                pc.amount AS 'amount',
                pc.gift_amount AS 'giftAmount', case
            when pc.is_first_recharge = true then '首充'
            when pc.is_first_recharge = false then '续充'
            else '-' end AS 'payType',
                bp.pay_name AS 'payName',
                ifnull(be.real_name, '-') AS 'employeeName',
                bc.real_name AS 'createdEmp',
                IFNULL(bo.name, '-') AS org
        from pos_cash pc
                 LEFT JOIN base_org bo on pc.org_id = bo.id
                 left join member_info mi ON pc.member_id = mi.id
                 left join member_grade mg on mi.grade_id = mg.id
                 left join base_payment bp on bp.source_id = pc.id and bp.delete_flag = 0
                 left join base_employee be on pc.employee_id = be.id
                 left join base_employee bc on pc.created_emp = bc.id
        where pc.delete_flag = 0
          AND pc.bill_state = '2'
          AND pc.type_ = '3'
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.completeTime_st != null and model.completeTime_st != ''">
            and pc.complete_time >= #{model.completeTime_st}
        </if>
        <if test="model.completeTime_ed != null and model.completeTime_ed != ''">
            and pc.complete_time &lt;= #{model.completeTime_ed}
        </if>
        <if test="model.createdEmp != null">
            and pc.created_by = #{model.createdEmp}
        </if>
        <if test="model.employeeId != null">
            and pc.employee_id = #{model.employeeId}
        </if>
        <if test="model.commenter != null">
            and pc.employee_id = #{model.commenter}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.gradeIds != null and model.gradeIds.size() > 0">
            and mg.id IN
            <foreach item="gradeId" collection="model.gradeIds" open="(" separator="," close=")">
                #{gradeId}
            </foreach>
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(mi.mobile, #{model.keyword}) or instr(mi.NAME, #{model.keyword})  or instr(mi.code, #{model.keyword})  or instr(be.real_name, #{model.keyword}))
        </if>
        <if test="model.gradeId != null">
            and mi.grade_id = #{model.gradeId}
        </if>
        order by pc.created_time desc
    </select>

    <select id="memberRechargeDetailsSum"  resultType="java.util.Map">
        select
        sum(pc.amount) AS 'amount',
        sum(pc.gift_amount) AS 'giftAmount'
        from pos_cash pc
        left join member_info mi ON pc.member_id = mi.id
        left join member_grade mg on mi.grade_id = mg.id
        left join base_employee be on pc.employee_id = be.id
        where pc.delete_flag = 0
        AND pc.bill_state = '2'
        AND pc.type_ = '3'
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.completeTime_st != null and model.completeTime_st != ''">
            and pc.complete_time >= #{model.completeTime_st}
        </if>
        <if test="model.completeTime_ed != null and model.completeTime_ed != ''">
            and pc.complete_time &lt;= #{model.completeTime_ed}
        </if>
        <if test="model.createdEmp != null">
            and pc.created_by = #{model.createdEmp}
        </if>
        <if test="model.employeeId != null">
            and pc.employee_id = #{model.employeeId}
        </if>
        <if test="model.commenter != null">
            and pc.employee_id = #{model.commenter}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.gradeIds != null and model.gradeIds.size() > 0">
            and mg.id IN
            <foreach item="gradeId" collection="model.gradeIds" open="(" separator="," close=")">
                #{gradeId}
            </foreach>
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(mi.mobile, #{model.keyword}) or instr(mi.NAME, #{model.keyword})  or instr(mi.code, #{model.keyword}) or instr(be.real_name, #{model.keyword}))
        </if>
        <if test="model.gradeId != null">
            and mi.grade_id = #{model.gradeId}
        </if>
    </select>

    <select id="memberRechargeDetailsExport" resultType="java.util.Map">
        select CAST(mi.code AS CHAR) AS 'id',
        ifnull(mi.NAME, '-') AS 'name',
        if(#{model.isEncryptMobile}, CONCAT(SUBSTRING(mi.mobile, 1, 3), '****',
        SUBSTRING(mi.mobile, 8)), mi.mobile) AS 'mobile',
        ifnull(mg.NAME, '-') AS 'gradeName',
        pc.complete_time AS 'completeTime',
        ifnull(pc.amount, 0.00) AS 'amount',
        pc.gift_amount AS 'giftAmount', case
        when pc.is_first_recharge = true then '首充'
        when pc.is_first_recharge = false then '续充'
        else '-' end AS 'payType',
        ifnull(bp.pay_name, '-') AS 'payName',
        ifnull(be.real_name, '-') AS 'employeeName',
        ifnull(bc.real_name, '-') AS 'createdEmp',
        IFNULL(bo.name, '-') AS org
        from pos_cash pc
        LEFT JOIN base_org bo on pc.org_id = bo.id
        left join member_info mi ON pc.member_id = mi.id
        left join member_grade mg on mi.grade_id = mg.id
        left join base_payment bp on bp.source_id = pc.id and bp.delete_flag = 0
        left join base_employee be on pc.employee_id = be.id
        left join base_employee bc on pc.created_emp = bc.id
        where pc.delete_flag = 0
        AND pc.bill_state = '2'
        AND pc.type_ = '3'
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.completeTime_st != null and model.completeTime_st != ''">
            and pc.complete_time >= #{model.completeTime_st}
        </if>
        <if test="model.completeTime_ed != null and model.completeTime_ed != ''">
            and pc.complete_time &lt;= #{model.completeTime_ed}
        </if>
        <if test="model.createdEmp != null">
            and pc.created_by = #{model.createdEmp}
        </if>
        <if test="model.employeeId != null">
            and pc.employee_id = #{model.employeeId}
        </if>
        <if test="model.commenter != null">
            and pc.employee_id = #{model.commenter}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.gradeIds != null and model.gradeIds.size() > 0">
            and mg.id IN
            <foreach item="gradeId" collection="model.gradeIds" open="(" separator="," close=")">
                #{gradeId}
            </foreach>
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(mi.mobile, #{model.keyword}) or instr(mi.NAME, #{model.keyword})  or instr(mi.code, #{model.keyword})  or instr(be.real_name, #{model.keyword}))
        </if>
        <if test="model.gradeId != null">
            and mi.grade_id = #{model.gradeId}
        </if>
        order by pc.created_time asc
    </select>

    <select id="memberConsume"  resultType="java.util.Map">
        select m.`name`                                                           AS 'name',
               m.id                  AS memberId,
               m.code                                                           AS 'code',
        (CASE m.sex WHEN '1' THEN '男' WHEN '2' THEN '女' ELSE '未知' END) AS 'sex',
        CONCAT(SUBSTRING(m.`mobile`, 1, 3), '****',
        SUBSTRING(m.`mobile`, 8))                                                         AS 'mobile',
        g.`name`                                                           AS 'grade',
        SUM(IFNULL(p.paid, 0))                                             AS 'totalPaid',
        count(p.id)                                                        AS 'num',
        SUM(IFNULL(p.table_amount, 0))                                     AS 'tableAmount',
        SUM(IFNULL(p.product_amount, 0))                                   AS 'productAmount',
        SUM(IFNULL(p.service_amount, 0))                                   AS 'serviceAmount',
        SUM(IFNULL(p.thail_amount, 0))                                     AS 'thailAmount',
        SUM(IFNULL(p.buy_card_amount, 0))                                  AS 'buyCardAmount',
        IFNULL(bo.name, '-') AS org
        from pos_cash p
        LEFT JOIN base_org bo on p.org_id = bo.id
        LEFT JOIN member_info m ON m.id = p.member_id
        LEFT JOIN member_grade g ON g.id = m.grade_id
        where p.bill_state in ('2','5')
        AND p.bill_type IN ('0', '3', '4')
        AND p.type_ != '3'
        AND p.delete_flag = 0
        AND p.member_id IS NOT NULL
        <if test="model.startDate != null and model.startDate != ''">
            and p.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.mobile != null and model.mobile != ''">
            and instr(m.mobile, #{model.mobile})
        </if>
        <if test="model.gradeId != null">
            and m.grade_id = #{model.gradeId}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and p.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.gradeIds != null and model.gradeIds.size() > 0">
            and m.grade_id IN
            <foreach item="gradeId" collection="model.gradeIds" open="(" separator="," close=")">
                #{gradeId}
            </foreach>
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(m.name, #{model.keyword}) or instr(m.mobile, #{model.keyword}) or instr(m.code, #{model.keyword}))
        </if>
        GROUP BY p.member_id
        ORDER BY SUM(IFNULL(p.paid, 0)) DESC
    </select>

    <select id="memberConsumeAmount" resultType="top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO">
        select p.member_id                   AS memberId,
               SUM(IFNULL(pay.amount, 0))       AS amount,
               SUM(IFNULL(pay.recharge_amount, 0)) AS rechargeAmount,
               SUM(IFNULL(pay.gift_amount, 0)) AS giftAmount
        from pos_cash_payment pay
                 INNER JOIN pos_cash p ON p.id = pay.cash_id
                 LEFT JOIN member_info m ON m.id = p.member_id
        where p.bill_state in ('2','5')
          AND p.bill_type IN ('0', '3', '4')
          AND p.type_ != '3'
          AND p.delete_flag = 0
          AND p.member_id IS NOT NULL
          AND pay.delete_flag = 0
          AND pay.status = '2'
        <if test="model.startDate != null and model.startDate != ''">
            and p.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.mobile != null and model.mobile != ''">
            and instr(m.mobile, #{model.mobile})
        </if>
        <if test="model.gradeId != null">
            and m.grade_id = #{model.gradeId}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and p.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.gradeIds != null and model.gradeIds.size() > 0">
            and m.grade_id IN
            <foreach item="gradeId" collection="model.gradeIds" open="(" separator="," close=")">
                #{gradeId}
            </foreach>
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(m.name, #{model.keyword}) or instr(m.mobile, #{model.keyword}) or instr(m.code, #{model.keyword}))
        </if>
        GROUP BY p.member_id
    </select>

    <select id="memberConsumeSum"  resultType="java.util.Map">
        select
        SUM(IFNULL(p.paid, 0))                                             AS 'totalPaid',
        count(p.id)                                                        AS 'num',
        SUM(IFNULL(p.table_amount, 0))                                     AS 'tableAmount',
        SUM(IFNULL(p.product_amount, 0))                                   AS 'productAmount',
        SUM(IFNULL(p.service_amount, 0))                                   AS 'serviceAmount',
        SUM(IFNULL(p.thail_amount, 0))                                     AS 'thailAmount',
        SUM(IFNULL(p.buy_card_amount, 0))                                  AS 'buyCardAmount'
        from pos_cash p
        LEFT JOIN member_info m ON m.id = p.member_id
        LEFT JOIN member_grade g ON g.id = m.grade_id
        where p.bill_state in ('2','5')
        AND p.bill_type IN ('0', '3', '4')
        AND p.type_ != '3'
        AND p.delete_flag = 0
        AND p.member_id IS NOT NULL
        <if test="model.startDate != null and model.startDate != ''">
            and p.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.mobile != null and model.mobile != ''">
            and instr(m.mobile, #{model.mobile})
        </if>
        <if test="model.gradeId != null">
            and m.grade_id = #{model.gradeId}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and p.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.gradeIds != null and model.gradeIds.size() > 0">
            and g.id IN
            <foreach item="gradeId" collection="model.gradeIds" open="(" separator="," close=")">
                #{gradeId}
            </foreach>
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(m.name, #{model.keyword}) or instr(m.mobile, #{model.keyword})  or instr(m.code, #{model.keyword}))
        </if>
    </select>

    <select id="memberConsumeExport"  resultType="java.util.Map">
        select
        p.member_id                   AS memberId,
            ifnull(m.`name`, '-')                                                           AS 'name',
               m.code                                                           AS 'code',
        (CASE m.sex WHEN '1' THEN '男' WHEN '2' THEN '女' ELSE '未知' END) AS 'sex',
        if(#{model.isEncryptMobile} = true, CONCAT(SUBSTRING(m.`mobile`, 1, 3), '****',
        SUBSTRING(m.`mobile`, 8)), m.mobile)                               AS 'mobile',
        ifnull(g.`name`, '-')                                              AS 'grade',
        SUM(IFNULL(p.paid, 0))                                             AS 'totalPaid',
        count(p.id)                                                        AS 'num',
        SUM(IFNULL(p.table_amount, 0))                                     AS 'tableAmount',
        SUM(IFNULL(p.product_amount, 0))                                   AS 'productAmount',
        SUM(IFNULL(p.service_amount, 0))                                   AS 'serviceAmount',
        SUM(IFNULL(p.thail_amount, 0))                                     AS 'thailAmount',
        SUM(IFNULL(p.buy_card_amount, 0))                                  AS 'buyCardAmount',
        IFNULL(bo.name, '-') AS org
        from  pos_cash p
        LEFT JOIN base_org bo on p.org_id = bo.id
        LEFT JOIN member_info m ON m.id = p.member_id
        LEFT JOIN member_grade g ON g.id = m.grade_id
        where p.bill_state in ('2','5')
        AND p.bill_type IN ('0', '3', '4')
        AND p.type_ != '3'
        AND p.delete_flag = 0
        AND p.member_id IS NOT NULL
        <if test="model.startDate != null and model.startDate != ''">
            and p.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.mobile != null and model.mobile != ''">
            and instr(m.mobile, #{model.mobile})
        </if>
        <if test="model.gradeId != null">
            and m.grade_id = #{model.gradeId}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and p.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.gradeIds != null and model.gradeIds.size() > 0">
            and g.id IN
            <foreach item="gradeId" collection="model.gradeIds" open="(" separator="," close=")">
                #{gradeId}
            </foreach>
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(m.name, #{model.keyword}) or instr(m.mobile, #{model.keyword}) or instr(m.code, #{model.keyword}))
        </if>
        GROUP BY p.member_id
        ORDER BY SUM(IFNULL(p.paid, 0)) DESC
    </select>

    <select id="memberRecharge"  resultType="java.util.Map">
        select m.`name`                                                           AS 'name',
               m.code                                                             AS 'code',
                (CASE m.sex WHEN '1' THEN '男' WHEN '2' THEN '女' ELSE '未知' END) AS 'sex',
        CONCAT(SUBSTRING(m.`mobile`, 1, 3), '****',
        SUBSTRING(m.`mobile`, 8))                                                         AS 'mobile',
                g.`name`                                                           AS 'gradeName',
                count(p.id)                                                        AS 'num',
                SUM(IFNULL(p.payment, 0)) + SUM(IFNULL(p.gift_amount, 0))          AS 'totalPayment',
                SUM(IFNULL(p.payment, 0))                                          AS 'payment',
                SUM(IFNULL(p.gift_amount, 0))                                      AS 'giftAmount',
                IFNULL(bo.name, '-') AS org
        from pos_cash p
                 LEFT JOIN base_org bo on p.org_id = bo.id
                 LEFT JOIN member_info m ON m.id = p.member_id
                 LEFT JOIN member_grade g ON g.id = m.grade_id
        where p.bill_state = '2'
          AND p.bill_type IN (0, 3, 4)
          AND p.type_ = 3
          AND p.delete_flag = 0
          AND p.member_id IS NOT NULL
        <if test="model.startDate != null and model.startDate != ''">
            and p.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.mobile != null and model.mobile != ''">
            and instr(m.mobile, #{model.mobile})
        </if>
        <if test="model.gradeId != null">
            and m.grade_id = #{model.gradeId}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and p.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.gradeIds != null and model.gradeIds.size() > 0">
            and g.id IN
            <foreach item="gradeId" collection="model.gradeIds" open="(" separator="," close=")">
                #{gradeId}
            </foreach>
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(m.name, #{model.keyword}) or instr(m.mobile, #{model.keyword}) or instr(m.code, #{model.keyword}))
        </if>
        GROUP BY p.member_id
        ORDER BY SUM(IFNULL(p.payment, 0)) + SUM(IFNULL(p.gift_amount, 0)) DESC
    </select>

    <select id="memberRechargeSum"  resultType="java.util.Map">
        select
        count(p.id)                                                        AS 'num',
        SUM(IFNULL(p.payment, 0)) + SUM(IFNULL(p.gift_amount, 0))          AS 'totalPayment',
        SUM(IFNULL(p.payment, 0))                                          AS 'payment',
        SUM(IFNULL(p.gift_amount, 0))                                      AS 'giftAmount'
        from pos_cash p
        LEFT JOIN member_info m ON m.id = p.member_id
        LEFT JOIN member_grade g ON g.id = m.grade_id
        where p.bill_state = '2'
        AND p.bill_type IN (0, 3, 4)
        AND p.type_ = 3
        AND p.delete_flag = 0
        AND p.member_id IS NOT NULL
        <if test="model.startDate != null and model.startDate != ''">
            and p.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.mobile != null and model.mobile != ''">
            and instr(m.mobile, #{model.mobile})
        </if>
        <if test="model.gradeId != null">
            and m.grade_id = #{model.gradeId}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and p.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.gradeIds != null and model.gradeIds.size() > 0">
            and g.id IN
            <foreach item="gradeId" collection="model.gradeIds" open="(" separator="," close=")">
                #{gradeId}
            </foreach>
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(m.name, #{model.keyword}) or instr(m.mobile, #{model.keyword}) or instr(m.code, #{model.keyword}))
        </if>
    </select>

    <select id="memberRechargeExport"  resultType="java.util.Map">
        select ifnull(m.`name`, '-')                                                           AS 'name',
                m.code                                                             AS 'code',
        (CASE m.sex WHEN '1' THEN '男' WHEN '2' THEN '女' ELSE '未知' END) AS 'sex',
        if(#{model.isEncryptMobile} = true,CONCAT(SUBSTRING(m.`mobile`, 1, 3), '****',
        SUBSTRING(m.`mobile`, 8)), m.mobile)                        AS 'mobile',
        ifnull(g.`name`, '-')                                              AS 'gradeName',
        count(p.id)                                                        AS 'num',
        SUM(IFNULL(p.payment, 0)) + SUM(IFNULL(p.gift_amount, 0))          AS 'totalPayment',
        SUM(IFNULL(p.payment, 0))                                          AS 'payment',
        SUM(IFNULL(p.gift_amount, 0))                                      AS 'giftAmount',
        IFNULL(bo.name, '-') AS org
        from pos_cash p
        LEFT JOIN base_org bo on p.org_id = bo.id
        LEFT JOIN member_info m ON m.id = p.member_id
        LEFT JOIN member_grade g ON g.id = m.grade_id
        where p.bill_state = '2'
        AND p.bill_type IN (0, 3, 4)
        AND p.type_ = 3
        AND p.delete_flag = 0
        AND p.member_id IS NOT NULL
        <if test="model.startDate != null and model.startDate != ''">
            and p.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.mobile != null and model.mobile != ''">
            and instr(m.mobile, #{model.mobile})
        </if>
        <if test="model.gradeId != null">
            and m.grade_id = #{model.gradeId}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and p.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.gradeIds != null and model.gradeIds.size() > 0">
            and g.id IN
            <foreach item="gradeId" collection="model.gradeIds" open="(" separator="," close=")">
                #{gradeId}
            </foreach>
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(m.name, #{model.keyword}) or instr(m.mobile, #{model.keyword}) or instr(m.code, #{model.keyword}))
        </if>
        GROUP BY p.member_id
        ORDER BY SUM(IFNULL(p.payment, 0)) + SUM(IFNULL(p.gift_amount, 0)) DESC
    </select>

    <select id="inventoryFlow"  resultType="java.util.Map">
        select p.`created_time`                                         AS 'createdTime',
                cate.`name`                                              AS 'categoryName',
                IF(pro.delete_flag = 1, concat(pro.`NAME`, '(已删除)'), pro.`NAME`)     AS 'name',
                CASE o.type_
                    WHEN 0 THEN '采购入库'
                    WHEN 1 THEN '盘盈入库'
                    WHEN 2 THEN '盘亏出库'
                    WHEN 3 THEN '销售出库'
                    WHEN 4 THEN '其他出库'
                    WHEN 5 THEN '退款入库'
                    WHEN 6 THEN '商品退货'
                    WHEN 7 THEN '采购退货'
                    WHEN 8 THEN '积分兑换'
                    WHEN 9 THEN '调库入库'
                    WHEN 10 THEN '调库出库'
                    WHEN 11 THEN '其他入库'
                    WHEN 12 THEN '采购入库红冲单'
                    WHEN 13 THEN '采购退货红冲单'
                    ELSE '无' END                                        AS 'typeName',
                CASE
                WHEN p.num_type = '1' and p.num > 0  THEN CONCAT('+', p.num)
                WHEN p.num_type = '2' and p.num > 0 THEN CONCAT('-', p.num)
                ELSE CONCAT('', p.num) END                           AS 'num',
                p.residue_num                                            AS 'residueNum',
                p.desc_                                                  AS 'desc',
                CASE o.bill_state WHEN 0 THEN '未结账' ELSE '已结账' END AS 'billState',
                e.real_name                                              AS 'employeeName',
                o.code                                                   AS 'code',
                IFNULL(bo.name, '-') AS org,
                IFNULL(bw.name, '-') AS warehouseName
        from base_outin_product p
                 LEFT JOIN base_warehouse bw on p.warehouse_id = bw.id
                 INNER JOIN base_outin o ON o.id = p.outin_id
                 LEFT JOIN base_org bo on o.org_id = bo.id
                 LEFT JOIN pos_cash_product pcp on pcp.id = p.cash_product_id and pcp.delete_flag = 0
                 LEFT JOIN pos_cash pc ON pc.id = pcp.cash_id and pc.delete_flag = 0
                 LEFT JOIN base_employee e ON e.id = p.employee_id
                 LEFT JOIN base_product pro ON pro.id = p.product_id
                 LEFT JOIN base_product_category cate ON cate.id = pro.category_id
        where p.delete_flag = 0
          AND o.delete_flag = 0
          AND o.state in (1,4)
        <if test="model.startDate != null and model.startDate != ''">
            and p.created_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.created_time &lt;= #{model.endDate}
        </if>
        <if test="model.createdTime_st != null and model.createdTime_st != ''">
            and p.created_time >= #{model.createdTime_st}
        </if>
        <if test="model.createdTime_ed != null and model.createdTime_ed != ''">
            and p.created_time &lt;= #{model.createdTime_ed}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and o.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.warehouseIds != null and model.warehouseIds.size() > 0">
            and p.warehouse_id IN
            <foreach item="item" collection="model.warehouseIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.categoryIds != null and model.categoryIds.size() > 0">
            and pro.category_id IN
            <foreach item="categoryId" collection="model.categoryIds" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
        <if test="model.type != null">
            and o.type_ = #{model.type}
        </if>
        <if test="model.name != null and model.name != ''">
            and (instr(p.name, #{model.name}) or instr(pc.code, #{model.name}))
        </if>
        <if test="model.createdEmp != null">
            and p.created_by = #{model.createdEmp}
        </if>
        <if test="model.warehouseId != null">
            and p.warehouse_id = #{model.warehouseId}
        </if>
        <if test="model.categoryId != null">
            and cate.id = #{model.categoryId}
        </if>
        ORDER BY p.created_time DESC, p.id desc
    </select>

    <select id="inventoryFlowSum"  resultType="java.util.Map">
        select
        sum(CASE p.num_type
        WHEN 1 THEN p.num
        WHEN 2 THEN -p.num
        ELSE p.num END) AS 'num'
        from base_outin_product p
        INNER JOIN base_outin o ON o.id = p.outin_id
        LEFT JOIN pos_cash_product pcp on pcp.id = p.cash_product_id and pcp.delete_flag = 0
        LEFT JOIN pos_cash pc ON pc.id = pcp.cash_id and pc.delete_flag = 0
        LEFT JOIN base_employee e ON e.id = p.employee_id
        LEFT JOIN base_product pro ON pro.id = p.product_id
        LEFT JOIN base_product_category cate ON cate.id = pro.category_id
        where p.delete_flag = 0
        AND o.delete_flag = 0
        AND o.state in (1,4)
        <if test="model.startDate != null and model.startDate != ''">
            and p.created_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.created_time &lt;= #{model.endDate}
        </if>
        <if test="model.createdTime_st != null and model.createdTime_st != ''">
            and p.created_time >= #{model.createdTime_st}
        </if>
        <if test="model.createdTime_ed != null and model.createdTime_ed != ''">
            and p.created_time &lt;= #{model.createdTime_ed}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and o.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.warehouseIds != null and model.warehouseIds.size() > 0">
            and p.warehouse_id IN
            <foreach item="item" collection="model.warehouseIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.categoryIds != null and model.categoryIds.size() > 0">
            and pro.category_id IN
            <foreach item="categoryId" collection="model.categoryIds" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
        <if test="model.type != null">
            and o.type_ = #{model.type}
        </if>
        <if test="model.name != null and model.name != ''">
            and (instr(p.name, #{model.name}) or instr(pc.code, #{model.name}))
        </if>
        <if test="model.createdEmp != null">
            and p.created_by = #{model.createdEmp}
        </if>
        <if test="model.warehouseId != null">
            and p.warehouse_id = #{model.warehouseId}
        </if>
        <if test="model.categoryId != null">
            and cate.id = #{model.categoryId}
        </if>
    </select>

    <select id="inventoryFlowExport"  resultType="java.util.Map">
        select p.`created_time`                                         AS 'createdTime',
        IFNULL(cate.`name`, '-')                                              AS 'categoryName',
        IFNULL(IF(pro.delete_flag = 1, concat(pro.`NAME`, '(已删除)'), pro.`NAME`), '-')      AS 'name',
        CASE o.type_
        WHEN 0 THEN '采购入库'
        WHEN 1 THEN '盘盈入库'
        WHEN 2 THEN '盘亏出库'
        WHEN 3 THEN '销售出库'
        WHEN 4 THEN '其他出库'
        WHEN 5 THEN '退款入库'
        WHEN 6 THEN '商品退货'
        WHEN 7 THEN '采购退货'
        WHEN 8 THEN '积分兑换'
        WHEN 9 THEN '调库入库'
        WHEN 10 THEN '调库出库'
        WHEN 11 THEN '其他入库'
        WHEN 12 THEN '采购入库红冲单'
        WHEN 13 THEN '采购退货红冲单'
        ELSE '无' END                                        AS 'typeName',
        CASE
        WHEN p.num_type = '1' and p.num > 0  THEN CONCAT('+', p.num)
        WHEN p.num_type = '2' and p.num > 0 THEN CONCAT('-', p.num)
        ELSE CONCAT('', p.num) END                          AS 'num',
        ifnull(p.residue_num, 0)                                            AS 'residueNum',
        ifnull(p.desc_, '-')                                                  AS 'desc',
        CASE o.bill_state WHEN 0 THEN '未结账' ELSE '已结账' END AS 'billState',
        ifnull(e.real_name, '-')                                              AS 'employeeName',
        ifnull(o.code, '-')                                                   AS 'code',
        IFNULL(bo.name, '-') AS org,
        IFNULL(bw.name, '-') AS warehouseName
        from base_outin_product p
        INNER JOIN base_outin o ON o.id = p.outin_id
        LEFT JOIN base_warehouse bw on p.warehouse_id = bw.id
        LEFT JOIN base_org bo on o.org_id = bo.id
        LEFT JOIN pos_cash_product pcp on pcp.id = p.cash_product_id and pcp.delete_flag = 0
        LEFT JOIN pos_cash pc ON pc.id = pcp.cash_id and pc.delete_flag = 0
        LEFT JOIN base_employee e ON e.id = p.employee_id
        LEFT JOIN base_product pro ON pro.id = p.product_id
        LEFT JOIN base_product_category cate ON cate.id = pro.category_id
        where p.delete_flag = 0
        AND o.delete_flag = 0
        AND o.state in (1,4)
        <if test="model.startDate != null and model.startDate != ''">
            and p.created_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.created_time &lt;= #{model.endDate}
        </if>
        <if test="model.createdTime_st != null and model.createdTime_st != ''">
            and p.created_time >= #{model.createdTime_st}
        </if>
        <if test="model.createdTime_ed != null and model.createdTime_ed != ''">
            and p.created_time &lt;= #{model.createdTime_ed}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and o.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.warehouseIds != null and model.warehouseIds.size() > 0">
            and p.warehouse_id IN
            <foreach item="item" collection="model.warehouseIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.categoryIds != null and model.categoryIds.size() > 0">
            and pro.category_id IN
            <foreach item="categoryId" collection="model.categoryIds" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
        <if test="model.type != null">
            and o.type_ = #{model.type}
        </if>
        <if test="model.name != null and model.name != ''">
            and (instr(p.name, #{model.name}) or instr(pc.code, #{model.name}))
        </if>
        <if test="model.createdEmp != null">
            and p.created_by = #{model.createdEmp}
        </if>
        <if test="model.warehouseId != null">
            and p.warehouse_id = #{model.warehouseId}
        </if>
        <if test="model.categoryId != null">
            and cate.id = #{model.categoryId}
        </if>
        ORDER BY p.created_time DESC, p.id desc
    </select>

    <select id="productOutboundDetails" resultType="top.kx.kxss.wxapp.vo.result.statistics.ProductOutboundDetailsResultVO">
        select
            pro.id      as id,
            pro.created_time                 AS 'createdTime',
        p.complete_time                  AS 'completeTime',
        o.type_         AS 'type',
            pro.product_id    AS 'productId',
        pro.num -ifnull(pro.refund_num, 0)                        AS 'num',
        pro.price                        AS 'price',
        pro.orgin_price      AS originPrice,
        ROUND(pro.discount_amount + pro.assessed_amount, 2) AS discountAmount,
        ROUND((pro.orgin_price - pro.discount_amount - pro.assessed_amount
            - ifNULL(pro.refund_amount,0)), 2) AS 'totalPrice',
        pro.cost_price                        AS 'costPrice',
        ((pro.num -ifnull(pro.refund_num, 0)) * IFNULL(pro.cost_price, 0)) AS 'totalCostPrice',
        IFNULL(pro.profit_price, 0)      AS 'profitPrice',
        GROUP_CONCAT(c.employee_id)                                         AS 'beneficiaryEmpIdStr',
        p.created_emp         AS 'createdEmpId',
        p.code                     AS 'code',
        pro.created_org_id           AS 'orgId',
        pro.warehouse_id AS warehouseId,
        pro.remarks AS remarks,
        IFNULL(p.table_name, '-') AS tableName
        from pos_cash_product pro
        INNER JOIN pos_cash p ON p.id = pro.cash_id
        LEFT JOIN base_outin o ON o.`code` = p.`code` and o.delete_flag = 0 AND o.state = 1
        LEFT JOIN pos_cash_commenter c
            ON c.source_id = pro.id AND c.cash_id = pro.cash_id AND c.delete_flag = 0 AND c.type_ = '2'
            ${ew.customSqlSegment}
    </select>

    <select id="productOutboundDetailsSum" resultType="top.kx.kxss.wxapp.vo.result.statistics.ProductOutboundDetailsResultVO">
        select
        sum(pro.num) -sum(ifnull(pro.refund_num, 0))                        AS 'num',
        sum(pro.price)                        AS 'price',
        sum(pro.orgin_price) AS originPrice,
        ROUND(sum(pro.discount_amount + pro.assessed_amount), 2) AS discountAmount,
        ROUND(sum(pro.orgin_price - pro.discount_amount - pro.assessed_amount
        - ifNULL(pro.refund_amount,0)), 2) AS 'totalPrice',
        sum(pro.cost_price)                        AS 'costPrice',
        sum((pro.num -ifnull(pro.refund_num, 0)) * IFNULL(pro.cost_price, 0)) AS 'totalCostPrice',
        sum(pro.profit_price)                 AS 'profitPrice'
        from pos_cash_product pro
        INNER JOIN pos_cash p ON p.id = pro.cash_id
        LEFT JOIN base_outin o ON o.`code` = p.`code`  and o.delete_flag = 0 AND o.state = 1 and o.type_ != 5
        LEFT JOIN pos_cash_commenter c
                ON c.source_id = pro.id AND c.cash_id = pro.cash_id AND c.delete_flag = 0 AND c.type_ = '2'
            ${ew.customSqlSegment}
    </select>

    <select id="productOutboundDetailsExport" resultType="top.kx.kxss.wxapp.vo.result.statistics.ProductOutboundDetailsResultVO">
        select
            pro.id      as id,
            pro.created_time                 AS 'createdTime',
            p.complete_time                  AS 'completeTime',
            o.type_         AS 'type',
            pro.product_id    AS 'productId',
            pro.num -ifnull(pro.refund_num, 0)                        AS 'num',
            pro.price                        AS 'price',
            pro.orgin_price      AS originPrice,
            ROUND(pro.discount_amount + pro.assessed_amount, 2) AS discountAmount,
            ROUND((pro.orgin_price - pro.discount_amount - pro.assessed_amount
                - ifNULL(pro.refund_amount,0)), 2) AS 'totalPrice',
            pro.cost_price                        AS 'costPrice',
            ((pro.num -ifnull(pro.refund_num, 0)) * IFNULL(pro.cost_price, 0)) AS 'totalCostPrice',
            IFNULL(pro.profit_price, 0)      AS 'profitPrice',
            GROUP_CONCAT(c.employee_id)                                         AS 'beneficiaryEmpIdStr',
            p.created_emp         AS 'createdEmpId',
            p.code                     AS 'code',
            pro.created_org_id           AS 'orgId',
            pro.warehouse_id AS warehouseId,
            pro.remarks AS remarks,
            IFNULL(p.table_name, '-') AS tableName
        from pos_cash_product pro
        INNER JOIN pos_cash p ON p.id = pro.cash_id
        LEFT JOIN base_outin o ON o.`code` = p.`code`  and o.delete_flag = 0 AND o.state = 1 and o.type_ != 5
        LEFT JOIN pos_cash_commenter c
                ON c.source_id = pro.id AND c.cash_id = pro.cash_id AND c.delete_flag = 0 AND c.type_ = '2'
            ${ew.customSqlSegment}
    </select>

    <select id="productInventoryDetails" resultType="java.util.Map">
        select distinct pro.created_time AS 'createdTime',
        (CASE o.type_
        WHEN 0 THEN '采购入库'
        WHEN 1 THEN '盘盈入库'
        WHEN 2 THEN '盘亏出库'
        WHEN 3 THEN '销售出库'
        WHEN 4 THEN '其他出库'
        WHEN 5 THEN '退款入库'
        WHEN 6 THEN '商品退货'
        WHEN 7 THEN '采购退货'
        WHEN 8 THEN '积分兑换'
        WHEN 9 THEN '调库入库'
        WHEN 10 THEN '调库出库'
        WHEN 11 THEN '其他入库'
        WHEN 12 THEN '采购入库红冲单'
        WHEN 13 THEN '采购退货红冲单'
        ELSE '无' END) AS 'type',
        cate.`name` AS 'categoryName',
        IF(p.delete_flag = 1, concat(p.`NAME`, '(已删除)'), p.`NAME`) AS 'name',
        dict.`NAME` AS 'measuringUnit',
        IF(num_type = '1', pro.num, - pro.num) AS 'num',
        pro.cost_price AS 'costPrice',
        IF(num_type = '1', (pro.num * IFNULL(pro.cost_price, 0)), - (pro.num * IFNULL(pro.cost_price, 0))) AS 'totalCostPrice',
        pro.remarks AS 'desc',
        e.real_name AS 'employeeName',
        o.code  as 'code',
        IFNULL(bo.name, '-') AS org,
        IFNULL(bw.name, '-') AS warehouseName
        from base_outin_product pro
        INNER JOIN base_outin o ON o.id = pro.outin_id
        LEFT JOIN base_warehouse bw on pro.warehouse_id = bw.id
        LEFT JOIN base_org bo on o.created_org_id = bo.id
        LEFT JOIN base_employee e ON e.id = o.employee_id
        LEFT JOIN base_product p ON p.id = pro.product_id
        LEFT JOIN base_product_category cate ON cate.id = p.category_id
        LEFT JOIN base_dict dict ON dict.key_ = p.measuring_unit AND dict.parent_key = 'PRODUCT_UNIT' and dict.created_org_id = cate.created_org_id
        where o.bill_state = 1
        AND pro.delete_flag = 0
        AND o.delete_flag = 0
        AND o.state = 1
        AND o.type_ != '3'
        AND o.type_ in ('0', '1', '5', '7')
        <if test="model.startDate != null and model.startDate != ''">
            and pro.created_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pro.created_time &lt;= #{model.endDate}
        </if>
        <if test="model.type != null">
            and o.type_ = #{model.type}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pro.created_org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.warehouseIds != null and model.warehouseIds.size() > 0">
            and pro.warehouse_id IN
            <foreach item="item" collection="model.warehouseIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.categoryIds != null and model.categoryIds.size() > 0">
            and p.category_id IN
            <foreach item="categoryId" collection="model.categoryIds" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
        <if test="model.name != null and model.name != ''">
            and (instr(p.name, #{model.name}) or instr(o.code, #{model.name}))
        </if>
        <if test="model.categoryId != null">
            and cate.id = #{model.categoryId}
        </if>
        <if test="model.createdEmp != null">
            and o.employee_id = #{model.createdEmp}
        </if>
        <if test="model.warehouseId != null">
            and pro.warehouse_id = #{model.warehouseId}
        </if>
        ORDER BY pro.created_time DESC, pro.id ASC
    </select>

    <select id="productInventoryDetailsSum" resultType="java.util.Map">
        select
        sum(IF(num_type = '1', pro.num, - pro.num)) AS 'num',
        sum(pro.cost_price) AS 'costPrice',
        sum(IF(num_type = '1', (pro.num * IFNULL(pro.cost_price, 0)), - (pro.num * IFNULL(pro.cost_price, 0)))) AS 'totalCostPrice'
        from base_outin_product pro
        INNER JOIN base_outin o ON o.id = pro.outin_id
        LEFT JOIN base_employee e ON e.id = pro.employee_id
        LEFT JOIN base_product p ON p.id = pro.product_id
        LEFT JOIN base_product_category cate ON cate.id = p.category_id
        LEFT JOIN base_dict dict ON dict.key_ = p.measuring_unit AND dict.parent_key = 'PRODUCT_UNIT'
        where o.bill_state = 1
        AND pro.delete_flag = 0
        AND o.delete_flag = 0
        AND o.type_ != '3'
        AND o.state = 1
        AND o.type_ in ('0', '1', '5', '7')
        <if test="model.startDate != null and model.startDate != ''">
            and pro.created_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pro.created_time &lt;= #{model.endDate}
        </if>
        <if test="model.type != null">
            and o.type_ = #{model.type}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pro.created_org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.warehouseIds != null and model.warehouseIds.size() > 0">
            and pro.warehouse_id IN
            <foreach item="item" collection="model.warehouseIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.categoryIds != null and model.categoryIds.size() > 0">
            and p.category_id IN
            <foreach item="categoryId" collection="model.categoryIds" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
        <if test="model.name != null and model.name != ''">
            and (instr(p.name, #{model.name}) or instr(o.code, #{model.name}))
        </if>
        <if test="model.categoryId != null">
            and cate.id = #{model.categoryId}
        </if>
        <if test="model.createdEmp != null">
            and o.employee_id = #{model.createdEmp}
        </if>
        <if test="model.warehouseId != null">
            and pro.warehouse_id = #{model.warehouseId}
        </if>
    </select>

    <select id="productInventoryDetailsExport" resultType="java.util.Map">
        select distinct pro.created_time AS 'createdTime',
        (CASE o.type_
        WHEN 0 THEN '采购入库'
        WHEN 1 THEN '盘盈入库'
        WHEN 2 THEN '盘亏出库'
        WHEN 3 THEN '销售出库'
        WHEN 4 THEN '其他出库'
        WHEN 5 THEN '退款入库'
        WHEN 6 THEN '商品退货'
        WHEN 7 THEN '采购退货'
        WHEN 8 THEN '积分兑换'
        WHEN 9 THEN '调库入库'
        WHEN 10 THEN '调库出库'
        WHEN 11 THEN '其他入库'
        WHEN 12 THEN '采购入库红冲单'
        WHEN 13 THEN '采购退货红冲单'
        ELSE '无' END) AS 'type',
        ifnull(cate.`name`, '-') AS 'categoryName',
        ifnull(IF(p.delete_flag = 1, concat(p.`NAME`, '(已删除)'), p.`NAME`), '-') AS 'name',
        ifnull(dict.`NAME`, '-') AS 'measuringUnit',
        IF(num_type = '1', pro.num, - pro.num) AS 'num',
        ifnull(pro.cost_price, 0.00) AS 'costPrice',
        IF(num_type = '1', (pro.num * IFNULL(pro.cost_price, 0)), - (pro.num * IFNULL(pro.cost_price, 0))) AS
        'totalCostPrice',
        ifnull(pro.remarks, '-') AS 'desc',
        ifnull(e.`real_name`, '-') AS 'employeeName',
        o.code  as 'code',
        IFNULL(bo.name, '-') AS org,
        IFNULL(bw.name, '-') AS warehouseName
        from base_outin_product pro
        INNER JOIN base_outin o ON o.id = pro.outin_id
        LEFT JOIN base_warehouse bw on pro.warehouse_id = bw.id
        LEFT JOIN base_org bo on o.created_org_id = bo.id
        LEFT JOIN base_employee e ON e.id = o.employee_id
        LEFT JOIN base_product p ON p.id = pro.product_id
        LEFT JOIN base_product_category cate ON cate.id = p.category_id
        LEFT JOIN base_dict dict ON dict.key_ = p.measuring_unit AND dict.parent_key = 'PRODUCT_UNIT' and dict.created_org_id = cate.created_org_id
        where o.bill_state = 1
        AND pro.delete_flag = 0
        AND o.delete_flag = 0
        AND o.type_ != '3'
        AND o.state = 1
        AND o.type_ in ('0', '1', '5', '7')
        <if test="model.startDate != null and model.startDate != ''">
            and pro.created_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pro.created_time &lt;= #{model.endDate}
        </if>
        <if test="model.type != null">
            and o.type_ = #{model.type}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pro.created_org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.warehouseIds != null and model.warehouseIds.size() > 0">
            and pro.warehouse_id IN
            <foreach item="item" collection="model.warehouseIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.categoryIds != null and model.categoryIds.size() > 0">
            and p.category_id IN
            <foreach item="categoryId" collection="model.categoryIds" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
        <if test="model.name != null and model.name != ''">
            and (instr(p.name, #{model.name}) or instr(o.code, #{model.name}))
        </if>
        <if test="model.categoryId != null">
            and cate.id = #{model.categoryId}
        </if>
        <if test="model.createdEmp != null">
            and o.employee_id = #{model.createdEmp}
        </if>
        <if test="model.warehouseId != null">
            and pro.warehouse_id = #{model.warehouseId}
        </if>
        ORDER BY pro.created_time DESC, pro.id ASC
    </select>


    <select id="realTimeInventoryOfGoods" resultType="java.util.Map">
        select s.product_id                                                      as 'productId',
               cate.`name`                                                            AS 'category',
               p.`NAME`                                                               AS "name",
               dict.`NAME`                                                            AS "measuringUnit",
               sum(s.num)                                                               AS "totalNum",
               sum(s.lock_num)                                                             AS 'lockNum',
               sum(IFNULL(s.num, 0) - IFNULL(s.lock_num, 0))                               AS 'num',
               sum(IFNULL(s.out_num, 0))                               AS 'outNum',
               IFNULL(p.retail_price, 0)                                              AS 'retailPrice',
               sum(IFNULL(p.retail_price, 0) * (IFNULL(s.num, 0) - IFNULL(s.lock_num, 0))) AS 'totalRetailPrice',
               FORMAT(IFNULL(s.cost_price, IFNULL(p.buying_price, 0)), 2)             AS 'buyingPrice',
                round(sum(IFNULL(s.cost_price, IFNULL(p.buying_price, 0)) *
                (IFNULL(s.num, 0) - IFNULL(s.lock_num, 0))), 2)                     AS 'totalBuyingPrice',
                IFNULL(bo.name, '-') AS org
        from base_product_stock s
                 LEFT JOIN base_product p ON p.id = s.product_id
                 LEFT JOIN base_product_category cate ON cate.id = p.category_id
                 LEFT JOIN base_org bo on s.created_org_id = bo.id
                 LEFT JOIN base_dict dict ON dict.key_ = p.measuring_unit AND dict.parent_key = 'PRODUCT_UNIT'
                    and dict.created_org_id = cate.created_org_id
        where s.delete_flag = 0
          and p.delete_flag = 0
        <if test="model.name != null and model.name != ''">
            and instr(p.name, #{model.name})
        </if>
        <if test="model.categoryId != null">
            and cate.id = #{model.categoryId}
        </if>
        <if test="model.warehouseId != null">
            and s.warehouse_id = #{model.warehouseId}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and s.created_org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.warehouseIds != null and model.warehouseIds.size() > 0">
            and s.warehouse_id IN
            <foreach item="item" collection="model.warehouseIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.categoryIds != null and model.categoryIds.size() > 0">
            and p.category_id IN
            <foreach item="categoryId" collection="model.categoryIds" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
        GROUP BY s.product_id
        ORDER BY p.sort_value ASC
    </select>

    <select id="sellOutNum" resultType="top.kx.kxss.wxapp.vo.result.statistics.ProductStockTypeResultVO">
        select sum(pcp.num - ifnull(pcp.refund_num, 0)) as 'num',
               pcp.product_id as productId
        from pos_cash pc
                 left join pos_cash_product pcp on pc.id = pcp.cash_id
                left join base_product bp on pcp.product_id = bp.id
        where pc.delete_flag = 0
          and pcp.delete_flag = 0
          and pc.bill_state in ('2', '5')
          and pc.bill_type in ('0', '3', '4')
          and bp.delete_flag = 0
        <if test="model.name != null and model.name != ''">
            and instr(bp.name, #{model.name})
        </if>
        <if test="model.warehouseIds != null and model.warehouseIds.size() > 0">
            and pcp.warehouse_id IN
            <foreach item="item" collection="model.warehouseIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.productIds != null and model.productIds.size() > 0">
            and pcp.product_id IN
            <foreach item="item" collection="model.productIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        GROUP BY pcp.product_id
    </select>

    <select id="productStockTypeStatis" resultType="top.kx.kxss.wxapp.vo.result.statistics.ProductStockTypeResultVO">
        select pro.product_id as productId,
               o.type_ as type,
               if(o.type_ = '3', SUM(if(num_type = '2', num, - num)), SUM(num)) as num
        FROM base_outin_product pro
                 INNER JOIN base_outin o ON o.id = pro.outin_id
                LEFT JOIN base_product p ON p.id = pro.product_id
                LEFT JOIN base_product_category cate ON cate.id = p.category_id
        where pro.delete_flag = 0 and o.bill_state = 1
          AND o.delete_flag = 0  and p.delete_flag = 0
          AND o.state = 1
        <if test="model.name != null and model.name != ''">
            and instr(p.name, #{model.name})
        </if>
        <if test="model.categoryId != null">
            and cate.id = #{model.categoryId}
        </if>
        <if test="model.warehouseId != null">
            and pro.warehouse_id = #{model.warehouseId}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and o.created_org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.warehouseIds != null and model.warehouseIds.size() > 0">
            and pro.warehouse_id IN
            <foreach item="item" collection="model.warehouseIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.productIds != null and model.productIds.size() > 0">
            and pro.product_id IN
            <foreach item="item" collection="model.productIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by pro.product_id, o.type_
    </select>

    <select id="stockTypeStatis"  resultType="top.kx.kxss.wxapp.vo.result.statistics.ProductStockTypeResultVO">
        select
        o.type_ as type,
        if(o.type_ = '3', SUM(if(num_type = '2', num, - num)), SUM(num)) as num
        FROM base_outin_product pro
        INNER JOIN base_outin o ON o.id = pro.outin_id
        LEFT JOIN base_product p ON p.id = pro.product_id
        LEFT JOIN base_product_category cate ON cate.id = p.category_id
        where pro.delete_flag = 0 and o.bill_state = 1
        AND o.delete_flag = 0
        and p.delete_flag = 0
        AND o.state = 1
        <if test="model.name != null and model.name != ''">
            and instr(p.name, #{model.name})
        </if>
        <if test="model.categoryId != null">
            and cate.id = #{model.categoryId}
        </if>
        <if test="model.warehouseId != null">
            and pro.warehouse_id = #{model.warehouseId}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and o.created_org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.warehouseIds != null and model.warehouseIds.size() > 0">
            and pro.warehouse_id IN
            <foreach item="item" collection="model.warehouseIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.productIds != null and model.productIds.size() > 0">
            and pro.product_id IN
            <foreach item="item" collection="model.productIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        group by o.type_
    </select>

    <select id="realTimeInventoryOfGoodsSum" resultType="java.util.Map">
        select
        sum(s.lock_num)                                                             AS 'lockNum',
        sum(s.out_num)                                                             AS 'outNum',
        sum(IFNULL(s.num, 0) - IFNULL(s.lock_num, 0))                               AS 'num',
        ROUND(sum(IFNULL(p.retail_price, 0) * (IFNULL(s.num, 0) - IFNULL(s.lock_num, 0))), 2) AS 'totalRetailPrice',
        round(sum(IFNULL(s.cost_price, IFNULL(p.buying_price, 0)) *
        (IFNULL(s.num, 0) - IFNULL(s.lock_num, 0))), 2)                        AS 'totalBuyingPrice'
        from base_product_stock s
        LEFT JOIN base_product p ON p.id = s.product_id
        LEFT JOIN base_product_category cate ON cate.id = p.category_id
        LEFT JOIN base_dict dict ON dict.key_ = p.measuring_unit AND dict.parent_key = 'PRODUCT_UNIT'
                and dict.created_org_id = cate.created_org_id
        where s.delete_flag = 0
        and p.delete_flag = 0
        <if test="model.name != null and model.name != ''">
            and instr(p.name, #{model.name})
        </if>
        <if test="model.categoryId != null">
            and cate.id = #{model.categoryId}
        </if>
        <if test="model.warehouseId != null">
            and s.warehouse_id = #{model.warehouseId}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and s.created_org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.warehouseIds != null and model.warehouseIds.size() > 0">
            and s.warehouse_id IN
            <foreach item="item" collection="model.warehouseIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.categoryIds != null and model.categoryIds.size() > 0">
            and p.category_id IN
            <foreach item="categoryId" collection="model.categoryIds" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
    </select>

    <select id="realTimeInventoryOfGoodsExport" resultType="java.util.Map">
        select s.product_id                                                      as 'productId',
               ifnull(cate.`name`, '-')                                                            AS 'category',
        ifnull(p.`NAME`, '-')                                                               AS "name",
        ifnull(dict.`NAME`, '-')                                                            AS "measuringUnit",
        sum(ifnull(s.lock_num, 0))                                                             AS 'lockNum',
        sum(IFNULL(s.out_num, 0))                               AS 'outNum',
        sum(IFNULL(s.num, 0) - IFNULL(s.lock_num, 0))                               AS 'num',
        IFNULL(p.retail_price, 0)                                              AS 'retailPrice',
        sum(IFNULL(p.retail_price, 0) * (IFNULL(s.num, 0) - IFNULL(s.lock_num, 0))) AS 'totalRetailPrice',
        FORMAT(IFNULL(s.cost_price, IFNULL(p.buying_price, 0)), 2)             AS 'buyingPrice',
        round(sum(IFNULL(s.cost_price, IFNULL(p.buying_price, 0)) *
        (IFNULL(s.num, 0) - IFNULL(s.lock_num, 0))), 2)                                                              AS 'totalBuyingPrice',
        IFNULL(bo.name, '-') AS org
        from base_product_stock s
        LEFT JOIN base_product p ON p.id = s.product_id
        LEFT JOIN base_product_category cate ON cate.id = p.category_id
        LEFT JOIN base_org bo on s.created_org_id = bo.id
        LEFT JOIN base_dict dict ON dict.key_ = p.measuring_unit AND dict.parent_key = 'PRODUCT_UNIT'
                and dict.created_org_id = cate.created_org_id
        where s.delete_flag = 0
        and p.delete_flag = 0
        <if test="model.name != null and model.name != ''">
            and instr(p.name, #{model.name})
        </if>
        <if test="model.categoryId != null">
            and cate.id = #{model.categoryId}
        </if>
        <if test="model.warehouseId != null">
            and s.warehouse_id = #{model.warehouseId}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and s.created_org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.warehouseIds != null and model.warehouseIds.size() > 0">
            and s.warehouse_id IN
            <foreach item="item" collection="model.warehouseIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.categoryIds != null and model.categoryIds.size() > 0">
            and p.category_id IN
            <foreach item="categoryId" collection="model.categoryIds" open="(" separator="," close=")">
                #{categoryId}
            </foreach>
        </if>
        GROUP BY s.product_id
        ORDER BY p.sort_value ASC
    </select>

    <select id="posCashDiscountTypeStatistics" resultType="java.util.Map">
        select ifnull(pcdd.step_desc, '-')              as discountName,
        ifnull(count(pc.id), 0)            as num,
        ifnull(sum(discount_amount), 0.00) as discountAmount,
        IFNULL(bo.name, '-') AS org
        from pos_cash_discount_detail pcdd
        LEFT JOIN pos_cash pc ON pcdd.pos_cash_id = pc.id
        LEFT JOIN base_org bo on pcdd.created_org_id = bo.id
        <where>
            and pc.bill_state in ('2', '5', '6')
            and pc.delete_flag = 0
            and pcdd.discount_type != '0'
            and pc.bill_type in ('0', '3', '4')
            <if test="model.startDate != null and model.startDate != ''">
                and pc.complete_time >= #{model.startDate}
            </if>
            <if test="model.endDate != null and model.endDate != ''">
                and pc.complete_time &lt;= #{model.endDate}
            </if>
            <if test="model.orgIdList != null and model.orgIdList.size() > 0">
                and pcdd.created_org_id IN
                <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
            </if>
            <if test="model.type != null and model.type != ''">
                and pc.type = #{model.type}
            </if>
            <if test="model.keyword != null and model.keyword != ''">
                and instr(pc.code, #{model.keyword})
            </if>
        </where>
        group by pcdd.step_desc
    </select>

    <select id="posCashDiscountTypeStatisticsSum" resultType="java.util.Map">
        select
        ifnull(count(pc.id), 0)            as num,
        ifnull(sum(discount_amount), 0.00) as discountAmount
        from pos_cash_discount_detail pcdd
        LEFT JOIN pos_cash pc ON pcdd.pos_cash_id = pc.id
        <where>
            and pc.bill_state in ('2', '5', '6')
            and pc.delete_flag = 0
            and pcdd.discount_type != '0'
            and pc.bill_type in ('0', '3', '4')
            <if test="model.startDate != null and model.startDate != ''">
                and pc.complete_time >= #{model.startDate}
            </if>
            <if test="model.endDate != null and model.endDate != ''">
                and pc.complete_time &lt;= #{model.endDate}
            </if>
            <if test="model.orgIdList != null and model.orgIdList.size() > 0">
                and pcdd.created_org_id IN
                <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
            </if>
            <if test="model.type != null and model.type != ''">
                and pc.type = #{model.type}
            </if>
            <if test="model.keyword != null and model.keyword != ''">
                and instr(pc.code, #{model.keyword})
            </if>
        </where>
    </select>


    <select id="posCashDiscountTypeStatisticsExport" resultType="java.util.Map">
        select ifnull(pcdd.step_desc, '-')              as discountName,
        ifnull(count(pc.id), 0)            as num,
        ifnull(sum(discount_amount), 0.00) as discountAmount,
        IFNULL(bo.name, '-') AS org
        from pos_cash_discount_detail pcdd
        LEFT JOIN pos_cash pc ON pcdd.pos_cash_id = pc.id
        LEFT JOIN base_org bo on pcdd.created_org_id = bo.id
        <where>
            and pc.bill_state in ('2', '5', '6')
            and pc.delete_flag = 0
            and pcdd.discount_type != '0'
            and pc.bill_type in ('0', '3', '4')
            <if test="model.startDate != null and model.startDate != ''">
                and pc.complete_time >= #{model.startDate}
            </if>
            <if test="model.endDate != null and model.endDate != ''">
                and pc.complete_time &lt;= #{model.endDate}
            </if>
            <if test="model.orgIdList != null and model.orgIdList.size() > 0">
                and pcdd.created_org_id IN
                <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
            </if>
            <if test="model.type != null and model.type != ''">
                and pc.type = #{model.type}
            </if>
            <if test="model.keyword != null and model.keyword != ''">
                and instr(pc.code, #{model.keyword})
            </if>
        </where>
        group by pcdd.step_desc
    </select>

    <select id="posCashDiscountTypeActivity" resultType="java.util.Map">
        select ifnull(replace(JSON_EXTRACT(pc.remarks, '$.discountRemarks'), '"', ''), '-') as discountName,
               ifnull(count(pc.id), 0)                                    as num,
               ifnull(sum(discount_amount), 0.00)                         as discountAmount,
               IFNULL(bo.name, '-')                                       AS org
        from pos_cash pc
                 LEFT JOIN base_org bo on pc.org_id = bo.id
        WHERE pc.bill_state in ('2', '5', '6')
          and pc.delete_flag = 0
          and pc.bill_type in ('0', '3', '4')
          and JSON_EXTRACT(pc.remarks, '$.discountRemarks') is not null and JSON_EXTRACT(pc.remarks, '$.discountRemarks') != ''
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.type != null and model.type != ''">
            and pc.type = #{model.type}
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and instr(pc.code, #{model.keyword})
        </if>
        group by discountName
    </select>

    <select id="posCashDiscountTypeActivitySum" resultType="java.util.Map">
        select
        ifnull(count(pc.id), 0)                                    as num,
        ifnull(sum(discount_amount), 0.00)                         as discountAmount
        from pos_cash pc
        WHERE pc.bill_state in ('2', '5', '6')
        and pc.delete_flag = 0
        and pc.bill_type in ('0', '3', '4')
        and JSON_EXTRACT(pc.remarks, '$.discountRemarks') is not null and JSON_EXTRACT(pc.remarks, '$.discountRemarks') != ''
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.type != null and model.type != ''">
            and pc.type = #{model.type}
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and instr(pc.code, #{model.keyword})
        </if>
    </select>

    <select id="posCashDiscountTypeActivityExport" resultType="java.util.Map">
        select ifnull(replace(JSON_EXTRACT(pc.remarks, '$.discountRemarks'), '"', ''), '-') as discountName,
                ifnull(count(pc.id), 0)                                    as num,
                ifnull(sum(discount_amount), 0.00)                         as discountAmount,
                IFNULL(bo.name, '-')                                       AS org
        from pos_cash pc
            LEFT JOIN base_org bo on pc.org_id = bo.id
        WHERE pc.bill_state in ('2', '5', '6')
                and pc.delete_flag = 0
                and pc.bill_type in ('0', '3', '4')
        and JSON_EXTRACT(pc.remarks, '$.discountRemarks') is not null and JSON_EXTRACT(pc.remarks, '$.discountRemarks') != ''
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.type != null and model.type != ''">
            and pc.type = #{model.type}
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and instr(pc.code, #{model.keyword})
        </if>
        group by discountName
    </select>

    <select id="posCashDiscountTypeDetails" resultType="java.util.Map">
        select pcdd.step_desc             as discountName,
                pc.code                    as code,
                pc.table_name              as tableName,
                pc.payment                 as payment,
                pcdd.price_change          as discountAmount,
                pcdd.pre_price             as previousPrice,
                pcdd.curr_price            as currPrice,
                pc.complete_time           as completeTime,
                group_concat(be.real_name) as createEmp,
                IFNULL(bo.name, '-') AS org
        from pos_cash_discount_detail pcdd
        left join pos_cash pc on pcdd.pos_cash_id = pc.id
        left join base_employee be on pc.created_emp = be.id
        LEFT JOIN base_org bo on pc.org_id = bo.id
        where pc.bill_state in ('2', '5', '6')
        and pc.delete_flag = 0
        and pc.bill_type in ('0', '3', '4')
        and pcdd.discount_type != '0'
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.type != null and model.type != ''">
            and pc.type = #{model.type}
        </if>
        <if test="model.createdEmp != null">
            and pc.created_by = #{model.createdEmp}
        </if>
        <if test="model.completeTime_st != null and model.completeTime_st != ''">
            and pc.complete_time >= #{model.completeTime_st}
        </if>
        <if test="model.completeTime_ed != null and model.completeTime_ed != ''">
            and pc.complete_time &lt;= #{model.completeTime_ed}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(pc.code, #{model.keyword}) or instr(pc.table_name, #{model.keyword}))
        </if>
        group by pcdd.id
        order by pc.created_time desc, pcdd.created_time asc
    </select>

    <select id="posCashDiscountTypeDetailsSum" resultType="java.util.Map">
        select
        ifnull(sum(pc.payment), 0.00)                 as payment,
        ifnull(sum(pcdd.price_change), 0.00)         as discountAmount
        from pos_cash_discount_detail pcdd
        left join pos_cash pc on pcdd.pos_cash_id = pc.id
        where pc.bill_state in ('2', '5', '6')
        and pc.delete_flag = 0
        and pc.bill_type in ('0', '3', '4')
        and pcdd.discount_type != '0'
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.type != null and model.type != ''">
            and pc.type = #{model.type}
        </if>
        <if test="model.createdEmp != null">
            and pc.created_by = #{model.createdEmp}
        </if>
        <if test="model.completeTime_st != null and model.completeTime_st != ''">
            and pc.complete_time >= #{model.completeTime_st}
        </if>
        <if test="model.completeTime_ed != null and model.completeTime_ed != ''">
            and pc.complete_time &lt;= #{model.completeTime_ed}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(pc.code, #{model.keyword}) or instr(pc.table_name, #{model.keyword}))
        </if>
    </select>

    <select id="posCashDiscountTypeDetailsExport" resultType="java.util.Map">
        select pcdd.step_desc             as discountName,
                pc.code                    as code,
                pc.table_name              as tableName,
                pc.payment                 as payment,
                pcdd.price_change          as discountAmount,
                pcdd.pre_price             as previousPrice,
                pcdd.curr_price            as currPrice,
                pc.complete_time           as completeTime,
                group_concat(be.real_name) as createEmp,
                IFNULL(bo.name, '-') AS org
        from pos_cash_discount_detail pcdd
        left join pos_cash pc on pcdd.pos_cash_id = pc.id
        left join base_employee be on pc.created_emp = be.id
        LEFT JOIN base_org bo on pc.org_id = bo.id
        where pc.bill_state in ('2', '5', '6')
                and pc.delete_flag = 0
                and pc.bill_type in ('0', '3', '4')
                and pcdd.discount_type != '0'
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.type != null and model.type != ''">
            and pc.type = #{model.type}
        </if>
        <if test="model.createdEmp != null">
            and pc.created_by = #{model.createdEmp}
        </if>
        <if test="model.completeTime_st != null and model.completeTime_st != ''">
            and pc.complete_time >= #{model.completeTime_st}
        </if>
        <if test="model.completeTime_ed != null and model.completeTime_ed != ''">
            and pc.complete_time &lt;= #{model.completeTime_ed}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(pc.code, #{model.keyword}) or instr(pc.table_name, #{model.keyword}))
        </if>
        group by pcdd.id
        order by pc.created_time desc, pcdd.created_time asc
    </select>


    <select id="posCashCommenter" resultType="java.util.Map">
        SELECT COALESCE(be.real_name, '-') as commenter,
                ROUND(SUM(pc.table_amount / COALESCE(cnt.commenter_count, 1)), 2)    AS tableAmount,
                ROUND(SUM(pc.product_amount / COALESCE(cnt.commenter_count, 1)), 2)  AS productAmount,
                ROUND(SUM(pc.service_amount / COALESCE(cnt.commenter_count, 1)), 2)  AS serviceAmount,
                ROUND(SUM(pc.thail_amount / COALESCE(cnt.commenter_count, 1)), 2)    AS thailAmount,
                ROUND(SUM(pc.amount / COALESCE(cnt.commenter_count, 1)), 2)          AS amount,
                ROUND(SUM(pc.discount_amount / COALESCE(cnt.commenter_count, 1)), 2) AS discountAmount,
                ROUND(SUM(pc.payment / COALESCE(cnt.commenter_count, 1)), 2)         AS payment,
                ROUND(SUM(pc.paid / COALESCE(cnt.commenter_count, 1)), 2)            AS paid,
                ROUND(SUM(pc.refund_amount / COALESCE(cnt.commenter_count, 1)), 2)   AS refundAmount,
               bo.short_name              as org
        FROM pos_cash pc
                LEFT JOIN (SELECT cash_id,
                                  COUNT(*) AS commenter_count
                            FROM pos_cash_commenter
                            WHERE type_ = '4002'
                                and delete_flag = 0
                            GROUP BY cash_id) cnt ON pc.id = cnt.cash_id
                 LEFT JOIN pos_cash_commenter pcc ON pc.id = pcc.cash_id AND pcc.type_ = '4002'
                 LEFT JOIN base_employee be ON pcc.employee_id = be.id
                 LEFT JOIN base_org bo ON pc.org_id = bo.id
        WHERE pc.delete_flag = 0
          AND pc.type_ !=  '3'
          AND (pcc.delete_flag = 0 OR pcc.delete_flag IS NULL)
          AND pc.bill_state IN ('2', '5', '6')
          AND pc.bill_type IN ('0', '3', '4')
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.type != null and model.type != ''">
            and pc.type = #{model.type}
        </if>
        <if test="model.orderSource != null and model.orderSource != ''">
            and pc.order_source = #{model.orderSource}
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and instr(be.real_name, #{model.keyword})
        </if>
        <if test="model.employeeId != null">
            and pcc.employee_id = #{model.employeeId}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        GROUP BY pcc.employee_id
        order by pcc.employee_id desc
    </select>


    <select id="posCashCommenterSum" resultType="java.util.Map">
        SELECT
            ROUND(SUM(pc.table_amount / COALESCE(cnt.commenter_count, 1)), 2)    AS tableAmount,
            ROUND(SUM(pc.product_amount / COALESCE(cnt.commenter_count, 1)), 2)  AS productAmount,
            ROUND(SUM(pc.service_amount / COALESCE(cnt.commenter_count, 1)), 2)  AS serviceAmount,
            ROUND(SUM(pc.thail_amount / COALESCE(cnt.commenter_count, 1)), 2)    AS thailAmount,
            ROUND(SUM(pc.amount / COALESCE(cnt.commenter_count, 1)), 2)          AS amount,
            ROUND(SUM(pc.discount_amount / COALESCE(cnt.commenter_count, 1)), 2) AS discountAmount,
            ROUND(SUM(pc.payment / COALESCE(cnt.commenter_count, 1)), 2)         AS payment,
            ROUND(SUM(pc.paid / COALESCE(cnt.commenter_count, 1)), 2)            AS paid,
            ROUND(SUM(pc.refund_amount / COALESCE(cnt.commenter_count, 1)), 2)   AS refundAmount
        FROM pos_cash pc
            LEFT JOIN (SELECT cash_id,
                              COUNT(*) AS commenter_count
                        FROM pos_cash_commenter
                        WHERE type_ = '4002'
                            and delete_flag = 0
                            GROUP BY cash_id) cnt ON pc.id = cnt.cash_id
            LEFT JOIN pos_cash_commenter pcc ON pc.id = pcc.cash_id AND pcc.type_ = '4002'
            LEFT JOIN base_employee be ON pcc.employee_id = be.id
            LEFT JOIN base_org bo ON pc.org_id = bo.id
        WHERE pc.delete_flag = 0
            AND pc.type_ !=  '3'
            AND (pcc.delete_flag = 0 OR pcc.delete_flag IS NULL)
            AND pc.bill_state IN ('2', '5', '6')
            AND pc.bill_type IN ('0', '3', '4')
            <if test="model.startDate != null and model.startDate != ''">
                and pc.complete_time >= #{model.startDate}
            </if>
            <if test="model.endDate != null and model.endDate != ''">
                and pc.complete_time &lt;= #{model.endDate}
            </if>
            <if test="model.type != null and model.type != ''">
                and pc.type = #{model.type}
            </if>
            <if test="model.orderSource != null and model.orderSource != ''">
                and pc.order_source = #{model.orderSource}
            </if>
            <if test="model.keyword != null and model.keyword != ''">
                and instr(be.real_name, #{model.keyword})
            </if>
            <if test="model.employeeId != null">
                and pcc.employee_id = #{model.employeeId}
            </if>
            <if test="model.orgIdList != null and model.orgIdList.size() > 0">
                and pc.org_id IN
                <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
            </if>
    </select>


    <select id="posCashCommenterExport" resultType="java.util.Map">
        SELECT COALESCE(be.real_name, '-') as commenter,
                ROUND(SUM(pc.table_amount / COALESCE(cnt.commenter_count, 1)), 2)    AS tableAmount,
                ROUND(SUM(pc.product_amount / COALESCE(cnt.commenter_count, 1)), 2)  AS productAmount,
                ROUND(SUM(pc.service_amount / COALESCE(cnt.commenter_count, 1)), 2)  AS serviceAmount,
                ROUND(SUM(pc.thail_amount / COALESCE(cnt.commenter_count, 1)), 2)    AS thailAmount,
                ROUND(SUM(pc.amount / COALESCE(cnt.commenter_count, 1)), 2)          AS amount,
                ROUND(SUM(pc.discount_amount / COALESCE(cnt.commenter_count, 1)), 2) AS discountAmount,
                ROUND(SUM(pc.payment / COALESCE(cnt.commenter_count, 1)), 2)         AS payment,
                ROUND(SUM(pc.paid / COALESCE(cnt.commenter_count, 1)), 2)            AS paid,
                ROUND(SUM(pc.refund_amount / COALESCE(cnt.commenter_count, 1)), 2)   AS refundAmount,
                bo.short_name              as org
        FROM pos_cash pc
            LEFT JOIN (SELECT cash_id,
                              COUNT(*) AS commenter_count
                        FROM pos_cash_commenter
                        WHERE type_ = '4002'
                            and delete_flag = 0
                        GROUP BY cash_id) cnt ON pc.id = cnt.cash_id
            LEFT JOIN pos_cash_commenter pcc ON pc.id = pcc.cash_id AND pcc.type_ = '4002'
            LEFT JOIN base_employee be ON pcc.employee_id = be.id
            LEFT JOIN base_org bo ON pc.org_id = bo.id
        WHERE pc.delete_flag = 0
            AND pc.type_ !=  '3'
            AND (pcc.delete_flag = 0 OR pcc.delete_flag IS NULL)
            AND pc.bill_state IN ('2', '5', '6')
            AND pc.bill_type IN ('0', '3', '4')
            <if test="model.startDate != null and model.startDate != ''">
                and pc.complete_time >= #{model.startDate}
            </if>
            <if test="model.endDate != null and model.endDate != ''">
                and pc.complete_time &lt;= #{model.endDate}
            </if>
            <if test="model.type != null and model.type != ''">
                and pc.type = #{model.type}
            </if>
            <if test="model.orderSource != null and model.orderSource != ''">
                and pc.order_source = #{model.orderSource}
            </if>
            <if test="model.keyword != null and model.keyword != ''">
                and instr(be.real_name, #{model.keyword})
            </if>
            <if test="model.employeeId != null">
                and pcc.employee_id = #{model.employeeId}
            </if>
            <if test="model.orgIdList != null and model.orgIdList.size() > 0">
                and pc.org_id IN
                <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                    #{orgId}
                </foreach>
            </if>
            GROUP BY pcc.employee_id
            order by pcc.employee_id desc
    </select>

    <select id="productStats" resultType="java.util.Map">
        select createdTime     AS createdTime,
        cateName    AS cateName,
        productName AS productName,
        sum(outNum) AS outNum,
        sum(procurementOutNum) AS procurementOutNum,
        sum(inNum)  AS inNum,
        sum(pdOut)  AS pdOut,
        sum(pdIn)   AS pdIn,
        org         AS org from (
        select
        <choose>
            <when test="model.type != null and model.type != '' and model.type == 1">
                DATE_FORMAT(p.created_time, '%Y-%m')
            </when>
            <when test="model.type != null and model.type != '' and model.type == 2">
                DATE_FORMAT(p.created_time, '%Y-%m-%d')
            </when>
            <otherwise>
                p.created_time
            </otherwise>
        </choose>
        as createdTime,
        cate.name as cateName,
        pro.name as productName,
        sum(IF(o.type_ = 3 or o.type_ = 4 or o.type_ = 8, if(p.num_type = '1', p.num, -p.num), 0)) as
        outNum,
        0                                                                       as procurementOutNum,
        0 as inNum,
        0 as pdOut,
        0 as pdIn,
        bo.name as org
        from base_outin_product p
        INNER JOIN base_outin o ON o.id = p.outin_id
        LEFT JOIN pos_cash pc ON pc.code = o.code and pc.delete_flag = 0
        LEFT JOIN base_product pro ON pro.id = p.product_id
        LEFT JOIN base_product_category cate ON cate.id = pro.category_id
        LEFT JOIN base_org bo on p.created_org_id = bo.id
        where o.bill_state = 1
        and p.delete_flag = 0
        AND o.delete_flag = 0
        <if test="model.keyword != null and model.keyword != ''">
            and instr(pro.name, #{model.keyword})
        </if>
        <if test="model.name != null and model.name != ''">
            and instr(pro.name, #{model.name})
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and p.created_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.created_time &lt;= #{model.endDate}
        </if>
        <if test="model.completeStartDate != null and model.completeStartDate != ''">
            and pc.complete_time >= #{model.completeStartDate}
        </if>
        <if test="model.completeEndDate != null and model.completeEndDate != ''">
            and pc.complete_time &lt;= #{model.completeEndDate}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and p.created_org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.warehouseIds != null and model.warehouseIds.size() > 0">
            and p.warehouse_id IN
            <foreach item="item" collection="model.warehouseIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY createdTime, cate.name, pro.name
        union
        select
        <choose>
            <when test="model.type != null and model.type != '' and model.type == 1">
                DATE_FORMAT(p.created_time, '%Y-%m')
            </when>
            <when test="model.type != null and model.type != '' and model.type == 2">
                DATE_FORMAT(p.created_time, '%Y-%m-%d')
            </when>
            <otherwise>
                p.created_time
            </otherwise>
        </choose>
        as createdTime,
        cate.name as cateName,
        pro.name as productName,
        0 as outNum,
        sum(IF(o.type_ = 7, if(p.num_type = '1', num, -num), 0))                               as procurementOutNum,
        sum(IF(o.type_ = 0 or o.type_ = 5 or o.type_ = 6, if(p.num_type = '1', p.num, -p.num), 0)) as inNum,
        sum(if(o.type_ = 2, if(p.num_type = '1', p.num, -p.num), 0)) as pdOut,
        sum(if(o.type_ = 1, if(p.num_type = '1', p.num, -p.num), 0)) as pdIn,
        bo.name as org
        from base_outin_product p
        INNER JOIN base_outin o ON o.id = p.outin_id
        LEFT JOIN base_product pro ON pro.id = p.product_id
        LEFT JOIN base_product_category cate ON cate.id = pro.category_id
        LEFT JOIN base_org bo on p.created_org_id = bo.id
        where o.bill_state = 1
        and p.delete_flag = 0
        AND o.delete_flag = 0
        <if test="model.keyword != null and model.keyword != ''">
            and instr(pro.name, #{model.keyword})
        </if>
        <if test="model.name != null and model.name != ''">
            and instr(pro.name, #{model.name})
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and p.created_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.created_time &lt;= #{model.endDate}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and p.created_org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.warehouseIds != null and model.warehouseIds.size() > 0">
            and p.warehouse_id IN
            <foreach item="item" collection="model.warehouseIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY createdTime, cate.name, pro.name) as outin
        GROUP BY createdTime, cateName, productName
        ORDER BY createdTime desc, cateName desc, productName DESC
    </select>

    <select id="productStatsSum" resultType="java.util.Map">
        select
        sum(outNum) AS outNum,
        sum(procurementOutNum) AS procurementOutNum,
        sum(inNum)  AS inNum,
        sum(pdOut)  AS pdOut,
        sum(pdIn)   AS pdIn from (
        select
        sum(IF(o.type_ = 3 or o.type_ = 4 or o.type_ = 8, if(p.num_type = '1', p.num, -p.num), 0)) as
        outNum,
        0                                                                       as procurementOutNum,
        0 as inNum,
        0 as pdOut,
        0 as pdIn
        from base_outin_product p
        INNER JOIN base_outin o ON o.id = p.outin_id
        LEFT JOIN pos_cash pc ON pc.code = o.code and pc.delete_flag = 0
        LEFT JOIN base_product pro ON pro.id = p.product_id
        LEFT JOIN base_product_category cate ON cate.id = pro.category_id
        LEFT JOIN base_org bo on p.created_org_id = bo.id
        where o.bill_state = 1
        and p.delete_flag = 0
        AND o.delete_flag = 0
        <if test="model.keyword != null and model.keyword != ''">
            and instr(pro.name, #{model.keyword})
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and p.created_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.created_time &lt;= #{model.endDate}
        </if>
        <if test="model.completeStartDate != null and model.completeStartDate != ''">
            and pc.complete_time >= #{model.completeStartDate}
        </if>
        <if test="model.completeEndDate != null and model.completeEndDate != ''">
            and pc.complete_time &lt;= #{model.completeEndDate}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and p.created_org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.warehouseIds != null and model.warehouseIds.size() > 0">
            and p.warehouse_id IN
            <foreach item="item" collection="model.warehouseIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        union
        select

        0 as outNum,
        sum(IF(o.type_ = 7, if(p.num_type = '1', num, -num), 0))                               as procurementOutNum,
        sum(IF(o.type_ = 0 or o.type_ = 5 or o.type_ = 6, if(p.num_type = '1', p.num, -p.num), 0)) as inNum,
        sum(if(o.type_ = 2, if(p.num_type = '1', p.num, -p.num), 0)) as pdOut,
        sum(if(o.type_ = 1, if(p.num_type = '1', p.num, -p.num), 0)) as pdIn
        from base_outin_product p
        INNER JOIN base_outin o ON o.id = p.outin_id
        LEFT JOIN base_product pro ON pro.id = p.product_id
        LEFT JOIN base_product_category cate ON cate.id = pro.category_id
        LEFT JOIN base_org bo on p.created_org_id = bo.id
        where o.bill_state = 1
        and p.delete_flag = 0
        AND o.delete_flag = 0
        <if test="model.keyword != null and model.keyword != ''">
            and instr(pro.name, #{model.keyword})
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and p.created_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.created_time &lt;= #{model.endDate}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and p.created_org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.warehouseIds != null and model.warehouseIds.size() > 0">
            and p.warehouse_id IN
            <foreach item="item" collection="model.warehouseIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ) as outin
    </select>

    <select id="productStatsExport" resultType="java.util.Map">
        select createdTime     AS createdTime,
        cateName    AS cateName,
        productName AS productName,
        sum(outNum) AS outNum,
        sum(procurementOutNum) AS procurementOutNum,
        sum(inNum)  AS inNum,
        sum(pdOut)  AS pdOut,
        sum(pdIn)   AS pdIn,
        org         AS org from (
        select
        <choose>
            <when test="model.type != null and model.type != '' and model.type == 1">
                DATE_FORMAT(p.created_time, '%Y-%m')
            </when>
            <when test="model.type != null and model.type != '' and model.type == 2">
                DATE_FORMAT(p.created_time, '%Y-%m-%d')
            </when>
            <otherwise>
                p.created_time
            </otherwise>
        </choose>
        as createdTime,
        cate.name as cateName,
        pro.name as productName,
        sum(IF(o.type_ = 3 or o.type_ = 4 or o.type_ = 8, if(p.num_type = '1', p.num, -p.num), 0)) as
        outNum,
        0                                                                       as procurementOutNum,
        0 as inNum,
        0 as pdOut,
        0 as pdIn,
        bo.name as org
        from base_outin_product p
        INNER JOIN base_outin o ON o.id = p.outin_id
        LEFT JOIN pos_cash pc ON pc.code = o.code and pc.delete_flag = 0
        LEFT JOIN base_product pro ON pro.id = p.product_id
        LEFT JOIN base_product_category cate ON cate.id = pro.category_id
        LEFT JOIN base_org bo on p.created_org_id = bo.id
        where o.bill_state = 1
        and p.delete_flag = 0
        AND o.delete_flag = 0
        <if test="model.keyword != null and model.keyword != ''">
            and instr(pro.name, #{model.keyword})
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and p.created_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.created_time &lt;= #{model.endDate}
        </if>
        <if test="model.completeStartDate != null and model.completeStartDate != ''">
            and pc.complete_time >= #{model.completeStartDate}
        </if>
        <if test="model.completeEndDate != null and model.completeEndDate != ''">
            and pc.complete_time &lt;= #{model.completeEndDate}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and p.created_org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.warehouseIds != null and model.warehouseIds.size() > 0">
            and p.warehouse_id IN
            <foreach item="item" collection="model.warehouseIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY createdTime, cate.name, pro.name
        union
        select
        <choose>
            <when test="model.type != null and model.type != '' and model.type == 1">
                DATE_FORMAT(p.created_time, '%Y-%m')
            </when>
            <when test="model.type != null and model.type != '' and model.type == 2">
                DATE_FORMAT(p.created_time, '%Y-%m-%d')
            </when>
            <otherwise>
                p.created_time
            </otherwise>
        </choose>
        as createdTime,
        cate.name as cateName,
        pro.name as productName,
        0 as outNum,
        sum(IF(o.type_ = 7, if(p.num_type = '1', num, -num), 0))                               as procurementOutNum,
        sum(IF(o.type_ = 0 or o.type_ = 5 or o.type_ = 6, if(p.num_type = '1', p.num, -p.num), 0)) as inNum,
        sum(if(o.type_ = 2, if(p.num_type = '1', p.num, -p.num), 0)) as pdOut,
        sum(if(o.type_ = 1, if(p.num_type = '1', p.num, -p.num), 0)) as pdIn,
        bo.name as org
        from base_outin_product p
        INNER JOIN base_outin o ON o.id = p.outin_id
        LEFT JOIN base_product pro ON pro.id = p.product_id
        LEFT JOIN base_product_category cate ON cate.id = pro.category_id
        LEFT JOIN base_org bo on p.created_org_id = bo.id
        where o.bill_state = 1
        and p.delete_flag = 0
        AND o.delete_flag = 0
        <if test="model.keyword != null and model.keyword != ''">
            and instr(pro.name, #{model.keyword})
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and p.created_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.created_time &lt;= #{model.endDate}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and p.created_org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.warehouseIds != null and model.warehouseIds.size() > 0">
            and p.warehouse_id IN
            <foreach item="item" collection="model.warehouseIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY createdTime, cate.name, pro.name) as outin
        GROUP BY createdTime, cateName, productName
        ORDER BY createdTime desc, cateName desc, productName DESC
    </select>


</mapper>
