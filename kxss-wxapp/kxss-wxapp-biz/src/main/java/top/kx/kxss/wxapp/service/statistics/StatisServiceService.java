package top.kx.kxss.wxapp.service.statistics;


import com.baomidou.mybatisplus.core.metadata.IPage;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.wxapp.vo.query.statistics.OverviewQuery;
import top.kx.kxss.wxapp.vo.query.statistics.PerformanceMemberQuery;
import top.kx.kxss.wxapp.vo.query.statistics.StatisServiceQuery;
import top.kx.kxss.wxapp.vo.query.statistics.StatisServiceRankingQuery;
import top.kx.kxss.wxapp.vo.result.statistics.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 服务统计
 *
 * <AUTHOR>
 */
public interface StatisServiceService {

    /**
     * 商品统计
     * @param query
     * @return
     */
    List<StatisServiceResultVO> statistics(StatisServiceQuery query);


    Map<String, Object> statisticsList(PageParams<StatisServiceQuery> query);


    StatiscsServiceListResultVO statisticsListSum(StatisServiceQuery query);


    void statisticsListExport(StatisServiceQuery query, HttpServletResponse response);

    /**
     * 条状图⌚表
     * @param query
     * @return
     */
    StripChartResultVO duration(OverviewQuery query);

    /**
     * 服务次数
     * @param query
     * @return
     */
    ChartResultVO serviceNum(OverviewQuery query);

    ChartResultVO serviceDuration(OverviewQuery query);

    /**
     * 服务时长统计排行
     * @param query
     * @return
     */
    List<StatisServiceRankingResultVO> durationRanking(StatisServiceRankingQuery query);

    IPage<StatisPerformanceMemberResultVO> memberRanking(PageParams<PerformanceMemberQuery> query);

    List<StatisServiceResultVO> listByService(StatisServiceQuery query);

    List<StatisServiceResultVO> listByServicePerson(StatisServiceQuery query);
}
