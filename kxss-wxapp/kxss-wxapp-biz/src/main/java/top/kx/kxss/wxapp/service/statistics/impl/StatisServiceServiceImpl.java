package top.kx.kxss.wxapp.service.statistics.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.mapper.cash.PosCashMapper;
import top.kx.kxss.app.service.cash.PosCashServiceService;
import top.kx.kxss.app.vo.result.cash.StatisResultVO;
import top.kx.kxss.base.entity.common.BaseDict;
import top.kx.kxss.base.entity.group.BaseGroup;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.service.common.BaseDictService;
import top.kx.kxss.base.service.common.BaseParameterService;
import top.kx.kxss.base.service.group.BaseGroupService;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.vo.result.common.BaseDictResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.file.api.FileApi;
import top.kx.kxss.model.constant.EchoDictType;
import top.kx.kxss.model.enumeration.app.CashTableStatusEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillTypeEnum;
import top.kx.kxss.model.enumeration.base.ServiceStaffTimeEnum;
import top.kx.kxss.wxapp.service.statistics.CustomService;
import top.kx.kxss.wxapp.service.statistics.StatisServiceService;
import top.kx.kxss.wxapp.vo.query.common.OrgIdListQuery;
import top.kx.kxss.wxapp.vo.query.statistics.OverviewQuery;
import top.kx.kxss.wxapp.vo.query.statistics.PerformanceMemberQuery;
import top.kx.kxss.wxapp.vo.query.statistics.StatisServiceQuery;
import top.kx.kxss.wxapp.vo.query.statistics.StatisServiceRankingQuery;
import top.kx.kxss.wxapp.vo.result.statistics.*;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@DS(DsConstant.BASE_TENANT)
public class StatisServiceServiceImpl implements StatisServiceService {

    @Autowired
    private PosCashMapper posCashMapper;
    @Autowired
    private CustomService customService;
    @Autowired
    private BaseDictService baseDictService;
    @Autowired
    private BaseGroupService baseGroupService;
    @Autowired
    private EchoService echoService;
    @Autowired
    private BaseEmployeeService baseEmployeeService;
    @Autowired
    private BaseParameterService baseParameterService;
    @Autowired
    private PosCashServiceService posCashServiceService;
    @Autowired
    private FileApi fileApi;

    @Override
    public List<StatisServiceResultVO> statistics(StatisServiceQuery query) {
        if (StringUtils.isNotBlank(query.getStartDate()) && StringUtils.isNotBlank(query.getEndDate())) {
            customService.storeTime(query);
        }
        ServiceStaffTimeEnum defaultServiceStaffTime = baseParameterService.getDefaultServiceStaffTime();
        if (Objects.nonNull(query.getStaffTimeEnum())) {
            defaultServiceStaffTime = query.getStaffTimeEnum();
        }
        initOrgIdList(query);
        //营业额数据，根据会员进行分组
        QueryWrapper<PosCash> wrapper = new QueryWrapper<>();
        wrapper//.isNull("t.cash_thail_id")
                .notIn("p.bill_type", Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                        , PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode()
                        , PosCashBillStateEnum.PART_REFUND.getCode()))
                .eq("p.delete_flag", 0)
                .eq("t.delete_flag", 0)
                .eq("t.status", CashTableStatusEnum.STOP.getCode())
                .and(StrUtil.isNotBlank(query.getName()), wrap -> wrap.like("e.real_name", query.getName())
                        .or().like("e.name", query.getName()))
                //.like(StrUtil.isNotBlank(query.getKeyword()), "e.real_name", query.getKeyword())
                .in(CollUtil.isNotEmpty(query.getServiceIds()), "t.service_id", query.getServiceIds())
                .in(CollUtil.isNotEmpty(query.getClockTypes()), "t.clock_type", query.getClockTypes())
                .between(StringUtils.isNotBlank(query.getStartDate()) && StringUtils.isNotBlank(query.getEndDate()),
                        "p.complete_time", query.getStartDate(), query.getEndDate())
                .in(CollUtil.isNotEmpty(query.getOrgIdList()), "p.org_id", query.getOrgIdList());
        if (CollUtil.isNotEmpty(query.getGroupIds())) {
            wrapper.in("e.group_id", query.getGroupIds());
        }
        if (StringUtils.isNotBlank(query.getKeyword())) {
            wrapper.and(w -> w.like("e.real_name", query.getKeyword()).or().like("e.name", query.getKeyword()));
        }
        // 注意分组时,在代码里面引入了 thailService, 固定值, 原因: 需要直接查询到套餐上的助教

        List<StatisResultVO> statisResultVOList = posCashMapper.selectByService("CONCAT(t.employee_id,'-',t.service_id,'-', if(t.clock_type is null and t.cash_thail_id is not null, 'thailService', t.clock_type))",
                " if(e.delete_flag = 1 ,CONCAT(e.real_name,'(已删除)'),e.real_name)", wrapper);
        if (CollUtil.isEmpty(statisResultVOList)) {
            return Lists.newArrayList();
        }
        //根据进行分组查询
        Map<String, StatisResultVO> voMap = statisResultVOList.stream()
                .filter(v -> StrUtil.isNotBlank(v.getField()))
                .collect(
                        Collectors.groupingBy(StatisResultVO::getField,
                                Collectors.reducing(new StatisResultVO(),
                                        energyVo -> energyVo, (preVo, curVo) -> StatisResultVO.merge(curVo, preVo))));
        Map<String, BaseDict> baseDictMap = getClockType();
        ServiceStaffTimeEnum finalDefaultServiceStaffTime = defaultServiceStaffTime;
        List<StatisServiceResultVO> resultVOList = voMap.values().stream().map(v -> {
            StatisServiceResultVO productResultVO = BeanUtil.copyProperties(v, StatisServiceResultVO.class);
            productResultVO.setEmpName(StringUtils.isNotBlank(v.getName()) ? v.getName() : "-");
            productResultVO.setName(StrUtil.isBlank(v.getFieldName()) ? "未知" : v.getFieldName());
            productResultVO.setCycleNum(v.getCycleNum() == null ? 0 : v.getCycleNum());
            if (StrUtil.isNotBlank(v.getServiceName())) {
                String[] split = v.getServiceName().split("-");
                productResultVO.setServiceName(split[0]);
            }
            if (StrUtil.isNotBlank(v.getClockType())) {
                if (CollUtil.isNotEmpty(baseDictMap) && ObjectUtil.isNotNull(baseDictMap.get(v.getClockType()))) {
                    productResultVO.setClockType(baseDictMap.get(v.getClockType()).getName());
                }
            }
            productResultVO.setIsThail(v.getField().contains("-thailService"));
            productResultVO.setDurationDesc(posCashServiceService.serviceDurationExportDesc(productResultVO.getDuration(), finalDefaultServiceStaffTime));
            productResultVO.setChargingDurationDesc(posCashServiceService.serviceDurationExportDesc(productResultVO.getChargingDuration(), finalDefaultServiceStaffTime));
            productResultVO.setFieldName(StrUtil.isBlank(v.getFieldName()) ? "未知" : v.getFieldName());
            return productResultVO;
        }).sorted(Comparator.comparing(StatisServiceResultVO::getName)).collect(Collectors.toList());
        StatisServiceResultVO resultVO = getStatisServiceSum(resultVOList, defaultServiceStaffTime);
        resultVOList.add(resultVO);
        return resultVOList;
    }

    @Override
    public List<StatisServiceResultVO> listByService(StatisServiceQuery query) {
        QueryWrapper<PosCash> wrapper = getListServiceWraps(query);
        // 注意分组时,在代码里面引入了 thailService, 固定值, 原因: 需要直接查询到套餐上的助教
        List<StatisResultVO> statisResultVOList = posCashMapper.selectByService("CONCAT(t.employee_id,'-',t.service_id,'-',if(t.cash_thail_id != '' and t.cash_thail_id is not null, 'thailService', 0))",
                "if(e.delete_flag = 1 ,CONCAT(e.real_name,'(已删除)'),e.real_name) ", wrapper);
        if (CollUtil.isEmpty(statisResultVOList)) {
            return Lists.newArrayList();
        }
        ServiceStaffTimeEnum serviceStaffTimeEnum = baseParameterService.getDefaultServiceStaffTime();
        //根据进行分组查询
        Map<String, StatisResultVO> voMap = statisResultVOList.stream()
                .filter(v -> StrUtil.isNotBlank(v.getField()))
                .collect(
                        Collectors.groupingBy(StatisResultVO::getField,
                                Collectors.reducing(new StatisResultVO(),
                                        energyVo -> energyVo, (preVo, curVo) -> StatisResultVO.merge(curVo, preVo))));
        Map<String, BaseDict> baseDictMap = getClockType();
        List<StatisServiceResultVO> resultVOList = voMap.values().stream().map(v -> {
            StatisServiceResultVO productResultVO = BeanUtil.copyProperties(v, StatisServiceResultVO.class);
            productResultVO.setName(StrUtil.isBlank(v.getFieldName()) ? "未知" : v.getFieldName());
            if (StrUtil.isNotBlank(v.getServiceName())) {
                String[] split = v.getServiceName().split("-");
                productResultVO.setServiceName(split[0]);
            }
            if (StrUtil.isNotBlank(v.getClockType())) {
                if (CollUtil.isNotEmpty(baseDictMap) && ObjectUtil.isNotNull(baseDictMap.get(v.getClockType()))) {
                    productResultVO.setClockType(baseDictMap.get(v.getClockType()).getName());
                }
            }
            productResultVO.setIsThail(v.getField().contains("-thailService"));
            productResultVO.setDurationDesc(posCashServiceService.serviceDurationExportDesc(productResultVO.getDuration(), serviceStaffTimeEnum));
            productResultVO.setChargingDurationDesc(posCashServiceService.serviceDurationExportDesc(productResultVO.getChargingDuration(), serviceStaffTimeEnum));
            productResultVO.setName(StrUtil.isBlank(v.getFieldName()) ? "未知" : v.getFieldName());
            return productResultVO;
        }).sorted(Comparator.comparing(StatisServiceResultVO::getName)).collect(Collectors.toList());
        StatisServiceResultVO resultVO = getServiceSum(resultVOList, serviceStaffTimeEnum);
        resultVOList.add(resultVO);
        return resultVOList;
    }

    @NotNull
    private StatisServiceResultVO getStatisServiceSum(List<StatisServiceResultVO> resultVOList, ServiceStaffTimeEnum serviceStaffTimeEnum) {
        StatisServiceResultVO resultVO = StatisServiceResultVO.builder()
                .number("合计")
                .num(resultVOList.stream().mapToInt(StatisServiceResultVO::getNum).sum())
                .duration(resultVOList.stream().mapToInt(StatisServiceResultVO::getDuration).sum())
                .amount(resultVOList.stream().map(StatisServiceResultVO::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP))
                .payment(resultVOList.stream().map(StatisServiceResultVO::getPayment)
                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP))
                .discountAmount(resultVOList.stream().map(StatisServiceResultVO::getDiscountAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP))
                .chargingDuration(resultVOList.stream().mapToInt(StatisServiceResultVO::getChargingDuration).sum())
                .build();
        resultVO.setChargingDurationDesc(posCashServiceService.serviceDurationExportDesc(resultVO.getChargingDuration(), serviceStaffTimeEnum));
        resultVO.setDurationDesc(posCashServiceService.serviceDurationExportDesc(resultVO.getDuration(), serviceStaffTimeEnum));
        return resultVO;
    }

    @NotNull
    private StatisServiceResultVO getServiceSum(List<StatisServiceResultVO> resultVOList, ServiceStaffTimeEnum serviceStaffTimeEnum) {
        StatisServiceResultVO resultVO = StatisServiceResultVO.builder()
                .number("合计")
                .num(resultVOList.stream().mapToInt(StatisServiceResultVO::getNum).sum())
                .duration(resultVOList.stream().mapToInt(StatisServiceResultVO::getDuration).sum())
                .amount(resultVOList.stream().filter(s -> Objects.isNull(s.getIsThail()) || !s.getIsThail()).map(StatisServiceResultVO::getAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP))
                .payment(resultVOList.stream().filter(s -> Objects.isNull(s.getIsThail()) || !s.getIsThail()).map(StatisServiceResultVO::getPayment)
                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP))
                .discountAmount(resultVOList.stream().filter(s -> Objects.isNull(s.getIsThail()) || !s.getIsThail()).map(StatisServiceResultVO::getDiscountAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP))
                .chargingDuration(resultVOList.stream().mapToInt(StatisServiceResultVO::getChargingDuration).sum())
                .build();
        resultVO.setChargingDurationDesc(posCashServiceService.serviceDurationExportDesc(resultVO.getChargingDuration(), serviceStaffTimeEnum));
        resultVO.setDurationDesc(posCashServiceService.serviceDurationExportDesc(resultVO.getDuration(), serviceStaffTimeEnum));
        return resultVO;
    }

    @NotNull
    private QueryWrapper<PosCash> getListServiceWraps(StatisServiceQuery query) {
        if (StringUtils.isNotBlank(query.getStartDate()) && StringUtils.isNotBlank(query.getEndDate())) {
            customService.storeTime(query);
        }
        initOrgIdList(query);
        //营业额数据，根据会员进行分组
        QueryWrapper<PosCash> wrapper = new QueryWrapper<>();
        wrapper
                .notIn("p.bill_type", Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                        , PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode()
                        , PosCashBillStateEnum.PART_REFUND.getCode()))
                .eq("p.delete_flag", 0)
                .eq("t.delete_flag", 0)
                .eq("t.status", CashTableStatusEnum.STOP.getCode())
                .and(StrUtil.isNotBlank(query.getName()), wrap -> wrap.like("e.real_name", query.getName())
                        .or().like("e.name", query.getName()))
                .like(StrUtil.isNotBlank(query.getKeyword()), "e.real_name", query.getKeyword())
                .in(CollUtil.isNotEmpty(query.getServiceIds()), "t.service_id", query.getServiceIds())
                .in(CollUtil.isNotEmpty(query.getClockTypes()), "t.clock_type", query.getClockTypes())
                .between(StringUtils.isNotBlank(query.getStartDate()) && StringUtils.isNotBlank(query.getEndDate()),
                        "p.complete_time", query.getStartDate(), query.getEndDate())
                .in(CollUtil.isNotEmpty(query.getOrgIdList()), "p.org_id", query.getOrgIdList());
        return wrapper;
    }

    @Override
    public List<StatisServiceResultVO> listByServicePerson(StatisServiceQuery query) {
        QueryWrapper<PosCash> wrapper = getListServiceWraps(query);
        List<StatisResultVO> statisResultVOList = posCashMapper.selectStatisServiceList("t.employee_id",
                "if(e.delete_flag = 1 ,CONCAT(e.real_name,'(已删除)'),e.real_name) ", wrapper);
        if (CollUtil.isEmpty(statisResultVOList)) {
            return Lists.newArrayList();
        }
        ServiceStaffTimeEnum serviceStaffTimeEnum = baseParameterService.getDefaultServiceStaffTime();
        // 根据field 分组
        Map<String, StatisResultVO> voMap = statisResultVOList.stream().collect(Collectors.toMap(StatisResultVO::getField, Function.identity()));
        List<StatisServiceResultVO> resultVOList = voMap.values().stream().map(v -> {
            StatisServiceResultVO productResultVO = BeanUtil.copyProperties(v, StatisServiceResultVO.class);
            productResultVO.setNum(v.getCashNum());
            productResultVO.setDurationDesc(posCashServiceService.serviceDurationExportDesc(productResultVO.getDuration(), serviceStaffTimeEnum));
            productResultVO.setChargingDurationDesc(posCashServiceService.serviceDurationExportDesc(productResultVO.getChargingDuration(), serviceStaffTimeEnum));
            productResultVO.setName(StrUtil.isBlank(v.getFieldName()) ? "未知" : v.getFieldName());
            return productResultVO;
        }).sorted(Comparator.comparing(StatisServiceResultVO::getName)).collect(Collectors.toList());
        StatisServiceResultVO resultVO = getServiceSum(resultVOList, serviceStaffTimeEnum);
        resultVOList.add(resultVO);
        return resultVOList;
    }

    @NotNull
    private Map<String, BaseDict> getClockType() {
        List<BaseDictResultVO> dictList = baseDictService.findList(Wraps.<BaseDict>lbQ().eq(BaseDict::getParentKey, EchoDictType.Base.CLOCK_TYPE).eq(BaseDict::getCreatedOrgId, ContextUtil.getCurrentCompanyId()));
        if (CollUtil.isEmpty(dictList)) {
            return MapUtil.newHashMap();
        }
        List<BaseDict> baseDictList = BeanPlusUtil.toBeanList(dictList, BaseDict.class);
        return CollUtil.isEmpty(baseDictList) ? MapUtil.newHashMap() : baseDictList.stream().collect(Collectors.toMap(BaseDict::getKey, Function.identity()));
    }

    @Override
    public Map<String, Object> statisticsList(PageParams<StatisServiceQuery> params) {
        params.setSort("");
        params.setSort("");
        IPage<StatisResultVO> page = params.buildPage(StatisResultVO.class);
        StatisServiceQuery query = params.getModel();
        initOrgIdList(query);
        if (StringUtils.isNotBlank(query.getStartDate()) && StringUtils.isNotBlank(query.getEndDate())) {
            customService.storeTime(query);
        }
        //营业额数据，根据会员进行分组
        QueryWrapper<PosCash> wrapper = new QueryWrapper<>();
        wrapper//.isNull("t.cash_thail_id")
                .notIn("p.bill_type", Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                        , PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode()
                        , PosCashBillStateEnum.PART_REFUND.getCode()))
                .eq("p.delete_flag", 0)
                .eq("t.delete_flag", 0)
                .eq("t.status", CashTableStatusEnum.STOP.getCode())
                .eq(StrUtil.isNotBlank(query.getName()), "e.real_name", query.getName())
                //.like(StrUtil.isNotBlank(query.getKeyword()), "e.real_name", query.getKeyword())
                .in(CollUtil.isNotEmpty(query.getServiceIds()), "t.service_id", query.getServiceIds())
                .in(CollUtil.isNotEmpty(query.getClockTypes()), "t.clock_type", query.getClockTypes())
                .between(StringUtils.isNotBlank(query.getStartDate()) && StringUtils.isNotBlank(query.getEndDate()), "p.complete_time", query.getStartDate(), query.getEndDate())
                .in(CollUtil.isNotEmpty(query.getOrgIdList()), "p.org_id", query.getOrgIdList());
        if (CollUtil.isNotEmpty(query.getGroupIds())) {
            wrapper.in("e.group_id", query.getGroupIds());
        }
        if (StringUtils.isNotBlank(query.getKeyword())) {
            wrapper.and(w -> w.like("e.real_name", query.getKeyword()).or().like("e.name", query.getKeyword()));
        }
        // 注意分组时,在代码里面引入了 thailService, 固定值, 原因: 需要直接查询到套餐上的助教
        IPage<StatisResultVO> statisResultVOIPage = posCashMapper.selectByServiceListPage(page, "CONCAT(t.employee_id,'-',t.service_id,'-', if(t.clock_type is null and t.cash_thail_id is not null, 'thailService', t.clock_type))",
                " if(e.delete_flag = 1 ,CONCAT(e.real_name,'(已删除)'),e.real_name)", wrapper);

        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("number").label("工号").width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("fieldName").label("艺名").width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("姓名").width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("groupName").label("组名").width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceName").label("服务项目").width(120).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("clockType").label("点钟方式").width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("服务次数").width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("durationDesc").label("服务时长").width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("chargingDurationDesc").label("计费时长").width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("订单金额(元)").width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payment").label("服务收入(元)").width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("discountAmount").label("优惠(元)").width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店").width(200).emptyString("-").fixed(false).build()
        );

        if (CollUtil.isEmpty(statisResultVOIPage.getRecords())) {
            Map<String, Object> objectMap = BeanUtil.beanToMap(statisResultVOIPage);
            objectMap.put("records", new ArrayList<>());
            objectMap.put("columnList", columnVOList);
            return objectMap;
        }
        //ServiceStaffTimeEnum serviceStaffTimeEnum = baseParameterService.getDefaultServiceStaffTime();
//        statisResultVOIPage.getRecords().forEach(v -> {
//            v.setDurationDesc(posCashServiceService.serviceDurationDesc(v.getDuration(), serviceStaffTimeEnum));
//            v.setChargingDurationDesc(posCashServiceService.serviceDurationDesc(v.getChargingDuration(), serviceStaffTimeEnum));
//        });

        IPage<Map> pageList = BeanPlusUtil.toBeanPage(statisResultVOIPage, Map.class);

        List<StatisResultVO> statisResultVOList = statisResultVOIPage.getRecords();

        //根据进行分组查询
        Map<String, StatisResultVO> voMap = statisResultVOList.stream()
                .filter(v -> StrUtil.isNotBlank(v.getField()))
                .collect(
                        Collectors.groupingBy(StatisResultVO::getField,
                                Collectors.reducing(new StatisResultVO(),
                                        energyVo -> energyVo, (preVo, curVo) -> StatisResultVO.merge(curVo, preVo))));
        Map<String, BaseDict> baseDictMap = getClockType();
        // 提取所有的group_id
        List<Long> groupIds = statisResultVOList.stream()
                .map(StatisResultVO::getGroupId)
                .filter(Objects::nonNull)
                .map(id -> Long.valueOf(id.toString()))
                .distinct()
                .collect(Collectors.toList());
        // 查询所有组信息
        Map<Long, String> groupNameMap = new HashMap<>();
        if (CollUtil.isNotEmpty(groupIds)) {
            groupNameMap = baseGroupService.listByIds(groupIds).stream().collect(Collectors.toMap(BaseGroup::getId, BaseGroup::getName));
        }

        Map<Long, String> finalGroupNameMap = groupNameMap;
        List<Map> resultVOList = voMap.values().stream().map(v -> {
            Map productResultVO = BeanUtil.copyProperties(v, Map.class);
            //productResultVO.put("fieldName", StrUtil.isBlank(v.getFieldName()) ? "未知" : v.getFieldName());
            //productResultVO.put("chargingDuration", 0);
            if (StrUtil.isNotBlank(v.getServiceName())) {
                String[] split = v.getServiceName().split("-");
                productResultVO.put("serviceName", split[0]);
            }
            productResultVO.put("durationDesc", posCashServiceService.serviceDurationDesc(v.getDuration(), ServiceStaffTimeEnum.MINUTE));
            productResultVO.put("chargingDurationDesc", posCashServiceService.serviceDurationDesc(v.getChargingDuration(), ServiceStaffTimeEnum.MINUTE));
            productResultVO.put("groupName", finalGroupNameMap.getOrDefault(v.getGroupId(), "-"));
            if (StrUtil.isNotBlank(v.getClockType())) {
                if (CollUtil.isNotEmpty(baseDictMap) && ObjectUtil.isNotNull(baseDictMap.get(v.getClockType()))) {
                    productResultVO.put("clockType", baseDictMap.get(v.getClockType()).getName());
                }
            }
            productResultVO.put("isThail", v.getField().contains("-thailService"));
//            if (StrUtil.isNotBlank(v.getCycle())) {
//                productResultVO.put("chargingDuration", getChargingDuration(v));
//            }
            productResultVO.put("fieldName", StrUtil.isBlank(v.getFieldName()) ? "未知" : v.getFieldName());
            return productResultVO;
        }).collect(Collectors.toList());
        pageList.setRecords(resultVOList);
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public StatiscsServiceListResultVO statisticsListSum(StatisServiceQuery query) {
        initOrgIdList(query);
        if (StringUtils.isNotBlank(query.getStartDate()) && StringUtils.isNotBlank(query.getEndDate())) {
            customService.storeTime(query);
        }
        //营业额数据，根据会员进行分组
        QueryWrapper<PosCash> wrapper = new QueryWrapper<>();
        wrapper//.isNull("t.cash_thail_id")
                .notIn("p.bill_type", Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                        , PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode()
                        , PosCashBillStateEnum.PART_REFUND.getCode()))
                .eq("p.delete_flag", 0)
                .eq("t.delete_flag", 0)
                .eq("t.status", CashTableStatusEnum.STOP.getCode())
                .eq(StrUtil.isNotBlank(query.getName()), "e.real_name", query.getName())
                //.like(StrUtil.isNotBlank(query.getKeyword()), "e.real_name", query.getKeyword())
                .in(CollUtil.isNotEmpty(query.getServiceIds()), "t.service_id", query.getServiceIds())
                .in(CollUtil.isNotEmpty(query.getClockTypes()), "t.clock_type", query.getClockTypes())
                .between(StringUtils.isNotBlank(query.getStartDate()) && StringUtils.isNotBlank(query.getEndDate()), "p.complete_time", query.getStartDate(), query.getEndDate())
                .in(CollUtil.isNotEmpty(query.getOrgIdList()), "p.org_id", query.getOrgIdList());
        if (CollUtil.isNotEmpty(query.getGroupIds())) {
            wrapper.in("e.group_id", query.getGroupIds());
        }
        if (StringUtils.isNotBlank(query.getKeyword())) {
            wrapper.and(w -> w.like("e.real_name", query.getKeyword()).or().like("e.name", query.getKeyword()));
        }
        StatisResultVO statisResultVO = posCashMapper.selectByServiceListSum(wrapper);
        if (Objects.isNull(statisResultVO)) {
            return StatiscsServiceListResultVO.builder().number("合计").build();
        }
        return StatiscsServiceListResultVO.builder()
                .number("合计")
                .num(statisResultVO.getNum())
                .durationDesc(posCashServiceService.serviceDurationDesc(statisResultVO.getDuration(), ServiceStaffTimeEnum.MINUTE))
                .chargingDurationDesc(posCashServiceService.serviceDurationDesc(statisResultVO.getChargingDuration(), ServiceStaffTimeEnum.MINUTE))
                .amount(statisResultVO.getAmount())
                .payment(statisResultVO.getPayment())
                .discountAmount(statisResultVO.getDiscountAmount())
                .build();
    }

    @Override
    public void statisticsListExport(StatisServiceQuery query, HttpServletResponse response) {
        initOrgIdList(query);
        query.setStaffTimeEnum(ServiceStaffTimeEnum.MINUTE);
        List<StatisServiceResultVO> statistics = statistics(query);
        statistics.forEach(s -> {
            s.setDurationDesc(posCashServiceService.serviceDurationDesc(s.getDurationDesc()));
            s.setChargingDurationDesc(posCashServiceService.serviceDurationDesc(s.getChargingDurationDesc()));
        });
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("number").label("工号").width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("fieldName").label("艺名").width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("empName").label("姓名").width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("groupName").label("组名").width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceName").label("服务项目").width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("clockType").label("点钟方式").width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("服务次数").width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("durationDesc").label("服务时长").width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("chargingDurationDesc").label("计费时长").width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("订单金额(元)").width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payment").label("服务收入(元)").width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("discountAmount").label("优惠(元)").width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店").width(200).emptyString("-").fixed(false).build()
        );
        // 提取所有的group_id
        List<Long> groupIds = statistics.stream()
                .map(StatisServiceResultVO::getGroupId)
                .filter(Objects::nonNull)
                .map(id -> Long.valueOf(id.toString()))
                .distinct()
                .collect(Collectors.toList());
        // 查询所有组信息
        Map<Long, String> groupNameMap = new HashMap<>();
        if (CollUtil.isNotEmpty(groupIds)) {
            groupNameMap = baseGroupService.listByIds(groupIds).stream().collect(Collectors.toMap(BaseGroup::getId, BaseGroup::getName));
        }
        Map<Long, String> finalGroupNameMap = groupNameMap;
        statistics.forEach(s -> {
            s.setGroupName(finalGroupNameMap.getOrDefault(s.getGroupId(), "-"));
        });
        export(response, statistics, columnVOList, "服务时间导出");
    }

    private Integer getChargingDuration(StatisResultVO statisResultVO) {
        if (ObjectUtil.isNull(statisResultVO)) {
            return 0;
        }
        if (StrUtil.isBlank(statisResultVO.getCycle())) {
            return 0;
        }
        String[] split = statisResultVO.getCycle().split("元/");
        if (split.length != 2) {
            return 0;
        }
        String str = split[1];
        int num = statisResultVO.getCycleNum() == null ? 0 : statisResultVO.getCycleNum();
        if (str.startsWith("小时")) {
            return 60 * num;
        } else if (str.contains("小时")) {
            String[] split1 = str.split("小时");
            return Integer.parseInt(split1[0]) * num;
        } else if (str.startsWith("分钟")) {
            return num;
        } else if (str.contains("分钟")) {
            String[] split1 = str.split("分钟");
            return Integer.parseInt(split1[0]) * num;
        }
        return 0;
    }

    @Override
    public StripChartResultVO duration(OverviewQuery query) {
        customService.storeTime(query);
        //营业额数据，根据会员进行分组
        QueryWrapper<PosCash> wrapper = new QueryWrapper<>();
        wrapper//.isNull("t.cash_thail_id")
                .eq("t.delete_flag", 0)
                .eq("p.delete_flag", 0)
                .eq("t.status", CashTableStatusEnum.STOP.getCode())
                .notIn("p.bill_type", Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                        , PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode()
                        , PosCashBillStateEnum.PART_REFUND.getCode()))
                .between("p.complete_time", query.getStartDate(), query.getEndDate())
                .eq("p.org_id", ContextUtil.getCurrentCompanyId());
        List<StatisResultVO> statisResultVOList = posCashMapper.selectDurationByService("t.employee_id", "e.real_name", wrapper);
        if (CollUtil.isEmpty(statisResultVOList)) {
            return StripChartResultVO.builder().dataX(Lists.newArrayList()).dataY(Lists.newArrayList()).build();
        }

//        //根据进行分组查询
        Map<String, StatisResultVO> voMap = statisResultVOList.stream().collect(
                Collectors.groupingBy(StatisResultVO::getField,
                        Collectors.reducing(new StatisResultVO(),
                                energyVo -> energyVo, (preVo, curVo) -> StatisResultVO.merge(curVo, preVo))));
        //服务时长
        List<StatisResultVO> voList = voMap.values().stream().sorted(Comparator.comparing(StatisResultVO::getDuration).reversed()).collect(Collectors.toList());

        voList.forEach(v -> v.setFieldName(StrUtil.isBlank(v.getFieldName()) ? "未知" : v.getFieldName()));
        return StripChartResultVO.builder().dataX(voList.stream().map(StatisResultVO::getFieldName).collect(Collectors.toList()))
                .dataY(voList.stream().map(StatisResultVO::getDuration).collect(Collectors.toList())).build();
    }

    @Override
    public ChartResultVO serviceNum(OverviewQuery query) {
        customService.storeTime(query);
        ServiceStaffTimeEnum serviceStaffTimeEnum = baseParameterService.getDefaultServiceStaffTime();
        //营业额数据，根据会员进行分组
        QueryWrapper<PosCash> wrapper = new QueryWrapper<>();
        wrapper//.isNull("t.cash_thail_id")
                .notIn("p.bill_type", Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                        , PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode()
                        , PosCashBillStateEnum.PART_REFUND.getCode()))
                .eq("p.delete_flag", 0)
                .eq("t.delete_flag", 0)
                .eq("t.status", CashTableStatusEnum.STOP.getCode())
                .between("p.complete_time", query.getStartDate(), query.getEndDate())
                .eq("p.org_id", ContextUtil.getCurrentCompanyId());
        List<StatisResultVO> statisResultVOList = posCashMapper.selectByService("concat(t.employee_id,'-',if(t.cash_thail_id != '' and t.cash_thail_id is not null, 'thailService', 0))", "e.real_name", wrapper);
        if (CollUtil.isEmpty(statisResultVOList)) {
            return ChartResultVO.builder().data(Lists.newArrayList()).dataList(Lists.newArrayList()).build();
        }
        //根据进行分组查询
        Map<String, StatisResultVO> voMap = statisResultVOList.stream().collect(
                Collectors.groupingBy(StatisResultVO::getField,
                        Collectors.reducing(new StatisResultVO(),
                                energyVo -> energyVo, (preVo, curVo) -> StatisResultVO.merge(curVo, preVo))));
        LbQueryWrap<BaseDict> eq = Wraps.<BaseDict>lbQ()
                .eq(BaseDict::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(BaseDict::getDeleteFlag, 0).eq(BaseDict::getParentKey, EchoDictType.Base.CLOCK_TYPE);
        List<BaseDict> baseDictList = baseDictService.list(eq);
        Map<String, BaseDict> baseDictMap = CollUtil.isEmpty(baseDictList) ? MapUtil.newHashMap() : baseDictList.stream().collect(Collectors.toMap(BaseDict::getKey, Function.identity()));
        List<StatisServiceResultVO> resultVOList = voMap.values().stream().map(v -> {
            StatisServiceResultVO productResultVO = BeanUtil.copyProperties(v, StatisServiceResultVO.class);
            productResultVO.setName(v.getFieldName());
            if (StrUtil.isNotBlank(v.getClockType())) {
                if (CollUtil.isNotEmpty(baseDictMap) && ObjectUtil.isNotNull(baseDictMap.get(v.getClockType()))) {
                    productResultVO.setClockType(baseDictMap.get(v.getClockType()).getName());
                }
            }
            productResultVO.setIsThail(v.getField().contains("-thailService"));
            productResultVO.setDurationDesc(posCashServiceService.serviceDurationExportDesc(productResultVO.getDuration(), serviceStaffTimeEnum));
            productResultVO.setChargingDurationDesc(posCashServiceService.serviceDurationExportDesc(productResultVO.getChargingDuration(), serviceStaffTimeEnum));
            return productResultVO;
        }).sorted(Comparator.comparing(StatisServiceResultVO::getPayment).reversed()).collect(Collectors.toList());
        StatisServiceResultVO resultVO = getServiceSum(resultVOList, serviceStaffTimeEnum);
        resultVOList.add(resultVO);
        return ChartResultVO.builder().data(voMap.keySet().stream().map(v -> ChartResultVO.NameValueResultVO.builder()
                .name(voMap.get(v).getFieldName()).value(voMap.get(v).getNum().toString())
                .build()).collect(Collectors.toList())).dataList(resultVOList).build();
    }

    @Override
    public ChartResultVO serviceDuration(OverviewQuery query) {
        customService.storeTime(query);
        ServiceStaffTimeEnum serviceStaffTimeEnum = baseParameterService.getDefaultServiceStaffTime();
        //营业额数据，根据会员进行分组
        QueryWrapper<PosCash> wrapper = new QueryWrapper<>();
        wrapper//.isNull("t.cash_thail_id")
                .notIn("p.bill_type", Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                        , PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode()
                        , PosCashBillStateEnum.PART_REFUND.getCode()))
                .eq("p.delete_flag", 0)
                .eq("t.delete_flag", 0)
                .eq("t.status", CashTableStatusEnum.STOP.getCode())
                .between("p.complete_time", query.getStartDate(), query.getEndDate())
                .eq("p.org_id", ContextUtil.getCurrentCompanyId());
        List<StatisResultVO> statisResultVOList = posCashMapper.selectByService("concat(t.service_id,'-',if(t.cash_thail_id != '' and t.cash_thail_id is not null, 'thailService', '0'))", "pro.name", wrapper);
        if (CollUtil.isEmpty(statisResultVOList)) {
            return ChartResultVO.builder().data(Lists.newArrayList()).dataList(Lists.newArrayList()).build();
        }
        //根据进行分组查询
        Map<String, StatisResultVO> voMap = statisResultVOList.stream().collect(
                Collectors.groupingBy(StatisResultVO::getField,
                        Collectors.reducing(new StatisResultVO(),
                                energyVo -> energyVo, (preVo, curVo) -> StatisResultVO.merge(curVo, preVo))));
        List<StatisServiceResultVO> resultVOList = voMap.values().stream().map(v -> {
            StatisServiceResultVO productResultVO = BeanUtil.copyProperties(v, StatisServiceResultVO.class);
            productResultVO.setName(v.getFieldName());
            productResultVO.setIsThail(v.getField().contains("-thailService"));
            productResultVO.setDurationDesc(posCashServiceService.serviceDurationExportDesc(productResultVO.getDuration(), serviceStaffTimeEnum));
            productResultVO.setChargingDurationDesc(posCashServiceService.serviceDurationExportDesc(productResultVO.getChargingDuration(), serviceStaffTimeEnum));
            return productResultVO;
        }).sorted(Comparator.comparing(StatisServiceResultVO::getDuration).reversed()).collect(Collectors.toList());
        StatisServiceResultVO resultVO = getServiceSum(resultVOList, serviceStaffTimeEnum);
        resultVOList.add(resultVO);
        return ChartResultVO.builder().data(voMap.keySet().stream().map(v -> ChartResultVO.NameValueResultVO.builder()
                .name(voMap.get(v).getFieldName()).value(voMap.get(v).getDuration().toString())
                .build()).collect(Collectors.toList())).dataList(resultVOList).build();
    }

    @Override
    public List<StatisServiceRankingResultVO> durationRanking(StatisServiceRankingQuery query) {
        customService.storeTime(query);
        initOrgIdList(query);
        List<StatisServiceRankingResultVO> rankingResultVOList = posCashMapper.durationRanking(query);
        if (CollUtil.isEmpty(rankingResultVOList)) {
            return Lists.newArrayList();
        }
        Map<Long, StatisServiceRankingResultVO> voMap = rankingResultVOList.stream().collect(Collectors.toMap(StatisServiceRankingResultVO::getEmployeeId, Function.identity()));
        List<Long> employeeIds = rankingResultVOList.stream().map(StatisServiceRankingResultVO::getEmployeeId).distinct().collect(Collectors.toList());
        List<BaseEmployee> employeeList = baseEmployeeService.findList(Wraps.<BaseEmployee>lbQ()
                .in(BaseEmployee::getId, employeeIds)
                .eq(Objects.nonNull(query.getGroupId()), BaseEmployee::getGroupId, query.getGroupId())
                .in(CollUtil.isNotEmpty(query.getGroupIds()), BaseEmployee::getGroupId, query.getGroupIds())
                .orderByAsc(BaseEmployee::getId));
        // 取list
        List<Long> photoIds = employeeList.stream().map(BaseEmployee::getPhotoId).collect(Collectors.toList());
        R<Map<Long, String>> photoR = fileApi.findUrlById(photoIds);
        Map<Long, String> photoRData = new HashMap<>();
        if (photoR.getIsSuccess()) {
            photoRData = photoR.getData();
        }
        List<StatisServiceRankingResultVO> resultVOList = new ArrayList<>();

        for (BaseEmployee employee : employeeList) {
            if (voMap.containsKey(employee.getId())) {
                resultVOList.add(voMap.get(employee.getId()));
            } else {
                resultVOList.add(StatisServiceRankingResultVO.builder()
                        .echoMap(MapUtil.newHashMap())
                        .employeeId(employee.getId())
                        .name(employee.getRealName())
                        .avatarFile(photoRData.get(employee.getPhotoId()))
                        .groupId(employee.getGroupId())
                        .chargingDuration(0)
                        .duration(0)
                        .build());
            }
        }
        if (StringUtils.equals(query.getSort(), "duration")) {
            resultVOList = resultVOList.stream()
                    .sorted(Comparator.comparing(StatisServiceRankingResultVO::getDuration, Comparator.reverseOrder())
                            .thenComparing(StatisServiceRankingResultVO::getEmployeeId))
                    .collect(Collectors.toList());
        }
        if (StringUtils.equals(query.getSort(), "chargingDuration")) {
            resultVOList = resultVOList.stream()
                    .sorted(Comparator.comparing(StatisServiceRankingResultVO::getChargingDuration, Comparator.reverseOrder())
                            .thenComparing(StatisServiceRankingResultVO::getEmployeeId))
                    .collect(Collectors.toList());
        }
        ServiceStaffTimeEnum serviceStaffTimeEnum = baseParameterService.getDefaultServiceStaffTime();
        resultVOList.forEach(v -> {
            v.setDurationDesc(posCashServiceService.serviceDurationDesc(v.getDuration(), serviceStaffTimeEnum));
            v.setChargingDurationDesc(posCashServiceService.serviceDurationDesc(v.getChargingDuration(), serviceStaffTimeEnum));
        });
        echoService.action(resultVOList);
        return resultVOList;
    }


    public void initOrgIdList(OrgIdListQuery params) {
        if (Objects.isNull(params)) {
            params = new OrgIdListQuery();
        }
        if (CollUtil.isEmpty(params.getOrgIdList())) {
            params.setOrgIdList(Collections.singletonList(ContextUtil.getCurrentCompanyId()));
        }
    }

    @Override
    public IPage<StatisPerformanceMemberResultVO> memberRanking(PageParams<PerformanceMemberQuery> params) {
        PerformanceMemberQuery query = params.getModel();
        //时间
        customService.storeTime(query);
        params.setSort("");
        params.setOrder("");
        IPage<StatisPerformanceMemberResultVO> page = params.buildPage(StatisPerformanceMemberResultVO.class);
        QueryWrapper<PosCash> wrapper = new QueryWrapper<>();
        wrapper.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(),
                        PosCashBillStateEnum.PART_REFUND.getCode()))
                .notIn("p.bill_type", Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode(),
                        PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .eq("p.delete_flag", 0)
                .eq("p.org_id", ContextUtil.getCurrentCompanyId())
                .eq("pro.delete_flag", 0)
                .eq("pro.status", '1')
                .eq(ObjectUtil.isNotNull(query.getEmployeeId()), "pro.employee_id", query.getEmployeeId())
                .between("p.complete_time", query.getStartDate(), query.getEndDate())
                .groupBy("p.member_id")
                .orderByDesc("sum( pro.duration )");
        IPage<StatisPerformanceMemberResultVO> resultVOIPage = posCashMapper.selectPageMemberRanking(page, wrapper);
        ServiceStaffTimeEnum serviceStaffTimeEnum = baseParameterService.getDefaultServiceStaffTime();
        resultVOIPage.getRecords().forEach(s -> {
            if (Objects.nonNull(s.getDuration()) && s.getDuration() > 0) {
                s.setDurationHour(posCashServiceService.serviceDurationDesc(s.getDuration(), serviceStaffTimeEnum));
            } else {
                s.setDurationHour("-");
            }
            if (Objects.nonNull(s.getCycleDuration()) && s.getCycleDuration() > 0) {
                s.setCycleDurationHour(posCashServiceService.serviceDurationDesc(s.getCycleDuration(), serviceStaffTimeEnum));
            } else {
                s.setCycleDurationHour("-");
            }
        });
        return resultVOIPage;
    }

    private static void export(HttpServletResponse response, List<StatisServiceResultVO> resultVOS, List<ColumnVO> columnVOList, String fileName) {
        // 1.获取ExcelWriter对象
        ExcelWriter writer = ExcelUtil.getBigWriter();
        // 2.写出表头
        for (ColumnVO columnVO : columnVOList) {
            // 自定义标题别名
            writer.addHeaderAlias(columnVO.getName(), columnVO.getLabel());
        }

        // ...
        // 3.定义表头单元格样式(可选)
        StyleSet style = writer.getStyleSet();
        CellStyle headCellStyle = style.getHeadCellStyle();
        // 自动换行
        headCellStyle.setWrapText(true);
        // 水平居中
        headCellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 上下居中
        headCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置表头字体大小
        Font headFont = writer.createFont();
        headFont.setFontName("宋体");
        // 设置字体大小为15磅
        headFont.setFontHeightInPoints((short) 15);
        headCellStyle.setFont(headFont);
        // 4.定义内容单元格样式(可选)
        CellStyle cellStyle = style.getCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        // 设置字体大小
        Font font = writer.createFont();
        font.setFontName("宋体");
        cellStyle.setFont(font);
        // 5.其他设置(可选)
        // 只写出设置别名的属性
        writer.setOnlyAlias(true);
        // 冻结行
        writer.setFreezePane(1);
        // 6.写入数据 设置列宽
        writer.write(resultVOS);
        writer.autoSizeColumnAll();
        // 7.开启筛选(可选)
        //CellRangeAddress filterRange = CellRangeAddress.valueOf("B1:D1");
        //writer.getSheet().setAutoFilter(filterRange);
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            // 8.导出
            response.setCharacterEncoding(CharsetUtil.UTF_8);
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()) + ".xlsx");
            response.setContentType("application/vnd.ms-excel;" + CharsetUtil.UTF_8);
            writer.flush(outputStream);
//            writer.close();
        } catch (Exception e) {
            log.warn("批量导出出错：{}", e.getMessage());
        } finally {
            writer.close();
        }
    }
}
