package top.kx.kxss.wxapp.service.statistics;


import top.kx.basic.base.request.PageParams;
import top.kx.kxss.base.vo.query.member.card.MemberCardPageQuery;
import top.kx.kxss.wxapp.vo.query.statistics.*;
import top.kx.kxss.wxapp.vo.result.statistics.ProductOutboundDetailsResultVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 报表service
 */
public interface StatisticsService {

    Map<String, Object> serviceDetails(PageParams<ServiceDetailsQuery> params);


    Map<String, Object> serviceDetailsSum(ServiceDetailsQuery params);

    void serviceDetailsExport(ServiceDetailsQuery params, HttpServletResponse response);

    Map<String, Object> serviceStatistics(PageParams<ServiceDetailsQuery> params);

    Map<String, Object> serviceStatisticsSum(ServiceDetailsQuery params);


    void serviceStatisticsExport(ServiceDetailsQuery params, HttpServletResponse response);

    Map<String, Object> serviceStatisticsSummary(PageParams<ServiceDetailsQuery> params);

    Map<String, Object> serviceStatisticsSummarySum(ServiceDetailsQuery params);

    void serviceStatisticsSummaryExport(ServiceDetailsQuery params, HttpServletResponse response);


    Map<String, Object> serviceAmount(PageParams<ServiceAmountQuery> params);

    Map<String, Object> serviceAmountSum(ServiceAmountQuery params);


    void serviceAmountExport(ServiceAmountQuery params, HttpServletResponse response);


    Map<String, Object> paymentDetails(PageParams<PaymentDetailsQuery> params);

    Map<String, Object> paymentDetailsSum(PaymentDetailsQuery params);


    void paymentDetailsExport(PaymentDetailsQuery params, HttpServletResponse response);


    Map<String, Object> memberRechargeDetails(PageParams<MemberDetailsQuery> params);

    Map<String, Object> memberRechargeDetailsSum(MemberDetailsQuery params);


    void memberRechargeDetailsExport(MemberDetailsQuery params, HttpServletResponse response);


    Map<String, Object> memberConsume(PageParams<MemberDetailsQuery> params);

    Map<String, Object> memberConsumeSum(MemberDetailsQuery params);


    void memberConsumeExport(MemberDetailsQuery params, HttpServletResponse response);


    Map<String, Object> memberRecharge(PageParams<MemberDetailsQuery> params);

    Map<String, Object> memberRechargeSum(MemberDetailsQuery params);


    void memberRechargeExport(MemberDetailsQuery params, HttpServletResponse response);

    Map<String, Object> inventoryFlow(PageParams<InventoryFlowQuery> params);

    Map<String, Object> inventoryFlowSum(InventoryFlowQuery params);


    void inventoryFlowExport(InventoryFlowQuery params, HttpServletResponse response);

    Map<String, Object> productOutboundDetails(PageParams<InventoryFlowQuery> params);

    ProductOutboundDetailsResultVO productOutboundDetailsSum(InventoryFlowQuery params);

    List<ProductOutboundDetailsResultVO> productOutboundDetailsExport(InventoryFlowQuery params, HttpServletResponse response);

    Map<String, Object> productInventoryDetails(PageParams<InventoryFlowQuery> params);

    Map<String, Object> productInventoryDetailsSum(InventoryFlowQuery params);

    void productInventoryDetailsExport(InventoryFlowQuery params, HttpServletResponse response);


    Map<String, Object> realTimeInventoryOfGoods(PageParams<RealTimeInventoryOfGoodsQuery> params);

    Map<String, Object> realTimeInventoryOfGoodsSum(RealTimeInventoryOfGoodsQuery params);

    void realTimeInventoryOfGoodsExport(RealTimeInventoryOfGoodsQuery params, HttpServletResponse response);


    Map<String, Object> posCashDetails(PageParams<PosCashDetailsQuery> params);


    Map<String, Object> posCashDetailsSum(PosCashDetailsQuery params);


    void posCashDetailsExport(PosCashDetailsQuery params, HttpServletResponse response);

    Map<String, Object> posCashOrderDetails(PageParams<PosCashDetailsQuery> params);


    Map<String, Object> posCashCommenter(PageParams<PosCashDetailsQuery> params);


    Map<String, Object> posCashCommenterSum(PosCashDetailsQuery params);


    void posCashCommenterExport(PosCashDetailsQuery params, HttpServletResponse response);



    Map<String, Object> posCashDiscountTypeStatistics(PageParams<PosCashDetailsQuery> params);


    Map<String, Object> posCashDiscountTypeStatisticsSum(PosCashDetailsQuery params);

    void posCashDiscountTypeStatisticsExport(PosCashDetailsQuery params, HttpServletResponse response);


    Map<String, Object> posCashDiscountTypeDetails(PageParams<PosCashDetailsQuery> params);


    Map<String, Object> posCashDiscountTypeDetailsSum(PosCashDetailsQuery params);

    void posCashDiscountTypeDetailsExport(PosCashDetailsQuery params, HttpServletResponse response);

    Map<String, Object> posCashDiscountTypeActivity(PageParams<PosCashDetailsQuery> params);


    Map<String, Object> posCashDiscountTypeActivitySum(PosCashDetailsQuery params);

    void posCashDiscountTypeActivityExport(PosCashDetailsQuery params, HttpServletResponse response);



    Map<String, Object> thailDetails(PageParams<ThailDetailsQuery> params);


    void thailDetailsExport(ThailDetailsQuery params, HttpServletResponse response);

    Map<String, Object> memberCardDetails(PageParams<MemberCardPageQuery> params);


    void memberCardDetailsExport(MemberCardPageQuery params, HttpServletResponse response);


    Map<String, Object> productStats(PageParams<ProductStatsQuery> params);


    Map<String, Object> productStatsSum(ProductStatsQuery params);

    void productStatsExport(ProductStatsQuery params, HttpServletResponse response);

}
