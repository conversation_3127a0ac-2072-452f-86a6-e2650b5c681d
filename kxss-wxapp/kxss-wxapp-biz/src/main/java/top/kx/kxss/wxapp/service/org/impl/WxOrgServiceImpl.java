package top.kx.kxss.wxapp.service.org.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.server.HttpServerRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.geo.*;
import org.springframework.data.redis.connection.RedisGeoCommands;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.domain.geo.GeoReference;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.cache.redis2.CacheResult;
import top.kx.basic.cache.redis2.RedisOps;
import top.kx.basic.cache.repository.CachePlusOps;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.model.LocateInfo;
import top.kx.basic.model.cache.CacheHashKey;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.DateUtils;
import top.kx.basic.utils.GEOHash;
import top.kx.kxss.app.service.cash.PosCashServiceService;
import top.kx.kxss.base.entity.access.BaseAccess;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.member.grade.MemberGrade;
import top.kx.kxss.base.entity.store.BaseStore;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.entity.user.BaseEmployeeOrgRel;
import top.kx.kxss.base.service.access.BaseAccessService;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.service.member.grade.MemberGradeService;
import top.kx.kxss.base.service.store.BaseStoreService;
import top.kx.kxss.base.service.user.BaseEmployeeOrgRelService;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.save.member.MemberInfoSaveVO;
import top.kx.kxss.common.api.HelperApi;
import top.kx.kxss.common.cache.wxapp.TenantOrgCacheKeyBuilder;
import top.kx.kxss.common.cache.wxapp.TenantOrgUserCacheKeyBuilder;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.ParameterKey;
import top.kx.kxss.common.constant.RedisConstant;
import top.kx.kxss.file.entity.File;
import top.kx.kxss.file.service.FileService;
import top.kx.kxss.model.enumeration.base.MemberSourceEnum;
import top.kx.kxss.model.enumeration.base.PostionStatusEnum;
import top.kx.kxss.model.enumeration.system.DefTenantOrgDeviceTypeEnum;
import top.kx.kxss.model.enumeration.system.DefTenantStatusEnum;
import top.kx.kxss.model.enumeration.system.QrCodeStatusEnum;
import top.kx.kxss.model.enumeration.system.subscription.FeatureCodeEnum;
import top.kx.kxss.model.enumeration.system.subscription.SubscriptionFeatureTypeEnum;
import top.kx.kxss.oauth.api.WxAppApi;
import top.kx.kxss.oauth.vo.param.ScanQuery;
import top.kx.kxss.oauth.vo.result.LoginResultVO;
import top.kx.kxss.pos.WsPushApi;
import top.kx.kxss.pos.query.StoreInfoQuery;
import top.kx.kxss.pos.query.ws.BindDevicePushQuery;
import top.kx.kxss.system.entity.application.DefTenantApplicationRel;
import top.kx.kxss.system.entity.system.DefApplet;
import top.kx.kxss.system.entity.system.DefClient;
import top.kx.kxss.system.entity.system.DefSnQrCode;
import top.kx.kxss.system.entity.system.DefWxUser;
import top.kx.kxss.system.entity.tenant.*;
import top.kx.kxss.system.enumeration.tenant.ApplicationTypeEnum;
import top.kx.kxss.system.service.application.DefTenantApplicationRelService;
import top.kx.kxss.system.service.subscription.impl.SubscriptionFeatureCheckService;
import top.kx.kxss.system.service.system.DefAppletService;
import top.kx.kxss.system.service.system.DefClientService;
import top.kx.kxss.system.service.system.DefSnQrCodeService;
import top.kx.kxss.system.service.system.DefWxUserService;
import top.kx.kxss.system.service.tenant.*;
import top.kx.kxss.system.vo.result.system.BindDeviceResultVO;
import top.kx.kxss.system.vo.result.system.DefAppletResultVO;
import top.kx.kxss.system.vo.result.system.TenatOrgDeviceResultVO;
import top.kx.kxss.system.vo.result.tenant.DefUserTenantRelResultVO;
import top.kx.kxss.wxapp.service.org.WxOrgService;
import top.kx.kxss.wxapp.vo.query.BindDeviceQuery;
import top.kx.kxss.wxapp.vo.query.org.OrgPageQuery;
import top.kx.kxss.wxapp.vo.query.org.OrgUnBindQuery;
import top.kx.kxss.wxapp.vo.result.org.CommonMemberOrgResultVO;
import top.kx.kxss.wxapp.vo.result.org.CommonOrgResultVO;
import top.kx.kxss.wxapp.vo.result.org.MemberOrgResultVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@DS(DsConstant.BASE_TENANT)
public class WxOrgServiceImpl implements WxOrgService {

    @Autowired
    private BaseAccessService baseAccessService;
    @Autowired
    private BaseStoreService baseStoreService;
    @Autowired
    private BaseEmployeeOrgRelService baseEmployeeOrgRelService;
    @Autowired
    private MemberGradeService memberGradeService;
    @Autowired
    private MemberInfoService memberInfoService;
    @Autowired
    private FileService fileService;
    @Autowired
    private DefAppletService defAppletService;
    @Autowired
    private DefClientService defClientService;
    @Autowired
    private DefTenantService defTenantService;
    @Autowired
    private DefUserService defUserService;
    @Autowired
    private HelperApi helperApi;
    @Autowired
    private DefUserTenantRelService defUserTenantRelService;
    @Autowired
    private DefTenantApplicationRelService defTenantApplicationRelService;
    @Autowired
    private WxAppApi wxAppApi;
    @Autowired
    private RedisOps redisOps;
    @Autowired
    private DefTenantOrgService tenantOrgService;
    @Autowired
    private DefTenantOrgDeviceService tenantOrgDeviceService;
    @Autowired
    private DefSnQrCodeService snQrCodeService;
    @Autowired
    private DefTenantMemberService defTenantMemberService;
    @Autowired
    private DefWxUserService defWxUserService;
    @Autowired
    private WsPushApi wsPushApi;
    @Autowired
    private PosCashServiceService posCashServiceService;
    @Autowired
    private CachePlusOps cachePlusOps;
    @Autowired
    private SubscriptionFeatureCheckService featureCheckService;
    @Autowired
    private BaseEmployeeService baseEmployeeService;

    @Override
    public IPage<CommonOrgResultVO> storePage(PageParams<OrgPageQuery> params) {
        IPage<BaseAccess> page = params.buildPage(BaseAccess.class);
        LbQueryWrap<BaseAccess> wrap = Wraps.lbQ();
        //当前会员的访问记录
        MemberInfo memberInfo = memberInfoService.getByUserId(ContextUtil.getUserId());
        wrap.eq(BaseAccess::getMemberId, memberInfo == null ? -1 : memberInfo.getId());
        wrap.orderByDesc(BaseAccess::getAccessTime);
        IPage<BaseAccess> accessPage = baseAccessService.page(page, wrap);
        //获取门店LOGO信息
        List<Long> orgIds = accessPage.getRecords().stream().map(BaseAccess::getOrgId).collect(Collectors.toList());
        Map<Long, BaseStore> storeMap = CollUtil.isNotEmpty(orgIds) ? baseStoreService.list(Wraps.<BaseStore>lbQ().in(BaseStore::getId, orgIds))
                .stream().collect(Collectors.toMap(BaseStore::getId, k -> k)) : new HashMap<>();
        List<Long> storeLogos = storeMap.values().stream().map(BaseStore::getStoreLogo).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        Map<Long, File> fileMap = CollUtil.isNotEmpty(storeLogos) ? fileService.list(Wraps.<File>lbQ().in(File::getId, storeLogos))
                .stream().collect(Collectors.toMap(File::getId, k -> k)) : new HashMap<>();
        //获取门店LOGO
        List<CommonOrgResultVO> list = accessPage.getRecords().stream().map(record -> {
            //封装门店数据
            BaseStore baseStore = storeMap.get(record.getOrgId());
            CommonOrgResultVO resultVO = CommonOrgResultVO.builder()
                    .orgId(baseStore.getId()).name(baseStore.getShortName())
                    .businessStart(ObjectUtil.isNotNull(baseStore.getBusinessStart()) ? baseStore.getBusinessStart() : LocalTime.MIN)
                    .businessEnd(ObjectUtil.isNotNull(baseStore.getBusinessEnd()) ? baseStore.getBusinessEnd() : LocalTime.MAX)
                    .build();
            //判断是否在营业时间内
            resultVO.setIsBusiness(DateUtils.between(resultVO.getBusinessStart(), resultVO.getBusinessEnd()));
            //封装门店logo
            if (ObjectUtil.isNotNull(baseStore) && ObjectUtil.isNotNull(baseStore.getStoreLogo()) && ObjectUtil.isNotNull(fileMap.get(baseStore.getStoreLogo()))) {
                resultVO.setOrgLogo(fileMap.get(baseStore.getStoreLogo()).getUrl());
            }
            return resultVO;
        }).collect(Collectors.toList());
        IPage<CommonOrgResultVO> resultPage = BeanPlusUtil.toBeanPage(accessPage, CommonOrgResultVO.class);
        resultPage.setRecords(list);
        return resultPage;
    }

    @Override
    public IPage<CommonOrgResultVO> tenantPage(PageParams<OrgPageQuery> params) {
        String clientId = ContextUtil.getClientId();
        ArgumentAssert.notBlank(clientId, "缺少应用请求头参数！");
        DefClient client = defClientService.getById(Wraps.<DefClient>lbQ().eq(DefClient::getClientId, clientId));
        ArgumentAssert.notNull(client, "应用不存在！");
        DefApplet defApplet = defAppletService.getOne(Wraps.<DefApplet>lbQ()
                .eq(DefApplet::getDeleteFlag, 0).eq(DefApplet::getClientId, client.getId()));
        ArgumentAssert.notNull(defApplet, "应用不存在！");
        DefAppletResultVO defAppletResultVO = BeanUtil.copyProperties(defApplet, DefAppletResultVO.class);
        List<DefTenant> tenantList = defTenantService.list(Wraps.<DefTenant>lbQ()
                .eq(DefTenant::getState, true)
                .in(DefTenant::getId, defAppletResultVO.getTenantId()));
        if (CollUtil.isEmpty(tenantList)) {
            return new Page<>(params.getCurrent(), params.getSize());
        }
        Map<String, Distance> distanceMap = getGeo(params);
        List<Long> tenantIds = Lists.newArrayList();
        List<Long> orgIds = Lists.newArrayList();
        if (CollUtil.isNotEmpty(distanceMap)) {
            for (String id : distanceMap.keySet()) {
                String[] s = id.split("_");
                //租户ID
                tenantIds.add(Long.parseLong(s[0]));
                //门店ID
                orgIds.add(Long.parseLong(s[1]));
            }
        }
        params.setSort("");
        params.setOrder("");
        IPage<DefTenantOrg> page = params.buildPage(DefTenantOrg.class);
        OrgPageQuery model = params.getModel();
        LbQueryWrap<DefTenantOrg> wrap = Wraps.lbQ();
        //当前应用下的租户信息
        List<Long> collect = tenantList.stream().map(DefTenant::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            wrap.in(DefTenantOrg::getTenantId, collect);
        }
        wrap.eq(DefTenantOrg::getOrgState, 1);
        wrap.eq(DefTenantOrg::getTenantState, 1);
        wrap.eq(DefTenantOrg::getDeleteFlag, 0);
        wrap.in(CollUtil.isNotEmpty(tenantIds), DefTenantOrg::getTenantId, tenantIds);
        wrap.in(CollUtil.isNotEmpty(orgIds), DefTenantOrg::getOrgId, orgIds);
        wrap.like(StrUtil.isNotBlank(model.getName()), DefTenantOrg::getOrgName, model.getName());
        wrap.orderByAsc(DefTenantOrg::getCreatedTime);
        tenantOrgService.page(page, wrap);
        IPage<CommonOrgResultVO> resultPage = BeanPlusUtil.toBeanPage(page, CommonOrgResultVO.class);
        List<CommonOrgResultVO> commonOrgResultVOList = getCommonOrgResultVOList(page, distanceMap);
        resultPage.setRecords(commonOrgResultVOList);
        return resultPage;
    }

    private List<CommonMemberOrgResultVO> getCommonMemberOrgResultVOList(IPage<DefTenantOrg> tenantPage,
                                                                         Map<String, Distance> distanceMap) {
        //封装门店信息
        List<CommonMemberOrgResultVO> commonOrgResultVOList = tenantPage.getRecords().stream().map(v -> {
            ContextUtil.setTenantBasePoolName(v.getTenantId());
            ContextUtil.setTenantId(v.getTenantId());
            ContextUtil.setCurrentCompanyId(v.getOrgId());
            List<BaseStore> list = baseStoreService.list(Wraps.<BaseStore>lbQ().eq(BaseStore::getId, v.getOrgId()));
            BaseStore baseStore = new BaseStore();
            if (CollUtil.isNotEmpty(list)) {
                baseStore = list.get(0);
            }
            if (ObjectUtil.isNull(baseStore)) {
                baseStore = new BaseStore();
                baseStore.setId(1L);
                baseStore.setStoreLogo(null);
            }
            CommonMemberOrgResultVO resultVO = CommonMemberOrgResultVO.builder().orgLogoId(baseStore.getStoreLogo()).tenantId(v.getTenantId())
                    .orgId(baseStore.getId()).name(baseStore.getShortName()).address(baseStore.getAddress())
                    .businessStart(ObjectUtil.isNotNull(baseStore.getBusinessStart()) ? baseStore.getBusinessStart() : LocalTime.MIN)
                    .businessEnd(ObjectUtil.isNotNull(baseStore.getBusinessEnd()) ? baseStore.getBusinessEnd() : LocalTime.MAX)
                    .longitude(StrUtil.isNotBlank(baseStore.getLongitude()) ? baseStore.getLongitude() : null)
                    .dimension(StrUtil.isNotBlank(baseStore.getDimension()) ? baseStore.getDimension() : null)
                    .contactNum(StrUtil.isNotBlank(baseStore.getContactNum()) ? baseStore.getContactNum() : null)
                    .build();
            //判断是否在营业时间内
            resultVO.setIsBusiness(DateUtils.between(resultVO.getBusinessStart(), resultVO.getBusinessEnd()));
            return resultVO;
        }).collect(Collectors.toList());
        //门店logo
        List<Long> storeLogos = commonOrgResultVOList.stream().map(CommonMemberOrgResultVO::getOrgLogoId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        Map<Long, File> fileMap = CollUtil.isNotEmpty(storeLogos) ? fileService.list(Wraps.<File>lbQ().in(File::getId, storeLogos))
                .stream().collect(Collectors.toMap(File::getId, k -> k)) : new HashMap<>();
        for (CommonMemberOrgResultVO resultVO : commonOrgResultVOList) {
            //封装门店logo
            if (ObjectUtil.isNotNull(resultVO.getOrgLogoId()) && ObjectUtil.isNotNull(fileMap.get(resultVO.getOrgLogoId()))) {
                resultVO.setOrgLogo(fileMap.get(resultVO.getOrgLogoId()).getUrl());
            }
            //获取坐标距离
            Distance distance = distanceMap.get(resultVO.getTenantId() + "_" + resultVO.getOrgId());
            if (distance != null) {
                String abbreviation = distance.getMetric().getAbbreviation();
                resultVO.setDistance(getDistance(distance.getValue(), abbreviation));
            }
        }
        return commonOrgResultVOList;
    }


    private List<CommonOrgResultVO> getCommonOrgResultVOList(IPage<DefTenantOrg> tenantPage,
                                                             Map<String, Distance> distanceMap) {
        //封装门店信息
        List<CommonOrgResultVO> commonOrgResultVOList = tenantPage.getRecords().stream().map(v -> {
            ContextUtil.setTenantBasePoolName(v.getTenantId());
            ContextUtil.setTenantId(v.getTenantId());
            ContextUtil.setCurrentCompanyId(v.getOrgId());
            List<BaseStore> list = baseStoreService.list(Wraps.<BaseStore>lbQ().eq(BaseStore::getId, v.getOrgId()));
            BaseStore baseStore = new BaseStore();
            if (CollUtil.isNotEmpty(list)) {
                baseStore = list.get(0);
            }
            if (ObjectUtil.isNull(baseStore)) {
                baseStore = new BaseStore();
                baseStore.setId(1L);
                baseStore.setStoreLogo(null);
            }
            CommonOrgResultVO resultVO = CommonOrgResultVO.builder().orgLogoId(baseStore.getStoreLogo()).tenantId(v.getTenantId())
                    .orgId(baseStore.getId()).name(baseStore.getShortName()).address(baseStore.getAddress())
                    .businessStart(ObjectUtil.isNotNull(baseStore.getBusinessStart()) ? baseStore.getBusinessStart() : LocalTime.MIN)
                    .businessEnd(ObjectUtil.isNotNull(baseStore.getBusinessEnd()) ? baseStore.getBusinessEnd() : LocalTime.MAX)
                    .longitude(StrUtil.isNotBlank(baseStore.getLongitude()) ? baseStore.getLongitude() : null)
                    .dimension(StrUtil.isNotBlank(baseStore.getDimension()) ? baseStore.getDimension() : null)
                    .contactNum(StrUtil.isNotBlank(baseStore.getContactNum()) ? baseStore.getContactNum() : null)
                    .build();
            //判断是否在营业时间内
            resultVO.setIsBusiness(DateUtils.between(resultVO.getBusinessStart(), resultVO.getBusinessEnd()));
            return resultVO;
        }).collect(Collectors.toList());
        //门店logo
        List<Long> storeLogos = commonOrgResultVOList.stream().map(CommonOrgResultVO::getOrgLogoId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        Map<Long, File> fileMap = CollUtil.isNotEmpty(storeLogos) ? fileService.list(Wraps.<File>lbQ().in(File::getId, storeLogos))
                .stream().collect(Collectors.toMap(File::getId, k -> k)) : new HashMap<>();
        for (CommonOrgResultVO resultVO : commonOrgResultVOList) {
            //封装门店logo
            if (ObjectUtil.isNotNull(resultVO.getOrgLogoId()) && ObjectUtil.isNotNull(fileMap.get(resultVO.getOrgLogoId()))) {
                resultVO.setOrgLogo(fileMap.get(resultVO.getOrgLogoId()).getUrl());
            }
            //获取坐标距离
            Distance distance = distanceMap.get(resultVO.getTenantId() + "_" + resultVO.getOrgId());
            if (distance != null) {
                String abbreviation = distance.getMetric().getAbbreviation();
                resultVO.setDistance(getDistance(distance.getValue(), abbreviation));
            }
        }
        return commonOrgResultVOList;
    }

    //判断距离为m或km
    private String getDistance(double value, String abbreviation) {
        BigDecimal bigDecimal = new BigDecimal(value);
        //比较是否小于1 小于1返回m
        if (bigDecimal.compareTo(BigDecimal.ONE) > 0
                && bigDecimal.compareTo(new BigDecimal(100)) < 0) {
            return "<100m";
        }
        if (bigDecimal.compareTo(BigDecimal.ONE) <= 0) {
            return "<10m";
        }
        return bigDecimal.setScale(1, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString() + abbreviation;
    }

    /**
     * 获取坐标
     */
    private Map<String, Distance> getGeo(PageParams<OrgPageQuery> params) {
        initOrgGeo();
        Map<String, Distance> map = new HashMap<>();
        if (ObjectUtil.isNotNull(params.getModel()) &&
                StrUtil.isNotBlank(params.getModel().getLongitude())
                && StrUtil.isNotBlank(params.getModel().getDimension())) {
            LocateInfo geo = GEOHash.getGeo(params.getModel().getLongitude(), params.getModel().getDimension());
            params.getModel().setLongitude(String.valueOf(geo.getLongitude()));
            params.getModel().setDimension(String.valueOf(geo.getLatitude()));
            StringRedisTemplate stringRedisTemplate = redisOps.getStringRedisTemplate();
            String key = RedisConstant.GEO;
            //2. 计算分页参数
            long form = (params.getCurrent() - 1) * params.getSize();
            long end = params.getCurrent() * params.getSize();
            //查询redis，按照距离排序
            GeoResults<RedisGeoCommands.GeoLocation<String>> results = stringRedisTemplate.opsForGeo().search(
                    key,
                    GeoReference.fromCoordinate(Double.parseDouble(params.getModel().getLongitude()),
                            Double.parseDouble(params.getModel().getDimension())),
                    new Distance(5000, Metrics.KILOMETERS),
                    RedisGeoCommands.GeoSearchCommandArgs.newGeoSearchArgs().includeDistance().limit(end));
            // 解析id
            if (results == null) {
                return map;
            }
            List<GeoResult<RedisGeoCommands.GeoLocation<String>>> content = results.getContent();
            if (content.size() <= form) {
                return map;
            }
            //拿到租户和门店数据
            content.stream().skip(form).forEach(result -> {
                // 获取租户_门店ID
                String id = result.getContent().getName();
                //获取距离
                Distance distance = result.getDistance();
                map.put(id, distance);
            });
        }
        return map;
    }

    public void initOrgGeo() {
        List<DefTenantOrg> tenantOrgList = tenantOrgService.list(Wraps.<DefTenantOrg>lbQ()
                .eq(DefTenantOrg::getOrgState, true)
                .isNotNull(DefTenantOrg::getLongitude)
                .isNotNull(DefTenantOrg::getDimension)
                .eq(DefTenantOrg::getDeleteFlag, 0).eq(DefTenantOrg::getTenantState, true));
        //获取数据
        if (CollUtil.isNotEmpty(tenantOrgList)) {
            StringRedisTemplate stringRedisTemplate = redisOps.getStringRedisTemplate();
            String key = RedisConstant.GEO;
            List<RedisGeoCommands.GeoLocation<String>> locations = com.google.common.collect.Lists.newArrayList();
            for (DefTenantOrg tenantOrg : tenantOrgList) {
                if (StrUtil.isBlank(tenantOrg.getLongitude()) || StrUtil.isBlank(tenantOrg.getDimension())) {
                    continue;
                }
                //写入redis  key 经度 维度
                locations.add(new RedisGeoCommands.GeoLocation<>(
                        tenantOrg.getTenantId() + "_" + tenantOrg.getOrgId(),
                        new Point(Double.parseDouble(tenantOrg.getLongitude()), Double.parseDouble(tenantOrg.getDimension()))));

            }
            stringRedisTemplate.opsForGeo().add(key, locations);
        }
    }

    @Override
    @GlobalTransactional
    public Map<String, Object> select(Long tenantId, Long orgId, Long memberId) {
        Map<String, Object> params = new HashMap<>();
        params.put("orgId", orgId);
        params.put("tenantId", tenantId);
        DefTenant byId = defTenantService.getById(tenantId);
        params.put("isScanShopping", byId != null && byId.getIsScanShopping() != null && byId.getIsScanShopping());
        params.put("memberId", ContextUtil.getMemberId() == null ? "" : ContextUtil.getMemberId());
        if (memberId != null) {
            params.put("memberId", memberId);
        }

        if (ObjectUtil.isNotNull(ContextUtil.getTenantId()) &&
                !ObjectUtil.equal(tenantId, ContextUtil.getTenantId())) {
            Long userId = ContextUtil.getUserId();
            //第一次无token，直接返回门店商户信息
            //第二次登录后 需要验证会员信息和初始化≠租户信息
            if (userId == null) {
                ContextUtil.setTenantBasePoolName(tenantId);
                BaseStore baseStore = baseStoreService.getById(orgId);
                ArgumentAssert.notNull(baseStore, "门店不存在！");
                params.put("orgName", StrUtil.isNotBlank(baseStore.getShortName()) ? baseStore.getShortName() : baseStore.getName());
                return params;
            }
            //header中租户
            ContextUtil.setTenantBasePoolName(ContextUtil.getTenantId());
//            BaseStore baseStore = baseStoreService.getById(orgId);
//            ArgumentAssert.notNull(baseStore, "门店不存在！");
            MemberInfo memberInfo = memberInfoService.getCustmoerMember();
            ArgumentAssert.notNull(memberInfo, "当前门店未识别到会员！");
            //选择的目标租户
            ContextUtil.setTenantBasePoolName(tenantId);
            MemberInfo byUserId = memberInfoService.getOne(Wraps.<MemberInfo>lbQ()
                    .eq(MemberInfo::getMobile, memberInfo.getMobile())
                    .last("limit 1"));
            if (ObjectUtil.isNull(byUserId)) {
                MemberGrade memberGrade = memberGradeService.getOne(Wraps.<MemberGrade>lbQ().eq(MemberGrade::getDeleteFlag, 0)
                        .eq(MemberGrade::getIsScan, 1).last("limit 1"));
                ArgumentAssert.notNull(memberGrade, "该门店暂不支持自主注册会员，请联系门店了解详情");
                MemberInfoSaveVO memberInfoSaveVO = MemberInfoSaveVO.builder()
                        .avatarId(null)
                        .platformOpenId(memberInfo.getPlatformOpenId())
                        .mobile(memberInfo.getMobile()).name(memberInfo.getName())
                        .code(memberInfoService.getMemberCode()).sex(memberInfo.getSex())
                        .userId(userId).rechargeAmount(BigDecimal.ZERO)
                        .giftAmount(BigDecimal.ZERO).gradeId(memberGrade.getId())
                        .createdOrgId(orgId).storeId(orgId)
                        .isLock(Boolean.FALSE).source(MemberSourceEnum.SELF.getCode()).growthValue(0)
                        .consumeAmount(BigDecimal.ZERO).consumeTimes(0).wechatNumber(memberInfo.getOpenId())
                        .openId(memberInfo.getOpenId()).build();
                byUserId = memberInfoService.save(memberInfoSaveVO);
            } else {
                if (ObjectUtil.isNotNull(byUserId)) {
                    byUserId.setPlatformOpenId(memberInfo.getPlatformOpenId());
                    memberInfoService.updateById(byUserId);
                }
            }
            if (ObjectUtil.isNotNull(byUserId)) {
                params.put("memberId", byUserId.getId());
                defTenantMemberService.saveOrUpdateInfo(byUserId,
                        tenantId, orgId);
            }
//            tenantInitService.initData(tenantId, orgId);
        } else {
            ContextUtil.setTenantBasePoolName(tenantId);
            Long userId = ContextUtil.getUserId();
            ArgumentAssert.notNull(userId, "账号异常");
            DefUser defUser = defUserService.getById(userId);
            ArgumentAssert.notNull(userId, "账号异常");
            MemberInfoResultVO memberByPhone = memberInfoService.getMemberByPhoneNoAuth(defUser.getMobile(), null);
            if (memberId != null) {
                memberByPhone = memberInfoService.getMemberInfo(memberId);
            }
            MemberInfo byUserId = null;
            if (ObjectUtil.isNotNull(memberByPhone)) {
                byUserId = BeanUtil.copyProperties(memberByPhone, MemberInfo.class);
            }
            if (ObjectUtil.isNull(memberByPhone)) {
                MemberInfoSaveVO memberInfoSaveVO = MemberInfoSaveVO.builder()
                        .mobile(defUser.getMobile()).name("用户" + PhoneUtil.subAfter(defUser.getMobile()))
                        .code(memberInfoService.getMemberCode()).sex(defUser.getSex())
                        .userId(defUser.getId()).rechargeAmount(BigDecimal.ZERO)
                        .giftAmount(BigDecimal.ZERO).gradeId(1L).createdOrgId(1L).storeId(1L)
                        .isLock(Boolean.FALSE).source(MemberSourceEnum.SELF.getCode()).growthValue(0)
                        .consumeAmount(BigDecimal.ZERO).consumeTimes(0)
                        .platformOpenId(defUser.getPlatformOpenId()).build();
                byUserId = memberInfoService.save(memberInfoSaveVO);
            } else {
                if (ObjectUtil.isNotNull(byUserId)) {
                    byUserId.setPlatformOpenId(defUser.getPlatformOpenId());
                    memberInfoService.updateById(byUserId);
                }
            }
            if (ObjectUtil.isNotNull(byUserId)) {
                params.put("memberId", byUserId.getId());
                defTenantMemberService.saveOrUpdateInfo(byUserId,
                        tenantId, orgId);
            }
        }
        //切换门店 刷新token
        R<LoginResultVO> loginResultVO = wxAppApi.switchTenantAndStore(tenantId, orgId, null);
        ArgumentAssert.isFalse(!loginResultVO.getIsSuccess(), "操作异常");
        params.put("token", loginResultVO.getData().getToken());
        params.put("refreshToken", loginResultVO.getData().getRefreshToken());
        BaseStore baseStore = baseStoreService.getById(orgId);
        params.put("orgName", baseStore != null && StrUtil.isNotBlank(baseStore.getShortName())
                ? baseStore.getShortName() : baseStore != null ? baseStore.getName() : null);
        return params;
    }

    @Override
    public List<CommonOrgResultVO> orgList(HttpServerRequest request) {
        List<DefUserTenantRelResultVO> resultVOList = defUserTenantRelService.listEmployeeByUserId(ContextUtil.getUserId());
        if (CollUtil.isEmpty(resultVOList)) {
            return Lists.newArrayList();
        }
        List<CommonOrgResultVO> list = Lists.newArrayList();
        Map<Long, List<DefUserTenantRelResultVO>> tenantRelMap =
                resultVOList.stream().collect(Collectors.groupingBy(DefUserTenantRelResultVO::getTenantId));
        //租户信息
        List<Long> tenantList = resultVOList.stream().map(DefUserTenantRelResultVO::getTenantId).collect(Collectors.toList());
        Map<Long, List<DefTenantApplicationRel>> applicationMap = defTenantApplicationRelService.list(Wraps.<DefTenantApplicationRel>lbQ()
                        .in(DefTenantApplicationRel::getTenantId, tenantList).inSql(DefTenantApplicationRel::getApplicationId,
                                "select id from def_application where delete_flag = 0 and type =" + ApplicationTypeEnum.APPLET_BOSS.getType()))
                .stream().collect(Collectors.groupingBy(DefTenantApplicationRel::getTenantId));
        CacheHashKey builder = TenantOrgUserCacheKeyBuilder.builder(ContextUtil.getUserId(),
                ContextUtil.getUserId().toString());
        CacheResult<List<String>> objectCacheResult = cachePlusOps.hGet(builder);
        if (!objectCacheResult.isNullVal() && !objectCacheResult.isNull()
                && ObjectUtil.isNotNull(objectCacheResult.getRawValue())) {
            List<String> valueList = objectCacheResult.getRawValue();
            List<CommonOrgResultVO> collect = valueList.stream().map(v -> {
                String[] split = v.split("_");
                List<DefUserTenantRelResultVO> resultVOS = tenantRelMap.get(Long.parseLong(split[0]));
                CacheHashKey hashKey = TenantOrgCacheKeyBuilder.builder(split[0],
                        split[0].concat("_").concat(split[1]));
                CacheResult<CommonOrgResultVO> cacheResult = cachePlusOps.hGet(hashKey);
                if (!cacheResult.isNullVal() && !cacheResult.isNull() && ObjectUtil.isNotNull(cacheResult.getRawValue())) {
                    CommonOrgResultVO rawValue = cacheResult.getRawValue();
                    rawValue.setEmployeeId(CollUtil.isEmpty(resultVOS) ? 0L : resultVOS.get(0).getId());
                    return rawValue;
                }
                return null;
            }).collect(Collectors.toList());
            collect.removeAll(Collections.singletonList(null));
            return collect.stream().sorted(Comparator.comparing(CommonOrgResultVO::getCreatedTime)).collect(Collectors.toList());
        }
        List<String> tenantOrgList = Lists.newArrayList();
        Map<Long, DefTenant> defTenantMap = defTenantService.list(Wraps.<DefTenant>lbQ()
                        .eq(DefTenant::getState, 1)
                        .eq(DefTenant::getDeleteFlag, 0)
                        .in(DefTenant::getId, tenantRelMap.keySet())
                        .eq(DefTenant::getStatus, DefTenantStatusEnum.NORMAL.getCode()))
                .stream().collect(Collectors.toMap(DefTenant::getId, k -> k));
        for (Long tenantId : tenantRelMap.keySet()) {
            if (tenantId == null) {
                continue;
            }
            if (CollUtil.isEmpty(defTenantMap) || !defTenantMap.containsKey(tenantId)) {
                continue;
            }
            List<DefUserTenantRelResultVO> defUserTenantRelList = tenantRelMap.get(tenantId);
            List<DefTenantApplicationRel> applicationRelList = applicationMap.get(tenantId);
            if (CollUtil.isEmpty(applicationRelList)) {
                continue;
            }
            DefTenantApplicationRel defTenantApplicationRel = applicationRelList.get(0);
            List<Long> employeeIds = defUserTenantRelList.stream().map(DefUserTenantRelResultVO::getId).collect(Collectors.toList());
            List<String> collect = employeeIds.stream().map(String::valueOf).collect(Collectors.toList());
            ContextUtil.setTenantBasePoolName(tenantId);
            ContextUtil.setTenantId(tenantId);
            List<BaseEmployee> baseEmployeeList = baseEmployeeService.list(Wraps.<BaseEmployee>lbQ()
                    .eq(BaseEmployee::getDeleteFlag, 0)
                    .eq(BaseEmployee::getState, 1)
                    .eq(BaseEmployee::getPositionStatus, PostionStatusEnum.POSED.getCode())
                    .in(BaseEmployee::getId, employeeIds));
            if (CollUtil.isEmpty(baseEmployeeList)) {
                continue;
            }
            //查询员工所在门店
            List<BaseEmployeeOrgRel> employeeOrgRelList = baseEmployeeOrgRelService.list(Wraps.<BaseEmployeeOrgRel>lbQ()
                    .inSql(BaseEmployeeOrgRel::getEmployeeId, "select id from base_employee where delete_flag = 0 and user_id =" +
                            ContextUtil.getUserId() +
                            " and id in (" + String.join(",", collect) + ")"));
            if (CollUtil.isEmpty(employeeOrgRelList)) {
                continue;
            }
            List<BaseStore> baseStoreList = baseStoreService.list(Wraps.<BaseStore>lbQ().in(BaseStore::getId,
                    employeeOrgRelList.stream().map(BaseEmployeeOrgRel::getOrgId).collect(Collectors.toList())));
            if (CollUtil.isEmpty(baseStoreList)) {
                continue;
            }
            tenantOrgList.addAll(baseStoreList.stream().map(v -> ContextUtil.getTenantId().toString().concat("_" + v.getId()))
                    .collect(Collectors.toList()));
            //整合门店数据
            list.addAll(baseStoreList.stream().map(baseStore -> {
                CommonOrgResultVO resultVO = CommonOrgResultVO.builder().orgLogoId(baseStore.getStoreLogo()).tenantId(defTenantApplicationRel.getTenantId())
                        .orgId(baseStore.getId()).address(baseStore.getAddress())
                        .name(baseStore.getName()).shortName(baseStore.getShortName())
                        .searchName(baseStore.getName().concat("_").concat(baseStore.getShortName()))
                        .applicationId(defTenantApplicationRel.getApplicationId()).employeeId(defUserTenantRelList.get(0).getId())
                        .businessStart(ObjectUtil.isNotNull(baseStore.getBusinessStart()) ? baseStore.getBusinessStart() : LocalTime.MIN)
                        .businessEnd(ObjectUtil.isNotNull(baseStore.getBusinessEnd()) ? baseStore.getBusinessEnd() : LocalTime.MAX)
                        .dimension(StrUtil.isNotBlank(baseStore.getDimension()) ? baseStore.getDimension() : null)
                        .longitude(StrUtil.isNotBlank(baseStore.getLongitude()) ? baseStore.getLongitude() : null)
                        .contactNum(StrUtil.isNotBlank(baseStore.getContactNum()) ? baseStore.getContactNum() : null)
                        .contacts(StrUtil.isNotBlank(baseStore.getContacts()) ? baseStore.getContacts() : null)
                        .createdTime(baseStore.getCreatedTime())
                        .build();
                resultVO.setSn(resultVO.getTenantId() + "_" + resultVO.getOrgId());
                //判断是否在营业时间内
                resultVO.setIsBusiness(DateUtils.between(resultVO.getBusinessStart(), resultVO.getBusinessEnd()));
                CacheHashKey hashKey = TenantOrgCacheKeyBuilder.builder(ContextUtil.getTenantId().toString(),
                        ContextUtil.getTenantId().toString().concat("_").concat(baseStore.getId().toString()));
                cachePlusOps.hSet(hashKey, resultVO);
                return resultVO;
            }).collect(Collectors.toList()));
        }
        CacheHashKey cacheHashKey = TenantOrgUserCacheKeyBuilder.builder(ContextUtil.getUserId(),
                ContextUtil.getUserId().toString());
        CacheResult<List<String>> cacheResult = cachePlusOps.hGet(cacheHashKey);
        List<String> tenantOrgIds = Lists.newArrayList();
        if (!cacheResult.isNullVal() && !cacheResult.isNull()
                && ObjectUtil.isNotNull(cacheResult.getRawValue())) {
            tenantOrgIds = cacheResult.getRawValue();
        }
        if (CollUtil.isNotEmpty(tenantOrgList)) {
            tenantOrgIds.addAll(tenantOrgList);
        }
        cachePlusOps.hSet(cacheHashKey, tenantOrgIds.stream().distinct().collect(Collectors.toList()));
        return list.stream().sorted(Comparator.comparing(CommonOrgResultVO::getCreatedTime)).collect(Collectors.toList());
    }

    @Override
    public IPage<CommonMemberOrgResultVO> tenantMemberPage(PageParams<OrgPageQuery> params) {
        String clientId = ContextUtil.getClientId();
        ArgumentAssert.notBlank(clientId, "缺少应用请求头参数！");
        DefClient client = defClientService.getById(Wraps.<DefClient>lbQ().eq(DefClient::getClientId, clientId));
        ArgumentAssert.notNull(client, "应用不存在！");
        DefApplet defApplet = defAppletService.getOne(Wraps.<DefApplet>lbQ()
                .eq(DefApplet::getDeleteFlag, 0).eq(DefApplet::getClientId, client.getId()));
        ArgumentAssert.notNull(defApplet, "应用不存在！");
        Long userId = ContextUtil.getUserId();
        ArgumentAssert.notNull(userId, "登录异常");
        DefUser defUser = defUserService.getById(userId);
        ArgumentAssert.notNull(defUser, "用户不存在");
        ArgumentAssert.isFalse(!defUser.getState(), "用户已禁用，请联系管理员");
        List<DefTenantMember> memberList = defTenantMemberService.list(Wraps.<DefTenantMember>lbQ()
                .eq(DefTenantMember::getDeleteFlag, 0)
                .eq(DefTenantMember::getMobile, defUser.getMobile()));
        if (CollUtil.isEmpty(memberList)) {
            return new Page<>(params.getCurrent(), params.getSize());
        }
        List<DefTenant> tenantList = defTenantService.list(Wraps.<DefTenant>lbQ()
                .eq(DefTenant::getState, true)
                .in(DefTenant::getId, memberList.stream().map(DefTenantMember::getTenantId).collect(Collectors.toList())));
        if (CollUtil.isEmpty(tenantList)) {
            return new Page<>(params.getCurrent(), params.getSize());
        }
        Map<String, Distance> distanceMap = getGeo(params);
        List<Long> tenantIds = Lists.newArrayList();
        List<Long> orgIds = Lists.newArrayList();
        if (CollUtil.isNotEmpty(distanceMap)) {
            for (String id : distanceMap.keySet()) {
                String[] s = id.split("_");
                //租户ID
                tenantIds.add(Long.parseLong(s[0]));
                //门店ID
                orgIds.add(Long.parseLong(s[1]));
            }
        }
        params.setSort("");
        params.setOrder("");
        IPage<DefTenantOrg> page = params.buildPage(DefTenantOrg.class);
        OrgPageQuery model = params.getModel();
        LbQueryWrap<DefTenantOrg> wrap = Wraps.lbQ();
        //当前应用下的租户信息
        List<Long> collect = tenantList.stream().map(DefTenant::getId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            wrap.in(DefTenantOrg::getTenantId, collect);
        }
        wrap.eq(DefTenantOrg::getOrgState, 1);
        wrap.eq(DefTenantOrg::getTenantState, 1);
        wrap.eq(DefTenantOrg::getDeleteFlag, 0);
        wrap.in(CollUtil.isNotEmpty(tenantIds), DefTenantOrg::getTenantId, tenantIds);
        wrap.in(CollUtil.isNotEmpty(orgIds), DefTenantOrg::getOrgId, orgIds);
        wrap.like(StrUtil.isNotBlank(model.getName()), DefTenantOrg::getOrgName, model.getName());
        wrap.orderByAsc(DefTenantOrg::getCreatedTime);
        tenantOrgService.page(page, wrap);
        IPage<CommonMemberOrgResultVO> resultPage = BeanPlusUtil.toBeanPage(page, CommonMemberOrgResultVO.class);
        List<CommonMemberOrgResultVO> commonOrgResultVOList = getCommonMemberOrgResultVOList(page, distanceMap);
        Map<String, List<DefTenantMember>> stringListMap = memberList.stream().collect
                (Collectors.groupingBy(v -> v.getTenantId().toString().concat(v.getOrgId() + "")));
        for (CommonMemberOrgResultVO commonMemberOrgResultVO : commonOrgResultVOList) {
            List<DefTenantMember> tenantMemberList = stringListMap.get(commonMemberOrgResultVO.getTenantId().toString()
                    .concat(commonMemberOrgResultVO.getOrgId() + ""));
            commonMemberOrgResultVO.setMemberId(tenantMemberList.get(0).getMemberId());
            commonMemberOrgResultVO.setMemberCount(tenantMemberList.size());
        }
        resultPage.setRecords(commonOrgResultVOList);
        return resultPage;
    }

    @Override
    public List<MemberOrgResultVO> query(OrgPageQuery query) {
        String clientId = ContextUtil.getClientId();
        ArgumentAssert.notBlank(clientId, "缺少应用请求头参数！");
        DefClient client = defClientService.getById(Wraps.<DefClient>lbQ().eq(DefClient::getClientId, clientId));
        ArgumentAssert.notNull(client, "应用不存在！");
        DefApplet defApplet = defAppletService.getOne(Wraps.<DefApplet>lbQ()
                .eq(DefApplet::getDeleteFlag, 0).eq(DefApplet::getClientId, client.getId()));
        ArgumentAssert.notNull(defApplet, "应用不存在！");
        Long userId = ContextUtil.getUserId();
        ArgumentAssert.notNull(userId, "登录异常");
        DefUser defUser = defUserService.getById(userId);
        ArgumentAssert.notNull(defUser, "用户不存在");
        ArgumentAssert.isFalse(!defUser.getState(), "用户已禁用，请联系管理员");
        LbQueryWrap<DefTenantMember> wrap = Wraps.lbQ();
        wrap.eq(DefTenantMember::getDeleteFlag, 0);
        wrap.eq(DefTenantMember::getMobile, defUser.getMobile());
        wrap.inSql(DefTenantMember::getTenantId, "select id from def_tenant " +
                " where delete_flag = 0 and state = 1 ");
        wrap.orderByDesc(DefTenantMember::getCreatedTime);
        wrap.last("limit 20");
        List<DefTenantMember> tenantMemberList = defTenantMemberService.list(wrap);
        if (CollUtil.isEmpty(tenantMemberList)) {
            return Lists.newArrayList();
        }
        List<MemberOrgResultVO> resultPage = BeanPlusUtil.toBeanList(tenantMemberList, MemberOrgResultVO.class);
        for (MemberOrgResultVO record : resultPage) {
            DefTenant byId = defTenantService.getById(record.getTenantId());
            record.setIsScanShopping(byId != null && byId.getIsScanShopping() != null && byId.getIsScanShopping());
            ContextUtil.setTenantBasePoolName(record.getTenantId());
            ContextUtil.setTenantId(record.getTenantId());
            ContextUtil.setCurrentCompanyId(record.getOrgId());
            MemberInfoResultVO memberInfo = memberInfoService.getMemberInfo(record.getMemberId());
            if (ObjectUtil.isNotNull(memberInfo)) {
                record.setMemberId(memberInfo.getId());
                record.setMemberName(memberInfo.getName());
                record.setMemberCode(memberInfo.getCode());
                record.setGradeId(memberInfo.getGradeId());
                record.setAccountBalance(memberInfo.getAccountBalance());
                record.setType(memberInfo.getType());
            }
            if (ObjectUtil.isNotNull(record.getGradeId())) {
                MemberGrade memberGrade = memberGradeService.getGradeById(record.getGradeId());
                if (ObjectUtil.isNotNull(memberGrade)) {
                    record.setGradeName(memberGrade.getName());
                }
            }
            if (ObjectUtil.isNotNull(record.getOrgId())) {
                BaseStore baseStore = baseStoreService.getById(record.getOrgId());
                if (ObjectUtil.isNotNull(baseStore)) {
                    record.setOrgName(baseStore.getShortName());
                }
            }
        }
        return resultPage;
    }

    @Override
    public IPage<MemberOrgResultVO> pageList(PageParams<OrgPageQuery> params) {
        String clientId = ContextUtil.getClientId();
        ArgumentAssert.notBlank(clientId, "缺少应用请求头参数！");
        DefClient client = defClientService.getById(Wraps.<DefClient>lbQ().eq(DefClient::getClientId, clientId));
        ArgumentAssert.notNull(client, "应用不存在！");
        DefApplet defApplet = defAppletService.getOne(Wraps.<DefApplet>lbQ()
                .eq(DefApplet::getDeleteFlag, 0).eq(DefApplet::getClientId, client.getId()));
        ArgumentAssert.notNull(defApplet, "应用不存在！");
        Long userId = ContextUtil.getUserId();
        ArgumentAssert.notNull(userId, "登录异常");
        DefUser defUser = defUserService.getById(userId);
        ArgumentAssert.notNull(defUser, "用户不存在");
        ArgumentAssert.isFalse(!defUser.getState(), "用户已禁用，请联系管理员");
        params.setSort("");
        params.setOrder("");
        IPage<DefTenantMember> page = params.buildPage(DefTenantMember.class);
        LbQueryWrap<DefTenantMember> wrap = Wraps.lbQ();
        wrap.eq(DefTenantMember::getDeleteFlag, 0);
        wrap.eq(DefTenantMember::getMobile, defUser.getMobile());
        wrap.inSql(DefTenantMember::getTenantId, "select id from def_tenant " +
                " where delete_flag = 0 and state = 1 ");
        wrap.orderByDesc(DefTenantMember::getCreatedTime);
        defTenantMemberService.page(page, wrap);
        IPage<MemberOrgResultVO> resultPage = BeanPlusUtil.toBeanPage(page, MemberOrgResultVO.class);
        if (CollUtil.isEmpty(resultPage.getRecords())) {
            return new Page<>(params.getCurrent(), params.getSize());
        }
        for (MemberOrgResultVO record : resultPage.getRecords()) {
            DefTenant byId = defTenantService.getById(record.getTenantId());
            record.setIsScanShopping(byId != null && byId.getIsScanShopping() != null && byId.getIsScanShopping());
            ContextUtil.setTenantBasePoolName(record.getTenantId());
            ContextUtil.setTenantId(record.getTenantId());
            ContextUtil.setCurrentCompanyId(record.getOrgId());
            MemberInfoResultVO memberInfo = memberInfoService.getMemberInfo(record.getMemberId());
            if (ObjectUtil.isNotNull(memberInfo)) {
                record.setMemberId(memberInfo.getId());
                record.setMemberName(memberInfo.getName());
                record.setMemberCode(memberInfo.getCode());
                record.setGradeId(memberInfo.getGradeId());
                record.setAccountBalance(memberInfo.getAccountBalance());
                record.setType(memberInfo.getType());
            }
            if (ObjectUtil.isNotNull(record.getGradeId())) {
                MemberGrade memberGrade = memberGradeService.getGradeById(record.getGradeId());
                if (ObjectUtil.isNotNull(memberGrade)) {
                    record.setGradeName(memberGrade.getName());
                }
            }
            if (ObjectUtil.isNotNull(record.getOrgId())) {
                BaseStore baseStore = baseStoreService.getById(record.getOrgId());
                if (ObjectUtil.isNotNull(baseStore)) {
                    record.setOrgName(baseStore.getShortName());
                }
            }
        }
        return resultPage;
    }

    @Override
    public Map<String, Object> memberSelect(Long tenantId, Long orgId, Long memberId) {
        Map<String, Object> params = new HashMap<>();
        params.put("orgId", orgId);
        params.put("tenantId", tenantId);
        //header中租户
        ContextUtil.setTenantBasePoolName(tenantId);
        MemberInfo memberInfo = memberInfoService.getMemberById(memberId);
        ArgumentAssert.notNull(memberInfo, "会员不存在");
        params.put("memberName", memberInfo.getName());
        //切换门店 刷新token
        R<LoginResultVO> loginResultVO = wxAppApi.switchTenantAndStore(tenantId, orgId, null);
        ArgumentAssert.isFalse(!loginResultVO.getIsSuccess(), "操作异常");
        params.put("token", loginResultVO.getData().getToken());
        params.put("refreshToken", loginResultVO.getData().getRefreshToken());
        BaseStore baseStore = baseStoreService.getById(orgId);
        params.put("orgName", baseStore != null && StrUtil.isNotBlank(baseStore.getShortName())
                ? baseStore.getShortName() : baseStore != null ? baseStore.getName() : null);
        return params;
    }

    @Override
    public BindDeviceResultVO getSnInfo(ScanQuery query) {
        DefSnQrCode defSnQrCode = snQrCodeService.getOne(Wraps.<DefSnQrCode>lbQ()
                .eq(DefSnQrCode::getScene, query.getScene()).last("limit 1"));
        ArgumentAssert.notNull(defSnQrCode, "无绑定设备信息");
        defSnQrCode.setStatus(QrCodeStatusEnum.RUNNING.getCode());
        snQrCodeService.updateById(defSnQrCode);
//        Map<String, String> hashMap = MapUtil.newHashMap();
//        hashMap.put("state", "SCANNED");
//        wsPushApi.bindDevicePush(BindDevicePushQuery.builder().sn(defSnQrCode.getSn())
//                .message(JSON.toJSONString(hashMap))
//                .build());
        return JSON.parseObject(defSnQrCode.getParams(), BindDeviceResultVO.class);
    }

    @Override
    @GlobalTransactional
    public Boolean bindSn(BindDeviceQuery query) {
        DefSnQrCode defSnQrCode = snQrCodeService.getOne(Wraps.<DefSnQrCode>lbQ()
                .eq(DefSnQrCode::getScene, query.getScene()).last("limit 1"));
        ArgumentAssert.notNull(defSnQrCode, "无绑定设备信息");
        BindDeviceResultVO vo = JSON.parseObject(defSnQrCode.getParams(), BindDeviceResultVO.class);
        //指定数据源查询门店数据
        DefTenant tenant = defTenantService.getById(ContextUtil.getTenantId());
        ArgumentAssert.notNull(tenant, "商户不存在");
        BaseStore baseStore = baseStoreService.getById(ContextUtil.getCurrentCompanyId());
        ArgumentAssert.notNull(baseStore, "门店不存在");
        ContextUtil.setDefTenantId();
        //推送前端 绑定成功/失败
        boolean save = tenantOrgDeviceService.save(DefTenantOrgDevice.builder()
                .sn(vo.getSn())
                .name(baseStore.getShortName()).tenantId(ContextUtil.getTenantId())
                .orgId(ContextUtil.getCurrentCompanyId())
                .model(vo.getModel())
                .orgState(true).brand(vo.getBrand()).tenantState(true)
                .type(StringUtils.isNotBlank(query.getType()) ? query.getType() : DefTenantOrgDeviceTypeEnum.VERTICAL_SCREEN.getCode())
                .terminalNum(tenantOrgDeviceService.getRandomTerminalNum(null))
                .snName(StringUtils.isNotBlank(query.getType()) ? (DefTenantOrgDeviceTypeEnum.get(query.getType()).getDesc() + String.format("%03d", (int) (Math.random() * 900 + 100))) : (DefTenantOrgDeviceTypeEnum.VERTICAL_SCREEN.getDesc() + String.format("%03d", (int) (Math.random() * 900 + 100))))
                .remarks(query.getRemarks()).build());
        defSnQrCode.setStatus(save ? QrCodeStatusEnum.SUCCESS.getCode()
                : QrCodeStatusEnum.EXPIRED.getCode());
        snQrCodeService.updateById(defSnQrCode);
//        Map<String, String> hashMap = MapUtil.newHashMap();
//        hashMap.put("state", save ? "SUCCESS" : "FAIL");
//        wsPushApi.bindDevicePush(BindDevicePushQuery.builder().sn(vo.getSn())
//                .message(JSON.toJSONString(hashMap))
//                .build());
        return save;
    }

    @Override
    public Boolean cancelBindSn(BindDeviceQuery query) {
        DefSnQrCode defSnQrCode = snQrCodeService.getOne(Wraps.<DefSnQrCode>lbQ()
                .eq(DefSnQrCode::getScene, query.getScene()).last("limit 1"));
        ArgumentAssert.notNull(defSnQrCode, "无绑定设备信息");
//        BindDeviceResultVO vo = JSON.parseObject(defSnQrCode.getParams(), BindDeviceResultVO.class);
        defSnQrCode.setStatus(QrCodeStatusEnum.CANCEL.getCode());
        snQrCodeService.updateById(defSnQrCode);
//        Map<String, String> hashMap = MapUtil.newHashMap();
//        hashMap.put("state", "CANCEL");
//        wsPushApi.bindDevicePush(BindDevicePushQuery.builder().sn(vo.getSn())
//                .message(JSON.toJSONString(hashMap))
//                .build());

        return true;
    }

    @Override
    public List<TenatOrgDeviceResultVO> deviceList() {
        ArgumentAssert.notNull(ContextUtil.getTenantId(), "商户不存在");
        ArgumentAssert.notNull(ContextUtil.getCurrentCompanyId(), "商户不存在");
        List<DefTenantOrgDevice> tenantOrgDeviceList = tenantOrgDeviceService.list(Wraps.<DefTenantOrgDevice>lbQ()
                .eq(DefTenantOrgDevice::getTenantId, ContextUtil.getTenantId())
                .eq(DefTenantOrgDevice::getOrgId, ContextUtil.getCurrentCompanyId())
        );
        if (CollUtil.isEmpty(tenantOrgDeviceList)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(tenantOrgDeviceList, TenatOrgDeviceResultVO.class);
    }

    @Override
    public Boolean wxSn(StoreInfoQuery query) {
        Map<String, String> hashMap = MapUtil.newHashMap();
        hashMap.put("state", "SUCCESS");
        wsPushApi.bindDevicePush(BindDevicePushQuery.builder().sn(query.getSn())
                .message(JSON.toJSONString(hashMap))
                .build());
//        DefSnQrCode defSnQrCode = snQrCodeService.getOne(Wraps.<DefSnQrCode>lbQ()
//                .eq(DefSnQrCode::getScene, query.getScene()).last("limit 1"));
//        defSnQrCode.setStatus(QrCodeStatusEnum.SUCCESS.getCode());
//        snQrCodeService.updateById(defSnQrCode);
        return true;
    }

    @Override
    public Boolean unbind(OrgUnBindQuery query) {
        ContextUtil.setTenantBasePoolName(query.getTenantId());
        ContextUtil.setTenantId(query.getTenantId());
        ContextUtil.setCurrentCompanyId(query.getOrgId());
        ContextUtil.setMemberId(query.getMemberId());
        //判断会员是否在使用中
        List<Long> longs = Collections.singletonList(query.getMemberId());
        Boolean b = posCashServiceService.checkMemberIsUse(longs);
        ArgumentAssert.isFalse(b, "存在未结账订单，请联系门店相关人员核实...");

        defWxUserService.update(Wraps.<DefWxUser>lbU()
                .set(DefWxUser::getDeleteFlag, 1)
                .eq(DefWxUser::getMemberId, query.getMemberId())
                .eq(DefWxUser::getTenantId, query.getTenantId())
                .eq(DefWxUser::getOrgId, query.getOrgId())
                .eq(DefWxUser::getDeleteFlag, 0));
        return defTenantMemberService.update(Wraps.<DefTenantMember>lbU()
                .set(DefTenantMember::getDeleteFlag, 1)
                .eq(DefTenantMember::getMemberId, query.getMemberId())
                .eq(DefTenantMember::getTenantId, query.getTenantId())
                .eq(DefTenantMember::getOrgId, query.getOrgId())
                .eq(DefTenantMember::getDeleteFlag, 0));
    }

    @Override
    public JSONObject configs() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("isOnlinePay", true);
        jsonObject.put("isResTablePay", false);
        Map<String, String> data = helperApi.findParams(Collections.singletonList(ParameterKey.SCAN_SHOPPING_ONLINE_PAY_TYPE)).getData();
        String scanShoppingOnlinePayType;
        if (CollUtil.isNotEmpty(data)) {
            scanShoppingOnlinePayType = data.get(ParameterKey.SCAN_SHOPPING_ONLINE_PAY_TYPE);
            switch (scanShoppingOnlinePayType) {
                case "10":
                    jsonObject.put("isOnlinePay", true);
                    jsonObject.put("isResTablePay", false);
                    break;
                case "20":
                    jsonObject.put("isOnlinePay", false);
                    jsonObject.put("isResTablePay", true);
                    break;
                case "30":
                    jsonObject.put("isOnlinePay", true);
                    jsonObject.put("isResTablePay", true);
                    break;
            }
        }

        return jsonObject;
    }

    @Override
    public JSONObject configsByKeys(List<String> keys) {
        JSONObject jsonObject = new JSONObject();
        if (keys.contains("TENANT_REWARD")) {
            DefTenant defTenant = defTenantService.getById(ContextUtil.getTenantId());
            jsonObject.put("TENANT_REWARD", defTenant != null && defTenant.getIsReward());
        }
        if (keys.contains("IS_POWER")) {
            boolean featurePermission = featureCheckService.hasFeaturePermission(ContextUtil.getTenantId(), new FeatureCodeEnum[]{FeatureCodeEnum.POWER}, new JSONObject(),
                    SubscriptionFeatureTypeEnum.VALUE_ADDED);
            jsonObject.put("IS_POWER", featurePermission);
        }
        return jsonObject;
    }
}
