package top.kx.kxss.wxapp.service.statistics.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.PosCashCommenter;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.service.cash.PosCashCommenterService;
import top.kx.kxss.app.service.cash.payment.PosCashPaymentService;
import top.kx.kxss.app.service.thail.PosCashThailService;
import top.kx.kxss.app.vo.query.thail.PosCashThailAmountQuery;
import top.kx.kxss.app.vo.result.cash.PosCashCommenterResultVO;
import top.kx.kxss.app.vo.result.cash.PosCashResultVO;
import top.kx.kxss.app.vo.result.thail.PosCashThailAmountResultVO;
import top.kx.kxss.base.entity.common.BaseDict;
import top.kx.kxss.base.entity.member.MemberBalanceChange;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.payment.BasePaymentType;
import top.kx.kxss.base.entity.tableArea.BaseTableArea;
import top.kx.kxss.base.service.common.BaseDictService;
import top.kx.kxss.base.service.member.MemberBalanceChangeService;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.service.payment.BasePaymentTypeService;
import top.kx.kxss.base.vo.result.payment.BasePaymentTypeResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.common.easyexcel.TableColsModel;
import top.kx.kxss.model.constant.EchoDictType;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.model.enumeration.base.*;
import top.kx.kxss.model.enumeration.pos.CashMirrorTypeEnum;
import top.kx.kxss.wxapp.service.excel.ExcelReportService;
import top.kx.kxss.wxapp.service.mapper.StatisCashMapper;
import top.kx.kxss.wxapp.service.statistics.CustomService;
import top.kx.kxss.wxapp.service.statistics.StatisPosCashService;
import top.kx.kxss.wxapp.vo.query.statistics.*;
import top.kx.kxss.wxapp.vo.result.cash.StatisCashMirrorResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.ColumnVO;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 订单明细统计 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@DS(DsConstant.BASE_TENANT)
public class StatisPosCashServiceImpl implements StatisPosCashService {


    @Autowired
    private ExcelReportService excelReportService;

    @Autowired
    private BasePaymentTypeService basePaymentTypeService;

    @Autowired
    private PosCashPaymentService posCashPaymentService;
    @Autowired
    private CustomService customService;
    @Autowired
    private BaseDictService baseDictService;
    @Autowired
    private PosCashThailService posCashThailService;
    @Autowired
    private PosCashCommenterService posCashCommenterService;
    @Autowired
    private MemberInfoService memberInfoService;
    @Autowired
    private StatisCashMapper statisCashMapper;
    @Autowired
    private MemberBalanceChangeService memberBalanceChangeService;

    @Autowired
    private EchoService echoService;

    /**
     * 初始化组织ID列表
     */
    private void initOrgIdList(PosCashDetailsQuery params) {
        if (Objects.isNull(params)) {
            params = new PosCashDetailsQuery();
        }
        if (CollUtil.isEmpty(params.getOrgIdList())) {
            params.setOrgIdList(Collections.singletonList(ContextUtil.getCurrentCompanyId()));
        }
    }


    @Override
    public Map<String, Object> posCashDetails(PageParams<PosCashDetailsQuery> params) {

        initOrgIdList(params.getModel());
        params.setSort("");
        params.setOrder("");

        // 所有支付方式
        List<BasePaymentType> basePaymentTypeList = basePaymentTypeService.list(Wraps.<BasePaymentType>lbQ().eq(BasePaymentType::getState, true)
                .eq(BasePaymentType::getDeleteFlag, 0).in(BasePaymentType::getCreatedOrgId, params.getModel().getOrgIdList()));

        List<BasePaymentTypeResultVO> basePaymentTypeResultVOList = BeanUtil.copyToList(basePaymentTypeList, BasePaymentTypeResultVO.class);
        echoService.action(basePaymentTypeResultVOList);

        basePaymentTypeResultVOList.forEach(basePaymentTypeResultVO -> {
            if (basePaymentTypeResultVO.getType() == null) {
                basePaymentTypeResultVO.setType("");
            }
        });

        // 支付方式类型
        Map<String, List<BasePaymentTypeResultVO>> basePaymentTypeResultVOMap = basePaymentTypeResultVOList.stream()
                .collect(Collectors.groupingBy(BasePaymentTypeResultVO::getType, LinkedHashMap::new, Collectors.toList()));

        List<ColumnVO> columnVOList = com.google.common.collect.Lists.newArrayList(        // 设置表头
                ColumnVO.builder().name("sourceCode").label("单号")
                        .width(230).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("mirrorType").label("收退类型")
                        .width(80).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("billDate").label("单据日期")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createTime").label("收款时间")
                        .width(160).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type").label("业务类型")
                        .width(80).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("code").label("订单号")
                        .width(230).emptyString("-").fixed(false).build()
        );
        ColumnVO paymentDetail = ColumnVO.builder().name("paymentDetail").label("收款情况(应收=收款金额+余额支付+储值卡支付)")
                .width(180).emptyString("-").fixed(false).build();
        paymentDetail.setChild(Lists.newArrayList(ColumnVO.builder().name("mirrorPayment").label("应收")
                        .width(120).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("paid").label("收款金额")
                        .width(120).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("mirrorRechargeAmount").label("余额支付(本金)")
                        .width(120).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("mirrorGiftAmount").label("余额支付(赠金)")
                        .width(120).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("storedCardAmount").label("储值卡支付")
                        .width(120).emptyString("-").fixed(false).build()));
        columnVOList.add(paymentDetail);
        ColumnVO orderPaymentDetail = ColumnVO.builder().name("orderPaymentDetail").label("订单收入构成")
                .width(180).emptyString("-").fixed(false).build();
        orderPaymentDetail.setChild(Lists.newArrayList(ColumnVO.builder().name("tableAmount").label("台桌金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("productAmount").label("商品金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceAmount").label("服务金额")
                        .width(100).emptyString("-").fixed(false).build(),
                // 店内套餐/团购套餐
                ColumnVO.builder().name("thailAmount").label("店内套餐")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("groupBuyAmount").label("团购套餐")
                        .width(100).emptyString("-").fixed(false).build(),
                // 充电
                ColumnVO.builder().name("powerAmount").label("充电")
                        .width(100).emptyString("-").fixed(false).build()));
        columnVOList.add(orderPaymentDetail);

        ColumnVO incomeDetail = ColumnVO.builder().name("orderPaymentDetail").label("收款渠道(所有渠道总和等于收款金额)")
                .width(180).emptyString("-").fixed(false).build();
        List<ColumnVO> incomeDetailChild = new ArrayList<>();
        for (String volumeId : basePaymentTypeResultVOMap.keySet()) {
            List<BasePaymentTypeResultVO> typeResultVOS = basePaymentTypeResultVOMap.get(volumeId);
            for (BasePaymentTypeResultVO typeResultVO : typeResultVOS) {
                if (StringUtils.equals(typeResultVO.getBizType(), PaymentBizTypeEnum.ACCOUNT.getCode()) || StringUtils.equals(typeResultVO.getBizType(), PaymentBizTypeEnum.STORED.getCode())) {
                    continue;
                }
                incomeDetailChild.add(ColumnVO.builder().name("payment_" + typeResultVO.getId()).label(typeResultVO.getName())
                        .width(180).emptyString("-").fixed(false).build());
            }
        }
        incomeDetail.setChild(incomeDetailChild);
        columnVOList.add(incomeDetail);

        List<ColumnVO> childrenVO = com.google.common.collect.Lists.newArrayList(
                ColumnVO.builder().name("tableName").label("台桌名称")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableType").label("台桌类型")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableArea").label("台桌区域")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orderSource").label("订单来源")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("memberId").label("会员名称")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("refundAmount").label("订单退款金额")
                        .width(150).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("rechargeAmount").label("充值本金")
                        .width(150).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("giftAmount").label("充值赠金")
                        .width(150).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(200).emptyString("-").fixed(false).build());
        columnVOList.addAll(childrenVO);
        PosCashDetailsQuery model = params.getModel();
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(model);
        }

        if (StringUtils.isNotBlank(model.getCompleteTime_st()) && StringUtils.isBlank(model.getCompleteTime_ed())) {
            model.setCompleteTime_ed(DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
        }
        QueryWrap<PosCash> initWarp = initWarp(params.getModel());
        initWarp.orderByAsc("pcm.created_time");
        IPage<StatisCashMirrorResultVO> pageResultVO = statisCashMapper.findPageResultVO(params.buildPage(StatisCashMirrorResultVO.class), initWarp);
        echoService.action(pageResultVO);
        Map<Long, PosCashThailAmountResultVO> thailAmountResultVOMap = new HashMap<>();
        // 查询团购套餐和店内套餐
        if (CollUtil.isNotEmpty(pageResultVO.getRecords())) {
            PosCashThailAmountQuery query = PosCashThailAmountQuery.builder()
                    .cashIds(pageResultVO.getRecords().stream().map(PosCashResultVO::getId).distinct().collect(Collectors.toList()))
                    .build();
            List<PosCashThailAmountResultVO> thailAmountResultVOList = posCashThailService.thailAmountList(query);
            if (CollUtil.isNotEmpty(thailAmountResultVOList)) {
                thailAmountResultVOMap = thailAmountResultVOList.stream().collect(Collectors.toMap(PosCashThailAmountResultVO::getCashId, Function.identity()));
            }
        }


        IPage<Map> pageList = BeanPlusUtil.toBeanPage(pageResultVO, Map.class);
        List<Long> cashIds = pageResultVO.getRecords().stream().map(StatisCashMirrorResultVO::getCashId)
                .collect(Collectors.toList());
        // 所有支付方式
        List<PosCashPayment> cashPaymentList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(cashIds)) {
            cashPaymentList = posCashPaymentService.list(Wraps.<PosCashPayment>lbQ()
                    .eq(PosCashPayment::getDeleteFlag, 0)
                    .eq(PosCashPayment::getStatus, PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                    .in(PosCashPayment::getCashId, cashIds));
        }
        Map<Long, BaseTableArea> baseTableAreaMap = new HashMap<>();
        Map<String, BaseDict> baseDictMap = baseDictService.list(Wraps.<BaseDict>lbQ()
                        .eq(BaseDict::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                        .eq(BaseDict::getDeleteFlag, 0).eq(BaseDict::getParentKey,
                                EchoDictType.Base.BASE_TABLE_TYPE))
                .stream().collect(Collectors.toMap(BaseDict::getKey, Function.identity()));
        // 会员信息
        Map<Long, MemberInfo> memberInfoMap = new HashMap<>();
        List<Long> memberIdList = pageResultVO.getRecords().stream().map(PosCashResultVO::getMemberId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(memberIdList)) {
            List<MemberInfo> memberInfoList = memberInfoService.list(Wraps.<MemberInfo>lbQ().in(MemberInfo::getId, memberIdList));
            memberInfoMap = memberInfoList.stream().collect(Collectors.toMap(MemberInfo::getId, Function.identity()));
        }
        List<Map> resultMapList = new ArrayList<>();
        for (StatisCashMirrorResultVO posCashResultVO : pageResultVO.getRecords()) {
            if (posCashResultVO.getRefundAmount() == null) {
                posCashResultVO.setRefundAmount(BigDecimal.ZERO);
            }
            if (posCashResultVO.getPaid() == null) {
                posCashResultVO.setPaid(BigDecimal.ZERO);
            }

            Map<String, Object> tableInfoMap = new HashMap();
            tableInfoMap.put("cashId", posCashResultVO.getCashId());
            tableInfoMap.put("code", posCashResultVO.getCode());
            tableInfoMap.put("mirrorType", CashMirrorTypeEnum.get(posCashResultVO.getMirrorType()).getDesc());
            tableInfoMap.put("sourceCode", posCashResultVO.getMirrorCode());
            tableInfoMap.put("billDate", posCashResultVO.getMirrorBillDate());
            tableInfoMap.put("type", posCashResultVO.getEchoMap().get("type"));
            tableInfoMap.put("tableName", posCashResultVO.getTableName());
            tableInfoMap.put("tableType", StrUtil.isNotBlank(posCashResultVO.getTableType())
                    && CollUtil.isNotEmpty(baseDictMap)
                    && ObjectUtil.isNotNull(baseDictMap.get(posCashResultVO.getTableType())) ? baseDictMap.get(posCashResultVO.getTableType()).getName() : "");
            tableInfoMap.put("tableArea", ObjectUtil.isNotNull(posCashResultVO.getTableArea())
                    && CollUtil.isNotEmpty(baseTableAreaMap)
                    && ObjectUtil.isNotNull(baseTableAreaMap.get(posCashResultVO.getTableArea())) ? baseTableAreaMap.get(posCashResultVO.getTableArea()).getName() : "");
            tableInfoMap.put("createdEmp", posCashResultVO.getEchoMap().get("createdEmp"));
            tableInfoMap.put("completeEmp", posCashResultVO.getEchoMap().get("completeEmp"));
            tableInfoMap.put("employeeEmp", posCashResultVO.getEchoMap().get("employeeId"));

            tableInfoMap.put("orderSource", posCashResultVO.getEchoMap().get("orderSource"));
            MemberInfo memberInfo = memberInfoMap.get(posCashResultVO.getMemberId());
            String memberName = "-";
            if (Objects.nonNull(memberInfo)) {
                memberName = memberInfo.getName() + "(" + StrUtil.subSuf(memberInfo.getMobile(), memberInfo.getMobile().length() - 4) + ")";
            }
            tableInfoMap.put("memberId", memberName);
            tableInfoMap.put("createTime", posCashResultVO.getCreatedTime());
            tableInfoMap.put("completeTime", posCashResultVO.getCompleteTime());
            tableInfoMap.put("mirrorPayment", posCashResultVO.getMirrorPayment());
            tableInfoMap.put("mirrorRechargeAmount", posCashResultVO.getMirrorRechargeAmount());
            tableInfoMap.put("mirrorGiftAmount", posCashResultVO.getMirrorGiftAmount());
            tableInfoMap.put("storedCardAmount", posCashResultVO.getStoredCardAmount());
            if (StringUtils.equals(posCashResultVO.getMirrorType(), CashMirrorTypeEnum.ORDER_PAY.getCode())) {
                tableInfoMap.put("tableAmount", posCashResultVO.getTableAmount());
                tableInfoMap.put("productAmount", posCashResultVO.getProductAmount());
                tableInfoMap.put("serviceAmount", posCashResultVO.getServiceAmount());
                PosCashThailAmountResultVO posCashThailAmountResultVO = thailAmountResultVOMap.get(posCashResultVO.getId());
                if (ObjectUtil.isNotNull(posCashThailAmountResultVO)) {
                    tableInfoMap.put("groupBuyAmount", posCashThailAmountResultVO.getGroupBuyAmount());
                    tableInfoMap.put("thailAmount", posCashThailAmountResultVO.getThailNum());
                } else {
                    tableInfoMap.put("groupBuyAmount", BigDecimal.ZERO);
                    tableInfoMap.put("thailAmount", BigDecimal.ZERO);
                }
                tableInfoMap.put("powerAmount", posCashResultVO.getPowerAmount());
                tableInfoMap.put("amount", posCashResultVO.getMirrorAmount());
                tableInfoMap.put("discountAmount", posCashResultVO.getDiscountAmount());
            }
            switch (CashMirrorTypeEnum.get(posCashResultVO.getMirrorType())) {
                case ORDER_PAY:
                    tableInfoMap.put("refundAmount", posCashResultVO.getRefundAmount());
                    break;
                case RECHARGE_PAY:
                    tableInfoMap.put("refundAmount", posCashResultVO.getRefundAmount());
                    tableInfoMap.put("rechargeAmount", posCashResultVO.getPayment());
                    tableInfoMap.put("giftAmount", posCashResultVO.getGiftAmount());
                    break;
                case RECHARGE_REFUND:
                    tableInfoMap.put("rechargeAmount", BigDecimal.ZERO.subtract(posCashResultVO.getPayment()));
                    if (Objects.nonNull(posCashResultVO.getGiftAmount())) {
                        tableInfoMap.put("giftAmount", BigDecimal.ZERO.subtract(posCashResultVO.getGiftAmount()));
                    }
                    break;
                default:
                    break;
            }

            tableInfoMap.put("paid", posCashResultVO.getMirrorAmount());

            //tableInfoMap.put("payment", posCashResultVO.getPayment());
            tableInfoMap.put("orderRemarks", posCashResultVO.getOrderRemarks());
            tableInfoMap.put("registrationRemarks", posCashResultVO.getRegistrationRemarks());
            tableInfoMap.put("org", posCashResultVO.getEchoMap().get("orgId"));

            if (StringUtils.equals(posCashResultVO.getMirrorType(), CashMirrorTypeEnum.ORDER_PAY.getCode())) {
                // 所有支付方式,
                for (BasePaymentType basePaymentTypeResultVO : basePaymentTypeList) {
                    List<PosCashPayment> posCashPayments = cashPaymentList.stream().filter(c ->
                                    Objects.equals(basePaymentTypeResultVO.getId(), c.getPayTypeId())
                                            && Objects.equals(posCashResultVO.getCashId(), c.getCashId()) && Objects.nonNull(c.getAmount()))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(posCashPayments)) {
                        if (StringUtils.equals(posCashResultVO.getBillState(), PosCashBillStateEnum.REFUNDED.getCode())) {
                            tableInfoMap.put("payment_" + basePaymentTypeResultVO.getId(),
                                    posCashPayments.stream().filter(c -> Objects.nonNull(c.getAmount())).map(PosCashPayment::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                                            .subtract(posCashPayments.stream().filter(c -> Objects.nonNull(c.getChangeAmount())).map(PosCashPayment::getChangeAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
                        } else {
                            tableInfoMap.put("payment_" + basePaymentTypeResultVO.getId(),
                                    posCashPayments.stream().filter(c -> Objects.nonNull(c.getAmount())).map(PosCashPayment::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                                            .subtract(posCashPayments.stream().filter(c -> Objects.nonNull(c.getRefundAmount())).map(PosCashPayment::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                                            .subtract(posCashPayments.stream().filter(c -> Objects.nonNull(c.getChangeAmount())).map(PosCashPayment::getChangeAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
                        }
                    } else {
                        tableInfoMap.put("payment_" + basePaymentTypeResultVO.getId(), 0.00);
                    }
                }
            } else {
                for (BasePaymentType basePaymentTypeResultVO : basePaymentTypeList) {
                    tableInfoMap.put("payment_" + basePaymentTypeResultVO.getId(), 0.00);
                }
            }
            resultMapList.add(tableInfoMap);
        }
        pageList.setRecords(resultMapList);
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }


    private QueryWrap<PosCash> initWarp(PosCashDetailsQuery model) {
        QueryWrap<PosCash> wrap = new QueryWrap<>();
        wrap.eq("pcm.delete_flag", 0)
                .eq("p.delete_flag", 0)
//                .in("p.bill_type", Arrays.asList('0', '3', '4'))
//                .in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode()
//                        , PosCashBillStateEnum.PART_REFUND.getCode(), PosCashBillStateEnum.REFUNDED.getCode()))
                .like(StringUtils.isNotBlank(model.getCode()), "p.code", model.getCode())
                .like(StringUtils.isNotBlank(model.getMirrorCode()), "pcm.code", model.getMirrorCode())
                .eq(StringUtils.isNotBlank(model.getMirrorType()), "pcm.type_", model.getMirrorType())
                .between(StringUtils.isNotBlank(model.getCompleteTime_st())
                        && StringUtils.isNotBlank(model.getCompleteTime_ed()), "p.complete_time", model.getCompleteTime_st(), model.getCompleteTime_ed())
                .eq(Objects.nonNull(model.getEmployeeId()), "p.employee_id", model.getEmployeeId())
                .between(StringUtils.isNotBlank(model.getStartBillDate()) && StringUtils.isNotBlank(model.getEndBillDate()), "pcm.bill_date", model.getStartBillDate(), model.getEndBillDate())
                .and(StringUtils.isNotBlank(model.getMemberName()), e -> e.like("m.name", model.getMemberName())
                        .or()
                        .like("m.mobile", model.getMemberName())
                        .or()
                        .like("m.code", model.getMemberName()))
                .inSql(StringUtils.isNotBlank(model.getOrderSource()), "p.order_source", model.getOrderSource())
                .in(CollUtil.isNotEmpty(model.getOrgIdList()), "pcm.created_org_id", model.getOrgIdList())
                .eq(StringUtils.isNotBlank(model.getTableArea()), "t.table_area", model.getTableArea())
                .like(StringUtils.isNotBlank(model.getTableName()), "p.table_name", model.getTableName())
                .inSql(StringUtils.isNotBlank(model.getTableType()), "t.table_type", model.getTableArea())
                .inSql(StringUtils.isNotBlank(model.getType()), "p.type_", model.getType())

        ;
        return wrap;
    }

    @Override
    public Map<String, Object> posCashDetailsSum(PosCashDetailsQuery params) {
        initOrgIdList(params);

        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        if (StringUtils.isNotBlank(params.getCompleteTime_st()) && StringUtils.isBlank(params.getCompleteTime_ed())) {
            params.setCompleteTime_ed(DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
        }
        StatisCashMirrorResultVO posCashResultVOS = statisCashMapper.findMirrorResultVO(initWarp(params));
        Map<String, Object> resultMap = new HashMap<>();
        if (Objects.isNull(posCashResultVOS)) {
            return resultMap;
        }
        resultMap.put("mirrorPayment", posCashResultVOS.getMirrorPayment());
        resultMap.put("mirrorRechargeAmount", posCashResultVOS.getMirrorRechargeAmount());
        resultMap.put("mirrorGiftAmount", posCashResultVOS.getMirrorGiftAmount());
        resultMap.put("storedCardAmount", posCashResultVOS.getStoredCardAmount());
        resultMap.put("paid", posCashResultVOS.getMirrorAmount());
        return resultMap;
    }

    @Override
    public void posCashDetailsExport(PosCashDetailsQuery params, HttpServletResponse response) {
        long startTime = System.currentTimeMillis();
        initOrgIdList(params);

        // 所有支付方式
        List<BasePaymentType> basePaymentTypeList = basePaymentTypeService.list(Wraps.<BasePaymentType>lbQ().eq(BasePaymentType::getState, true)
                .eq(BasePaymentType::getDeleteFlag, 0).eq(BasePaymentType::getCreatedOrgId, ContextUtil.getCurrentCompanyId()));

        List<BasePaymentTypeResultVO> basePaymentTypeResultVOList = BeanUtil.copyToList(basePaymentTypeList, BasePaymentTypeResultVO.class);
        echoService.action(basePaymentTypeResultVOList);

        basePaymentTypeResultVOList.forEach(basePaymentTypeResultVO -> {
            if (StringUtils.isBlank(basePaymentTypeResultVO.getType())) {
                basePaymentTypeResultVO.setType("未设置");
            }
        });

        // 支付方式类型
        Map<String, List<BasePaymentTypeResultVO>> basePaymentTypeResultVOMap = basePaymentTypeResultVOList.stream()
                .collect(Collectors.groupingBy(BasePaymentTypeResultVO::getType, LinkedHashMap::new, Collectors.toList()));

        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        if (StringUtils.isNotBlank(params.getCompleteTime_st()) && StringUtils.isBlank(params.getCompleteTime_ed())) {
            params.setCompleteTime_ed(DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
        }
        QueryWrap<PosCash> initWarp = initWarp(params);
        initWarp.orderByAsc("pcm.created_time");
        List<StatisCashMirrorResultVO> posCashResultVOS = statisCashMapper.findAllResultVO(initWarp);
        echoService.action(posCashResultVOS);
        Map<Long, PosCashThailAmountResultVO> thailAmountMap = new HashMap<>();
        if (CollUtil.isNotEmpty(posCashResultVOS)) {
            List<PosCashThailAmountResultVO> thailAmountList = posCashThailService.thailAmountList(params);
            thailAmountMap = thailAmountList.stream().collect(Collectors.toMap(PosCashThailAmountResultVO::getCashId, v -> v));
        }


        Map<Long, PosCashThailAmountResultVO> finalThailAmountMap = thailAmountMap;
        posCashResultVOS.forEach(v -> {
            PosCashThailAmountResultVO thailAmountResultVO = finalThailAmountMap.get(v.getId());
            if (ObjectUtil.isNotNull(thailAmountResultVO)) {
                v.setThailAmount(thailAmountResultVO.getThailAmount());
                v.setGroupBuyAmount(thailAmountResultVO.getGroupBuyAmount());
            } else {
                v.setThailAmount(BigDecimal.ZERO);
                v.setGroupBuyAmount(BigDecimal.ZERO);
            }
        });

        List<Long> cashIds = posCashResultVOS.stream().map(StatisCashMirrorResultVO::getCashId).distinct()
                .collect(Collectors.toList());


        // 查询整单提成人
        List<PosCashCommenter> cashCommenterList = posCashCommenterService.list(Wraps.<PosCashCommenter>lbQ().eq(PosCashCommenter::getDeleteFlag, 0)
                .eq(PosCashCommenter::getType, PosCashConstant.Event.ORDER.getCode()).in(PosCashCommenter::getCashId, cashIds));
        List<PosCashCommenterResultVO> cashCommenterListResultVO = BeanPlusUtil.toBeanList(cashCommenterList, PosCashCommenterResultVO.class);
        echoService.action(cashCommenterListResultVO);

        Map<Long, List<PosCashCommenterResultVO>> cashCommenterListResultVOMap = cashCommenterListResultVO.stream().collect(Collectors.groupingBy(PosCashCommenterResultVO::getCashId));


        // 所有支付方式
        // 所有支付方式
        List<PosCashPayment> cashPaymentList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(cashIds)) {
            cashPaymentList = posCashPaymentService.list(Wraps.<PosCashPayment>lbQ()
                    .eq(PosCashPayment::getDeleteFlag, 0)
                    .eq(PosCashPayment::getStatus, PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                    .in(PosCashPayment::getCashId, cashIds.stream().distinct().collect(Collectors.toList())));
        }
       Map<Long, BaseTableArea> baseTableAreaMap = new HashMap<>();
       Map<String, BaseDict> baseDictMap = baseDictService.list(Wraps.<BaseDict>lbQ()
                        .eq(BaseDict::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                        .eq(BaseDict::getDeleteFlag, 0).eq(BaseDict::getParentKey,
                                EchoDictType.Base.BASE_TABLE_TYPE))
                .stream().collect(Collectors.toMap(BaseDict::getKey, Function.identity()));

        // 会员信息
        Map<Long, MemberInfo> memberInfoMap = new HashMap<>();
        List<Long> memberIdList = posCashResultVOS.stream().map(PosCashResultVO::getMemberId)
                .filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(memberIdList)) {
            List<MemberInfo> memberInfoList = memberInfoService.list(Wraps.<MemberInfo>lbQ()
                    .in(MemberInfo::getId, memberIdList.stream().distinct().collect(Collectors.toList()))
                    .orderByDesc(MemberInfo::getCreatedTime));
            memberInfoMap = memberInfoList.stream().collect(Collectors.toMap(MemberInfo::getId, Function.identity()));
        }
        log.info("耗时：{}ms", (System.currentTimeMillis() - startTime));
        excelReportService.excelExport(cashDatailsTableColsList(basePaymentTypeResultVOMap),
                cashDatailsList(basePaymentTypeResultVOMap, basePaymentTypeResultVOList, posCashResultVOS, baseTableAreaMap, baseDictMap,
                        cashPaymentList, cashCommenterListResultVOMap, memberInfoMap, params)
                , response);
    }


    public List<TableColsModel> cashDatailsTableColsList(Map<String, List<BasePaymentTypeResultVO>> basePaymentTypeMap) {
        List<TableColsModel> tabCols = new ArrayList<>();
        tabCols.add(TableColsModel.builder().columnField("sourceCode").columnName("单号").build());
        tabCols.add(TableColsModel.builder().columnField("mirrorType").columnName("收退类型").build());
        tabCols.add(TableColsModel.builder().columnField("billDate").columnName("单据日期").build());
        tabCols.add(TableColsModel.builder().columnField("createTime").columnName("收款时间").build());
        tabCols.add(TableColsModel.builder().columnField("type").columnName("业务类型").build());
        tabCols.add(TableColsModel.builder().columnField("code").columnName("订单号").build());

        tabCols.add(TableColsModel.builder().columnField("mirrorPayment").columnName("应收")
                .columnParent("收款情况(应收=收款金额+余额支付+储值卡支付)").build());
        tabCols.add(TableColsModel.builder().columnField("paid").columnName("收款金额")
                .columnParent("收款情况(应收=收款金额+余额支付+储值卡支付)").build());
        tabCols.add(TableColsModel.builder().columnField("mirrorRechargeAmount").columnName("余额支付(本金)")
                .columnParent("收款情况(应收=收款金额+余额支付+储值卡支付)").build());
        tabCols.add(TableColsModel.builder().columnField("mirrorGiftAmount").columnName("余额支付(赠金)")
                .columnParent("收款情况(应收=收款金额+余额支付+储值卡支付)").build());
        tabCols.add(TableColsModel.builder().columnField("storedCardAmount").columnName("储值卡支付")
                .columnParent("收款情况(应收=收款金额+余额支付+储值卡支付)").build());

        tabCols.add(TableColsModel.builder().columnField("tableAmount").columnName("台桌金额")
                .columnParent("订单收入构成").build());
        tabCols.add(TableColsModel.builder().columnField("productAmount").columnName("商品金额")
                .columnParent("订单收入构成").build());
        tabCols.add(TableColsModel.builder().columnField("serviceAmount").columnName("服务金额")
                .columnParent("订单收入构成").build());
        tabCols.add(TableColsModel.builder().columnField("thailAmount").columnName("店内套餐")
                .columnParent("订单收入构成").build());
        tabCols.add(TableColsModel.builder().columnField("groupBuyAmount").columnName("团购套餐")
                .columnParent("订单收入构成").build());
        tabCols.add(TableColsModel.builder().columnField("powerAmount").columnName("充电")
                .columnParent("订单收入构成").build());

        for (String type : basePaymentTypeMap.keySet()) {
            List<BasePaymentTypeResultVO> basePaymentTypeResultVOS = basePaymentTypeMap.get(type);
            for (BasePaymentTypeResultVO basePaymentTypeResultVO : basePaymentTypeResultVOS) {
                if (StringUtils.equals(basePaymentTypeResultVO.getBizType(), PaymentBizTypeEnum.ACCOUNT.getCode()) || StringUtils.equals(basePaymentTypeResultVO.getBizType(), PaymentBizTypeEnum.STORED.getCode())) {
                    continue;
                }
                TableColsModel build = TableColsModel.builder().columnField("payment_" + basePaymentTypeResultVO.getId())
                        .columnName(basePaymentTypeResultVO.getName())
                        .columnParent("收款渠道(所有渠道总和等于收款金额)").build();
                tabCols.add(build);
            }
        }

        tabCols.add(TableColsModel.builder().columnField("tableName").columnName("台桌名称").build());
        tabCols.add(TableColsModel.builder().columnField("tableType").columnName("台桌类型").build());
        tabCols.add(TableColsModel.builder().columnField("tableArea").columnName("台桌区域").build());
        tabCols.add(TableColsModel.builder().columnField("orderSource").columnName("订单来源").build());
        tabCols.add(TableColsModel.builder().columnField("memberId").columnName("会员名称").build());
        tabCols.add(TableColsModel.builder().columnField("refundAmount").columnName("订单退款金额").build());
        tabCols.add(TableColsModel.builder().columnField("rechargeAmount").columnName("充值本金").build());
        tabCols.add(TableColsModel.builder().columnField("giftAmount").columnName("充值赠金").build());
        tabCols.add(TableColsModel.builder().columnField("org").columnName("门店").build());
        return tabCols;
    }


    public List<com.alibaba.fastjson.JSONObject> cashDatailsList(Map<String, List<BasePaymentTypeResultVO>> basePaymentTypeMap,
                                                                 List<BasePaymentTypeResultVO> basePaymentTypeList,
                                                                 List<StatisCashMirrorResultVO> posCashResultVOList,
                                                                 Map<Long, BaseTableArea> baseTableAreaMap,
                                                                 Map<String, BaseDict> baseTypeMap,
                                                                 List<PosCashPayment> cashPaymentList, Map<Long, List<PosCashCommenterResultVO>> cashCommenterListMap,
                                                                 Map<Long, MemberInfo> memberInfoMap, PosCashDetailsQuery query) {
        Map<String, com.alibaba.fastjson.JSONObject> dataMap = MapUtil.newHashMap();
        long startTime = System.currentTimeMillis();
        if (CollUtil.isEmpty(posCashResultVOList)) {
            return Lists.newArrayList();
        }
        int threadNum = 15;
        int size = posCashResultVOList.size();
        int perSize = size / threadNum;
        int remainder = size % threadNum;
        // 有余数, 将每个线程的处理数量 + 1
        if (remainder != 0) {
            threadNum = threadNum + 1;
        }

        ExecutorService executorService = Executors.newFixedThreadPool(threadNum);
        CountDownLatch countDownLatch = new CountDownLatch(threadNum);
        // 定义接受数据集合  多线程情况下，使用线程安全集合
        for (int i = 0; i < threadNum; i++) {
            StatisPosCashServiceImpl.MultiThread thread = new StatisPosCashServiceImpl.MultiThread();
            // 最后一个线程
            if (i == threadNum - 1) {
                thread.setIdList(posCashResultVOList.subList(i * perSize, size));
            } else {
                thread.setIdList(posCashResultVOList.subList(i * perSize, (i + 1) * perSize));
            }
            //thread.setIdList(posCashResultVOList.subList(i * perSize, Math.min((i + 1) * perSize, posCashResultVOList.size())));
            thread.setCashPaymentList(cashPaymentList);
            thread.setCashCommenterListMap(cashCommenterListMap);
            thread.setBasePaymentTypeMap(basePaymentTypeMap);
            thread.setMemberInfoMap(memberInfoMap);
            thread.setBaseTableTypeMap(baseTypeMap);
            thread.setBaseTableAreaMap(baseTableAreaMap);
            thread.setCountDownLatch(countDownLatch);
            thread.setResultList(dataMap);
            thread.setQuery(query);
            thread.setBasePaymentTypeList(basePaymentTypeList);
            executorService.submit(thread);
        }
        try {
            countDownLatch.await();
            executorService.shutdown();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
        log.info("处理数据耗时111：{}ms", (System.currentTimeMillis() - startTime));
        List<com.alibaba.fastjson.JSONObject> dataList = new ArrayList<>(dataMap.values());
        // 求和
        StatisCashMirrorResultVO mirrorResultVO = statisCashMapper.findMirrorResultVO(initWarp(query));
        if (Objects.nonNull(mirrorResultVO)) {
            JSONObject resultMap = new JSONObject();
            // 合计
            resultMap.put("sourceCode", "合计");
            resultMap.put("mirrorPayment", mirrorResultVO.getMirrorPayment());
            resultMap.put("mirrorRechargeAmount", mirrorResultVO.getMirrorRechargeAmount());
            resultMap.put("mirrorGiftAmount", mirrorResultVO.getMirrorGiftAmount());
            resultMap.put("storedCardAmount", mirrorResultVO.getStoredCardAmount());
            resultMap.put("paid", mirrorResultVO.getMirrorAmount());
            dataList.add(resultMap);
        }

        log.info("处理数据耗时：{}ms", (System.currentTimeMillis() - startTime));
        return dataList;
    }


    class MultiThread extends Thread {
        private List<StatisCashMirrorResultVO> idList;

        private CountDownLatch countDownLatch;
        private PosCashDetailsQuery query;

        private Map<String, com.alibaba.fastjson.JSONObject> result;

        private Map<String, List<BasePaymentTypeResultVO>> basePaymentTypeMap;
        private List<BasePaymentTypeResultVO> basePaymentTypeList;
        private Map<Long, BaseTableArea> baseTableAreaMap;
        private Map<String, BaseDict> baseTableTypeMap;
        private List<PosCashPayment> cashPaymentList;
        private Map<Long, List<PosCashCommenterResultVO>> cashCommenterListMap;
        private Map<Long, MemberInfo> memberInfoMap;

        public void setResultList(Map<String, com.alibaba.fastjson.JSONObject> result) {
            this.result = result;
        }

        public void setIdList(List<StatisCashMirrorResultVO> idList) {
            this.idList = idList;
        }

        public void setBasePaymentTypeMap(Map<String, List<BasePaymentTypeResultVO>> basePaymentTypeMap) {
            this.basePaymentTypeMap = basePaymentTypeMap;
        }

        public void setQuery(PosCashDetailsQuery query) {
            this.query = query;
        }

        public void setBaseTableAreaMap(Map<Long, BaseTableArea> baseTableAreaMap) {
            this.baseTableAreaMap = baseTableAreaMap;
        }

        public void setBaseTableTypeMap(Map<String, BaseDict> baseTypeMap) {
            this.baseTableTypeMap = baseTypeMap;
        }

        public void setCashPaymentList(List<PosCashPayment> cashPaymentList) {
            this.cashPaymentList = cashPaymentList;
        }

        public void setBasePaymentTypeList(List<BasePaymentTypeResultVO> basePaymentTypeList) {
            this.basePaymentTypeList = basePaymentTypeList;
        }


        public void setCashCommenterListMap(Map<Long, List<PosCashCommenterResultVO>> cashCommenterListMap) {
            this.cashCommenterListMap = cashCommenterListMap;
        }

        public void setMemberInfoMap(Map<Long, MemberInfo> memberInfoMap) {
            this.memberInfoMap = memberInfoMap;
        }

        public void setCountDownLatch(CountDownLatch countDownLatch) {
            this.countDownLatch = countDownLatch;
        }

        @Override
        public void run() {
            try {
                // 数据处理
                for (int i = 0; i < idList.size(); i++) {
                    String s1 = IdUtil.fastSimpleUUID();
                    StatisCashMirrorResultVO posCashResultVO = idList.get(i);
                    if (posCashResultVO.getRefundAmount() == null) {
                        posCashResultVO.setRefundAmount(BigDecimal.ZERO);
                    }
                    if (posCashResultVO.getPaid() == null) {
                        posCashResultVO.setPaid(BigDecimal.ZERO);
                    }

                    com.alibaba.fastjson.JSONObject tableInfoMap = new JSONObject();
                    tableInfoMap.put("sourceCode", posCashResultVO.getCode());
                    tableInfoMap.put("mirrorType", CashMirrorTypeEnum.get(posCashResultVO.getMirrorType()).getDesc());
                    tableInfoMap.put("code", posCashResultVO.getMirrorCode());
                    tableInfoMap.put("billDate", posCashResultVO.getMirrorBillDate());
                    tableInfoMap.put("type", posCashResultVO.getEchoMap().get("type"));
                    tableInfoMap.put("tableName", posCashResultVO.getTableName());
                    tableInfoMap.put("tableType", StrUtil.isNotBlank(posCashResultVO.getTableType())
                            && CollUtil.isNotEmpty(baseTableTypeMap)
                            && ObjectUtil.isNotNull(baseTableTypeMap.get(posCashResultVO.getTableType())) ? baseTableTypeMap.get(posCashResultVO.getTableType()).getName() : "");
                    tableInfoMap.put("tableArea", ObjectUtil.isNotNull(posCashResultVO.getTableArea())
                            && CollUtil.isNotEmpty(baseTableAreaMap)
                            && ObjectUtil.isNotNull(baseTableAreaMap.get(posCashResultVO.getTableArea())) ? baseTableAreaMap.get(posCashResultVO.getTableArea()).getName() : "");
                    tableInfoMap.put("createdEmp", posCashResultVO.getEchoMap().get("createdEmp"));


                    tableInfoMap.put("orderSource", posCashResultVO.getEchoMap().get("orderSource"));
                    MemberInfo memberInfo = memberInfoMap.get(posCashResultVO.getMemberId());
                    String memberName = "-";
                    if (Objects.nonNull(memberInfo)) {
                        memberName = memberInfo.getName() + "(" + StrUtil.subSuf(memberInfo.getMobile(), memberInfo.getMobile().length() - 4) + ")";
                    }
                    tableInfoMap.put("memberId", memberName);
                    tableInfoMap.put("createTime", posCashResultVO.getCreatedTime());
                    tableInfoMap.put("completeTime", posCashResultVO.getCompleteTime());
                    tableInfoMap.put("mirrorPayment", posCashResultVO.getMirrorPayment());
                    tableInfoMap.put("mirrorRechargeAmount", posCashResultVO.getMirrorRechargeAmount());
                    tableInfoMap.put("mirrorGiftAmount", posCashResultVO.getMirrorGiftAmount());
                    tableInfoMap.put("storedCardAmount", posCashResultVO.getStoredCardAmount());
                    if (StringUtils.equals(posCashResultVO.getMirrorType(), CashMirrorTypeEnum.ORDER_PAY.getCode())) {
                        tableInfoMap.put("tableAmount", posCashResultVO.getTableAmount());
                        tableInfoMap.put("productAmount", posCashResultVO.getProductAmount());
                        tableInfoMap.put("serviceAmount", posCashResultVO.getServiceAmount());
                        tableInfoMap.put("groupBuyAmount", posCashResultVO.getGroupBuyAmount());
                        tableInfoMap.put("thailAmount", posCashResultVO.getThailAmount());
                        tableInfoMap.put("powerAmount", posCashResultVO.getPowerAmount());
                        tableInfoMap.put("amount", posCashResultVO.getMirrorAmount());
                        tableInfoMap.put("discountAmount", posCashResultVO.getDiscountAmount());
                    }
                    switch (CashMirrorTypeEnum.get(posCashResultVO.getMirrorType())) {
                        case ORDER_PAY:
                            tableInfoMap.put("refundAmount", posCashResultVO.getRefundAmount());
                            break;
                        case RECHARGE_PAY:
                            tableInfoMap.put("refundAmount", posCashResultVO.getRefundAmount());
                            tableInfoMap.put("rechargeAmount", posCashResultVO.getPayment());
                            tableInfoMap.put("giftAmount", posCashResultVO.getGiftAmount());
                            break;
                        case RECHARGE_REFUND:
                            tableInfoMap.put("rechargeAmount", BigDecimal.ZERO.subtract(posCashResultVO.getPayment()));
                            if (Objects.nonNull(posCashResultVO.getGiftAmount())) {
                                tableInfoMap.put("giftAmount", BigDecimal.ZERO.subtract(posCashResultVO.getGiftAmount()));
                            }
                            break;
                        default:
                            break;
                    }

                    tableInfoMap.put("paid", posCashResultVO.getMirrorAmount());

                    //tableInfoMap.put("payment", posCashResultVO.getPayment());
                    tableInfoMap.put("orderRemarks", posCashResultVO.getOrderRemarks());
                    tableInfoMap.put("org", posCashResultVO.getEchoMap().get("orgId"));

                    if (StringUtils.equals(posCashResultVO.getMirrorType(), CashMirrorTypeEnum.ORDER_PAY.getCode())) {
                        // 所有支付方式,
                        for (BasePaymentTypeResultVO basePaymentTypeResultVO : basePaymentTypeList) {
                            List<PosCashPayment> posCashPayments = cashPaymentList.stream().filter(c ->
                                            Objects.equals(basePaymentTypeResultVO.getId(), c.getPayTypeId())
                                                    && Objects.equals(posCashResultVO.getCashId(), c.getCashId()) && Objects.nonNull(c.getAmount()))
                                    .collect(Collectors.toList());
                            if (CollUtil.isNotEmpty(posCashPayments)) {
                                if (StringUtils.equals(posCashResultVO.getBillState(), PosCashBillStateEnum.REFUNDED.getCode())) {
                                    tableInfoMap.put("payment_" + basePaymentTypeResultVO.getId(),
                                            posCashPayments.stream().filter(c -> Objects.nonNull(c.getAmount())).map(PosCashPayment::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                                                    .subtract(posCashPayments.stream().filter(c -> Objects.nonNull(c.getChangeAmount())).map(PosCashPayment::getChangeAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
                                } else {
                                    tableInfoMap.put("payment_" + basePaymentTypeResultVO.getId(),
                                            posCashPayments.stream().filter(c -> Objects.nonNull(c.getAmount())).map(PosCashPayment::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                                                    .subtract(posCashPayments.stream().filter(c -> Objects.nonNull(c.getRefundAmount())).map(PosCashPayment::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                                                    .subtract(posCashPayments.stream().filter(c -> Objects.nonNull(c.getChangeAmount())).map(PosCashPayment::getChangeAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
                                }
                            } else {
                                tableInfoMap.put("payment_" + basePaymentTypeResultVO.getId(), 0.00);
                            }
                        }
                    } else {
                        for (BasePaymentTypeResultVO basePaymentTypeResultVO : basePaymentTypeList) {
                            tableInfoMap.put("payment_" + basePaymentTypeResultVO.getId(), 0.00);
                        }
                    }
                    result.put(s1, tableInfoMap);
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (countDownLatch != null) {
                    countDownLatch.countDown();
                }
            }
        }
    }

    public static boolean isInRange(String startStr, String endStr, String targetStr) {
        // 解析目标日期
        DateTime target = DateUtil.parse(targetStr, "yyyy-MM-dd");
        if (startStr != null && !startStr.isEmpty()) {
            DateTime start = DateUtil.parse(startStr, "yyyy-MM-dd");
            if (!target.isAfterOrEquals(start)) {
                return false;
            } // target < start
        }

        if (endStr != null && !endStr.isEmpty()) {
            DateTime end = DateUtil.parse(endStr, "yyyy-MM-dd");
            if (!target.isBeforeOrEquals(end)) {
                return false;
            } // target > end
        }

        return true;
    }
}
