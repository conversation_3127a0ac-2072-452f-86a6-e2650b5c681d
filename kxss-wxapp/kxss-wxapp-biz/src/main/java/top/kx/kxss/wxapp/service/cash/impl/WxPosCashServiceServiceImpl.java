package top.kx.kxss.wxapp.service.cash.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.app.mapper.cash.service.PosCashServiceMapper;
import top.kx.kxss.app.service.table.TableService;
import top.kx.kxss.app.vo.result.cash.service.PosCashServiceDetailResultVO;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.store.BaseStore;
import top.kx.kxss.base.service.common.BaseParameterService;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.service.service.BaseServiceService;
import top.kx.kxss.base.service.store.BaseStoreService;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.app.CashTableStatusEnum;
import top.kx.kxss.model.enumeration.base.OrderBillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillTypeEnum;
import top.kx.kxss.model.enumeration.base.ServiceStaffTimeEnum;
import top.kx.kxss.model.enumeration.pos.ServiceOrderQueryTypeEnum;
import top.kx.kxss.wxapp.service.cash.WxPosCashServiceService;
import top.kx.kxss.wxapp.service.statistics.CustomService;
import top.kx.kxss.wxapp.vo.query.cash.WxPosCashServicePageQuery;
import top.kx.kxss.wxapp.vo.result.cash.WxPosCashServiceDetailResultVO;
import top.kx.kxss.wxapp.vo.result.cash.WxPosCashServiceResultVO;
import top.kx.kxss.wxapp.vo.result.cash.WxPosCashServiceTotalResultVO;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@DS(DsConstant.BASE_TENANT)
public class WxPosCashServiceServiceImpl implements WxPosCashServiceService {
    @Autowired
    private PosCashServiceMapper posCashServiceMapper;
    @Autowired
    private top.kx.kxss.app.service.cash.PosCashServiceService posCashService;

    @Autowired
    private BaseServiceService baseServiceService;
    @Autowired
    private BaseStoreService baseStoreService;
    @Autowired
    private EchoService echoService;
    @Autowired
    private CustomService customService;
    @Autowired
    private MemberInfoService memberInfoService;
    @Autowired
    private TableService tableService;
    @Autowired
    private BaseParameterService baseParameterService;

    @Override
    public IPage<WxPosCashServiceResultVO> page(PageParams<WxPosCashServicePageQuery> params) {
        WxPosCashServicePageQuery model = params.getModel();
        customService.storeTime(model);
        params.setSort("");
        params.setOrder("");
        IPage<PosCashService> page = params.buildPage(PosCashService.class);
        QueryWrapper<PosCashService> queryWrap = this.buildQueryWrap(model);
        IPage<PosCashServiceDetailResultVO> posCashServicePage = posCashServiceMapper.queryServiceList(page, queryWrap);
        IPage<WxPosCashServiceResultVO> resultPage = BeanPlusUtil.toBeanPage(posCashServicePage, WxPosCashServiceResultVO.class);
        List<WxPosCashServiceResultVO> records = resultPage.getRecords();
        calAmount(records);
        fillOrgName(records);
        echoService.action(resultPage);
        return resultPage;
    }

    private QueryWrapper<PosCashService> buildQueryWrap(WxPosCashServicePageQuery model) {
        String orderQueryType = model.getOrderQueryType();
        if (StringUtils.isBlank(orderQueryType)) {
            orderQueryType = ServiceOrderQueryTypeEnum.COMPLETE_TIME.getCode();
        }
        QueryWrapper<PosCashService> queryWrap = new QueryWrapper<>();
        queryWrap.eq("p.org_id", ContextUtil.getCurrentCompanyId());
        if (ObjectUtil.equal(orderQueryType, ServiceOrderQueryTypeEnum.CREATED_TIME.getCode())) {
            queryWrap.between("p.created_time", model.getStartDate(), model.getEndDate());
            queryWrap.notIn("t.status", Collections.singletonList(CashTableStatusEnum.REFUND.getCode()));
        } else if (ObjectUtil.equal(orderQueryType, ServiceOrderQueryTypeEnum.SERVICE_START_TIME.getCode())) {
            queryWrap.between("t.start_time", model.getStartDate(), model.getEndDate());
            queryWrap.notIn("t.status", Collections.singletonList(CashTableStatusEnum.REFUND.getCode()));
        } else if (ObjectUtil.equal(orderQueryType, ServiceOrderQueryTypeEnum.SERVICE_END_TIME.getCode())) {
            queryWrap.between("t.end_time", model.getStartDate(), model.getEndDate());
            queryWrap.notIn("t.status", Arrays.asList(CashTableStatusEnum.TIMING.getCode(),
                    CashTableStatusEnum.REFUND.getCode()));
        } else {
            queryWrap.between("p.complete_time", model.getStartDate(), model.getEndDate());
            queryWrap.notIn("t.status", Collections.singletonList(CashTableStatusEnum.REFUND.getCode()));
            queryWrap.in("p.bill_state", Arrays.asList(OrderBillStateEnum.COMPLETE.getCode(),
                    PosCashBillStateEnum.PART_REFUND.getCode()));
        }
        if (StrUtil.isNotBlank(model.getBillState()) &&
                ObjectUtil.equal(model.getBillState(), OrderBillStateEnum.CANCEL.getCode())) {
            queryWrap.eq("p.bill_type", PosCashBillTypeEnum.CANCELLATION.getCode());
        } else {
            queryWrap.eq(StrUtil.isNotBlank(model.getBillState()), "p.bill_state", model.getBillState());
        }
        queryWrap.ne("p.bill_state", PosCashBillStateEnum.REFUNDED.getCode());
        //已撤单的不查
        queryWrap.ne("p.bill_type", PosCashBillTypeEnum.CANCELLATION.getCode());
        queryWrap.eq(model.getEmployeeId() != null, "t.employee_id", model.getEmployeeId());
        queryWrap.like(StringUtils.isNotBlank(model.getEmployeeName()), "t.employee_name", model.getEmployeeName());
        queryWrap.eq(StringUtils.isNotBlank(model.getBillType()), "p.bill_type", model.getBillType());
        queryWrap.eq(StringUtils.isNotBlank(model.getType()), "p.type_", model.getType());
        queryWrap.eq(StringUtils.isNotBlank(model.getClockType()), "t.clock_type", model.getClockType());
        queryWrap.eq(model.getServiceId() != null, "t.service_id", model.getServiceId());
        //queryWrap.isNull("t.cash_thail_id");
        queryWrap.eq("t.delete_flag", 0);
        queryWrap.eq("p.delete_flag", 0);
        if (StringUtils.isNotBlank(model.getKeyword())) {
            queryWrap.and(wrapper -> wrapper
                    .like("e.real_name", model.getKeyword())
                    .or().like("e.number", model.getKeyword()));
        }
        if (ObjectUtil.equal(orderQueryType, ServiceOrderQueryTypeEnum.CREATED_TIME.getCode())) {
            queryWrap.orderByDesc("p.created_time").orderByDesc("t.id");
        } else if (ObjectUtil.equal(orderQueryType, ServiceOrderQueryTypeEnum.SERVICE_START_TIME.getCode())) {
            queryWrap.orderByDesc("t.start_time").orderByDesc("t.id");
            ;
        } else if (ObjectUtil.equal(orderQueryType, ServiceOrderQueryTypeEnum.SERVICE_END_TIME.getCode())) {
            queryWrap.orderByDesc("t.end_time").orderByDesc("t.id");
            ;
        } else {
            queryWrap.orderByDesc("p.complete_time").orderByDesc("t.id");
        }
        return queryWrap;
    }

    @Override
    public WxPosCashServiceDetailResultVO detail(Long cashServiceId) {
        PosCashService service = posCashServiceMapper.selectById(cashServiceId);
        if (service == null) {
            return null;
        }
        WxPosCashServiceDetailResultVO resultVO = BeanPlusUtil.toBean(service, WxPosCashServiceDetailResultVO.class);
        resultVO.setOrgId(service.getCreatedOrgId());
        BaseStore baseStore = baseStoreService.getByIdCache(resultVO.getOrgId());
        if (baseStore != null) {
            resultVO.setOrgName(StrUtil.isNotBlank(baseStore.getShortName()) ? baseStore.getShortName() : baseStore.getName());
        }
        PosCash posCash = posCashService.getById(service.getCashId());
        Long memberId = null;
        if (posCash != null) {
            resultVO.setCode(posCash.getCode());
            resultVO.setTableName(posCash.getTableName());
            resultVO.setBillState(posCash.getBillState());
            resultVO.setBillType(posCash.getBillType());
            resultVO.setType(posCash.getType());
            resultVO.setOrderCreatedTime(posCash.getCreatedTime());
            resultVO.setOrderCompleteTime(posCash.getCompleteTime());
            memberId = posCash.getMemberId();
        }
        if (memberId != null) {
            MemberInfo memberInfo = memberInfoService.getById(memberId);
            if (memberInfo != null) {
                resultVO.setMemberName(memberInfo.getName());
            }
            if (ObjectUtil.isNotNull(memberInfo) && StrUtil.isNotBlank(memberInfo.getMobile())) {
                resultVO.setMemberName(resultVO.getMemberName()
                        .concat("(")
                        .concat(PhoneUtil.hideBetween(memberInfo.getMobile()).toString())
                        .concat(")"));
            }
        }
        resultVO.setActualAmount(resultVO.getAmount());
        if (resultVO.getAssessedAmount() != null) {
            resultVO.setActualAmount(resultVO.getAmount().subtract(resultVO.getAssessedAmount()));
        }
        resultVO.setPayment(resultVO.getActualAmount());
        if (resultVO.getRefundAmount() == null) {
            resultVO.setRefundAmount(BigDecimal.ZERO);
        }
        resultVO.setActualAmount(resultVO.getActualAmount().subtract(resultVO.getRefundAmount()));

        if (ObjectUtil.isNotNull(service.getCashThailId())) {
            if (resultVO.getAssessedAmount() != null) {
                resultVO.setActualAmount(service.getAssessedAmount());
            }
            if (service.getThailAssessedAmount() != null) {
                resultVO.setActualAmount(service.getThailAssessedAmount());
            }
        }
        // 计费时长
        resultVO.setCycleDuration(getChargingDuration(resultVO.getCycle(), resultVO.getCycleNum()));
        resultVO.setStatusDesc(CashTableStatusEnum.get(resultVO.getStatus()).getDesc());
        echoService.action(resultVO);
        return resultVO;
    }

    @Override
    public WxPosCashServiceTotalResultVO total(WxPosCashServicePageQuery query) {
        customService.storeTime(query);
        ServiceStaffTimeEnum defaultServiceStaffTime = baseParameterService.getDefaultServiceStaffTime();
        QueryWrapper<PosCashService> queryWrapper = this.buildQueryWrap(query);
        WxPosCashServiceTotalResultVO resultVO = posCashServiceMapper.queryTotal(queryWrapper);
        resultVO.setTotalDurationDesc(posCashService.serviceDurationDesc(resultVO.getTotalDuration(), defaultServiceStaffTime));
        resultVO.setTotalChargingDurationDesc(posCashService.serviceDurationDesc(resultVO.getTotalChargingDuration(), defaultServiceStaffTime));
        return resultVO;
    }

    /**
     * 补充主单信息以及计算价格
     *
     * @param records
     */
    private void calAmount(List<WxPosCashServiceResultVO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        for (WxPosCashServiceResultVO record : records) {
            record.setOrgId(record.getCreatedOrgId());
            //价格计算
            record.setActualAmount(record.getAmount());
            if (record.getAssessedAmount() != null) {
                record.setActualAmount(record.getAmount().subtract(record.getAssessedAmount()));
            }
            if (record.getRefundAmount() != null) {
                record.setActualAmount(record.getActualAmount().subtract(record.getRefundAmount()));
            }
            if (ObjectUtil.isNotNull(record.getCashThailId())) {
                if (record.getAssessedAmount() != null) {
                    record.setActualAmount(record.getAssessedAmount());
                }
                if (record.getThailAssessedAmount() != null) {
                    record.setActualAmount(record.getThailAssessedAmount());
                }
            }
            record.setStatusDesc(CashTableStatusEnum.get(record.getStatus()).getDesc());

        }
    }

    private void fillOrgName(List<WxPosCashServiceResultVO> records) {
        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        List<Long> orgIds = records.stream().map(WxPosCashServiceResultVO::getOrgId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<BaseStore> baseStoreList = baseStoreService.list(Wraps.<BaseStore>lbQ().in(BaseStore::getId, orgIds));
        if (CollectionUtils.isEmpty(baseStoreList)) {
            return;
        }
        Map<Long, BaseStore> baseStoreMap = baseStoreList.stream().collect(Collectors.toMap(BaseStore::getId, baseStore -> baseStore));
        for (WxPosCashServiceResultVO record : records) {
            BaseStore baseStore = baseStoreMap.get(record.getOrgId());
            if (ObjectUtil.isNull(baseStore)) {
                continue;
            }
            record.setOrgName(StrUtil.isNotBlank(baseStore.getShortName()) ? baseStore.getShortName() : baseStore.getName());
        }
    }

    private Integer getChargingDuration(String cycle, Integer cycleNum) {
        if (StringUtils.isBlank(cycle)) {
            return 0;
        }
        String[] split = cycle.split("元/");
        if (split.length != 2) {
            return 0;
        }
        String str = split[1];
        int num = cycleNum == null ? 0 : cycleNum;
        if (str.startsWith("小时")) {
            return 60 * num;
        } else if (str.contains("小时")) {
            String[] split1 = str.split("小时");
            return Integer.parseInt(split1[0]) * num;
        } else if (str.startsWith("分钟")) {
            return num;
        } else if (str.contains("分钟")) {
            String[] split1 = str.split("分钟");
            return Integer.parseInt(split1[0]) * num;
        }
        return 0;
    }
}
