package top.kx.kxss.wxapp.service.statistics.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.mapper.cash.PosCashMapper;
import top.kx.kxss.app.vo.result.cash.AmountResultVO;
import top.kx.kxss.base.entity.accounting.BaseAccountingDate;
import top.kx.kxss.base.entity.accounting.BaseAccountingInfo;
import top.kx.kxss.base.manager.accounting.BaseAccountingInfoManager;
import top.kx.kxss.base.querywraps.PosCashWraps;
import top.kx.kxss.base.service.accounting.BaseAccountingDateService;
import top.kx.kxss.base.vo.result.accounting.AccountingCalenderSumResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillTypeEnum;
import top.kx.kxss.wxapp.service.mapper.StatisAccountingMapper;
import top.kx.kxss.wxapp.service.statistics.CustomService;
import top.kx.kxss.wxapp.service.statistics.StatisAccountingService;
import top.kx.kxss.wxapp.vo.query.common.OrgIdListQuery;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.AccountingExpendOverviewResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.AccountingMonthOverviewResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.AccountingOverviewResultVO;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@DS(DsConstant.BASE_TENANT)
public class StatisAccountingServiceImpl implements StatisAccountingService {

    @Autowired
    private CustomService customService;
    @Resource
    private PosCashMapper posCashMapper;
    @Resource
    private BaseAccountingDateService baseAccountingDateService;
    @Resource
    private StatisAccountingMapper statisAccountingMapper;
    @Resource
    private EchoService echoService;
    @Autowired
    private BaseAccountingInfoManager baseAccountingInfoManager;

    @Override
    public AccountingOverviewResultVO overview(DataOverviewQuery query) {
        String startDate  = query.getStartDate();
        String endDate = query.getEndDate();
        // 收入金额
        BigDecimal incomeAmount = BigDecimal.ZERO;
        // 上月收入金额
        BigDecimal lastMonthIncomeAmount = BigDecimal.ZERO;
        // 手动记账收入
        BigDecimal manualPayInAmount = BigDecimal.ZERO;
        // 上个月手动记账收入
        BigDecimal lastMonthManualPayInAmount = BigDecimal.ZERO;
        // 支出金额
        BigDecimal expendAmount = BigDecimal.ZERO;
        // 上个月支出金额
        BigDecimal lastMonthExpendAmount = BigDecimal.ZERO;

        // 当月
        // 记账金额
        LbQueryWrap<BaseAccountingDate> queryWrap = Wraps.<BaseAccountingDate>lbQ()
                .eq(BaseAccountingDate::getCreatedOrgId, ContextUtil.getCurrentCompanyId());
        queryWrap.ge(BaseAccountingDate::getRecordingDate, DateUtils.getStartTime(query.getStartDate()));
        queryWrap.le(BaseAccountingDate::getRecordingDate, DateUtils.getEndTime(query.getEndDate()));
        List<BaseAccountingDate> baseAccountingDateList = baseAccountingDateService.list(queryWrap);
        if (CollUtil.isNotEmpty(baseAccountingDateList)) {
            expendAmount = baseAccountingDateList.stream().map(BaseAccountingDate::getPayOut).filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
        }

        // 手动记账收入
        QueryWrapper<BaseAccountingInfo> queryWrapper = getAccountingInfoQueryWrapper(query);
        AccountingCalenderSumResultVO calendarSum = baseAccountingInfoManager.calendarSum(queryWrapper);
        if (Objects.nonNull(calendarSum)) {
            manualPayInAmount = manualPayInAmount.add(calendarSum.getManualPayInAmount());
        }

        DataOverviewQuery incomeQuery = new DataOverviewQuery();
        incomeQuery.setStartDate(startDate);
        incomeQuery.setEndDate(endDate);
        customService.storeTime(incomeQuery);
        QueryWrapper<PosCash> wrapper = getPosCashQueryWrapper(incomeQuery);
        // 订单收入
        List<AmountResultVO> amountResultVOList = posCashMapper.selectByPayType(wrapper);
        if (CollUtil.isNotEmpty(amountResultVOList)) {
            incomeAmount =  amountResultVOList.stream().map(AmountResultVO::getAmount).filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
        }
        // 手动记账+订单收入
        incomeAmount = incomeAmount.add(manualPayInAmount).setScale(2, RoundingMode.HALF_UP);

        // 利润金额
        BigDecimal profitAmount = incomeAmount.subtract(expendAmount).setScale(2, RoundingMode.HALF_UP);

        // 上月
        query.setStartDate(DateUtil.formatDate(DateUtil.offsetMonth(DateUtil.parse(startDate), -1)));
        query.setEndDate(DateUtil.formatDate(DateUtil.offsetMonth(DateUtil.parse(endDate), -1)));

        // 记账收入
        List<BaseAccountingDate> lastMonthBaseAccountingDateList = baseAccountingDateService.list(Wraps.<BaseAccountingDate>lbQ()
                .eq(BaseAccountingDate::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .between(BaseAccountingDate::getRecordingDate, query.getStartDate(), query.getEndDate()));
        if (CollUtil.isNotEmpty(lastMonthBaseAccountingDateList)) {
            lastMonthExpendAmount = lastMonthBaseAccountingDateList.stream().map(BaseAccountingDate::getPayOut).filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
        }
        // 手动记账收入
        QueryWrapper<BaseAccountingInfo> lastMonthQueryWrapper = getAccountingInfoQueryWrapper(query);
        calendarSum = baseAccountingInfoManager.calendarSum(lastMonthQueryWrapper);
        if (Objects.nonNull(calendarSum)) {
            lastMonthManualPayInAmount = lastMonthManualPayInAmount.add(calendarSum.getManualPayInAmount());
        }
        incomeQuery = new DataOverviewQuery();
        incomeQuery.setStartDate(query.getStartDate());
        incomeQuery.setEndDate(query.getEndDate());
        customService.storeTime(incomeQuery);
        QueryWrapper<PosCash> lastWrapper = getPosCashQueryWrapper(incomeQuery);
        List<AmountResultVO> lastMonthAmountResultVOList = posCashMapper.selectByPayType(lastWrapper);
        if (CollUtil.isNotEmpty(lastMonthAmountResultVOList)) {
            lastMonthIncomeAmount =  lastMonthAmountResultVOList.stream().map(AmountResultVO::getAmount).filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP);
        }
        lastMonthIncomeAmount = lastMonthIncomeAmount.add(lastMonthManualPayInAmount).setScale(2, RoundingMode.HALF_UP);
        // 上个月利润
        BigDecimal lastMonthProfitAmount = lastMonthIncomeAmount.subtract(lastMonthExpendAmount).setScale(2, RoundingMode.HALF_UP);

        // 上个月涨幅
        BigDecimal incomeAmountRise = BigDecimal.ZERO;
        if (lastMonthIncomeAmount.compareTo(BigDecimal.ZERO) != 0) {
            incomeAmountRise = (incomeAmount.subtract(lastMonthIncomeAmount)).divide(lastMonthIncomeAmount, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP);
        }
        return AccountingOverviewResultVO.builder().incomeAmount(incomeAmount)
                .expendAmount(expendAmount)
                .profitAmount(profitAmount)
                .manualPayInAmount(manualPayInAmount)
                .lastMonthIncomeAmount(lastMonthIncomeAmount)
                .lastMonthExpendAmount(lastMonthExpendAmount)
                .lastMonthProfitAmount(lastMonthProfitAmount)
                .incomeAmountRise(incomeAmountRise)
                .build();
    }

    @NotNull
    private static QueryWrapper<BaseAccountingInfo> getAccountingInfoQueryWrapper(DataOverviewQuery query) {
        QueryWrapper<BaseAccountingInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bai.delete_flag", 0)
                .eq("bai.created_org_id", ContextUtil.getCurrentCompanyId());
        if (StringUtils.isNotBlank(query.getStartDate())) {
            queryWrapper.ge("bai.recording_date", query.getStartDate());
        }
        if (StringUtils.isNotBlank(query.getEndDate())) {
            queryWrapper.le("bai.recording_date", query.getEndDate());
        }
        return queryWrapper;
    }

    @NotNull
    private static QueryWrapper<PosCash> getPosCashQueryWrapper(DataOverviewQuery incomeQuery) {
        QueryWrapper<PosCash> wrapper = new QueryWrapper<>();
        //营业收入
        wrapper.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(),
                        PosCashBillStateEnum.PART_REFUND.getCode(), PosCashBillStateEnum.REFUNDED.getCode()))
                .notIn("p.bill_type", Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode(),
                        PosCashBillTypeEnum.CHARGEBACK.getCode())).eq("p.delete_flag", 0)
                .between("p.complete_time", incomeQuery.getStartDate(), incomeQuery.getEndDate())
                .notInSql("t.pay_type_id", "select id from base_payment_type where " +
                        "biz_type in ('1','8') and delete_flag = 0 ")
                .eq("t.status", PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                .eq("t.delete_flag", 0)
                .eq("p.org_id", ContextUtil.getCurrentCompanyId());
        return wrapper;
    }

    @Override
    public List<AccountingMonthOverviewResultVO> incomeExpend(DataOverviewQuery query) {
        initOrgIdList(query);
        String endDate = query.getEndDate();

        // 从结束时间点,往前退6个月,的开始时间
        query.setStartDate(DateUtil.formatDate(DateUtil.beginOfDay(DateUtil.beginOfMonth(DateUtil.offset(DateUtil.parse(query.getEndDate()), DateField.MONTH, -6)))));
        List<AccountingMonthOverviewResultVO> overviewResultVOList = statisAccountingMapper.monthOverview(query);
        // 根据月份进行分组
        Map<String, AccountingMonthOverviewResultVO> overviewResultVOMap = overviewResultVOList.stream().collect(
                Collectors.toMap(AccountingMonthOverviewResultVO::getDate, k -> k));
        customService.storeTime(query);
        QueryWrapper<PosCash> wrapper = getPosCashQueryWrapper(query);
        Map<String, AmountResultVO> incomeAmountMap = new HashMap<>();
        List<AmountResultVO> amountResultVOList = baseAccountingInfoManager.calendarIn(wrapper, query.getHour(), "%Y年%m月");
        if (CollUtil.isNotEmpty(amountResultVOList)) {
            incomeAmountMap = amountResultVOList.stream().collect(Collectors.toMap(AmountResultVO::getField, Function.identity()));
        }
        List<String> monthList = getMonthList(endDate);
        String[] maxYearMonth = monthList.get(5).split("年");
        Map<String, AmountResultVO> finalIncomeAmountMap = incomeAmountMap;
        return monthList.stream().map(month -> {
            AccountingMonthOverviewResultVO overviewResultVO = overviewResultVOMap.get(month);
            AmountResultVO amountResultVO = finalIncomeAmountMap.get(month);
            String mon = getMonth(month, maxYearMonth);
            BigDecimal incomeAmount = BigDecimal.ZERO;
            if (Objects.nonNull(amountResultVO)) {
                incomeAmount = amountResultVO.getAmount();
            }
            if (Objects.nonNull(overviewResultVO)) {
                incomeAmount = incomeAmount.add(overviewResultVO.getManualPayInAmount());
            }
            return AccountingMonthOverviewResultVO.builder()
                    .date(mon)
                    .incomeAmount(incomeAmount)
                    .expendAmount(Objects.isNull(overviewResultVO) ? BigDecimal.ZERO : overviewResultVO.getPayOutAmount())
                    .build();
        }).collect(Collectors.toList());
    }


    public void initOrgIdList(OrgIdListQuery params) {
        if (Objects.isNull(params)) {
            params = new OrgIdListQuery();
        }
        if (CollUtil.isEmpty(params.getOrgIdList())) {
            params.setOrgIdList(Collections.singletonList(ContextUtil.getCurrentCompanyId()));
        }
    }

    @Override
    public List<AccountingExpendOverviewResultVO> expendRise(DataOverviewQuery query) {

        // 本月
        //customService.storeTime(query);
        initOrgIdList(query);

        List<AccountingExpendOverviewResultVO> resultVOList = statisAccountingMapper.expendOverview(query);
        if (CollUtil.isEmpty(resultVOList)) {
            return resultVOList;
        }
        echoService.action(resultVOList);

        // 上个月
        query.setStartDate(DateUtil.formatDate(DateUtil.offsetMonth(DateUtil.parse(query.getStartDate()), -1)));
        query.setEndDate(DateUtil.formatDate(DateUtil.offsetMonth(DateUtil.parse(query.getEndDate()), -1)));

        //customService.storeTime(query);

        List<AccountingExpendOverviewResultVO> lastMonthresultVOList = statisAccountingMapper.expendOverview(query);
        Map<String, AccountingExpendOverviewResultVO> lastMonthMap = new HashMap<>();
        if (CollUtil.isNotEmpty(lastMonthresultVOList)) {
            lastMonthMap = lastMonthresultVOList.stream().collect(Collectors.toMap(AccountingExpendOverviewResultVO::getPayOutType, Function.identity()));
        }
        for (AccountingExpendOverviewResultVO resultVO : resultVOList) {
            AccountingExpendOverviewResultVO accountingExpendOverviewResultVO = lastMonthMap.get(resultVO.getPayOutType());
            resultVO.setPayOutTypeName(Objects.nonNull(resultVO.getEchoMap().get("payOutType")) ? resultVO.getEchoMap().get("payOutType").toString() : "-");
            if (Objects.nonNull(accountingExpendOverviewResultVO) && resultVO.getExpendAmount().compareTo(BigDecimal.ZERO) != 0 && accountingExpendOverviewResultVO.getExpendAmount().compareTo(BigDecimal.ZERO) != 0) {
                resultVO.setRise((resultVO.getExpendAmount().subtract(accountingExpendOverviewResultVO.getExpendAmount())).divide(accountingExpendOverviewResultVO.getExpendAmount(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP));
            } else if (resultVO.getExpendAmount().compareTo(BigDecimal.ZERO) == 0) {
                resultVO.setRise(BigDecimal.ZERO.subtract(new BigDecimal("100.00")));
            }
//            没有比例
//            else {
//                resultVO.setRise(new BigDecimal("100.00"));
//            }
        }
        return resultVOList;
    }

    @NotNull
    private static String getMonth(String YearMonth, String[] maxYearMonth) {
        String[] monthSplit = YearMonth.split("年");
        if (StringUtils.equals(maxYearMonth[0], monthSplit[0])) {
            int monthValue = Integer.parseInt(monthSplit[1].trim().replace("月", ""));
            if (monthValue < 10) {
                return monthValue + "月";
            }
            return monthSplit[1].trim();
        }
        // 不是同一年
        int monthValue = Integer.parseInt(monthSplit[1].trim().replace("月", ""));
        if (monthValue == 12) {
            return YearMonth;
        }
        if (monthValue < 10) {
            return monthValue + "月";
        }
        return monthSplit[1].trim();
    }

    @Override
    public List<AccountingExpendOverviewResultVO> expendOverview(DataOverviewQuery query) {
        // customService.storeTime(query);
        initOrgIdList(query);
        List<AccountingExpendOverviewResultVO> resultVOList = statisAccountingMapper.expendOverview(query);
        if (CollUtil.isEmpty(resultVOList)) {
            return resultVOList;
        }
        echoService.action(resultVOList);

        BigDecimal totalExpendAmount = resultVOList.stream().map(AccountingExpendOverviewResultVO::getExpendAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);

        for (AccountingExpendOverviewResultVO resultVO : resultVOList) {
            resultVO.setRise(totalExpendAmount.compareTo(BigDecimal.ZERO) != 0 ? resultVO.getExpendAmount().divide(totalExpendAmount, 4, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).setScale(2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            resultVO.setPayOutTypeName(Objects.nonNull(resultVO.getEchoMap().get("payOutType")) ? resultVO.getEchoMap().get("payOutType").toString() : "-");
        }
        return resultVOList.stream().sorted(Comparator.comparing(AccountingExpendOverviewResultVO::getRise).reversed()).collect(Collectors.toList());
    }

    /**
     * 获取营业收入统计
     *
     * @param query
     */
    private List<AmountResultVO> getAmountResultVOList(DataOverviewQuery query) {
        // 查询支出统计
        return posCashMapper.selectByPayType(PosCashWraps.incomePaymentWraps(query));
    }


    /**
     * 前6个月的月份
     *
     * @param date
     * @return
     */
    private List<String> getMonthList(String date) {
        Date inputDate = DateUtil.parse(date);
        List<String> list = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            list.add(DateUtil.format(DateUtil.offset(inputDate, DateField.MONTH, -i), DateUtils.DEFAULT_MONTH_FORMAT_EN));
        }
        Collections.sort(list);
        return list;

    }
}
