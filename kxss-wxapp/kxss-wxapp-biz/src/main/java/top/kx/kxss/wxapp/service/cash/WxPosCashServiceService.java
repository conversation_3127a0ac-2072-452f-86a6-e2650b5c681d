package top.kx.kxss.wxapp.service.cash;

import com.baomidou.mybatisplus.core.metadata.IPage;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.wxapp.vo.query.cash.WxPosCashServicePageQuery;
import top.kx.kxss.wxapp.vo.result.cash.WxPosCashServiceDetailResultVO;
import top.kx.kxss.wxapp.vo.result.cash.WxPosCashServiceResultVO;
import top.kx.kxss.wxapp.vo.result.cash.WxPosCashServiceTotalResultVO;

public interface WxPosCashServiceService {
    IPage<WxPosCashServiceResultVO> page(PageParams<WxPosCashServicePageQuery> params);

    WxPosCashServiceDetailResultVO detail(Long cashServiceId);

    WxPosCashServiceTotalResultVO total(WxPosCashServicePageQuery query);
}
