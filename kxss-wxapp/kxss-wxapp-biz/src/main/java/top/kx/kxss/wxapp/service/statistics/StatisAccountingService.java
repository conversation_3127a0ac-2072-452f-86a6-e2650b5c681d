package top.kx.kxss.wxapp.service.statistics;


import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.AccountingExpendOverviewResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.AccountingMonthOverviewResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.AccountingOverviewResultVO;

import java.util.List;

/**
 * 笔记本收支统计
 *
 * <AUTHOR>
 */
public interface StatisAccountingService {

    AccountingOverviewResultVO overview(DataOverviewQuery query);

    List<AccountingMonthOverviewResultVO> incomeExpend(DataOverviewQuery query);

    List<AccountingExpendOverviewResultVO> expendRise(DataOverviewQuery query);

    List<AccountingExpendOverviewResultVO> expendOverview(DataOverviewQuery query);


}
