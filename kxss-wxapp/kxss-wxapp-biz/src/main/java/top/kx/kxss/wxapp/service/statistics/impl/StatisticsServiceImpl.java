package top.kx.kxss.wxapp.service.statistics.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.StyleSet;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.PosCashCommenter;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.entity.thail.PosCashThail;
import top.kx.kxss.app.service.bizcache.BizCacheService;
import top.kx.kxss.app.service.cash.PosCashCommenterService;
import top.kx.kxss.app.service.cash.PosCashServiceService;
import top.kx.kxss.app.service.cash.payment.PosCashPaymentService;
import top.kx.kxss.app.service.cash.product.PosCashProductService;
import top.kx.kxss.app.service.cash.table.PosCashTableService;
import top.kx.kxss.app.service.thail.PosCashThailService;
import top.kx.kxss.app.vo.query.thail.PosCashThailAmountQuery;
import top.kx.kxss.app.vo.result.cash.PosCashCommenterResultVO;
import top.kx.kxss.app.vo.result.cash.PosCashResultVO;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO;
import top.kx.kxss.app.vo.result.thail.PosCashThailAmountResultVO;
import top.kx.kxss.base.entity.common.BaseDict;
import top.kx.kxss.base.entity.group.BaseGroup;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.payment.BasePaymentType;
import top.kx.kxss.base.entity.performance.BasePerformance;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.service.BaseService;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.entity.tableArea.BaseTableArea;
import top.kx.kxss.base.entity.thail.BaseThail;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.entity.user.BaseOrg;
import top.kx.kxss.base.manager.product.BaseProductManager;
import top.kx.kxss.base.service.common.BaseDictService;
import top.kx.kxss.base.service.common.BaseParameterService;
import top.kx.kxss.base.service.group.BaseGroupService;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.service.payment.BasePaymentTypeService;
import top.kx.kxss.base.service.product.BaseProductService;
import top.kx.kxss.base.service.service.BaseServiceService;
import top.kx.kxss.base.service.table.BaseTableInfoService;
import top.kx.kxss.base.service.tableArea.BaseTableAreaService;
import top.kx.kxss.base.service.thail.BaseThailService;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.service.user.BaseOrgService;
import top.kx.kxss.base.vo.query.member.card.MemberCardPageQuery;
import top.kx.kxss.base.vo.result.common.BaseDictResultVO;
import top.kx.kxss.base.vo.result.member.card.MemberCardResultVO;
import top.kx.kxss.base.vo.result.payment.BasePaymentTypeResultVO;
import top.kx.kxss.base.vo.result.product.BaseProductResultVO;
import top.kx.kxss.base.vo.result.service.BaseServiceResultVO;
import top.kx.kxss.base.vo.result.table.BaseTableInfoResultVO;
import top.kx.kxss.base.vo.result.thail.BaseThailResultVO;
import top.kx.kxss.base.vo.save.member.card.MemberCardEquityApplySaveVO;
import top.kx.kxss.base.vo.save.member.card.MemberCardEquityExcludeSaveVO;
import top.kx.kxss.base.vo.save.member.card.MemberCardEquitySaveVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.member.MemberCardApi;
import top.kx.kxss.model.constant.EchoDictType;
import top.kx.kxss.model.enumeration.BizCacheEnum;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.model.enumeration.base.*;
import top.kx.kxss.pos.vo.ItemRemarksExtraResultVO;
import top.kx.kxss.pos.vo.OrderRemarksExtraResultVO;
import top.kx.kxss.pos.vo.OrderRemarksResultVO;
import top.kx.kxss.system.entity.system.DefDict;
import top.kx.kxss.system.service.system.DefDictService;
import top.kx.kxss.wxapp.service.excel.ExcelReportService;
import top.kx.kxss.wxapp.service.excel.TableColsService;
import top.kx.kxss.wxapp.service.excel.TableDataService;
import top.kx.kxss.wxapp.service.mapper.StatisticsMapper;
import top.kx.kxss.wxapp.service.statistics.CustomService;
import top.kx.kxss.wxapp.service.statistics.StatisticsService;
import top.kx.kxss.wxapp.vo.query.common.OrgIdListQuery;
import top.kx.kxss.wxapp.vo.query.statistics.*;
import top.kx.kxss.wxapp.vo.result.statistics.ColumnVO;
import top.kx.kxss.wxapp.vo.result.statistics.ProductOutboundDetailsResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.ProductStockTypeResultVO;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@DS(DsConstant.BASE_TENANT)
public class StatisticsServiceImpl implements StatisticsService {

    @Autowired
    private StatisticsMapper statisticsMapper;

    @Autowired
    private PosCashServiceService posCashService;

    @Autowired
    private BaseTableInfoService baseTableInfoService;
    @Autowired
    private ExcelReportService excelReportService;
    @Autowired
    private TableDataService tableDataService;
    @Autowired
    private TableColsService tableColsService;

    @Autowired
    private BasePaymentTypeService basePaymentTypeService;

    @Autowired
    private PosCashPaymentService posCashPaymentService;
    @Autowired
    private CustomService customService;
    @Autowired
    private BaseThailService baseThailService;
    @Autowired
    private BaseDictService baseDictService;
    @Autowired
    private BaseTableAreaService baseTableAreaService;
    @Autowired
    private BaseEmployeeService baseEmployeeService;
    @Autowired
    private BizCacheService bizCacheService;
    @Autowired
    private BaseServiceService baseServiceService;
    @Autowired
    private BaseProductService baseProductsService;
    @Autowired
    private BaseProductManager baseProductManager;
    @Autowired
    private PosCashTableService posCashTableService;
    @Autowired
    private top.kx.kxss.app.service.cash.service.PosCashServiceService posCashServiceService;

    @Autowired
    private top.kx.kxss.app.service.cash.PosCashServiceService cashServiceService;
    @Autowired
    private PosCashProductService posCashProductService;
    @Autowired
    private PosCashThailService posCashThailService;
    @Autowired
    private MemberCardApi memberCardApi;

    @Autowired
    private DefDictService defDictService;
    @Autowired
    private PosCashCommenterService posCashCommenterService;
    @Autowired
    private MemberInfoService memberInfoService;

    @Autowired
    private EchoService echoService;
    @Autowired
    private BaseParameterService baseParameterService;
    @Autowired
    private BaseGroupService baseGroupService;
    @Autowired
    private BaseOrgService baseOrgService;


    @Override
    public Map<String, Object> serviceDetails(PageParams<ServiceDetailsQuery> params) {
        params.setSort(null);
        params.setOrder(null);
        ServiceDetailsQuery model = params.getModel();
        initOrgIdList(model);

        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(model);
        }
        IPage<Map<String, Object>> pageList = statisticsMapper.serviceDetailsPage(params.buildPage(Map.class), model);
        if (CollUtil.isNotEmpty(pageList.getRecords())) {
            // 添加组名
            addGroupNames(pageList.getRecords());
            // 批量填充orgName、serviceName、tableName、bdName
            fillNames(pageList.getRecords());
            List<ItemRemarksExtraResultVO> itemRemarksExtraResultVOS = new ArrayList<>();
            pageList.getRecords().forEach(item -> {
                if (Objects.nonNull(item) && Objects.nonNull(item.get("remarks")) && StringUtils.isNotBlank(item.get("remarks").toString()) && Objects.nonNull(item.get("id"))) {
                    ItemRemarksExtraResultVO itemRemarksExtraResultVO = JSON.parseObject(item.get("remarks").toString(), ItemRemarksExtraResultVO.class);
                    if (Objects.nonNull(itemRemarksExtraResultVO)) {
                        itemRemarksExtraResultVO.setId((long) item.get("id"));
                        itemRemarksExtraResultVOS.add(itemRemarksExtraResultVO);
                    }
                }
            });
            echoService.action(itemRemarksExtraResultVOS);
            Map<Long, String> itemTagsMap = itemRemarksExtraResultVOS.stream().collect(Collectors.toMap(ItemRemarksExtraResultVO::getId, v -> {
                String itemTags = v.getEchoMap().get("itemTags") != null ? v.getEchoMap().get("itemTags").toString() : "";
                String remarks = v.getRemarks() != null ? v.getRemarks() : "";
                if (StrUtil.isNotBlank(itemTags) && StrUtil.isNotBlank(remarks)) {
                    return itemTags + "," + remarks;
                } else if (StrUtil.isNotBlank(itemTags)) {
                    return itemTags;
                } else if (StrUtil.isNotBlank(remarks)) {
                    return remarks;
                } else {
                    return "-";
                }
            }));

            ServiceStaffTimeEnum serviceStaffTimeEnum = baseParameterService.getDefaultServiceStaffTime();
            pageList.getRecords().forEach(item -> {
                if (Objects.nonNull(item)) {
                    if (Objects.nonNull(item.get("cashThailId"))) {
                        item.put("amount", "-");
                        item.put("refundAmount", "-");
                        item.put("income", "-");
                    }
                    if (Objects.nonNull(item.get("id"))) {
                        item.put("remarks", itemTagsMap.get((long) item.get("id")));
                    }
                    // 服务时长
                    if (Objects.nonNull(item.get("duration"))) {
                        item.put("durationDesc", cashServiceService.serviceDurationDesc(new BigDecimal(item.get("duration").toString()).intValue(), serviceStaffTimeEnum));
                    }
                    if (Objects.nonNull(item.get("chargingDuration"))) {
                        item.put("chargingDurationDesc", cashServiceService.serviceDurationDesc(new BigDecimal(item.get("chargingDuration").toString()).intValue(), serviceStaffTimeEnum));
                    }
                }
            });
        }
        // 设置表头，补充groupName列
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("pcCode").label("订单尾号")
                        .width(120).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("createTime").label("订单开始时间")
                        .width(160).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("completeTime").label("订单结束时间")
                        .width(160).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("employeeName").label("姓名")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("name").label("真实姓名")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("groupName").label("组名")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableName").label("台桌")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceName").label("服务项目")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("bdName").label("点钟方式")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("refundAmount").label("退款金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("income").label("服务收入")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("durationDesc").label("服务时长")
                        .width(140).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("chargingDurationDesc").label("计费时长")
                        .width(140).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("cycle").label("计费周期")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("remarks").label("备注")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orgName").label("门店")
                        .width(200).emptyString("-").fixed(false).build()
        );
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public Map<String, Object> serviceDetailsSum(ServiceDetailsQuery params) {

        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        Map<String, Object> stringObjectMap = statisticsMapper.serviceDetailsSum(params);
        ServiceStaffTimeEnum serviceStaffTimeEnum = baseParameterService.getDefaultServiceStaffTime();
        if (Objects.nonNull(stringObjectMap.get("duration"))) {
            stringObjectMap.put("durationDesc", cashServiceService.serviceDurationDesc(new BigDecimal(stringObjectMap.get("duration").toString()).intValue(), serviceStaffTimeEnum));
        }
        if (Objects.nonNull(stringObjectMap.get("chargingDuration"))) {
            stringObjectMap.put("chargingDurationDesc", cashServiceService.serviceDurationDesc(new BigDecimal(stringObjectMap.get("chargingDuration").toString()).intValue(), serviceStaffTimeEnum));
        }
        return stringObjectMap;
    }

    @Override
    public void serviceDetailsExport(ServiceDetailsQuery params, HttpServletResponse response) {
        ServiceStaffTimeEnum serviceStaffTimeEnum = baseParameterService.getDefaultServiceStaffTime();
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        List<Map<String, Object>> resultVOList = statisticsMapper.serviceDetailsExport(params);
        if (CollUtil.isNotEmpty(resultVOList)) {
            // 添加组名
            addGroupNames(resultVOList);
            // 批量填充orgName、serviceName、tableName、bdName
            fillNames(resultVOList);
            List<ItemRemarksExtraResultVO> itemRemarksExtraResultVOS = new ArrayList<>();
            resultVOList.forEach(item -> {
                if (Objects.nonNull(item) && Objects.nonNull(item.get("remarks")) && StringUtils.isNotBlank(item.get("remarks").toString()) && Objects.nonNull(item.get("id"))) {
                    ItemRemarksExtraResultVO itemRemarksExtraResultVO = JSON.parseObject(item.get("remarks").toString(), ItemRemarksExtraResultVO.class);
                    if (Objects.nonNull(itemRemarksExtraResultVO)) {
                        itemRemarksExtraResultVO.setId((long) item.get("id"));
                        itemRemarksExtraResultVOS.add(itemRemarksExtraResultVO);
                    }
                }
            });
            echoService.action(itemRemarksExtraResultVOS);
            Map<Long, String> itemTagsMap = itemRemarksExtraResultVOS.stream().collect(Collectors.toMap(ItemRemarksExtraResultVO::getId, v -> {
                String itemTags = v.getEchoMap().get("itemTags") != null ? v.getEchoMap().get("itemTags").toString() : "";
                String remarks = v.getRemarks() != null ? v.getRemarks() : "";
                if (StrUtil.isNotBlank(itemTags) && StrUtil.isNotBlank(remarks)) {
                    return itemTags + "," + remarks;
                } else if (StrUtil.isNotBlank(itemTags)) {
                    return itemTags;
                } else if (StrUtil.isNotBlank(remarks)) {
                    return remarks;
                } else {
                    return "-";
                }
            }));
            resultVOList.forEach(item -> {
                if (Objects.nonNull(item)) {
                    if (Objects.nonNull(item.get("cashThailId"))) {
                        item.put("amount", "-");
                        item.put("refundAmount", "-");
                        item.put("income", "-");
                    }
                    if (Objects.nonNull(item.get("id"))) {
                        item.put("remarks", itemTagsMap.get((long) item.get("id")));
                    }
                    if (Objects.nonNull(item.get("duration"))) {
                        item.put("durationDesc", cashServiceService.serviceDurationExportDesc(new BigDecimal(item.get("duration").toString()).intValue(), serviceStaffTimeEnum));
                    }
                    if (Objects.nonNull(item.get("chargingDuration"))) {
                        item.put("chargingDurationDesc", cashServiceService.serviceDurationExportDesc(new BigDecimal(item.get("chargingDuration").toString()).intValue(), serviceStaffTimeEnum));
                    }
                }
            });
        }
        // 设置表头，补充groupName列
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("pcCode").label("订单尾号")
                        .width(120).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("createTime").label("订单开始时间")
                        .width(160).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("completeTime").label("订单结束时间")
                        .width(160).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("employeeName").label("姓名")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("name").label("真实姓名")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("groupName").label("组名")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableName").label("台桌")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceName").label("服务项目")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("bdName").label("点钟方式")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("refundAmount").label("退款金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("income").label("服务收入")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("durationDesc").label("服务时长")
                        .width(140).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("chargingDurationDesc").label("计费时长")
                        .width(140).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("cycle").label("计费周期")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("remarks").label("备注")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orgName").label("门店")
                        .width(200).emptyString("-").fixed(false).build()
        );
        export(response, resultVOList, columnVOList, "服务明细");
    }

    @Override
    public Map<String, Object> serviceStatistics(PageParams<ServiceDetailsQuery> params) {
        params.setSort(null);
        params.setOrder(null);
        ServiceDetailsQuery model = params.getModel();
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(model);
        }
        QueryWrapper<PosCashService> wrapper = getServiceStatisticsWrapper(model);

        IPage<Map<String, Object>> pageList = statisticsMapper.serviceStatistics(params.buildPage(PosCashService.class), wrapper);
        if (CollUtil.isNotEmpty(pageList.getRecords())) {
            ServiceStaffTimeEnum defaultServiceStaffTime = baseParameterService.getDefaultServiceStaffTime();
            // 添加组名
            addGroupNames(pageList.getRecords());
            // 批量填充orgName、serviceName、tableName、bdName
            fillNames(pageList.getRecords());
            pageList.getRecords().forEach(map -> {
                Object duration = map.get("duration");
                if (Objects.nonNull(duration)) {
                    map.put("durationDesc", cashServiceService.serviceDurationDesc(new BigDecimal(duration.toString()).intValue(), defaultServiceStaffTime));
                }
                Object chargingDuration = map.get("chargingDuration");
                if (Objects.nonNull(chargingDuration)) {
                    map.put("chargingDurationDesc", cashServiceService.serviceDurationDesc(new BigDecimal(chargingDuration.toString()).intValue(), defaultServiceStaffTime));
                }
            });
        }
        List<ColumnVO> columnVOList = getServiceStatisticsColumnList(model);
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public Map<String, Object> serviceStatisticsSum(ServiceDetailsQuery params) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        QueryWrapper<PosCashService> wrapper = new QueryWrapper<>();
        wrapper.eq("pcs.delete_flag", 0)
                .eq("pc.delete_flag", 0)
                .in("pc.bill_state", PosCashBillStateEnum.COMPLETE.getCode(), PosCashBillStateEnum.PART_REFUND.getCode())
                .in("pc.bill_type", PosCashBillTypeEnum.REGULAR_SINGLE.getCode(), PosCashBillTypeEnum.REGISTRATION.getCode(), PosCashBillTypeEnum.COUNTER_CHECKOUT.getCode())
                .in("pcs.status", "0", "1")
                .between("pc.complete_time", params.getStartDate(), params.getEndDate());
        if (StringUtils.isNotBlank(params.getKeyword())) {
            wrapper.and(wrap -> wrap
                    .like("be.real_name", params.getKeyword())
                    .or().like("be.name", params.getKeyword()));
        }
        if (Objects.nonNull(params.getId())) {
            wrapper.eq("pcs.service_id", params.getId());
        }
        if (Objects.nonNull(params.getServiceId())) {
            wrapper.eq("pcs.service_id", params.getServiceId());
        }
        if (CollUtil.isNotEmpty(params.getOrgIdList())) {
            wrapper.in("pc.org_id", params.getOrgIdList());
        }
        if (CollUtil.isNotEmpty(params.getGroupIds())) {
            wrapper.in("be.group_id", params.getGroupIds());
        }
        Map<String, Object> stringObjectMap = statisticsMapper.serviceStatisticsSum(wrapper);
        if (CollUtil.isEmpty(stringObjectMap)) {
            return stringObjectMap;
        }
        ServiceStaffTimeEnum serviceStaffTimeEnum = baseParameterService.getDefaultServiceStaffTime();
        if (Objects.nonNull(stringObjectMap.get("duration"))) {
            stringObjectMap.put("durationDesc", cashServiceService.serviceDurationDesc(new BigDecimal(stringObjectMap.get("duration").toString()).intValue(), serviceStaffTimeEnum));
        }
        if (Objects.nonNull(stringObjectMap.get("chargingDuration"))) {
            stringObjectMap.put("chargingDurationDesc", cashServiceService.serviceDurationDesc(new BigDecimal(stringObjectMap.get("chargingDuration").toString()).intValue(), serviceStaffTimeEnum));
        }
        return stringObjectMap;
    }

    @NotNull
    private static QueryWrapper<PosCashService> getServiceStatisticsWrapper(ServiceDetailsQuery params) {
        QueryWrapper<PosCashService> wrapper = new QueryWrapper<>();
        wrapper.eq("pcs.delete_flag", 0)
                .eq("pc.delete_flag", 0)
                .in("pc.bill_state", PosCashBillStateEnum.COMPLETE.getCode(), PosCashBillStateEnum.PART_REFUND.getCode())
                .in("pc.bill_type", PosCashBillTypeEnum.REGULAR_SINGLE.getCode(), PosCashBillTypeEnum.REGISTRATION.getCode(), PosCashBillTypeEnum.COUNTER_CHECKOUT.getCode())
                .in("pcs.status", "0", "1")
                .between("pc.complete_time", params.getStartDate(), params.getEndDate());
        if (StringUtils.isNotBlank(params.getKeyword())) {
            wrapper.and(wrap -> wrap
                    .like("be.real_name", params.getKeyword())
                    .or().like("be.name", params.getKeyword()));
        }
        if (Objects.nonNull(params.getId())) {
            wrapper.eq("pcs.service_id", params.getId());
        }
        if (Objects.nonNull(params.getServiceId())) {
            wrapper.eq("pcs.service_id", params.getServiceId());
        }
        if (CollUtil.isNotEmpty(params.getOrgIdList())) {
            wrapper.in("pc.org_id", params.getOrgIdList());
        }
        if (CollUtil.isNotEmpty(params.getGroupIds())) {
            wrapper.in("be.group_id", params.getGroupIds());
        }

        switch (params.getSummaryType()) {
            case 1:
                wrapper.groupBy("pcs.employee_id", "pcs.service_id")
                        .orderByAsc("pcs.employee_id");
                break;
            case 2:
                wrapper.groupBy("pcs.employee_id", "pcs.service_id", "pcs.clock_type")
                        .orderByAsc("pcs.employee_id", "pcs.service_id", "pcs.clock_type");
                break;
            case 3:
                wrapper.groupBy("pcs.employee_id")
                        .orderByAsc("pcs.employee_id");
                break;
        }
        return wrapper;
    }

    @Override
    public void serviceStatisticsExport(ServiceDetailsQuery params, HttpServletResponse response) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        QueryWrapper<PosCashService> wrapper = getServiceStatisticsWrapper(params);
        ServiceStaffTimeEnum serviceStaffTimeEnum = baseParameterService.getDefaultServiceStaffTime();
        List<Map<String, Object>> resultVOList = statisticsMapper.serviceStatisticsExport(wrapper);
        if (CollUtil.isNotEmpty(resultVOList)) {
            // 添加组名
            addGroupNames(resultVOList);
            // 批量填充orgName、serviceName、tableName、bdName
            fillNames(resultVOList);
            resultVOList.forEach(map -> {
                Object duration = map.get("duration");
                if (Objects.nonNull(duration)) {
                    map.put("durationDesc", cashServiceService.serviceDurationExportDesc(new BigDecimal(duration.toString()).intValue(), serviceStaffTimeEnum));
                }
                Object chargingDuration = map.get("chargingDuration");
                if (Objects.nonNull(chargingDuration)) {
                    map.put("chargingDurationDesc", cashServiceService.serviceDurationExportDesc(new BigDecimal(chargingDuration.toString()).intValue(), serviceStaffTimeEnum));
                }
            });
        }
        // 设置表头
        List<ColumnVO> columnVOList = getServiceStatisticsColumnList(params);


        Map<String, Object> sumMap = new HashMap<>();
        sumMap.put("completeTime", "合计");
        // 应收金额
        sumMap.put("amount", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("amount") != null && !StringUtils.equals(map.get("amount").toString(), "-"))
                .mapToDouble(map -> Double.parseDouble(map.get("amount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));
        sumMap.put("refundAmount", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("refundAmount") != null && !StringUtils.equals(map.get("refundAmount").toString(), "-"))
                .mapToDouble(map -> Double.parseDouble(map.get("refundAmount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));
        sumMap.put("income", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("income") != null && !StringUtils.equals(map.get("income").toString(), "-"))
                .mapToDouble(map -> Double.parseDouble(map.get("income").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));
        // 持续时长
        sumMap.put("durationDesc", cashServiceService.serviceDurationExportDesc(resultVOList.stream()
                .filter(map -> map.get("duration") != null)
                .mapToInt(map -> (int) Double.parseDouble(map.get("duration").toString()))
                .sum(), serviceStaffTimeEnum));
        sumMap.put("chargingDurationDesc", cashServiceService.serviceDurationExportDesc(resultVOList.stream()
                .filter(map -> map.get("chargingDuration") != null)
                .mapToInt(map -> (int) Double.parseDouble(map.get("chargingDuration").toString()))
                .sum(), serviceStaffTimeEnum));
        resultVOList.add(sumMap);
        export(response, resultVOList, columnVOList, "服务统计");
    }

    @NotNull
    private static List<ColumnVO> getServiceStatisticsColumnList(ServiceDetailsQuery params) {
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("employeeName").label("姓名")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("真实姓名")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("groupName").label("组名")
                        .width(100).emptyString("-").fixed(false).build());
        //1-服务项目，2-点钟方式，3-服务人员
        switch (params.getSummaryType()) {
            case 1:
                columnVOList.add(ColumnVO.builder().name("serviceName").label("服务项目")
                        .width(100).emptyString("-").fixed(false).build());
                columnVOList.add(ColumnVO.builder().name("durationDesc").label("服务时长")
                        .width(100).emptyString("-").fixed(false).build());
                columnVOList.add(ColumnVO.builder().name("chargingDurationDesc").label("计费时长")
                        .width(100).emptyString("-").fixed(false).build());
                break;
            case 2:
                columnVOList.add(ColumnVO.builder().name("serviceName").label("服务项目")
                        .width(100).emptyString("-").fixed(false).build());
                columnVOList.add(ColumnVO.builder().name("durationDesc").label("服务时长")
                        .width(100).emptyString("-").fixed(false).build());
                columnVOList.add(ColumnVO.builder().name("chargingDurationDesc").label("计费时长")
                        .width(100).emptyString("-").fixed(false).build());
                columnVOList.add(ColumnVO.builder().name("clockTypeName").label("点钟方式")
                        .width(100).emptyString("-").fixed(false).build());
                break;
            case 3:
                columnVOList.add(ColumnVO.builder().name("durationDesc").label("服务时长")
                        .width(100).emptyString("-").fixed(false).build());
                columnVOList.add(ColumnVO.builder().name("chargingDurationDesc").label("计费时长")
                        .width(100).emptyString("-").fixed(false).build());
                break;
            default:
                ArgumentAssert.isTrue(false, "暂不支持此汇总方式");
        }
        columnVOList.add(ColumnVO.builder().name("amount").label("金额")
                .width(100).emptyString("-").fixed(false).build());
        columnVOList.add(ColumnVO.builder().name("refundAmount").label("退款金额")
                .width(100).emptyString("-").fixed(false).build());
        columnVOList.add(ColumnVO.builder().name("income").label("服务收入")
                .width(100).emptyString("-").fixed(false).build());
        columnVOList.add(ColumnVO.builder().name("num").label("服务次数")
                .width(100).emptyString("-").fixed(false).build());
        columnVOList.add(ColumnVO.builder().name("orgName").label("门店")
                .width(200).emptyString("-").fixed(false).build());
        return columnVOList;
    }

    @Override
    public Map<String, Object> serviceStatisticsSummary(PageParams<ServiceDetailsQuery> params) {
        params.setSort(null);
        params.setOrder(null);
        ServiceDetailsQuery model = params.getModel();
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(model);
        }
        ServiceStaffTimeEnum serviceStaffTimeEnum = baseParameterService.getDefaultServiceStaffTime();
        IPage<Map<String, Object>> pageList = statisticsMapper.serviceStatisticsSummary(params.buildPage(Map.class), model);
        if (CollUtil.isNotEmpty(pageList.getRecords())) {
            // 添加组名
            addGroupNames(pageList.getRecords());
            // 批量填充orgName、serviceName、tableName、bdName
            fillNames(pageList.getRecords());
            pageList.getRecords().forEach(map -> {
                Object duration = map.get("duration");
                if (Objects.nonNull(duration)) {
                    map.put("durationDesc", cashServiceService.serviceDurationDesc(new BigDecimal(duration.toString()).intValue(), serviceStaffTimeEnum));
                }
                Object chargingDuration = map.get("chargingDuration");
                if (Objects.nonNull(chargingDuration)) {
                    map.put("chargingDurationDesc", cashServiceService.serviceDurationDesc(new BigDecimal(chargingDuration.toString()).intValue(), serviceStaffTimeEnum));
                }
            });
        }
        // 设置表头（去掉completeTime字段）
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("employeeName").label("姓名")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("真实姓名")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("groupName").label("组名")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceName").label("服务项目")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("durationDesc").label("服务时长")
                        .width(140).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("chargingDurationDesc").label("计费时长")
                        .width(140).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("refundAmount").label("退款金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("income").label("服务收入")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("服务次数")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orgName").label("门店")
                        .width(200).emptyString("-").fixed(false).build()
        );
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public Map<String, Object> serviceStatisticsSummarySum(ServiceDetailsQuery params) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        Map<String, Object> stringObjectMap = statisticsMapper.serviceStatisticsSummarySum(params);
        ServiceStaffTimeEnum serviceStaffTimeEnum = baseParameterService.getDefaultServiceStaffTime();
        if (Objects.nonNull(stringObjectMap.get("duration"))) {
            stringObjectMap.put("durationDesc", cashServiceService.serviceDurationDesc(new BigDecimal(stringObjectMap.get("duration").toString()).intValue(), serviceStaffTimeEnum));
        }
        if (Objects.nonNull(stringObjectMap.get("chargingDuration"))) {
            stringObjectMap.put("chargingDurationDesc", cashServiceService.serviceDurationDesc(new BigDecimal(stringObjectMap.get("chargingDuration").toString()).intValue(), serviceStaffTimeEnum));
        }
        return stringObjectMap;
    }

    @Override
    public void serviceStatisticsSummaryExport(ServiceDetailsQuery params, HttpServletResponse response) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        ServiceStaffTimeEnum serviceStaffTimeEnum = baseParameterService.getDefaultServiceStaffTime();
        List<Map<String, Object>> resultVOList = statisticsMapper.serviceStatisticsSummaryExport(params);
        if (CollUtil.isNotEmpty(resultVOList)) {
            // 添加组名
            addGroupNames(resultVOList);

            resultVOList.forEach(map -> {
                Object duration = map.get("duration");
                if (Objects.nonNull(duration)) {
                    map.put("durationDesc", cashServiceService.serviceDurationExportDesc(new BigDecimal(duration.toString()).intValue(), serviceStaffTimeEnum));
                }
                Object chargingDuration = map.get("chargingDuration");
                if (Objects.nonNull(chargingDuration)) {
                    map.put("chargingDurationDesc", cashServiceService.serviceDurationExportDesc(new BigDecimal(chargingDuration.toString()).intValue(), serviceStaffTimeEnum));
                }
            });
        }
        // 设置表头（去掉completeTime字段）
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("employeeName").label("姓名")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("真实姓名")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("groupName").label("组名")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceName").label("服务项目")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("durationDesc").label("服务时长")
                        .width(140).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("chargingDurationDesc").label("计费时长")
                        .width(140).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("refundAmount").label("退款金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("income").label("服务收入")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("服务次数")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orgName").label("门店")
                        .width(200).emptyString("-").fixed(false).build()
        );

        Map<String, Object> sumMap = new HashMap<>();
        sumMap.put("employeeName", "合计");
        // 应收金额
        sumMap.put("amount", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("amount") != null)
                .mapToDouble(map -> Double.parseDouble(map.get("amount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));
        sumMap.put("refundAmount", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("refundAmount") != null)
                .mapToDouble(map -> Double.parseDouble(map.get("refundAmount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));
        sumMap.put("income", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("income") != null)
                .mapToDouble(map -> Double.parseDouble(map.get("income").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));
        // 持续时长
        sumMap.put("durationDesc", cashServiceService.serviceDurationExportDesc(resultVOList.stream()
                .filter(map -> map.get("duration") != null)
                .mapToInt(map -> (int) Double.parseDouble(map.get("duration").toString()))
                .sum(), serviceStaffTimeEnum));
        sumMap.put("chargingDurationDesc", cashServiceService.serviceDurationExportDesc(resultVOList.stream()
                .filter(map -> map.get("chargingDuration") != null)
                .mapToInt(map -> (int) Double.parseDouble(map.get("chargingDuration").toString()))
                .sum(), serviceStaffTimeEnum));
        // 服务次数
        sumMap.put("num", resultVOList.stream()
                .filter(map -> map.get("num") != null)
                .mapToInt(map -> Integer.parseInt(map.get("num").toString()))
                .sum());
        resultVOList.add(sumMap);
        export(response, resultVOList, columnVOList, "服务汇总统计");
    }

    @Override
    public Map<String, Object> serviceAmount(PageParams<ServiceAmountQuery> params) {

        ServiceAmountQuery model = params.getModel();

        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(model);
        }

        initOrgIdList(model);

        params.setSort(null);
        params.setOrder(null);
        IPage<Map<String, Object>> pageList = statisticsMapper.serviceAmount(params.buildPage(Map.class), model);
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("employeeName").label("姓名")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("amount").label("服务金额")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("num").label("服务次数")
                        .width(100).emptyString("-").fixed(true).build()
        );
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public Map<String, Object> serviceAmountSum(ServiceAmountQuery params) {
        initOrgIdList(params);

        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }

        return statisticsMapper.serviceAmountSum(params);
    }

    @Override
    public void serviceAmountExport(ServiceAmountQuery params, HttpServletResponse response) {

        initOrgIdList(params);

        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }

        List<Map<String, Object>> resultVOList = statisticsMapper.serviceAmountExport(params);
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("employeeName").label("姓名")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("amount").label("服务金额")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("num").label("服务次数")
                        .width(100).emptyString("-").fixed(false).build()
        );

        Map<String, Object> sumMap = new HashMap<>();
        sumMap.put("employeeName", "合计");
        // 应收金额
        sumMap.put("amount", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("amount") != null)
                .mapToDouble(map -> Double.parseDouble(map.get("amount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));
        // 持续时长
        sumMap.put("num", resultVOList.stream()
                .filter(map -> map.get("num") != null)
                .mapToInt(map -> Integer.parseInt(map.get("num").toString()))
                .sum());

        resultVOList.add(sumMap);
        export(response, resultVOList, columnVOList, "服务金额");

    }

    @Override
    public Map<String, Object> paymentDetails(PageParams<PaymentDetailsQuery> params) {
        PaymentDetailsQuery model = params.getModel();
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(params.getModel());
        }

        params.setSort(null);
        params.setOrder(null);
        IPage<Map<String, Object>> pageList = statisticsMapper.paymentDetails(params.buildPage(Map.class), model);
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("code").label("订单")
                        .width(200).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("createdTime").label("订单创建时间")
                        .width(160).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("completeTime").label("订单完成时间")
                        .width(160).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payTime").label("支付时间")
                        .width(160).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payName").label("支付方式")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("paidIn").label("实收金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("收款金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("refundAmount").label("退款金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("rechargeAmount").label("本金")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("giftAmount").label("赠金")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orderId").label("聚合流水号")
                        .width(160).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(200).emptyString("-").fixed(false).build()
        );
        for (Map<String, Object> record : pageList.getRecords()) {
            BigDecimal amount = new BigDecimal(record.get("amount").toString());
            BigDecimal refundAmount = new BigDecimal(record.get("refundAmount").toString());
            BigDecimal paidIn = new BigDecimal(record.get("paidIn").toString());
            record.put("amount", amount.setScale(2, RoundingMode.HALF_UP).toPlainString());
            record.put("refundAmount", refundAmount.setScale(2, RoundingMode.HALF_UP).toPlainString());
            record.put("paidIn", paidIn.setScale(2, RoundingMode.HALF_UP).toPlainString());
        }
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public Map<String, Object> paymentDetailsSum(PaymentDetailsQuery params) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }

        return statisticsMapper.paymentDetailsSum(params);
    }

    @Override
    public void paymentDetailsExport(PaymentDetailsQuery params, HttpServletResponse response) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }

        List<Map<String, Object>> resultVOList = statisticsMapper.paymentDetailsExport(params);
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("code").label("订单")
                        .width(200).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("createdTime").label("订单创建时间")
                        .width(160).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("completeTime").label("订单完成时间")
                        .width(160).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payTime").label("支付时间")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payName").label("支付方式")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("paidIn").label("实收金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("收款金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("refundAmount").label("退款金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("rechargeAmount").label("本金")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("giftAmount").label("赠金")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orderId").label("聚合流水号")
                        .width(160).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(200).emptyString("-").fixed(false).build()
        );

        Map<String, Object> sumMap = new HashMap<>();
        sumMap.put("code", "合计");
        BigDecimal amount = BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("amount") != null && !StringUtils.equals(map.get("amount").toString(), "-"))
                .mapToDouble(map -> Double.parseDouble(map.get("amount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP);
        BigDecimal refundAmount = BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("refundAmount") != null && !StringUtils.equals(map.get("refundAmount").toString(), "-"))
                .mapToDouble(map -> Double.parseDouble(map.get("refundAmount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP);
        sumMap.put("paidIn", amount.subtract(refundAmount).setScale(2, RoundingMode.HALF_UP));
        sumMap.put("amount", amount.setScale(2, RoundingMode.HALF_UP));
        sumMap.put("refundAmount", refundAmount.setScale(2, RoundingMode.HALF_UP));
        sumMap.put("rechargeAmount", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("rechargeAmount") != null && !StringUtils.equals(map.get("rechargeAmount").toString(), "-"))
                .mapToDouble(map -> Double.parseDouble(map.get("rechargeAmount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));
        sumMap.put("giftAmount", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("giftAmount") != null && !StringUtils.equals(map.get("giftAmount").toString(), "-"))
                .mapToDouble(map -> Double.parseDouble(map.get("giftAmount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));

        resultVOList.add(sumMap);
        export(response, resultVOList, columnVOList, "支付方式明细");
    }

    @Override
    public Map<String, Object> memberRechargeDetails(PageParams<MemberDetailsQuery> params) {
        MemberDetailsQuery model = params.getModel();
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(model);
        }

        params.setSort(null);
        params.setOrder(null);
        IPage<Map<String, Object>> pageList = statisticsMapper.memberRechargeDetails(params.buildPage(Map.class), model);
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("id").label("会员编号")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("会员名称")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("mobile").label("会员手机号")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("gradeName").label("会员等级名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeTime").label("时间")
                        .width(160).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("实收金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("giftAmount").label("赠送金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payType").label("充值类型")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payName").label("支付方式")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("employeeName").label("销售员工")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createdEmp").label("操作员工")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(200).emptyString("-").fixed(false).build()
        );
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public Map<String, Object> memberRechargeDetailsSum(MemberDetailsQuery params) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        return statisticsMapper.memberRechargeDetailsSum(params);
    }

    @Override
    public void memberRechargeDetailsExport(MemberDetailsQuery params, HttpServletResponse response) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        List<Map<String, Object>> resultVOList = statisticsMapper.memberRechargeDetailsExport(params);
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("id").label("会员编号")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("会员名称")
                        .width(120).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("mobile").label("会员手机号")
                        .width(120).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("gradeName").label("会员等级名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeTime").label("时间")
                        .width(160).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("实收金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("giftAmount").label("赠送金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payType").label("充值类型")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payName").label("支付方式")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("employeeName").label("销售员工")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createdEmp").label("操作员工")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(200).emptyString("-").fixed(false).build()
        );

        Map<String, Object> sumMap = new HashMap<>();
        sumMap.put("id", "合计");
        sumMap.put("amount", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("amount") != null && map.get("amount") != "-")
                .mapToDouble(map -> Double.parseDouble(map.get("amount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));
        sumMap.put("giftAmount", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("giftAmount") != null && map.get("giftAmount") != "-")
                .mapToDouble(map -> Double.parseDouble(map.get("giftAmount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));

        resultVOList.add(sumMap);
        export(response, resultVOList, columnVOList, "会员充值");
    }

    @Override
    public Map<String, Object> memberConsume(PageParams<MemberDetailsQuery> params) {
        MemberDetailsQuery model = params.getModel();
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(model);
        }

        params.setSort(null);
        params.setOrder(null);
        IPage<Map<String, Object>> pageList = statisticsMapper.memberConsume(params.buildPage(Map.class), model);
        List<PosCashPaymentResultVO> paymentResultVOList = statisticsMapper.memberConsumeAmount(model);
        Map<Long, PosCashPaymentResultVO> consumeAmountMap = new HashMap<>();
        if (CollUtil.isNotEmpty(paymentResultVOList)) {
            consumeAmountMap = paymentResultVOList.stream().collect(Collectors.toMap(PosCashPaymentResultVO::getMemberId, s -> s));
        }
        if (CollUtil.isNotEmpty(pageList.getRecords())) {
            Map<Long, PosCashPaymentResultVO> finalConsumeAmountMap = consumeAmountMap;
            pageList.getRecords().forEach(item -> {
                if (Objects.nonNull(item.get("memberId"))) {
                    PosCashPaymentResultVO posCashPaymentResultVO = finalConsumeAmountMap.get((Long) item.get("memberId"));
                    if (Objects.nonNull(posCashPaymentResultVO)) {
                        item.put("totalPaid", posCashPaymentResultVO.getAmount());
                        item.put("paid", posCashPaymentResultVO.getAmount().subtract(posCashPaymentResultVO.getGiftAmount()).setScale(2, RoundingMode.HALF_UP));
                        item.put("balancePaid", posCashPaymentResultVO.getRechargeAmount().add(posCashPaymentResultVO.getGiftAmount()));
                        item.put("balance", posCashPaymentResultVO.getRechargeAmount());
                    } else {
                        item.put("totalPaid", 0.00);
                        item.put("paid", 0.00);
                        item.put("balancePaid", 0.00);
                        item.put("balance", 0.00);
                    }
                }
            });
        }
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("code").label("编号")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("姓名")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("sex").label("性别")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("mobile").label("手机号")
                        .width(150).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("grade").label("会员等级")
                        .width(180).emptyString("-").fixed(false).build(),
                // 余额支付(含赠金)
                ColumnVO.builder().name("balancePaid").label("余额支付(含赠金)")
                        .width(180).emptyString("-").fixed(false).build(),
                // 余额支付(不含赠金)
                ColumnVO.builder().name("balance").label("余额支付(不含赠金)")
                        .width(100).emptyString("-").fixed(false).build(),

                ColumnVO.builder().name("totalPaid").label("消费总金额(含赠送)")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("paid").label("消费总金额(不含赠送)")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("累计到店次数")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableAmount").label("累计消费台费")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("productAmount").label("累计消费商品")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceAmount").label("累计消费服务")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("thailAmount").label("累计消费套餐")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("buyCardAmount").label("累计消费购卡")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(280).emptyString("-").fixed(false).build()
        );
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public Map<String, Object> memberConsumeSum(MemberDetailsQuery params) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        Map<String, Object> stringObjectMap = statisticsMapper.memberConsumeSum(params);
        if (CollUtil.isEmpty(stringObjectMap)) {
            return MapUtil.newHashMap();
        }
        List<PosCashPaymentResultVO> paymentResultVOList = statisticsMapper.memberConsumeAmount(params);
        BigDecimal amount = paymentResultVOList.stream().map(PosCashPaymentResultVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal recharge = paymentResultVOList.stream().map(PosCashPaymentResultVO::getRechargeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal giftAmount = paymentResultVOList.stream().map(PosCashPaymentResultVO::getGiftAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        stringObjectMap.put("totalPaid", amount);
        stringObjectMap.put("paid", amount.subtract(giftAmount).setScale(2, RoundingMode.HALF_UP));
        stringObjectMap.put("balancePaid", recharge.add(giftAmount));
        stringObjectMap.put("balance", recharge);
        return stringObjectMap;
    }

    @Override
    public void memberConsumeExport(MemberDetailsQuery params, HttpServletResponse response) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }

        List<Map<String, Object>> resultVOList = statisticsMapper.memberConsumeExport(params);
        if (CollUtil.isNotEmpty(resultVOList)) {
            List<PosCashPaymentResultVO> paymentResultVOList = statisticsMapper.memberConsumeAmount(params);
            Map<Long, PosCashPaymentResultVO> consumeAmountMap = new HashMap<>();
            if (CollUtil.isNotEmpty(paymentResultVOList)) {
                consumeAmountMap = paymentResultVOList.stream().collect(Collectors.toMap(PosCashPaymentResultVO::getMemberId, s -> s));
            }
            Map<Long, PosCashPaymentResultVO> finalConsumeAmountMap = consumeAmountMap;
            resultVOList.forEach(item -> {
                if (Objects.nonNull(item.get("memberId"))) {
                    PosCashPaymentResultVO posCashPaymentResultVO = finalConsumeAmountMap.get((Long) item.get("memberId"));
                    if (Objects.nonNull(posCashPaymentResultVO)) {
                        item.put("totalPaid", posCashPaymentResultVO.getAmount());
                        item.put("paid", posCashPaymentResultVO.getAmount().subtract(posCashPaymentResultVO.getGiftAmount()).setScale(2, RoundingMode.HALF_UP));
                        item.put("balancePaid", posCashPaymentResultVO.getRechargeAmount().add(posCashPaymentResultVO.getGiftAmount()));
                        item.put("balance", posCashPaymentResultVO.getRechargeAmount());
                    } else {
                        item.put("totalPaid", 0.00);
                        item.put("paid", 0.00);
                        item.put("balancePaid", 0.00);
                        item.put("balance", 0.00);
                    }
                }
            });
        }
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("code").label("编号")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("姓名")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("sex").label("性别")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("mobile").label("手机号")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("grade").label("会员等级")
                        .width(180).emptyString("-").fixed(false).build(),
                // 余额支付(含赠金)
                ColumnVO.builder().name("balancePaid").label("余额支付(含赠金)")
                        .width(180).emptyString("-").fixed(false).build(),
                // 余额支付(不含赠金)
                ColumnVO.builder().name("balance").label("余额支付(不含赠金)")
                        .width(100).emptyString("-").fixed(false).build(),

                ColumnVO.builder().name("totalPaid").label("消费总金额(含赠送)")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("paid").label("消费总金额(不含赠送)")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("累计到店次数")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableAmount").label("累计消费台费")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("productAmount").label("累计消费商品")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceAmount").label("累计消费服务")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("thailAmount").label("累计消费套餐")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("buyCardAmount").label("累计消费购卡")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(200).emptyString("-").fixed(false).build()
        );

        Map<String, Object> sumMap = new HashMap<>();
        sumMap.put("name", "合计");
        sumMap.put("totalPaid", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("totalPaid") != null && map.get("totalPaid") != "-")
                .mapToDouble(map -> Double.parseDouble(map.get("totalPaid").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));
        sumMap.put("paid", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("paid") != null && map.get("paid") != "-")
                .mapToDouble(map -> Double.parseDouble(map.get("paid").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));
        sumMap.put("num", resultVOList.stream()
                .filter(map -> map.get("num") != null && map.get("num") != "-")
                .mapToInt(map -> Integer.parseInt(map.get("num").toString()))
                .sum());
        sumMap.put("tableAmount", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("tableAmount") != null && map.get("tableAmount") != "-")
                .mapToDouble(map -> Double.parseDouble(map.get("tableAmount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));
        sumMap.put("productAmount", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("productAmount") != null && map.get("productAmount") != "-")
                .mapToDouble(map -> Double.parseDouble(map.get("productAmount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));
        sumMap.put("serviceAmount", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("serviceAmount") != null && map.get("serviceAmount") != "-")
                .mapToDouble(map -> Double.parseDouble(map.get("serviceAmount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));
        sumMap.put("thailAmount", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("thailAmount") != null && map.get("thailAmount") != "-")
                .mapToDouble(map -> Double.parseDouble(map.get("thailAmount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));
        sumMap.put("buyCardAmount", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("buyCardAmount") != null && map.get("buyCardAmount") != "-")
                .mapToDouble(map -> Double.parseDouble(map.get("buyCardAmount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));

        resultVOList.add(sumMap);
        export(response, resultVOList, columnVOList, "会员消费");
    }

    @Override
    public Map<String, Object> memberRecharge(PageParams<MemberDetailsQuery> params) {
        MemberDetailsQuery model = params.getModel();
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(model);
        }

        params.setSort(null);
        params.setOrder(null);
        IPage<Map<String, Object>> pageList = statisticsMapper.memberRecharge(params.buildPage(Map.class), model);
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("name").label("姓名")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("code").label("会员编号")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("sex").label("性别")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("mobile").label("手机号")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("gradeName").label("会员等级")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("累计充值次数")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("totalPayment").label("累计充值金额(含赠送)")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payment").label("累计充值本金")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("giftAmount").label("累计充值赠金")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(200).emptyString("-").fixed(false).build()
        );
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public Map<String, Object> memberRechargeSum(MemberDetailsQuery params) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }

        return statisticsMapper.memberRechargeSum(params);
    }

    @Override
    public void memberRechargeExport(MemberDetailsQuery params, HttpServletResponse response) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }

        List<Map<String, Object>> resultVOList = statisticsMapper.memberRechargeExport(params);
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("name").label("姓名")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("code").label("会员编号")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("sex").label("性别")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("mobile").label("手机号")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("gradeName").label("会员等级")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("累计充值次数")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("totalPayment").label("累计充值金额(含赠送)")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payment").label("累计充值本金")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("giftAmount").label("累计充值赠金")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(200).emptyString("-").fixed(false).build()
        );

        Map<String, Object> sumMap = new HashMap<>();
        sumMap.put("name", "合计");
        sumMap.put("totalPayment", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("totalPayment") != null && map.get("totalPayment") != "-")
                .mapToDouble(map -> Double.parseDouble(map.get("totalPayment").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));
        sumMap.put("num", resultVOList.stream()
                .filter(map -> map.get("num") != null && map.get("num") != "-")
                .mapToInt(map -> Integer.parseInt(map.get("num").toString()))
                .sum());
        sumMap.put("payment", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("payment") != null && map.get("payment") != "-")
                .mapToDouble(map -> Double.parseDouble(map.get("payment").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));
        sumMap.put("giftAmount", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("giftAmount") != null && map.get("giftAmount") != "-")
                .mapToDouble(map -> Double.parseDouble(map.get("giftAmount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));

        resultVOList.add(sumMap);
        export(response, resultVOList, columnVOList, "会员充值统计");
    }

    @Override
    public Map<String, Object> inventoryFlow(PageParams<InventoryFlowQuery> params) {
        InventoryFlowQuery model = params.getModel();
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(model);
        }

        params.setSort(null);
        params.setOrder(null);
        IPage<Map<String, Object>> pageList = statisticsMapper.inventoryFlow(params.buildPage(Map.class), model);
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("createdTime").label("操作时间")
                        .width(160).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("categoryName").label("分类")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("商品名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("typeName").label("类型")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("数量")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("residueNum").label("净库存")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("desc").label("描述")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("billState").label("状态")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("employeeName").label("操作员工")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("code").label("订单号")
                        .width(200).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(200).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("warehouseName").label("仓库")
                        .width(160).emptyString("-").fixed(false).build()
        );
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public Map<String, Object> inventoryFlowSum(InventoryFlowQuery params) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        return statisticsMapper.inventoryFlowSum(params);
    }

    @Override
    public void inventoryFlowExport(InventoryFlowQuery params, HttpServletResponse response) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        List<Map<String, Object>> resultVOList = statisticsMapper.inventoryFlowExport(params);
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("createdTime").label("操作时间")
                        .width(160).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("categoryName").label("分类")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("商品名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("typeName").label("类型")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("数量")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("residueNum").label("净库存")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("desc").label("描述")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("billState").label("状态")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("employeeName").label("操作员工")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("code").label("订单号")
                        .width(200).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(200).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("warehouseName").label("仓库")
                        .width(180).emptyString("-").fixed(false).build()
        );

        Map<String, Object> sumMap = new HashMap<>();
        sumMap.put("createdTime", "合计");
        sumMap.put("num", resultVOList.stream()
                .filter(map -> map.get("num") != null && map.get("num") != "-")
                .mapToInt(map -> Integer.parseInt(map.get("num").toString()))
                .sum());

        resultVOList.add(sumMap);
        export(response, resultVOList, columnVOList, "商品出入库流水");
    }

    @Override
    public Map<String, Object> productOutboundDetails(PageParams<InventoryFlowQuery> params) {
        InventoryFlowQuery model = params.getModel();
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(model);
        }
        QueryWrapper<PosCashProduct> wrapper = getProductOutboundDetailsWrap(model);
        wrapper.groupBy("pro.id").orderByAsc("p.created_time");

        params.setSort(null);
        params.setOrder(null);
        IPage<ProductOutboundDetailsResultVO> pageList = statisticsMapper.productOutboundDetails(params.buildPage(ProductOutboundDetailsResultVO.class), wrapper);
        List<ItemRemarksExtraResultVO> itemRemarksExtraResultVOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(pageList.getRecords())) {
            List<Long> productIds = pageList.getRecords().stream().map(ProductOutboundDetailsResultVO::getProductId).distinct().collect(Collectors.toList());
            List<BaseProductResultVO> productList = baseProductManager.findList(Wraps.<BaseProduct>lbQ().in(SuperEntity::getId, productIds));
            Map<Long, BaseProductResultVO> productMap = CollUtil.isNotEmpty(productList) ?
                    productList.stream().collect(Collectors.toMap(BaseProductResultVO::getId, k -> k)) : new HashMap<>();

            pageList.getRecords().forEach(s-> {
                BaseProductResultVO productResultVO = productMap.get(s.getProductId());
                if (Objects.nonNull(productResultVO)) {
                    s.setCategoryId(productResultVO.getCategoryId());
                    s.setMeasuringUnitKey(productResultVO.getMeasuringUnit());
                    s.setName(productResultVO.getName());
                }
                if (StringUtils.isNotBlank(s.getBeneficiaryEmpIdStr())) {
                    s.setBeneficiaryEmpIdList(Arrays.stream(s.getBeneficiaryEmpIdStr().split(","))
                            .filter(d -> !d.trim().isEmpty())  // 过滤空项
                            .map(String::trim)                              // 去除空格
                            .map(Long::valueOf)                             // 转换为 Long
                            .collect(Collectors.toList()));
                }
                if (StringUtils.isNotBlank(s.getRemarks()) && Objects.nonNull(s.getId())) {
                    ItemRemarksExtraResultVO itemRemarksExtraResultVO = JSON.parseObject(s.getRemarks(), ItemRemarksExtraResultVO.class);
                    if (Objects.nonNull(itemRemarksExtraResultVO)) {
                        itemRemarksExtraResultVO.setId(s.getId());
                        itemRemarksExtraResultVOS.add(itemRemarksExtraResultVO);
                    }
                }
            });
        }
        echoService.action(itemRemarksExtraResultVOS);
        Map<Long, String> itemTagsMap = itemRemarksExtraResultVOS.stream().collect(Collectors.toMap(ItemRemarksExtraResultVO::getId, v -> {
                    String itemTags = v.getEchoMap().get("itemTags") != null ? v.getEchoMap().get("itemTags").toString() : "";
                    String remarks = v.getRemarks() != null ? v.getRemarks() : "";
                    if (StrUtil.isNotBlank(itemTags) && StrUtil.isNotBlank(remarks)) {
                        return itemTags + "," + remarks;
                    } else if (StrUtil.isNotBlank(itemTags)) {
                        return itemTags;
                    } else if (StrUtil.isNotBlank(remarks)) {
                        return remarks;
                    } else {
                        return "-";
                    }
        }));
        echoService.action(pageList.getRecords());
        pageList.getRecords().forEach(s -> {
            if (Objects.nonNull(s.getId())) {
                s.setItemRemark(itemTagsMap.get(s.getId()));
            }
            Map<String, Object> echoMap = s.getEchoMap();
            if (CollUtil.isNotEmpty(echoMap)) {
                if (echoMap.containsKey("categoryId") && Objects.nonNull(echoMap.get("categoryId"))) {
                    s.setCategoryName(echoMap.get("categoryId").toString());
                } else {
                    s.setCategoryName("-");
                }
                if (echoMap.containsKey("measuringUnitKey") && Objects.nonNull(echoMap.get("measuringUnitKey"))) {
                    s.setMeasuringUnit(echoMap.get("measuringUnitKey").toString());
                } else {
                    s.setMeasuringUnit("-");
                }
                if (echoMap.containsKey("createdEmpId") && Objects.nonNull(echoMap.get("createdEmpId"))) {
                    s.setCreatedEmp(echoMap.get("createdEmpId").toString());
                } else {
                    s.setCreatedEmp("-");
                }
                if (echoMap.containsKey("orgId") && Objects.nonNull(echoMap.get("orgId"))) {
                    s.setOrg(echoMap.get("orgId").toString());
                } else {
                    s.setOrg("-");
                }
                if (echoMap.containsKey("warehouseId") && Objects.nonNull(echoMap.get("warehouseId"))) {
                    s.setWarehouseName(echoMap.get("warehouseId").toString());
                } else {
                    s.setWarehouseName("-");
                }
                if (echoMap.containsKey("beneficiaryEmpIdList") && Objects.nonNull(echoMap.get("beneficiaryEmpIdList"))) {
                    s.setBeneficiaryEmp(StrUtil.join(",", JSONUtil.toList(echoMap.get("beneficiaryEmpIdList").toString(), String.class)));
                } else {
                    s.setBeneficiaryEmp("-");
                }
                if (echoMap.containsKey("type") && Objects.nonNull(echoMap.get("type"))) {
                    s.setTypeName(echoMap.get("type").toString());
                } else {
                    s.setTypeName("-");
                }

            }
        });

            // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("createdTime").label("出库时间")
                        .width(160).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("completeTime").label("结账时间")
                        .width(160).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("code").label("单号")
                        .width(200).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("typeName").label("出库类型")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("categoryName").label("分类")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("name").label("商品名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("measuringUnit").label("单位")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("数量")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("price").label("销售单价")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("originPrice").label("销售金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("discountAmount").label("优惠金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("totalPrice").label("销售收入")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("costPrice").label("成本单价")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("totalCostPrice").label("成本金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("profitPrice").label("利润")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("beneficiaryEmp").label("提成员工")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createdEmp").label("操作员工")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(200).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("warehouseName").label("仓库")
                        .width(160).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableName").label("台桌名称")
                        .width(120).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("itemRemark").label("备注")
                        .width(120).emptyString("-").fixed(false).build()
        );
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public ProductOutboundDetailsResultVO productOutboundDetailsSum(InventoryFlowQuery params) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        QueryWrapper<PosCashProduct> wrapper = getProductOutboundDetailsWrap(params);
        return statisticsMapper.productOutboundDetailsSum(wrapper);
    }

    @Override
    public List<ProductOutboundDetailsResultVO> productOutboundDetailsExport(InventoryFlowQuery params, HttpServletResponse response) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        QueryWrapper<PosCashProduct> wrapper = getProductOutboundDetailsWrap(params);
        wrapper.groupBy("pro.id").orderByAsc("p.created_time");
        List<ProductOutboundDetailsResultVO> resultVOList = statisticsMapper.productOutboundDetailsExport(wrapper);
        List<ItemRemarksExtraResultVO> itemRemarksExtraResultVOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(resultVOList)) {
            List<Long> productIds = resultVOList.stream().map(ProductOutboundDetailsResultVO::getProductId).distinct().collect(Collectors.toList());
            List<BaseProductResultVO> productList = baseProductManager.findList(Wraps.<BaseProduct>lbQ().in(SuperEntity::getId, productIds));
            Map<Long, BaseProductResultVO> productMap = CollUtil.isNotEmpty(productList) ?
                    productList.stream().collect(Collectors.toMap(BaseProductResultVO::getId, k -> k)) : new HashMap<>();

            resultVOList.forEach(s-> {
                BaseProductResultVO productResultVO = productMap.get(s.getProductId());
                if (Objects.nonNull(productResultVO)) {
                    s.setCategoryId(productResultVO.getCategoryId());
                    s.setMeasuringUnitKey(productResultVO.getMeasuringUnit());
                    s.setName(productResultVO.getName());
                }
                if (StringUtils.isNotBlank(s.getBeneficiaryEmpIdStr())) {
                    s.setBeneficiaryEmpIdList(Arrays.stream(s.getBeneficiaryEmpIdStr().split(","))
                            .filter(d -> !d.trim().isEmpty())  // 过滤空项
                            .map(String::trim)                              // 去除空格
                            .map(Long::valueOf)                             // 转换为 Long
                            .collect(Collectors.toList()));
                }
                if (StringUtils.isNotBlank(s.getRemarks()) && Objects.nonNull(s.getId())) {
                    ItemRemarksExtraResultVO itemRemarksExtraResultVO = JSON.parseObject(s.getRemarks(), ItemRemarksExtraResultVO.class);
                    if (Objects.nonNull(itemRemarksExtraResultVO)) {
                        itemRemarksExtraResultVO.setId(s.getId());
                        itemRemarksExtraResultVOS.add(itemRemarksExtraResultVO);
                    }
                }
            });
        }
        echoService.action(itemRemarksExtraResultVOS);
        Map<Long, String> itemTagsMap = itemRemarksExtraResultVOS.stream().collect(Collectors.toMap(ItemRemarksExtraResultVO::getId, v -> {
            String itemTags = v.getEchoMap().get("itemTags") != null ? v.getEchoMap().get("itemTags").toString() : "";
            String remarks = v.getRemarks() != null ? v.getRemarks() : "";
            if (StrUtil.isNotBlank(itemTags) && StrUtil.isNotBlank(remarks)) {
                return itemTags + "," + remarks;
            } else if (StrUtil.isNotBlank(itemTags)) {
                return itemTags;
            } else if (StrUtil.isNotBlank(remarks)) {
                return remarks;
            } else {
                return "-";
            }
        }));
        echoService.action(resultVOList);
        resultVOList.forEach(s -> {
            if (Objects.nonNull(s.getId())) {
                s.setItemRemark(itemTagsMap.get(s.getId()));
            }
            Map<String, Object> echoMap = s.getEchoMap();
            if (CollUtil.isNotEmpty(echoMap)) {
                if (echoMap.containsKey("categoryId") && Objects.nonNull(echoMap.get("categoryId"))) {
                    s.setCategoryName(echoMap.get("categoryId").toString());
                } else {
                    s.setCategoryName("-");
                }
                if (echoMap.containsKey("measuringUnitKey") && Objects.nonNull(echoMap.get("measuringUnitKey"))) {
                    s.setMeasuringUnit(echoMap.get("measuringUnitKey").toString());
                } else {
                    s.setMeasuringUnit("-");
                }
                if (echoMap.containsKey("createdEmpId") && Objects.nonNull(echoMap.get("createdEmpId"))) {
                    s.setCreatedEmp(echoMap.get("createdEmpId").toString());
                } else {
                    s.setCreatedEmp("-");
                }
                if (echoMap.containsKey("orgId") && Objects.nonNull(echoMap.get("orgId"))) {
                    s.setOrg(echoMap.get("orgId").toString());
                } else {
                    s.setOrg("-");
                }
                if (echoMap.containsKey("warehouseId") && Objects.nonNull(echoMap.get("warehouseId"))) {
                    s.setWarehouseName(echoMap.get("warehouseId").toString());
                } else {
                    s.setWarehouseName("-");
                }
                if (echoMap.containsKey("beneficiaryEmpIdList") && Objects.nonNull(echoMap.get("beneficiaryEmpIdList"))) {
                    s.setBeneficiaryEmp(StrUtil.join(",", JSONUtil.toList(echoMap.get("beneficiaryEmpIdList").toString(), String.class)));
                } else {
                    s.setBeneficiaryEmp("-");
                }
                if (echoMap.containsKey("type") && Objects.nonNull(echoMap.get("type"))) {
                    s.setTypeName(echoMap.get("type").toString());
                } else {
                    s.setTypeName("-");
                }

            }
        });

        ProductOutboundDetailsResultVO sumMap = ProductOutboundDetailsResultVO.builder().build();
        sumMap.setNum(resultVOList.stream().mapToInt(ProductOutboundDetailsResultVO::getNum).sum());
        sumMap.setTotalPrice(resultVOList.stream().map(ProductOutboundDetailsResultVO::getTotalPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        sumMap.setOriginPrice(resultVOList.stream().map(ProductOutboundDetailsResultVO::getOriginPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        sumMap.setDiscountAmount(resultVOList.stream().map(ProductOutboundDetailsResultVO::getDiscountAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        sumMap.setTotalCostPrice(resultVOList.stream().map(ProductOutboundDetailsResultVO::getTotalCostPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        sumMap.setProfitPrice(resultVOList.stream().map(ProductOutboundDetailsResultVO::getProfitPrice).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultVOList.add(sumMap);
        return resultVOList;
    }

    @NotNull
    private static QueryWrapper<PosCashProduct> getProductOutboundDetailsWrap(InventoryFlowQuery model) {
        QueryWrapper<PosCashProduct> wrapper = new QueryWrapper<>();
        wrapper.eq("pro.delete_flag", 0)
                .eq("p.delete_flag", 0)
                .in("p.bill_state", PosCashBillStateEnum.COMPLETE.getCode(), PosCashBillStateEnum.PART_REFUND.getCode())
                .in("p.bill_type", PosCashBillTypeEnum.REGULAR_SINGLE.getCode(), PosCashBillTypeEnum.REGISTRATION.getCode(), PosCashBillTypeEnum.COUNTER_CHECKOUT.getCode());
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            wrapper.between("p.complete_time", model.getStartDate(), model.getEndDate());
        }
        if (StringUtils.isNotBlank(model.getCompleteTime_st()) && StringUtils.isNotBlank(model.getCompleteTime_ed())) {
            wrapper.between("p.complete_time", model.getCompleteTime_st(), model.getCompleteTime_ed());
        }
        if (StringUtils.isNotBlank(model.getCreatedTime_st()) && StringUtils.isNotBlank(model.getCreatedTime_ed())) {
            wrapper.between("pro.created_time", model.getCreatedTime_st(), model.getCreatedTime_ed());
        }
        if (Objects.nonNull(model.getType())) {
            wrapper.eq("o.type_", model.getType());
        }
        if (CollUtil.isNotEmpty(model.getCategoryIds())) {
            wrapper.inSql("pro.product_id", "select id from base_product bp where bp.category_id in (" + model.getCategoryIds().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(",")) + ")");
        }
        if (CollUtil.isNotEmpty(model.getOrgIdList())) {
            wrapper.in("pro.created_org_id", model.getOrgIdList());
        }
        if (CollUtil.isNotEmpty(model.getWarehouseIds())) {
            wrapper.in("pro.warehouse_id", model.getWarehouseIds());
        }
        if (Objects.nonNull(model.getEmployeeId())) {
            wrapper.eq("p.employee_id", model.getEmployeeId());
        }
        if (Objects.nonNull(model.getWarehouseId())) {
            wrapper.eq("pro.warehouse_id", model.getWarehouseId());
        }
        if (Objects.nonNull(model.getCommenter())) {
            wrapper.eq("c.employee_id", model.getCommenter());
        }
        if (StringUtils.isNotBlank(model.getName())) {
            wrapper.and(wra -> wra.like("pro.product_name", model.getName())
                    .or().like("p.code", model.getName()));
        }
        if (Objects.nonNull(model.getCategoryId())) {
            wrapper.inSql("pro.product_id", "select id from base_product bp where bp.category_id = " + model.getCategoryId());
        }
        return wrapper;
    }

    @Override
    public Map<String, Object> productInventoryDetails(PageParams<InventoryFlowQuery> params) {
        InventoryFlowQuery model = params.getModel();
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(model);
        }

        params.setSort(null);
        params.setOrder(null);
        IPage<Map<String, Object>> pageList = statisticsMapper.productInventoryDetails(params.buildPage(Map.class), model);
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("createdTime").label("入库时间")
                        .width(160).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("type").label("入库方式")
                        .width(160).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("code").label("单号")
                        .width(200).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("categoryName").label("商品分类")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("name").label("商品名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("measuringUnit").label("单位")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("数量")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("costPrice").label("进货单价")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("totalCostPrice").label("成本合计")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("desc").label("备注")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("employeeName").label("操作员工")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(200).emptyString("-").fixed(false).build()
        );
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public Map<String, Object> productInventoryDetailsSum(InventoryFlowQuery params) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        return statisticsMapper.productInventoryDetailsSum(params);
    }

    @Override
    public void productInventoryDetailsExport(InventoryFlowQuery params, HttpServletResponse response) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        List<Map<String, Object>> resultVOList = statisticsMapper.productInventoryDetailsExport(params);
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("createdTime").label("入库时间")
                        .width(160).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("type").label("入库方式")
                        .width(120).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("code").label("单号")
                        .width(200).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("categoryName").label("商品分类")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("name").label("商品名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("measuringUnit").label("单位")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("数量")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("costPrice").label("进货单价")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("totalCostPrice").label("成本合计")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("desc").label("备注")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("employeeName").label("操作员工")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(200).emptyString("-").fixed(false).build()
        );

        Map<String, Object> sumMap = new HashMap<>();
        sumMap.put("createdTime", "合计");
        sumMap.put("num", resultVOList.stream()
                .filter(map -> map.get("num") != null && map.get("num") != "-")
                .mapToInt(map -> Integer.parseInt(map.get("num").toString()))
                .sum());
        sumMap.put("totalCostPrice", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("totalCostPrice") != null && map.get("totalCostPrice") != "-")
                .mapToDouble(map -> Double.parseDouble(map.get("totalCostPrice").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));

        resultVOList.add(sumMap);
        export(response, resultVOList, columnVOList, "商品入库明细");
    }

    @Override
    public Map<String, Object> realTimeInventoryOfGoods(PageParams<RealTimeInventoryOfGoodsQuery> params) {
        params.setSort(null);
        params.setOrder(null);
        RealTimeInventoryOfGoodsQuery model = params.getModel();
        initOrgIdList(model);
        IPage<Map<String, Object>> pageList = statisticsMapper.realTimeInventoryOfGoods(params.buildPage(Map.class), model);
        List<Long> productIds = pageList.getRecords().stream().map(map -> Long.parseLong(map.get("productId").toString())).collect(Collectors.toList());
        model.setProductIds(productIds);
        // 销售数量取订单中数量
        List<ProductStockTypeResultVO> resultVOList = statisticsMapper.sellOutNum(model);
        Map<Long, ProductStockTypeResultVO> productStockTypeMap = resultVOList.stream().collect(Collectors.toMap(ProductStockTypeResultVO::getProductId, Function.identity()));
        List<ProductStockTypeResultVO> stockTypeResultVOList = statisticsMapper.productStockTypeStatis(model);
        Map<Long, List<ProductStockTypeResultVO>> stockTypeMap = stockTypeResultVOList.stream().collect(Collectors.groupingBy(ProductStockTypeResultVO::getProductId));
        if (CollUtil.isNotEmpty(stockTypeMap)) {
            pageList.getRecords().forEach(s -> {
                List<ProductStockTypeResultVO> stockTypeResultVOS = stockTypeMap.get(Long.parseLong(s.get("productId").toString()));
                if (CollUtil.isNotEmpty(stockTypeResultVOS)) {
                    stockTypeResultVOS.forEach(stockTypeResultVO -> {
                        OutinTypeEnum outinTypeEnum = OutinTypeEnum.get(stockTypeResultVO.getType());
                        switch (outinTypeEnum) {
                            case SELL_OUT:
                                break;
                            case INVENTORY_LOSS_OUT:
                            case OTHER_OUT:
                            case ADJUSTMENT_OUT:
                            case PURCHASE_OUT:
                                s.put("type_" + stockTypeResultVO.getType(), -stockTypeResultVO.getNum());
                                break;
                            default:
                                s.put("type_" + stockTypeResultVO.getType(), stockTypeResultVO.getNum());
                                break;
                        }
                    });
                    ProductStockTypeResultVO stockTypeResultVO = productStockTypeMap.get(Long.parseLong(s.get("productId").toString()));
                    if (stockTypeResultVO != null) {
                        s.put("type_" + OutinTypeEnum.SELL_OUT.getCode(), stockTypeResultVO.getNum());
                    } else {
                        s.put("type_" + OutinTypeEnum.SELL_OUT.getCode(), 0);
                    }
                }
            });
        }

        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("category").label("分类")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("商品名称")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("measuringUnit").label("单位")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type_0").label("采购入库")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type_7").label("采购退库")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type_9").label("调库入库")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type_10").label("调库出库")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type_2").label("盘亏出库")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type_1").label("盘盈入库")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type_3").label("销售出库")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("lockNum").label("待结")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("净库存")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("retailPrice").label("销售单价")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("totalRetailPrice").label("预售合计")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("buyingPrice").label("成本单价")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("totalBuyingPrice").label("库存成本")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(200).emptyString("-").fixed(false).build()
        );
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public Map<String, Object> realTimeInventoryOfGoodsSum(RealTimeInventoryOfGoodsQuery params) {
        initOrgIdList(params);
        Map<String, Object> stringObjectMap = statisticsMapper.realTimeInventoryOfGoodsSum(params);
        if (CollUtil.isNotEmpty(stringObjectMap)) {
            List<ProductStockTypeResultVO> stockTypeResultVOS = statisticsMapper.stockTypeStatis(params);
            if (CollUtil.isNotEmpty(stockTypeResultVOS)) {
                List<ProductStockTypeResultVO> resultVOList = statisticsMapper.sellOutNum(params);
                stockTypeResultVOS.forEach(stockTypeResultVO -> {
                    OutinTypeEnum outinTypeEnum = OutinTypeEnum.get(stockTypeResultVO.getType());
                    switch (outinTypeEnum) {
                        case SELL_OUT:
                            break;
                        case INVENTORY_LOSS_OUT:
                        case OTHER_OUT:
                        case ADJUSTMENT_OUT:
                        case PURCHASE_OUT:
                            stringObjectMap.put("type_" + stockTypeResultVO.getType(), -stockTypeResultVO.getNum());
                            break;
                        default:
                            stringObjectMap.put("type_" + stockTypeResultVO.getType(), stockTypeResultVO.getNum());
                            break;
                    }
                });
                stringObjectMap.put("type_" + OutinTypeEnum.SELL_OUT.getCode(), resultVOList.stream().mapToInt(ProductStockTypeResultVO::getNum).sum());
            }
        }
        return stringObjectMap;
    }

    @Override
    public void realTimeInventoryOfGoodsExport(RealTimeInventoryOfGoodsQuery params, HttpServletResponse response) {
        initOrgIdList(params);
        List<Map<String, Object>> resultVOList = statisticsMapper.realTimeInventoryOfGoodsExport(params);
        // 需要查询的所有的商品id
        List<Long> productIds = resultVOList.stream().map(map -> Long.parseLong(map.get("productId").toString())).collect(Collectors.toList());
        List<ProductStockTypeResultVO> sellOutNumResultVOList = statisticsMapper.sellOutNum(params);
        Map<Long, ProductStockTypeResultVO> sellOutNumMap = sellOutNumResultVOList.stream().collect(Collectors.toMap(ProductStockTypeResultVO::getProductId, s -> s));

        params.setProductIds(productIds);
        List<ProductStockTypeResultVO> stockTypeResultVOList = statisticsMapper.productStockTypeStatis(params);
        Map<Long, List<ProductStockTypeResultVO>> stockTypeMap = stockTypeResultVOList.stream().collect(Collectors.groupingBy(ProductStockTypeResultVO::getProductId));
        if (CollUtil.isNotEmpty(stockTypeMap)) {
            resultVOList.forEach(s -> {
                if (Objects.nonNull(s.get("lockNum"))) {
                    s.put("lockNum", Long.parseLong(s.get("lockNum").toString()));
                }
                if (Objects.nonNull(s.get("num"))) {
                    s.put("num", Long.parseLong(s.get("num").toString()));
                }
                s.put("type_0", 0);
                s.put("type_7", 0);
                s.put("type_9", 0);
                s.put("type_10", 0);
                s.put("type_2", 0);
                s.put("type_1", 0);
                ProductStockTypeResultVO sellOutNum = sellOutNumMap.get(Long.parseLong(s.get("productId").toString()));
                if (Objects.nonNull(sellOutNum)) {
                    s.put("type_3", sellOutNum.getNum());
                } else {
                    s.put("type_3", 0);
                }
                List<ProductStockTypeResultVO> stockTypeResultVOS = stockTypeMap.get(Long.parseLong(s.get("productId").toString()));
                if (CollUtil.isNotEmpty(stockTypeResultVOS)) {
                    stockTypeResultVOS.forEach(stockTypeResultVO -> {
                        OutinTypeEnum outinTypeEnum = OutinTypeEnum.get(stockTypeResultVO.getType());
                        switch (outinTypeEnum) {
                            case SELL_OUT:
                                break;
                            case INVENTORY_LOSS_OUT:
                            case OTHER_OUT:
                            case ADJUSTMENT_OUT:
                            case PURCHASE_OUT:
                                s.put("type_" + stockTypeResultVO.getType(), -stockTypeResultVO.getNum());
                                break;
                            default:
                                s.put("type_" + stockTypeResultVO.getType(), stockTypeResultVO.getNum());
                                break;
                        }
                    });
                }
            });
        }
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("category").label("分类")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("商品名称")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("measuringUnit").label("单位")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type_0").label("采购入库")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type_7").label("采购退库")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type_9").label("调库入库")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type_10").label("调库出库")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type_2").label("盘亏出库")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type_1").label("盘盈入库")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type_3").label("销售出库")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("lockNum").label("待结")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("净库存")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("retailPrice").label("销售单价")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("totalRetailPrice").label("预售合计")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("buyingPrice").label("成本单价")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("totalBuyingPrice").label("库存成本")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(200).emptyString("-").fixed(false).build()
        );

        Map<String, Object> stringObjectMap = realTimeInventoryOfGoodsSum(params);
        stringObjectMap.put("category", "合计");
//        Map<String, Object> sumMap = new HashMap<>();
//        sumMap.put("category", "合计");
//        sumMap.put("inUnm", resultVOList.stream()
//                .filter(map -> map.get("inUnm") != null && map.get("inUnm") != "-")
//                .mapToInt(map -> Integer.parseInt(map.get("inUnm").toString()))
//                .sum());
//        sumMap.put("outNum", resultVOList.stream()
//                .filter(map -> map.get("outNum") != null && map.get("outNum") != "-")
//                .mapToInt(map -> Integer.parseInt(map.get("outNum").toString()))
//                .sum());
//        sumMap.put("lockNum", resultVOList.stream()
//                .filter(map -> map.get("lockNum") != null && map.get("lockNum") != "-")
//                .mapToInt(map -> Integer.parseInt(map.get("lockNum").toString()))
//                .sum());
//        sumMap.put("num", resultVOList.stream()
//                .filter(map -> map.get("num") != null && map.get("num") != "-")
//                .mapToInt(map -> Integer.parseInt(map.get("num").toString()))
//                .sum());
//        sumMap.put("totalRetailPrice", BigDecimal.valueOf(resultVOList.stream()
//                .filter(map -> map.get("totalRetailPrice") != null && map.get("totalRetailPrice") != "-")
//                .mapToDouble(map -> Double.parseDouble(map.get("totalRetailPrice").toString()))
//                .sum()).setScale(2, RoundingMode.HALF_UP));
//        sumMap.put("totalBuyingPrice", BigDecimal.valueOf(resultVOList.stream()
//                .filter(map -> map.get("totalBuyingPrice") != null && !StringUtils.equals(map.get("totalBuyingPrice").toString(), "-"))
//                .mapToDouble(map -> Double.parseDouble(map.get("totalBuyingPrice").toString()))
//                .sum()).setScale(2, RoundingMode.HALF_UP));

        resultVOList.add(stringObjectMap);
        export(response, resultVOList, columnVOList, "商品实时库存");
    }


    @Override
    public Map<String, Object> posCashDetails(PageParams<PosCashDetailsQuery> params) {

        initOrgIdList(params.getModel());

        // 所有支付方式
        List<BasePaymentType> basePaymentTypeList = basePaymentTypeService.list(Wraps.<BasePaymentType>lbQ().eq(BasePaymentType::getState, true)
                .eq(BasePaymentType::getDeleteFlag, 0).in(BasePaymentType::getCreatedOrgId, params.getModel().getOrgIdList()));

        List<BasePaymentTypeResultVO> basePaymentTypeResultVOList = BeanUtil.copyToList(basePaymentTypeList, BasePaymentTypeResultVO.class);
        echoService.action(basePaymentTypeResultVOList);

        basePaymentTypeResultVOList.forEach(basePaymentTypeResultVO -> {
            if (basePaymentTypeResultVO.getType() == null) {
                basePaymentTypeResultVO.setType("");
            }
        });

        // 支付方式类型
        Map<String, List<BasePaymentTypeResultVO>> basePaymentTypeResultVOMap = basePaymentTypeResultVOList.stream()
                .collect(Collectors.groupingBy(BasePaymentTypeResultVO::getType, LinkedHashMap::new, Collectors.toList()));

        List<ColumnVO> columnVOList = com.google.common.collect.Lists.newArrayList();
        ColumnVO emptyColumn = ColumnVO.builder().name("emptyColumn").label("订单")
                .width(200).emptyString("-").fixed(true).build();
        // 设置表头
        List<ColumnVO> columnChildrenVO = com.google.common.collect.Lists.newArrayList(
                ColumnVO.builder().name("code").label("流水号")
                        .width(250).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("type").label("业务类型")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("tableName").label("台桌名称")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableType").label("台桌类型")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableArea").label("台桌区域")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createdEmp").label("创建员工")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeEmp").label("完成员工")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("employeeEmp").label("销售员工")// employeeId
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("commenter").label("提成员工")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orderSource").label("订单来源")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("memberId").label("会员名称")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createTime").label("开台时间")
                        .width(200).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeTime").label("结账时间")
                        .width(200).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableAmount").label("台桌金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("productAmount").label("商品金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceAmount").label("服务金额")
                        .width(100).emptyString("-").fixed(false).build(),
                // 店内套餐/团购套餐
                ColumnVO.builder().name("thailAmount").label("店内套餐")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("groupBuyAmount").label("团购套餐")
                        .width(100).emptyString("-").fixed(false).build(),
                // 充电金额
                ColumnVO.builder().name("powerAmount").label("充电金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("订单原价")// 原本是订单金额,原价 54.72, amount 字段
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("discountAmount").label("订单优惠金额")
                        .width(150).emptyString("-").fixed(false).build(),
//                ColumnVO.builder().name("payment").label("订单应收金额")
//                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("refundAmount").label("订单部分退款金额")
                        .width(150).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("paid").label("订单实收金额")
                        .width(120).emptyString("-").fixed(false).build(),// amount - discountAmount - refundAmount
                ColumnVO.builder().name("orderRemarks").label("整单备注")
                        .width(160).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("registrationRemarks").label("挂单备注")
                        .width(160).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(230).emptyString("-").fixed(false).build()
        );
        emptyColumn.setChild(columnChildrenVO);
        columnVOList.add(emptyColumn);
        for (String volumeId : basePaymentTypeResultVOMap.keySet()) {
            ColumnVO type = ColumnVO.builder().name("type_" + volumeId).label(String.valueOf(basePaymentTypeResultVOMap.get(volumeId).get(0).getEchoMap().get("type")))
                    .width(180).emptyString("-").fixed(false).build();
            if ("null".equals(type.getLabel())) {
                type.setLabel("未设置");
            }
            List<BasePaymentTypeResultVO> typeResultVOS = basePaymentTypeResultVOMap.get(volumeId);
            List<ColumnVO> columnVOS = new ArrayList<>();
            for (BasePaymentTypeResultVO typeResultVO : typeResultVOS) {
                columnVOS.add(ColumnVO.builder().name("payment_" + typeResultVO.getId()).label(typeResultVO.getName())
                        .width(180).emptyString("-").fixed(false).build());
            }
            type.setChild(columnVOS);
            columnVOList.add(type);
        }
        PosCashDetailsQuery model = params.getModel();
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(model);
        }

        if (StringUtils.isNotBlank(model.getCompleteTime_st()) && StringUtils.isBlank(model.getCompleteTime_ed())) {
            model.setCompleteTime_ed(DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
        }
        IPage<PosCashResultVO> pageResultVO = posCashService.findPageResultVO(params);

        echoService.action(pageResultVO);
        Map<Long, PosCashThailAmountResultVO> thailAmountResultVOMap = new HashMap<>();
        // 查询团购套餐和店内套餐
        if (CollUtil.isNotEmpty(pageResultVO.getRecords())) {
            PosCashThailAmountQuery query = PosCashThailAmountQuery.builder()
                    .cashIds(pageResultVO.getRecords().stream().map(PosCashResultVO::getId).distinct().collect(Collectors.toList()))
                    .build();
            List<PosCashThailAmountResultVO> thailAmountResultVOList = posCashThailService.thailAmountList(query);
            if (CollUtil.isNotEmpty(thailAmountResultVOList)) {
                thailAmountResultVOMap = thailAmountResultVOList.stream().collect(Collectors.toMap(PosCashThailAmountResultVO::getCashId, Function.identity()));
            }
        }


        IPage<Map> pageList = BeanPlusUtil.toBeanPage(pageResultVO, Map.class);
        List<OrderRemarksExtraResultVO> orderRemarksExtraResultVOS = new ArrayList<>();
        pageResultVO.getRecords().forEach(item -> {
            if (Objects.nonNull(item) && StringUtils.isNotBlank(item.getRemarks())) {
                OrderRemarksExtraResultVO orderRemarksExtraResultVO = JSON.parseObject(item.getRemarks(), OrderRemarksExtraResultVO.class);
                if (Objects.nonNull(orderRemarksExtraResultVO)) {
                    orderRemarksExtraResultVO.setId(item.getId());
                    orderRemarksExtraResultVOS.add(orderRemarksExtraResultVO);
                }
            }
        });
        echoService.action(orderRemarksExtraResultVOS);
        Map<Long, OrderRemarksExtraResultVO> orderRemarksExtraResultVOMap = orderRemarksExtraResultVOS.stream().collect(Collectors.toMap(OrderRemarksExtraResultVO::getId, Function.identity()));
        for (PosCashResultVO posCash : pageResultVO.getRecords()) {
            //相关备注
            OrderRemarksExtraResultVO orderRemarksResultVO = orderRemarksExtraResultVOMap.get(posCash.getId());
            if (Objects.nonNull(orderRemarksResultVO)) {
                // 整单备注
                String orderTags = ((CollUtil.isNotEmpty(orderRemarksResultVO.getEchoMap()) && ObjectUtil.isNotNull(orderRemarksResultVO.getEchoMap().get("orderTags"))) ? orderRemarksResultVO.getEchoMap().get("orderTags").toString() : "");
                // 挂单备注
                String registrationTags = ((CollUtil.isNotEmpty(orderRemarksResultVO.getEchoMap()) && ObjectUtil.isNotNull(orderRemarksResultVO.getEchoMap().get("registrationTags"))) ? orderRemarksResultVO.getEchoMap().get("registrationTags").toString() : "");
                if (StrUtil.isNotBlank(orderTags)) {
                    if (StringUtils.isNotBlank(orderRemarksResultVO.getRemarks())) {
                        orderTags = orderTags + "," + orderRemarksResultVO.getRemarks();
                    }
                } else {
                    if (StringUtils.isNotBlank(orderRemarksResultVO.getRemarks())) {
                        orderTags = orderRemarksResultVO.getRemarks();
                    }
                }
                if (StringUtils.isNotBlank(registrationTags)) {
                    if (StringUtils.isNotBlank(orderRemarksResultVO.getRegistrationRemarks())) {
                        registrationTags = registrationTags + "," + orderRemarksResultVO.getRegistrationRemarks();
                    }
                } else {
                    if (StringUtils.isNotBlank(orderRemarksResultVO.getRegistrationRemarks())) {
                        registrationTags = orderRemarksResultVO.getRegistrationRemarks();
                    }
                }
                if (StringUtils.isNotBlank(orderTags)) {
                    posCash.setOrderRemarks(orderTags);
                } else {
                    posCash.setOrderRemarks("-");
                }
                if (StringUtils.isNotBlank(registrationTags)) {
                    posCash.setRegistrationRemarks(registrationTags);
                } else {
                    posCash.setRegistrationRemarks("-");
                }
            }
        }
        List<Long> cashIds = pageResultVO.getRecords().stream().map(PosCashResultVO::getId)
                .collect(Collectors.toList());

        // 查询整单提成人
        List<PosCashCommenter> cashCommenterList = posCashCommenterService.list(Wraps.<PosCashCommenter>lbQ().eq(PosCashCommenter::getDeleteFlag, 0)
                .eq(PosCashCommenter::getType, PosCashConstant.Event.ORDER.getCode())
                .in(PosCashCommenter::getCashId, cashIds));
        List<PosCashCommenterResultVO> cashCommenterListResultVO = BeanPlusUtil.toBeanList(cashCommenterList, PosCashCommenterResultVO.class);
        Map<Long, String> baseEmployeeMap = MapUtil.newHashMap();
        if (CollUtil.isNotEmpty(cashCommenterListResultVO)) {
            Map<Long, String> collect = baseEmployeeService.list(Wraps.<BaseEmployee>lbQ().eq(BaseEmployee::getDeleteFlag, 0)
                            .in(BaseEmployee::getId, cashCommenterListResultVO.stream().map(PosCashCommenterResultVO::getEmployeeId).collect(Collectors.toList())))
                    .stream().collect(Collectors.toMap(BaseEmployee::getId, BaseEmployee::getRealName));
            if (CollUtil.isNotEmpty(collect)) {
                baseEmployeeMap = collect;
            }
        }
        Map<Long, List<PosCashCommenterResultVO>> cashCommenterListResultVOMap = cashCommenterListResultVO.stream().collect(Collectors.groupingBy(PosCashCommenterResultVO::getCashId));
        // 所有支付方式
        List<PosCashPayment> cashPaymentList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(cashIds)) {
            cashPaymentList = posCashPaymentService.list(Wraps.<PosCashPayment>lbQ()
                    .eq(PosCashPayment::getDeleteFlag, 0)
                    .eq(PosCashPayment::getStatus, PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                    .in(PosCashPayment::getCashId, cashIds));
        }
        // 所有台桌id
        List<Long> tableIdList = pageResultVO.getRecords().stream().map(PosCashResultVO::getTableId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        List<BaseTableInfo> tableInfoList = bizCacheService.cacheList(BaseTableInfo.class, BizCacheEnum.BASE_TABLE_INFO);
        tableInfoList = tableInfoList.stream().filter(v -> tableIdList.contains(v.getId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(tableInfoList)) {
            tableInfoList = Lists.newArrayList();
        }
        List<BaseTableInfoResultVO> tableInfoResultVOS = BeanUtil.copyToList(tableInfoList, BaseTableInfoResultVO.class);
        Map<Long, BaseTableArea> baseTableAreaMap = new HashMap<>();
        Map<String, BaseDict> baseDictMap = new HashMap<>();
        if (CollUtil.isNotEmpty(tableInfoResultVOS)) {
            baseTableAreaMap = baseTableAreaService.list(Wraps.<BaseTableArea>lbQ()
                            .in(BaseTableArea::getId, tableInfoResultVOS.stream().map(BaseTableInfoResultVO::getTableArea)
                                    .collect(Collectors.toList())))
                    .stream().collect(Collectors.toMap(BaseTableArea::getId, Function.identity()));
            baseDictMap = baseDictService.list(Wraps.<BaseDict>lbQ()
                            .eq(BaseDict::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                            .eq(BaseDict::getDeleteFlag, 0).eq(BaseDict::getParentKey,
                                    EchoDictType.Base.BASE_TABLE_TYPE))
                    .stream().collect(Collectors.toMap(BaseDict::getKey, Function.identity()));
        }
        // 会员信息
        Map<Long, MemberInfo> memberInfoMap = new HashMap<>();
        List<Long> memberIdList = pageResultVO.getRecords().stream().map(PosCashResultVO::getMemberId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(memberIdList)) {
            List<MemberInfo> memberInfoList = memberInfoService.list(Wraps.<MemberInfo>lbQ().in(MemberInfo::getId, memberIdList));
            memberInfoMap = memberInfoList.stream().collect(Collectors.toMap(MemberInfo::getId, Function.identity()));
        }
        List<Map> resultMapList = new ArrayList<>();
        for (PosCashResultVO posCashResultVO : pageResultVO.getRecords()) {
            if (posCashResultVO.getRefundAmount() == null) {
                posCashResultVO.setRefundAmount(BigDecimal.ZERO);
            }
            if (posCashResultVO.getPaid() == null) {
                posCashResultVO.setPaid(BigDecimal.ZERO);
            }

            Map<String, Object> tableInfoMap = new HashMap();
            tableInfoMap.put("code", posCashResultVO.getCode());
            tableInfoMap.put("type", posCashResultVO.getEchoMap().get("type"));
            tableInfoMap.put("tableName", posCashResultVO.getTableName());
            tableInfoMap.put("tableType", StrUtil.isNotBlank(posCashResultVO.getTableType())
                    && CollUtil.isNotEmpty(baseDictMap)
                    && ObjectUtil.isNotNull(baseDictMap.get(posCashResultVO.getTableType())) ? baseDictMap.get(posCashResultVO.getTableType()).getName() : "");
            tableInfoMap.put("tableArea", ObjectUtil.isNotNull(posCashResultVO.getTableArea())
                    && CollUtil.isNotEmpty(baseTableAreaMap)
                    && ObjectUtil.isNotNull(baseTableAreaMap.get(posCashResultVO.getTableArea())) ? baseTableAreaMap.get(posCashResultVO.getTableArea()).getName() : "");
            tableInfoMap.put("createdEmp", posCashResultVO.getEchoMap().get("createdEmp"));
            tableInfoMap.put("completeEmp", posCashResultVO.getEchoMap().get("completeEmp"));
            tableInfoMap.put("employeeEmp", posCashResultVO.getEchoMap().get("employeeId"));
            if (CollUtil.isNotEmpty(cashCommenterListResultVOMap.get(posCashResultVO.getId()))) {
                Map<Long, String> finalBaseEmployeeMap = baseEmployeeMap;
                tableInfoMap.put("commenter", cashCommenterListResultVOMap.get(posCashResultVO.getId())
                        .stream().filter(c -> CollUtil.isNotEmpty(finalBaseEmployeeMap) && ObjectUtil.isNotNull(finalBaseEmployeeMap.containsKey(c.getEmployeeId())))
                        .map(s -> finalBaseEmployeeMap.get(s.getEmployeeId())).collect(Collectors.joining(",")));
            } else {
                tableInfoMap.put("commenter", "-");
            }
            tableInfoMap.put("powerAmount", posCashResultVO.getPowerAmount());
            tableInfoMap.put("orderSource", posCashResultVO.getEchoMap().get("orderSource"));
            MemberInfo memberInfo = memberInfoMap.get(posCashResultVO.getMemberId());
            String memberName = "-";
            if (Objects.nonNull(memberInfo)) {
                memberName = memberInfo.getName() + "(" + StrUtil.subSuf(memberInfo.getMobile(), memberInfo.getMobile().length() - 4) + ")";
            }
            tableInfoMap.put("memberId", memberName);
            tableInfoMap.put("createTime", posCashResultVO.getCreatedTime());
            tableInfoMap.put("completeTime", posCashResultVO.getCompleteTime());
            tableInfoMap.put("tableAmount", posCashResultVO.getTableAmount());
            tableInfoMap.put("productAmount", posCashResultVO.getProductAmount());
            tableInfoMap.put("serviceAmount", posCashResultVO.getServiceAmount());
            PosCashThailAmountResultVO posCashThailAmountResultVO = thailAmountResultVOMap.get(posCashResultVO.getId());
            if (ObjectUtil.isNotNull(posCashThailAmountResultVO)) {
                tableInfoMap.put("groupBuyAmount", posCashThailAmountResultVO.getGroupBuyAmount());
                tableInfoMap.put("thailAmount", posCashThailAmountResultVO.getThailNum());
            } else {
                tableInfoMap.put("groupBuyAmount", BigDecimal.ZERO);
                tableInfoMap.put("thailAmount", BigDecimal.ZERO);
            }
            tableInfoMap.put("powerAmount", posCashResultVO.getPowerDuration());
            tableInfoMap.put("amount", posCashResultVO.getAmount());
            tableInfoMap.put("discountAmount", posCashResultVO.getDiscountAmount());
            // 判断是不是整单退款,整单退的退款金额设置为0
            if (StringUtils.equals(posCashResultVO.getBillState(), PosCashBillStateEnum.REFUNDED.getCode())) {
                tableInfoMap.put("refundAmount", BigDecimal.ZERO);
                tableInfoMap.put("paid", posCashResultVO.getAmount().subtract(posCashResultVO.getDiscountAmount()));
            } else {
                tableInfoMap.put("refundAmount", posCashResultVO.getRefundAmount());
                tableInfoMap.put("paid", posCashResultVO.getAmount().subtract(posCashResultVO.getDiscountAmount()).subtract(posCashResultVO.getRefundAmount()));
            }

            //tableInfoMap.put("payment", posCashResultVO.getPayment());
            tableInfoMap.put("orderRemarks", posCashResultVO.getOrderRemarks());
            tableInfoMap.put("registrationRemarks", posCashResultVO.getRegistrationRemarks());
            tableInfoMap.put("org", posCashResultVO.getEchoMap().get("orgId"));

            // 判断是不是整单退款, 整单退,多添加一条记录, 金额取反
            Map<String, Object> refundedMap = null;
            if (StringUtils.equals(posCashResultVO.getBillState(), PosCashBillStateEnum.REFUNDED.getCode())) {
                if (posCashResultVO.getRefundAmount() == null) {
                    posCashResultVO.setRefundAmount(BigDecimal.ZERO);
                }
                if (posCashResultVO.getPaid() == null) {
                    posCashResultVO.setPaid(BigDecimal.ZERO);
                }

                refundedMap = new HashMap();
                refundedMap.put("code", posCashResultVO.getCode());
                refundedMap.put("type", posCashResultVO.getEchoMap().get("type"));
                refundedMap.put("tableName", posCashResultVO.getTableName());
                refundedMap.put("tableType", StrUtil.isNotBlank(posCashResultVO.getTableType())
                        && CollUtil.isNotEmpty(baseDictMap)
                        && ObjectUtil.isNotNull(baseDictMap.get(posCashResultVO.getTableType())) ? baseDictMap.get(posCashResultVO.getTableType()).getName() : "");
                refundedMap.put("tableArea", ObjectUtil.isNotNull(posCashResultVO.getTableArea())
                        && CollUtil.isNotEmpty(baseTableAreaMap)
                        && ObjectUtil.isNotNull(baseTableAreaMap.get(posCashResultVO.getTableArea())) ? baseTableAreaMap.get(posCashResultVO.getTableArea()).getName() : "");
                refundedMap.put("createdEmp", posCashResultVO.getEchoMap().get("createdEmp"));
                refundedMap.put("completeEmp", posCashResultVO.getEchoMap().get("completeEmp"));
                refundedMap.put("employeeEmp", posCashResultVO.getEchoMap().get("employeeId"));
                if (CollUtil.isNotEmpty(cashCommenterListResultVOMap.get(posCashResultVO.getId()))) {
                    refundedMap.put("commenter", cashCommenterListResultVOMap.get(posCashResultVO.getId())
                            .stream().filter(c -> Objects.nonNull(c.getEchoMap().get("employeeId"))).map(s -> s.getEchoMap().get("employeeId").toString()).collect(Collectors.joining(",")));
                } else {
                    refundedMap.put("commenter", "-");
                }

                refundedMap.put("orderSource", posCashResultVO.getEchoMap().get("orderSource"));
                refundedMap.put("memberId", memberName);
                refundedMap.put("createTime", posCashResultVO.getCreatedTime());
                refundedMap.put("completeTime", posCashResultVO.getCompleteTime());
                refundedMap.put("tableAmount", BigDecimal.ZERO.subtract(posCashResultVO.getTableAmount()));
                refundedMap.put("productAmount", BigDecimal.ZERO.subtract(posCashResultVO.getProductAmount()));
                refundedMap.put("serviceAmount", BigDecimal.ZERO.subtract(posCashResultVO.getServiceAmount()));
                refundedMap.put("thailAmount", BigDecimal.ZERO.subtract(posCashResultVO.getThailAmount()));
                refundedMap.put("powerAmount", BigDecimal.ZERO.subtract(posCashResultVO.getPowerAmount()));
                refundedMap.put("amount", BigDecimal.ZERO.subtract(posCashResultVO.getAmount()));
                refundedMap.put("discountAmount", posCashResultVO.getDiscountAmount());
                refundedMap.put("refundAmount", BigDecimal.ZERO);
                //tableInfoMap.put("payment", posCashResultVO.getPayment());
                refundedMap.put("paid", BigDecimal.ZERO.subtract(posCashResultVO.getAmount().subtract(posCashResultVO.getDiscountAmount())));
                refundedMap.put("orderRemarks", posCashResultVO.getOrderRemarks());
                refundedMap.put("registrationRemarks", posCashResultVO.getRegistrationRemarks());
                refundedMap.put("org", posCashResultVO.getEchoMap().get("orgId"));

            }
            // 所有支付方式,
            for (BasePaymentType basePaymentTypeResultVO : basePaymentTypeList) {
                List<PosCashPayment> posCashPayments = cashPaymentList.stream().filter(c ->
                                Objects.equals(basePaymentTypeResultVO.getId(), c.getPayTypeId())
                                        && Objects.equals(posCashResultVO.getId(), c.getCashId()) && Objects.nonNull(c.getAmount()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(posCashPayments)) {
                    if (StringUtils.equals(posCashResultVO.getBillState(), PosCashBillStateEnum.REFUNDED.getCode())) {
                        tableInfoMap.put("payment_" + basePaymentTypeResultVO.getId(),
                                posCashPayments.stream().filter(c -> Objects.nonNull(c.getAmount())).map(PosCashPayment::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .subtract(posCashPayments.stream().filter(c -> Objects.nonNull(c.getChangeAmount())).map(PosCashPayment::getChangeAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
                    } else {
                        tableInfoMap.put("payment_" + basePaymentTypeResultVO.getId(),
                                posCashPayments.stream().filter(c -> Objects.nonNull(c.getAmount())).map(PosCashPayment::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .subtract(posCashPayments.stream().filter(c -> Objects.nonNull(c.getRefundAmount())).map(PosCashPayment::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                                        .subtract(posCashPayments.stream().filter(c -> Objects.nonNull(c.getChangeAmount())).map(PosCashPayment::getChangeAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
                    }
                } else {
                    tableInfoMap.put("payment_" + basePaymentTypeResultVO.getId(), 0.00);
                }
                if (CollUtil.isNotEmpty(refundedMap)) {
                    posCashPayments = cashPaymentList.stream().filter(c ->
                                    Objects.equals(basePaymentTypeResultVO.getId(), c.getPayTypeId())
                                            && Objects.equals(posCashResultVO.getId(), c.getCashId()) && Objects.nonNull(c.getAmount()))
                            .collect(Collectors.toList());
                    if (CollUtil.isNotEmpty(posCashPayments)) {
                        refundedMap.put("payment_" + basePaymentTypeResultVO.getId(),
                                BigDecimal.ZERO.subtract(posCashPayments.stream().filter(c -> Objects.nonNull(c.getAmount())).map(PosCashPayment::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .subtract(posCashPayments.stream().filter(c -> Objects.nonNull(c.getChangeAmount())).map(PosCashPayment::getChangeAmount).reduce(BigDecimal.ZERO, BigDecimal::add))));
                    } else {
                        refundedMap.put("payment_" + basePaymentTypeResultVO.getId(), 0.00);
                    }
                }
            }
            if (CollUtil.isNotEmpty(refundedMap)) {
                tableInfoMap.put("children", Collections.singletonList(refundedMap));
            }
            resultMapList.add(tableInfoMap);
        }
        pageList.setRecords(resultMapList);
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public Map<String, Object> posCashDetailsSum(PosCashDetailsQuery params) {
        initOrgIdList(params);

        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        if (StringUtils.isNotBlank(params.getCompleteTime_st()) && StringUtils.isBlank(params.getCompleteTime_ed())) {
            params.setCompleteTime_ed(DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
        }
        List<PosCashResultVO> posCashResultVOS = posCashService.findAllResultVO(params);
        echoService.action(posCashResultVOS);
        posCashResultVOS = posCashResultVOS.stream().filter(s -> !StringUtils.equals(s.getBillState(), PosCashBillStateEnum.REFUNDED.getCode())).collect(Collectors.toList());

        List<Long> cashIds = posCashResultVOS.stream().map(PosCashResultVO::getId).distinct()
                .collect(Collectors.toList());
        // 所有支付方式
        List<PosCashPayment> cashPaymentList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(cashIds)) {
            cashPaymentList = posCashPaymentService.list(Wraps.<PosCashPayment>lbQ()
                    .eq(PosCashPayment::getStatus, PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                    .in(PosCashPayment::getCashId, cashIds));
        }

        // 所有支付方式
        List<BasePaymentType> basePaymentTypeList = basePaymentTypeService.list(Wraps.<BasePaymentType>lbQ().eq(BasePaymentType::getState, true)
                .eq(BasePaymentType::getDeleteFlag, 0));

        List<BasePaymentTypeResultVO> basePaymentTypeResultVOList = BeanUtil.copyToList(basePaymentTypeList, BasePaymentTypeResultVO.class);
        echoService.action(basePaymentTypeResultVOList);

        PosCashThailAmountResultVO thailAmountSum = posCashThailService.thailAmountSum(params);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("tableAmount", posCashResultVOS.stream().map(PosCashResultVO::getTableAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultMap.put("productAmount", posCashResultVOS.stream().map(PosCashResultVO::getProductAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultMap.put("serviceAmount", posCashResultVOS.stream().map(PosCashResultVO::getServiceAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultMap.put("thailAmount", posCashResultVOS.stream().map(PosCashResultVO::getThailAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal amount = posCashResultVOS.stream().map(PosCashResultVO::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        resultMap.put("amount", amount);
        BigDecimal discountAmount = posCashResultVOS.stream().map(PosCashResultVO::getDiscountAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        resultMap.put("discountAmount", discountAmount);
        //BigDecimal paid = posCashResultVOS.stream().map(PosCashResultVO::getPaid).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal refundAmount = posCashResultVOS.stream().map(PosCashResultVO::getRefundAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        //resultMap.put("payment", posCashResultVOS.stream().map(PosCashResultVO::getPayment).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultMap.put("paid", amount.subtract(refundAmount).subtract(discountAmount));
        resultMap.put("refundAmount", refundAmount);
        if (Objects.nonNull(thailAmountSum)) {
            resultMap.put("thailAmount", thailAmountSum.getThailAmount());
            resultMap.put("groupBuyAmount", thailAmountSum.getGroupBuyAmount());
        } else {
            resultMap.put("thailAmount", 0.00);
            resultMap.put("groupBuyAmount", 0.00);
        }
        resultMap.put("powerAmount", posCashResultVOS.stream().map(PosCashResultVO::getPowerAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        for (BasePaymentTypeResultVO basePaymentTypeResultVO : basePaymentTypeResultVOList) {
            List<PosCashPayment> posCashPaymentList = cashPaymentList.stream().filter(c -> Objects.equals(basePaymentTypeResultVO.getId(), c.getPayTypeId()) && Objects.nonNull(c.getAmount())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(posCashPaymentList)) {
                resultMap.put("payment_" + basePaymentTypeResultVO.getId(), posCashPaymentList.stream().filter(c -> Objects.nonNull(c.getAmount())).map(PosCashPayment::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                        .subtract(posCashPaymentList.stream().filter(c -> Objects.nonNull(c.getRefundAmount())).map(PosCashPayment::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                        .subtract(posCashPaymentList.stream().filter(c -> Objects.nonNull(c.getChangeAmount())).map(PosCashPayment::getChangeAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
            } else {
                resultMap.put("payment_" + basePaymentTypeResultVO.getId(), 0.00);
            }
        }
        return resultMap;
    }

    @Override
    public void posCashDetailsExport(PosCashDetailsQuery params, HttpServletResponse response) {
        long startTime = System.currentTimeMillis();
        initOrgIdList(params);

        // 所有支付方式
        List<BasePaymentType> basePaymentTypeList = basePaymentTypeService.list(Wraps.<BasePaymentType>lbQ().eq(BasePaymentType::getState, true)
                .eq(BasePaymentType::getDeleteFlag, 0).eq(BasePaymentType::getCreatedOrgId, ContextUtil.getCurrentCompanyId()));

        List<BasePaymentTypeResultVO> basePaymentTypeResultVOList = BeanUtil.copyToList(basePaymentTypeList, BasePaymentTypeResultVO.class);
        echoService.action(basePaymentTypeResultVOList);

        basePaymentTypeResultVOList.forEach(basePaymentTypeResultVO -> {
            if (StringUtils.isBlank(basePaymentTypeResultVO.getType())) {
                basePaymentTypeResultVO.setType("未设置");
            }
        });

        // 支付方式类型
        Map<String, List<BasePaymentTypeResultVO>> basePaymentTypeResultVOMap = basePaymentTypeResultVOList.stream()
                .collect(Collectors.groupingBy(BasePaymentTypeResultVO::getType, LinkedHashMap::new, Collectors.toList()));

        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        if (StringUtils.isNotBlank(params.getCompleteTime_st()) && StringUtils.isBlank(params.getCompleteTime_ed())) {
            params.setCompleteTime_ed(DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
        }
        List<PosCashResultVO> posCashResultVOS = posCashService.findAllResultVO(params);
        List<OrderRemarksExtraResultVO> orderRemarksExtraResultVOS = new ArrayList<>();
        posCashResultVOS.forEach(item -> {
            if (Objects.nonNull(item) && StringUtils.isNotBlank(item.getRemarks())) {
                OrderRemarksExtraResultVO orderRemarksExtraResultVO = JSON.parseObject(item.getRemarks(), OrderRemarksExtraResultVO.class);
                if (Objects.nonNull(orderRemarksExtraResultVO)) {
                    orderRemarksExtraResultVO.setId(item.getId());
                    orderRemarksExtraResultVOS.add(orderRemarksExtraResultVO);
                }
            }
        });
        echoService.action(orderRemarksExtraResultVOS);
        Map<Long, OrderRemarksExtraResultVO> orderRemarksExtraResultVOMap = orderRemarksExtraResultVOS.stream().collect(Collectors.toMap(OrderRemarksExtraResultVO::getId, Function.identity()));

        for (PosCashResultVO posCash : posCashResultVOS) {
            //相关备注
            OrderRemarksExtraResultVO orderRemarksResultVO = orderRemarksExtraResultVOMap.get(posCash.getId());
            if (Objects.nonNull(orderRemarksResultVO)) {
                String orderTags = orderRemarksResultVO.getOrderTags();
                posCash.setOrderRemarks("");
                if (StringUtils.isNotBlank(orderTags)) {
                    posCash.setOrderRemarks(orderTags);
                }
                String registrationTags = orderRemarksResultVO.getRegistrationTags();
                posCash.setRegistrationRemarks("");
                posCash.setRegistrationCustomRemarks(orderRemarksResultVO.getRegistrationRemarks());
                if (StringUtils.isNotBlank(registrationTags)) {
                    posCash.setRegistrationRemarks(registrationTags);
                }
                posCash.setRemarks("");
                if (StringUtils.isNotBlank(orderRemarksResultVO.getRemarks())) {
                    posCash.setRemarks(orderRemarksResultVO.getRemarks());
                }
            }
        }
        echoService.action(posCashResultVOS);
        Map<Long, PosCashThailAmountResultVO> thailAmountMap = new HashMap<>();
        if (CollUtil.isNotEmpty(posCashResultVOS)) {
            List<PosCashThailAmountResultVO> thailAmountList = posCashThailService.thailAmountList(params);
            thailAmountMap = thailAmountList.stream().collect(Collectors.toMap(PosCashThailAmountResultVO::getCashId, v -> v));
        }


        Map<Long, PosCashThailAmountResultVO> finalThailAmountMap = thailAmountMap;
        posCashResultVOS.forEach(v -> {
            PosCashThailAmountResultVO thailAmountResultVO = finalThailAmountMap.get(v.getId());
            if (ObjectUtil.isNotNull(thailAmountResultVO)) {
                v.setThailAmount(thailAmountResultVO.getThailAmount());
                v.setGroupBuyAmount(thailAmountResultVO.getGroupBuyAmount());
            } else {
                v.setThailAmount(BigDecimal.ZERO);
                v.setGroupBuyAmount(BigDecimal.ZERO);
            }
            String remarks = "";
            String registrationTags = "";
            if (CollUtil.isNotEmpty(v.getEchoMap()) &&
                    ObjectUtil.isNotNull(v.getEchoMap().get("orderRemarks"))) {
                remarks = remarks.concat(v.getEchoMap().get("orderRemarks").toString());
            }
            if (CollUtil.isNotEmpty(v.getEchoMap()) &&
                    ObjectUtil.isNotNull(v.getEchoMap().get("registrationRemarks"))) {
                registrationTags = registrationTags.concat(v.getEchoMap().get("registrationRemarks").toString());
            }
            if (StrUtil.isNotBlank(v.getRemarks())) {
                if (StrUtil.isNotBlank(remarks)) {
                    remarks = remarks.concat(" ");
                }
                remarks = remarks.concat(v.getRemarks());
            }
            if (StrUtil.isNotBlank(v.getRegistrationCustomRemarks())) {
                if (StrUtil.isNotBlank(registrationTags)) {
                    registrationTags = registrationTags.concat(" ");
                }
                registrationTags = registrationTags.concat(v.getRegistrationCustomRemarks());
            }
            v.setOrderRemarks(remarks);
            v.setRemarks(remarks);
            v.setRegistrationRemarks(registrationTags);
        });

        List<Long> cashIds = posCashResultVOS.stream().map(PosCashResultVO::getId).distinct()
                .collect(Collectors.toList());


        // 查询整单提成人
        List<PosCashCommenter> cashCommenterList = posCashCommenterService.list(Wraps.<PosCashCommenter>lbQ().eq(PosCashCommenter::getDeleteFlag, 0)
                .eq(PosCashCommenter::getType, PosCashConstant.Event.ORDER.getCode()).in(PosCashCommenter::getCashId, cashIds));
        List<PosCashCommenterResultVO> cashCommenterListResultVO = BeanPlusUtil.toBeanList(cashCommenterList, PosCashCommenterResultVO.class);
        echoService.action(cashCommenterListResultVO);

        Map<Long, List<PosCashCommenterResultVO>> cashCommenterListResultVOMap = cashCommenterListResultVO.stream().collect(Collectors.groupingBy(PosCashCommenterResultVO::getCashId));


        // 所有支付方式
        // 所有支付方式
        List<PosCashPayment> cashPaymentList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(cashIds)) {
            cashPaymentList = posCashPaymentService.list(Wraps.<PosCashPayment>lbQ()
                    .eq(PosCashPayment::getDeleteFlag, 0)
                    .eq(PosCashPayment::getStatus, PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                    .in(PosCashPayment::getCashId, cashIds.stream().distinct().collect(Collectors.toList())));
        }

        // 所有台桌id
        List<Long> tableIdList = posCashResultVOS.stream().map(PosCashResultVO::getTableId)
                .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<BaseTableInfo> tableInfoList = bizCacheService.cacheList(BaseTableInfo.class, BizCacheEnum.BASE_TABLE_INFO);
        tableInfoList = tableInfoList.stream().filter(v -> tableIdList.contains(v.getId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(tableInfoList)) {
            tableInfoList = Lists.newArrayList();
        }
        List<BaseTableInfoResultVO> tableInfoResultVOS = BeanUtil.copyToList(tableInfoList, BaseTableInfoResultVO.class);
        Map<Long, BaseTableArea> baseTableAreaMap = new HashMap<>();
        Map<String, BaseDict> baseDictMap = new HashMap<>();
        if (CollUtil.isNotEmpty(tableInfoResultVOS)) {
            baseTableAreaMap = baseTableAreaService.list(Wraps.<BaseTableArea>lbQ()
                            .in(BaseTableArea::getId, tableInfoResultVOS.stream().map(BaseTableInfoResultVO::getTableArea)
                                    .collect(Collectors.toList())))
                    .stream().collect(Collectors.toMap(BaseTableArea::getId, Function.identity()));
            baseDictMap = baseDictService.list(Wraps.<BaseDict>lbQ()
                            .eq(BaseDict::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                            .eq(BaseDict::getDeleteFlag, 0).eq(BaseDict::getParentKey,
                                    EchoDictType.Base.BASE_TABLE_TYPE))
                    .stream().collect(Collectors.toMap(BaseDict::getKey, Function.identity()));
        }

        // 会员信息
        Map<Long, MemberInfo> memberInfoMap = new HashMap<>();
        List<Long> memberIdList = posCashResultVOS.stream().map(PosCashResultVO::getMemberId)
                .filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(memberIdList)) {
            List<MemberInfo> memberInfoList = memberInfoService.list(Wraps.<MemberInfo>lbQ()
                    .in(MemberInfo::getId, memberIdList.stream().distinct().collect(Collectors.toList()))
                    .orderByDesc(MemberInfo::getCreatedTime));
            memberInfoMap = memberInfoList.stream().collect(Collectors.toMap(MemberInfo::getId, Function.identity()));
        }
        log.info("耗时：{}ms", (System.currentTimeMillis() - startTime));
        excelReportService.excelExport(tableColsService.cashDatailsTableColsList(basePaymentTypeResultVOMap),
                tableDataService.cashDatailsList(basePaymentTypeResultVOMap, posCashResultVOS, baseTableAreaMap, baseDictMap,
                        cashPaymentList, cashCommenterListResultVOMap, memberInfoMap)
                , response);
    }

    @Override
    public Map<String, Object> posCashOrderDetails(PageParams<PosCashDetailsQuery> params) {
        params.setSort("");
        params.setOrder("");
        initOrgIdList(params.getModel());

        // 所有支付方式
        List<BasePaymentType> basePaymentTypeList = basePaymentTypeService.list(Wraps.<BasePaymentType>lbQ().eq(BasePaymentType::getState, true)
                .eq(BasePaymentType::getDeleteFlag, 0).in(BasePaymentType::getCreatedOrgId, params.getModel().getOrgIdList()));

        List<BasePaymentTypeResultVO> basePaymentTypeResultVOList = BeanUtil.copyToList(basePaymentTypeList, BasePaymentTypeResultVO.class);
        echoService.action(basePaymentTypeResultVOList);

        List<ColumnVO> columnVOS = com.google.common.collect.Lists.newArrayList(
                ColumnVO.builder().name("code").label("订单尾号")
                        .width(120).emptyString("-").fixed(true).build(),  // * +后8位
                ColumnVO.builder().name("completeTime").label("结账时间")
                        .width(160).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type").label("业务类型")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("amount").label("订单实收金额")
                        .width(100).emptyString("-").fixed(false).build()  // 订单实收金额
        );
        for (BasePaymentTypeResultVO typeResultVO : basePaymentTypeResultVOList) {
            columnVOS.add(ColumnVO.builder().name("payment_" + typeResultVO.getId()).label(typeResultVO.getName())
                    .width(180).emptyString("-").fixed(false).build());
        }
        columnVOS.add(ColumnVO.builder().name("tableName").label("台桌名称")
                .width(100).emptyString("-").fixed(false).build());
        columnVOS.add(ColumnVO.builder().name("memberId").label("会员名称")
                .width(100).emptyString("-").fixed(false).build()); // 拼后四位手机号
        columnVOS.add(ColumnVO.builder().name("createdEmp").label("操作员工")
                .width(100).emptyString("-").fixed(false).build());
        columnVOS.add(ColumnVO.builder().name("completeEmp").label("完成员工")
                .width(100).emptyString("-").fixed(false).build());
        columnVOS.add(ColumnVO.builder().name("orderRemarks").label("整单备注")
                .width(160).emptyString("-").fixed(false).build());
        PosCashDetailsQuery model = params.getModel();
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(model);
        }
        LbQueryWrap<PosCash> wrap = Wraps.<PosCash>lbQ()
                .in(PosCash::getBillState, Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode()
                        , PosCashBillStateEnum.PART_REFUND.getCode()))
                .eq(StringUtils.isNotBlank(model.getType()), PosCash::getType, model.getType())
                .eq(StringUtils.isNotBlank(model.getOrderSource()), PosCash::getOrderSource, model.getOrderSource())
                .like(StringUtils.isNotBlank(model.getTableName()), PosCash::getTableName, model.getTableName())
                .like(StringUtils.isNotBlank(model.getKeyword()), PosCash::getCode, model.getKeyword())
                .like(StringUtils.isNotBlank(model.getCode()), PosCash::getCode, model.getCode())
                .eq(ObjectUtil.isNotNull(model.getEmployeeId()), PosCash::getEmployeeId, model.getEmployeeId())
                // 台桌类型小程序
                .in(PosCash::getBillType, Arrays.asList('0', '3', '4'))
                .in(CollUtil.isNotEmpty(model.getOrgIdList()), PosCash::getOrgId, model.getOrgIdList())
                .between(StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate()),
                        PosCash::getCompleteTime, model.getStartDate(), model.getEndDate())
                .ge(ObjectUtil.isNotNull(model.getCompleteTime_st()), PosCash::getCompleteTime, model.getCompleteTime_st())
                .le(ObjectUtil.isNotNull(model.getCompleteTime_ed()), PosCash::getCompleteTime, model.getCompleteTime_ed())
                .inSql(ObjectUtil.isNotNull(model.getCommenter()), PosCash::getId, "select pcc.cash_id from pos_cash_commenter pcc " +
                        "where pcc.delete_flag = 0 and pcc.employee_id = " + model.getCommenter())
                .inSql(StringUtils.isNotBlank(model.getMemberName()), PosCash::getMemberId, "select mi.id from member_info mi " +
                        "where mi.delete_flag = 0 and instr(mi.name, '" + model.getMemberName() + "')");
        if (StringUtils.isNotBlank(model.getTableType()) || StringUtils.isNotBlank(model.getTableArea())) {
            String tableInfoSql = "select t.id from base_table_info t where t.delete_flag = 0 ";
            if (StringUtils.isNotBlank(model.getTableType())) {
                tableInfoSql = tableInfoSql + " and t.table_type = " + model.getTableType();
            }
            if (StringUtils.isNotBlank(model.getTableArea())) {
                tableInfoSql = tableInfoSql + " and t.table_area = " + model.getTableArea();
            }
            wrap.inSql(PosCash::getTableId, tableInfoSql);
        }
        wrap.orderByDesc(PosCash::getCompleteTime).orderByDesc(SuperEntity::getId);
        IPage<PosCash> ipage = posCashService.page(params.buildPage(PosCash.class), wrap);
        IPage<Map> pageList = BeanPlusUtil.toBeanPage(ipage, Map.class);
        List<PosCash> posCashList = ipage.getRecords();
        List<PosCashResultVO> posCashResultVOS = BeanUtil.copyToList(posCashList, PosCashResultVO.class);
        echoService.action(posCashResultVOS);
        for (PosCashResultVO posCash : posCashResultVOS) {
            //相关备注
            if (StrUtil.isNotBlank(posCash.getRemarks())) {
                OrderRemarksResultVO orderRemarksResultVO = JSON.parseObject(posCash.getRemarks(), OrderRemarksResultVO.class);
                echoService.action(orderRemarksResultVO);
                // 整单备注
                String orderTags = ((CollUtil.isNotEmpty(orderRemarksResultVO.getEchoMap()) && ObjectUtil.isNotNull(orderRemarksResultVO.getEchoMap().get("orderTags"))) ? orderRemarksResultVO.getEchoMap().get("orderTags").toString() : "");
                if (StrUtil.isNotBlank(orderTags)) {
                    if (StringUtils.isNotBlank(orderRemarksResultVO.getRemarks())) {
                        orderTags = orderTags + "," + orderRemarksResultVO.getRemarks();
                    }
                } else {
                    if (StringUtils.isNotBlank(orderRemarksResultVO.getRemarks())) {
                        orderTags = orderRemarksResultVO.getRemarks();
                    }
                }
                if (StringUtils.isNotBlank(orderTags)) {
                    posCash.setOrderRemarks(orderTags);
                } else {
                    posCash.setOrderRemarks("-");
                }
            }
        }

        List<Long> cashIds = posCashList.stream().map(PosCash::getId)
                .collect(Collectors.toList());

        // 查询整单提成人
        List<PosCashCommenter> cashCommenterList = posCashCommenterService.list(Wraps.<PosCashCommenter>lbQ().eq(PosCashCommenter::getDeleteFlag, 0)
                .eq(PosCashCommenter::getType, PosCashConstant.Event.ORDER.getCode()).in(PosCashCommenter::getCashId, cashIds));
        List<PosCashCommenterResultVO> cashCommenterListResultVO = BeanPlusUtil.toBeanList(cashCommenterList, PosCashCommenterResultVO.class);
        echoService.action(cashCommenterListResultVO);

        Map<Long, List<PosCashCommenterResultVO>> cashCommenterListResultVOMap = cashCommenterListResultVO.stream().collect(Collectors.groupingBy(PosCashCommenterResultVO::getCashId));


        // 所有支付方式
        List<PosCashPayment> cashPaymentList = posCashPaymentService.list(Wraps.<PosCashPayment>lbQ()
                .in(PosCashPayment::getStatus, Arrays.asList(PosCashPaymentStatusEnum.PAY_SUCCESS.getCode()))
                .in(PosCashPayment::getCashId, cashIds));


        // 所有台桌id
//        List<Long> tableIdList = posCashResultVOS.stream().map(PosCashResultVO::getTableId).collect(Collectors.toList());
//        List<BaseTableInfo> tableList = baseTableInfoService.list(Wraps.<BaseTableInfo>lbQ().in(BaseTableInfo::getId, tableIdList));
//        List<BaseTableInfoResultVO> tableInfoResultVOS = BeanUtil.copyToList(tableList, BaseTableInfoResultVO.class);
//        echoService.action(tableInfoResultVOS);
        // 台桌
//        Map<Long, BaseTableInfoResultVO> tableInfoResultVOMap = tableInfoResultVOS.stream().collect(Collectors.toMap(BaseTableInfoResultVO::getId, Function.identity()));

        List<Long> memberIds = posCashResultVOS.stream().map(PosCashResultVO::getMemberId).distinct().collect(Collectors.toList());
        // 所有手机号
        List<MemberInfo> memberInfoList = memberInfoService.list(Wraps.<MemberInfo>lbQ().in(MemberInfo::getId, memberIds));
        Map<Long, MemberInfo> memberInfoMap = memberInfoList.stream().collect(Collectors.toMap(MemberInfo::getId, Function.identity()));


        List<Map> resultMapList = new ArrayList<>();
        for (PosCashResultVO posCashResultVO : posCashResultVOS) {
            if (posCashResultVO.getRefundAmount() == null) {
                posCashResultVO.setRefundAmount(BigDecimal.ZERO);
            }
            if (posCashResultVO.getPaid() == null) {
                posCashResultVO.setPaid(BigDecimal.ZERO);
            }

            Map<String, Object> tableInfoMap = new HashMap();
            // 取 posCashResultVO.getCode() 的后8位
            tableInfoMap.put("code", "*" + posCashResultVO.getCode().substring(posCashResultVO.getCode().length() - 8));
            tableInfoMap.put("type", posCashResultVO.getEchoMap().get("type"));
            tableInfoMap.put("tableName", posCashResultVO.getTableName());
//            tableInfoMap.put("tableType", Objects.nonNull(tableInfoResultVOMap.get(posCashResultVO.getTableId())) ? tableInfoResultVOMap.get(posCashResultVO.getTableId()).getEchoMap().get("tableType") : null);
//            tableInfoMap.put("tableArea", Objects.nonNull(tableInfoResultVOMap.get(posCashResultVO.getTableId())) ? tableInfoResultVOMap.get(posCashResultVO.getTableId()).getEchoMap().get("tableArea") : null);
            tableInfoMap.put("createdEmp", posCashResultVO.getEchoMap().get("createdEmp"));
            tableInfoMap.put("completeEmp", posCashResultVO.getEchoMap().get("completeEmp"));
            tableInfoMap.put("employeeEmp", posCashResultVO.getEchoMap().get("employeeId"));
            if (CollUtil.isNotEmpty(cashCommenterListResultVOMap.get(posCashResultVO.getId()))) {
                tableInfoMap.put("commenter", cashCommenterListResultVOMap.get(posCashResultVO.getId())
                        .stream().filter(c -> Objects.nonNull(c.getEchoMap().get("employeeId"))).map(s -> s.getEchoMap().get("employeeId").toString()).collect(Collectors.joining(",")));
            } else {
                tableInfoMap.put("commenter", "-");
            }

//            tableInfoMap.put("orderSource", posCashResultVO.getEchoMap().get("orderSource"));
            tableInfoMap.put("memberId", posCashResultVO.getEchoMap().get("memberId"));
            if (Objects.nonNull(tableInfoMap.get("memberId"))) {
                if (Objects.nonNull(posCashResultVO.getMemberId()) && Objects.nonNull(memberInfoMap.get(posCashResultVO.getMemberId()))
                        && StringUtils.isNotBlank(memberInfoMap.get(posCashResultVO.getMemberId()).getMobile())) {
                    tableInfoMap.put("memberId", tableInfoMap.get("memberId").toString().concat("(").concat(memberInfoMap.get(posCashResultVO.getMemberId()).getMobile().substring(memberInfoMap.get(posCashResultVO.getMemberId()).getMobile().length() - 4)).concat(")"));
                }
            }
            tableInfoMap.put("createTime", posCashResultVO.getCreatedTime());
            tableInfoMap.put("completeTime", posCashResultVO.getCompleteTime());
            tableInfoMap.put("amount", posCashResultVO.getPaid().subtract(Objects.nonNull(posCashResultVO.getRefundAmount()) ? posCashResultVO.getRefundAmount() : BigDecimal.ZERO));
            tableInfoMap.put("orderRemarks", posCashResultVO.getOrderRemarks());
            // 所有支付方式,
            for (BasePaymentType basePaymentTypeResultVO : basePaymentTypeList) {
                List<PosCashPayment> posCashPayments = cashPaymentList.stream().filter(c ->
                                Objects.equals(basePaymentTypeResultVO.getId(), c.getPayTypeId())
                                        && Objects.equals(posCashResultVO.getId(), c.getCashId()) && Objects.nonNull(c.getAmount()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(posCashPayments)) {
                    tableInfoMap.put("payment_" + basePaymentTypeResultVO.getId(),
                            posCashPayments.stream().filter(c -> Objects.nonNull(c.getAmount())).map(PosCashPayment::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .subtract(posCashPayments.stream().filter(c -> Objects.nonNull(c.getRefundAmount())).map(PosCashPayment::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                                    .subtract(posCashPayments.stream().filter(c -> Objects.nonNull(c.getChangeAmount())).map(PosCashPayment::getChangeAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
                } else {
                    tableInfoMap.put("payment_" + basePaymentTypeResultVO.getId(), 0.00);
                }
            }

            resultMapList.add(tableInfoMap);
        }

        pageList.setRecords(resultMapList);
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOS);
        return objectMap;
    }

    @Override
    public Map<String, Object> posCashCommenter(PageParams<PosCashDetailsQuery> params) {
        PosCashDetailsQuery model = params.getModel();
        initOrgIdList(model);
        if (Objects.nonNull(model) && StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(model);
        }

        params.setSort(null);
        params.setOrder(null);
        IPage<Map<String, Object>> pageList = statisticsMapper.posCashCommenter(params.buildPage(Map.class), model);

        List<ColumnVO> columnVOList = com.google.common.collect.Lists.newArrayList(
                ColumnVO.builder().name("commenter").label("提成员工")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("tableAmount").label("台桌金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("productAmount").label("商品金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceAmount").label("服务金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("thailAmount").label("套餐金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("订单金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("discountAmount").label("订单优惠金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payment").label("订单应收金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("paid").label("订单收款金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("refundAmount").label("订单退款金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(200).emptyString("-").fixed(false).build()
        );
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public Map<String, Object> posCashCommenterSum(PosCashDetailsQuery params) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        return statisticsMapper.posCashCommenterSum(params);
    }

    @Override
    public void posCashCommenterExport(PosCashDetailsQuery params, HttpServletResponse response) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        List<Map<String, Object>> resultVOList = statisticsMapper.posCashCommenterExport(params);
        List<ColumnVO> columnVOList = com.google.common.collect.Lists.newArrayList(
                ColumnVO.builder().name("commenter").label("提成员工")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("tableAmount").label("台桌金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("productAmount").label("商品金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceAmount").label("服务金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("thailAmount").label("套餐金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("订单金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("discountAmount").label("订单优惠金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payment").label("订单应收金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("paid").label("订单收款金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("refundAmount").label("订单退款金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(200).emptyString("-").fixed(false).build()
        );

        Map<String, Object> sumMap = posCashCommenterSum(params);
        if (CollUtil.isEmpty(sumMap)) {
            sumMap = MapUtil.newHashMap();
        }
        sumMap.put("commenter", "总计");

        resultVOList.add(sumMap);
        export(response, resultVOList, columnVOList, "员工提成统计");
    }

    private static void exportPosCashDetails(HttpServletResponse response, Map<String, List<BasePaymentTypeResultVO>> basePaymentTypeResultVOMap,
                                             List<PosCashResultVO> posCashResultVOS, Map<Long, BaseTableInfoResultVO> tableInfoResultVOMap,
                                             List<PosCashPayment> cashPaymentList, Map<Long, List<PosCashCommenterResultVO>> cashCommenterListResultVOMap,
                                             Map<Long, MemberInfo> memberInfoMap) {
        List<ColumnVO> columnVOList = com.google.common.collect.Lists.newArrayList(
                ColumnVO.builder().name("code").label("流水号")
                        .width(160).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("type").label("业务类型")
                        .width(100).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("tableName").label("台桌名称")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableType").label("台桌类型")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableArea").label("台桌区域")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createdEmp").label("操作员工")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeEmp").label("完成员工")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("employeeEmp").label("销售员工")// employeeId
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("commenter").label("提成员工")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orderSource").label("订单来源")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("memberId").label("会员名称")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createTime").label("开台时间")
                        .width(160).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeTime").label("结账时间")
                        .width(160).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableAmount").label("台桌金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("productAmount").label("商品金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceAmount").label("服务金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("thailAmount").label("套餐金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("订单原价")// 原本是订单金额,原价 54.72, amount 字段
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("discountAmount").label("订单优惠金额")
                        .width(100).emptyString("-").fixed(false).build(),
//                ColumnVO.builder().name("payment").label("订单应收金额")
//                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("refundAmount").label("订单部分退款金额")
                        .width(100).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("paid").label("订单实收金额")
                        .width(100).emptyString("-").fixed(false).build(),// amount - discountAmount - refundAmount
                ColumnVO.builder().name("orderRemarks").label("整单备注")
                        .width(140).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(200).emptyString("-").fixed(false).build()
        );

        for (String type : basePaymentTypeResultVOMap.keySet()) {
            basePaymentTypeResultVOMap.get(type).forEach(basePaymentTypeResultVO -> {
                columnVOList.add(ColumnVO.builder().name("payment_" + basePaymentTypeResultVO.getId()).label(basePaymentTypeResultVO.getName())
                        .width(180).emptyString("-").fixed(false).build());
            });
        }

        // 导出

        // 1.获取ExcelWriter对象
        ExcelWriter writer = ExcelUtil.getBigWriter();

        // ...
        // 3.定义表头单元格样式(可选)
        StyleSet style = writer.getStyleSet();
        CellStyle headCellStyle = style.getHeadCellStyle();
        // 自动换行
        headCellStyle.setWrapText(true);
        // 水平居中
        headCellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 上下居中
        headCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置表头字体大小
        Font headFont = writer.createFont();
        headFont.setFontName("宋体");
        // 设置字体大小为15磅
        headFont.setFontHeightInPoints((short) 15);
        headCellStyle.setFont(headFont);
        // 4.定义内容单元格样式(可选)
        CellStyle cellStyle = style.getCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        // 设置字体大小
        Font font = writer.createFont();
        font.setFontName("宋体");
        cellStyle.setFont(font);
        // 5.其他设置(可选)
        // 只写出设置别名的属性
        writer.setOnlyAlias(true);
        // 冻结行
        writer.setFreezePane(2);

        writer.merge(0, 0, 0, 21, "", true);
        Integer index = 21;
        for (String type : basePaymentTypeResultVOMap.keySet()) {
            if (basePaymentTypeResultVOMap.get(type).size() > 1) {
                writer.merge(0, 0, index + 1, index + basePaymentTypeResultVOMap.get(type).size(), basePaymentTypeResultVOMap.get(type).get(0).getEchoMap().get("type"), true);
            } else {
                writer.writeCellValue(index + 1, 0, basePaymentTypeResultVOMap.get(type).get(0).getEchoMap().get("type"));
                writer.setStyle(writer.getHeadCellStyle(), index + 1, 0);
            }

            index = index + basePaymentTypeResultVOMap.get(type).size();
        }
        writer.passCurrentRow();
        writer.writeHeadRow(columnVOList.stream().map(ColumnVO::getLabel).collect(Collectors.toList()));

        // 6.写入数据 设置列宽
        for (int i = 0; i < posCashResultVOS.size(); i++) {
            List<Object> row = new ArrayList<>();
            PosCashResultVO posCashResultVO = posCashResultVOS.get(i);

            row.add(posCashResultVO.getCode());
            row.add(ObjectUtil.isNotNull(posCashResultVO.getEchoMap().get("type")) ? posCashResultVO.getEchoMap().get("type") : "-");
            row.add(StringUtils.isNotBlank(posCashResultVO.getTableName()) ? posCashResultVO.getTableName() : "-");
            row.add(Objects.nonNull(tableInfoResultVOMap.get(posCashResultVO.getTableId())) ? (ObjectUtil.isNotNull(tableInfoResultVOMap.get(posCashResultVO.getTableId()).getEchoMap().get("tableType")) ? tableInfoResultVOMap.get(posCashResultVOS.get(i).getTableId()).getEchoMap().get("tableType").toString() : "-") : "-");
            row.add(Objects.nonNull(tableInfoResultVOMap.get(posCashResultVO.getTableId())) ? (ObjectUtil.isNotNull(tableInfoResultVOMap.get(posCashResultVO.getTableId()).getEchoMap().get("tableArea")) ? tableInfoResultVOMap.get(posCashResultVOS.get(i).getTableId()).getEchoMap().get("tableArea").toString() : "-") : "-");
            row.add(Objects.nonNull(posCashResultVO.getEchoMap().get("createdEmp")) ? posCashResultVO.getEchoMap().get("createdEmp").toString() : "-");
            row.add(Objects.nonNull(posCashResultVO.getEchoMap().get("employeeEmp")) ? posCashResultVO.getEchoMap().get("employeeId").toString() : "-");
            String commenter = null;
            if (CollUtil.isNotEmpty(cashCommenterListResultVOMap.get(posCashResultVO.getId()))) {
                commenter = cashCommenterListResultVOMap.get(posCashResultVO.getId())
                        .stream().filter(c -> Objects.nonNull(c.getEchoMap().get("employeeId")))
                        .map(s -> s.getEchoMap().get("employeeId").toString()).collect(Collectors.joining(","));
            }
            row.add(StringUtils.isNotBlank(commenter) ? commenter : "-");
            row.add(Objects.nonNull(posCashResultVO.getEchoMap().get("orderSource")) ? posCashResultVO.getEchoMap().get("orderSource").toString() : "-");
            row.add((Objects.nonNull(posCashResultVO.getMemberId()) && Objects.nonNull(memberInfoMap.get(posCashResultVO.getMemberId()))) ? memberInfoMap.get(posCashResultVO.getMemberId()).getName() : "-");
            row.add(Objects.nonNull(posCashResultVO.getCreatedTime()) ? DateUtils.format(posCashResultVO.getCreatedTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT) : "-");
            row.add(Objects.nonNull(posCashResultVO.getCompleteTime()) ? DateUtils.format(posCashResultVO.getCompleteTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT) : "-");
            row.add(Objects.nonNull(posCashResultVO.getTableAmount()) ? posCashResultVO.getTableAmount() : 0.00);
            row.add(Objects.nonNull(posCashResultVO.getProductAmount()) ? posCashResultVO.getProductAmount() : 0.00);
            row.add(Objects.nonNull(posCashResultVO.getServiceAmount()) ? posCashResultVO.getServiceAmount() : 0.00);
            row.add(Objects.nonNull(posCashResultVO.getThailAmount()) ? posCashResultVO.getThailAmount() : 0.00);
            row.add(Objects.nonNull(posCashResultVO.getAmount()) ? posCashResultVO.getAmount() : 0.00);
            row.add(Objects.nonNull(posCashResultVO.getDiscountAmount()) ? posCashResultVO.getDiscountAmount() : 0.00);
            if (StringUtils.equals(posCashResultVO.getBillState(), PosCashBillStateEnum.REFUNDED.getCode())) {
                row.add(0.00);
            } else {
                row.add(Objects.nonNull(posCashResultVO.getRefundAmount()) ? posCashResultVO.getRefundAmount() : 0.00);
            }

            //    row.add(Objects.nonNull(posCashResultVO.getPaidIn()) ? posCashResultVO.getPaidIn() : 0.00);
            // 整单退, 实付金额 不需要减去 退款金额
            BigDecimal paid = BigDecimal.ZERO;
            // 判断是不是整单退
            if (StringUtils.equals(posCashResultVO.getBillState(), PosCashBillStateEnum.REFUNDED.getCode())) {
                paid = posCashResultVO.getAmount()
                        .subtract(Objects.nonNull(posCashResultVO.getDiscountAmount()) ? posCashResultVO.getDiscountAmount() : BigDecimal.ZERO);
            } else {
                paid = posCashResultVO.getAmount()
                        .subtract(Objects.nonNull(posCashResultVO.getDiscountAmount()) ? posCashResultVO.getDiscountAmount() : BigDecimal.ZERO)
                        .subtract(Objects.nonNull(posCashResultVO.getRefundAmount()) ? posCashResultVO.getRefundAmount() : BigDecimal.ZERO);
            }
            row.add(paid);
            row.add(StringUtils.isNotBlank(posCashResultVO.getOrderRemarks()) ? posCashResultVO.getOrderRemarks() : "-");
            row.add(Objects.nonNull(posCashResultVO.getEchoMap().get("orgId")) ? posCashResultVO.getEchoMap().get("orgId").toString() : "-");
            // 支付详情
            for (String type : basePaymentTypeResultVOMap.keySet()) {
                int finalI = i;
                basePaymentTypeResultVOMap.get(type).forEach(basePaymentTypeResultVO -> {
                    if (StringUtils.equals(posCashResultVO.getBillState(), PosCashBillStateEnum.REFUNDED.getCode())) {
                        // 所有支付方式
                        BigDecimal paymentAmount = cashPaymentList.stream().filter(c -> Objects.equals(c.getCashId(), posCashResultVOS.get(finalI).getId()) && Objects.equals(c.getPayTypeId(), basePaymentTypeResultVO.getId())).map(PosCashPayment::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                                .subtract(cashPaymentList.stream().filter(c -> Objects.equals(c.getCashId(), posCashResultVOS.get(finalI).getId()) && Objects.equals(c.getPayTypeId(), basePaymentTypeResultVO.getId()) && Objects.nonNull(c.getChangeAmount())).map(PosCashPayment::getChangeAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                        row.add(paymentAmount);
                    } else {
                        // 所有支付方式
                        BigDecimal paymentAmount = cashPaymentList.stream().filter(c -> Objects.equals(c.getCashId(), posCashResultVOS.get(finalI).getId()) && Objects.equals(c.getPayTypeId(), basePaymentTypeResultVO.getId())).map(PosCashPayment::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                                .subtract(cashPaymentList.stream().filter(c -> Objects.equals(c.getCashId(), posCashResultVOS.get(finalI).getId()) && Objects.equals(c.getPayTypeId(), basePaymentTypeResultVO.getId()) && Objects.nonNull(c.getRefundAmount())).map(PosCashPayment::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                                .subtract(cashPaymentList.stream().filter(c -> Objects.equals(c.getCashId(), posCashResultVOS.get(finalI).getId()) && Objects.equals(c.getPayTypeId(), basePaymentTypeResultVO.getId()) && Objects.nonNull(c.getChangeAmount())).map(PosCashPayment::getChangeAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                        row.add(paymentAmount);
                    }
                });
            }
            writer.writeRow(row);

            if (StringUtils.equals(posCashResultVO.getBillState(), PosCashBillStateEnum.REFUNDED.getCode())) {
                List<Object> refundedRow = new ArrayList<>();

                refundedRow.add(posCashResultVO.getCode());
                refundedRow.add(ObjectUtil.isNotNull(posCashResultVO.getEchoMap().get("type")) ? posCashResultVO.getEchoMap().get("type") : "-");
                refundedRow.add(StringUtils.isNotBlank(posCashResultVO.getTableName()) ? posCashResultVO.getTableName() : "-");
                refundedRow.add(Objects.nonNull(tableInfoResultVOMap.get(posCashResultVO.getTableId())) ? (ObjectUtil.isNotNull(tableInfoResultVOMap.get(posCashResultVO.getTableId()).getEchoMap().get("tableType")) ? tableInfoResultVOMap.get(posCashResultVOS.get(i).getTableId()).getEchoMap().get("tableType").toString() : "-") : "-");
                refundedRow.add(Objects.nonNull(tableInfoResultVOMap.get(posCashResultVO.getTableId())) ? (ObjectUtil.isNotNull(tableInfoResultVOMap.get(posCashResultVO.getTableId()).getEchoMap().get("tableArea")) ? tableInfoResultVOMap.get(posCashResultVOS.get(i).getTableId()).getEchoMap().get("tableArea").toString() : "-") : "-");
                refundedRow.add(Objects.nonNull(posCashResultVO.getEchoMap().get("createdEmp")) ? posCashResultVO.getEchoMap().get("createdEmp").toString() : "-");
                refundedRow.add(Objects.nonNull(posCashResultVO.getEchoMap().get("employeeEmp")) ? posCashResultVO.getEchoMap().get("employeeId").toString() : "-");
                refundedRow.add(StringUtils.isNotBlank(commenter) ? commenter : "-");
                refundedRow.add(Objects.nonNull(posCashResultVO.getEchoMap().get("orderSource")) ? posCashResultVO.getEchoMap().get("orderSource").toString() : "-");
                refundedRow.add(Objects.nonNull(posCashResultVO.getEchoMap().get("memberId")) ? posCashResultVO.getEchoMap().get("memberId").toString() : "-");
                refundedRow.add(Objects.nonNull(posCashResultVO.getCreatedTime()) ? DateUtils.format(posCashResultVO.getCreatedTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT) : "-");
                refundedRow.add(Objects.nonNull(posCashResultVO.getCompleteTime()) ? DateUtils.format(posCashResultVO.getCompleteTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT) : "-");
                refundedRow.add(BigDecimal.ZERO.subtract(Objects.nonNull(posCashResultVO.getTableAmount()) ? posCashResultVO.getTableAmount() : BigDecimal.ZERO));
                refundedRow.add(BigDecimal.ZERO.subtract(Objects.nonNull(posCashResultVO.getProductAmount()) ? posCashResultVO.getProductAmount() : BigDecimal.ZERO));
                refundedRow.add(BigDecimal.ZERO.subtract(Objects.nonNull(posCashResultVO.getServiceAmount()) ? posCashResultVO.getServiceAmount() : BigDecimal.ZERO));
                refundedRow.add(BigDecimal.ZERO.subtract(Objects.nonNull(posCashResultVO.getThailAmount()) ? posCashResultVO.getThailAmount() : BigDecimal.ZERO));
                refundedRow.add(BigDecimal.ZERO.subtract(Objects.nonNull(posCashResultVO.getAmount()) ? posCashResultVO.getAmount() : BigDecimal.ZERO));
                refundedRow.add(BigDecimal.ZERO.subtract(Objects.nonNull(posCashResultVO.getDiscountAmount()) ? posCashResultVO.getDiscountAmount() : BigDecimal.ZERO));
                refundedRow.add(0.00);

                //    row.add(Objects.nonNull(posCashResultVO.getPaidIn()) ? posCashResultVO.getPaidIn() : 0.00);
                BigDecimal refundedPaid = posCashResultVO.getAmount()
                        .subtract(Objects.nonNull(posCashResultVO.getDiscountAmount()) ? posCashResultVO.getDiscountAmount() : BigDecimal.ZERO);
                refundedRow.add(BigDecimal.ZERO.subtract(refundedPaid));
                refundedRow.add(StringUtils.isNotBlank(posCashResultVO.getOrderRemarks()) ? posCashResultVO.getOrderRemarks() : "-");
                refundedRow.add(Objects.nonNull(posCashResultVO.getEchoMap().get("orgId")) ? posCashResultVO.getEchoMap().get("orgId").toString() : "-");
                // 支付详情
                for (String type : basePaymentTypeResultVOMap.keySet()) {
                    int finalI = i;
                    basePaymentTypeResultVOMap.get(type).forEach(basePaymentTypeResultVO -> {
                        // 所有支付方式
                        BigDecimal paymentAmount = cashPaymentList.stream().filter(c -> Objects.equals(c.getCashId(), posCashResultVOS.get(finalI).getId()) && Objects.equals(c.getPayTypeId(), basePaymentTypeResultVO.getId())).map(PosCashPayment::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                                .subtract(cashPaymentList.stream().filter(c -> Objects.equals(c.getCashId(), posCashResultVOS.get(finalI).getId()) && Objects.equals(c.getPayTypeId(), basePaymentTypeResultVO.getId()) && Objects.nonNull(c.getChangeAmount())).map(PosCashPayment::getChangeAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                        refundedRow.add(BigDecimal.ZERO.subtract(paymentAmount));
                    });
                }
                writer.writeRow(refundedRow);
            }
        }
        BigDecimal amount = posCashResultVOS.stream().map(PosCashResultVO::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal discountAmount = posCashResultVOS.stream().map(PosCashResultVO::getDiscountAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal refundAmount = posCashResultVOS.stream().filter(s -> !StringUtils.equals(s.getBillState(), PosCashBillStateEnum.REFUNDED.getCode()))
                .map(PosCashResultVO::getRefundAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal paid = amount.subtract(discountAmount).subtract(refundAmount);
        // 添加最后一行合计
        List<Object> row = new ArrayList<>();
        row.addAll(Arrays.asList("合计", "-", "-", "-", "-", "-", "-", "-", "-", "-", "-", "-",
                posCashResultVOS.stream().map(PosCashResultVO::getTableAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add),
                posCashResultVOS.stream().map(PosCashResultVO::getProductAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add),
                posCashResultVOS.stream().map(PosCashResultVO::getServiceAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add),
                posCashResultVOS.stream().map(PosCashResultVO::getThailAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add),
                amount,
                discountAmount,
                refundAmount,
                paid,
                "-", "-"));
        // 支付详情
        for (String type : basePaymentTypeResultVOMap.keySet()) {
            List<BasePaymentTypeResultVO> typeResultVOList = basePaymentTypeResultVOMap.get(type);
            for (BasePaymentTypeResultVO basePaymentTypeResultVO : typeResultVOList) {
                List<PosCashPayment> posCashPaymentList = cashPaymentList.stream().filter(c -> Objects.equals(basePaymentTypeResultVO.getId(), c.getPayTypeId()) && Objects.nonNull(c.getAmount())).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(posCashPaymentList)) {
                    row.add(posCashPaymentList.stream().filter(c -> Objects.nonNull(c.getAmount())).map(PosCashPayment::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                            .subtract(posCashPaymentList.stream().filter(c -> Objects.nonNull(c.getRefundAmount())).map(PosCashPayment::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                            .subtract(posCashPaymentList.stream().filter(c -> Objects.nonNull(c.getChangeAmount())).map(PosCashPayment::getChangeAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
                } else {
                    row.add(0.00);
                }
            }
        }
        writer.writeRow(row);

        writer.autoSizeColumnAll();
        // 7.开启筛选(可选)
        //CellRangeAddress filterRange = CellRangeAddress.valueOf("B1:D1");
        //writer.getSheet().setAutoFilter(filterRange);
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            // 8.导出
            response.setCharacterEncoding(CharsetUtil.UTF_8);
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("订单明细表", StandardCharsets.UTF_8.toString()) + ".xlsx");
            response.setContentType("application/vnd.ms-excel;" + CharsetUtil.UTF_8);
            writer.flush(outputStream);
            writer.close();
        } catch (Exception e) {
            log.warn("批量导出出错：{}", e.getMessage());
        } finally {
            writer.close();
        }
    }

    @Override
    public Map<String, Object> posCashDiscountTypeStatistics(PageParams<PosCashDetailsQuery> params) {
        PosCashDetailsQuery model = params.getModel();
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(model);
        }
        params.setSort(null);
        params.setOrder(null);
        IPage<Map<String, Object>> pageList = statisticsMapper.posCashDiscountTypeStatistics(params.buildPage(Map.class), model);
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("discountName").label("优惠名称")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("num").label("订单量")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("discountAmount").label("折扣金额(元)")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(180).emptyString("-").fixed(false).build()
        );
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public Map<String, Object> posCashDiscountTypeStatisticsSum(PosCashDetailsQuery params) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        return statisticsMapper.posCashDiscountTypeStatisticsSum(params);
    }

    @Override
    public void posCashDiscountTypeStatisticsExport(PosCashDetailsQuery params, HttpServletResponse response) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }

        List<Map<String, Object>> resultVOList = statisticsMapper.posCashDiscountTypeStatisticsExport(params);
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("discountName").label("优惠名称")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("num").label("订单量")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("discountAmount").label("折扣金额(元)")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(180).emptyString("-").fixed(false).build()
        );

        Map<String, Object> sumMap = new HashMap<>();
        sumMap.put("discountName", "合计");
        sumMap.put("num", resultVOList.stream()
                .filter(map -> map.get("num") != null && map.get("num") != "-")
                .mapToInt(map -> Integer.parseInt(map.get("num").toString()))
                .sum());
        sumMap.put("discountAmount", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("discountAmount") != null && !StringUtils.equals(map.get("discountAmount").toString(), "-"))
                .mapToDouble(map -> Double.parseDouble(map.get("discountAmount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));

        resultVOList.add(sumMap);
        export(response, resultVOList, columnVOList, "订单优惠方式统计");
    }

    @Override
    public Map<String, Object> posCashDiscountTypeDetails(PageParams<PosCashDetailsQuery> params) {
        PosCashDetailsQuery model = params.getModel();
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(model);
        }
        params.setSort(null);
        params.setOrder(null);
        IPage<Map<String, Object>> pageList = statisticsMapper.posCashDiscountTypeDetails(params.buildPage(Map.class), model);
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("discountName").label("优惠名称")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("discountAmount").label("优惠金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableName").label("台桌")
                        .width(180).emptyString("-").fixed(true).build(),
//                ColumnVO.builder().name("payment").label("订单实收")
//                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("code").label("订单号")
                        .width(200).emptyString("-").fixed(false).build(),
//                ColumnVO.builder().name("previousPrice").label("优惠前金额")
//                        .width(180).emptyString("-").fixed(false).build(),
//                ColumnVO.builder().name("currPrice").label("优惠后金额")
//                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeTime").label("订单完成时间")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createEmp").label("操作员工")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(180).emptyString("-").fixed(false).build()
        );
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public Map<String, Object> posCashDiscountTypeDetailsSum(PosCashDetailsQuery params) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        return statisticsMapper.posCashDiscountTypeDetailsSum(params);
    }

    @Override
    public void posCashDiscountTypeDetailsExport(PosCashDetailsQuery params, HttpServletResponse response) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        List<Map<String, Object>> resultVOList = statisticsMapper.posCashDiscountTypeDetailsExport(params);
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("discountName").label("优惠名称")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("discountAmount").label("优惠金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableName").label("台桌")
                        .width(180).emptyString("-").fixed(true).build(),
//                ColumnVO.builder().name("payment").label("订单实收")
//                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("code").label("订单号")
                        .width(200).emptyString("-").fixed(false).build(),
//                ColumnVO.builder().name("previousPrice").label("优惠前金额")
//                        .width(180).emptyString("-").fixed(false).build(),
//                ColumnVO.builder().name("currPrice").label("优惠后金额")
//                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeTime").label("订单完成时间")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createEmp").label("操作员工")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(180).emptyString("-").fixed(false).build()
        );

        Map<String, Object> sumMap = new HashMap<>();
        sumMap.put("discountName", "合计");
//        sumMap.put("payment", BigDecimal.valueOf(resultVOList.stream()
//                .filter(map -> map.get("payment") != null && !StringUtils.equals(map.get("payment").toString(), "-"))
//                .mapToDouble(map -> Double.parseDouble(map.get("payment").toString()))
//                .sum()).setScale(2, RoundingMode.HALF_UP));
        sumMap.put("discountAmount", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("discountAmount") != null && !StringUtils.equals(map.get("discountAmount").toString(), "-"))
                .mapToDouble(map -> Double.parseDouble(map.get("discountAmount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));

        resultVOList.add(sumMap);
        export(response, resultVOList, columnVOList, "订单优惠方式明细");
    }

    @Override
    public Map<String, Object> posCashDiscountTypeActivity(PageParams<PosCashDetailsQuery> params) {
        PosCashDetailsQuery model = params.getModel();
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(model);
        }
        params.setSort(null);
        params.setOrder(null);
        IPage<Map<String, Object>> pageList = statisticsMapper.posCashDiscountTypeActivity(params.buildPage(Map.class), model);
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("discountName").label("优惠活动名称")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("num").label("订单量")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("discountAmount").label("折扣金额(元)")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(180).emptyString("-").fixed(false).build()
        );
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public Map<String, Object> posCashDiscountTypeActivitySum(PosCashDetailsQuery params) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        return statisticsMapper.posCashDiscountTypeActivitySum(params);
    }

    @Override
    public void posCashDiscountTypeActivityExport(PosCashDetailsQuery params, HttpServletResponse response) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }

        List<Map<String, Object>> resultVOList = statisticsMapper.posCashDiscountTypeActivityExport(params);
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("discountName").label("优惠活动名称")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("num").label("订单量")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("discountAmount").label("折扣金额(元)")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(180).emptyString("-").fixed(false).build()
        );

        Map<String, Object> sumMap = new HashMap<>();
        sumMap.put("discountName", "合计");
        sumMap.put("num", resultVOList.stream()
                .filter(map -> map.get("num") != null && map.get("num") != "-")
                .mapToInt(map -> Integer.parseInt(map.get("num").toString()))
                .sum());
        sumMap.put("discountAmount", BigDecimal.valueOf(resultVOList.stream()
                .filter(map -> map.get("discountAmount") != null && !StringUtils.equals(map.get("discountAmount").toString(), "-"))
                .mapToDouble(map -> Double.parseDouble(map.get("discountAmount").toString()))
                .sum()).setScale(2, RoundingMode.HALF_UP));

        resultVOList.add(sumMap);
        export(response, resultVOList, columnVOList, "优惠活动统计");
    }

    @Override
    public Map<String, Object> thailDetails(PageParams<ThailDetailsQuery> params) {
        //订单中销售额:总计(thail_amount), 分页值需要分页外面的即可
        //      id, 名称(商品/服务等), 单位, 分类(饮料), 数量, 类型(台桌,服务等), 汇总的销售额
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("id").label("编号")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("名称")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("measuringUnit").label("单位")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type").label("类型")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("classify").label("分类")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("数量")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("销售额")
                        .width(180).emptyString("-").fixed(false).build()
        );

        customService.storeTime(params.getModel());
        ThailDetailsQuery model = params.getModel();
        initOrgIdList(model);
        IPage<PosCash> iPage = posCashService.page(params.buildPage(PosCash.class), Wraps.<PosCash>lbQ().select(PosCash::getThailId).eq(PosCash::getDeleteFlag, 0)
                .in(PosCash::getBillType, Arrays.asList("0", "3", "4"))
                .eq(PosCash::getBillState, PosCashBillStateEnum.COMPLETE)
                .between(PosCash::getCompleteTime, model.getStartDate(), model.getEndDate()).isNotNull(PosCash::getThailId).groupBy(PosCash::getThailId));

        // 如果为空,则无需进行后续步骤
        if (CollUtil.isEmpty(iPage.getRecords())) {
            Map<String, Object> objectMap = BeanUtil.beanToMap(iPage);
            objectMap.put("columnList", columnVOList);
            return objectMap;
        }

        List<PosCash> records = iPage.getRecords();

        List<Map<String, Object>> resultVOS = getThailDetailResult(model, records);

        Map<String, Object> objectMap = BeanUtil.beanToMap(iPage);
        objectMap.put("records", resultVOS);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public void thailDetailsExport(ThailDetailsQuery params, HttpServletResponse response) {
        customService.storeTime(params);
        initOrgIdList(params);
        List<PosCash> records = posCashService.list(Wraps.<PosCash>lbQ().select(PosCash::getThailId).eq(PosCash::getDeleteFlag, 0)
                .in(PosCash::getBillType, Arrays.asList("0", "3", "4"))
                .eq(PosCash::getBillState, PosCashBillStateEnum.COMPLETE)
                .between(PosCash::getCompleteTime, params.getStartDate(), params.getEndDate())
                .isNotNull(PosCash::getThailId).groupBy(PosCash::getThailId));

        List<Map<String, Object>> resultVOS = getThailDetailResult(params, records);

        // 导出- 两个 sheet
        // 1.获取ExcelWriter对象
        ExcelWriter writerThail = ExcelUtil.getBigWriter();
        writerThail.renameSheet("套餐统计");
        // 2.写出表头
        writerThail.addHeaderAlias("id", "套餐编号");
        writerThail.addHeaderAlias("name", "套餐名称");
        writerThail.addHeaderAlias("type", "类型");
        writerThail.addHeaderAlias("classify", "分类");
        writerThail.addHeaderAlias("num", "数量");
        writerThail.addHeaderAlias("amount", "销售额");

        // ...
        // 3.定义表头单元格样式(可选)
        StyleSet style = writerThail.getStyleSet();
        CellStyle headCellStyle = style.getHeadCellStyle();
        // 自动换行
        headCellStyle.setWrapText(true);
        // 水平居中
        headCellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 上下居中
        headCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置表头字体大小
        Font headFont = writerThail.createFont();
        headFont.setFontName("宋体");
        // 设置字体大小为15磅
        headFont.setFontHeightInPoints((short) 15);
        headCellStyle.setFont(headFont);
        // 4.定义内容单元格样式(可选)
        CellStyle cellStyle = style.getCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        // 设置字体大小
        Font font = writerThail.createFont();
        font.setFontName("宋体");
        cellStyle.setFont(font);
        // 5.其他设置(可选)
        // 只写出设置别名的属性
        writerThail.setOnlyAlias(true);
        // 冻结行
        writerThail.setFreezePane(1);
        // 6.写入数据 设置列宽
        writerThail.write(resultVOS);
        writerThail.autoSizeColumnAll();

        // 第二个sheet
        writerThail.setSheet("套餐详情");
        writerThail.addHeaderAlias("detailId", "编号");
        writerThail.addHeaderAlias("thailName", "套餐名称");
        writerThail.addHeaderAlias("detailName", "名称");
        writerThail.addHeaderAlias("detailType", "类型");
        writerThail.addHeaderAlias("detailClassify", "分类");
        writerThail.addHeaderAlias("detailNum", "数量");
        writerThail.addHeaderAlias("measuringUnit", "单位");
        writerThail.addHeaderAlias("detailAmount", "销售额");
        List<Map<String, Object>> detailList = new ArrayList<>();
        for (Map<String, Object> resultVO : resultVOS) {
            List<Map> maps = JSON.parseArray(JSONObject.toJSONString(resultVO.get("children")), Map.class);
            for (Map map : maps) {
                Map<String, Object> detail = new HashMap<>();
                detail.put("detailId", map.get("id"));
                detail.put("thailName", resultVO.get("name"));
                detail.put("detailName", map.get("name"));
                detail.put("detailType", map.get("type"));
                detail.put("detailClassify", map.get("classify"));
                detail.put("measuringUnit", map.get("measuringUnit"));
                detail.put("detailNum", map.get("num"));
                //detail.put("detailAmount", map.get("amount"));
                detail.put("detailAmount", "-");
                detailList.add(detail);
            }

        }
        writerThail.write(detailList);
        writerThail.autoSizeColumnAll();

        try (ServletOutputStream outputStream = response.getOutputStream()) {
            // 8.导出
            response.setCharacterEncoding(CharsetUtil.UTF_8);
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode("套餐报表", StandardCharsets.UTF_8.toString()) + ".xlsx");
            response.setContentType("application/vnd.ms-excel;" + CharsetUtil.UTF_8);
            writerThail.flush(outputStream);
            writerThail.close();
        } catch (Exception e) {
            log.warn("批量导出出错：{}", e.getMessage());
        }
    }

    @Override
    public Map<String, Object> memberCardDetails(PageParams<MemberCardPageQuery> params) {
        // 领取时间, 用户,卡名称,可类型, 可用范围, 可用日期, 购卡金额, 剩余, 有效期, 状态, 操作(变更记录)
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("createdTime").label("领取时间")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("memberIdEcho").label("用户名称")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("会员卡名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("typeEcho").label("卡类型")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("equity").label("可用范围")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("weeks").label("可用日期")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("price").label("购卡金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("remainingDesc").label("卡券剩余")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("validityDate").label("有效期至")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("statusEcho").label("状态")
                        .width(180).emptyString("-").fixed(false).build()
        );
        R<Page<MemberCardResultVO>> pageR = memberCardApi.pageList(params);
        Page<MemberCardResultVO> resultVOPage = pageR.getData();

        // 如果为空,则无需进行后续步骤
        if (CollUtil.isEmpty(resultVOPage.getRecords())) {
            Map<String, Object> objectMap = BeanUtil.beanToMap(resultVOPage);
            objectMap.put("columnList", columnVOList);
            return objectMap;
        }

        List<Map<String, Object>> resultVOS = getResultVOS(resultVOPage);

        Map<String, Object> objectMap = BeanUtil.beanToMap(resultVOPage);
        objectMap.put("records", resultVOS);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @NotNull
    private List<Map<String, Object>> getResultVOS(Page<MemberCardResultVO> resultVOPage) {
        List<Map<String, Object>> resultVOS = new ArrayList<>();

        // 查询所有字典
        List<DefDict> baseDictList = defDictService.list(Wraps.<DefDict>lbQ().eq(SuperEntity::getDeleteFlag, 0).eq(DefDict::getParentKey, EchoDictType.Base.WEEK));

        List<MemberCardResultVO> records = resultVOPage.getRecords();
        for (MemberCardResultVO record : records) {
            Map<String, Object> map = BeanPlusUtil.beanToMap(record);
            map.put("memberIdEcho", record.getEchoMap().get("memberId"));
            map.put("typeEcho", record.getEchoMap().get("type"));
            map.put("weeks", baseDictList.stream().filter(s -> record.getWeeks().contains(s.getKey())).map(DefDict::getName).collect(Collectors.joining(",")));
            map.put("statusEcho", record.getEchoMap().get("status"));
            StringBuffer sb = new StringBuffer();
            // 可用范围
            for (MemberCardEquitySaveVO memberCardEquitySaveVO : record.getEquityList()) {
                sb.append(memberCardEquitySaveVO.getName()).append(":").append(EquityBizTypeEnum.get(memberCardEquitySaveVO.getBizType()).getDesc()).append("可用");
                // 制定可用
                List<MemberCardEquityApplySaveVO> applyList = memberCardEquitySaveVO.getApplyList();
                if (CollUtil.isNotEmpty(applyList)) {
                    sb.append(",");
                    for (int i = 0; i < applyList.size(); i++) {
                        sb.append(applyList.get(i).getName());
                        if (i < applyList.size() - 1) {
                            sb.append("、");
                        }
                    }
//                    for (MemberCardEquityApplySaveVO memberCardEquityApplySaveVO : applyList) {
//                        sb.append(memberCardEquityApplySaveVO.getName()).append("、");
//                    }
                    sb.append("可用");
                }

                // 指定不可用
                List<MemberCardEquityExcludeSaveVO> excludeList = memberCardEquitySaveVO.getExcludeList();
                if (CollUtil.isNotEmpty(excludeList)) {
                    sb.append(",");
                    for (int i = 0; i < excludeList.size(); i++) {
                        sb.append(excludeList.get(i).getName());
                        if (i < excludeList.size() - 1) {
                            sb.append("、");
                        }
                    }

//                    for (MemberCardEquityExcludeSaveVO cardEquityExcludeSaveVO : excludeList) {
//                        sb.append(cardEquityExcludeSaveVO.getName()).append("、");
//                    }
                    sb.append("不可用");
                }
                sb.append(";");
            }
            if (ObjectUtil.isNotNull(sb)) {
                map.put("equity", sb.toString());
            } else {
                map.put("equity", "-");
            }

            resultVOS.add(map);
        }
        return resultVOS;
    }

    @Override
    public void memberCardDetailsExport(MemberCardPageQuery params, HttpServletResponse response) {
        PageParams<MemberCardPageQuery> pageParams = new PageParams<>();
        pageParams.setModel(params);
        pageParams.setSize(99999999L);
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("createdTime").label("领取时间")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("memberIdEcho").label("用户名称")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("会员卡名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("typeEcho").label("卡类型")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("equity").label("可用范围")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("weeks").label("可用日期")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("price").label("购卡金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("remainingDesc").label("卡券剩余")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("validityDate").label("有效期至")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("statusEcho").label("状态")
                        .width(180).emptyString("-").fixed(false).build()
        );

        R<Page<MemberCardResultVO>> pageR = memberCardApi.pageList(pageParams);
        Page<MemberCardResultVO> resultVOPage = pageR.getData();

        // 如果为空,则无需进行后续步骤
        if (CollUtil.isEmpty(resultVOPage.getRecords())) {
            Map<String, Object> objectMap = BeanUtil.beanToMap(resultVOPage);
            objectMap.put("columnList", columnVOList);
            export(response, new ArrayList<>(), columnVOList, "会员卡详情");
            return;
        }

        List<Map<String, Object>> resultVOS = getResultVOS(resultVOPage);

        export(response, resultVOS, columnVOList, "会员卡详情");
    }

    @Override
    public Map<String, Object> productStats(PageParams<ProductStatsQuery> params) {
        ProductStatsQuery model = params.getModel();
        initOrgIdList(model);
        if (Objects.nonNull(model) && StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            customService.storeTime(model);
        }
        params.setSort(null);
        params.setOrder(null);
        IPage<Map<String, Object>> pageList = statisticsMapper.productStats(params.buildPage(Map.class), model);
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("createdTime").label("时间")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("cateName").label("分类")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("productName").label("名称")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("outNum").label("销售出库")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("procurementOutNum").label("采购退货")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("inNum").label("采购入库")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("pdOut").label("盘点出库")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("pdIn").label("盘点入库")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(180).emptyString("-").fixed(false).build()
        );
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public Map<String, Object> productStatsSum(ProductStatsQuery params) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }
        return statisticsMapper.productStatsSum(params);
    }

    @Override
    public void productStatsExport(ProductStatsQuery params, HttpServletResponse response) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            customService.storeTime(params);
        }

        List<Map<String, Object>> resultVOList = statisticsMapper.productStatsExport(params);
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("createdTime").label("时间")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("cateName").label("分类")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("productName").label("名称")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("outNum").label("销售出库")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("procurementOutNum").label("采购退货")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("inNum").label("采购入库")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("pdOut").label("盘点出库")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("pdIn").label("盘点入库")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(180).emptyString("-").fixed(false).build()
        );

        Map<String, Object> sumMap = statisticsMapper.productStatsSum(params);
        if (CollUtil.isEmpty(sumMap)) {
            sumMap = MapUtil.newHashMap();
        }
        resultVOList.add(sumMap);
        export(response, resultVOList, columnVOList, "商品出入库统计");
    }

    @NotNull
    private List<Map<String, Object>> getThailDetailResult(ThailDetailsQuery model, List<PosCash> records) {
        // 查询所有的订单
        List<PosCash> posCashList = posCashService.list(Wraps.<PosCash>lbQ().eq(PosCash::getDeleteFlag, 0)
                .in(PosCash::getBillType, Arrays.asList("0", "3", "4"))
                .eq(PosCash::getBillState, PosCashBillStateEnum.COMPLETE)
                .between(PosCash::getCompleteTime, model.getStartDate(), model.getEndDate()).isNotNull(PosCash::getThailId));
        Map<Long, List<PosCash>> posCashMap = posCashList.stream()
                .collect(Collectors.groupingBy(PosCash::getThailId, LinkedHashMap::new, Collectors.toList()));


        // 查询所有的套餐id
        List<PosCashThail> posCashThailList = posCashThailService.list(Wraps.<PosCashThail>lbQ().eq(PosCashThail::getDeleteFlag, 0)
                .in(PosCashThail::getCashId, posCashList.stream().map(PosCash::getId).collect(Collectors.toList())));

        List<Long> thailIds = posCashThailList.stream().map(PosCashThail::getId).collect(Collectors.toList());
        Map<Long, List<PosCashThail>> posCashThailMap = posCashThailList.stream()
                .collect(Collectors.groupingBy(PosCashThail::getThailId, LinkedHashMap::new, Collectors.toList()));

        // 台桌
        List<PosCashTable> posCashTableList = posCashTableService.list(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getDeleteFlag, 0).in(PosCashTable::getCashId,
                posCashList.stream().map(PosCash::getId).collect(Collectors.toList())).in(PosCashTable::getCashThailId, thailIds));
        // 根据套餐分组
        Map<Long, List<PosCashTable>> posCashTableMap = posCashTableList.stream()
                .collect(Collectors.groupingBy(PosCashTable::getCashThailId, LinkedHashMap::new, Collectors.toList()));
        List<BaseTableInfo> baseTableInfoList = baseTableInfoService.list(Wraps.<BaseTableInfo>lbQ().in(SuperEntity::getId, posCashTableList.stream().map(PosCashTable::getTableId).collect(Collectors.toList())));
        List<BaseTableInfoResultVO> baseTableInfoResultVOS = BeanPlusUtil.toBeanList(baseTableInfoList, BaseTableInfoResultVO.class);
        echoService.action(baseTableInfoResultVOS);
        Map<Long, BaseTableInfoResultVO> baseTableInfoMap = baseTableInfoResultVOS.stream().collect(Collectors.toMap(BaseTableInfoResultVO::getId, Function.identity()));

        // 服务
        List<PosCashService> posCashServiceList = posCashServiceService.list(Wraps.<PosCashService>lbQ().eq(PosCashService::getDeleteFlag, 0)
                .in(PosCashService::getCashId, posCashList.stream().map(PosCash::getId).collect(Collectors.toList())).in(PosCashService::getCashThailId, thailIds));
        // 根据套餐分组
        Map<Long, List<PosCashService>> posCashServiceMap = posCashServiceList.stream()
                .collect(Collectors.groupingBy(PosCashService::getCashThailId, LinkedHashMap::new, Collectors.toList()));
        List<BaseService> baseServiceList = baseServiceService.list(Wraps.<BaseService>lbQ()
                .in(SuperEntity::getId, posCashServiceList.stream().map(PosCashService::getServiceId).collect(Collectors.toList())));
        List<BaseServiceResultVO> baseServiceResultVOS = BeanPlusUtil.toBeanList(baseServiceList, BaseServiceResultVO.class);
        echoService.action(baseServiceResultVOS);
        Map<Long, BaseServiceResultVO> baseServiceMap = baseServiceResultVOS.stream().collect(Collectors.toMap(BaseServiceResultVO::getId, Function.identity()));


        // 商品
        List<PosCashProduct> posCashProductList = posCashProductService.list(Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getDeleteFlag, 0)
                .in(PosCashProduct::getCashId, posCashList.stream().map(PosCash::getId).collect(Collectors.toList()))
                .in(PosCashProduct::getCashThailId, thailIds));
        // 根据套餐分组
        Map<Long, List<PosCashProduct>> posCashProductMap = posCashProductList.stream()
                .collect(Collectors.groupingBy(PosCashProduct::getCashThailId, LinkedHashMap::new, Collectors.toList()));
        List<BaseProduct> baseProductList = baseProductsService.list(Wraps.<BaseProduct>lbQ()
                .eq(BaseProduct::getDeleteFlag, 0)
                .in(BaseProduct::getId, posCashProductList.stream().map(PosCashProduct::getProductId).collect(Collectors.toList())));
        List<BaseProductResultVO> baseProductResultVOS = BeanPlusUtil.toBeanList(baseProductList, BaseProductResultVO.class);
        echoService.action(baseProductResultVOS);
        Map<Long, BaseProductResultVO> baseProductMap = baseProductResultVOS.stream().collect(Collectors.toMap(BaseProductResultVO::getId, Function.identity()));


        // 获取所有套餐
        List<BaseThail> baseThailList = baseThailService.list(Wraps.<BaseThail>lbQ().in(BaseThail::getId, records.stream().map(PosCash::getThailId).collect(Collectors.toList())));

        List<BaseThailResultVO> baseThailResultVOList = BeanPlusUtil.toBeanList(baseThailList, BaseThailResultVO.class);
        echoService.action(baseThailResultVOList);

        List<Map<String, Object>> resultVOS = new ArrayList<>();

        // 最外层
        for (BaseThailResultVO baseThail : baseThailResultVOList) {
            Map<String, Object> map = new HashMap<>();
            map.put("id", baseThail.getId().toString());
            map.put("name", baseThail.getName());
            map.put("measuringUnit", "-");
            map.put("type", "套餐");
            map.put("classify", Objects.nonNull(baseThail.getEchoMap().get("categroyId")) ? baseThail.getEchoMap().get("categroyId") : "-");
            List<Long> collect = posCashMap.get(baseThail.getId()).stream().map(SuperEntity::getId).collect(Collectors.toList());
            map.put("num", (int) posCashThailList.stream().filter(c -> collect.contains(c.getCashId())).count());
            map.put("amount", posCashMap.get(baseThail.getId()).stream().anyMatch(s -> Objects.nonNull(s.getThailAmount())) ? posCashMap.get(baseThail.getId()).stream().map(PosCash::getThailAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add) : 0.00);

            resultVOS.add(map);
        }

        // 遍历之后,设置子集
        for (Map<String, Object> resultVO : resultVOS) {
            // 台桌 - 价格就是台桌的基础价格

            List<PosCashThail> cashThailList = posCashThailMap.get(Long.valueOf(resultVO.get("id").toString()));
            List<Map<String, Object>> childMap = new ArrayList<>();

            List<PosCashTable> posCashTables = new ArrayList<>();
            for (PosCashThail posCashThail : cashThailList) {
                if (CollUtil.isNotEmpty(posCashTableMap.get(posCashThail.getId()))) {
                    posCashTables.addAll(posCashTableMap.get(posCashThail.getId()));
                }
            }
            if (CollUtil.isNotEmpty(posCashTables)) {
                List<Map<String, Object>> tableMap = new ArrayList<>();
                for (PosCashTable table : posCashTables) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", table.getTableId().toString());
                    map.put("name", table.getTableName());
                    map.put("measuringUnit", "分钟");
                    map.put("type", "台桌");
                    map.put("classify", Objects.nonNull(baseTableInfoMap.get(table.getTableId()).getEchoMap().get("tableType")) ? baseTableInfoMap.get(table.getTableId()).getEchoMap().get("tableType") : "-");
                    // 打球多少分钟
                    map.put("num", cashThailList.stream().filter(s -> Objects.equals(s.getId(), table.getCashThailId())).mapToInt(PosCashThail::getDuration).sum());
                    map.put("amount", "-");
                    tableMap.add(map);
                }
                tableMap = getMaps(tableMap);
                childMap.addAll(tableMap);
            }

            // 服务
            List<PosCashService> posCashServices = new ArrayList<>();
            for (PosCashThail posCashThail : cashThailList) {
                if (CollUtil.isNotEmpty(posCashServiceMap.get(posCashThail.getId()))) {
                    posCashServices.addAll(posCashServiceMap.get(posCashThail.getId()));
                }
            }
            if (CollUtil.isNotEmpty(posCashServices)) {
                List<Map<String, Object>> serviceMap = new ArrayList<>();
                for (PosCashService service : posCashServices) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", service.getServiceId().toString());
                    map.put("name", Objects.nonNull(baseServiceMap.get(service.getServiceId()).getName()) ? baseServiceMap.get(service.getServiceId()).getName() : "-");
                    map.put("measuringUnit", "分钟");
                    map.put("type", "服务");
                    map.put("classify", Objects.nonNull(baseServiceMap.get(service.getServiceId()).getEchoMap().get("categoryId")) ? baseServiceMap.get(service.getServiceId()).getEchoMap().get("categoryId") : "-");
                    map.put("num", cashThailList.stream().filter(s -> Objects.equals(s.getId(), service.getCashThailId())).mapToInt(PosCashThail::getDuration).sum());
                    map.put("amount", "-");
                    serviceMap.add(map);
                }
                serviceMap = getMaps(serviceMap);
                childMap.addAll(serviceMap);
            }

            // 商品
            List<PosCashProduct> posCashProducts = new ArrayList<>();
            for (PosCashThail posCashThail : cashThailList) {
                if (CollUtil.isNotEmpty(posCashProductMap.get(posCashThail.getId()))) {
                    posCashProducts.addAll(posCashProductMap.get(posCashThail.getId()));
                }
            }
            if (CollUtil.isNotEmpty(posCashProducts)) {
                List<Map<String, Object>> productsMap = new ArrayList<>();
                for (PosCashProduct product : posCashProducts) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("id", product.getProductId().toString());
                    map.put("name", Objects.nonNull(product.getProductName()) ? product.getProductName() : "-");
                    map.put("measuringUnit", Objects.nonNull(baseProductMap.get(product.getProductId()).getEchoMap().get("measuringUnit")) ? baseProductMap.get(product.getProductId()).getEchoMap().get("measuringUnit") : "-");
                    map.put("type", "商品");
                    map.put("classify", Objects.nonNull(baseProductMap.get(product.getProductId()).getEchoMap().get("categoryId")) ? baseProductMap.get(product.getProductId()).getEchoMap().get("categoryId") : "-");
                    map.put("num", 1);
                    map.put("amount", "-");
                    productsMap.add(map);
                }
                productsMap = getMaps(productsMap);
                childMap.addAll(productsMap);
            }

            resultVO.put("children", childMap);

        }
        return resultVOS;
    }

    @NotNull
    private static List<Map<String, Object>> getMaps(List<Map<String, Object>> mapList) {
        Map<String, List<Map<String, Object>>> mapGroup = mapList.stream()
                .collect(Collectors.groupingBy(s -> s.get("id").toString(), LinkedHashMap::new, Collectors.toList()));
        // 去重
        mapList = mapList.stream()
                .collect(Collectors.toMap(map -> map.get("id").toString(), map -> map, (oldValue, newValue) -> newValue))
                .values()
                .stream()
                .collect(Collectors.toList());
        for (Map<String, Object> stringObjectMap : mapList) {
            stringObjectMap.put("num", mapGroup.get(stringObjectMap.get("id").toString()).stream().mapToInt(s -> (Integer) s.get("num")).sum());
        }
        return mapList;
    }


    public void initOrgIdList(OrgIdListQuery params) {
        if (Objects.isNull(params)) {
            params = new OrgIdListQuery();
        }
        if (CollUtil.isEmpty(params.getOrgIdList())) {
            params.setOrgIdList(Collections.singletonList(ContextUtil.getCurrentCompanyId()));
        }
    }

    /**
     * 为结果数据添加组名
     *
     * @param resultList 结果列表
     */
    private void addGroupNames(List<Map<String, Object>> resultList) {
        if (CollUtil.isEmpty(resultList)) {
            return;
        }

        // 提取所有的group_id
        List<Long> groupIds = resultList.stream()
                .map(map -> map.get("groupId"))
                .filter(Objects::nonNull)
                .map(id -> Long.valueOf(id.toString()))
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(groupIds)) {
            return;
        }

        // 查询所有组信息
        Map<Long, String> groupNameMap = baseGroupService.listByIds(groupIds).stream().collect(Collectors.toMap(BaseGroup::getId, BaseGroup::getName));

        // 为每个结果添加组名
        resultList.forEach(map -> {
            Object groupId = map.get("groupId");
            if (groupId != null) {
                String groupName = groupNameMap.get(Long.valueOf(groupId.toString()));
                map.put("groupName", groupName != null ? groupName : "-");
            } else {
                map.put("groupName", "-");
            }
        });
    }

    /**
     * 批量填充 orgName、serviceName、tableName、bdName
     */
    private void fillNames(List<Map<String, Object>> resultList) {
        if (CollUtil.isEmpty(resultList)) return;
        // orgName
        Set<Long> orgIds = resultList.stream().map(m -> m.get("orgId")).filter(Objects::nonNull).map(id -> Long.valueOf(id.toString())).collect(Collectors.toSet());
        Map<Long, String> orgNameMap = orgIds.isEmpty() ? Collections.emptyMap() :
                baseOrgService.listByIds(new ArrayList<>(orgIds)).stream().collect(Collectors.toMap(BaseOrg::getId, BaseOrg::getName));
        // serviceName
        Set<Long> serviceIds = resultList.stream().map(m -> m.get("serviceId")).filter(Objects::nonNull).map(id -> Long.valueOf(id.toString())).collect(Collectors.toSet());
        Map<Long, String> serviceNameMap = serviceIds.isEmpty() ? Collections.emptyMap() :
                baseServiceService.findList(Wraps.<BaseService>lbQ().in(SuperEntity::getId, serviceIds)).stream().collect(Collectors.toMap(BaseServiceResultVO::getId, BaseServiceResultVO::getName));
        // tableName
        Set<Long> tableIds = resultList.stream().map(m -> m.get("tableId")).filter(Objects::nonNull).map(id -> Long.valueOf(id.toString())).collect(Collectors.toSet());
        Map<Long, String> tableNameMap = tableIds.isEmpty() ? Collections.emptyMap() :
                baseTableInfoService.listByIds(new ArrayList<>(tableIds)).stream().collect(Collectors.toMap(BaseTableInfo::getId, BaseTableInfo::getName));
        // bdName（字典）
        Set<String> dictKeys = resultList.stream().filter(m -> m.get("clockType") != null && m.get("createdOrgId") != null)
                .map(m -> m.get("clockType") + "_" + m.get("createdOrgId")).collect(Collectors.toSet());
        List<String> clockTypeKeys = resultList.stream().map(m -> m.get("clockType")).filter(Objects::nonNull).map(id -> id.toString()).collect(Collectors.toList());
        Map<String, String> clockTypeMap = new HashMap<>();
        if (CollUtil.isNotEmpty(dictKeys)) {
            clockTypeMap = baseDictService.findList(Wraps.<BaseDict>lbQ().in(BaseDict::getKey, clockTypeKeys).eq(BaseDict::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                            .eq(BaseDict::getParentKey, EchoDictType.Base.CLOCK_TYPE))
                    .stream().collect(Collectors.toMap(BaseDictResultVO::getKey, BaseDictResultVO::getName));
        }
        // 填充
        Map<String, String> finalClockNameMap = clockTypeMap;
        resultList.forEach(map -> {
            Object orgId = map.get("orgId");
            map.put("orgName", orgId != null ? orgNameMap.getOrDefault(Long.valueOf(orgId.toString()), "-") : "-");
            Object serviceId = map.get("serviceId");
            map.put("serviceName", serviceId != null ? serviceNameMap.getOrDefault(Long.valueOf(serviceId.toString()), "-") : "-");
            Object tableId = map.get("tableId");
            map.put("tableName", tableId != null ? tableNameMap.getOrDefault(Long.valueOf(tableId.toString()), "-") : "-");
            Object clockType = map.get("clockType");
            map.put("bdName", Objects.nonNull(clockType) ? finalClockNameMap.getOrDefault(clockType.toString(), "-") : "-");
            map.put("clockTypeName", Objects.nonNull(clockType) ? finalClockNameMap.getOrDefault(clockType.toString(), "-") : "-");
        });
    }

    private static void export(HttpServletResponse response, List<Map<String, Object>> resultVOS, List<ColumnVO> columnVOList, String fileName) {
        // 1.获取ExcelWriter对象
        ExcelWriter writer = ExcelUtil.getBigWriter();
        // 2.写出表头
        for (ColumnVO columnVO : columnVOList) {
            // 自定义标题别名
            writer.addHeaderAlias(columnVO.getName(), columnVO.getLabel());
        }

        // ...
        // 3.定义表头单元格样式(可选)
        StyleSet style = writer.getStyleSet();
        CellStyle headCellStyle = style.getHeadCellStyle();
        // 自动换行
        headCellStyle.setWrapText(true);
        // 水平居中
        headCellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 上下居中
        headCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置表头字体大小
        Font headFont = writer.createFont();
        headFont.setFontName("宋体");
        // 设置字体大小为15磅
        headFont.setFontHeightInPoints((short) 15);
        headCellStyle.setFont(headFont);
        // 4.定义内容单元格样式(可选)
        CellStyle cellStyle = style.getCellStyle();
        cellStyle.setAlignment(HorizontalAlignment.LEFT);
        // 设置字体大小
        Font font = writer.createFont();
        font.setFontName("宋体");
        cellStyle.setFont(font);
        // 5.其他设置(可选)
        // 只写出设置别名的属性
        writer.setOnlyAlias(true);
        // 冻结行
        writer.setFreezePane(1);
        // 6.写入数据 设置列宽
        writer.write(resultVOS);
        writer.autoSizeColumnAll();
        // 7.开启筛选(可选)
        //CellRangeAddress filterRange = CellRangeAddress.valueOf("B1:D1");
        //writer.getSheet().setAutoFilter(filterRange);
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            // 8.导出
            response.setCharacterEncoding(CharsetUtil.UTF_8);
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()) + ".xlsx");
            response.setContentType("application/vnd.ms-excel;" + CharsetUtil.UTF_8);
            writer.flush(outputStream);
//            writer.close();
        } catch (Exception e) {
            log.warn("批量导出出错：{}", e.getMessage());
        } finally {
            writer.close();
        }
    }
}
