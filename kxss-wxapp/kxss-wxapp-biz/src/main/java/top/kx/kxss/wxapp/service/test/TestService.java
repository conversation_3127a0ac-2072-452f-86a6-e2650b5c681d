package top.kx.kxss.wxapp.service.test;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Service;
import top.kx.kxss.common.easyexcel.TableColsModel;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/9/24 15:27
 */
@Service
@Slf4j
public class TestService {

    //主要的构造tabCols和tableData，注意表头的字段，基本构造出了该格式所有的都能适配
    public void excelExport(List<TableColsModel> tabCols, List<JSONObject> dataList,
                            HttpServletResponse response) throws IOException {
        response.reset();
        response.setCharacterEncoding("UTF-8");
        //响应内容格式
        response.setContentType("application/vnd.ms-excel");
        //设置文件名
        String fileName = System.currentTimeMillis() + ".xlsx";

        try {
            //设置前端下载文件名
            String urlFileName = URLEncoder.encode(fileName + "详情" + System.currentTimeMillis(), "UTF-8");
            response.setHeader("content-disposition", "attachment; filename=" + urlFileName + ".xlsx");

            //  *代表所有请求都可访问
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET");
            response.setHeader("Access-Control-Allow-Headers", "Origin, No-Cache, X-Requested-With, " +
                    "If-Modified-Since, Pragma, Last-Modified, Cache-Control, Expires, Content-Type, X-E4M-With");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        ExcelWriter excelWriter = null;
        try {
            List<List<String>> excelHead = head(tabCols);
            // 第一页数据
            //5w数据一个sheet
            int batchSize = 50000;
            int totalSize = dataList.size();
            int sheetCount = (int) Math.ceil((double) totalSize / batchSize);

            excelWriter = EasyExcelFactory.write(response.getOutputStream()).build();

            for (int i = 0; i < sheetCount; i++) {
                int fromIndex = i * batchSize;
                int toIndex = Math.min((i + 1) * batchSize, totalSize);
                List<JSONObject> currentData = dataList.subList(fromIndex, toIndex);

                WriteSheet writeSheet = EasyExcel
                        .writerSheet("sheet" + (i + 1))
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .registerWriteHandler(horizontalCellStyleStrategy())
                        .head(excelHead)
                        .build();
                excelWriter.write(dataList(tabCols, currentData), writeSheet);
            }
        } catch (Throwable throwable) {
            log.error("导出excel异常", throwable);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (response.getOutputStream() != null) {
                response.getOutputStream().close();
            }
        }
    }

    private List<List<String>> head(List<TableColsModel> tabCols) {
        return tabCols.stream()
                .map(e -> Stream.of(e.getColumnParent(), e.getColumnName()).filter(Objects::nonNull).collect(Collectors.toList()))
                .collect(Collectors.toList());
    }

    private List<List<String>> dataList(List<TableColsModel> tabCols, List<JSONObject> mapList) {
        List<List<String>> list = new ArrayList<>();
        for (JSONObject row : mapList) {
            List<String> data = new ArrayList<>();
            for (TableColsModel tabCol : tabCols) {
                String filed = tabCol.getColumnField();
                Object objVal = row.get(filed);
                String val = Objects.toString(objVal, "");
                data.add(val);
            }
            list.add(data);
        }
        return list;
    }

    private HorizontalCellStyleStrategy horizontalCellStyleStrategy() {
        // 头的策略
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        // 背景设置为红色
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 14);
        headWriteCellStyle.setWriteFont(headWriteFont);
        // 内容的策略
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        // 这里需要指定 FillPatternType 为FillPatternType.SOLID_FOREGROUND 不然无法显示背景颜色.头默认了 FillPatternType所以可以不指定
        contentWriteCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        // 背景绿色
        contentWriteCellStyle.setFillForegroundColor(IndexedColors.WHITE1.getIndex());
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontHeightInPoints((short) 10);
        contentWriteFont.setColor(IndexedColors.GREY_80_PERCENT.getIndex());

        contentWriteCellStyle.setWriteFont(contentWriteFont);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);

        contentWriteCellStyle.setBorderTop(BorderStyle.THIN);
        contentWriteCellStyle.setBorderRight(BorderStyle.THIN);
        contentWriteCellStyle.setBorderBottom(BorderStyle.THIN);
        contentWriteCellStyle.setBorderLeft(BorderStyle.THIN);

        contentWriteCellStyle.setTopBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        contentWriteCellStyle.setRightBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        contentWriteCellStyle.setBottomBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        contentWriteCellStyle.setLeftBorderColor(IndexedColors.GREY_50_PERCENT.getIndex());
        // 这个策略是 头是头的样式 内容是内容的样式 其他的策略可以自己实现
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }
}
