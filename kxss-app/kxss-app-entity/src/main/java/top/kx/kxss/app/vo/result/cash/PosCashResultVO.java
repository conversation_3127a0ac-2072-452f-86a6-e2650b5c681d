package top.kx.kxss.app.vo.result.cash;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.app.vo.result.cash.product.PosCashProductResultVO;
import top.kx.kxss.app.vo.result.cash.service.PosCashServiceResultVO;
import top.kx.kxss.app.vo.result.cash.table.PosCashTableResultVO;
import top.kx.kxss.app.vo.result.recharge.DepositRuleResultVO;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.constant.EchoDictType;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * pos结算 含商品出库 服务 台费结算
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:04:53
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "PosCashResultVO", description = "pos结算 含商品出库 服务 台费结算")
public class PosCashResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 类型 0开台 1购物 2卡券 3充值
     */
    @ApiModelProperty(value = "类型 当前没用")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.CASH_TYPE)
    private String type;

    @ApiModelProperty(value = "台桌名称")
    private String tableName;
    /**
     * 单据号
     */
    @ApiModelProperty(value = "单据号")
    private String code;
    /**
     * 单据日期yyyy-mm-dd
     */
    @ApiModelProperty(value = "单据日期yyyy-mm-dd")
    private LocalDate billDate;
    /**
     * 单据状态 0待结算  1挂单 2完成
     */
    @ApiModelProperty(value = "单据状态   0正单  1挂单 2退单")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.BILL_STATE)
    private String billState;
    /**
     * 单据类型 0正单 1退单 2取消
     */
    @ApiModelProperty(value = "单据类型   0正单  1退单  2 取消")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.BILL_TYPE)
    private String billType;
    /**
     * 所属门店ID
     */
    @ApiModelProperty(value = "所属门店ID")
    @Echo(api = EchoApi.ORG_ID_CLASS)
    private Long orgId;
    /**
     * 员工id，用于记录和提成相关业务员信息
     */
    @ApiModelProperty(value = "员工id，用于记录和提成相关业务员信息")
    @Echo(api = EchoApi.EMPLOYEE_CONTAIN_REMOVE_CLASS)
    private Long employeeId;
    /**
     * 会员id  在customer表建立一个名为散客的记录，如果是散客即默认此id
     */
    @ApiModelProperty(value = "会员id  在customer表建立一个名为散客的记录，如果是散客即默认此id")
    //@Echo(api = EchoApi.MEMBER_CLASS)
    private Long memberId;

    @ApiModelProperty(value = "台桌ID")
    private Long tableId;
    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private BigDecimal amount;
    /**
     * 折扣金额
     */
    @ApiModelProperty(value = "折扣金额")
    private BigDecimal discountAmount;
    /**
     * 收款金额
     */
    @ApiModelProperty(value = "收款金额")
    private BigDecimal payment;
    /**
     * 已付款金额
     */
    @ApiModelProperty(value = "已付款金额")
    private BigDecimal paid;
    /**
     * 未付款金额
     */
    @ApiModelProperty(value = "未付款金额")
    private BigDecimal unpaid;
    /**
     * 抹零
     */
    @ApiModelProperty(value = "抹零")
    private BigDecimal roundAmount;
    /**
     * 收款的类型冗余字段，直接是收款类型名称，如：现金+支付宝
     */
    @ApiModelProperty(value = "收款的类型冗余字段，直接是收款类型名称，如：现金+支付宝")
    private String payName;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;


    @ApiModelProperty(value = "整单备注标签")
    private String orderTages;

    @ApiModelProperty(value = "整单备注")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.App.ORDER_TAGS)
    private String orderRemarks;

    /**
     * 挂单时间
     */
    @ApiModelProperty(value = "挂单时间")
    private LocalDateTime registrationTime;

    @ApiModelProperty(value = "挂单备注")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.App.REGISTRATION_TAGS)
    private String registrationRemarks;

    @ApiModelProperty(value = "挂单自定义备注")
    private String registrationCustomRemarks;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织")
    @Echo(api = EchoApi.ORG_ID_CLASS)
    private Long createdOrgId;
    /**
     * 来源单据（整单）id
     */
    @ApiModelProperty(value = "来源单据（整单）id")
    private Long sourceId;
    /**
     * 退单id
     */
    @ApiModelProperty(value = "退单id")
    private Long chargebackId;

    /**
     * 商品信息
     */
    @ApiModelProperty(value = "商品信息")
    private List<PosCashProductResultVO> productList;

    /**
     * 服务信息
     */
    @ApiModelProperty(value = "服务信息")
    private List<PosCashServiceResultVO> serviceList;
    /**
     * 台费信息
     */
    @ApiModelProperty(value = "台费信息")
    private List<PosCashTableResultVO> tableList;

    /**
     * 储值信息
     */
    @ApiModelProperty(value = "储值信息")
    private DepositRuleResultVO depositRuleVO;

    /**
     * 储值规则id
     */
    @ApiModelProperty(value = "储值规则id")
    private Long depositRuleId;
    /**
     * 订单来源 1 pos 2 自助 3 扫码
     */
    @ApiModelProperty(value = "订单来源 1 pos 2 自助 3 扫码")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.ORDER_SOURCE)
    private String orderSource;


    @ApiModelProperty(value = "操作员工")
    @Echo(api = EchoApi.EMPLOYEE_CONTAIN_REMOVE_CLASS)
    private Long createdEmp;

    @ApiModelProperty(value = "完成员工")
    @Echo(api = EchoApi.EMPLOYEE_CONTAIN_REMOVE_CLASS)
    private Long completeEmp;

    @ApiModelProperty(value = "结账时间")
    private LocalDateTime completeTime;


    @ApiModelProperty(value = "退款时间")
    private LocalDateTime refundTime;

    @ApiModelProperty(value = "台桌金额")
    private BigDecimal tableAmount;

    @ApiModelProperty(value = "商品金额")
    private BigDecimal productAmount;

    @ApiModelProperty(value = "服务金额")
    private BigDecimal serviceAmount;

    @ApiModelProperty(value = "店内套餐金额")
    private BigDecimal thailAmount;

    @ApiModelProperty(value = "团购套餐金额")
    private BigDecimal groupBuyAmount;

    @ApiModelProperty(value = "充电金额")
    private BigDecimal powerAmount;

    @ApiModelProperty(value = "团购ID")
    private Long groupBuyId;

    @ApiModelProperty(value = "订单退款金额")
    private BigDecimal refundAmount;
    @ApiModelProperty(value = "订单实收金额")
    private BigDecimal paidIn;
    /**
     * 台桌类型
     */
    @ApiModelProperty(value = "台桌类型")
    private String tableType;
    /**
     * 台桌区域
     */
    @ApiModelProperty(value = "台桌区域")
    private Long tableArea;

    /**
     * 开台总时长
     */
    @ApiModelProperty(value = "开台总时长")
    private Integer tableDuration;

    /**
     * 服务总时长
     */
    @ApiModelProperty(value = "服务总时长")
    private Integer serviceDuration;

    /**
     * 套餐总时长
     */
    @ApiModelProperty(value = "套餐总时长")
    private Integer thailDuration;

    /**
     * 充电总时长
     */
    @ApiModelProperty(value = "充电总时长")
    private Integer powerDuration;

    @ApiModelProperty(value = "赠送金额")
    private BigDecimal giftAmount;

}
