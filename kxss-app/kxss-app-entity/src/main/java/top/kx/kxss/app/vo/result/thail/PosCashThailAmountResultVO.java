package top.kx.kxss.app.vo.result.thail;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 表单查询方法返回值VO
 * 订单套餐信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 19:17:23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@Builder
@ApiModel(value = "PosCashThailAmountResultVO", description = "套餐金额和团购套餐")
public class PosCashThailAmountResultVO implements Serializable{

    /**
    * 结算ID
    */
    @ApiModelProperty(value = "结算ID")
    private Long cashId;

    @ApiModelProperty(value = "店内套餐")
    private BigDecimal thailAmount;

    @ApiModelProperty(value = "团购套餐")
    private BigDecimal groupBuyAmount;

    @ApiModelProperty(value = "店内套餐-原价")
    private BigDecimal thailPrice;

    @ApiModelProperty(value = "团购套餐-原价")
    private BigDecimal groupBuyPrice;

    /**
     * 团购订单数量
     */
    @ApiModelProperty(value = "包含团购的订单数")
    private BigDecimal groupBuyNum;

    /**
     * 包含店内套餐数量
     */
    @ApiModelProperty(value = "包含店内套餐的订单数")
    private BigDecimal thailNum;





}
