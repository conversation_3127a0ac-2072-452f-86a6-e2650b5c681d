package top.kx.kxss.app.manager.cash.service;

import top.kx.basic.base.manager.SuperManager;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.app.vo.result.ProfitResultVO;

import java.util.List;

/**
 * <p>
 * 通用业务接口
 * 收银-服务子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:44:58
 * @create [2023-04-19 14:44:58] [dou] [代码生成器生成]
 */
public interface PosCashServiceManager extends SuperManager<PosCashService> {

    ProfitResultVO findProfit(List<Long> posCashIdList);
}


