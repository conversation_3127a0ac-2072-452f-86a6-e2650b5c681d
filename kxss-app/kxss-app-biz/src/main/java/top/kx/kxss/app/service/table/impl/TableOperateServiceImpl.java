package top.kx.kxss.app.service.table.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.exception.BizException;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.equity.PosCashEquity;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.entity.cash.table.PosCashTableCash;
import top.kx.kxss.app.manager.cash.PosCashManager;
import top.kx.kxss.app.manager.cash.equity.PosCashEquityManager;
import top.kx.kxss.app.manager.cash.payment.PosCashPaymentManager;
import top.kx.kxss.app.manager.cash.product.PosCashProductManager;
import top.kx.kxss.app.manager.cash.service.PosCashServiceManager;
import top.kx.kxss.app.manager.cash.table.PosCashTableManager;
import top.kx.kxss.app.service.table.TableOperateService;
import top.kx.kxss.app.utils.DateUtils;
import top.kx.kxss.app.vo.result.table.charing.AppTableCharingResultVo;
import top.kx.kxss.app.vo.save.cash.PosCashSaveVO;
import top.kx.kxss.app.vo.save.cash.table.PosCashExchangeTableSaveVO;
import top.kx.kxss.app.vo.save.cash.table.PosCashMergeTableSaveVO;
import top.kx.kxss.base.entity.service.BaseService;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.entity.tableCharging.BaseTableCharging;
import top.kx.kxss.base.manager.system.BaseServiceManager;
import top.kx.kxss.base.manager.table.BaseTableInfoManager;
import top.kx.kxss.base.manager.tableCharging.BaseTableChargingManager;
import top.kx.kxss.base.vo.query.table.BaseTableInfoPageQuery;
import top.kx.kxss.base.vo.result.table.BaseTableInfoResultVO;
import top.kx.kxss.base.vo.save.table.BaseTableInfoSaveVO;
import top.kx.kxss.base.vo.update.table.BaseTableInfoUpdateVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillTypeEnum;
import top.kx.kxss.model.enumeration.base.TableStatus;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * <p>
 * 业务实现类
 * 台桌操作业务
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-18 23:00:01
 * @create [2023-04-18 23:00:01] [zhou]
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DS(DsConstant.BASE_TENANT)
public class TableOperateServiceImpl extends SuperServiceImpl<BaseTableInfoManager, Long, BaseTableInfo, BaseTableInfoSaveVO, BaseTableInfoUpdateVO, BaseTableInfoPageQuery, BaseTableInfoResultVO> implements TableOperateService {

    @Autowired
    private PosCashPaymentManager cashPaymentManager;
    @Autowired
    private PosCashProductManager cashProductManager;
    @Autowired
    private PosCashServiceManager cashServiceManager;
    @Autowired
    private PosCashTableManager cashTableManager;
    @Autowired
    private PosCashManager cashManager;
    @Autowired
    private PosCashEquityManager cashEquityManager;
    @Autowired
    private BaseServiceManager baseServiceManager;
    @Autowired
    private CalculateBizServiceImpl calculateBizService;
    @Autowired
    private BaseTableChargingManager chargingManager;

    private volatile boolean suc = false;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> mergeTable(PosCashMergeTableSaveVO posCashMergeTableSaveVO) {
        Map<String, Object> result = new HashMap<>();
        List<Long> fromTableIds = posCashMergeTableSaveVO.getFromTableIds();
        BaseTableInfo table = superManager.getById(fromTableIds.get(0));
        if (CollectionUtils.isEmpty(fromTableIds)) {
            throw new BizException("并桌失败！");
        }

        // 源台桌信息
        List<PosCash> posCashes = cashManager.list(Wraps.<PosCash>lbQ().eq(PosCash::getBillState, "9").in(PosCash::getTableId, fromTableIds));
        if (CollectionUtils.isEmpty(posCashes)) {
            throw new BizException("并桌失败！");
        }

        // 目标台桌信息
        PosCash toPosCash = cashManager.getOne(Wraps.<PosCash>lbQ().eq(PosCash::getBillState, "9").eq(PosCash::getTableId, posCashMergeTableSaveVO.getToTableId()));
        if (toPosCash == null) {
            throw new BizException("并桌失败！");
        }

        // 目标台桌结算ID
        Long toPosCashTableId = toPosCash.getId();
        Long toTableId = toPosCash.getTableId();

        // 将源台桌service、table记录合并到目标台桌
        posCashes.stream().forEach(item -> {
            // 结算ID
            Long cashId = item.getId();

            // service、table等记录合并
            List<PosCashPayment> cashPayments = cashPaymentManager.list(Wraps.<PosCashPayment>lbQ().eq(PosCashPayment::getCashId, cashId));
            if (CollectionUtils.isNotEmpty(cashPayments)) {
                List<PosCashPayment> addCashPayments = new ArrayList<>();
                cashPayments.stream().forEach(nItem -> {
                    nItem.setId(null);
                    nItem.setCashId(toPosCash.getId());
                    nItem.setRemarks("并桌：结算记录合并" + nItem.getCashId() + " -> " + toPosCashTableId);
                    nItem.setSn(ContextUtil.getSn());
                    addCashPayments.add(nItem);
                });
                suc = cashPaymentManager.saveBatch(addCashPayments);
                if (!suc) {
                    throw new BizException("换桌失败！");
                }
            }

            List<PosCashProduct> cashProducts = cashProductManager.list(Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getCashId, cashId));
            if (CollectionUtils.isNotEmpty(cashProducts)) {
                List<PosCashProduct> addCashProducts = new ArrayList<>();
                cashProducts.stream().forEach(nItem -> {
                    nItem.setId(null);
                    nItem.setCashId(toPosCash.getId());
                    nItem.setRemarks("并桌：结算记录合并" + nItem.getCashId() + " -> " + toPosCashTableId);
                    nItem.setSn(ContextUtil.getSn());
                    addCashProducts.add(nItem);
                });
                suc = cashProductManager.saveBatch(addCashProducts);
                if (!suc) {
                    throw new BizException("换桌失败！");
                }
            }

            List<PosCashService> cashServices = cashServiceManager.list(Wraps.<PosCashService>lbQ().eq(PosCashService::getCashId, cashId));
            if (CollectionUtils.isNotEmpty(cashServices)) {
                List<PosCashService> addCashServices = new ArrayList<>();
                List<Long> serviceIds = cashServices.stream().map(PosCashService::getServiceId).collect(Collectors.toList());
                List<BaseService> baseServices = baseServiceManager.list(Wraps.<BaseService>lbQ().in(BaseService::getId, serviceIds));
                Map<Long, BaseService> serviceMap = baseServices.stream().collect(Collectors.toMap(BaseService::getId, e -> e));
                for (PosCashService posCashService : cashServices) {
                    posCashService.setId(null);
                    posCashService.setCashId(toPosCash.getId());
                    posCashService.setRemarks("并桌：结算记录合并" + posCashService.getCashId() + " -> " + toPosCashTableId);

                    // 停止计时并计算价格
                    BaseService baseService = serviceMap.get(posCashService.getServiceId());
                    posCashService.setEndTime(LocalDateTime.now());
                    Long startMinutes = DateUtils.calDifMinutes(posCashService.getStartTime(), posCashService.getEndTime());
                    posCashService.setDuration(Integer.valueOf(startMinutes + ""));
                    // 计算金额
                    BigDecimal orginPrice = new BigDecimal(0);
                    BigDecimal amount = new BigDecimal(0);
                    if (0 != startMinutes) {
                        orginPrice = calculateBizService.overTimeCash(posCashService.getPrice(), startMinutes, baseService.getBillingCycle(), baseService.getTimeoutPeriod());
                        amount = orginPrice;
                        if (!"".equals(posCashService.getType())) {
                            if ("6".equals(posCashService.getType())) {
                                long dicountTime = posCashService.getDiscount().setScale(0, RoundingMode.DOWN).longValue();
                                amount = calculateBizService.orginPriceCountAmountByType6(posCashService.getPrice(), posCashService.getDuration().longValue(), dicountTime, baseService.getBillingCycle(), baseService.getTimeoutPeriod());
                            } else {
                                amount = calculateBizService.orginPriceCountAmount(orginPrice, posCashService.getType(), posCashService.getDiscount());
                            }
                        }
                    }

                    posCashService.setOrginPrice(orginPrice);
                    posCashService.setAmount(amount);
                    posCashService.setSn(ContextUtil.getSn());

                    addCashServices.add(posCashService);
                }
                suc = cashServiceManager.saveBatch(addCashServices);
                if (!suc) {
                    throw new BizException("换桌失败！");
                }
            }

            List<PosCashTable> cashTables = cashTableManager.list(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, cashId));
            if (CollectionUtils.isNotEmpty(cashTables)) {
                List<PosCashTable> addCashTables = new ArrayList<>();
                for (PosCashTable posCashTable : cashTables) {
                    posCashTable.setId(null);
                    posCashTable.setCashId(toPosCashTableId);
                    posCashTable.setTableId(toTableId);
                    posCashTable.setRemarks("并桌：台桌信息合并" + posCashTable.getTableId() + " -> " + toTableId);

                    // 停止并计算金额
                    posCashTable.setEndTime(LocalDateTime.now());
                    long startMinutes = DateUtils.calDifMinutes(posCashTable.getStartTime(), posCashTable.getEndTime());
                    posCashTable.setDuration(Integer.valueOf(startMinutes + ""));

                    BigDecimal orginPrice = new BigDecimal(0);
                    BigDecimal amount = new BigDecimal(0);
                    if (0 != startMinutes) {
                        BaseTableCharging charging = chargingManager.getOne(Wraps.<BaseTableCharging>lbQ().eq(BaseTableCharging::getTableType, table.getTableType()));
                        PosCashTableCash posCashTableCash = BeanUtil.copyProperties(posCashTable, PosCashTableCash.class);
                        AppTableCharingResultVo chargingResultVO = BeanUtil.copyProperties(charging, AppTableCharingResultVo.class);
                        calculateBizService.calAmount(posCashTableCash, null, chargingResultVO, null != posCashes.get(0).getMemberId());
                        orginPrice = posCashTableCash.getAmount();
                        amount = orginPrice;
                        if (!"".equals(posCashTableCash.getType())) {
                            if ("6".equals(posCashTableCash.getType())) {
                                long dicountTime = posCashTableCash.getDiscount().setScale(0, RoundingMode.DOWN).longValue();
                                amount = calculateBizService.orginPriceCountAmountByType6(posCashTableCash.getPrice(), posCashTableCash.getDuration().longValue(), dicountTime, charging.getPeriod(), charging.getOvertime());
                            } else {
                                amount = calculateBizService.orginPriceCountAmount(orginPrice, posCashTableCash.getType(), posCashTableCash.getDiscount());
                            }
                        }
                    }

                    posCashTable.setOrginPrice(orginPrice);
                    posCashTable.setAmount(amount);
                    posCashTable.setSn(ContextUtil.getSn());

                    addCashTables.add(posCashTable);
                }
                suc = cashTableManager.saveBatch(addCashTables);
                if (!suc) {
                    throw new BizException("并桌失败！");
                }
            }
        });

        // 更新源台桌信息 TODO 暂时将单据状态更新为5
        PosCash updateEntity = new PosCash();
        updateEntity.setBillState("5");
        suc = cashManager.update(updateEntity, Wraps.<PosCash>lbQ().in(PosCash::getId, posCashes.stream().map(PosCash::getId).collect(Collectors.toList())));
        if (!suc) {
            throw new BizException("并桌失败！");
        }

        // 源台桌释放为空闲
        BaseTableInfo tableInfo = new BaseTableInfo();
        tableInfo.setTableStatus(TableStatus.UNUSED.getCode());
        tableInfo.setLightStatus("0");
        suc = superManager.update(tableInfo, Wraps.<BaseTableInfo>lbQ().in(BaseTableInfo::getId, fromTableIds));
        if (!suc) {
            throw new BizException("并桌失败！");
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> exchangeTable(PosCashExchangeTableSaveVO posCashExchangeTableSaveVO) {
        Long fromTableId = posCashExchangeTableSaveVO.getFromTableId();
        Long toTableId = posCashExchangeTableSaveVO.getToTableId();

        // 修改结算表台桌信息
        PosCash fromPosCash = cashManager.getOne(Wraps.<PosCash>lbQ().eq(PosCash::getBillState, "9").eq(PosCash::getTableId, fromTableId));
        fromPosCash.setTableId(toTableId);
        fromPosCash.setRemarks("换桌：台桌信息变更" + fromTableId + " -> " + toTableId);
        suc = cashManager.updateAllById(fromPosCash);
        if (!suc) {
            throw new BizException("换桌失败！");
        }
        // 源台桌信息
        List<PosCashTable> sourcePosCashTables = cashTableManager.list(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, fromPosCash.getId()));
        if (CollectionUtils.isNotEmpty(sourcePosCashTables)) {
            sourcePosCashTables.stream().forEach(item -> item.setCashId(fromPosCash.getId()).setTableId(toTableId).setRemarks("换桌：台桌信息变更" + fromTableId + " -> " + toTableId));
        }

        // 保存
        suc = cashTableManager.saveOrUpdateBatch(sourcePosCashTables);
        if (!suc) {
            throw new BizException("换桌失败！");
        }

        // 将源台桌释放
        BaseTableInfo tableInfo = new BaseTableInfo();
        tableInfo.setTableStatus(TableStatus.UNUSED.getCode());
        tableInfo.setLightStatus("0");
        suc = superManager.update(tableInfo, Wraps.<BaseTableInfo>lbQ().eq(BaseTableInfo::getId, fromTableId));
        if (!suc) {
            throw new BizException("换桌失败！");
        }

        // 将目标台桌状态改为已开始
        BaseTableInfo toTableInfo = new BaseTableInfo();
        toTableInfo.setTableStatus(TableStatus.USING.getCode());
        suc = superManager.update(toTableInfo, Wraps.<BaseTableInfo>lbQ().eq(BaseTableInfo::getId, toTableId));
        if (!suc) {
            throw new BizException("换桌失败！");
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> orderCancel(PosCashSaveVO posCashSaveVO) {

        // 台桌ID
        Long tableId = posCashSaveVO.getTableId();

        // 修改结算表台桌信息
        PosCash posCash = cashManager.getOne(Wraps.<PosCash>lbQ().eq(PosCash::getBillState, PosCashBillStateEnum.NO_SETTLED.getCode())
                .eq(PosCash::getBillType, PosCashBillTypeEnum.REGULAR_SINGLE.getCode()).eq(PosCash::getTableId, tableId));

        if (posCash == null) {
            throw new BizException("整单取消失败！");
        }

        posCash.setBillType(PosCashBillTypeEnum.CANCELLATION.getCode());
        posCash.setRemarks(posCashSaveVO.getRemarks());
        posCash.setUpdatedTime(LocalDateTime.now());
        // 保存
        suc = cashManager.updateById(posCash);
        if (!suc) {
            throw new BizException("整单取消失败！");
        }

        // 将源台桌释放
        BaseTableInfo tableInfo = new BaseTableInfo();
        tableInfo.setTableStatus(TableStatus.UNUSED.getCode());
        tableInfo.setLightStatus("0");
        suc = superManager.update(tableInfo, Wraps.<BaseTableInfo>lbQ().eq(BaseTableInfo::getId, tableId));
        if (!suc) {
            throw new BizException("整单取消失败！");
        }
        return null;
    }

    @Override
    public Map<String, Object> singleRemark(Map<String, Object> param) {
        // ID
        String id = (String) param.get("id");
        // 1-商品，2-服务，3-台费
        Integer type = (Integer) param.get("type");
        // 备注
        String remarks = (String) param.get("remarks");

        if (Integer.valueOf(1).equals(type)) {
            PosCashProduct posCashProduct = cashProductManager.getById(id);
            JSONObject remarksObj = convert2JSON(posCashProduct.getRemarks());
            // 单品备注
            remarksObj.put("productRemarks", remarks);
            posCashProduct.setRemarks(remarksObj.toJSONString());
            suc = cashProductManager.update(posCashProduct, Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getId, id));
        } else if (Integer.valueOf(2).equals(type)) {
            PosCashService posCashService = cashServiceManager.getById(id);
            JSONObject remarksObj = convert2JSON(posCashService.getRemarks());
            // 单品备注
            remarksObj.put("productRemarks", remarks);
            posCashService.setRemarks(remarksObj.toJSONString());
            suc = cashServiceManager.update(posCashService, Wraps.<PosCashService>lbQ().eq(PosCashService::getId, id));
        } else if (Integer.valueOf(3).equals(type)) {
            PosCashTable posCashTable = cashTableManager.getById(id);
            JSONObject remarksObj = convert2JSON(posCashTable.getRemarks());
            // 单品备注
            remarksObj.put("productRemarks", remarks);
            posCashTable.setRemarks(remarksObj.toJSONString());
            suc = cashTableManager.update(posCashTable, Wraps.<PosCashTable>lbQ().eq(PosCashTable::getId, id));
        }
        if (!suc) {
            throw new BizException("单品备注失败！");
        }

        return null;
    }

    @Override
    public Map<String, Object> singleSale(Map<String, Object> param) {
        // ID
        String id = (String) param.get("id");
        // 1-商品，2-服务，3-台费
        Integer categoryType = (Integer) param.get("categoryType");
        // 备注
        String remark = (String) param.get("remarks");
        // 打折/减免值
        BigDecimal value = param.get("value") instanceof String ? new BigDecimal((String) param.get("value")) : (BigDecimal) param.get("value");
        // 优惠类型: 1-打折 2-减免 3-赠送
        String type = String.valueOf(param.get("type"));
        // 计算价格
        if ("1".equals(type)) {
            if (Integer.valueOf(1).equals(categoryType)) {
                PosCashProduct posCashProduct = cashProductManager.getById(id);

                PosCashProduct updatePosCashProduct = new PosCashProduct();

                // 打折优惠后价格
                BigDecimal amountAfter = posCashProduct.getAmount().multiply(value.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.UP);

                // 更改金额记录
                JSONObject productDiscountRemakrs = convert2JSON(posCashProduct.getRemarks());
                productDiscountRemakrs.put("productDiscountRemakrs", StringUtils.isEmpty(productDiscountRemakrs.getString("productDiscountRemakrs")) ? "商品打折优惠，打折优惠前:" + posCashProduct.getAmount() + ",打折优惠后:" + amountAfter : productDiscountRemakrs.getString("productDiscountRemakrs") + "商品打折优惠，打折优惠前:" + posCashProduct.getAmount() + ",打折优惠后:" + amountAfter);
                productDiscountRemakrs.put("remarks", remark);
                updatePosCashProduct.setRemarks(productDiscountRemakrs.toJSONString());
                updatePosCashProduct.setType(type);
                // 更改原金额
                updatePosCashProduct.setAmount(amountAfter);
                updatePosCashProduct.setDiscount(value);
                suc = cashProductManager.update(updatePosCashProduct, Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getId, id));
            } else if (Integer.valueOf(2).equals(categoryType)) {
                PosCashService posCashService = cashServiceManager.getById(id);

                PosCashService updatePosCashService = new PosCashService();
                // 打折优惠后价格
                BigDecimal amountAfter = posCashService.getAmount().multiply(value.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.UP);

                // 更改金额记录
                JSONObject serviceDiscountRemakrs = convert2JSON(posCashService.getRemarks());
                serviceDiscountRemakrs.put("serviceDiscountRemakrs", StringUtils.isEmpty(serviceDiscountRemakrs.getString("serviceDiscountRemakrs")) ? "服务打折优惠，打折优惠前:" + posCashService.getAmount() + ",打折优惠后:" + amountAfter : serviceDiscountRemakrs.getString("serviceDiscountRemakrs") + ";服务打折优惠，打折优惠前:" + posCashService.getAmount() + ",打折优惠后:" + amountAfter);
                serviceDiscountRemakrs.put("remarks", remark);
                updatePosCashService.setType(type);
                updatePosCashService.setDiscount(value);
                // 更改原金额
                updatePosCashService.setAmount(amountAfter);
                updatePosCashService.setRemarks(serviceDiscountRemakrs.toJSONString());
                suc = cashServiceManager.update(updatePosCashService, Wraps.<PosCashService>lbQ().eq(PosCashService::getId, id));
            } else if (Integer.valueOf(3).equals(categoryType)) {
                PosCashTable posCashTable = cashTableManager.getById(id);

                PosCashTable updatePosCashTable = new PosCashTable();
                // 打折优惠后价格
                BigDecimal amountAfter = posCashTable.getAmount().multiply(value.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.UP);

                // 更改金额记录
                JSONObject tableDiscountRemakrs = convert2JSON(posCashTable.getRemarks());
                tableDiscountRemakrs.put("tableDiscountRemakrs", StringUtils.isEmpty(tableDiscountRemakrs.getString("tableDiscountRemakrs")) ? "台费打折优惠，打折优惠前:" + posCashTable.getAmount() + ",打折优惠后:" + amountAfter : tableDiscountRemakrs.getString("tableDiscountRemakrs") + ";台费打折优惠，打折优惠前:" + posCashTable.getAmount() + ",打折优惠后:" + amountAfter);
                tableDiscountRemakrs.put("remarks", remark);
                updatePosCashTable.setRemarks(tableDiscountRemakrs.toJSONString());
                updatePosCashTable.setType(type);
                // 更改原金额
                updatePosCashTable.setAmount(amountAfter);
                updatePosCashTable.setDiscount(value);
                suc = cashTableManager.update(updatePosCashTable, Wraps.<PosCashTable>lbQ().eq(PosCashTable::getId, id));
            }
        } else if ("2".equals(type)) {
            if (Integer.valueOf(1).equals(categoryType)) {
                PosCashProduct posCashProduct = cashProductManager.getById(id);

                PosCashProduct updatePosCashProduct = new PosCashProduct();
                // 减免优惠后价格
                BigDecimal amountAfter = posCashProduct.getAmount().subtract(value).setScale(2, RoundingMode.UP);

                // 更改金额记录
                JSONObject productSaleRemakrs = convert2JSON(posCashProduct.getRemarks());
                productSaleRemakrs.put("productDerateRemakrs", StringUtils.isEmpty(productSaleRemakrs.getString("productDerateRemakrs")) ? "商品减免优惠，减免优惠前:" + posCashProduct.getAmount() + ",减免优惠后:" + amountAfter : productSaleRemakrs.getString("productDerateRemakrs") + ";商品减免优惠，减免优惠前:" + posCashProduct.getAmount() + ",减免优惠后:" + amountAfter);
                productSaleRemakrs.put("remarks", remark);
                updatePosCashProduct.setType(type);
                updatePosCashProduct.setDiscount(value);
                // 更改原金额
                updatePosCashProduct.setAmount(amountAfter);
                updatePosCashProduct.setRemarks(productSaleRemakrs.toJSONString());
                suc = cashProductManager.update(updatePosCashProduct, Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getId, id));
            } else if (Integer.valueOf(2).equals(categoryType)) {
                PosCashService posCashService = cashServiceManager.getById(id);

                PosCashService updatePosCashService = new PosCashService();
                // 减免优惠后价格
                BigDecimal amountAfter = posCashService.getAmount().subtract(value).setScale(2, RoundingMode.UP);
                // 更改金额记录
                JSONObject serviceSaleRemakrs = convert2JSON(posCashService.getRemarks());
                serviceSaleRemakrs.put("serviceDerateRemakrs", StringUtils.isEmpty(serviceSaleRemakrs.getString("serviceDerateRemakrs")) ? "服务减免优惠，减免优惠前:" + posCashService.getAmount() + ",减免优惠后:" + amountAfter : serviceSaleRemakrs.getString("serviceDerateRemakrs") + ";服务减免优惠，减免优惠前:" + posCashService.getAmount() + ",减免优惠后:" + amountAfter);
                serviceSaleRemakrs.put("remarks", remark);
                updatePosCashService.setType(type);
                updatePosCashService.setDiscount(value);
                // 更改原金额
                updatePosCashService.setAmount(amountAfter);
                updatePosCashService.setRemarks(serviceSaleRemakrs.toJSONString());
                suc = cashServiceManager.update(updatePosCashService, Wraps.<PosCashService>lbQ().eq(PosCashService::getId, id));
            } else if (Integer.valueOf(3).equals(categoryType)) {
                PosCashTable posCashTable = cashTableManager.getById(id);

                PosCashTable updatePosCashTable = new PosCashTable();
                // 减免优惠后价格
                BigDecimal amountAfter = posCashTable.getAmount().subtract(value).setScale(2, RoundingMode.UP);

                // 更给金额记录
                JSONObject tableSaleRemakrs = convert2JSON(posCashTable.getRemarks());
                tableSaleRemakrs.put("tableDerateRemakrs", StringUtils.isEmpty(tableSaleRemakrs.getString("tableDerateRemakrs")) ? "台费减免优惠，减免优惠前:" + posCashTable.getAmount() + ",减免优惠后:" + amountAfter : tableSaleRemakrs.getString("tableDerateRemakrs") + ";台费减免优惠，减免优惠前:" + posCashTable.getAmount() + ",减免优惠后:" + amountAfter);
                tableSaleRemakrs.put("remarks", remark);
                updatePosCashTable.setRemarks(tableSaleRemakrs.toJSONString());
                updatePosCashTable.setType(type);
                updatePosCashTable.setDiscount(value);
                // 更改原金额
                updatePosCashTable.setAmount(amountAfter);
                updatePosCashTable.setRemarks(tableSaleRemakrs.toJSONString());
                suc = cashTableManager.update(updatePosCashTable, Wraps.<PosCashTable>lbQ().eq(PosCashTable::getId, id));
            }
        } else if ("3".equals(type)) {
            if (Integer.valueOf(1).equals(categoryType)) {
                PosCashProduct posCashProduct = cashProductManager.getById(id);

                // 赠送后,剩余商品数量
                int prodCount = posCashProduct.getNum() - value.intValue();

                if (prodCount > 0) {
                    PosCashProduct updatePosCashProduct = new PosCashProduct();
                    // 计算赠送后价格,赠送后价格,先判断之前是否有优惠操作,根据商品剩余数量计算价格
                    BigDecimal amountAfter = posCashProduct.getPrice().multiply(new BigDecimal(prodCount)).setScale(2, RoundingMode.UP);
                    if ("1".equals(posCashProduct.getType())) {
                        amountAfter = posCashProduct.getPrice().multiply(new BigDecimal(prodCount)).multiply(posCashProduct.getDiscount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.UP);
                    } else if ("2".equals(posCashProduct.getType())) {
                        amountAfter = posCashProduct.getPrice().multiply(new BigDecimal(prodCount)).subtract(posCashProduct.getDiscount()).setScale(2, RoundingMode.UP);
                        if (amountAfter.compareTo(BigDecimal.ZERO) == -1) {
                            throw new BizException("单品优惠失败！");
                        }
                    }
                    // 更改原金额
                    updatePosCashProduct.setAmount(amountAfter);
                    // 更给改金额记录
                    JSONObject productSaleRemakrs = convert2JSON(posCashProduct.getRemarks());
                    productSaleRemakrs.put("productSaleRemakrs", StringUtils.isEmpty(productSaleRemakrs.getString("productSaleRemakrs")) ? "商品赠送优惠，赠送优惠前:" + posCashProduct.getAmount() + ",赠送优惠后:" + amountAfter : productSaleRemakrs.getString("productSaleRemakrs") + ";商品赠送优惠，赠送优惠前:" + posCashProduct.getAmount() + ",赠送优惠后:" + amountAfter);
                    productSaleRemakrs.put("remarks", remark);
                    updatePosCashProduct.setRemarks(productSaleRemakrs.toJSONString());
                    // 根据赠送商品数量进行拆单
                    updatePosCashProduct.setNum(prodCount);
                    suc = cashProductManager.update(updatePosCashProduct, Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getId, id));

                    // 赠送商品新增记录
                    posCashProduct.setId(null);
                    posCashProduct.setIsGift(true);
                    posCashProduct.setNum(value.intValue());
                    posCashProduct.setAmount(BigDecimal.ZERO);
                    productSaleRemakrs.put("remarks", remark);
                    JSONObject obj = new JSONObject();
                    obj.put("remarks", "商品赠送");
                    posCashProduct.setRemarks(obj.toJSONString());
                    posCashProduct.setType(type);
                    posCashProduct.setDiscount(value);
                    posCashProduct.setRemarks(obj.toJSONString());
                    posCashProduct.setSn(ContextUtil.getSn());
                    suc = cashProductManager.save(posCashProduct);
                } else if (posCashProduct.getNum().intValue() - value.intValue() == 0) {
                    PosCashProduct updatePosCashProduct = new PosCashProduct();
                    // 赠送优惠后价格
                    BigDecimal amountAfter = posCashProduct.getAmount().subtract(posCashProduct.getPrice().multiply(new BigDecimal(posCashProduct.getNum()))).setScale(2, RoundingMode.UP);
                    // 更改原金额
                    updatePosCashProduct.setAmount(BigDecimal.ZERO);
                    // 更给改金额记录
                    JSONObject productSaleRemakrs = convert2JSON(posCashProduct.getRemarks());
                    productSaleRemakrs.put("productSaleRemakrs", StringUtils.isEmpty(productSaleRemakrs.getString("productSaleRemakrs")) ? "商品赠送优惠，赠送优惠前:" + posCashProduct.getAmount() + ",赠送优惠后:" + amountAfter : productSaleRemakrs.getString("productSaleRemakrs") + ";商品赠送优惠，赠送优惠前:" + posCashProduct.getAmount() + ",赠送优惠后:" + amountAfter);
                    productSaleRemakrs.put("remarks", remark);
                    updatePosCashProduct.setRemarks(productSaleRemakrs.toJSONString());
                    updatePosCashProduct.setDiscount(value);
                    updatePosCashProduct.setIsGift(true);
                    suc = cashProductManager.update(updatePosCashProduct, Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getId, id));
                }
            }
        }

        if (!suc) {
            throw new BizException("单品优惠失败！");
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> completeOrder(Map<String, Object> param) {
        String msg = "";
        // ID
        String id = (String) param.get("cashId");
        // 备注
        String remark = (String) param.get("remarks");
        // 打折/减免值
        BigDecimal value = param.get("value") instanceof String ? new BigDecimal((String) param.get("value")) : (BigDecimal) param.get("value");
        // 优惠类型: 1-打折 2-减免 3-赠送
        String type = String.valueOf(param.get("type"));
        // 计算价格
        PosCash posCash = cashManager.getById(id);
        posCash.setDiscountType(type);
        posCash.setDiscount(value);
//        BigDecimal middle = null != posCash.getUnpaid() ? posCash.getUnpaid() : posCash.getPayment();
        BigDecimal middle = posCash.getAmount();
        if ("1".equals(type)) {
            msg = "手动打折失败！";

            // 打折后金额

            BigDecimal amountAfter = middle.multiply(value.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.UP);
//            if (null != posCash.getRoundAmount()) {
//                amountAfter = amountAfter.subtract(posCash.getRoundAmount());
//            }
            // 优惠金额
            BigDecimal yhAmount = posCash.getAmount().subtract(amountAfter);
//            BigDecimal discountAmount = posCash.getAmount().subtract(amountAfter);
            BigDecimal discountAmount = yhAmount.add(null == posCash.getRoundAmount() ? new BigDecimal(0) : posCash.getRoundAmount());

            posCash.setDiscountAmount(discountAmount);

            // 更给改金额记录
            JSONObject remakrsObj = convert2JSON(posCash.getRemarks());
            remakrsObj.put("discountRemakrs", StringUtils.isEmpty(remakrsObj.getString("discountRemakrs")) ? "手动打折优惠，打折优惠前:" + posCash.getAmount() + ",打折优惠后:" + amountAfter : remakrsObj.getString("discountRemakrs") + ";手动打折优惠，打折优惠前:" + posCash.getAmount() + ",打折优惠后:" + amountAfter);
            remakrsObj.put("remarks", remark);
            posCash.setPayment(posCash.getAmount().subtract(discountAmount));
            posCash.setUnpaid(posCash.getAmount().subtract(discountAmount));
            posCash.setRemarks(remakrsObj.toJSONString());
            suc = cashManager.saveOrUpdate(posCash);
        } else if ("2".equals(type)) {
            msg = "手动减免失败！";

            BigDecimal amountAfter = middle.subtract(value).setScale(2, RoundingMode.UP);
//            if (null != posCash.getRoundAmount()) {
//                amountAfter = amountAfter.subtract(posCash.getRoundAmount());
//            }
            posCash.setDiscountAmount(value.add(null == posCash.getRoundAmount() ? new BigDecimal(0) : posCash.getRoundAmount()));
            BigDecimal aa = posCash.getAmount().subtract(value.add(null == posCash.getRoundAmount() ? new BigDecimal(0) : posCash.getRoundAmount()));
            JSONObject remakrsObj = convert2JSON(posCash.getRemarks());
            remakrsObj.put("derateRemakrs", StringUtils.isEmpty(remakrsObj.getString("derateRemakrs")) ? "手动减免优惠，减免优惠前:" + posCash.getAmount() + ",减免优惠后:" + amountAfter : remakrsObj.getString("derateRemakrs") + ";手动减免优惠，减免优惠前:" + posCash.getAmount() + ",减免优惠后:" + amountAfter);
            remakrsObj.put("remarks", remark);
            posCash.setPayment(aa);
            posCash.setUnpaid(aa);
            posCash.setRemarks(remakrsObj.toJSONString());
            suc = cashManager.saveOrUpdate(posCash);
        } else if ("3".equals(type)) {
            msg = "免单失败！";
            JSONObject remakrsObj = convert2JSON(posCash.getRemarks());
            remakrsObj.put("derateRemakrs", StringUtils.isEmpty(remakrsObj.getString("derateRemakrs")) ? "免单" : remakrsObj.getString("derateRemakrs") + ";免单");
            remakrsObj.put("remarks", remark);
            posCash.setPayment(BigDecimal.ZERO);
            posCash.setUnpaid(BigDecimal.ZERO);
            posCash.setRemarks(remakrsObj.toJSONString());
            // TODO 更改结算表台桌状态
            posCash.setBillState("6");
            suc = cashManager.saveOrUpdate(posCash);

            if (suc) {
                // 将源台桌释放
                BaseTableInfo tableInfo = new BaseTableInfo();
                tableInfo.setTableStatus(TableStatus.UNUSED.getCode());
                tableInfo.setLightStatus("0");
                suc = superManager.update(tableInfo, Wraps.<BaseTableInfo>lbQ().eq(BaseTableInfo::getId, posCash.getTableId()));
            }
        }

        if (!suc) {
            throw new BizException(msg);
        }
        return null;
    }

    @Override
    public Map<String, Object> unGive(Map<String, Object> param) {
        // ID
        String id = (String) param.get("id");
        PosCashProduct posCashProduct = cashProductManager.getById(id);
        posCashProduct.setIsGift(false);
        posCashProduct.setNum(posCashProduct.getDiscount().intValue());
        posCashProduct.setAmount(posCashProduct.getDiscount().multiply(posCashProduct.getPrice()));
        posCashProduct.setDiscount(BigDecimal.ZERO);
        suc = cashProductManager.saveOrUpdate(posCashProduct);
        if (!suc) {
            throw new BizException("取消赠送失败！");
        }
        return null;
    }

    @Override
    public Map<String, Object> disSale(Map<String, Object> param) {
        // ID
        Long id = Long.valueOf((String) param.get("id"));

        // 1-商品，2-服务，3-台费
        Integer categoryType = (Integer) param.get("categoryType");

        if (Integer.valueOf(1).equals(categoryType)) {
            PosCashProduct posCashProduct = cashProductManager.getById(id);

            // 打折优惠后价格
            BigDecimal amountAfter = posCashProduct.getAmount();
            // 根据优惠类型,还原价格
            if ("1".equals(posCashProduct.getType())) {
                amountAfter = posCashProduct.getAmount().divide(posCashProduct.getDiscount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.UP);

                JSONObject productDiscountRemakrs = convert2JSON(posCashProduct.getRemarks());
                productDiscountRemakrs.put("productDiscountRemakrs", StringUtils.isEmpty(productDiscountRemakrs.getString("productDiscountRemakrs")) ? "取消打折优惠" : productDiscountRemakrs.getString("productDiscountRemakrs") + ";取消打折优惠");
                // 单品优惠取消,字段置空
                productDiscountRemakrs.remove("remarks");
                posCashProduct.setRemarks(productDiscountRemakrs.toJSONString());

            } else if ("2".equals(posCashProduct.getType())) {
                amountAfter = posCashProduct.getAmount().add(posCashProduct.getDiscount()).setScale(2, RoundingMode.UP);

                JSONObject productDiscountRemakrs = convert2JSON(posCashProduct.getRemarks());
                productDiscountRemakrs.put("productDerateRemakrs", StringUtils.isEmpty(productDiscountRemakrs.getString("productDerateRemakrs")) ? "取消减免优惠" : productDiscountRemakrs.getString("productDerateRemakrs") + ";取消减免优惠");
                // 单品优惠取消,字段置空
                productDiscountRemakrs.remove("remarks");
                posCashProduct.setRemarks(productDiscountRemakrs.toJSONString());
            }

            // 还原原金额，是否使用了卡权益
            // 查询库中是否有使用中的卡
            PosCashEquity posCashEquity = cashEquityManager.getOne(Wraps.<PosCashEquity>lbQ().eq(PosCashEquity::getCashId, posCashProduct.getCashId()).eq(PosCashEquity::getType, 1));
            if (posCashEquity == null) {
                posCashProduct.setDiscount(BigDecimal.ZERO);
                posCashProduct.setType("");
                posCashProduct.setAmount(amountAfter);
            }

            suc = cashProductManager.saveOrUpdate(posCashProduct);
        } else if (Integer.valueOf(2).equals(categoryType)) {
            PosCashService posCashService = cashServiceManager.getById(id);

            // 打折优惠后价格
            BigDecimal amountAfter = posCashService.getAmount();
            // 根据优惠类型,还原价格
            if ("1".equals(posCashService.getType())) {
                amountAfter = posCashService.getAmount().divide(posCashService.getDiscount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.UP);

                JSONObject productDiscountRemakrs = convert2JSON(posCashService.getRemarks());
                productDiscountRemakrs.put("productDiscountRemakrs", StringUtils.isEmpty(productDiscountRemakrs.getString("productDiscountRemakrs")) ? "取消打折优惠" : productDiscountRemakrs.getString("productDiscountRemakrs") + ";取消打折优惠");
                // 单品优惠取消,字段置空
                productDiscountRemakrs.remove("remarks");
                posCashService.setRemarks(productDiscountRemakrs.toJSONString());
            } else if ("2".equals(posCashService.getType())) {
                amountAfter = posCashService.getAmount().add(posCashService.getDiscount()).setScale(2, RoundingMode.UP);

                JSONObject productDiscountRemakrs = convert2JSON(posCashService.getRemarks());
                productDiscountRemakrs.put("productDerateRemakrs", StringUtils.isEmpty(productDiscountRemakrs.getString("productDerateRemakrs")) ? "取消减免优惠" : productDiscountRemakrs.getString("productDerateRemakrs") + ";取消减免优惠");
                // 单品优惠取消,字段置空
                productDiscountRemakrs.remove("remarks");
                posCashService.setRemarks(productDiscountRemakrs.toJSONString());
            }
            // 查询库中是否有使用中的卡
            // 还原原金额
            PosCashEquity posCashEquity = cashEquityManager.getOne(Wraps.<PosCashEquity>lbQ().eq(PosCashEquity::getCashId, posCashService.getCashId()).eq(PosCashEquity::getType, 1));
            if (posCashEquity == null) {
                posCashService.setDiscount(BigDecimal.ZERO);
                posCashService.setType("");
                posCashService.setAmount(amountAfter);
            }
            suc = cashServiceManager.saveOrUpdate(posCashService);
        } else if (Integer.valueOf(3).equals(categoryType)) {
            PosCashTable posCashTable = cashTableManager.getById(id);

            // 打折优惠后价格
            BigDecimal amountAfter = posCashTable.getAmount();
            // 根据优惠类型,还原价格
            if ("1".equals(posCashTable.getType())) {
                amountAfter = posCashTable.getAmount().divide(posCashTable.getDiscount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.UP);

                JSONObject productDiscountRemakrs = convert2JSON(posCashTable.getRemarks());
                productDiscountRemakrs.put("productDiscountRemakrs", StringUtils.isEmpty(productDiscountRemakrs.getString("productDiscountRemakrs")) ? "取消打折优惠" : productDiscountRemakrs.getString("productDiscountRemakrs") + ";取消打折优惠");
                // 单品优惠取消,字段置空
                productDiscountRemakrs.remove("remarks");
                posCashTable.setRemarks(productDiscountRemakrs.toJSONString());
            } else if ("2".equals(posCashTable.getType())) {
                amountAfter = posCashTable.getAmount().add(posCashTable.getDiscount()).setScale(2, RoundingMode.UP);

                JSONObject productDiscountRemakrs = convert2JSON(posCashTable.getRemarks());
                productDiscountRemakrs.put("productDerateRemakrs", StringUtils.isEmpty(productDiscountRemakrs.getString("productDerateRemakrs")) ? "取消减免优惠" : productDiscountRemakrs.getString("productDerateRemakrs") + ";取消减免优惠");
                // 单品优惠取消,字段置空
                productDiscountRemakrs.remove("remarks");
                posCashTable.setRemarks(productDiscountRemakrs.toJSONString());
            }

            // 还原原金额
            PosCashEquity posCashEquity = cashEquityManager.getOne(Wraps.<PosCashEquity>lbQ().eq(PosCashEquity::getCashId, posCashTable.getCashId()).eq(PosCashEquity::getType, 1));
            if (posCashEquity == null) {
                posCashTable.setDiscount(BigDecimal.ZERO);
                posCashTable.setType("");
                posCashTable.setAmount(amountAfter);
            }
            suc = cashTableManager.saveOrUpdate(posCashTable);
        }

        if (!suc) {
            throw new BizException("取消优惠失败！");
        }
        return null;
    }

    @Override
    public Map<String, Object> disCompleteOrder(Map<String, Object> param) {
        String msg = "";
        // ID
        String id = (String) param.get("cashId");
        // 计算价格
        PosCash posCash = cashManager.getById(id);
        BigDecimal amountBefore = BigDecimal.ZERO;
        JSONObject remakrsObj = convert2JSON(posCash.getRemarks());

        if ("1".equals(posCash.getDiscountType())) {
            // 打折前金额
//            amountBefore = posCash.getPayment().divide(posCash.getDiscount().divide(new BigDecimal(100), 2, RoundingMode.UP), 2, RoundingMode.UP).setScale(2, RoundingMode.UP);
            amountBefore = posCash.getPayment().add(posCash.getDiscountAmount()).setScale(2, RoundingMode.UP);
//            if (null != posCash.getRoundAmount()) {
//                amountBefore = amountBefore.subtract(posCash.getRoundAmount());
//            }
            // 更给改金额记录
            remakrsObj.put("discountRemakrs", StringUtils.isEmpty(remakrsObj.getString("discountRemakrs")) ? "取消整单打折操作" : remakrsObj.getString("discountRemakrs") + ";取消整单打折操作");

        } else if ("2".equals(posCash.getDiscountType())) {
            // 打折前金额
            amountBefore = posCash.getPayment().add(posCash.getDiscountAmount()).setScale(2, RoundingMode.UP);
//            if (null != posCash.getRoundAmount()) {
//                amountBefore = amountBefore.subtract(posCash.getRoundAmount());
//            }
            // 更给改金额记录
            remakrsObj.put("derateRemakrs", StringUtils.isEmpty(remakrsObj.getString("derateRemakrs")) ? "取消整单减免操作" : remakrsObj.getString("derateRemakrs") + ";取消整单减免操作");
        }
        posCash.setRemarks(remakrsObj.toJSONString());
//        posCash.setAmount(amountBefore);
        posCash.setPayment(amountBefore);
        posCash.setDiscountType("");
        posCash.setDiscount(BigDecimal.ZERO);
        posCash.setDiscountAmount(BigDecimal.ZERO);
        posCash.setUnpaid(posCash.getPayment().subtract(posCash.getDiscount()));
        suc = cashManager.saveOrUpdate(posCash);

        if (!suc) {
            throw new BizException("整单取消打折/减免失败!");
        }
        return null;
    }

    private JSONObject convert2JSON(String text) {
        try {
            return StringUtils.isNotEmpty(text) ? JSONObject.parseObject(text) : new JSONObject();
        } catch (Exception e) {
            log.error("备注数据格式错误,error:{}", e);
            return new JSONObject();
        }
    }
}
