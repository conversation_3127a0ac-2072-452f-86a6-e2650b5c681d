package top.kx.kxss.app.service.cash.payresult;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.app.entity.cash.payresult.PosCashPayResult;
import top.kx.kxss.app.vo.save.cash.payresult.PosCashPayResultSaveVO;
import top.kx.kxss.app.vo.update.cash.payresult.PosCashPayResultUpdateVO;
import top.kx.kxss.app.vo.result.cash.payresult.PosCashPayResultResultVO;
import top.kx.kxss.app.vo.query.cash.payresult.PosCashPayResultPageQuery;


/**
 * <p>
 * 业务接口
 * 结算单支付结果
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-19 17:25:46
 * @create [2023-05-19 17:25:46] [dou] [代码生成器生成]
 */
public interface PosCashPayResultService extends SuperService<Long, PosCashPayResult, PosCashPayResultSaveVO,
    PosCashPayResultUpdateVO, PosCashPayResultPageQuery, PosCashPayResultResultVO> {

}


