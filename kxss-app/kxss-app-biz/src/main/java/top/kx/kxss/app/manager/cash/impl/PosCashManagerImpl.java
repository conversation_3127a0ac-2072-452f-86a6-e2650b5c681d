package top.kx.kxss.app.manager.cash.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.manager.cash.PosCashManager;
import top.kx.kxss.app.mapper.cash.PosCashMapper;
import top.kx.kxss.app.vo.result.cash.PosCashResultVO;
import top.kx.kxss.pos.vo.service.ServiceTableResultVO;
import top.kx.kxss.wxapp.vo.query.statistics.ConsumeQuery;
import top.kx.kxss.wxapp.vo.result.statistics.StatisConsumeResultVO;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 通用业务实现类
 * pos结算 含商品出库 服务 台费结算
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:04:53
 * @create [2023-04-19 14:04:53] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashManagerImpl extends SuperManagerImpl<PosCashMapper, PosCash> implements PosCashManager {

    @Autowired
    private PosCashMapper posCashMapper;


    @Override
    public void deletePosCash(Long id) {
        posCashMapper.deletePosCash(id);
    }

    @Override
    public List<ServiceTableResultVO> selectPosCashByEmployeeId(List<Long> employeeIdList) {
        return posCashMapper.selectPosCashByEmployeeId(employeeIdList);
    }

    @Override
    public List<ServiceTableResultVO> selectCashByEmployeeId(QueryWrapper<PosCash> wrapper) {
        return posCashMapper.selectCashByEmployeeId(wrapper);
    }

    @Override
    public List<ServiceTableResultVO> selectPosCashByEmployeeIdAndServiceId(List<Long> employeeIdList) {
        return posCashMapper.selectPosCashByEmployeeIdAndServiceId(employeeIdList);
    }

    @Override
    public StatisConsumeResultVO consumeSum(ConsumeQuery params) {
        return posCashMapper.consumeSum(params);
    }

    @Override
    public PosCash queryOne(Long id) {
        return posCashMapper.queryOne(id);
    }

    @Override
    public List<PosCash> getAnyList(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return posCashMapper.getAnyList(ids);
    }

    @Override
    public IPage<PosCashResultVO> selectPageResultVO(IPage<PosCash> page, QueryWrap<PosCash> wrap) {
        return posCashMapper.selectPageResultVO(page, wrap);
    }

    @Override
    public List<PosCashResultVO> selectAllResultVO(QueryWrap<PosCash> wrap) {
        return posCashMapper.selectPageResultVO(wrap);
    }

    @Override
    public List<PosCash> selectPosCashWithConditions() {
        return posCashMapper.selectPosCashWithConditions(ContextUtil.getCurrentCompanyId());
    }
}


