package top.kx.kxss.app.service.cash.discount;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.app.entity.cash.discount.PosCashDiscountDetail;
import top.kx.kxss.app.vo.save.cash.discount.PosCashDiscountDetailSaveVO;
import top.kx.kxss.app.vo.update.cash.discount.PosCashDiscountDetailUpdateVO;
import top.kx.kxss.app.vo.result.cash.discount.PosCashDiscountDetailResultVO;
import top.kx.kxss.app.vo.query.cash.discount.PosCashDiscountDetailPageQuery;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 订单优惠明细
 * </p>
 *
 * <AUTHOR>
 * @date 2023-08-02 18:45:43
 * @create [2023-08-02 18:45:43] [dou] [代码生成器生成]
 */
public interface PosCashDiscountDetailService extends SuperService<Long, PosCashDiscountDetail, PosCashDiscountDetailSaveVO,
    PosCashDiscountDetailUpdateVO, PosCashDiscountDetailPageQuery, PosCashDiscountDetailResultVO> {

    void deleteByPosCashId(Long id);

    long count(LbQueryWrap<PosCashDiscountDetail> wrap);

    void save(PosCashDiscountDetail build);

    void updateBatchById(List<PosCashDiscountDetail> discountDetailList);

    void update(LbUpdateWrap<PosCashDiscountDetail> eq);
}


