package top.kx.kxss.app.statemachine.module;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.kxss.app.biz.PosCashBiz;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.statemachine.AbstractPosCashProcessor;
import top.kx.kxss.app.statemachine.annotation.PosCashProcessor;
import top.kx.kxss.common.constant.PosCashConstant;

/**
 * 订单取消
 *
 * <AUTHOR>
 */
@Component
@PosCashProcessor
public class PosCashCancelProcessor extends AbstractPosCashProcessor {

    @Autowired
    private PosCashBiz posCashBiz;

    public PosCashCancelProcessor() {
        super.setBillState(PosCashConstant.Event.CANCEL.getEventId());
    }

    @Override
    public boolean process(Long posCashId, Object... params) {
        PosCash posCash = posCashBiz.getById(posCashId);
        posCashBiz.doCancel(posCash);
        return true;
    }
}
