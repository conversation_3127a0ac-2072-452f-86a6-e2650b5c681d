package top.kx.kxss.app.manager.cash.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.app.entity.cash.PosCashOperateVerify;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.app.manager.cash.PosCashOperateVerifyManager;
import top.kx.kxss.app.mapper.cash.PosCashOperateVerifyMapper;

/**
 * <p>
 * 通用业务实现类
 * pos操作验证记录
 * </p>
 *
 * <AUTHOR>
 * @date 2023-12-21 12:18:16
 * @create [2023-12-21 12:18:16] [yh] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashOperateVerifyManagerImpl extends SuperManagerImpl<PosCashOperateVerifyMapper, PosCashOperateVerify> implements PosCashOperateVerifyManager {

}


