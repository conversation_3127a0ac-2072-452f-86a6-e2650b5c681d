package top.kx.kxss.app.mapper.cash.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.app.vo.result.ProfitResultVO;
import top.kx.kxss.app.vo.result.cash.service.PosCashServiceDetailResultVO;
import top.kx.kxss.wxapp.vo.result.cash.WxPosCashServiceTotalResultVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * 收银-服务子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:44:58
 * @create [2023-04-19 14:44:58] [dou] [代码生成器生成]
 */
@Repository
public interface PosCashServiceMapper extends SuperMapper<PosCashService> {

    List<Map<String, Object>> getServiceByCashIds(@Param(value = "cashIds") List<Long> cashIds);

    List<Map<String, Object>> queryServices(@Param(value = "id") Long cashId);

    IPage<PosCashServiceDetailResultVO> queryServiceList(IPage<PosCashService> page, @Param(Constants.WRAPPER) Wrapper<PosCashService> wrapper);

    WxPosCashServiceTotalResultVO queryTotal(@Param(Constants.WRAPPER) Wrapper<PosCashService> wrapper);

    ProfitResultVO findProfit(@Param(value = "posCashIdList") List<Long> posCashIdList);
}


