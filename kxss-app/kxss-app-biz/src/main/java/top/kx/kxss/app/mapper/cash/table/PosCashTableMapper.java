package top.kx.kxss.app.mapper.cash.table;

import org.apache.ibatis.annotations.Param;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import org.springframework.stereotype.Repository;
import top.kx.kxss.app.entity.cash.table.PosCashTableCash;
import top.kx.kxss.app.vo.result.ProfitResultVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * 台桌计时费用
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:44:58
 * @create [2023-04-19 14:44:58] [dou] [代码生成器生成]
 */
@Repository
public interface PosCashTableMapper extends SuperMapper<PosCashTable> {

    List<PosCashTableCash> queryStartTables();

    List<PosCashTableCash> queryStartTablesWithMember();

    ProfitResultVO findProfit(@Param(value = "posCashIdList") List<Long> posCashIdList, @Param(value = "thailIsNull") Boolean thailIsNull);
}


