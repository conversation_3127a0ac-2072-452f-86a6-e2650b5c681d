package top.kx.kxss.app.service.cash.payresult.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.app.service.cash.payresult.PosCashPayResultService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.app.manager.cash.payresult.PosCashPayResultManager;
import top.kx.kxss.app.entity.cash.payresult.PosCashPayResult;
import top.kx.kxss.app.vo.save.cash.payresult.PosCashPayResultSaveVO;
import top.kx.kxss.app.vo.update.cash.payresult.PosCashPayResultUpdateVO;
import top.kx.kxss.app.vo.result.cash.payresult.PosCashPayResultResultVO;
import top.kx.kxss.app.vo.query.cash.payresult.PosCashPayResultPageQuery;

/**
 * <p>
 * 业务实现类
 * 结算单支付结果
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-19 17:25:46
 * @create [2023-05-19 17:25:46] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class PosCashPayResultServiceImpl extends SuperServiceImpl<PosCashPayResultManager, Long, PosCashPayResult, PosCashPayResultSaveVO,
    PosCashPayResultUpdateVO, PosCashPayResultPageQuery, PosCashPayResultResultVO> implements PosCashPayResultService {


}


