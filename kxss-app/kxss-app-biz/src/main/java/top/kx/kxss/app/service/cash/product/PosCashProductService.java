package top.kx.kxss.app.service.cash.product;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.vo.query.cash.product.PosCashProductPageQuery;
import top.kx.kxss.app.vo.result.ProfitResultVO;
import top.kx.kxss.app.vo.result.cash.product.PosCashProductResultVO;
import top.kx.kxss.app.vo.save.cash.product.PosCashProductSaveVO;
import top.kx.kxss.app.vo.update.cash.product.PosCashProductUpdateVO;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 结算单商品子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:40:33
 * @create [2023-04-19 14:40:33] [dou] [代码生成器生成]
 */
public interface PosCashProductService extends SuperService<Long, PosCashProduct, PosCashProductSaveVO,
    PosCashProductUpdateVO, PosCashProductPageQuery, PosCashProductResultVO> {

    boolean updateBatchById(List<PosCashProduct> cashProducts);

    Boolean removeBatchByIds(List<PosCashProduct> cashProductList);

    Boolean checkProductIsUse(List<Long> longs);

    ProfitResultVO findProfit(List<Long> posCashIdList);

    List<String> productRanking(Long memberId);

    long count(LbQueryWrap<PosCashProduct> eq);

    boolean removeById(Long id);

    boolean removeById(PosCashProduct cashProduct);

    boolean update(LbUpdateWrap<PosCashProduct> eq);

    boolean updateById(PosCashProduct posCashProduct);

    List<PosCashProductResultVO> selectDiscount();

    /**
     * 查询列表
     * @param queryWrap 默认没有屏蔽 deleteFlag = 0
     * @return
     */
    List<PosCashProduct> queryList(LbQueryWrap<PosCashProduct> queryWrap);

    PosCashProduct getOne(LbQueryWrap<PosCashProduct> last);

    boolean save(PosCashProduct posCashProduct);

}


