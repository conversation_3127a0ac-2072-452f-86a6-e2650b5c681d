package top.kx.kxss.app.mapper.member;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * 会员信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-26
 * @create [2023-04-26 ] [zhou]
 */
@Repository
public interface MemberMapper {

    Map<String, Object> getMemberInfo(@Param(value = "id") Long memberId);

    Map<String, Object> getMemberDiscount(@Param(value = "memberId")Long memberId, @Param(value = "type")String type);


}
