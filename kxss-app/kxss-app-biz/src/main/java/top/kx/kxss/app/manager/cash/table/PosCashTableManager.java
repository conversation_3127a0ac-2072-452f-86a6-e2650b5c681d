package top.kx.kxss.app.manager.cash.table;

import top.kx.basic.base.manager.SuperManager;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.vo.result.ProfitResultVO;

import java.util.List;

/**
 * <p>
 * 通用业务接口
 * 台桌计时费用
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:44:58
 * @create [2023-04-19 14:44:58] [dou] [代码生成器生成]
 */
public interface PosCashTableManager extends SuperManager<PosCashTable> {

    /**
     * 验证台桌使用状态
     * @param posCash
     * @return
     */
    Boolean checkTableUsed(PosCash posCash);


    /**
     * 验证台桌停止
     * @param posCash
     * @return
     */
    Boolean checkTableStop(PosCash posCash);

    ProfitResultVO findProfit(List<Long> posCashIdList, Boolean thailIsNull);
}


