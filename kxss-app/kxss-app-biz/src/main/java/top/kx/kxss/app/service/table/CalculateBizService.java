package top.kx.kxss.app.service.table;

import org.springframework.stereotype.Service;
import top.kx.kxss.app.entity.cash.table.PosCashTableCash;
import top.kx.kxss.app.vo.result.table.charing.AppTableCharingResultVo;
import top.kx.kxss.app.vo.result.table.settings.AppTableChargingSettingResultVO;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.service.BaseService;

import java.math.BigDecimal;
import java.util.Map;

public interface CalculateBizService {

    /**
     * 商品单价计算
     *
     * @param product
     * @param memberId
     * @return
     */
    BigDecimal calProductAmount(BaseProduct product, Long memberId);

    /**
     * 服务单价计算
     *
     * @param product
     * @param memberId
     * @return
     */
    BigDecimal calServiceAmount(BaseService service, Long memberId);

    /**
     * 台费单价计算
     *
     * @param product
     * @param memberId
     * @return
     */
    BigDecimal calTableAmount(AppTableChargingSettingResultVO todaySet, Long memberId, Map<String, Object> memberDiscount);

    BigDecimal orginPriceCountAmount(BigDecimal orginPrice, String type, BigDecimal discount);

    BigDecimal orginPriceCountAmountByType6(BigDecimal price, Long duration, Long dicountTime, int period, int overtime);

    void calAmount(PosCashTableCash pct, AppTableChargingSettingResultVO preSettings, AppTableCharingResultVo charing, boolean isMember);

    BigDecimal overTimeCash(BigDecimal price, Long duration, int period, int overtime);
}
