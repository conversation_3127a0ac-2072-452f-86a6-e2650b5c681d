package top.kx.kxss.app.service.cash.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.exception.BizException;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.discount.PosCashDiscountDetail;
import top.kx.kxss.app.entity.cash.equity.AppEquity;
import top.kx.kxss.app.entity.cash.equity.PosCashEquity;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.entity.cash.refund.PosCashRefundPayment;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.entity.thail.PosCashThail;
import top.kx.kxss.app.manager.cash.PosCashManager;
import top.kx.kxss.app.manager.cash.discount.PosCashDiscountDetailManager;
import top.kx.kxss.app.manager.cash.equity.PosCashEquityManager;
import top.kx.kxss.app.manager.cash.payment.PosCashPaymentManager;
import top.kx.kxss.app.manager.cash.product.PosCashProductManager;
import top.kx.kxss.app.manager.cash.refund.PosCashRefundPaymentManager;
import top.kx.kxss.app.manager.cash.service.PosCashServiceManager;
import top.kx.kxss.app.manager.cash.table.PosCashTableManager;
import top.kx.kxss.app.manager.thail.PosCashThailManager;
import top.kx.kxss.app.mapper.cash.equity.PosCashEquityMapper;
import top.kx.kxss.app.mapper.member.AppMemberCouponMapper;
import top.kx.kxss.app.service.cash.PosCashServiceService;
import top.kx.kxss.app.service.table.TableOperateService;
import top.kx.kxss.app.service.table.impl.CalculateBizServiceImpl;
import top.kx.kxss.app.utils.DateUtils;
import top.kx.kxss.app.vo.coupon.CouponResultVo;
import top.kx.kxss.app.vo.coupon.CouponVo;
import top.kx.kxss.app.vo.pay.PayResponseVO;
import top.kx.kxss.app.vo.query.cash.PosCashPageQuery;
import top.kx.kxss.app.vo.query.cash.payment.PosCashPaymentPageQuery;
import top.kx.kxss.app.vo.result.cash.PosCashAmountVO;
import top.kx.kxss.app.vo.result.cash.PosCashResultVO;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPayVo;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO;
import top.kx.kxss.app.vo.result.cash.product.PosCashProductResultVO;
import top.kx.kxss.app.vo.result.cash.service.PosCashServiceResultVO;
import top.kx.kxss.app.vo.result.cash.table.PosCashTableResultVO;
import top.kx.kxss.app.vo.result.recharge.DepositCouponResultVO;
import top.kx.kxss.app.vo.result.recharge.DepositRuleResultVO;
import top.kx.kxss.app.vo.save.cash.PosCashDelOrderQuery;
import top.kx.kxss.app.vo.save.cash.PosCashSaveVO;
import top.kx.kxss.app.vo.save.cash.payment.PosCashPaymentSaveVO;
import top.kx.kxss.app.vo.update.cash.PosCashUpdateVO;
import top.kx.kxss.base.entity.biz.BaseBizLog;
import top.kx.kxss.base.entity.coupon.BaseCouponInfo;
import top.kx.kxss.base.entity.coupon.BaseCouponRange;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.member.coupon.MemberCoupon;
import top.kx.kxss.base.entity.member.deposit.MemberDepositCoupon;
import top.kx.kxss.base.entity.member.deposit.MemberDepositRule;
import top.kx.kxss.base.entity.payment.BasePayment;
import top.kx.kxss.base.entity.payment.BasePaymentType;
import top.kx.kxss.base.entity.service.BaseService;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.entity.tableCharging.BaseTableCharging;
import top.kx.kxss.base.manager.biz.BaseBizLogManager;
import top.kx.kxss.base.manager.coupon.BaseCouponInfoManager;
import top.kx.kxss.base.manager.coupon.BaseCouponRangeManager;
import top.kx.kxss.base.manager.member.MemberInfoManager;
import top.kx.kxss.base.manager.member.coupon.MemberCouponManager;
import top.kx.kxss.base.manager.member.deposit.MemberDepositCouponManager;
import top.kx.kxss.base.manager.member.deposit.MemberDepositRuleManager;
import top.kx.kxss.base.manager.member.grade.MemberGradeManager;
import top.kx.kxss.base.manager.payment.BasePaymentManager;
import top.kx.kxss.base.manager.payment.BasePaymentTypeManager;
import top.kx.kxss.base.manager.system.BaseServiceManager;
import top.kx.kxss.base.manager.table.BaseTableInfoManager;
import top.kx.kxss.base.manager.tableCharging.BaseTableChargingManager;
import top.kx.kxss.common.constant.BizConstant;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.app.BasePaymentTypeEnum;
import top.kx.kxss.model.enumeration.app.PayTypeEnum;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.model.enumeration.base.*;
import top.kx.kxss.pos.vo.service.ServiceTableResultVO;
import top.kx.kxss.wxapp.vo.query.statistics.PosCashDetailsQuery;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * pos结算 含商品出库 服务 台费结算
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:04:53
 * @create [2023-04-19 14:04:53] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class PosCashServiceImpl extends SuperServiceImpl<PosCashManager, Long, PosCash, PosCashSaveVO,
        PosCashUpdateVO, PosCashPageQuery, PosCashResultVO> implements PosCashServiceService {

    @Autowired
    private PosCashTableManager tableManager;

    @Autowired
    private MemberInfoManager memberInfoManager;

    @Autowired
    private PosCashProductManager productManager;
    @Autowired
    private PosCashThailManager posCashThailManager;

    @Autowired
    private PosCashServiceManager serviceManager;

    @Autowired
    private BaseServiceManager baseServiceManager;

    @Autowired
    private BaseTableChargingManager baseTableChargingManager;

    @Autowired
    private MemberGradeManager memberGradeManager;

    @Autowired
    private BaseTableInfoManager tableInfoManager;

    @Autowired
    private MemberCouponManager memberCouponManager;

    @Autowired
    private PosCashPaymentManager posCashPaymentManager;
    @Autowired
    private PosCashDiscountDetailManager posCashDiscountDetailManager;
    @Autowired
    private BaseBizLogManager baseBizLogManager;

    @Autowired
    private BaseCouponInfoManager baseCouponInfoManager;

    @Autowired
    private BaseCouponRangeManager baseCouponRangeManager;

    @Autowired
    private TableOperateService tableOperateService;

    @Autowired
    private AppMemberCouponMapper appMemberCouponMapper;

    @Autowired
    private PosCashEquityManager posCashEquityManager;

    @Autowired
    private PosCashEquityMapper posCashEquityMapper;

    @Autowired
    private PosCashManager posCashManager;
    @Autowired
    private BaseCouponInfoManager couponInfoManager;

    @Autowired
    private BasePaymentTypeManager basePaymentTypeManager;

    @Autowired
    private CalculateBizServiceImpl calculateBizService;

    @Autowired
    private PosCashTableManager posCashTableManager;
    @Autowired
    private PosCashRefundPaymentManager posCashRefundPaymentManager;

    @Autowired
    private BasePaymentManager basePaymentManager;
    @Autowired
    private MemberDepositRuleManager depositRuleManager;
    @Autowired
    private MemberDepositCouponManager depositCouponManager;


    @Override
    public List<PosCashResultVO> getInfoByMemberId(Long memberId) {
        List<PosCash> posCashes = superManager.list(Wraps.<PosCash>lbQ()
                .eq(PosCash::getMemberId, memberId)
                .orderByDesc(PosCash::getCreatedTime));
        if (CollUtil.isEmpty(posCashes)) {
            return null;
        }
        return posCashes.stream().map(v -> BeanUtil.copyProperties(v, PosCashResultVO.class)).collect(Collectors.toList());
    }

    @Override
    public PosCashResultVO detail(Long aLong) {
        PosCash byId = superManager.getById(aLong);
        if (ObjectUtil.isNull(byId)) {
            return null;
        }
        PosCashResultVO posCashResultVO = BeanUtil.copyProperties(byId, PosCashResultVO.class);
        //商品信息
        posCashResultVO.setProductList(productManager.list(Wraps.<PosCashProduct>lbQ()
                .eq(PosCashProduct::getCashId, byId.getId())).stream().map(v -> BeanUtil.copyProperties(v, PosCashProductResultVO.class)).collect(Collectors.toList()));
        //台桌信息
        posCashResultVO.setTableList(tableManager.list(Wraps.<PosCashTable>lbQ()
                .eq(PosCashTable::getCashId, byId.getId())).stream().map(v -> BeanUtil.copyProperties(v, PosCashTableResultVO.class)).collect(Collectors.toList()));
        //服务信息
        posCashResultVO.setServiceList(serviceManager.list(Wraps.<PosCashService>lbQ()
                        .eq(top.kx.kxss.app.entity.cash.service.PosCashService::getCashId, byId.getId())).stream()
                .map(v -> BeanUtil.copyProperties(v, PosCashServiceResultVO.class)).collect(Collectors.toList()));
        //储值信息
        if (ObjectUtil.isNotNull(posCashResultVO.getDepositRuleId())) {
            MemberDepositRule depositRule = depositRuleManager.getById(posCashResultVO.getDepositRuleId());
            DepositRuleResultVO depositRuleResultVO = BeanUtil.copyProperties(depositRule, DepositRuleResultVO.class);
            List<DepositCouponResultVO> couponResultVOList = depositCouponManager.list(Wraps.<MemberDepositCoupon>lbQ()
                            .eq(MemberDepositCoupon::getDepositRuleId, posCashResultVO.getDepositRuleId()))
                    .stream().map(v -> BeanUtil.copyProperties(v, DepositCouponResultVO.class)).collect(Collectors.toList());
            depositRuleResultVO.setCouponList(couponResultVOList);
            posCashResultVO.setDepositRuleVO(depositRuleResultVO);
        }
        return posCashResultVO;
    }

    @Override
    public Map<String, Object> memeberCouponByType(Long memberId, Long cashId) {
        Map<String, Object> resultMap = new HashMap<>();

        // 未使用的优惠券
        List<CouponVo> unUsedCoupons = coupons(1, memberId);
        // 已使用的优惠券
        List<CouponVo> usedCoupons = coupons(2, memberId);
        // 过期的优惠券
        List<CouponVo> outCoupons = coupons(3, memberId);
        // 正在使用
        List<CouponVo> useingCoupons = getUseingCoupons(cashId);

        resultMap.put("total", unUsedCoupons.size() + usedCoupons.size() + outCoupons.size());
        resultMap.put("unUsedCoupons", unUsedCoupons);
        resultMap.put("usedCoupons", usedCoupons);
        resultMap.put("outCoupons", outCoupons);
        resultMap.put("useingCoupons", useingCoupons);
        return resultMap;
    }

    @Override
    public Map<String, Object> memebercCouponDetail(Long memberId, Long couponId) {
        List<MemberCoupon> memberCoupons = memberCouponManager.list(Wraps.<MemberCoupon>lbQ()
                .eq(MemberCoupon::getMemberId, memberId)
                .eq(MemberCoupon::getCouponId, couponId));
        BaseCouponInfo couponInfo = baseCouponInfoManager.getById(couponId);

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("name", memberCoupons.get(0).getName());
        resultMap.put("num", memberCoupons.size());
        resultMap.put("type", memberCoupons.get(0).getType());
        resultMap.put("status", memberCoupons.get(0).getStatus());
        resultMap.put("original_price", couponInfo.getOriginalPrice());
        resultMap.put("usedLimited", couponInfo.getUsedLimited());
        resultMap.put("usableRange", couponInfo.getUsableRange());
        resultMap.put("usedRepeat", couponInfo.getUsedRepeat());
        resultMap.put("usageTime", memberCoupons.get(0).getUsageTime());
        resultMap.put("expiresTime", memberCoupons.get(0).getExpiresTime());

        return resultMap;
    }

    @Override
    public LinkedHashMap<String, Object> tablePosCash(Map<String, Object> param) {
        Boolean needUpdate = (Boolean) param.get("needUpdate");
        Long tableId = null;
        if (param.containsKey("tableId") && ObjectUtil.isNotNull(param.get("tableId"))) {
            if (param.get("tableId") instanceof Long) {
                tableId = (Long) param.get("tableId");
            } else {
                tableId = Long.parseLong((String) param.get("tableId"));
            }
        }

        Long cashId;
        if (param.get("cashId") instanceof Long) {
            cashId = (Long) param.get("cashId");
        } else {
            cashId = Long.parseLong((String) param.get("cashId"));
        }
        // 获取优惠劵
        List<CouponVo> coupons = getUseingCoupons(cashId);

        List<CouponVo> couponVosByTable = new ArrayList<>();
        List<CouponVo> couponVosByProduct = new ArrayList<>();
        List<CouponVo> couponVosByService = new ArrayList<>();
        for (CouponVo vo : coupons) {
            if (vo.getUsableRange().contains("1")) {
                couponVosByTable.add(vo);
            }
            if (vo.getUsableRange().contains("2")) {
                couponVosByProduct.add(vo);
            }
            if (vo.getUsableRange().contains("3")) {
                couponVosByService.add(vo);
            }
        }

        // 获取订单信息
        PosCash posCash = superManager.getById(cashId);

        LinkedHashMap<String, Object> memberInfoMap = new LinkedHashMap<>();
        if (null != posCash.getMemberId()) {
            MemberInfo memberInfo = memberInfoManager.getById(posCash.getMemberId());
            memberInfoMap.put("name", memberInfo.getName());
            memberInfoMap.put("phone", memberInfo.getMobile());
            memberInfoMap.put("grade", memberGradeManager.getById(memberInfo.getGradeId()).getName());
            memberInfoMap.put("gradeId", memberGradeManager.getById(memberInfo.getGradeId()).getId());
        } else {
            memberInfoMap.put("name", "非会员");
            memberInfoMap.put("phone", "非会员");
            memberInfoMap.put("grade", "非会员");
            memberInfoMap.put("gradeId", "非会员");
        }

        // 台桌费用结果
        PosCashAmountVO tableCashVo = tableCash(tableId, posCash.getId(), needUpdate);

        // 服务总价和折扣
        PosCashAmountVO serviceCashVo = serviceCash(posCash.getId(), needUpdate);

        // 物品总价与折扣
        PosCashAmountVO productCashVo = productCash(posCash.getId());

        // 充值总价与折扣
        PosCashAmountVO depositCashVo = depositCash(posCash.getId());

        // 计算总价
        BigDecimal tableAmount = tableCashVo.getAmount();
        BigDecimal serviceAmount = serviceCashVo.getAmount();
        BigDecimal productAmount = productCashVo.getAmount();
        BigDecimal totalAmount = tableAmount.add(serviceAmount).add(productAmount).add(depositCashVo.getAmount());

        BigDecimal tableDiscount = tableCashVo.getDiscountAmount();
        BigDecimal serviceDiscount = serviceCashVo.getDiscountAmount();
        BigDecimal productDiscount = productCashVo.getDiscountAmount();
        BigDecimal totalDiscount = tableDiscount.add(serviceDiscount).add(productDiscount).add(depositCashVo.getDiscountAmount());

        List<CouponResultVo> couponResultVos = new ArrayList<>();
//        // 优惠券减免信息
//        Map<Long, BigDecimal> couponMap = couponUsed(coupons, cashId);
//        BigDecimal totalCoupon = new BigDecimal(0);
//        for (Long cId : couponMap.keySet()) {
//            totalCoupon = totalCoupon.add(couponMap.get(cId));
//            CouponResultVo vo = new CouponResultVo();
//            vo.setId(cId);
//            vo.setName(couponNameMap.get(cId));
//            vo.setType(1);
//            vo.setAmount(couponMap.get(cId));
//            couponResultVos.add(vo);
//        }
//
//        if (new BigDecimal(0).compareTo(totalDiscount) != 0) {
//            CouponResultVo vo = new CouponResultVo();
//            vo.setName("会员折扣");
//            vo.setAmount(totalDiscount);
//            couponResultVos.add(vo);
//        }

//        BigDecimal tableResultAmount = tableAmount.subtract(tableDiscount).subtract(couponMap.get(1L));
//        if (new BigDecimal(0).compareTo(tableResultAmount) > 0) {
//            tableResultAmount = new BigDecimal(0);
//        }
//        BigDecimal serviceResultAmount = serviceAmount.subtract(serviceDiscount).subtract(couponMap.get(2L));
//        if (new BigDecimal(0).compareTo(serviceResultAmount) > 0) {
//            serviceResultAmount = new BigDecimal(0);
//        }
//        BigDecimal productResultAmount = productAmount.subtract(productDiscount).subtract(couponMap.get(3L));
//        if (new BigDecimal(0).compareTo(productResultAmount) > 0) {
//            productResultAmount = new BigDecimal(0);
//        }
//
//        BigDecimal totalResultAmount = tableResultAmount.add(serviceResultAmount).add(productResultAmount);

        // TODO 优惠券暂不考虑
        //BigDecimal totalResultAmount = totalAmount.subtract(totalDiscount).subtract(totalCoupon);
        BigDecimal totalResultAmount = totalAmount.subtract(totalDiscount);


        // 手动减免
        BigDecimal handDiscount = new BigDecimal(0);
        if (null != posCash.getDiscount() && new BigDecimal("0.00").compareTo(posCash.getDiscount()) < 0) {
            if ("2".equals(posCash.getDiscountType())) {
                // 代金券
                handDiscount = posCash.getDiscount();
            } else if ("1".equals(posCash.getDiscountType())) {
                // 打折
                BigDecimal hDiscount = new BigDecimal(1).subtract(posCash.getDiscount().divide(new BigDecimal(100), 2, RoundingMode.UP)).setScale(2, RoundingMode.UP);
                handDiscount = totalAmount.multiply(hDiscount).setScale(2, RoundingMode.UP);
            }
            CouponResultVo vo = new CouponResultVo();
            vo.setId(BizConstant.DEFAULT_ID);
            vo.setName("手动减免");
            vo.setType(2);
            vo.setAmount(handDiscount);
            couponResultVos.add(vo);
        }

        totalResultAmount = totalResultAmount.subtract(handDiscount);
        if (null != posCash.getRoundAmount()) {
            totalResultAmount = totalResultAmount.subtract(posCash.getRoundAmount());
        }

        // 总价与总折扣
        BigDecimal subtractAmount = totalAmount.subtract(totalResultAmount);

        PosCashAmountVO totalVo = new PosCashAmountVO();
        totalVo.setAmount(totalAmount);
        totalVo.setDiscountAmount(new BigDecimal(0));
        totalVo.setCouponAmount(subtractAmount);
        totalVo.setResultAmount(totalResultAmount);
        totalVo.setCouponResultVos(couponResultVos);
        // 设置代收金额
        if (null != posCash.getUnpaid() && posCash.getUnpaid().compareTo(new BigDecimal(0)) != 0) {
            totalVo.setYsAmount(posCash.getUnpaid());
        } else {
            totalVo.setYsAmount(totalResultAmount);
        }

//        if (null != posCash.getDiscountAmount()) {
//            totalVo.setCouponAmount(posCash.getDiscountAmount());
//        }

//        if(null != posCash.getRoundAmount() &&  posCash.getRoundAmount().compareTo(new BigDecimal(0)) != 0) {
//            totalVo.setYsAmount(totalVo.getYsAmount().subtract(posCash.getRoundAmount()));
//        }

        // 获取已支付过的金额
        List<PosCashPayment> payments = posCashPaymentManager.list(Wraps.<PosCashPayment>lbQ().eq(PosCashPayment::getCashId, cashId).eq(PosCashPayment::getStatus, '1').eq(PosCashPayment::getDeleteFlag, 0).orderByAsc(PosCashPayment::getPayTime));

        List<PosCashPaymentResultVO> paymentResults = BeanUtil.copyToList(payments, PosCashPaymentResultVO.class);

        if (null != posCash.getRoundAmount() && posCash.getRoundAmount().compareTo(new BigDecimal(0)) != 0) {
            // 加入 couponResultVos
            CouponResultVo vo = new CouponResultVo();
            vo.setId(2L);
            vo.setName("抹零");
            vo.setType(99);
            vo.setAmount(posCash.getRoundAmount());
            totalVo.getCouponResultVos().add(vo);
        }

        // 结果集
        LinkedHashMap<String, Object> result = new LinkedHashMap<>();
        result.put("memberInfo", memberInfoMap);
        result.put("totalCash", totalVo);
        result.put("tableCash", tableCashVo);
        result.put("serviceCash", serviceCashVo);
        result.put("productCash", productCashVo);
        result.put("paymentResults", paymentResults);
        return result;
    }

    @Override
    public String paymentSave(PosCashPaymentSaveVO vo) {
        PosCashPayment posCashPayment = new PosCashPayment();
        BeanUtils.copyProperties(vo, posCashPayment);
        if (StringUtils.isBlank(posCashPayment.getSn())) {
            posCashPayment.setSn(ContextUtil.getSn());
        }
        boolean suc = posCashPaymentManager.save(posCashPayment);

        if (!suc) {
            return "保存失败！";
        }

        return "";
    }

    @Override
    public String couponChecked(Map<String, Object> param) {
        Long cashId = Long.parseLong((String) param.get("cashId"));
        List<Long> couponIds = JSONObject.parseArray(param.get("couponIds").toString(), Long.class);

        List<BaseCouponInfo> couponInfos = baseCouponInfoManager.list(Wraps.<BaseCouponInfo>lbQ()
                .in(BaseCouponInfo::getId, couponIds));

        List<PosCashService> posCashServices = serviceManager.list(Wraps.<PosCashService>lbQ()
                .eq(PosCashService::getCashId, cashId));
        Map<Long, List<PosCashService>> posCashServiceMap = posCashServices.stream().collect(Collectors.groupingBy(PosCashService::getServiceId));

        List<PosCashProduct> posCashProducts = productManager.list(Wraps.<PosCashProduct>lbQ()
                .eq(PosCashProduct::getCashId, cashId));
        Map<Long, List<PosCashProduct>> posCashProductMap = posCashProducts.stream().collect(Collectors.groupingBy(PosCashProduct::getProductId));

        List<BaseCouponRange> couponRanges = baseCouponRangeManager.list(Wraps.<BaseCouponRange>lbQ()
                .in(BaseCouponRange::getCouponId, couponIds));

        int loopCnt = 0;
        for (BaseCouponInfo coupon : couponInfos) {
            if (coupon.getUsableRange().contains("1")) {
                loopCnt++;
            }
            // TODO 判断是否可以叠加使用
            if ("1".equals(coupon.getUsedRepeat())) {
                return "优惠券不可叠加使用";
            }
        }

        for (BaseCouponRange bcr : couponRanges) {
            if (null == posCashServiceMap.get(bcr.getSourceId()) || null == posCashProductMap.get(bcr.getSourceId())) {
                loopCnt++;
            }
        }


        if (loopCnt == couponRanges.size()) {
            return "没有该优惠劵可用商品！";
        } else {
            return "";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveUsingCoupon(Map<String, Object> param) {
        Long memberCouponId = Long.parseLong((String) param.get("memberCouponId"));
        Long cashId = Long.parseLong((String) param.get("cashId"));
        PosCashEquity posCashEquity = new PosCashEquity();
        posCashEquity.setCashId(cashId);
        posCashEquity.setType("2");
        posCashEquity.setBizId(memberCouponId);
        posCashEquity.setSn(ContextUtil.getSn());
        boolean suc = posCashEquityManager.save(posCashEquity);
        if (!suc) {
            return false;
        }
        // 更新memberCoupon为已使用状态
        MemberCoupon memberCoupon = memberCouponManager.getById(memberCouponId);
        memberCoupon.setStatus("2");
        memberCoupon.setUsageTime(LocalDateTime.now());
        memberCoupon.setUpdatedTime(LocalDateTime.now());
        suc = memberCouponManager.updateById(memberCoupon);
        if (!suc) {
            throw new BizException("更新失败");
        }
        return true;
    }

    @Override
    public Map<String, Object> queryUsingEquity(Map<String, Object> param) {
        Long cashId = Long.parseLong((String) param.get("cashId"));
        Map<String, Object> result = new HashMap<>();
        // 已使用优惠券
        List<AppEquity> coupons = posCashEquityMapper.queryMemberCoupon(cashId);
        result.put("coupons", coupons);
        return result;
    }

    @Override
    public String disabledCoupon(Map<String, Object> param) {
        Long cashId = Long.parseLong((String) param.get("cashId"));
        CouponResultVo vo = JSONObject.parseObject(JSONObject.toJSONString(param.get("couponVo")), CouponResultVo.class);

        PosCash posCash = superManager.getById(cashId);
        Long memberId = posCash.getMemberId();

        if (1 == vo.getType()) {
            MemberCoupon memberCoupon = memberCouponManager.getOne(Wraps.<MemberCoupon>lbQ()
                    .eq(MemberCoupon::getMemberId, memberId)
                    .eq(MemberCoupon::getCouponId, vo.getId()));
            memberCoupon.setStatus("1");
            memberCoupon.setUpdatedTime(LocalDateTime.now());
            memberCouponManager.updateById(memberCoupon);

            posCashEquityManager.remove(Wraps.<PosCashEquity>lbQ()
                    .eq(PosCashEquity::getBizId, memberCoupon.getId())
                    .eq(PosCashEquity::getCashId, cashId));
        } else {
            tableOperateService.disCompleteOrder(param);
        }
        return "";
    }

    @Override
    public BigDecimal queryMemberBalance(Map<String, Object> param) {
        MemberInfo memberInfo = memberInfoManager.getById((String) param.get("memberId"));
        BigDecimal giftAmount = null == memberInfo.getGiftAmount() ? new BigDecimal(0) : memberInfo.getGiftAmount();
        BigDecimal rechargeAmount = null == memberInfo.getRechargeAmount() ? new BigDecimal(0) : memberInfo.getRechargeAmount();
        return giftAmount.add(rechargeAmount);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PosCashPayVo doCashPay(PosCashPaymentSaveVO saveVO) {
        PosCashPayVo result = new PosCashPayVo();
        // 1. 当前支付payment
        // 2. 获取posCash
        PosCash posCash = posCashManager.getById(saveVO.getCashId());
        // 3. 查询本次订单是否全部终止
        long paymentCnt = posCashPaymentManager.count(Wraps.<PosCashPayment>lbQ().eq(PosCashPayment::getCashId, saveVO.getCashId()).eq(PosCashPayment::getDeleteFlag, 0));
        boolean needUpdate = 0 == paymentCnt;
        // 4. 重新计算所有金额
        Map<String, Object> param = new HashMap<>();
        param.put("tableId", posCash.getTableId());
        param.put("cashId", posCash.getId());
        param.put("needUpdate", needUpdate);
        LinkedHashMap<String, Object> posResult = tablePosCash(param);

        PosCashAmountVO totalVo = (PosCashAmountVO) posResult.get("totalCash");

        // 插入payment表
        PosCashPayment payment = BeanUtil.copyProperties(saveVO, PosCashPayment.class);
        payment.setRemarks("现金支付" + payment.getAmount() + "元");
        payment.setPayTime(LocalDateTime.now());
        // 当有找零时，amount为收款金额 - 找零金额
        if (null != saveVO.getZlPaice() && saveVO.getZlPaice().compareTo(new BigDecimal(0)) != 0) {
            payment.setAmount((saveVO.getAmount()).subtract(saveVO.getZlPaice()));
        }
        payment.setStatus("1");
        payment.setSn(ContextUtil.getSn());
        boolean suc = posCashPaymentManager.save(payment);
        if (!suc) {
            // 可以不回滚事务
            return null;
        }
        // 更新posCash
        if (null == posCash.getPaid()) {
            // 第一次支付
            // 金额
            posCash.setAmount(totalVo.getAmount());
            // 优惠金额
            BigDecimal coupon = null == totalVo.getCouponAmount() ? new BigDecimal(0) : totalVo.getCouponAmount();
            posCash.setDiscountAmount(coupon);
            // 支付金额
            posCash.setPayment(posCash.getAmount().subtract(posCash.getDiscountAmount()));
            // 已付款金额
            posCash.setPaid(payment.getAmount());
            // 未付款金额
            // 判断是否全部支付完
            if (posCash.getPayment().compareTo(posCash.getPaid()) <= 0) {
                // 已支付完成
                posCash.setBillState("0");
                posCash.setUnpaid(new BigDecimal(0));
                result.setIsFinish(true);
            } else {
                // 计算未付款金额
                posCash.setUnpaid(posCash.getPayment().subtract(posCash.getPaid()));
                result.setIsFinish(false);
            }
            posCash.setPayName("现金");
        } else {
            // 之后的支付
            BigDecimal coupon = null == totalVo.getCouponAmount() ? new BigDecimal(0) : totalVo.getCouponAmount();
            // 金额
            posCash.setAmount(totalVo.getAmount());
            // 是否抹零
            if (null != saveVO.getRoundAmount() && !BigDecimal.ZERO.equals(saveVO.getRoundAmount())) {
                posCash.setDiscountAmount(coupon);
                // 支付金额
                posCash.setPayment(posCash.getAmount().subtract(posCash.getDiscountAmount()));
            }
            // 已付款金额
            posCash.setPaid(payment.getAmount().add(posCash.getPaid()));
            // 未付款金额
            // 判断是否全部支付完
            if (posCash.getPayment().compareTo(posCash.getPaid()) <= 0) {
                // 已支付完成
                posCash.setBillState("0");
                posCash.setUnpaid(new BigDecimal(0));
                result.setIsFinish(true);
            } else {
                // 计算未付款金额
                posCash.setUnpaid(posCash.getPayment().subtract(posCash.getPaid()));
                result.setIsFinish(false);
            }
        }

        // 更新
        posCash.setUpdatedTime(LocalDateTime.now());
        posCash.setUpdatedBy(ContextUtil.getUserId());
        suc = posCashManager.updateById(posCash);
        if (!suc) {
            throw new BizException("支付失败，请稍后再试");
        }
        // 更新所有cash_table 的status 为1
        PosCashTable posCashTable = new PosCashTable();
        posCashTable.setStatus("1");
        posCashTable.setUpdatedTime(LocalDateTime.now());
        posCashTableManager.update(posCashTable, Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, posCash.getId()));
        // 全部支付完后 释放台桌
        if ("0".equals(posCash.getBillState()) && ObjectUtil.isNotNull(posCash.getTableId())) {
            BaseTableInfo tableInfo = tableInfoManager.getById(posCash.getTableId());
            tableInfo.setTableStatus(TableStatus.UNUSED.getCode());
            tableInfo.setLightStatus("0");
            tableInfo.setUpdatedTime(LocalDateTime.now());
            suc = tableInfoManager.updateById(tableInfo);
            if (!suc) {
                throw new BizException("台桌释放失败，请稍后再试");
            }
            // 计算各金额合计
            calPosCashTotal(posCash);
            // 不回滚 允许支付成功
            posCash.setUpdatedBy(ContextUtil.getUserId());
            posCash.setUpdatedTime(LocalDateTime.now());
            posCashManager.updateById(posCash);
        }
        // 全部支付完后 增加账户金额
        if ("0".equals(posCash.getBillState()) && ObjectUtil.equal(posCash.getType(), PosCashTypeEnum.RECHARGE.getCode())) {
            MemberInfo memberInfo = memberInfoManager.getById(posCash.getMemberId());
            memberInfo.setRechargeAmount(memberInfo.getRechargeAmount().add(ObjectUtil.isNotNull(posCash.getAmount()) ? posCash.getAmount() : BigDecimal.ZERO));
            memberInfo.setGiftAmount(memberInfo.getGiftAmount().add(ObjectUtil.isNotNull(posCash.getGiftAmount()) ? posCash.getGiftAmount() : BigDecimal.ZERO));
            memberInfo.setUpdatedTime(LocalDateTime.now());
            memberInfoManager.updateById(memberInfo);
        }
        //全部支付完成  写入base_payment
        if (result.getIsFinish()) {
            suc = createBasePayment(posCash.getId(), BasePaymentTypeEnum.CONSUMPTION);
            if (!suc) {
                throw new BizException("收款单保存失败！");
            }
        }
        return result;
    }

    /**
     * 保存收款单
     */
    @Override
    public Boolean createBasePayment(Long posCashId, BasePaymentTypeEnum basePaymentTypeEnum) {
        PosCash posCash = posCashManager.getById(posCashId);
        BigDecimal accountBalance = BigDecimal.ZERO;
        if (ObjectUtil.isNotNull(posCash.getMemberId())) {
            MemberInfo memberInfo = memberInfoManager.getById(posCash.getMemberId());
            accountBalance = accountBalance.add(memberInfo.getRechargeAmount()).add(memberInfo.getGiftAmount());
        }
        List<PosCashPayment> cashPaymentList = posCashPaymentManager.list(Wraps.<PosCashPayment>lbQ().eq(PosCashPayment::getCashId, posCash.getId())
                .eq(PosCashPayment::getStatus, PosCashPaymentStatusEnum.PAY_SUCCESS.getCode()));
        BigDecimal amount = BigDecimal.ZERO;
        StringBuilder sb = new StringBuilder();
        List<Long> payTypeIds = cashPaymentList.stream().map(PosCashPayment::getPayTypeId).collect(Collectors.toList());
        Map<Long, BasePaymentType> paymentTypeMap = CollUtil.isNotEmpty(payTypeIds) ? basePaymentTypeManager.list(Wraps.<BasePaymentType>lbQ().in(BasePaymentType::getId, payTypeIds))
                .stream().collect(Collectors.toMap(BasePaymentType::getId, Function.identity())) : MapUtil.newHashMap();
        for (PosCashPayment posCashPayment : cashPaymentList) {
            amount = amount.add(posCashPayment.getAmount());
            if (ObjectUtil.isNotNull(posCashPayment.getChangeAmount())) {
                amount = amount.subtract(posCashPayment.getChangeAmount());
            }
            if (CollUtil.isNotEmpty(paymentTypeMap)
                    && ObjectUtil.isNotNull(paymentTypeMap.get(posCashPayment.getPayTypeId()))) {
                BasePaymentType basePaymentType = paymentTypeMap.get(posCashPayment.getPayTypeId());
                sb.append(StrUtil.isNotBlank(sb.toString()) ? "+" : "")
                        .append(basePaymentType.getName());
                continue;
            }
            sb.append(StrUtil.isNotBlank(sb.toString()) ? "+" : "")
                    .append(PayTypeEnum.get(posCashPayment.getPayTypeId().toString()).getDesc());

        }
        return basePaymentManager.save(BasePayment.builder()
                .amount(amount).payName(sb.toString()).accountBalance(accountBalance)
                .createdOrgId(ContextUtil.getCurrentCompanyId()).type(basePaymentTypeEnum.getCode())
                .payTime(LocalDateTime.now()).memberId(posCash.getMemberId()).sourceId(posCash.getId())
                .employeeId(posCash.getEmployeeId()).code(posCash.getCode())
                .build());
    }


    @Override
    public Boolean createBasePayment(PosCash posCash, BasePaymentTypeEnum typeEnum, MemberInfo memberInfo) {
        BigDecimal accountBalance = BigDecimal.ZERO;
        if (ObjectUtil.isNotNull(memberInfo)) {
            memberInfo.setRechargeAmount(memberInfo.getRechargeAmount() == null ? BigDecimal.ZERO : memberInfo.getRechargeAmount());
            memberInfo.setGiftAmount(memberInfo.getGiftAmount() == null ? BigDecimal.ZERO : memberInfo.getGiftAmount());
            accountBalance = accountBalance.add(memberInfo.getRechargeAmount()).add(memberInfo.getGiftAmount());
        }
        List<PosCashPayment> cashPaymentList = posCashPaymentManager.list(Wraps.<PosCashPayment>lbQ()
                .eq(PosCashPayment::getDeleteFlag, 0).eq(PosCashPayment::getCashId, posCash.getId())
                .eq(PosCashPayment::getStatus, PosCashPaymentStatusEnum.PAY_SUCCESS.getCode()));
        BigDecimal amount = BigDecimal.ZERO;
        StringBuilder sb = new StringBuilder();
        List<Long> payTypeIds = cashPaymentList.stream().map(PosCashPayment::getPayTypeId).collect(Collectors.toList());
        Map<Long, BasePaymentType> paymentTypeMap = CollUtil.isNotEmpty(payTypeIds) ? basePaymentTypeManager.list(Wraps.<BasePaymentType>lbQ().in(BasePaymentType::getId, payTypeIds))
                .stream().collect(Collectors.toMap(BasePaymentType::getId, Function.identity())) : MapUtil.newHashMap();
        for (PosCashPayment posCashPayment : cashPaymentList) {
            amount = amount.add(posCashPayment.getAmount());
            if (ObjectUtil.isNotNull(posCashPayment.getChangeAmount())) {
                amount = amount.subtract(posCashPayment.getChangeAmount());
            }
            if (CollUtil.isNotEmpty(paymentTypeMap)
                    && ObjectUtil.isNotNull(paymentTypeMap.get(posCashPayment.getPayTypeId()))) {
                BasePaymentType basePaymentType = paymentTypeMap.get(posCashPayment.getPayTypeId());
                sb.append(StrUtil.isNotBlank(sb.toString()) ? "+" : "")
                        .append(basePaymentType.getName());
                continue;
            }
            sb.append(StrUtil.isNotBlank(sb.toString()) ? "+" : "")
                    .append(PayTypeEnum.get(posCashPayment.getPayTypeId().toString()).getDesc());

        }
        basePaymentManager.update(Wraps.<BasePayment>lbU()
                .set(BasePayment::getDeleteFlag, 1)
                .eq(BasePayment::getSourceId, posCash.getId())
                .eq(BasePayment::getDeleteFlag, 0));

        return basePaymentManager.save(BasePayment.builder()
                .amount(amount).payName(sb.toString()).accountBalance(accountBalance)
                .createdOrgId(ContextUtil.getCurrentCompanyId()).type(typeEnum.getCode())
                .payTime(LocalDateTime.now()).memberId(posCash.getMemberId()).sourceId(posCash.getId())
                .employeeId(posCash.getEmployeeId()).code(posCash.getCode())
                .build());
    }

    @Override
    public Boolean createBasePayment(PosCash posCash, BasePaymentTypeEnum typeEnum, List<PosCashRefundPayment> refundPaymentList, MemberInfo memberInfo) {
        BigDecimal accountBalance = BigDecimal.ZERO;
        if (ObjectUtil.isNotNull(memberInfo)) {
            memberInfo.setRechargeAmount(memberInfo.getRechargeAmount() == null ? BigDecimal.ZERO : memberInfo.getRechargeAmount());
            memberInfo.setGiftAmount(memberInfo.getGiftAmount() == null ? BigDecimal.ZERO : memberInfo.getGiftAmount());
            accountBalance = accountBalance.add(memberInfo.getRechargeAmount()).add(memberInfo.getGiftAmount());
        }
        BigDecimal amount = BigDecimal.ZERO;
        StringBuilder sb = new StringBuilder();
        List<Long> payTypeIds = refundPaymentList.stream().map(PosCashRefundPayment::getPayTypeId).collect(Collectors.toList());
        Map<Long, BasePaymentType> paymentTypeMap = CollUtil.isNotEmpty(payTypeIds) ? basePaymentTypeManager.list(Wraps.<BasePaymentType>lbQ().in(BasePaymentType::getId, payTypeIds))
                .stream().collect(Collectors.toMap(BasePaymentType::getId, Function.identity())) : MapUtil.newHashMap();
        for (PosCashRefundPayment posCashPayment : refundPaymentList) {
            amount = amount.add(posCashPayment.getAmount());
            if (ObjectUtil.isNotNull(posCashPayment.getChangeAmount())) {
                amount = amount.subtract(posCashPayment.getChangeAmount());
            }
            if (CollUtil.isNotEmpty(paymentTypeMap)
                    && ObjectUtil.isNotNull(paymentTypeMap.get(posCashPayment.getPayTypeId()))) {
                BasePaymentType basePaymentType = paymentTypeMap.get(posCashPayment.getPayTypeId());
                sb.append(StrUtil.isNotBlank(sb.toString()) ? "+" : "")
                        .append(basePaymentType.getName());
                continue;
            }
            sb.append(StrUtil.isNotBlank(sb.toString()) ? "+" : "")
                    .append(PayTypeEnum.get(posCashPayment.getPayTypeId().toString()).getDesc());

        }
        BasePayment basePayment = basePaymentManager
                .getOne(Wraps.<BasePayment>lbQ().eq(BasePayment::getDeleteFlag, 0)
                        .eq(BasePayment::getType, typeEnum.getCode())
                        .eq(BasePayment::getSourceId, posCash.getId()));
        if (ObjectUtil.isNotNull(basePayment)) {
            basePaymentManager.remove(Wraps.<BasePayment>lbQ()
                    .eq(BasePayment::getType, typeEnum.getCode())
                    .eq(BasePayment::getSourceId, posCash.getId()));
        }
        return basePaymentManager.save(BasePayment.builder()
                .amount(amount).payName(sb.toString()).accountBalance(accountBalance)
                .createdOrgId(ContextUtil.getCurrentCompanyId()).type(typeEnum.getCode())
                .payTime(LocalDateTime.now()).memberId(posCash.getMemberId()).sourceId(posCash.getId())
                .employeeId(posCash.getEmployeeId()).code(posCash.getCode())
                .explain(ObjectUtil.equal(posCash.getType(), PosCashTypeEnum.RECHARGE.getCode())
                        ? "充值退费" : "消费退费")
                .remarks(ObjectUtil.equal(posCash.getType(), PosCashTypeEnum.RECHARGE.getCode())
                        ? "充值退费" : "消费退费")
                .build());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String removeMl(PosCashPaymentPageQuery queryVo) {
        // 获取抹零的总金额-针对payment
//        List<PosCashPayment> payments = posCashPaymentManager.list(Wraps.<PosCashPayment>lbQ().eq(PosCashPayment::getCashId, queryVo.getCashId()).eq(PosCashPayment::getDeleteFlag, 0).orderByAsc(PosCashPayment::getPayTime));
//        BigDecimal totalMl = new BigDecimal(0);
//        if(null != payments && 0 != payments.size()) {
//            // 查看是否有抹零 有则加入 couponResultVos 中
//            for(PosCashPayment pcp : payments) {
//                if(null != pcp.getRoundAmount()) {
//                    totalMl = totalMl.add(new BigDecimal(pcp.getRoundAmount()));
//                }
//            }
//        }
//        PosCashPayment posCashPayment = new PosCashPayment();
//        posCashPayment.setRoundAmount(0d);
//        boolean suc = posCashPaymentManager.update(posCashPayment, Wraps.<PosCashPayment>lbQ().eq(PosCashPayment::getCashId, queryVo.getCashId()));
//        if(!suc) {
//            return "取消失败";
//        }
        // 更新posCash的抹零
        PosCash posCash = posCashManager.getById(queryVo.getCashId());
        BigDecimal roundAmount = posCash.getRoundAmount();
        posCash.setRoundAmount(new BigDecimal(0));
        // 优惠减
        if (decimalNull(posCash.getDiscountAmount())) {
            posCash.setDiscountAmount(posCash.getDiscountAmount().subtract(roundAmount));
        }

        // 未支付加
        if (decimalNull(posCash.getUnpaid())) {
            posCash.setUnpaid(posCash.getUnpaid().add(roundAmount));
        }
        posCash.setPayment(posCash.getPayment().add(roundAmount));
        posCash.setUpdatedTime(LocalDateTime.now());
        posCash.setUpdatedBy(ContextUtil.getUserId());
        boolean suc = posCashManager.updateById(posCash);
        if (!suc) {
            throw new BizException("取消失败");
        }
        return null;
    }

    @Override
    public Boolean createBasePayment(Long posCashId, BasePaymentTypeEnum typeEnum,
                                     PosCashPayment cashPayment, MemberInfo memberInfo) {
        PosCash posCash = posCashManager.getById(posCashId);
        BigDecimal accountBalance = BigDecimal.ZERO;
        BasePayment basePayment = basePaymentManager.getOne(Wraps.<BasePayment>lbQ().eq(BasePayment::getSourceId, posCash.getId()));
        if (ObjectUtil.isNotNull(basePayment)) {
            return true;
        }
        if (ObjectUtil.isNotNull(posCash.getMemberId()) && ObjectUtil.isNotNull(memberInfo)) {
            accountBalance = accountBalance.add(memberInfo.getRechargeAmount() == null ? BigDecimal.ZERO : memberInfo.getRechargeAmount())
                    .add(memberInfo.getGiftAmount() == null ? BigDecimal.ZERO : memberInfo.getGiftAmount());
        }
        List<PosCashPayment> cashPaymentList = posCashPaymentManager.list(Wraps.<PosCashPayment>lbQ().eq(PosCashPayment::getCashId, posCash.getId())
                .notIn(PosCashPayment::getStatus, Arrays.asList(PosCashPaymentStatusEnum.NO_PAY.getCode(),
                        PosCashPaymentStatusEnum.PAY_FAIL.getCode())).ne(PosCashPayment::getId, cashPayment.getId()));
        BigDecimal amount = posCash.getPayment();
        StringBuilder sb = new StringBuilder();
        if (CollUtil.isEmpty(cashPaymentList) && ObjectUtil.equal(cashPayment.getStatus(), PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())) {
            cashPaymentList.add(cashPayment);
        }
        List<Long> payTypeIds = cashPaymentList.stream().map(PosCashPayment::getPayTypeId).collect(Collectors.toList());
        Map<Long, BasePaymentType> paymentTypeMap = CollUtil.isNotEmpty(payTypeIds) ? basePaymentTypeManager.list(Wraps.<BasePaymentType>lbQ().in(BasePaymentType::getId, payTypeIds))
                .stream().collect(Collectors.toMap(BasePaymentType::getId, Function.identity())) : MapUtil.newHashMap();
        for (PosCashPayment posCashPayment : cashPaymentList) {
            amount = amount.add(posCashPayment.getAmount());
            if (ObjectUtil.isNotNull(posCashPayment.getChangeAmount())) {
                amount = amount.subtract(posCashPayment.getChangeAmount());
            }
            if (CollUtil.isNotEmpty(paymentTypeMap)
                    && ObjectUtil.isNotNull(paymentTypeMap.get(posCashPayment.getPayTypeId()))) {
                BasePaymentType basePaymentType = paymentTypeMap.get(posCashPayment.getPayTypeId());
                sb.append(StrUtil.isNotBlank(sb.toString()) ? "+" : "")
                        .append(basePaymentType.getName());
                continue;
            }
            sb.append(StrUtil.isNotBlank(sb.toString()) ? "+" : "")
                    .append(PayTypeEnum.get(posCashPayment.getPayTypeId().toString()).getDesc());

        }
        return basePaymentManager.save(BasePayment.builder()
                .amount(amount).payName(sb.toString()).accountBalance(accountBalance)
                .createdOrgId(ContextUtil.getCurrentCompanyId()).type(typeEnum.getCode())
                .payTime(LocalDateTime.now()).memberId(posCash.getMemberId()).sourceId(posCash.getId())
                .employeeId(posCash.getEmployeeId()).code(posCash.getCode())
                .build());
    }

    @Override
    public String removePay(PosCashPaymentPageQuery queryVo) {
        PosCashPayment posCashPayment = posCashPaymentManager.getById(queryVo.getId());
        BigDecimal amount = posCashPayment.getAmount();
        PosCash posCash = posCashManager.getById(queryVo.getCashId());
        // 已支付 - payment的金额
        posCash.setPaid(posCash.getPaid().subtract(amount));
        // 未支付
        posCash.setUnpaid(posCash.getUnpaid().add(amount));
        posCash.setUpdatedBy(ContextUtil.getUserId());
        posCash.setUpdatedTime(LocalDateTime.now());
        boolean suc = posCashManager.updateById(posCash);
        if (!suc) {
            return "取消失败";
        }
        // 删除poscashpayment
        posCashPayment.setDeleteFlag(1);
        posCashPayment.setUpdatedTime(LocalDateTime.now());
        suc = posCashPaymentManager.removeById(posCashPayment);
        if (!suc) {
            throw new BizException("取消失败");
        }

        // 查询posCashPayment所有记录
        long paymentCnt = posCashPaymentManager.count(Wraps.<PosCashPayment>lbQ().eq(PosCashPayment::getCashId, posCash.getId()).eq(PosCashPayment::getDeleteFlag, '0'));
        if (0 == paymentCnt) {
            posCash.setPaid(new BigDecimal(0));
            posCash.setUnpaid(new BigDecimal(0));
            posCash.setUpdatedBy(ContextUtil.getUserId());
            posCash.setUpdatedTime(LocalDateTime.now());
            posCashManager.updateById(posCash);
        }
        return null;
    }

    @Override
    public String roundPrice(PosCashPageQuery queryVo) {
        PosCash posCash = posCashManager.getById(queryVo.getId());
        // 上一次金额
        if (null != posCash.getPayment()) {
            BigDecimal beforeMl = null == posCash.getRoundAmount() ? new BigDecimal(0) : posCash.getRoundAmount();
            posCash.setPayment(posCash.getPayment().add(beforeMl).subtract(queryVo.getRoundAmount()));
            posCash.setRoundAmount(queryVo.getRoundAmount());
            // 加优惠
            if (decimalNull(posCash.getDiscountAmount())) {
                posCash.setDiscountAmount(posCash.getDiscountAmount().subtract(beforeMl).add(queryVo.getRoundAmount()));
            } else {
                posCash.setDiscountAmount(queryVo.getRoundAmount());
            }
            // 减未支付
            if (decimalNull(posCash.getUnpaid())) {
                posCash.setUnpaid(posCash.getPayment().subtract(null == posCash.getPaid() ? new BigDecimal(0) : posCash.getPaid()));
            }
        } else {
            posCash.setRoundAmount(queryVo.getRoundAmount());
        }
        posCash.setUpdatedTime(LocalDateTime.now());
        posCash.setUpdatedBy(ContextUtil.getUserId());
        posCashManager.updateById(posCash);
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PosCashPayVo doBalancePay(PosCashPaymentSaveVO saveVO) {
        PosCashPayVo result = new PosCashPayVo();
        // 1. 当前支付payment
        // 2. 获取posCash
        PosCash posCash = posCashManager.getById(saveVO.getCashId());
        // 3. 获取用户信息
        if (null == posCash.getMemberId()) {
            result.setMsg("您不是会员！");
            result.setSuccess(false);
            return result;
        }
        MemberInfo memberInfo = memberInfoManager.getById(posCash.getMemberId());
        // 判断余额是否够减
        BigDecimal rechargeAmount = null == memberInfo.getRechargeAmount() ? new BigDecimal(0) : memberInfo.getRechargeAmount();
        BigDecimal giftAmount = null == memberInfo.getGiftAmount() ? new BigDecimal(0) : memberInfo.getGiftAmount();
        if (rechargeAmount.add(giftAmount).compareTo(saveVO.getAmount()) < 0) {
            result.setMsg("余额不足，当前余额" + rechargeAmount.add(giftAmount).toPlainString() + "元");
            result.setSuccess(false);
            return result;
        }

        // 4. 查询本次订单是否全部终止
        long paymentCnt = posCashPaymentManager.count(Wraps.<PosCashPayment>lbQ().eq(PosCashPayment::getCashId, saveVO.getCashId()).eq(PosCashPayment::getDeleteFlag, 0));
        boolean needUpdate = 0 == paymentCnt;
        // 5. 重新计算所有金额
        Map<String, Object> param = new HashMap<>();
        param.put("tableId", posCash.getTableId());
        param.put("cashId", posCash.getId());
        param.put("needUpdate", needUpdate);
        LinkedHashMap<String, Object> posResult = tablePosCash(param);

        PosCashAmountVO totalVo = (PosCashAmountVO) posResult.get("totalCash");

        // 插入payment表
        PosCashPayment payment = BeanUtil.copyProperties(saveVO, PosCashPayment.class);
        payment.setRemarks("余额支付" + payment.getAmount() + "元");
        payment.setPayTime(LocalDateTime.now());
        payment.setStatus("1");
        payment.setSn(ContextUtil.getSn());

        // 更新posCash
        if (null == posCash.getPaid()) {
            // 第一次支付
            // 金额
            posCash.setAmount(totalVo.getAmount());
            // 优惠金额
//            BigDecimal discount = null == totalVo.getDiscountAmount() ? new BigDecimal(0) : totalVo.getDiscountAmount();
            BigDecimal coupon = null == totalVo.getCouponAmount() ? new BigDecimal(0) : totalVo.getCouponAmount();
            // 是否抹零
            posCash.setDiscountAmount(coupon);
            // 支付金额
            posCash.setPayment(posCash.getAmount().subtract(posCash.getDiscountAmount()));
//            if (!nullDecimal(posCash.getRoundAmount())) {
//                posCash.setPayment(posCash.getPayment().subtract(posCash.getRoundAmount()));
//            }
            // 已付款金额
            posCash.setPaid(payment.getAmount());
            // 未付款金额
            // 判断是否全部支付完
            if (posCash.getPayment().compareTo(posCash.getPaid()) <= 0) {
                // 已支付完成
                posCash.setBillState("0");
                posCash.setUnpaid(new BigDecimal(0));
                result.setIsFinish(true);
            } else {
                // 计算未付款金额
                posCash.setUnpaid(posCash.getPayment().subtract(posCash.getPaid()));
                result.setIsFinish(false);
            }
            posCash.setPayName("现金");
        } else {
            // 之后的支付
            BigDecimal coupon = null == totalVo.getCouponAmount() ? new BigDecimal(0) : totalVo.getCouponAmount();
            // 金额
            posCash.setAmount(totalVo.getAmount());
            // 是否抹零
            posCash.setDiscountAmount(coupon);
            // 支付金额
            posCash.setPayment(posCash.getAmount().subtract(posCash.getDiscountAmount()));
            // 已付款金额
            posCash.setPaid(payment.getAmount().add(posCash.getPaid()));
            // 未付款金额
            // 判断是否全部支付完
            if (posCash.getPayment().compareTo(posCash.getPaid()) <= 0) {
                // 已支付完成
                posCash.setBillState("0");
                posCash.setUnpaid(new BigDecimal(0));
                result.setIsFinish(true);
            } else {
                // 计算未付款金额
                posCash.setUnpaid(posCash.getPayment().subtract(posCash.getPaid()));
                result.setIsFinish(false);
            }
        }
        // 更新
        posCash.setUpdatedTime(LocalDateTime.now());
        posCash.setUpdatedBy(ContextUtil.getUserId());
        boolean suc = posCashManager.updateById(posCash);
        if (!suc) {
            throw new BizException("支付失败，请稍后再试");
        }
        // 更新所有cash_table 的status 为1
        PosCashTable posCashTable = new PosCashTable();
        posCashTable.setStatus("1");
        posCashTable.setUpdatedTime(LocalDateTime.now());
        posCashTableManager.update(posCashTable, Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, posCash.getId()));
        // 更新用户余额
        // 先扣充值金额
        if (rechargeAmount.compareTo(saveVO.getAmount()) >= 0) {
            // 只扣充值
            memberInfo.setRechargeAmount(rechargeAmount.subtract(saveVO.getAmount()));
            payment.setRechargeAmount(saveVO.getAmount());
        } else {
            BigDecimal difAmount = saveVO.getAmount().subtract(memberInfo.getRechargeAmount());
            memberInfo.setGiftAmount(memberInfo.getGiftAmount().subtract(difAmount));
            payment.setRechargeAmount(memberInfo.getRechargeAmount());
            payment.setGiftAmount(difAmount);
            memberInfo.setRechargeAmount(new BigDecimal(0));

        }
        suc = posCashPaymentManager.save(payment);
        if (!suc) {
            throw new BizException("支付失败，请稍后再试");
        }
        suc = memberInfoManager.updateById(memberInfo);
        if (!suc) {
            throw new BizException("支付失败，请稍后再试");
        }
        // 全部支付完后 释放台桌
        if ("0".equals(posCash.getBillState())) {
            BaseTableInfo tableInfo = tableInfoManager.getById(posCash.getTableId());
            tableInfo.setTableStatus(TableStatus.UNUSED.getCode());
            tableInfo.setLightStatus("0");
            suc = tableInfoManager.updateById(tableInfo);
            if (!suc) {
                throw new BizException("台桌释放失败，请稍后再试");
            }
            // 计算各金额合计
            calPosCashTotal(posCash);
            // 不回滚 允许支付成功
            posCash.setUpdatedBy(ContextUtil.getUserId());
            posCash.setUpdatedTime(LocalDateTime.now());
            posCashManager.updateById(posCash);
        }
        // 全部支付完后 增加账户金额
        if ("0".equals(posCash.getBillState()) && ObjectUtil.equal(posCash.getType(), PosCashTypeEnum.RECHARGE.getCode())) {
            memberInfo = memberInfoManager.getById(posCash.getMemberId());
            memberInfo.setRechargeAmount(memberInfo.getRechargeAmount().add(ObjectUtil.isNotNull(posCash.getAmount()) ? posCash.getAmount() : BigDecimal.ZERO));
            memberInfo.setGiftAmount(memberInfo.getGiftAmount().add(ObjectUtil.isNotNull(posCash.getGiftAmount()) ? posCash.getGiftAmount() : BigDecimal.ZERO));
            memberInfo.setUpdatedTime(LocalDateTime.now());
            memberInfoManager.updateById(memberInfo);
        }
        // 全部支付完后 增加账户卡劵
        if ("0".equals(posCash.getBillState()) && ObjectUtil.equal(posCash.getType(), PosCashTypeEnum.CARD_COUPON.getCode())) {
            //发放卡
            List<PosCashEquity> posCashEquityList = posCashEquityManager.list(Wraps.<PosCashEquity>lbQ().eq(PosCashEquity::getCashId, posCash.getId()));
            Map<String, List<PosCashEquity>> equityTypeMap = posCashEquityList.stream().collect(Collectors.groupingBy(PosCashEquity::getType));
            //发放劵
            List<PosCashEquity> equityList = equityTypeMap.get("2");
            if (CollUtil.isNotEmpty(equityList)) {
                List<Long> couponIds = equityList.stream().map(PosCashEquity::getBizId).collect(Collectors.toList());
                Map<Long, BaseCouponInfo> couponInfoMap = couponInfoManager.list(Wraps.<BaseCouponInfo>lbQ().in(BaseCouponInfo::getId, couponIds)).stream().collect(Collectors.toMap(BaseCouponInfo::getId, k -> k));
                memberCouponManager.saveBatch(equityList.stream().map(v -> {
                    BaseCouponInfo couponInfo = couponInfoMap.get(v.getBizId());
                    return MemberCoupon.builder()
                            .memberId(posCash.getMemberId()).couponId(v.getBizId()).name(couponInfo.getName())
                            .createdOrgId(ContextUtil.getCurrentCompanyId()).type(couponInfo.getType()).status(MemberCouponStatusEnum.NO_USE.getCode())
                            .build();
                }).collect(Collectors.toList()));
            }
        }
        //保存收款单
        if (result.getIsFinish()) {
            suc = createBasePayment(posCash.getId(), BasePaymentTypeEnum.CONSUMPTION);
            if (!suc) {
                throw new BizException("收款单保存失败！");
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String removeBalancePay(PosCashPaymentPageQuery queryVo) {
        PosCashPayment posCashPayment = posCashPaymentManager.getById(queryVo.getId());
        BigDecimal amount = posCashPayment.getAmount();
        PosCash posCash = posCashManager.getById(queryVo.getCashId());
        // 已支付 - payment的金额
        posCash.setPaid(posCash.getPaid().subtract(amount));
        // 未支付
        posCash.setUnpaid(posCash.getUnpaid().add(amount));
        posCash.setUpdatedBy(ContextUtil.getUserId());
        posCash.setUpdatedTime(LocalDateTime.now());
        boolean suc = posCashManager.updateById(posCash);
        if (!suc) {
            return "取消失败";
        }
        // 删除poscashpayment
        BigDecimal rechargAmount = null == posCashPayment.getRechargeAmount() ? new BigDecimal(0) : posCashPayment.getRechargeAmount();
        BigDecimal giftAmount = null == posCashPayment.getGiftAmount() ? new BigDecimal(0) : posCashPayment.getGiftAmount();
        posCashPayment.setDeleteFlag(1);
        suc = posCashPaymentManager.removeById(posCashPayment);
        if (!suc) {
            throw new BizException("取消失败");
        }
        // 返还用户余额
        MemberInfo memberInfo = memberInfoManager.getById(posCash.getMemberId());
        memberInfo.setRechargeAmount(memberInfo.getRechargeAmount().add(rechargAmount));
        memberInfo.setGiftAmount(memberInfo.getGiftAmount().add(giftAmount));
        suc = memberInfoManager.updateById(memberInfo);
        if (!suc) {
            throw new BizException("取消失败");
        }
        return null;
    }

    @Override
    public PosCashPayment createPayment(PosCashPaymentSaveVO saveVO) {
        // 1. 获取posCash
        PosCash posCash = posCashManager.getById(saveVO.getCashId());

        // 4. 查询本次订单是否全部终止
        long paymentCnt = posCashPaymentManager.count(Wraps.<PosCashPayment>lbQ().eq(PosCashPayment::getCashId, saveVO.getCashId()).eq(PosCashPayment::getDeleteFlag, 0));
        boolean needUpdate = 0 == paymentCnt;
        // 5. 重新计算所有金额
        Map<String, Object> param = new HashMap<>();
        param.put("tableId", posCash.getTableId());
        param.put("cashId", posCash.getId());
        param.put("needUpdate", needUpdate);
        LinkedHashMap<String, Object> posResult = tablePosCash(param);
        PosCashAmountVO totalVo = (PosCashAmountVO) posResult.get("totalCash");
        // 插入payment表
        PosCashPayment payment = BeanUtil.copyProperties(saveVO, PosCashPayment.class);
        payment.setAmount(totalVo.getYsAmount());
        payment.setRemarks("扫码支付" + payment.getAmount() + "元");
        payment.setPayTime(LocalDateTime.now());
        payment.setStatus("0");
        payment.setSn(ContextUtil.getSn());
        boolean suc = posCashPaymentManager.save(payment);
        if (!suc) {
            return null;
        }
        return payment;
    }


    @Override
    public long count(LbQueryWrap<PosCash> eq) {
        return superManager.count(eq);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(PosCash posCash) {
        return superManager.updateById(posCash);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(PosCash posCash) {
        if (StringUtils.isBlank(posCash.getSn())) {
            posCash.setSn(ContextUtil.getSn());
        }
        return superManager.save(posCash);
    }

    @Override
    public Boolean checkMemberIsUse(List<Long> longs) {
        return posCashManager.count(Wraps.<PosCash>lbQ()
                .isNotNull(PosCash::getMemberId)
                .in(PosCash::getMemberId, longs)
                .ne(PosCash::getType, PosCashTypeEnum.RECHARGE.getCode())
                .notIn(PosCash::getBillType, Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                        , PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .notIn(PosCash::getBillState,
                        Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode()
                                , PosCashBillStateEnum.REFUNDED.getCode()
                                , PosCashBillStateEnum.PART_REFUND.getCode()))) > 0;
    }

    @Override
    public Boolean checkStatusByMemberCardId(Long memberCardId) {
        // 查询未完成的订单
        List<PosCashPayment> cashPaymentList = posCashPaymentManager.list(Wraps.<PosCashPayment>lbQ()
                .eq(PosCashPayment::getMemberCardId, memberCardId).eq(SuperEntity::getDeleteFlag, 0));
        if (CollUtil.isEmpty(cashPaymentList)) {
            return false;
        }
        List<Long> cashIds = cashPaymentList.stream().map(PosCashPayment::getCashId).distinct().collect(Collectors.toList());
        List<PosCash> posCashList = list(Wraps.<PosCash>lbQ().eq(PosCash::getDeleteFlag, 0)
                .in(PosCash::getId, cashIds)
                .notIn(PosCash::getBillState, Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(),
                        PosCashBillStateEnum.REFUNDED.getCode())));
        return CollUtil.isNotEmpty(posCashList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchById(List<PosCash> posCashList) {
        return superManager.updateBatchById(posCashList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
        return superManager.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(LambdaUpdateWrapper<PosCash> wrapper) {
        return superManager.update(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delOrder(PosCashDelOrderQuery vo) {
        String dateTime = top.kx.basic.utils.DateUtils.format(top.kx.basic.utils.DateUtils.getEndTime(vo.getEndDate()),
                top.kx.basic.utils.DateUtils.DEFAULT_DATE_TIME_FORMAT);
        //台费
        posCashTableManager.update(Wraps.<PosCashTable>lbU()
                .set(PosCashTable::getDeleteFlag, 1)
                .inSql(PosCashTable::getCashId, "select * from pos_cash " +
                        "where delete_flag = 0  and bill_state in('2','5','6') and complete_time is not null" +
                        " and complete_time <= '" + dateTime + "'"));
        //套餐
        posCashThailManager.update(Wraps.<PosCashThail>lbU()
                .set(PosCashThail::getDeleteFlag, 1)
                .inSql(PosCashThail::getCashId, "select id from pos_cash " +
                        "where delete_flag = 0  and bill_state in('2','5','6') and complete_time is not null" +
                        " and complete_time <= '" + dateTime + "'"));

        //商品
        productManager.update(Wraps.<PosCashProduct>lbU()
                .set(PosCashProduct::getDeleteFlag, 1)
                .inSql(PosCashProduct::getCashId, "select id from pos_cash " +
                        "where delete_flag = 0  and bill_state in('2','5','6') and complete_time is not null" +
                        " and complete_time <= '" + dateTime + "'"));
        //服务
        serviceManager.update(Wraps.<PosCashService>lbU()
                .set(PosCashService::getDeleteFlag, 1)
                .inSql(PosCashService::getCashId, "select id from pos_cash " +
                        "where delete_flag = 0  and bill_state in('2','5','6') and complete_time is not null" +
                        " and complete_time <= '" + dateTime + "'"));
        //权益
        posCashEquityManager.update(Wraps.<PosCashEquity>lbU()
                .set(PosCashEquity::getDeleteFlag, 1)
                .inSql(PosCashEquity::getCashId, "select id from pos_cash " +
                        "where delete_flag = 0  and bill_state in('2','5','6') and complete_time is not null" +
                        " and complete_time <= '" + dateTime + "'"));
        //支付明细
        posCashPaymentManager.update(Wraps.<PosCashPayment>lbU()
                .set(PosCashPayment::getDeleteFlag, 1)
                .inSql(PosCashPayment::getCashId, "select id from pos_cash " +
                        "where delete_flag = 0  and bill_state in('2','5','6') and complete_time is not null" +
                        " and complete_time <= '" + dateTime + "'"));
        //支付明细
        posCashPaymentManager.update(Wraps.<PosCashPayment>lbU()
                .set(PosCashPayment::getDeleteFlag, 1)
                .inSql(PosCashPayment::getCashId, "select id from pos_cash " +
                        "where delete_flag = 0  and bill_state in('2','5','6') and complete_time is not null" +
                        " and complete_time <= '" + dateTime + "'"));

        //支付退款明细
        posCashRefundPaymentManager.update(Wraps.<PosCashRefundPayment>lbU()
                .set(PosCashRefundPayment::getDeleteFlag, 1)
                .inSql(PosCashRefundPayment::getCashId, "select id from pos_cash " +
                        "where delete_flag = 0  and bill_state in('2','5','6') and complete_time is not null" +
                        " and complete_time <= '" + dateTime + "'"));
        //优惠明细
        posCashDiscountDetailManager.update(Wraps.<PosCashDiscountDetail>lbU()
                .set(PosCashDiscountDetail::getDeleteFlag, 1)
                .inSql(PosCashDiscountDetail::getPosCashId, "select id from pos_cash " +
                        "where delete_flag = 0  and bill_state in('2','5','6') and complete_time is not null" +
                        " and complete_time <= '" + dateTime + "'"));
        //日志明细
        baseBizLogManager.update(Wraps.<BaseBizLog>lbU()
                .set(BaseBizLog::getDeleteFlag, 1)
                .inSql(BaseBizLog::getSourceId, "select id from pos_cash " +
                        "where delete_flag = 0  and bill_state in('2','5','6') and complete_time is not null" +
                        " and complete_time <= '" + dateTime + "'"));

        //订单信息
        return posCashManager.update(Wraps.<PosCash>lbU().set(PosCash::getDeleteFlag, 1)
                .in(PosCash::getBillState, Arrays.asList(
                        PosCashBillStateEnum.REFUNDED.getCode(),
                        PosCashBillStateEnum.REFUNDED.getCode(),
                        PosCashBillStateEnum.COMPLETE.getCode()))
                .isNotNull(PosCash::getCompleteTime)
                .le(PosCash::getCompleteTime,
                        dateTime));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PosCashPayVo savePayResult(PayResponseVO saveVO) {
        PosCashPayVo result = new PosCashPayVo();
        String cashId = saveVO.getBusinessId();
        String paymentId = saveVO.getOrderId();
        PosCashPayment posCashPayment = posCashPaymentManager.getById(Long.parseLong(paymentId));

        // start ======================= 以下代码为 还未申请商户时使用  默认支付成功  TODO
        posCashPayment.setStatus("1");
        // 更新poscash
        PosCash posCash = posCashManager.getById(cashId);
        posCash.setUnpaid(new BigDecimal(0));
        posCash.setPaid(posCash.getPayment());
        posCash.setBillState("0");
        // 计算各金额合计
        calPosCashTotal(posCash);
        // 更新posCash
        posCash.setUpdatedTime(LocalDateTime.now());
        posCash.setUpdatedBy(ContextUtil.getUserId());
        boolean suc = posCashManager.updateById(posCash);
        if (!suc) {
            throw new BizException("订单更新失败");
        }
        // 释放台桌
        BaseTableInfo tableInfo = tableInfoManager.getById(posCash.getTableId());
        tableInfo.setTableStatus(TableStatus.UNUSED.getCode());
        tableInfo.setLightStatus("0");
        tableInfo.setUpdatedTime(LocalDateTime.now());
        suc = tableInfoManager.updateById(tableInfo);
        if (!suc) {
            throw new BizException("台桌释放失败，请稍后再试");
        } else {
            result.setSuccess(true);
            return result;
        }
        // end ======================= 以上代码为 还未申请商户时使用  默认支付成功  TODO

        // start ======================= 以下代码为商户申请下来后使用 TODO
//        if ("T00".equals(saveVO.getResultCode())) {
//            // 调用成功
//            // 更新payment
//            posCashPayment.setStatus("1");
//            suc = posCashPaymentManager.updateById(posCashPayment);
//            if (!suc) {
//                result.setMsg("订单更新失败");
//                result.setSuccess(false);
//                return result;
//            }
//            // 更新poscash
//            posCash = posCashManager.getById(cashId);
//            posCash.setUnpaid(new BigDecimal(0));
//            posCash.setPaid(posCash.getPayment());
//            posCash.setBillState("0");
//            // 计算各金额合计
//            calPosCashTotal(posCash);
//            // 更新posCash
//            suc = posCashManager.updateById(posCash);
//            if (!suc) {
//                throw new BizException("订单更新失败");
//            }
//            // 释放台桌
//            tableInfo = tableInfoManager.getById(posCash.getTableId());
//            tableInfo.setTableStatus("10");
//            suc = tableInfoManager.updateById(tableInfo);
//            if (!suc) {
//                throw new BizException("台桌释放失败，请稍后再试");
//            }
//        } else {
//            // 调用失败
//            posCashPayment.setStatus("2");
//            posCashPaymentManager.updateById(posCashPayment);
//            result.setMsg(saveVO.getResultMsg());
//            result.setSuccess(false);
//            return result;
//        }
//        // 回调信息入库
//        PosCashPayResult posCashPayResult = BeanUtil.copyProperties(saveVO, PosCashPayResult.class);
//        posCashPayResultManager.save(posCashPayResult);
        // end ======================= 以上代码为商户申请下来后使用 TODO
//        return null;
    }

    private void calPosCashTotal(PosCash posCash) {
        // 获取所有台桌
        List<PosCashTable> posCashTables = posCashTableManager.list(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, posCash.getId()).eq(PosCashTable::getDeleteFlag, "0"));
        // 获取所有服务
        List<PosCashService> posCashServices = serviceManager.list(Wraps.<PosCashService>lbQ().eq(PosCashService::getCashId, posCash.getId()).eq(PosCashService::getDeleteFlag, "0"));
        // 获取所有商品
        List<PosCashProduct> products = productManager.list(Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getCashId, posCash.getId()).eq(PosCashProduct::getDeleteFlag, "0"));
        // 获取所有套餐
        List<PosCashThail> thails = posCashThailManager.list(Wraps.<PosCashThail>lbQ().eq(PosCashThail::getCashId, posCash.getId()).eq(PosCashThail::getDeleteFlag, "0"));

        BigDecimal amount = new BigDecimal(0);

        if (null != posCashTables) {
            for (PosCashTable vo : posCashTables) {
                amount = amount.add(vo.getAmount());
            }
        }
        posCash.setTableAmount(amount);
        amount = new BigDecimal(0);
        if (null != posCashServices) {
            for (PosCashService vo : posCashServices) {
                amount = amount.add(vo.getAmount());
            }
        }
        posCash.setServiceAmount(amount);
        amount = new BigDecimal(0);
        if (null != products) {
            for (PosCashProduct vo : products) {
                amount = amount.add(vo.getAmount());
            }
        }
        posCash.setProductAmount(amount);
        amount = new BigDecimal(0);
        if (null != thails) {
            for (PosCashThail vo : thails) {
                amount = amount.add(vo.getAmount());
            }
        }
        posCash.setThailAmount(amount);
    }

    private boolean decimalNull(BigDecimal amount) {
        return null != amount && amount.compareTo(new BigDecimal(0)) != 0;
    }

    /*************************************** 计算台桌费用（包含会员折扣，优惠券） *******************************************************/
    public PosCashAmountVO tableCash(Long tableId, Long cashId, boolean needUpdate) {
        if (tableId == null) {
            PosCashAmountVO vo = new PosCashAmountVO();
            vo.setAmount(BigDecimal.ZERO);
            vo.setDiscountAmount(BigDecimal.ZERO);
            vo.setResultAmount(BigDecimal.ZERO);
            return vo;
        }
        // 台桌信息
        BaseTableInfo tableInfo = tableInfoManager.getById(tableId);
        // 台桌计费规则
        BaseTableCharging tableCharging = baseTableChargingManager.getOne(Wraps.<BaseTableCharging>lbQ()
                .eq(BaseTableCharging::getTableType, tableInfo.getTableType()));

        // 台桌信息与台桌计费标准
        List<PosCashTable> posCashTables = tableManager.list(Wraps.<PosCashTable>lbQ()
                .eq(PosCashTable::getTableId, tableId)
                .eq(PosCashTable::getCashId, cashId)
                .orderByDesc(PosCashTable::getCreatedTime));

        // 除最后一个时段内的费用总计
        BigDecimal amountByNoLast = new BigDecimal(0);
        for (int i = 1; i < posCashTables.size(); i++) {
            amountByNoLast = amountByNoLast.add(posCashTables.get(i).getAmount()).setScale(2, RoundingMode.UP);
        }

        // 台桌最后一个时段内的持续时间和费用
        LocalDateTime localEndTime = LocalDateTime.now();
        if (null != posCashTables.get(0).getEndTime()) {
            localEndTime = posCashTables.get(0).getEndTime();
        }

        long lastRnage = DateUtils.calDifMinutes(posCashTables.get(0).getStartTime(), localEndTime);
        BigDecimal amountByLast = new BigDecimal(0);
        BigDecimal amount = new BigDecimal(0);
        if (0 != lastRnage) {
            amountByLast = calculateBizService.overTimeCash(posCashTables.get(0).getPrice(), lastRnage, tableCharging.getPeriod(), tableCharging.getOvertime());
            amount = amountByLast;
            if (!"".equals(posCashTables.get(0).getType())) {
                if ("6".equals(posCashTables.get(0).getType())) {
                    long dicountTime = posCashTables.get(0).getDiscount().setScale(0, RoundingMode.DOWN).longValue();
                    amount = calculateBizService.orginPriceCountAmountByType6(posCashTables.get(0).getPrice(), posCashTables.get(0).getDuration().longValue(), dicountTime, tableCharging.getPeriod(), tableCharging.getOvertime());
                } else {
                    amount = calculateBizService.orginPriceCountAmount(amountByLast, posCashTables.get(0).getType(), posCashTables.get(0).getDiscount());
                }
            }
        }


        BigDecimal tableAmount = amountByNoLast.add(amount);

        // 关于优惠券先注掉
//        BigDecimal amountByLast;
//        BigDecimal tableAmount;
//        BigDecimal tableResultAmount;
//        BigDecimal amountByNoLast = new BigDecimal(0);
//
//        for (int i = 1; i < posCashTables.size(); i++) {
//            amountByNoLast = amountByNoLast.add(posCashTables.get(i).getAmount()).setScale(2, RoundingMode.UP);
//        }
//        if (0 != coupons.size()) {
//            // 选择优惠券，根据原价计算总价
//            if (null != posCashTables.get(0).getEndTime()) {
//                amountByLast = posCashTables.get(0).getAmount();
//            } else {
//                amountByLast = overTimeCash(posCashTables.get(0).getOrginPrice(), lastRnage, tableCharging.getPeriod(), tableCharging.getOvertime());
//            }
//            tableAmount = amountByNoLast.add(amountByLast);
//            tableResultAmount = tableAmount;
//        } else {
//            if (null != posCashTables.get(0).getEndTime()) {
//                amountByLast = posCashTables.get(0).getAmount();
//            } else {
//                amountByLast = overTimeCash(posCashTables.get(0).getPrice(), lastRnage, tableCharging.getPeriod(), tableCharging.getOvertime());
//            }
//            tableAmount = amountByNoLast.add(amountByLast);
//
//            // 未选优惠券，才使用会员折扣
//            boolean vip = (boolean) vipMap.get("isVip");
//
//            // 计算折扣的价格
//            if (vip) {
//                // 根据会员信息查询会员折扣力度
//                BigDecimal tableDiscount = (BigDecimal) vipMap.get("tableDiscount");
//                tableResultAmount = tableAmount.multiply(tableDiscount).setScale(2, RoundingMode.UP);
//            } else {
//                tableResultAmount = tableAmount;
//            }
//        }

        // 更新pos_cash_table的根据cashId查询列表的最新一条
        PosCashTable lastPosCashTable = posCashTables.get(0);
        lastPosCashTable.setDuration((int) lastRnage);
        lastPosCashTable.setEndTime(localEndTime);
        lastPosCashTable.setOrginPrice(amountByLast);
        lastPosCashTable.setAmount(amount);
        lastPosCashTable.setUpdatedTime(LocalDateTime.now());
        lastPosCashTable.setUpdatedBy(ContextUtil.getEmployeeId());
        if (needUpdate) {
            tableManager.updateById(lastPosCashTable);
        }

//        BigDecimal discountAmount = tableAmount.subtract(tableResultAmount);
        BigDecimal discountAmount = new BigDecimal(0);

        PosCashAmountVO vo = new PosCashAmountVO();
        vo.setAmount(tableAmount);
        vo.setDiscountAmount(discountAmount);
        vo.setResultAmount(tableAmount);

        return vo;
    }

    /*************************************** 计算台桌服务费用（包含会员折扣，优惠券） *******************************************************/
    public PosCashAmountVO serviceCash(Long cashId, boolean needUpdate) {
        // 获取会员信息

        List<BaseService> services = baseServiceManager.list(Wraps.lbQ());
        Map<Long, BaseService> serviceMap = services.stream().collect(Collectors.toMap(BaseService::getId, e -> e));

        List<PosCashService> posCashServiceLasts = serviceManager.list(Wraps.<PosCashService>lbQ()
                .eq(PosCashService::getCashId, cashId)
                .isNull(PosCashService::getEndTime));

        List<PosCashService> posCashServices = serviceManager.list(Wraps.<PosCashService>lbQ()
                .eq(PosCashService::getCashId, cashId)
                .isNotNull(PosCashService::getEndTime));

        BigDecimal lastTotal = new BigDecimal(0);
        List<PosCashService> pcsNewList = new ArrayList<>();
        for (PosCashService posCashService : posCashServiceLasts) {
//            if (null == serviceMap.get(posCashService.getId()).getDuration()) {
            // 服务最后一个时段内的持续时间
            LocalDateTime localEndTime = LocalDateTime.now();
            long range = DateUtils.calDifMinutes(posCashService.getStartTime(), localEndTime);

            // 服务最后一个时间段的总费
            BigDecimal lastServiceCash = calculateBizService.overTimeCash(posCashService.getPrice(), range, serviceMap.get(posCashService.getServiceId()).getBillingCycle(), serviceMap.get(posCashService.getServiceId()).getTimeoutPeriod());
            BigDecimal amount = lastServiceCash;
            if (!"".equals(posCashService.getType())) {
                if ("6".equals(posCashService.getType())) {
                    long dicountTime = posCashService.getDiscount().setScale(0, RoundingMode.DOWN).longValue();
                    amount = calculateBizService.orginPriceCountAmountByType6(posCashService.getPrice(), posCashService.getDuration() != null ? posCashService.getDuration().longValue() : 0L, dicountTime, serviceMap.get(posCashService.getServiceId()).getBillingCycle(), serviceMap.get(posCashService.getServiceId()).getTimeoutPeriod());
                } else {
                    amount = calculateBizService.orginPriceCountAmount(lastServiceCash, posCashService.getType(), posCashService.getDiscount());
                }
            }

            if (0 == range) {
                amount = new BigDecimal(0);
                lastServiceCash = new BigDecimal(0);
            }
            PosCashService pcs = new PosCashService();
            BeanUtils.copyProperties(posCashService, pcs);
            pcs.setDuration((int) range);
            pcs.setEndTime(localEndTime);
            pcs.setOrginPrice(lastServiceCash);
            pcs.setAmount(amount);
            pcs.setUpdatedTime(LocalDateTime.now());
            pcs.setUpdatedBy(ContextUtil.getEmployeeId());
            pcsNewList.add(pcs);

            lastTotal = lastTotal.add(amount);
//            } else {
//                // 此服务为计次服务，直接用amount相加
//                lastTotal = lastTotal.add(posCashService.getAmount());
//            }
        }

        if (!pcsNewList.isEmpty() && needUpdate) {
            serviceManager.updateBatchById(pcsNewList);
        }

        BigDecimal amountByNoLast = BigDecimal.ZERO;
        for (PosCashService posCashService : posCashServices) {
            amountByNoLast = amountByNoLast.add(posCashService.getAmount()).setScale(2, RoundingMode.UP);
        }

        // 总价
        BigDecimal serviceAmount = amountByNoLast.add(lastTotal);

        BigDecimal discountAmount = new BigDecimal(0);

        PosCashAmountVO vo = new PosCashAmountVO();
        vo.setAmount(serviceAmount);
        vo.setDiscountAmount(discountAmount);
        vo.setResultAmount(serviceAmount);

        return vo;
    }

    /*************************************** 计算台桌商品费用（包含会员折扣，优惠券） *******************************************************/
    public PosCashAmountVO productCash(Long cashId) {

        List<PosCashProduct> posCashProducts = productManager.list(Wraps.<PosCashProduct>lbQ()
                .eq(PosCashProduct::getCashId, cashId)
                .eq(PosCashProduct::getIsGift, 0));

        BigDecimal productAmount = new BigDecimal(0);
        for (PosCashProduct product : posCashProducts) {
            productAmount = productAmount.add(product.getAmount()).setScale(2, RoundingMode.UP);
        }

        BigDecimal productResultAmount;
        BigDecimal discountAmount = new BigDecimal(0);
        // 获取会员信息
        productResultAmount = productAmount.subtract(discountAmount);

        PosCashAmountVO vo = new PosCashAmountVO();
        vo.setAmount(productAmount);
        vo.setDiscountAmount(discountAmount);
        vo.setResultAmount(productResultAmount);
        return vo;
    }

    /*************************************** 根据会员信息查询优惠券 *******************************************************/
    public List<CouponVo> coupons(int type, Long memberId) {
        List<BaseCouponInfo> couponInfos = baseCouponInfoManager.list(Wraps.lbQ());
        Map<Long, BaseCouponInfo> couponInfoMap = couponInfos.stream().collect(Collectors.toMap(BaseCouponInfo::getId, e -> e));

        List<MemberCoupon> memberCoupons = memberCouponManager.list(Wraps.<MemberCoupon>lbQ()
                .eq(MemberCoupon::getMemberId, memberId)
                .eq(MemberCoupon::getStatus, type)
                .orderByDesc(MemberCoupon::getUpdatedTime)
                .orderByDesc(MemberCoupon::getCreatedTime));

        List<CouponVo> coupons = new ArrayList<>();
        for (MemberCoupon coupon : memberCoupons) {
            LocalDateTime localDateTime = LocalDateTime.now();
            if (1 == type && localDateTime.isAfter(coupon.getExpiresTime())) {
                coupon.setStatus("3");
                coupon.setUpdatedTime(LocalDateTime.now());
                coupon.setUpdatedBy(ContextUtil.getEmployeeId());
                memberCouponManager.updateById(coupon);
            } else {
                CouponVo vo = new CouponVo();
                vo.setId(coupon.getCouponId());
                vo.setName(coupon.getName());
                vo.setType(Integer.valueOf(couponInfoMap.get(coupon.getCouponId()).getType()));
                vo.setOriginalPrice(couponInfoMap.get(coupon.getCouponId()).getOriginalPrice());
                vo.setUsedMethod(!"0".equals(couponInfoMap.get(coupon.getCouponId()).getUsedMethod()));
                vo.setUsedLimited(couponInfoMap.get(coupon.getCouponId()).getUsedLimited());
                vo.setUsedRepeat(!"0".equals(couponInfoMap.get(coupon.getCouponId()).getUsedRepeat()));
                vo.setUsableRange(couponInfoMap.get(coupon.getCouponId()).getUsableRange());
                vo.setExpiresTime(coupon.getExpiresTime());
                vo.setMemberCouponId(coupon.getId());
                coupons.add(vo);
            }
        }

        return coupons;
    }

    /*************************************** 优惠券使用 *******************************************************/
    private List<CouponVo> getUseingCoupons(Long cashId) {
        List<BaseCouponInfo> couponInfos = baseCouponInfoManager.list(Wraps.lbQ());
        Map<Long, BaseCouponInfo> couponInfoMap = couponInfos.stream().collect(Collectors.toMap(BaseCouponInfo::getId, e -> e));
        List<MemberCoupon> memberCoupons = appMemberCouponMapper.getUseingMemberCoupon(cashId);
        List<CouponVo> coupons = new ArrayList<>();
        for (MemberCoupon coupon : memberCoupons) {
            CouponVo vo = new CouponVo();
            vo.setId(coupon.getCouponId());
            vo.setName(coupon.getName());
            vo.setType(Integer.valueOf(couponInfoMap.get(coupon.getCouponId()).getType()));
            vo.setOriginalPrice(couponInfoMap.get(coupon.getCouponId()).getOriginalPrice());
            vo.setUsedMethod(!"0".equals(couponInfoMap.get(coupon.getCouponId()).getUsedMethod()));
            vo.setUsedLimited(couponInfoMap.get(coupon.getCouponId()).getUsedLimited());
            vo.setUsedRepeat(!"0".equals(couponInfoMap.get(coupon.getCouponId()).getUsedRepeat()));
            vo.setUsableRange(couponInfoMap.get(coupon.getCouponId()).getUsableRange());
            vo.setExpiresTime(coupon.getExpiresTime());
            vo.setMemberCouponId(coupon.getId());
            coupons.add(vo);
        }
        return coupons;
    }


    /*************************************** 计算充值费用（充值） *******************************************************/
    public PosCashAmountVO depositCash(Long cashId) {
        PosCash posCash = posCashManager.getById(cashId);
        BigDecimal amount = BigDecimal.ZERO;
        if (ObjectUtil.equal(posCash.getType(), PosCashTypeEnum.RECHARGE.getCode()) && ObjectUtil.isNotNull(posCash.getAmount())) {
            amount = posCash.getAmount();
        }
        BigDecimal discountAmount = BigDecimal.ZERO;
        BigDecimal resultAmount = amount.subtract(discountAmount);
        PosCashAmountVO vo = new PosCashAmountVO();
        vo.setAmount(amount);
        vo.setDiscountAmount(discountAmount);
        vo.setResultAmount(resultAmount);
        return vo;
    }

    @Override
    public PosCash getOne(LbQueryWrap<PosCash> eq) {
        return superManager.getOne(eq);
    }

    @Override
    public List<PosCashResultVO> queryListByCardId(Long memberCardId) {
        List<PosCashPayment> cashPaymentList = posCashPaymentManager.list(Wraps.<PosCashPayment>lbQ()
                .eq(PosCashPayment::getDeleteFlag, 0).eq(PosCashPayment::getMemberCardId, memberCardId)
                .eq(PosCashPayment::getStatus, PosCashPaymentStatusEnum.PAY_SUCCESS.getCode()));
        if (CollUtil.isEmpty(cashPaymentList)) {
            return new ArrayList<>();
        }
        List<PosCash> posCashList = list(Wraps.<PosCash>lbQ().eq(SuperEntity::getDeleteFlag, 0)
                .in(SuperEntity::getId, cashPaymentList.stream().map(PosCashPayment::getCashId).collect(Collectors.toList()))
                .orderByDesc(SuperEntity::getCreatedTime));
        return BeanPlusUtil.toBeanList(posCashList, PosCashResultVO.class);
    }

    @Override
    public List<ServiceTableResultVO> selectPosCashByEmployeeId(List<Long> employeeIdList) {
        return superManager.selectPosCashByEmployeeId(employeeIdList);
    }

    @Override
    public List<ServiceTableResultVO> selectCashByEmployeeId(QueryWrapper<PosCash> wrapper) {
        return superManager.selectCashByEmployeeId(wrapper);
    }

    @Override
    public List<ServiceTableResultVO> selectPosCashByEmployeeIdAndServiceId(List<Long> employeeIdList) {
        return superManager.selectPosCashByEmployeeIdAndServiceId(employeeIdList);
    }

    @Override
    public IPage<PosCashResultVO> findPageResultVO(PageParams<PosCashDetailsQuery> params) {
        IPage<PosCash> page = params.buildPage(PosCash.class);
        PosCashDetailsQuery model = params.getModel();
        return superManager.selectPageResultVO(page, initWarp(model));
    }

    @Override
    public List<PosCashResultVO> findAllResultVO(PosCashDetailsQuery params) {
        return superManager.selectAllResultVO(initWarp(params));
    }

    private QueryWrap<PosCash> initWarp(PosCashDetailsQuery model) {
        QueryWrap<PosCash> wrap = new QueryWrap<>();
        wrap.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode()
                        , PosCashBillStateEnum.PART_REFUND.getCode(), PosCashBillStateEnum.REFUNDED.getCode()))
                .in(StringUtils.isNotBlank(model.getType()), "p.type_", model.getType())
                .in(CollUtil.isNotEmpty(model.getTypeList()), "p.type_", model.getTypeList())
                .eq(StringUtils.isNotBlank(model.getOrderSource()), "p.order_source", model.getOrderSource())
                .like(StringUtils.isNotBlank(model.getTableName()), "p.table_name", model.getTableName())
                .like(StringUtils.isNotBlank(model.getKeyword()), "p.code", model.getKeyword())
                .like(StringUtils.isNotBlank(model.getCode()), "p.code", model.getCode())
                .eq(ObjectUtil.isNotNull(model.getEmployeeId()), "p.employee_id", model.getEmployeeId())
                // 台桌类型小程序
                .in("p.bill_type", Arrays.asList('0', '3', '4'))
                .eq("p.delete_flag", 0)
                .in(CollUtil.isNotEmpty(model.getOrgIdList()), "p.org_id", model.getOrgIdList())
                .between(StringUtils.isNotBlank(model.getStartDate())
                        && StringUtils.isNotBlank(model.getEndDate()), "p.complete_time", model.getStartDate(), model.getEndDate())
                .between(StringUtils.isNotBlank(model.getCompleteTime_st())
                        && StringUtils.isNotBlank(model.getCompleteTime_ed()), "p.complete_time", model.getCompleteTime_st(), model.getCompleteTime_ed())
                .and(StringUtils.isNotBlank(model.getStartBillDate()) && StringUtils.isNotBlank(model.getEndBillDate()), w ->
                        w.between("p.bill_date", DateUtil.beginOfDay(DateUtil.parse(model.getStartBillDate())).toLocalDateTime(), DateUtil.endOfDay(DateUtil.parse(model.getEndBillDate())).toLocalDateTime())
                                .or()
                        .between("p.refund_time",DateUtil.beginOfDay(DateUtil.parse(model.getStartBillDate())).toLocalDateTime(), DateUtil.endOfDay(DateUtil.parse(model.getStartBillDate())).toLocalDateTime()))
                .eq(ObjectUtil.isNotNull(model.getCommenter()), "c.employee_id", model.getCommenter())
                .eq(ObjectUtil.isNotNull(model.getCommenter()), "c.delete_flag", 0)
                .eq(StringUtils.isNotBlank(model.getTableType()) || StringUtils.isNotBlank(model.getTableArea()),
                        "t.delete_flag", 0)
                .eq(StringUtils.isNotBlank(model.getTableType()), "t.table_type", model.getTableType())
                .eq(StringUtils.isNotBlank(model.getTableArea()), "t.table_area", model.getTableArea())
                .eq(StringUtils.isNotBlank(model.getMemberName()), "m.delete_flag", 0)
                .and(StringUtils.isNotBlank(model.getMemberName()), e -> e.like("m.name", model.getMemberName())
                        .or()
                        .like("m.mobile", model.getMemberName())
                        .or()
                        .like("m.code", model.getMemberName()))
        ;
        //.apply(StringUtils.isNotBlank(model.getMemberName()), "instr(m.name, '" + model.getMemberName() + "')");
        wrap.orderByDesc("p.complete_time");
        return wrap;
    }

    @Override
    public String mergeRemarks(String targetRemarks, String sourceRemarks) {
        if (StrUtil.isBlank(sourceRemarks)) {
            return targetRemarks;
        }
        if (StrUtil.isBlank(targetRemarks)) {
            return sourceRemarks;
        }
        JSONObject obj1 = JSON.parseObject(targetRemarks);
        JSONObject obj2 = JSON.parseObject(sourceRemarks);
        // 创建一个新的对象用于存储结果
        JSONObject result = mergeJson(obj1, obj2);
        // 合并后的 JSON
        return JSON.toJSONString(result, true);
    }

    @Override
    public List<PosCash> selectPosCashWithConditions() {
        return superManager.selectPosCashWithConditions();
    }

    private static JSONObject mergeJson(JSONObject obj1, JSONObject obj2) {
        // 创建一个新的对象用于存储结果
        JSONObject merged = new JSONObject();

        // 合并 obj1 中的内容
        for (String key : obj1.keySet()) {
            merged.put(key, obj1.get(key));
        }

        // 合并 obj2 中的内容
        for (String key : obj2.keySet()) {
            // 如果 obj1 中也存在该键，则进行特殊处理
            if (merged.containsKey(key)) {
                Object value1 = merged.get(key);
                Object value2 = obj2.get(key);

                // 如果都是数组，则合并数组
                if (value1 instanceof List && value2 instanceof List) {
                    List<Object> list1 = (List<Object>) value1;
                    List<Object> list2 = (List<Object>) value2;
                    List<Object> newList = new ArrayList<>(list1);
                    newList.addAll(list2);
                    merged.put(key, newList);
                } else if (value1 instanceof String && value2 instanceof String) {
                    String string1 = (String) value1;
                    String string2 = (String) value2;
                    String newString = string1.concat(",").concat(string2);
                    String collect = Arrays.stream(newString.split(",")).distinct().collect(Collectors.joining(","));
                    // 覆盖模式
                    merged.put(key, collect);
                } else {
                    // 覆盖模式
                    merged.put(key, value2);
                }
            } else {
                // 直接添加到结果中
                merged.put(key, obj2.get(key));
            }
        }

        return merged;
    }


    @Override
    public String serviceDurationDesc(Integer duration, ServiceStaffTimeEnum defaultServiceStaffTime) {
        if (Objects.isNull(duration)) {
            return null;
        }
        if (Objects.isNull(defaultServiceStaffTime)) {
            return duration + "分钟";
        }
        switch (defaultServiceStaffTime) {
            case HOUR:
                return String.format("%.2f h", (duration / 60.0));
            case HOUR_MINUTE:
                return duration > 60 ? (duration % 60 == 0 ? StrUtil.format("{}小时", duration / 60) : StrUtil.format("{}小时{}分钟", duration / 60, duration % 60)) : StrUtil.format("{}分钟", duration);
            default:
                return duration + "分钟";
        }
    }

    @Override
    public String serviceDurationExportDesc(Integer duration, ServiceStaffTimeEnum defaultServiceStaffTime) {
        if (Objects.isNull(duration)) {
            return null;
        }
        if (Objects.isNull(defaultServiceStaffTime)) {
            return duration.toString();
        }
        switch (defaultServiceStaffTime) {
            case HOUR:
                return BigDecimal.valueOf(duration).divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP).toString();
            case HOUR_MINUTE:
                return duration > 60 ? (duration % 60 == 0 ? StrUtil.format("{}小时", duration / 60) : StrUtil.format("{}小时{}分钟", duration / 60, duration % 60)) : StrUtil.format("{}分钟", duration);
            default:
                return duration.toString();
        }
    }

    @Override
    public String serviceDurationDesc(String duration) {
        if (StringUtils.isBlank(duration)) {
            return null;
        }
        if (duration.endsWith(" h")) {
            return duration.replace(" h", "");
        }
        if (duration.endsWith("分钟")) {
            return duration.replace("分钟", "");
        }
        return duration;
    }

    @Override
    public PosCash getAnyOne(Long id) {
        return posCashManager.queryOne(id);
    }

    @Override
    public List<PosCash> getAnyList(List<Long> ids) {
        return posCashManager.getAnyList(ids);
    }

    @Override
    public Boolean updateCompleteEmp(Long id, Long completeEmp) {
        return posCashManager.update(Wraps.<PosCash>lbU().set(PosCash::getCompleteEmp, completeEmp).eq(PosCash::getId, id));
    }

    @Override
    public <V> List<V> listObjs(Wrapper<PosCash> queryWrapper, Function<? super Object, V> mapper) {
        return superManager.listObjs(queryWrapper, mapper);
    }
}


