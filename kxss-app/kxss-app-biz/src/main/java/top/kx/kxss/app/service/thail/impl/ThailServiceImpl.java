package top.kx.kxss.app.service.thail.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.app.service.thail.ThailService;
import top.kx.kxss.base.entity.thail.BaseThail;
import top.kx.kxss.base.manager.thail.BaseThailManager;
import top.kx.kxss.base.vo.query.thail.BaseThailPageQuery;
import top.kx.kxss.base.vo.result.thail.BaseThailResultVO;
import top.kx.kxss.base.vo.save.thail.BaseThailSaveVO;
import top.kx.kxss.base.vo.update.thail.BaseThailUpdateVO;
import top.kx.kxss.common.constant.DsConstant;

/**
 * <p>
 * 业务实现类
 * 套餐数据
 * </p>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class ThailServiceImpl extends
        SuperServiceImpl<BaseThailManager, Long, BaseThail, BaseThailSaveVO, BaseThailUpdateVO, BaseThailPageQuery, BaseThailResultVO> implements ThailService {

}
