package top.kx.kxss.app.service.table.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.kxss.app.entity.cash.table.PosCashTableCash;
import top.kx.kxss.app.mapper.member.MemberMapper;
import top.kx.kxss.app.service.table.CalculateBizService;
import top.kx.kxss.app.utils.DateUtils;
import top.kx.kxss.app.vo.result.table.charing.AppTableCharingResultVo;
import top.kx.kxss.app.vo.result.table.settings.AppTableChargingSettingResultVO;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.service.BaseService;
import top.kx.kxss.common.constant.DsConstant;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
@DS(DsConstant.BASE_TENANT)
public class CalculateBizServiceImpl implements CalculateBizService {
    @Autowired
    private MemberMapper memberMapper;

    @Override
    public BigDecimal calProductAmount(BaseProduct product, Long memberId) {
        if (null != memberId) {
            // 是会员
            // 会员的时候，获取会员的折扣权益
            Map<String, Object> memberDiscount = memberMapper.getMemberDiscount(memberId, "2");
            BigDecimal discount = new BigDecimal(10);
            if (null != memberDiscount) {
                discount = (BigDecimal) memberDiscount.get("discount");
            }
            // 计算会员折扣
            BigDecimal memberPrice = product.getRetailPrice().multiply(discount.divide(new BigDecimal(10), 2, RoundingMode.UP)).setScale(2, RoundingMode.UP);
            // 商品会员价
            BigDecimal goodsMemberPrice = product.getMemberPrice();
            if (null == goodsMemberPrice) {
                return memberPrice;
            }
            return memberPrice.compareTo(goodsMemberPrice) >= 0 ? goodsMemberPrice : memberPrice;
        } else {
            // 不是会员取原价
            return product.getRetailPrice();
        }
    }

    @Override
    public BigDecimal calServiceAmount(BaseService service, Long memberId) {
        if (null != memberId) {
            // 是会员
            // 会员的时候，获取会员的折扣权益
            Map<String, Object> memberDiscount = memberMapper.getMemberDiscount(memberId, "3");
            BigDecimal discount = new BigDecimal(10);
            if (null != memberDiscount) {
                discount = (BigDecimal) memberDiscount.get("discount");
            }
//            if (!service.getIsDiscount()) {
//                // 不支持打折
//                return service.getTimingMemberPrice();
//            }

            // 计算会员折扣
            BigDecimal memberPrice = service.getTimingCustomerPrice().multiply(discount.divide(new BigDecimal(10), 2, RoundingMode.UP)).setScale(2, RoundingMode.UP);

            // 服务会员价
//            if (null == service.getTimingMemberPrice()) {
//                return memberPrice;
//            }
            return memberPrice.compareTo(service.getTimingMemberPrice()) >= 0 ? service.getTimingMemberPrice() : memberPrice;
        } else {
            // 不是会员取原价
            return service.getTimingCustomerPrice();

        }
    }

    @Override
    public BigDecimal calTableAmount(AppTableChargingSettingResultVO todaySet, Long memberId, Map<String, Object> memberDiscount) {
        if (null == memberDiscount) {
            return todaySet.getCustomerPrice();
        }
        if (null != memberId) {
            // 会员
            BigDecimal memberPrice = todaySet.getMemberPrice();
            if (null == memberPrice && null == memberDiscount) {
                return todaySet.getCustomerPrice();
            }
            BigDecimal discount = (BigDecimal) memberDiscount.get("discount");
            // 计算会员折扣
            BigDecimal discountPrice = todaySet.getCustomerPrice().multiply(discount.divide(new BigDecimal(10), 2, RoundingMode.HALF_UP)).setScale(2, BigDecimal.ROUND_HALF_UP);
            if (null == memberPrice) {
                return discountPrice;
            }
            if (null == discountPrice) {
                return memberPrice;
            }
            return discountPrice.compareTo(memberPrice) >= 0 ? memberPrice : discountPrice;
        } else {
            return todaySet.getCustomerPrice();
        }
    }

    @Override
    public BigDecimal orginPriceCountAmount(BigDecimal orginPrice, String type, BigDecimal discount) {
        BigDecimal amount = orginPrice;

        if (null != discount){
            if ("1".equals(type) || "4".equals(type)) {
                // 单品优惠-打折||卡-打折
                amount = amount.multiply(discount.divide(new BigDecimal(10), 2, RoundingMode.UP)).setScale(2, RoundingMode.UP);
            } else if ("2".equals(type)) {
                // 单品优惠-减免
                amount = amount.subtract(discount);
                if (amount.compareTo(new BigDecimal(0)) < 0) {
                    amount = new BigDecimal(0);
                }
            } else if ("3".equals(type) || "5".equals(type)) {
                // 单品优惠-赠送||卡-计次
                amount = new BigDecimal(0);
            }
        }

        return amount;
    }

    @Override
    public BigDecimal orginPriceCountAmountByType6(BigDecimal price, Long duration, Long dicountTime, int period, int overtime) {
        BigDecimal amount = new BigDecimal(0);
        if (dicountTime >= duration) {
            return amount;
        } else {
            duration = duration - dicountTime;
        }

        if (duration > period) {
            long ys = duration / period;
            BigDecimal ysAmount = new BigDecimal(ys).multiply(price);
            // 计算超时
            long overMinite = duration % period;
            // 超时周期
            long voerRange;
            if (0 != overMinite % overtime) {
                voerRange = overMinite / overtime + 1;
            } else {
                voerRange = overMinite / overtime;
            }
            // 超时费用
            BigDecimal overAmount = new BigDecimal(voerRange).multiply(price);
            amount = ysAmount.add(overAmount);
        } else {
            amount = price;
        }
        return amount;
    }

    @Override
    public void calAmount(PosCashTableCash pct, AppTableChargingSettingResultVO preSettings, AppTableCharingResultVo charing, boolean isMember) {
        // 计算分钟差
        LocalDateTime end = null == pct.getEndTime() ? LocalDateTime.now() : pct.getEndTime();
        long difMinite = DateUtils.calDifMinutes(pct.getStartTime(), end);
        if (difMinite > charing.getPeriod()) {
            // 超时开始计费
            long ys = difMinite / charing.getPeriod();
            BigDecimal ysAmount = new BigDecimal(ys).multiply(pct.getPrice());
            // 计算超时
            long overMinite = difMinite % charing.getPeriod();
            // 超时周期
            long voerRange = 0L;
            if (0 != overMinite % charing.getOvertime()) {
                voerRange = overMinite / charing.getOvertime() + 1;
            } else {
                voerRange = overMinite / charing.getOvertime();
            }
            // 超时费用
            BigDecimal overAmount = new BigDecimal(voerRange).multiply(pct.getPrice());
            BigDecimal amount = ysAmount.add(overAmount);
//            long calTime = difMinite - Long.parseLong(charing.getOvertime().toString());
//            BigDecimal amount = pct.getPrice().add(new BigDecimal(calTime).divide(new BigDecimal(charing.getPeriod()), 0, BigDecimal.ROUND_HALF_UP).multiply(pct.getPrice()));
            if ("1".equals(charing.getIsCapping())) {
                // 有封顶 TODO 有问题，同一个时间段怎么会存在封顶价格
//                BigDecimal topAmount = isMember ? charing.getMemberCapping() : charing.getCustomerCapping();
//                if(amount.compareTo(topAmount) > 0) {
//                    pct.setAmount(topAmount);
//                } else {
//                    pct.setAmount(amount);
//                }
                pct.setAmount(amount);
            } else {
                pct.setAmount(amount);
            }
        } else if (0 < difMinite && difMinite <= charing.getPeriod()) {
            pct.setAmount(pct.getPrice());
        } else if (0 == difMinite) {
            pct.setAmount(new BigDecimal(0));
        }
    }

    @Override
    public BigDecimal overTimeCash(BigDecimal price, Long duration, int period, int overtime) {
        BigDecimal amount;
        if (duration > period) {
            long ys = duration / period;
            BigDecimal ysAmount = new BigDecimal(ys).multiply(price);
            // 计算超时
            long overMinite = duration % period;
            // 超时周期
            long voerRange;
            if (0 != overMinite % overtime) {
                voerRange = overMinite / overtime + 1;
            } else {
                voerRange = overMinite / overtime;
            }
            // 超时费用
            BigDecimal overAmount = new BigDecimal(voerRange).multiply(price);
            amount = ysAmount.add(overAmount);
        } else {
            amount = price;
        }
        return amount;
    }


}
