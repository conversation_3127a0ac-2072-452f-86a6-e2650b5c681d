package top.kx.kxss.app.mapper.cash.discount;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.discount.PosCashDiscountDetail;
import org.springframework.stereotype.Repository;
import top.kx.kxss.app.vo.result.cash.discount.PosCashDiscountDetailResultVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * 订单优惠明细
 * </p>
 *
 * <AUTHOR>
 * @date 2023-08-02 18:45:43
 * @create [2023-08-02 18:45:43] [dou] [代码生成器生成]
 */
@Repository
public interface PosCashDiscountDetailMapper extends SuperMapper<PosCashDiscountDetail> {

    void deleteByPosCashId(Long posCashId);

    List<PosCashDiscountDetailResultVO> selectByDiscountType(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<PosCashDiscountDetailResultVO> selectDiscountDetailByOrderSource(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);
}


