package top.kx.kxss.app.service.cash.service;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.app.vo.query.cash.service.PosCashServicePageQuery;
import top.kx.kxss.app.vo.result.ProfitResultVO;
import top.kx.kxss.app.vo.result.cash.service.PosCashServiceResultVO;
import top.kx.kxss.app.vo.save.cash.service.PosCashServiceSaveVO;
import top.kx.kxss.app.vo.update.cash.service.PosCashServiceUpdateVO;
import java.util.List;


/**
 * <p>
 * 业务接口
 * 收银-服务子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:44:58
 * @create [2023-04-19 14:44:58] [dou] [代码生成器生成]
 */
public interface PosCashServiceService extends SuperService<Long, PosCashService, PosCashServiceSaveVO,
    PosCashServiceUpdateVO, PosCashServicePageQuery, PosCashServiceResultVO> {

    void updateBatch(List<PosCashService> posCashServiceList);

    boolean updateBatchById(List<PosCashService> cashProducts);

    boolean removeBatchByIds(List<PosCashService> cashServiceList);

    ProfitResultVO findProfit(List<Long> posCashIdList);

    Boolean checkIsUse(List<Long> longs);

    Boolean checkEmpIsUse(List<Long> longs);

    boolean removeById(Long id);

    long count(LbQueryWrap<PosCashService> eq);

    boolean update(LbUpdateWrap<PosCashService> eq);

    boolean updateById(PosCashService posCashService);

    PosCashService getOne(LbQueryWrap<PosCashService> eq);

    boolean save(PosCashService cashService);

    Boolean checkServiceActivity(List<Long> longs);
}


