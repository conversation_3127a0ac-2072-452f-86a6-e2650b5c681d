package top.kx.kxss.app.statemachine.module.light;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.BuiltinExchangeType;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.SpringUtils;
import top.kx.kxss.IotApi;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.event.LightEvent;
import top.kx.kxss.app.event.model.LightDTO;
import top.kx.kxss.app.mqtt.handler.MQTTGateway;
import top.kx.kxss.app.statemachine.AbstractPosCashProcessor;
import top.kx.kxss.app.statemachine.annotation.PosCashProcessor;
import top.kx.kxss.app.vo.mqtt.MQTTMessage;
import top.kx.kxss.app.vo.mqtt.MQTTParams;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.service.common.BaseParameterService;
import top.kx.kxss.base.service.table.BaseTableInfoService;
import top.kx.kxss.base.vo.save.biz.BaseBizLogSaveVO;
import top.kx.kxss.common.constant.MqttConstant;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.model.enumeration.app.MQTTTypeEnum;
import top.kx.kxss.model.enumeration.base.BizLogModuleEnum;
import top.kx.kxss.model.enumeration.base.BizLogTypeEnum;
import top.kx.kxss.model.enumeration.base.TableStatus;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static top.kx.basic.context.ContextUtil.getCurrentCompanyId;

/**
 * 开灯
 *
 * <AUTHOR>
 */
@Component
@PosCashProcessor
@Slf4j
public class OpenLightProcessor extends AbstractPosCashProcessor {

    @Autowired
    private MQTTGateway mqttGateway;
    @Autowired
    private BaseTableInfoService tableInfoService;
    @Autowired
    private BaseBizLogService bizLogService;
    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private RabbitTemplate template;
    @Autowired
    private BaseParameterService baseParameterService;
    @Autowired
    public IotApi iotApi;

    public OpenLightProcessor() {
        super.setBillState(PosCashConstant.Event.OPEN_LIGHT.getEventId());
    }

    @Override
    public boolean process(Long posCashId, Object... params) {
        PosCash posCash = (PosCash) params[0];
        BaseTableInfo tableInfo = (BaseTableInfo) params[1];
        boolean suc = false;
        boolean lock = false;
        // 判断是否需要控灯
        Boolean controlLights = baseParameterService.manualControlLights();
        if (controlLights) {
            log.warn("手动控制灯控，无需开关灯");
            Long tenantId = ContextUtil.getTenantId();
            //YC(迁就煞笔客户 2024-12-04 12:51) 547502532314973184
            if (ObjectUtil.isNotNull(tableInfo)
                    && !ObjectUtil.equal(tenantId, 547502532314973184L)) {
                tableInfoService.updateById(tableInfo);
                return true;
            }
        }
        try {
            if (ObjectUtil.isNull(tableInfo)) {
                tableInfo = tableInfoService.getById(posCash.getTableId());
            }
            ArgumentAssert.notNull(tableInfo, "台桌异常");
            ArgumentAssert.notBlank(tableInfo.getLineNum(), "请配置灯控信息！");
            //判断lightname和linenum，选择灯控的发送方式jin，判断lightname不包含.,且长度>10为无线灯控
            if (!controlLights) {
                if (!tableInfo.getLightName().contains(".") && tableInfo.getLightName().length() > 10) {

                    LightDTO lightDTO = LightDTO.openLight(ContextUtil.getTenantId(), ContextUtil.getEmployeeId(), tableInfo);
                    SpringUtils.publishEvent(new LightEvent(lightDTO));

                } else {
                    // 发送消息到指定主题
                    Map<String, Object> map = MapUtil.newHashMap();
                    map.put("tenantId", String.valueOf(ContextUtil.getTenantId()));
                    map.put("currentCompanyId", String.valueOf(ContextUtil.getCurrentCompanyId()));
                    map.put("tableId", String.valueOf(posCash.getTableId()));
                    map.put("lightName", tableInfo.getLightName());
                    map.put("lineNum", tableInfo.getLineNum());
                    map.put("tableStatus", String.valueOf(TableStatus.USING.getCode()));
                    MQTTMessage message = MQTTMessage.builder()
                            .content(JSON.toJSONString(MQTTParams.builder()
                                    .data(map).type(MQTTTypeEnum.OPEN_LIGHT.getCode())
                                    .build())).qos(2)
                            .topic(MqttConstant.BIZ_TOPIC.concat(ContextUtil.getTenantId() + "_" +
                                    ContextUtil.getCurrentCompanyId()))
                            .build();
                    mqttGateway.sendToMqtt(message.getTopic(), message.getQos(), message.getContent());
                    log.info("开灯请求");
                }
                tableInfo.setIsShowLight(false);
            }
            tableInfo.setLightStatus("1");
            tableInfo.setUpdatedBy(ContextUtil.getUserId());
            tableInfoService.updateById(tableInfo);
            //新增操作日志
            bizLogService.save(BaseBizLogSaveVO.builder()
                    .orgId(getCurrentCompanyId()).description("开灯【" + tableInfo.getName() + "/线路号" + tableInfo.getLightName() + "】")
                    .bizModule(BizLogModuleEnum.OPEN_LIGHT.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                    .employeeId(ContextUtil.getEmployeeId()).createdOrgId(getCurrentCompanyId())
                    .opearteTime(LocalDateTime.now()).sourceId(posCash.getId()).remarks("")
                    .build());
        } finally {
            if (lock) {
                distributedLock.releaseLock(posCash.getTableId() + "_LIGHT");
            }
        }
        return suc;
    }

    public void createQueue(Long tenantId, Long orgId) {

        //创建信道
        Channel channel = template.getConnectionFactory().createConnection().createChannel(true);

        try {
            //声明一个交换机与生产者相同
            channel.exchangeDeclare(DirectExchange.DEFAULT.getName(), BuiltinExchangeType.FANOUT, true);
            //获取一个随机的队列名称，使用默认方式，产生的队列为临时队列，在没有消费者时将会自动删除
            String queueName = channel.queueDeclare().getQueue();
            //用户Id与队列名绑定
            ConcurrentHashMap<String, String> userQueueMap = new ConcurrentHashMap<>();
            userQueueMap.putIfAbsent(queueName, tenantId.toString() + orgId);
            //关联 exchange 和 queue ，因为是广播无需指定routekey，routingKey设置为空字符串
            // channel.queueBind(queue, exchange, routingKey)
            channel.queueBind(queueName, DirectExchange.DEFAULT.getName(), "");
        } catch (Exception ex) {
        }
    }
}
