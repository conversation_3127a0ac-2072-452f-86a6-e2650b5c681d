package top.kx.kxss.app.manager.cash.product;

import top.kx.basic.base.manager.SuperManager;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.vo.result.ProfitResultVO;
import top.kx.kxss.app.vo.result.cash.product.PosCashProductResultVO;

import java.util.List;

/**
 * <p>
 * 通用业务接口
 * 结算单商品子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:40:33
 * @create [2023-04-19 14:40:33] [dou] [代码生成器生成]
 */
public interface PosCashProductManager extends SuperManager<PosCashProduct> {

    ProfitResultVO findProfit(List<Long> posCashIdList);

    List<String> productRanking(Long memberId);

    List<PosCashProductResultVO> selectDiscount();

    List<PosCashProduct> queryList(LbQueryWrap<PosCashProduct> queryWrap);
}


