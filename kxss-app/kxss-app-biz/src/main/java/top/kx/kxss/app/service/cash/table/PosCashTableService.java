package top.kx.kxss.app.service.cash.table;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.app.entity.cash.PackFieldSaveVO;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.vo.query.cash.table.PosCashTablePageQuery;
import top.kx.kxss.app.vo.result.ProfitResultVO;
import top.kx.kxss.app.vo.result.cash.table.PosCashTableResultVO;
import top.kx.kxss.app.vo.save.cash.table.PosCashTableSaveVO;
import top.kx.kxss.app.vo.update.cash.table.PosCashTableUpdateVO;
import top.kx.kxss.base.entity.table.BaseTableInfo;

import java.util.List;
import java.util.function.Function;


/**
 * <p>
 * 业务接口
 * 台桌计时费用
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:44:58
 * @create [2023-04-19 14:44:58] [dou] [代码生成器生成]
 */
public interface PosCashTableService extends SuperService<Long, PosCashTable, PosCashTableSaveVO,
    PosCashTableUpdateVO, PosCashTablePageQuery, PosCashTableResultVO> {

    boolean updateBatchById(List<PosCashTable> cashTableList);

    long count(LbQueryWrap<PosCashTable> eq);

    boolean removeBatchByIds(List<PosCashTable> cashTableList);

    boolean update(LbUpdateWrap<PosCashTable> eq);

    ProfitResultVO findProfit(List<Long> posCashIdList, Boolean thailIsNull);

    Boolean checkIsUse(List<Long> longs);

    Boolean checkTableTypeIsUse(List<String> longs);

    Boolean checkTableTypeIdIsUse(List<Long> longs);

    PosCashTable getPackField(PosCash posCash, BaseTableInfo baseTableInfo, PackFieldSaveVO saveVO);

    Boolean updateById(PosCashTable posCashTable);

    PosCashTable getOne(LbQueryWrap<PosCashTable> eq);

    <V> List<V> listObjs(Wrapper<PosCashTable> queryWrapper, Function<? super Object, V> mapper);

    Boolean save(PosCashTable cashTable);

    Boolean checkTableUsed(PosCash posCash);

    Boolean checkTableStop(PosCash posCash);

    Boolean checkServiceActivity(List<Long> longs);

    Boolean saveOrUpdate(PosCashTable cashTable);

}


