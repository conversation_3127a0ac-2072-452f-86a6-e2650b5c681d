package top.kx.kxss.app.manager.cash.refund.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.app.entity.cash.refund.PosCashRefundPayment;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.app.manager.cash.refund.PosCashRefundPaymentManager;
import top.kx.kxss.app.mapper.cash.refund.PosCashRefundPaymentMapper;

/**
 * <p>
 * 通用业务实现类
 * 退款单
 * </p>
 *
 * <AUTHOR>
 * @date 2023-11-13 16:05:52
 * @create [2023-11-13 16:05:52] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashRefundPaymentManagerImpl extends SuperManagerImpl<PosCashRefundPaymentMapper, PosCashRefundPayment> implements PosCashRefundPaymentManager {

}


