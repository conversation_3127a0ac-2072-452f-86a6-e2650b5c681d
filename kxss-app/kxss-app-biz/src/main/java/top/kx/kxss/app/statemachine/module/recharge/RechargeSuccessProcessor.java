package top.kx.kxss.app.statemachine.module.recharge;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.wechat.pay.java.service.payments.model.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.exception.BizException;
import top.kx.basic.exception.code.ExceptionCode;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.granter.CalcAmountGranter;
import top.kx.kxss.app.properties.WxPayProperties;
import top.kx.kxss.app.service.cash.PosCashServiceService;
import top.kx.kxss.app.service.cash.payment.PosCashPaymentService;
import top.kx.kxss.app.service.pay.WxPayService;
import top.kx.kxss.app.statemachine.AbstractPosCashProcessor;
import top.kx.kxss.app.statemachine.annotation.PosCashProcessor;
import top.kx.kxss.app.vo.save.cash.payment.PosCashPaymentSaveVO;
import top.kx.kxss.app.vo.update.cash.PosCashUpdateVO;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.service.member.deposit.MemberDepositCouponService;
import top.kx.kxss.base.vo.save.biz.BaseBizLogSaveVO;
import top.kx.kxss.base.vo.update.member.MemberInfoUpdateVO;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.model.enumeration.app.BasePaymentTypeEnum;
import top.kx.kxss.model.enumeration.app.PayTypeEnum;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.model.enumeration.base.*;
import top.kx.kxss.model.enumeration.pos.PosCashPaymentTransactionStatusEnum;
import top.kx.kxss.model.enumeration.pos.PosCashPaymentTransactionTypeEnum;
import top.kx.kxss.pos.PosCashPaymentTransactionApi;
import top.kx.kxss.pos.vo.save.cash.PosCashPaymentTransactionSaveVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Objects;

import static top.kx.basic.context.ContextUtil.getCurrentCompanyId;

/**
 * 计算价格
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@PosCashProcessor
@EnableConfigurationProperties(WxPayProperties.class)
public class RechargeSuccessProcessor extends AbstractPosCashProcessor {

    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private PosCashServiceService posCashService;
    @Autowired
    private CalcAmountGranter calcAmountGranter;
    @Autowired
    private MemberInfoService memberInfoService;
    @Autowired
    private WxPayService wxPayService;
    @Autowired
    private PosCashPaymentService posCashPaymentService;
    @Autowired
    private BaseBizLogService bizLogService;
    @Autowired
    private MemberDepositCouponService memberDepositCouponService;
    @Autowired
    private PosCashPaymentTransactionApi posCashPaymentTransactionApi;

    public RechargeSuccessProcessor() {
        super.setBillState(PosCashConstant.Event.RECHARGE_SUCCESS.getEventId());
    }

    @Override
    public boolean process(Long posCashId, Object... params) {
        PosCash posCash = (PosCash) params[0];
        boolean lock = false;
        boolean lock1 = false;
        try {
            lock = distributedLock.lock(posCashId + PosCashConstant.Event.ORDER.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            //验证微信订单是否存在
            Transaction transaction = wxPayService.queryOrderByOutTradeNo(posCashId);
            ArgumentAssert.notNull(transaction, "支付订单不存在或支付失败！");
            log.info("支付订单信息：{}", transaction);
            Map map = JSON.parseObject(transaction.getAttach(), Map.class);
            ContextUtil.setTenantBasePoolName(map.get("tenantId").toString());
            ContextUtil.setCurrentCompanyId(map.get("currentCompanyId").toString());
            ContextUtil.setClientId(map.get("clientId").toString());
            ContextUtil.setTenantId(map.get("tenantId").toString());
            //操作订单
            ArgumentAssert.notNull(posCash, "订单不存在！");
            if (ObjectUtil.equal(posCash.getBillState(), PosCashBillStateEnum.COMPLETE.getCode())) {
                return true;
            }
            if (ObjectUtil.isNotNull(posCash.getMemberId())) {
                lock1 = distributedLock.lock(posCash.getMemberId() + PosCashConstant.Event.MEMBER.getCode(), 0);
            }
            BigDecimal payAmount = calcAmountGranter.changeF2Y(transaction.getAmount().getTotal());
            //新增支付信息
            PosCashPaymentSaveVO cashPaymentSaveVO = PosCashPaymentSaveVO.builder()
                    .cashId(posCash.getId()).amount(payAmount)
                    .payType(PayTypeEnum.WEIXIN.getCode()).payTime(LocalDateTime.now())
                    .createdOrgId(ContextUtil.getCurrentCompanyId()).zlPaice(BigDecimal.ZERO)
                    .status(PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                    .roundAmount(BigDecimal.ZERO).orderSource(OrderSourceEnum.SELF.getCode())
                    .sn(ContextUtil.getSn()).isNewCum(false)
                    .build();
            PosCashPayment posCashPayment = posCashPaymentService.save(cashPaymentSaveVO);
            posCashPaymentTransactionApi.save(PosCashPaymentTransactionSaveVO.builder()
                    .type(!StringUtils.equals(PosCashTypeEnum.RECHARGE.getCode(), posCash.getType()) ? PosCashPaymentTransactionTypeEnum.ORDER.getCode() : PosCashPaymentTransactionTypeEnum.RECHARGE.getCode())
                    .payTypeId(posCashPayment.getPayTypeId())
                    .payType(posCashPayment.getPayName())
                    .payTypeDetail(null)
                    .orderSource(ContextUtil.getOrderSource())
                    .securitiesType(null)
                    .securitiesNumber(null)
                    .status(PosCashPaymentTransactionStatusEnum.PAY_SUCCESS.getCode())
                    .cashId(posCash.getId())
                    .paymentId(posCashPayment.getId())
                    .code(posCash.getCode())
                    .amount(posCashPayment.getAmount())
                    .feeAmount(Objects.isNull(posCashPayment.getMchFeeAmount()) ? BigDecimal.ZERO : posCashPayment.getMchFeeAmount())
                    .payTime(Objects.nonNull(posCashPayment.getPayTime()) ? posCashPayment.getPayTime() : LocalDateTime.now())
                    .orderId(posCashPayment.getOrderId())
                    .employeeId(posCashPayment.getEmployeeId())
                    .build());
            //订单结束
            BigDecimal unpaid = posCash.getUnpaid().subtract(payAmount);
            if (unpaid.compareTo(BigDecimal.ZERO) == 0) {
                log.info("支付完成：{}", "创建base_payment");
                MemberInfo memberInfo = null;
                if (ObjectUtil.isNotNull(posCash.getMemberId())) {
                    //充值到账
                    memberInfo = memberInfoService.getById(posCash.getMemberId());
                    ArgumentAssert.notNull(memberInfo, "订单异常！");
                    MemberInfoUpdateVO memberInfoUpdateVO = BeanUtil.copyProperties(memberInfo, MemberInfoUpdateVO.class);
                    memberInfoUpdateVO.setRechargeAmount(memberInfo.getRechargeAmount().add(posCash.getAmount()));
                    memberInfoUpdateVO.setGiftAmount(memberInfo.getGiftAmount().add(posCash.getGiftAmount()));
                    memberInfo = memberInfoService.updateById(memberInfoUpdateVO);
                }
                posCashService.createBasePayment(posCashId, BasePaymentTypeEnum.RECHARGE, posCashPayment, memberInfo);

            }
            PosCashUpdateVO posCashUpdateVO = BeanUtil.copyProperties(posCash, PosCashUpdateVO.class);
//            posCashUpdateVO.setBillState(unpaid.compareTo(BigDecimal.ZERO) == 0 ? PosCashBillStateEnum.COMPLETE.getCode() : PosCashBillStateEnum.NO_SETTLED.getCode());
            posCashUpdateVO.setPaid(posCash.getPaid().add(payAmount));
            posCashUpdateVO.setUnpaid(unpaid);
            posCashUpdateVO.setPayName((StrUtil.isBlank(posCash.getPayName()) ? posCash.getPayName() : posCash.getPayName() + "\r\n") + "微信支付：" + payAmount);
            posCash.setUpdatedTime(LocalDateTime.now());
            posCashService.updateById(posCashUpdateVO);
            log.info("支付信息：{}", transaction);
            //发放优惠劵
            memberDepositCouponService.grantCoupon(posCash.getMemberId(), posCash.getDepositRuleId());
            //新增操作日志
            bizLogService.save(BaseBizLogSaveVO.builder()
                    .orgId(getCurrentCompanyId()).description("充值成功")
                    .bizModule(BizLogModuleEnum.POS_CASH.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                    .employeeId(ContextUtil.getEmployeeId()).createdOrgId(getCurrentCompanyId())
                    .opearteTime(LocalDateTime.now()).sourceId(posCash.getId()).remarks("充值成功")
                    .build());
        } finally {
            ContextUtil.remove();
            if (lock) {
                distributedLock.releaseLock(posCashId + PosCashConstant.Event.ORDER.getCode());
            }
            if (lock1) {
                distributedLock.releaseLock(posCashId + PosCashConstant.Event.MEMBER.getCode());
            }
        }
        return true;
    }

}
