package top.kx.kxss.app.mapper.cash.payment;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO;
import top.kx.kxss.pos.query.report.ReportPaymentDetailsQuery;
import top.kx.kxss.pos.query.report.ReportPaymentQuery;
import top.kx.kxss.wxapp.vo.query.payment.PaymentTypeQuery;
import top.kx.kxss.wxapp.vo.query.statistics.ConsumeQuery;
import top.kx.kxss.wxapp.vo.result.payment.PaymentTypeStatisticsDetailsResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.PaymentDetailsOverviewResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.PaymentDetailsResultVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * 商品结算单收款子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:38:34
 * @create [2023-04-19 14:38:34] [dou] [代码生成器生成]
 */
@Repository
public interface PosCashPaymentMapper extends SuperMapper<PosCashPayment> {

    IPage<PaymentDetailsResultVO> paymentDetails(@Param("page") IPage<PaymentDetailsResultVO> page, @Param("model") ReportPaymentDetailsQuery model);

    /**
     * 聚合支付支付明细
     * @param page
     * @param model
     * @return
     */
    IPage<PaymentDetailsResultVO> polymerizeCashPaymentPage(@Param("page") IPage<PaymentDetailsResultVO> page, @Param("model") PaymentTypeQuery model);

    /**
     * 聚合支付订单列表
     * @param model
     * @return
     */
    List<PaymentDetailsResultVO> polymerizeCashPaymentList(@Param("model") PaymentTypeQuery model);


    PaymentDetailsOverviewResultVO paymentDetailsOverviewAmount(ReportPaymentDetailsQuery model);
    PaymentDetailsOverviewResultVO paymentDetailsOverviewRefundAmount(ReportPaymentDetailsQuery model);

    /**
     * 聚合支付支付方式汇总
     * @param params
     * @return
     */
    List<PaymentTypeStatisticsDetailsResultVO> statisticsPolymerize(PaymentTypeQuery params);

    IPage<Map<String, Object>> paymentDataReportPage(@Param("page") IPage<Object> page, @Param("model") ReportPaymentQuery model);


    List<PosCashPaymentResultVO> paymentDataReport(@Param("model") ReportPaymentQuery model);



    List<PosCashPaymentResultVO> statisConsumeAmount(@Param("model") ConsumeQuery model);

    PosCashPaymentResultVO selectOneAmount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<PosCashPaymentResultVO> selectGiftPaymentByOrderSource(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);


    List<PosCashPaymentResultVO> selectListAmountBySource(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<PosCashPaymentResultVO> selectListAmountByType(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<PosCashPaymentResultVO> selectAmountByPayTypeId(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<PosCashPaymentResultVO> selectRefundByPayTypeId(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);



}


