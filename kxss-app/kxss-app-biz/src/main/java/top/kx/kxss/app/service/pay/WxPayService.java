package top.kx.kxss.app.service.pay;

import com.wechat.pay.java.service.payments.model.Transaction;

/**
 * <p>
 * 业务接口
 * 微信支付接口
 * </p>
 *
 * <AUTHOR>
 */
public interface WxPayService {

    /**
     * 商户订单查询
     * @param posCashId
     */
    Transaction queryOrderByOutTradeNo(Long posCashId);

    String decryptToString(String associatedData, String nonce, String ciphertext);

    String componentDecryptToString(String associatedData, String nonce, String ciphertext);
}


