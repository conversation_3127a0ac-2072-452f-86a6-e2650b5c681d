package top.kx.kxss.app.service.cash.product.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.manager.cash.product.PosCashProductManager;
import top.kx.kxss.app.service.cash.product.PosCashProductService;
import top.kx.kxss.app.vo.query.cash.product.PosCashProductPageQuery;
import top.kx.kxss.app.vo.result.ProfitResultVO;
import top.kx.kxss.app.vo.result.cash.product.PosCashProductResultVO;
import top.kx.kxss.app.vo.save.cash.product.PosCashProductSaveVO;
import top.kx.kxss.app.vo.update.cash.product.PosCashProductUpdateVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillTypeEnum;
import top.kx.kxss.model.enumeration.base.PosCashTypeEnum;

import java.util.List;

/**
 * <p>
 * 业务实现类
 * 结算单商品子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:40:33
 * @create [2023-04-19 14:40:33] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashProductServiceImpl extends SuperServiceImpl<PosCashProductManager, Long, PosCashProduct, PosCashProductSaveVO,
        PosCashProductUpdateVO, PosCashProductPageQuery, PosCashProductResultVO> implements PosCashProductService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchById(List<PosCashProduct> cashProducts) {
        return superManager.updateBatchById(cashProducts);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeBatchByIds(List<PosCashProduct> cashProductList) {
        return superManager.removeBatchByIds(cashProductList);
    }

    @Override
    public Boolean checkProductIsUse(List<Long> longs) {
        return superManager.count(Wraps.<PosCashProduct>lbQ()
                .in(PosCashProduct::getProductId, longs)
                .inSql(PosCashProduct::getCashId,

                        " select p.id from pos_cash p where p.delete_flag = 0 "
                                + " and p.type_ != "
                                + PosCashTypeEnum.RECHARGE.getCode()
                                + " and p.bill_type not in ('"
                                + PosCashBillTypeEnum.CANCELLATION.getCode()
                                + "','" + PosCashBillTypeEnum.CHARGEBACK.getCode() + "') and p.bill_state not  in ('"
                                + PosCashBillStateEnum.COMPLETE.getCode() + "','"
                                + PosCashBillStateEnum.REFUNDED.getCode()
                                + "','" + PosCashBillStateEnum.PART_REFUND.getCode()
                                + "')")) > 0;
    }

    @Override
    public ProfitResultVO findProfit(List<Long> posCashIdList) {
        return superManager.findProfit(posCashIdList);
    }


    @Override
    public List<String> productRanking(Long memberId) {
        return superManager.productRanking(memberId);
    }

    @Override
    public long count(LbQueryWrap<PosCashProduct> eq) {
        return superManager.count(eq);
    }

    @Override
    @Transactional
    public boolean removeById(Long id) {
        return superManager.removeById(id);
    }

    @Override
    public boolean removeById(PosCashProduct cashProduct) {
        return superManager.removeById(cashProduct);
    }

    @Override
    @Transactional
    public boolean update(LbUpdateWrap<PosCashProduct> eq) {
        return superManager.update(eq);
    }

    @Override
    @Transactional
    public boolean updateById(PosCashProduct posCashProduct) {
        return superManager.updateById(posCashProduct);
    }

    @Override
    public List<PosCashProductResultVO> selectDiscount() {
        return superManager.selectDiscount();
    }

    @Override
    public List<PosCashProduct> queryList(LbQueryWrap<PosCashProduct> queryWrap) {
        return superManager.queryList(queryWrap);
    }

    @Override
    public PosCashProduct getOne(LbQueryWrap<PosCashProduct> last) {
        return superManager.getOne(last);
    }

    @Override
    public boolean save(PosCashProduct posCashProduct) {
        return superManager.save(posCashProduct);
    }
}


