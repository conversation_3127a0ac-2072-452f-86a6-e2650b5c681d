package top.kx.kxss.app.service.cash.payment.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.jackson.JsonUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.manager.cash.payment.PosCashPaymentManager;
import top.kx.kxss.app.mapper.cash.payment.PosCashPaymentMapper;
import top.kx.kxss.app.service.cash.payment.PosCashPaymentService;
import top.kx.kxss.app.vo.member.AccountDeductResultVO;
import top.kx.kxss.app.vo.query.cash.payment.PosCashPaymentPageQuery;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO;
import top.kx.kxss.app.vo.save.cash.payment.PosCashPaymentSaveVO;
import top.kx.kxss.app.vo.update.cash.payment.PosCashPaymentUpdateVO;
import top.kx.kxss.base.entity.exportrecord.BaseExportRecord;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.member.grade.MemberGrade;
import top.kx.kxss.base.entity.payment.BasePaymentTypeChannel;
import top.kx.kxss.base.service.exportrecord.BaseExportRecordService;
import top.kx.kxss.base.service.member.grade.MemberConsumeLimitService;
import top.kx.kxss.base.service.payment.BasePaymentTypeChannelService;
import top.kx.kxss.base.vo.query.member.grade.MemberConsumeLimitQuery;
import top.kx.kxss.base.vo.save.exportrecord.BaseExportRecordSaveVO;
import top.kx.kxss.base.vo.save.exportrecord.BaseExportRecordSendVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.RabbitMqConstant;
import top.kx.kxss.common.utils.AmountUtil;
import top.kx.kxss.model.enumeration.app.AccountDeductTypeEnum;
import top.kx.kxss.model.enumeration.base.ExportRecordBizTypeEnum;
import top.kx.kxss.wxapp.api.custom.CustomApi;
import top.kx.kxss.wxapp.vo.query.payment.PaymentTypeQuery;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.result.payment.PaymentTypeStatisticsDetailsResultVO;
import top.kx.kxss.wxapp.vo.result.payment.PaymentTypeStatisticsResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.PaymentDetailsResultVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 商品结算单收款子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:38:34
 * @create [2023-04-19 14:38:34] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashPaymentServiceImpl extends SuperServiceImpl<PosCashPaymentManager, Long, PosCashPayment, PosCashPaymentSaveVO,
        PosCashPaymentUpdateVO, PosCashPaymentPageQuery, PosCashPaymentResultVO> implements PosCashPaymentService {

    @Autowired
    private PosCashPaymentMapper posCashPaymentMapper;
    @Autowired
    private CustomApi customApi;
    @Autowired
    private RabbitTemplate template;
    @Autowired
    private BaseExportRecordService baseExportRecordService;
    @Autowired
    private BasePaymentTypeChannelService basePaymentTypeChannelService;
    @Autowired
    private MemberConsumeLimitService consumeLimitService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(PosCashPayment posCashPayment) {
        if (posCashPayment.getMchFeeAmount() == null) {
            posCashPayment.setMchFeeAmount(BigDecimal.ZERO);
        }
        if (posCashPayment.getMchFeeRate() == null) {
            posCashPayment.setMchFeeRate(BigDecimal.ZERO);
        }
        if (StringUtils.isBlank(posCashPayment.getSn())) {
            posCashPayment.setSn(ContextUtil.getSn());
        }
        superManager.save(posCashPayment);
    }

    @Override
    public Boolean updateById(PosCashPayment posCashPayment) {
        return superManager.updateById(posCashPayment);
    }

    @Override
    public long count(LbQueryWrap<PosCashPayment> wrap) {
        return superManager.count(wrap);
    }

    @Override
    public PosCashPayment getOne(LbQueryWrap<PosCashPayment> eq) {
        return superManager.getOne(eq);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchById(List<PosCashPayment> paymentList) {
        return superManager.updateBatchById(paymentList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeById(Long id) {
        return superManager.removeById(id);
    }

    @Override
    public PaymentTypeStatisticsResultVO statisticsPolymerize(PaymentTypeQuery params) {
        R<DataOverviewQuery> storeTimeR = customApi.getStoreTime(params);
        log.info("处理报表中时间,返回结果:{}", storeTimeR);
        ArgumentAssert.isTrue(storeTimeR.getIsSuccess(), storeTimeR.getMsg());
        params.setStartDate(storeTimeR.getData().getStartDate());
        params.setEndDate(storeTimeR.getData().getEndDate());
        params.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        PaymentTypeStatisticsResultVO resultVO = PaymentTypeStatisticsResultVO.builder()
                .totalAmount(BigDecimal.ZERO)
                .totalMchFeeAmount(BigDecimal.ZERO)
                .build();
        List<PaymentTypeStatisticsDetailsResultVO> details = posCashPaymentMapper.statisticsPolymerize(params);
        if (CollUtil.isEmpty(details)) {
            return resultVO;
        }
        BigDecimal amount = details.stream().map(PaymentTypeStatisticsDetailsResultVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        resultVO.setTotalAmount(amount);
        BigDecimal mchFeeAmount = details.stream().map(PaymentTypeStatisticsDetailsResultVO::getMchFeeAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        resultVO.setTotalMchFeeAmount(mchFeeAmount);
        resultVO.setDetails(details);
        return resultVO;
    }

    @Override
    public IPage<PaymentDetailsResultVO> polymerizeCashPaymentPage(PageParams<PaymentTypeQuery> params) {
        PaymentTypeQuery model = params.getModel();
        R<DataOverviewQuery> storeTimeR = customApi.getStoreTime(model);
        log.info("处理报表中时间,返回结果:{}", storeTimeR);
        ArgumentAssert.isTrue(storeTimeR.getIsSuccess(), storeTimeR.getMsg());
        model.setStartDate(storeTimeR.getData().getStartDate());
        model.setEndDate(storeTimeR.getData().getEndDate());
        model.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        params.setSort(null);
        params.setOrder(null);
        IPage<PaymentDetailsResultVO> page = posCashPaymentMapper.polymerizeCashPaymentPage(params.buildPage(PaymentDetailsResultVO.class), model);
        return page;
    }

    @Override
    public List<PaymentDetailsResultVO> polymerizeCashPaymentList(PaymentTypeQuery params) {
        R<DataOverviewQuery> storeTimeR = customApi.getStoreTime(params);
        log.info("处理报表中时间,返回结果:{}", storeTimeR);
        ArgumentAssert.isTrue(storeTimeR.getIsSuccess(), storeTimeR.getMsg());
        params.setStartDate(storeTimeR.getData().getStartDate());
        params.setEndDate(storeTimeR.getData().getEndDate());
        params.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return posCashPaymentMapper.polymerizeCashPaymentList(params);
    }

    @GlobalTransactional(rollbackFor = Exception.class)
    @Override
    public Boolean cashPaymentExport(PaymentTypeQuery params) {
        BaseExportRecordSendVO build = BeanPlusUtil.toBean(params, BaseExportRecordSendVO.class);
        build.setBizTypeEnum(ExportRecordBizTypeEnum.RECONCILIATION);
        build.setTenantId(ContextUtil.getTenantId());
        build.setSn(ContextUtil.getSn());
        build.setOrgId(ContextUtil.getCurrentCompanyId());
        build.setParam(JsonUtil.toJson(params));
        BaseExportRecordSaveVO saveVO = BaseExportRecordSaveVO.builder()
                .bizType(build.getBizTypeEnum().getCode())
                .param(build.getParam())
                .createdOrgId(build.getOrgId())
                .fileName(build.getBizTypeEnum().getDesc() + "导出-" + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")))
                .build();
        BaseExportRecord save = baseExportRecordService.save(saveVO);
        build.setExportRecordId(save.getId());
        template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.EXPORT_RECORD,
                JsonUtil.toJson(build));
        return true;
    }

    @Override
    public AccountDeductResultVO accountDeduct(MemberGrade memberGrade,
                                               MemberInfo memberInfo,
                                               BigDecimal giftPayAmount,
                                               BigDecimal amount,
                                               BigDecimal rechargePayAmount,
                                               BigDecimal singleRechargePayAmount, BigDecimal singleGiftPayAmount,
                                               List<PosCashPayment> paymentList) {
        if (CollUtil.isEmpty(paymentList)) {
            paymentList = Lists.newArrayList();
        }
        AccountDeductResultVO build = AccountDeductResultVO.builder()
                .changeGiftAmount(BigDecimal.ZERO)
                .changeRechargeAmount(BigDecimal.ZERO)
                .build();
        if (giftPayAmount == null) {
            giftPayAmount = BigDecimal.ZERO;
        }
        if (ObjectUtil.isNull(memberInfo) || ObjectUtil.isNull(memberGrade)) {
            return build;
        }
        //单笔消费限制比例 备注
        String remarks = consumeLimitService.singleConsumeRemarks(memberGrade, singleRechargePayAmount, singleGiftPayAmount);

        BigDecimal rechargeConsumeAmount = memberInfo.getRechargeAmount();
        BigDecimal giftConsumeAmount = memberInfo.getGiftAmount();
        //本金消费金额
        BigDecimal singleConsumeLimit = consumeLimitService.singleConsumeLimit(memberGrade, rechargePayAmount, memberGrade.getRechargeSingleProportion(),
                singleRechargePayAmount, paymentList.stream()
                        .map(PosCashPayment::getRechargeAmount).filter(ObjectUtil::isNotNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
        if (singleConsumeLimit.compareTo(rechargeConsumeAmount) <= 0) {
            rechargeConsumeAmount = singleConsumeLimit;
        }
        //赠金消费金额
        singleConsumeLimit = consumeLimitService.singleConsumeLimit(memberGrade, giftPayAmount, memberGrade.getGiftSingleProportion(),
                singleGiftPayAmount, paymentList.stream()
                        .map(PosCashPayment::getGiftAmount).filter(ObjectUtil::isNotNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add));
        if (singleConsumeLimit.compareTo(giftConsumeAmount) <= 0) {
            giftConsumeAmount = singleConsumeLimit;
        }
        //判断当日限制金额
        //获取最大本金/赠金扣款金额
        MemberConsumeLimitQuery consumeLimit = consumeLimitService.calcAmount(memberGrade, memberInfo, remarks);
        if (StrUtil.isNotBlank(consumeLimit.getRemarks())) {
            if (StrUtil.isNotBlank(remarks)) {
                remarks = remarks.concat("<br/>");
            }
            remarks = remarks.concat(consumeLimit.getRemarks());
        }
        if (memberGrade.getIsConsumeLimit() != null && memberGrade.getIsConsumeLimit()) {
            if (consumeLimit.getRechargeAmount().compareTo(rechargeConsumeAmount) <= 0) {
                rechargeConsumeAmount = consumeLimit.getRechargeAmount();
            }
            if (consumeLimit.getGiftAmount().compareTo(giftConsumeAmount) <= 0) {
                giftConsumeAmount = consumeLimit.getGiftAmount();
            }
        }
        build.setConsumeLimitId(consumeLimit.getConsumeLimitId());
        build.setOldGiftAmount(memberInfo.getGiftAmount());
        build.setOldRechargeAmount(memberInfo.getRechargeAmount());
        String accountDeductType = memberGrade.getAccountDeductType();
        if (StrUtil.isBlank(accountDeductType)) {
            accountDeductType = AccountDeductTypeEnum.RECHARGE.getCode();
        }
        build.setAccountDeductType(accountDeductType);
        build.setAccountDeductTypeDesc(AccountDeductTypeEnum.get(accountDeductType).getDesc());
        if (AccountDeductTypeEnum.RECHARGE.getCode().equals(accountDeductType)) {
            if (rechargeConsumeAmount.compareTo(rechargePayAmount) <= 0) {
                build.setChangeRechargeAmount(rechargeConsumeAmount);
                if (singleRechargePayAmount.compareTo(rechargeConsumeAmount) <= 0) {
                    build.setChangeRechargeAmount(rechargeConsumeAmount);
                }
                BigDecimal subtract = amount.subtract(build.getChangeRechargeAmount())
                        .setScale(2, RoundingMode.HALF_UP);
                if (giftConsumeAmount.compareTo(subtract) >= 0) {
                    build.setChangeGiftAmount(subtract);
                } else {
                    build.setChangeGiftAmount(giftConsumeAmount);
                }
                build.setChangeGiftAmount(build.getChangeGiftAmount().compareTo(giftPayAmount) >= 0
                        ? giftPayAmount : build.getChangeGiftAmount());
            } else {
                build.setChangeRechargeAmount(amount);
                build.setChangeGiftAmount(BigDecimal.ZERO);
            }
        } else if (AccountDeductTypeEnum.GIFT.getCode().equals(accountDeductType)) {
            if (giftPayAmount.compareTo(amount) > 0) {
                giftPayAmount = amount;
            }
            if (giftConsumeAmount.compareTo(giftPayAmount) < 0) {
                build.setChangeGiftAmount(giftConsumeAmount);
                BigDecimal subtract = amount.subtract(build.getChangeGiftAmount())
                        .setScale(2, RoundingMode.HALF_UP);
                if (rechargeConsumeAmount.compareTo(subtract) >= 0) {
                    build.setChangeRechargeAmount(subtract);
                } else {
                    build.setChangeRechargeAmount(rechargeConsumeAmount);
                }
            } else {
                build.setChangeGiftAmount(giftPayAmount);
                BigDecimal subtract = amount.subtract(build.getChangeGiftAmount())
                        .setScale(2, RoundingMode.HALF_UP);
                if (rechargeConsumeAmount.compareTo(subtract) >= 0) {
                    build.setChangeRechargeAmount(subtract);
                } else {
                    build.setChangeRechargeAmount(rechargeConsumeAmount);
                }
            }
        } else if (AccountDeductTypeEnum.PROPORTION.getCode().equals(accountDeductType)) {
            //消费金额 乘以 (充值金额 除以 (充值金额+赠送金额))
            BigDecimal account = memberInfo.getRechargeAmount().add(memberInfo.getGiftAmount());
            if (amount.compareTo(account) > 0) {
                amount = account;
            }
            BigDecimal divide = BigDecimal.ZERO;
            if (account.compareTo(BigDecimal.ZERO) > 0) {
                divide = memberInfo.getGiftAmount()
                        .divide(account, 10, RoundingMode.HALF_UP);
            }

            BigDecimal giftAmount = BigDecimal.ZERO;
            //有赠送金额
            if (memberInfo.getGiftAmount().compareTo(BigDecimal.ZERO) >= 0) {
                //有本金
                if (memberInfo.getRechargeAmount().compareTo(BigDecimal.ZERO) > 0) {
                    //计算比例
                    //赠金扣除金额
                    giftAmount = amount.multiply(divide)
                            .setScale(2, RoundingMode.HALF_UP);
                } else {
                    giftAmount = giftPayAmount.compareTo(BigDecimal.ZERO) > 0
                            ? giftPayAmount : BigDecimal.ZERO;
                    if (memberInfo.getGiftAmount().compareTo(giftAmount) <= 0) {
                        giftAmount = memberInfo.getGiftAmount();
                    }
                }
            }
            build.setChangeGiftAmount(giftAmount.compareTo(giftPayAmount) <= 0 ? giftAmount : giftPayAmount);
            if (build.getChangeGiftAmount().compareTo(giftConsumeAmount) >= 0) {
                build.setChangeGiftAmount(giftConsumeAmount);
            }
            BigDecimal rechargeAmount = amount.subtract(build.getChangeGiftAmount())
                    .setScale(2, RoundingMode.HALF_UP);
            if (memberInfo.getRechargeAmount().compareTo(rechargeAmount) <= 0) {
                rechargeAmount = memberInfo.getRechargeAmount();
            }
            build.setChangeRechargeAmount(rechargeAmount.compareTo(amount) <= 0 ? rechargeAmount : amount);
            build.setChangeRechargeAmount(build.getChangeRechargeAmount().compareTo(memberInfo.getRechargeAmount()) <= 0
                    ? build.getChangeRechargeAmount() : memberInfo.getRechargeAmount());
            if (build.getChangeRechargeAmount().compareTo(rechargeConsumeAmount) >= 0) {
                build.setChangeRechargeAmount(rechargeConsumeAmount);
            }

        }
        build.setRemarks(remarks);
        if (build.getChangeRechargeAmount().compareTo(BigDecimal.ZERO) <= 0) {
            build.setChangeRechargeAmount(BigDecimal.ZERO);
        }
        if (build.getChangeGiftAmount().compareTo(BigDecimal.ZERO) <= 0) {
            build.setChangeGiftAmount(BigDecimal.ZERO);
        }
        build.setChangeGiftAmount(build.getChangeGiftAmount().setScale(2, RoundingMode.HALF_UP));
        build.setChangeRechargeAmount(build.getChangeRechargeAmount().setScale(2, RoundingMode.HALF_UP));
        return build;
    }

    @Override
    public List<String> getMchNoListByCurOrg() {
        return basePaymentTypeChannelService.list(Wraps.<BasePaymentTypeChannel>lbQ()
                        .eq(BasePaymentTypeChannel::getDeleteFlag, 0)
                        .eq(BasePaymentTypeChannel::getCreatedOrgId, ContextUtil.getCurrentCompanyId()))
                .stream().map(BasePaymentTypeChannel::getMchNo).collect(Collectors.toList());
    }

    @Override
    public void calcFeeRate(PosCashPayment cashPayment, BigDecimal feeRate) {
        //计算费率
        if (cashPayment.getChangeAmount() == null) {
            cashPayment.setChangeAmount(BigDecimal.ZERO);
        }
        cashPayment.setMchFeeRate(feeRate == null ? BigDecimal.ZERO : feeRate);
        BigDecimal pay = cashPayment.getAmount().subtract(cashPayment.getChangeAmount()).subtract(Objects.nonNull(cashPayment.getRefundAmount()) ? cashPayment.getRefundAmount() : BigDecimal.ZERO);
        cashPayment.setMchFeeAmount(BigDecimal.valueOf(AmountUtil.calPercentageFee(pay.multiply(BigDecimal.valueOf(100)).longValue(),
                cashPayment.getMchFeeRate())).divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP));
    }

    @Override
    public BigDecimal calcFeeRate(BigDecimal amount, BigDecimal feeRate) {
        if (amount == null || feeRate == null) {
            return BigDecimal.ZERO;
        }
        return BigDecimal.valueOf(AmountUtil.calPercentageFee(amount.multiply(BigDecimal.valueOf(100)).longValue(),
                feeRate)).divide(BigDecimal.valueOf(10000), 2, RoundingMode.HALF_UP);
    }
}


