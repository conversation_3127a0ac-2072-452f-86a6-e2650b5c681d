package top.kx.kxss.app.service.recharge.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.exception.BizException;
import top.kx.basic.exception.code.ExceptionCode;
import top.kx.basic.jackson.JsonUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.manager.cash.PosCashManager;
import top.kx.kxss.app.mapper.member.MemberMapper;
import top.kx.kxss.app.query.OrderAutoCancelConsumeQuery;
import top.kx.kxss.app.query.PerformanceCommissionConsumeQuery;
import top.kx.kxss.app.service.cash.PosCashServiceService;
import top.kx.kxss.app.service.recharge.RechargeService;
import top.kx.kxss.app.vo.employee.EmployeeResultVO;
import top.kx.kxss.app.vo.query.cash.PosCashIdQuery;
import top.kx.kxss.app.vo.query.cash.PosCashMemberQuery;
import top.kx.kxss.app.vo.result.cash.PosCashAmountVO;
import top.kx.kxss.app.vo.result.recharge.DepositCardResultVO;
import top.kx.kxss.app.vo.result.recharge.DepositCouponResultVO;
import top.kx.kxss.app.vo.result.recharge.DepositRuleResultVO;
import top.kx.kxss.app.vo.result.recharge.QueryRechargeDetailVO;
import top.kx.kxss.app.vo.save.recharge.RechargeEmployeeSaveVO;
import top.kx.kxss.app.vo.save.recharge.RechargeRegistrationSaveVO;
import top.kx.kxss.base.entity.biz.BaseBizLog;
import top.kx.kxss.base.entity.member.MemberBalanceChange;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.member.deposit.MemberDeposit;
import top.kx.kxss.base.entity.member.deposit.MemberDepositCard;
import top.kx.kxss.base.entity.member.deposit.MemberDepositCoupon;
import top.kx.kxss.base.entity.member.deposit.MemberDepositRule;
import top.kx.kxss.base.entity.member.grade.MemberGrade;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.manager.member.MemberBalanceChangeManager;
import top.kx.kxss.base.manager.member.MemberInfoManager;
import top.kx.kxss.base.manager.member.deposit.MemberDepositCardManager;
import top.kx.kxss.base.manager.member.deposit.MemberDepositCouponManager;
import top.kx.kxss.base.manager.member.deposit.MemberDepositManager;
import top.kx.kxss.base.manager.member.deposit.MemberDepositRuleManager;
import top.kx.kxss.base.manager.user.BaseEmployeeManager;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.service.member.grade.MemberGradeService;
import top.kx.kxss.base.service.table.BaseTableInfoService;
import top.kx.kxss.base.vo.query.member.grade.MemberGradeIdQuery;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.common.constant.RabbitMqConstant;
import top.kx.kxss.model.enumeration.base.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static top.kx.basic.context.ContextUtil.getCurrentCompanyId;
import static top.kx.basic.context.ContextUtil.getEmployeeId;

/**
 * <p>
 * 业务实现类
 * 充值相关接口
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class RechargeServiceImpl implements RechargeService {

    @Autowired
    private MemberInfoManager memberInfoManager;
    @Autowired
    private BaseTableInfoService tableService;
    @Autowired
    private PosCashManager posCashManager;
    @Autowired
    private MemberMapper memberMapper;
    @Autowired
    private MemberDepositManager depositManager;
    @Autowired
    private MemberDepositRuleManager depositRuleManager;
    @Autowired
    private MemberDepositCouponManager depositCouponManager;
    @Autowired
    private MemberDepositCardManager depositCardManager;
    @Autowired
    private PosCashServiceService posCashServiceService;
    @Autowired
    private BaseEmployeeManager employeeManager;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private MemberGradeService memberGradeService;
    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private RabbitTemplate template;
    @Autowired
    private BaseBizLogService baseBizLogManager;
    @Autowired
    private MemberBalanceChangeManager memberBalanceChangeManager;

    @Override
    public MemberInfoResultVO getMemberByPhone(String phone) {
        ArgumentAssert.notBlank(phone, "请输入手机号");
        MemberInfo memberInfo = memberInfoManager.getOne(Wraps.<MemberInfo>lbQ().eq(MemberInfo::getMobile, phone).last("limit 1"));
        if (null == memberInfo) {
            return null;
        }
        return BeanUtil.copyProperties(memberInfo, MemberInfoResultVO.class);
    }

    @Override
    public QueryRechargeDetailVO queryDetail(Long posCashId) {
        QueryRechargeDetailVO q = QueryRechargeDetailVO.builder().build();
        // 1. 充值结算信息 pos_cash
        PosCash posCash = posCashManager.getById(posCashId);
        try {
            if (null == posCash) {
                posCash = posCashManager.getOne(Wraps.<PosCash>lbQ().eq(PosCash::getOrgId, ContextUtil.getCurrentCompanyId())
                        .eq(PosCash::getType, PosCashTypeEnum.RECHARGE.getCode())
                        .eq(PosCash::getOrderSource, OrderSourceEnum.POS.getCode())
                        .eq(PosCash::getBillType, PosCashBillTypeEnum.REGULAR_SINGLE.getCode())
                        .eq(PosCash::getBillState, PosCashBillStateEnum.NO_SETTLED.getCode()));
            }
        } catch (Exception e) {
            log.error("posCash中存在多个已记录");
            throw new BizException("充值信息异常");
        } finally {
            if (null == posCash) {
                return q;
            }
        }
        q.setPosCash(posCash);
        // 2. 获取会员信息
        if (null != posCash.getMemberId()) {
            Map<String, Object> memberInfo = memberMapper.getMemberInfo(posCash.getMemberId());
            BigDecimal giftAmount = null == memberInfo.get("giftAmount") ? new BigDecimal(0) : (BigDecimal) memberInfo.get("giftAmount");
            BigDecimal rechargeAmount = null == memberInfo.get("rechargeAmount") ? new BigDecimal(0) : (BigDecimal) memberInfo.get("rechargeAmount");
            memberInfo.put("balance", giftAmount.add(rechargeAmount));
            q.setMemberInfo(memberInfo);
        }
        // 调用结算接口 展示费用信息 TODO
        q.setOrgTotal(posCash.getAmount());
        q.setAmountTotal(posCash.getAmount());
        if (ObjectUtil.isNotNull(posCash.getDepositRuleId())) {
            MemberDepositRule depositRule = depositRuleManager.getById(posCash.getDepositRuleId());
            DepositRuleResultVO depositRuleResultVO = BeanUtil.copyProperties(depositRule, DepositRuleResultVO.class);
            if (ObjectUtil.isNull(depositRuleResultVO)) {
                return null;
            }
            List<DepositCouponResultVO> couponResultVOList = depositCouponManager.list(Wraps.<MemberDepositCoupon>lbQ()
                            .eq(MemberDepositCoupon::getDepositRuleId, posCash.getDepositRuleId()))
                    .stream().map(v -> BeanUtil.copyProperties(v, DepositCouponResultVO.class)).collect(Collectors.toList());
            depositRuleResultVO.setCouponList(couponResultVOList);
            q.setDepositRuleVO(depositRuleResultVO);

        }
        if (ObjectUtil.isNotNull(posCash.getEmployeeId())) {
            BaseEmployee baseEmployee = employeeManager.getById(posCash.getEmployeeId());
            q.setEmployee(BeanUtil.copyProperties(baseEmployee, EmployeeResultVO.class));
        }
        // 调用结算接口 展示费用信息 TODO
        Map<String, Object> param = new HashMap<>();
        param.put("cashId", posCash.getId());
        param.put("needUpdate", false);
        LinkedHashMap<String, Object> posResult = posCashServiceService.tablePosCash(param);
        q.setPosResult(posResult);
//        calcAmount(posCashId);
        return q;
    }


    @Override
    public List<DepositRuleResultVO> depositList(MemberGradeIdQuery query) {
        //获取储值配置
        MemberDeposit deposit = depositManager.getOne(Wraps.<MemberDeposit>lbQ().eq(MemberDeposit::getCreatedOrgId, ContextUtil.getCurrentCompanyId()));
        ArgumentAssert.notNull(deposit, "请设置储值规则");
        //获取规则信息
        List<MemberDepositRule> depositRuleList = depositRuleManager.list(Wraps.<MemberDepositRule>lbQ()
                .eq(MemberDepositRule::getDepositId, deposit.getId())
                .and(ObjectUtil.isNotNull(query.getGradeId()), wq -> wq.isNull(MemberDepositRule::getGradeIds)
                        .or().apply("JSON_CONTAINS(grade_ids, '" + query.getGradeId().toString() + "')")));
        //获取商品信息
        List<DepositRuleResultVO> resultVOList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(depositRuleList)) {
            List<Long> depositRuleIds = depositRuleList.stream().map(MemberDepositRule::getId).collect(Collectors.toList());
            Map<Long, List<DepositCouponResultVO>> depositCouponMap = CollUtil.isNotEmpty(depositRuleIds) ? depositCouponManager.list(Wraps.<MemberDepositCoupon>lbQ().in(MemberDepositCoupon::getDepositRuleId, depositRuleIds))
                    .stream().map(v -> BeanUtil.copyProperties(v, DepositCouponResultVO.class)).collect(Collectors.toList())
                    .stream().collect(Collectors.groupingBy(DepositCouponResultVO::getDepositRuleId)) : new HashMap<>();
            Map<Long, List<DepositCardResultVO>> depositCardMap = CollUtil.isNotEmpty(depositRuleIds) ? depositCardManager.list(Wraps.<MemberDepositCard>lbQ()
                            .in(MemberDepositCard::getDepositRuleId, depositRuleIds))
                    .stream().map(v -> BeanUtil.copyProperties(v, DepositCardResultVO.class)).collect(Collectors.toList())
                    .stream().collect(Collectors.groupingBy(DepositCardResultVO::getDepositRuleId)) : new HashMap<>();

            resultVOList = depositRuleList.stream().map(depositRule -> {
                return DepositRuleResultVO.builder().id(depositRule.getId())
                        .name(depositRule.getName())
                        .remarks(depositRule.getRemarks()).rechargeAmount(depositRule.getRechargeAmount())
                        .giftAmount(depositRule.getGiftAmount()).isDef(false)
                        .couponList(depositCouponMap.get(depositRule.getId()))
                        .cardList(depositCardMap.get(depositRule.getId()))
                        .build();
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(resultVOList)) {
                resultVOList.sort(Comparator.comparing(DepositRuleResultVO::getRechargeAmount));
            }
        }
        //封装自定义数据
        if (deposit.getIsDef()) {
            resultVOList.add(DepositRuleResultVO.builder().remarks("自定义金额").rechargeAmount(BigDecimal.ZERO)
                    .giftAmount(BigDecimal.ZERO).isDef(true).couponList(null).build());
        }
        if (deposit.getIsDefGift()) {
            resultVOList.add(DepositRuleResultVO.builder().remarks("自定义赠金").rechargeAmount(BigDecimal.ZERO)
                    .giftAmount(BigDecimal.ZERO).isDef(true).isDefGift(true).couponList(null).build());
        }
        return resultVOList;
    }

    @Override
    public Boolean bindMember(PosCashMemberQuery query) {
        return ObjectUtil.isNotNull(getRechargePosCash(RechargeRegistrationSaveVO.builder()
                .memberId(query.getMemberId()).posCashId(query.getPosCashId()).build()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveDeposit(RechargeRegistrationSaveVO model) {
        ArgumentAssert.isFalse(ObjectUtil.isNull(model.getPosCashId()), "参数检验失败");
        PosCash posCash = getRechargePosCash(model);
        ArgumentAssert.isFalse(ObjectUtil.isNull(posCash.getMemberId()), "请选择会员");
        calcAmount(posCash.getId());
        return ObjectUtil.isNotNull(posCash);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PosCash saveDepositNoCash(RechargeRegistrationSaveVO model) {
        String memberId = ContextUtil.getSn();
        if (model.getMemberId() != null) {
            memberId = model.getMemberId().toString();
        }
        boolean lock = false;
        try {
            lock = distributedLock.lock(memberId + "_ORDER_RECHARGE", 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            PosCash posCash = getRechargePosCash(model);
            ArgumentAssert.isFalse(ObjectUtil.isNull(posCash.getMemberId()), "请选择会员");
            return posCash;
        } finally {
            if (lock) {
                distributedLock.releaseLock(memberId + "_ORDER_RECHARGE");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delDeposit(PosCashIdQuery query) {
        PosCash posCash = posCashManager.getById(query.getPosCashId());
        ArgumentAssert.notNull(posCash, "结算单不存在");
        if (ObjectUtil.isNull(posCash.getMemberId())) {
            posCashManager.deletePosCash(query.getPosCashId());
        }
        calcAmount(posCash.getId());
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveEmp(RechargeEmployeeSaveVO employeeSaveVO) {
        ArgumentAssert.isFalse(ObjectUtil.isNull(employeeSaveVO.getPosCashId()), "请选择会员");
        PosCash posCash = posCashManager.getById(employeeSaveVO.getPosCashId());
        ArgumentAssert.notNull(posCash, "结算单不存在");
        List<Long> employeeIds = new ArrayList<>();
        Long sourceEmployeeId = posCash.getEmployeeId();
        Long newEmployeeId = employeeSaveVO.getEmployeeId();
        employeeIds.add(posCash.getEmployeeId());
        employeeIds.add(employeeSaveVO.getEmployeeId());
        posCash.setEmployeeId(employeeSaveVO.getEmployeeId());
        posCash.setUpdatedBy(ContextUtil.getUserId());
        posCash.setUpdatedTime(LocalDateTime.now());
        boolean flag = posCashManager.updateById(posCash);
        List<BaseEmployeeResultVO> employeeResultVOList = employeeManager.findList(Wraps.<BaseEmployee>lbQ().in(BaseEmployee::getId, employeeIds));
        String sourceEmployee = employeeResultVOList.stream().filter(v -> v.getId().equals(sourceEmployeeId)).findFirst().map(BaseEmployeeResultVO::getName).orElse("");
        String paramEmployee = employeeResultVOList.stream().filter(v -> v.getId().equals(newEmployeeId)).findFirst().map(BaseEmployeeResultVO::getName).orElse("");

        baseBizLogManager.createBizLog(BaseBizLog.builder()
                .tenantId(ContextUtil.getTenantId())
                .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                .description("修改充值销售人" + "【" + sourceEmployee + ">>>" + paramEmployee + "】")
                .bizModule(BizLogModuleEnum.SETTING_COMMENTER.getCode())
                .type(BizLogTypeEnum.CREATED.getCode())
                .sourceId(posCash.getId()).remarks("-")
                .sn(ContextUtil.getSn())
                .build());

        template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.PERFORMANCE_COMMISSION,
                JsonUtil.toJson(PerformanceCommissionConsumeQuery.builder()
                        .posCashId(employeeSaveVO.getPosCashId())
                        .orgId(ContextUtil.getCurrentCompanyId())
                        .tenantId(ContextUtil.getTenantId())
                        .build()));
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean registration(RechargeRegistrationSaveVO model) {
        ArgumentAssert.isFalse(ObjectUtil.isNull(model.getPosCashId()), "请选择会员");
        PosCash posCash = getRechargePosCash(model);
        ArgumentAssert.isFalse(ObjectUtil.isNull(posCash.getMemberId()), "请选择会员");
        posCash.setBillType(PosCashBillTypeEnum.REGISTRATION.getCode());
        posCash.setRegistrationTime(LocalDateTime.now());
        posCash.setUpdatedTime(LocalDateTime.now());
        posCash.setUpdatedBy(ContextUtil.getUserId());
        posCash.setUpdatedTime(LocalDateTime.now());
        return posCashManager.updateById(posCash);
    }

    @Override
    public Boolean reset(PosCashIdQuery query) {
        PosCash posCash = posCashManager.getOne(Wraps.<PosCash>lbQ().eq(PosCash::getOrgId, getCurrentCompanyId())
                .eq(PosCash::getType, PosCashTypeEnum.RECHARGE.getCode())
                .eq(PosCash::getBillType, PosCashBillTypeEnum.REGULAR_SINGLE.getCode())
                .eq(PosCash::getBillState, PosCashBillStateEnum.NO_SETTLED.getCode()));
        ArgumentAssert.notNull(posCash, "结算单不存在！");
        //订单删除
        posCashManager.deletePosCash(posCash.getId());

        return true;
    }

    @Override
    public Boolean delEmp(PosCashIdQuery query) {
        PosCash posCash = posCashManager.getOne(Wraps.<PosCash>lbQ()
                .eq(PosCash::getOrgId, getCurrentCompanyId())
                .eq(PosCash::getDeleteFlag, 0)
                .eq(PosCash::getId, query.getPosCashId()));
        ArgumentAssert.notNull(posCash, "结算单不存在！");
        Long employeeId = posCash.getEmployeeId();
        posCash.setEmployeeId(null);
        posCash.setUpdatedTime(LocalDateTime.now());
        posCash.setUpdatedBy(ContextUtil.getUserId());
        boolean b = posCashManager.updateById(posCash);
        if (Objects.nonNull(employeeId)) {
            BaseEmployee employee = employeeManager.getById(employeeId);
            baseBizLogManager.createBizLog(BaseBizLog.builder()
                    .tenantId(ContextUtil.getTenantId())
                    .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                    .description("移除充值销售人" + "【" + employee.getName()+ "】")
                    .bizModule(BizLogModuleEnum.SETTING_COMMENTER.getCode())
                    .type(BizLogTypeEnum.CREATED.getCode())
                    .sourceId(posCash.getId()).remarks("-")
                    .sn(ContextUtil.getSn())
                    .build());
        }

        template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.PERFORMANCE_COMMISSION,
                JsonUtil.toJson(PerformanceCommissionConsumeQuery.builder()
                        .posCashId(posCash.getId())
                        .orgId(ContextUtil.getCurrentCompanyId())
                        .tenantId(ContextUtil.getTenantId())
                        .build()));
        return b;
    }

    @Override
    public List<BaseEmployeeResultVO> empList() {
        return employeeManager.list(Wraps.<BaseEmployee>lbQ().inSql(BaseEmployee::getId, "select distinct employee_id from base_employee_org_rel where  delete_flag = 0 and org_id =" + ContextUtil.getCurrentCompanyId()))
                .stream().map(v -> BeanUtil.copyProperties(v, BaseEmployeeResultVO.class)).collect(Collectors.toList());
    }

    private PosCash getRechargePosCash(RechargeRegistrationSaveVO model) {
        if (model.getIsDef() != null && model.getIsDef()) {
            ArgumentAssert.isFalse(ObjectUtil.isNull(model.getRechargeAmount())
                    || model.getRechargeAmount().compareTo(BigDecimal.ZERO) < 0
                    || ObjectUtil.isNull(model.getGiftAmount())
                    || model.getGiftAmount().compareTo(BigDecimal.ZERO) < 0, "自定义金额输入有误");
        }
        if (model.getMemberId() == null) {
            model.setMemberId(ContextUtil.getMemberId());
        }
        ArgumentAssert.notNull(model.getMemberId(), "未绑定会员");
        PosCash posCash = null;
        // 1. 保存pos_cash
        if (ObjectUtil.isNotNull(model.getPosCashId())) {
            posCash = posCashManager.getById(model.getPosCashId());
        }
        //删除以前的充值记录
        posCashManager.update(Wraps.<PosCash>lbU()
                .set(PosCash::getDeleteFlag, 1)
                .eq(PosCash::getDeleteFlag, 0)
                .le(PosCash::getCreatedTime, DateUtils.format(LocalDateTime.now().minusMinutes(15),
                        DateUtils.DEFAULT_DATE_TIME_FORMAT))
                .eq(PosCash::getType, PosCashTypeEnum.RECHARGE.getCode())
                .in(PosCash::getBillType, Arrays.asList(PosCashBillTypeEnum.REGISTRATION.getCode(),
                        PosCashBillTypeEnum.REGULAR_SINGLE.getCode()))
                .eq(PosCash::getBillState, PosCashBillStateEnum.NO_PAY.getCode())
        );
        if (ObjectUtil.isNull(posCash)) {
            MemberInfo memberInfo = memberInfoManager.getOne(Wraps.<MemberInfo>lbQ().eq(MemberInfo::getId, model.getMemberId())
                    .eq(MemberInfo::getDeleteFlag, 0));
            ArgumentAssert.isTrue(Objects.nonNull(memberInfo), "会员不存在,无法充值");
            // 查询会员等级， 是否满足续充条件
            if (Objects.nonNull(model.getIsDef()) && model.getIsDef()) {
                // 只有自定义充值才需要， 自定义赠送不需要
                if (Objects.isNull(model.getIsDefGift()) || !model.getIsDefGift()) {
                    MemberGrade memberGrade = memberGradeService.getGradeById(memberInfo.getGradeId());
                    ArgumentAssert.isTrue(Objects.nonNull(memberGrade), "会员等级不存在,无法充值");

                    // 只有当最小充值金额不为空且大于0时才校验
                    BigDecimal minRechargeAmount = memberGrade.getMinRechargeAmount();
                    if (Objects.nonNull(minRechargeAmount) && BigDecimal.ZERO.compareTo(minRechargeAmount) < 0) {
                        if (model.getRechargeAmount().compareTo(minRechargeAmount) < 0) {
                            throw BizException.wrapTips("充值失败", "不满足起充金额，请到小程序-会员等级，修改起充金额");
                        }
                    }
                }

            }
            posCash = new PosCash();
            // 类型
            posCash.setType(PosCashTypeEnum.RECHARGE.getCode());
            // 单据code
            posCash.setCode(tableService.randomOrderCode());
            // 单据日期
            posCash.setBillDate(LocalDate.now());
            // 单据状态
            posCash.setBillState(PosCashBillStateEnum.NO_PAY.getCode());
            posCash.setBillType(PosCashBillTypeEnum.REGULAR_SINGLE.getCode());
            // 门店id
            posCash.setOrgId(getCurrentCompanyId());
            posCash.setCreatedOrgId(getCurrentCompanyId());
            posCash.setCreatedEmp(getEmployeeId());
            // 员工id
            posCash.setEmployeeId(model.getEmployeeId());
            // 是否首充
            List<PosCash> posCashList = posCashManager.list(Wraps.<PosCash>lbQ().eq(PosCash::getMemberId, model.getMemberId())
                    .eq(PosCash::getType, PosCashTypeEnum.RECHARGE.getCode())
                    .eq(PosCash::getBillState, PosCashBillStateEnum.COMPLETE.getCode())
                    .in(PosCash::getBillType, Arrays.asList(PosCashBillTypeEnum.REGULAR_SINGLE.getCode(),
                            PosCashBillTypeEnum.REGISTRATION.getCode(),
                            PosCashBillTypeEnum.COUNTER_CHECKOUT.getCode())));
            // 查询余额变动记录
            long memberBalanceChangeCount = memberBalanceChangeManager.count(Wraps.<MemberBalanceChange>lbQ().eq(MemberBalanceChange::getMemberId, memberInfo.getId()));
            // 查询 账户余额, 必须是没有充值订单,并且是没有钱的才算是首充
            if (CollUtil.isEmpty(posCashList) && memberBalanceChangeCount <= 0) {
                posCash.setIsFirstRecharge(true);
            } else {
                posCash.setIsFirstRecharge(false);
            }

            // 支付名
            posCash.setPayName("创建充值，待支付");
            posCash.setUpdatedTime(null);
            posCash.setOrderSource(OrderSourceEnum.POS.getCode());
        }
        savePosCash(posCash, model);
        rabbitTemplate.convertAndSend(RabbitMqConstant.COUNTDOWN_ORDER_EXCHANGE, RabbitMqConstant.COUNTDOWN_ORDER_DEAD_ROUTING_KEY,
                JSON.toJSONString(OrderAutoCancelConsumeQuery.builder()
                        .tenantId(ContextUtil.getTenantId())
                        .orgId(ContextUtil.getCurrentCompanyId())
                        .posCashId(posCash.getId())
                        .build()), message -> {
                    //5分钟自动取消
                    message.getMessageProperties().setExpiration((5 * 60000) + "");
                    return message;
                });
        return posCash;
    }


    private void savePosCash(PosCash posCash, RechargeRegistrationSaveVO model) {
        //金额
        if (model.getIsDef() != null && !model.getIsDef()) {
            MemberDepositRule depositRule = depositRuleManager.getById(model.getDepositRuleId());
            ArgumentAssert.notNull(depositRule, "储值规则不存在！");
            posCash.setAmount(depositRule.getRechargeAmount());
            posCash.setGiftAmount(depositRule.getGiftAmount());
        } else {
            posCash.setAmount(model.getRechargeAmount());
            posCash.setGiftAmount(model.getGiftAmount());
        }
        if (model.getAmount() != null) {
            long payment = posCash.getAmount().multiply(new BigDecimal(100)).longValue();
            long payAmount = model.getAmount();
            ArgumentAssert.isFalse(payAmount > payment, "支付金额不能超过应收金额");
        }
        if (ObjectUtil.isNull(posCash.getAmount())) {
            posCash.setAmount(BigDecimal.ZERO);
        }
        posCash.setDiscountAmount(BigDecimal.ZERO);
        posCash.setPayment(posCash.getAmount());
        posCash.setPaid(BigDecimal.ZERO);
        posCash.setUnpaid(posCash.getAmount());
        posCash.setDepositRuleId(model.getDepositRuleId());
        posCash.setMemberId(model.getMemberId());
        if (StrUtil.isNotBlank(model.getRemarks())) {
            JSONObject remakrsObj = new JSONObject();
            remakrsObj.put("rechargeRemarks", model.getRemarks());
            posCash.setRemarks(remakrsObj.toJSONString());
        }
        if (ObjectUtil.isNull(posCash.getGiftAmount())) {
            posCash.setGiftAmount(BigDecimal.ZERO);
        }
        boolean suc;
        if (ObjectUtil.isNotNull(posCash.getId())) {
            posCash.setUpdatedTime(LocalDateTime.now());
            posCash.setUpdatedBy(ContextUtil.getUserId());
            suc = posCashManager.updateById(posCash);
        } else {
            posCash.setCreatedTime(LocalDateTime.now());
            posCash.setIsTurn(false);
            posCash.setCreatedBy(ContextUtil.getUserId());
            posCash.setRefundAmount(BigDecimal.ZERO);
            posCash.setSn(ContextUtil.getSn());
            suc = posCashManager.save(posCash);
        }
        ArgumentAssert.isFalse(!suc, "操作失败！");
    }


    /**
     * 计算价格
     *
     * @param posCashId
     */
    private void calcAmount(Long posCashId) {
        PosCash posCash = posCashManager.getById(posCashId);
        // 8. 调用结算接口 展示费用信息 TODO
        Map<String, Object> param = new HashMap<>();
        param.put("cashId", posCash.getId());
        param.put("needUpdate", false);
        LinkedHashMap<String, Object> posResult = posCashServiceService.tablePosCash(param);
        PosCashAmountVO totalVo = (PosCashAmountVO) posResult.get("totalCash");
        if (posCash.getAmount() == null || posCash.getAmount().compareTo(totalVo.getAmount()) != 0) {
            posCash.setAmount(totalVo.getAmount());
            posCash.setDiscountAmount(totalVo.getCouponAmount());
            posCash.setPayment(totalVo.getResultAmount());
            posCash.setUpdatedBy(ContextUtil.getUserId());
            posCash.setUpdatedTime(LocalDateTime.now());
            posCashManager.updateById(posCash);
        }

    }
}
