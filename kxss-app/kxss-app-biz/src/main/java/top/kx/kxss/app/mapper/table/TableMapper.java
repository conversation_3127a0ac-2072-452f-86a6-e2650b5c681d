package top.kx.kxss.app.mapper.table;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.base.entity.table.BaseTableInfo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * 台桌信息表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-03-10 23:00:01
 * @create [2023-03-10 23:00:01] [<PERSON>] [代码生成器生成]
 */
@Repository
public interface TableMapper extends SuperMapper<BaseTableInfo> {

    List<Map<String, Object>> countWithStatus();

    List<Map<String, Object>> getSettingsByTable(@Param(value = "tableId") Long tableId);

}


