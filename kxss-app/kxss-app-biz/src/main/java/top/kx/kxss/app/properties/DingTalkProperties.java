package top.kx.kxss.app.properties;


import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import top.kx.basic.constant.Constants;


/**
 * <AUTHOR>
 */
@Setter
@Getter
@ConfigurationProperties(prefix = DingTalkProperties.PREFIX)
@RefreshScope
public class DingTalkProperties {
    public static final String PREFIX = Constants.PROJECT_PREFIX + ".dingtalk";

    /**
     * URL
     */
    private String url;
    /**
     * secret
     */
    private String secret;


}
