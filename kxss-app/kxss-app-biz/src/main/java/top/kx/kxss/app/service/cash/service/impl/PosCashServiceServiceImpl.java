package top.kx.kxss.app.service.cash.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.app.manager.cash.service.PosCashServiceManager;
import top.kx.kxss.app.service.cash.service.PosCashServiceService;
import top.kx.kxss.app.vo.query.cash.service.PosCashServicePageQuery;
import top.kx.kxss.app.vo.result.ProfitResultVO;
import top.kx.kxss.app.vo.result.cash.service.PosCashServiceResultVO;
import top.kx.kxss.app.vo.save.cash.service.PosCashServiceSaveVO;
import top.kx.kxss.app.vo.update.cash.service.PosCashServiceUpdateVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillTypeEnum;
import top.kx.kxss.model.enumeration.base.PosCashTypeEnum;

import java.util.List;

/**
 * <p>
 * 业务实现类
 * 收银-服务子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:44:58
 * @create [2023-04-19 14:44:58] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class PosCashServiceServiceImpl extends SuperServiceImpl<PosCashServiceManager, Long, PosCashService, PosCashServiceSaveVO,
        PosCashServiceUpdateVO, PosCashServicePageQuery, PosCashServiceResultVO> implements PosCashServiceService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBatch(List<PosCashService> posCashServiceList) {
        superManager.updateBatchById(posCashServiceList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchById(List<PosCashService> cashProducts) {
        return superManager.updateBatchById(cashProducts);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatchByIds(List<PosCashService> cashServiceList) {
        return superManager.removeBatchByIds(cashServiceList);
    }

    @Override
    public ProfitResultVO findProfit(List<Long> posCashIdList) {
        return superManager.findProfit(posCashIdList);
    }

    @Override
    public Boolean checkIsUse(List<Long> longs) {
        return superManager.count(Wraps.<PosCashService>lbQ()
                .in(PosCashService::getServiceId, longs)
                .inSql(PosCashService::getCashId,
                        " select p.id from pos_cash p where p.delete_flag = 0 "
                                + " and p.type_ != "
                                + PosCashTypeEnum.RECHARGE.getCode()
                                + " and p.bill_type not in ('"
                                + PosCashBillTypeEnum.CANCELLATION.getCode()
                                + "','" + PosCashBillTypeEnum.CHARGEBACK.getCode() + "') and p.bill_state not  in ('"
                                + PosCashBillStateEnum.COMPLETE.getCode() + "','"
                                + PosCashBillStateEnum.REFUNDED.getCode()
                                + "','" + PosCashBillStateEnum.PART_REFUND.getCode()
                                + "')")) > 0;
    }

    @Override
    public Boolean checkEmpIsUse(List<Long> longs) {
        return superManager.count(Wraps.<PosCashService>lbQ()
                .in(PosCashService::getEmployeeId, longs)
                .inSql(PosCashService::getCashId,
                        " select p.id from pos_cash p where p.delete_flag = 0 "
                                + " and p.type_ != "
                                + PosCashTypeEnum.RECHARGE.getCode()
                                + " and p.bill_type not in ('"
                                + PosCashBillTypeEnum.CANCELLATION.getCode()
                                + "','" + PosCashBillTypeEnum.CHARGEBACK.getCode() + "') and p.bill_state not  in ('"
                                + PosCashBillStateEnum.COMPLETE.getCode() + "','"
                                + PosCashBillStateEnum.REFUNDED.getCode()
                                + "','" + PosCashBillStateEnum.PART_REFUND.getCode()
                                + "')")) > 0;
    }

    @Override
    @Transactional
    public boolean removeById(Long id) {
        return superManager.removeById(id);
    }

    @Override
    public long count(LbQueryWrap<PosCashService> eq) {
        return superManager.count(eq);
    }

    @Override
    @Transactional
    public boolean update(LbUpdateWrap<PosCashService> eq) {
        return superManager.update(eq);
    }

    @Override
    @Transactional
    public boolean updateById(PosCashService posCashService) {
        return superManager.updateById(posCashService);
    }

    @Override
    public PosCashService getOne(LbQueryWrap<PosCashService> eq) {
        return superManager.getOne(eq);
    }

    @Override
    @Transactional
    public boolean save(PosCashService cashService) {
        if (StringUtils.isBlank(cashService.getSn())) {
            cashService.setSn(ContextUtil.getSn());
        }
        return superManager.save(cashService);
    }

    @Override
    public Boolean checkServiceActivity(List<Long> longs) {
        return superManager.count(Wraps.<PosCashService>lbQ()
                .isNotNull(PosCashService::getServiceActivityId)
                .eq(PosCashService::getDeleteFlag, 0)
                .in(PosCashService::getServiceActivityId, longs)
                .inSql(PosCashService::getCashId,
                        " select p.id from pos_cash p where p.delete_flag = 0 "
                                + " and p.type_ != "
                                + PosCashTypeEnum.RECHARGE.getCode()
                                + " and p.bill_type not in ('"
                                + PosCashBillTypeEnum.CANCELLATION.getCode()
                                + "','" + PosCashBillTypeEnum.CHARGEBACK.getCode() + "') and p.bill_state not  in ('"
                                + PosCashBillStateEnum.COMPLETE.getCode() + "','"
                                + PosCashBillStateEnum.REFUNDED.getCode()
                                + "','" + PosCashBillStateEnum.PART_REFUND.getCode()
                                + "')")) > 0;
    }
}


