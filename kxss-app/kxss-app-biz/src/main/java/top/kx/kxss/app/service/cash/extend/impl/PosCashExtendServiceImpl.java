package top.kx.kxss.app.service.cash.extend.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.app.entity.cash.extend.PosCashExtend;
import top.kx.kxss.app.manager.cash.extend.PosCashExtendManager;
import top.kx.kxss.app.service.cash.extend.PosCashExtendService;
import top.kx.kxss.app.vo.query.cash.extend.PosCashExtendPageQuery;
import top.kx.kxss.app.vo.result.cash.extend.PosCashExtendResultVO;
import top.kx.kxss.app.vo.save.cash.extend.PosCashExtendSaveVO;
import top.kx.kxss.app.vo.update.cash.extend.PosCashExtendUpdateVO;
import top.kx.kxss.common.constant.DsConstant;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 业务实现类
 * 订单续时
 * </p>
 *
 * <AUTHOR>
 * @date 2023-10-21 17:10:25
 * @create [2023-10-21 17:10:25] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class PosCashExtendServiceImpl extends SuperServiceImpl<PosCashExtendManager, Long, PosCashExtend, PosCashExtendSaveVO,
        PosCashExtendUpdateVO, PosCashExtendPageQuery, PosCashExtendResultVO> implements PosCashExtendService {

    @Override
    public PosCashExtend getOne(LbQueryWrap<PosCashExtend> eq) {
        return superManager.getOne(eq);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(PosCashExtend posCashExtend) {
        return superManager.save(posCashExtend);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeById(Long id) {
        return superManager.removeById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(PosCashExtend posCashExtend) {
        posCashExtend.setUpdatedTime(LocalDateTime.now());
        return superManager.updateById(posCashExtend);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchById(List<PosCashExtend> posCashExtendList) {
        return superManager.updateBatchById(posCashExtendList);
    }
}


