package top.kx.kxss.app.service.cash;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.app.entity.cash.PosCashOperateVerify;
import top.kx.kxss.app.vo.query.cash.PosCashOperateVerifyPageQuery;
import top.kx.kxss.app.vo.result.cash.PosCashOperateVerifyResultVO;
import top.kx.kxss.app.vo.save.cash.PosCashOperateVerifySaveVO;
import top.kx.kxss.app.vo.update.cash.PosCashOperateVerifyUpdateVO;


/**
 * <p>
 * 业务接口
 * pos操作验证记录
 * </p>
 *
 * <AUTHOR>
 * @date 2023-12-21 12:18:16
 * @create [2023-12-21 12:18:16] [yh] [代码生成器生成]
 */
public interface PosCashOperateVerifyService extends SuperService<Long, PosCashOperateVerify, PosCashOperateVerifySaveVO,
    PosCashOperateVerifyUpdateVO, PosCashOperateVerifyPageQuery, PosCashOperateVerifyResultVO> {
    void checkAndSave(PosCashOperateVerifySaveVO saveVO);

    void logicDeleteByCashIdAndMemberId(Long cashId,Long memberId);

}


