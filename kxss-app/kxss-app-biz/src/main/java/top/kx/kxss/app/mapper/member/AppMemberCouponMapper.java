package top.kx.kxss.app.mapper.member;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.base.entity.member.coupon.MemberCoupon;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * 会员绑定优惠劵信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-18 15:36:12
 * @create [2023-04-18 15:36:12] [dou] [代码生成器生成]
 */
@Repository
public interface AppMemberCouponMapper extends SuperMapper<MemberCoupon> {

    List<MemberCoupon> getUseingMemberCoupon(@Param(value = "cashId") Long cashId);

}


