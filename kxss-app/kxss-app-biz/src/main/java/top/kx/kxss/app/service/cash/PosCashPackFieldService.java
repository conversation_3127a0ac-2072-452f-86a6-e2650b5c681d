package top.kx.kxss.app.service.cash;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.app.entity.cash.PosCashPackField;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.vo.save.cash.PosCashPackFieldSaveVO;
import top.kx.kxss.app.vo.update.cash.PosCashPackFieldUpdateVO;
import top.kx.kxss.app.vo.result.cash.PosCashPackFieldResultVO;
import top.kx.kxss.app.vo.query.cash.PosCashPackFieldPageQuery;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 包场信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-22 11:37:26
 * @create [2024-04-22 11:37:26] [dou] [代码生成器生成]
 */
public interface PosCashPackFieldService extends SuperService<Long, PosCashPackField, PosCashPackFieldSaveVO,
    PosCashPackFieldUpdateVO, PosCashPackFieldPageQuery, PosCashPackFieldResultVO> {

    boolean save(PosCashPackField packField);

    PosCashPackField getOne(LbQueryWrap<PosCashPackField> eq);

    boolean updateBatchById(List<PosCashPackField> posCashPackFieldList);

    List<PosCashTable> refreshTable(List<PosCashTable> packFieldTableList, Boolean isStop,
                                    List<PosCashPackField> packFieldList);
}


