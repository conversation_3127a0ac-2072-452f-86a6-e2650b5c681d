package top.kx.kxss.app.service.cash.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.app.entity.cash.PosCashOperateVerify;
import top.kx.kxss.app.manager.cash.PosCashOperateVerifyManager;
import top.kx.kxss.app.service.cash.PosCashOperateVerifyService;
import top.kx.kxss.app.vo.query.cash.PosCashOperateVerifyPageQuery;
import top.kx.kxss.app.vo.result.cash.PosCashOperateVerifyResultVO;
import top.kx.kxss.app.vo.save.cash.PosCashOperateVerifySaveVO;
import top.kx.kxss.app.vo.update.cash.PosCashOperateVerifyUpdateVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.pos.VerifyTypeEnum;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 业务实现类
 * pos操作验证记录
 * </p>
 *
 * <AUTHOR>
 * @date 2023-12-21 12:18:16
 * @create [2023-12-21 12:18:16] [yh] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class PosCashOperateVerifyServiceImpl extends SuperServiceImpl<PosCashOperateVerifyManager, Long, PosCashOperateVerify, PosCashOperateVerifySaveVO,
        PosCashOperateVerifyUpdateVO, PosCashOperateVerifyPageQuery, PosCashOperateVerifyResultVO> implements PosCashOperateVerifyService {


    @Transactional
    @Override
    public void checkAndSave(PosCashOperateVerifySaveVO saveVO) {
        //密码不保存
        if(Objects.equals(saveVO.getVerifyType(), VerifyTypeEnum.PASSWORD.getCode())){
            saveVO.setVerifyValue(null);
        }
        saveVO.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        save(saveVO);
    }

    @Transactional
    @Override
    public void logicDeleteByCashIdAndMemberId(Long cashId, Long memberId) {
        LbQueryWrap<PosCashOperateVerify> wrap = Wraps.<PosCashOperateVerify>lbQ()
                .eq(PosCashOperateVerify::getMemberId, memberId)
                .eq(PosCashOperateVerify::getBizId, cashId)
                .eq(PosCashOperateVerify::getDeleteFlag, 0);
        superManager.update(PosCashOperateVerify.builder().deleteFlag(1).build(), wrap);
    }
}


