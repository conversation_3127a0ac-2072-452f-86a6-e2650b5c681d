package top.kx.kxss.app.service.thail;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.thail.BaseThail;
import top.kx.kxss.base.vo.query.thail.BaseThailPageQuery;
import top.kx.kxss.base.vo.result.thail.BaseThailResultVO;
import top.kx.kxss.base.vo.save.thail.BaseThailSaveVO;
import top.kx.kxss.base.vo.update.thail.BaseThailUpdateVO;


/**
 * <p>
 * 业务接口
 * 套餐数据
 * </p>
 */
public interface ThailService extends SuperService<Long, BaseThail, BaseThailSaveVO,
        BaseThailUpdateVO, BaseThailPageQuery, BaseThailResultVO> {




}


