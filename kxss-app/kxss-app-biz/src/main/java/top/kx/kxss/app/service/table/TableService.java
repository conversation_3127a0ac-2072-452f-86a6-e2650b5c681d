package top.kx.kxss.app.service.table;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.app.vo.query.cash.PosCashPageQuery;
import top.kx.kxss.app.vo.query.cash.product.PosCashProductPageQuery;
import top.kx.kxss.app.vo.query.cash.service.PosCashServicePageQuery;
import top.kx.kxss.app.vo.result.MenuPerResultVo;
import top.kx.kxss.app.vo.result.product.AppProductResultVo;
import top.kx.kxss.app.vo.result.service.AppServiceResultVO;
import top.kx.kxss.app.vo.save.cash.PosCashSaveVO;
import top.kx.kxss.app.vo.save.cash.service.PosCashServiceSaveVO;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.vo.query.product.BaseProductPageQuery;
import top.kx.kxss.base.vo.query.product.category.BaseProductCategoryPageQuery;
import top.kx.kxss.base.vo.query.service.BaseServicePageQuery;
import top.kx.kxss.base.vo.query.table.BaseTableInfoPageQuery;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.member.coupon.MemberCouponResultVO;
import top.kx.kxss.base.vo.result.product.category.BaseProductCategoryResultVO;
import top.kx.kxss.base.vo.result.service.category.BaseServiceCategoryResultVO;
import top.kx.kxss.base.vo.result.table.BaseTableInfoResultVO;
import top.kx.kxss.base.vo.save.table.BaseTableInfoSaveVO;
import top.kx.kxss.base.vo.update.table.BaseTableInfoUpdateVO;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * 业务接口
 * 台桌数据
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-18 23:00:01
 * @create [2023-04-18 23:00:01] [zhou]
 */
public interface TableService extends SuperService<Long, BaseTableInfo, BaseTableInfoSaveVO,
        BaseTableInfoUpdateVO, BaseTableInfoPageQuery, BaseTableInfoResultVO> {

    Map<String, Object> listAll(BaseTableInfoPageQuery query);

    Map<String, Object> getCache();

    Map<String, Object> startTable(PosCashSaveVO saveVO);

    List<MemberInfoResultVO> queryMember(Map<String, Object> query);

    Map<String, Object> queryTableDetail(BaseTableInfoPageQuery query);

    List<AppProductResultVo> queryProducts(BaseProductPageQuery query);

    List<BaseProductCategoryResultVO> queryProductCategory(BaseProductCategoryPageQuery query);

    String saveProduct(Map<String, Object> query);

    List<MemberCouponResultVO> queryMemberCoupon(Map<String, Object> query);

    String saveProductCount(PosCashProductPageQuery saveVO);

    String deleteProduct(PosCashProductPageQuery saveVO);

    String updateCashMember(Map<String, Object> query);

    String saveCashTableOperate(Map<String, Object> query);

    List<AppServiceResultVO> queryService(BaseServicePageQuery query);

    List<BaseServiceCategoryResultVO> queryServiceCategory(BaseServicePageQuery query);

    String saveService(PosCashServiceSaveVO saveVO);

    String deleteService(PosCashServicePageQuery saveVO);

    String saveCashServiceOperate(Map<String, Object> query);

    String saveStopAll(PosCashPageQuery query);

    String pendingOrder(PosCashPageQuery query);

    String saveChangeLight(BaseTableInfoPageQuery query);

    String doChangeProductCount(PosCashProductPageQuery saveVO);

    List<MemberInfoResultVO> queryMemberByPhone(Map<String, Object> query);

    String reflushTable(BaseTableInfoPageQuery query);
    /**
     * 获取菜单显示
     * @return
     */
    MenuPerResultVo menuPer();

    String durationDesc(Integer duration);

}


