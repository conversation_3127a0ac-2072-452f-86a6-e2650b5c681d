package top.kx.kxss.app.statemachine.module.pay;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import com.wechat.pay.java.service.payments.jsapi.model.Amount;
import com.wechat.pay.java.service.payments.jsapi.model.Payer;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest;
import com.wechat.pay.java.service.payments.jsapi.model.PrepayWithRequestPaymentResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Component;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.exception.BizException;
import top.kx.basic.exception.code.ExceptionCode;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.granter.CalcAmountGranter;
import top.kx.kxss.app.properties.WxPayProperties;
import top.kx.kxss.app.service.cash.payment.PosCashPaymentService;
import top.kx.kxss.app.statemachine.AbstractPosCashProcessor;
import top.kx.kxss.app.statemachine.annotation.PosCashProcessor;
import top.kx.kxss.app.vo.save.cash.payment.PosCashPaymentSaveVO;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.service.transaction.BaseWxTransactionService;
import top.kx.kxss.base.vo.save.biz.BaseBizLogSaveVO;
import top.kx.kxss.base.vo.save.transaction.BaseWxTransactionSaveVO;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.model.enumeration.app.PayTypeEnum;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.model.enumeration.base.BizLogModuleEnum;
import top.kx.kxss.model.enumeration.base.BizLogTypeEnum;
import top.kx.kxss.system.entity.system.DefApplet;
import top.kx.kxss.system.entity.system.DefClient;
import top.kx.kxss.system.service.system.DefAppletService;
import top.kx.kxss.system.service.system.DefClientService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

import static top.kx.basic.context.ContextUtil.getCurrentCompanyId;

/**
 * 计算价格
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@PosCashProcessor
@EnableConfigurationProperties(WxPayProperties.class)
public class PaymentInfoProcessor extends AbstractPosCashProcessor {

    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private DefClientService defClientService;
    @Autowired
    private DefAppletService defAppletService;
    @Autowired
    private MemberInfoService memberInfoService;
    @Autowired
    private PosCashPaymentService posCashPaymentService;
    @Autowired
    private CalcAmountGranter calcAmountGranter;
    @Autowired
    private BaseBizLogService bizLogService;
    @Autowired
    private BaseWxTransactionService wxTransactionService;
    @Autowired
    private WxPayProperties wxPayProperties;


    public PaymentInfoProcessor() {
        super.setBillState(PosCashConstant.Event.PAYMENT_INFO.getEventId());
    }

    @Override
    public boolean process(Long posCashId, Object... params) {
        PosCash posCash = (PosCash) params[0];
        PrepayWithRequestPaymentResponse prepayWithRequestPaymentResponse = (PrepayWithRequestPaymentResponse) params[1];
        boolean lock = false;
        try {
            lock = distributedLock.lock(posCashId + PosCashConstant.Event.PAYMENT_INFO.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            //应用信息
            DefClient defClient = defClientService.getById(Wraps.<DefClient>lbQ()
                    .eq(DefClient::getClientId, ContextUtil.getClientId()).eq(DefClient::getState, true));
            DefApplet defApplet = defAppletService.getOne(Wraps.<DefApplet>lbQ().eq(DefApplet::getClientId, defClient.getId())
                    .apply("JSON_CONTAINS(tenant_id, '" + ContextUtil.getTenantId() + "')"));
            ArgumentAssert.notNull(defApplet, "商户未绑定应用!");
            Config config = getConfig();
            MemberInfo memberInfo = memberInfoService.getByUserId(ContextUtil.getUserId());
            String format = DateUtils.format(LocalDateTime.now().plusMinutes(2), "yyyy-MM-dd HH:mm:ss");
            Amount amount = new Amount();
            amount.setTotal(calcAmountGranter.changeY2F(posCash.getUnpaid()));
            amount.setCurrency("CNY");
            Payer payer = new Payer();
            payer.setOpenid(memberInfo.getOpenId());
            //支付参数
            PrepayRequest request = new PrepayRequest();
            request.setAppid(defApplet.getAppKey());
            request.setOutTradeNo(posCash.getId().toString());
            request.setTimeExpire(format.replace(" ", "T") + "+08:00");
            request.setMchid(defApplet.getMchId() + "");
            request.setDescription("小程序结账");
            request.setNotifyUrl(StrUtil.isBlank(wxPayProperties.getNotifyUrl())
                    ? "https://x32b691533.zicp.fun/app/callback/wx_notify" : wxPayProperties.getNotifyUrl());
            request.setAmount(amount);
            request.setPayer(payer);
            Map<String, Object> map = MapUtil.newHashMap();
            map.put("tenantId", ContextUtil.getTenantId());
            map.put("currentCompanyId", ContextUtil.getCurrentCompanyId());
            map.put("clientId", ContextUtil.getClientId());
            request.setAttach(JSON.toJSONString(map));
            log.info("支付请求参数信息：{}", request);
            JsapiServiceExtension service = new JsapiServiceExtension.Builder().config(config).build();
            try {
                PrepayWithRequestPaymentResponse requestPaymentResponse = service.prepayWithRequestPayment(request);
                BeanUtil.copyProperties(requestPaymentResponse, prepayWithRequestPaymentResponse);
            } catch (Exception e) {
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "创建支付异常或已支付");
            }
            log.info("支付信息：{}", prepayWithRequestPaymentResponse);
            //新增支付信息
            PosCashPaymentSaveVO cashPaymentSaveVO = PosCashPaymentSaveVO.builder()
                    .cashId(posCash.getId()).amount(posCash.getAmount())
                    .payType(PayTypeEnum.WEIXIN.getCode()).payTime(LocalDateTime.now())
                    .createdOrgId(ContextUtil.getCurrentCompanyId()).zlPaice(BigDecimal.ZERO)
                    .status(PosCashPaymentStatusEnum.NO_PAY.getCode())
                    .roundAmount(BigDecimal.ZERO)
                    .sn(ContextUtil.getSn()).isNewCum(false)
                    .build();
            posCashPaymentService.save(cashPaymentSaveVO);

            //新增操作日志
            bizLogService.save(BaseBizLogSaveVO.builder()
                    .orgId(getCurrentCompanyId()).description(memberInfo.getName() + "发起支付请求")
                    .bizModule(BizLogModuleEnum.POS_CASH.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                    .employeeId(ContextUtil.getEmployeeId()).createdOrgId(getCurrentCompanyId())
                    .opearteTime(LocalDateTime.now()).sourceId(posCash.getId()).remarks("支付")
                    .build());
            //微信流水记录
            BaseWxTransactionSaveVO baseWxTransactionSaveVO = BeanUtil.copyProperties(request, BaseWxTransactionSaveVO.class);
            baseWxTransactionSaveVO.setAmount(posCash.getUnpaid().toPlainString());
            baseWxTransactionSaveVO.setPayer(null);
            baseWxTransactionSaveVO.setTranPayer(JSON.toJSONString(request.getPayer()));
            baseWxTransactionSaveVO.setTranAmount(JSON.toJSONString(request.getAmount()));
            baseWxTransactionSaveVO.setPosCashId(posCash.getId());
            wxTransactionService.save(baseWxTransactionSaveVO);
        } finally {
            if (lock) {
                distributedLock.releaseLock(posCashId + PosCashConstant.Event.PAYMENT_INFO.getCode());
            }
        }
        return true;
    }

    public Config getConfig() {
        //应用信息
        DefClient defClient = defClientService.getById(Wraps.<DefClient>lbQ()
                .eq(DefClient::getClientId, ContextUtil.getClientId()).eq(DefClient::getState, true));
        DefApplet defApplet = defAppletService.getOne(Wraps.<DefApplet>lbQ().eq(DefApplet::getClientId, defClient.getId())
                .apply("JSON_CONTAINS(tenant_id, '" + ContextUtil.getTenantId() + "')"));
        ArgumentAssert.notNull(defApplet, "商户未绑定应用!");
        return new RSAAutoCertificateConfig.Builder()
                .merchantId(defApplet.getMchId() + "").privateKeyFromPath(wxPayProperties.getPrivateKeyFromPath())
                .merchantSerialNumber(wxPayProperties.getMerchantSerialNumber()).apiV3Key(wxPayProperties.getApiV3Key())
                .build();
    }

    public static void main(String[] args) {
        String s= UUID.randomUUID().toString().replace("-", "");
        System.out.println(s);
    }
}
