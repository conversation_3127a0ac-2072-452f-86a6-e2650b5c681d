package top.kx.kxss.app.biz;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.manager.cash.PosCashManager;

/**
 * 订单相关操作
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class PosCashBiz {


    @Autowired
    private PosCashManager posCashManager;

    /**
     * 查询单个数据
     *
     * @param posCashId
     * @return
     */
    public PosCash getById(Long posCashId) {
        return posCashManager.getById(posCashId);
    }

    /**
     * 取消订单
     * @param posCash
     */
    public void doCancel(PosCash posCash) {
        ArgumentAssert.notNull(posCash, "订单不存在");
    }
}
