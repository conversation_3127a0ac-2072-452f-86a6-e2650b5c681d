package top.kx.kxss.app.event.model;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.servlet.ServletUtil;
import lombok.*;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.base.entity.table.BaseTableInfo;

import javax.servlet.http.HttpServletRequest;
import java.io.Serializable;

/**
 * 登录状态DTO
 *
 * <AUTHOR>
 * @date 2020年03月18日17:25:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@Slf4j
public class LightDTO implements Serializable {
    private static final long serialVersionUID = -3124612657759050173L;
    /**
     * 所属企业
     */
    private Long tenantId;
    /**
     * 当前门店
     */
    private Long orgId;
    /**
     * 台桌信息
     */
    private BaseTableInfo tableInfo;
    /**
     * 登录员工
     */
    private Long employeeId;
    /**
     * 状态 0 关灯 1 开灯
     */
    private Integer status;
    /**
     * 描述
     */
    private String description;

    public static LightDTO closeLight(Long tenantId, Long employeeId, BaseTableInfo tableInfo) {
        return LightDTO.builder()
                .tenantId(tenantId)
                .orgId(ContextUtil.getCurrentCompanyId() == null ? 1 : ContextUtil.getCurrentCompanyId())
                .employeeId(employeeId)
                .status(0).description("关灯成功").tableInfo(tableInfo)
                .build().setInfo();
    }

    public static LightDTO openLight(Long tenantId, Long employeeId, BaseTableInfo tableInfo) {
        return LightDTO.builder()
                .tenantId(tenantId)
                .orgId(ContextUtil.getCurrentCompanyId() == null ? 1 : ContextUtil.getCurrentCompanyId())
                .employeeId(employeeId)
                .tableInfo(tableInfo)
                .status(1).description("开灯成功")
                .build().setInfo();
    }


    private LightDTO setInfo() {
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            return this;
        }
        HttpServletRequest request = ((ServletRequestAttributes) requestAttributes).getRequest();
        String tempUa = StrUtil.sub(request.getHeader("user-agent"), 0, 500);
        String tempIp = ServletUtil.getClientIPByHeader(request, "X-Real-IP");
        log.info("tempIp={}, ua={}", tempIp, tempUa);
        return this;
    }

}
