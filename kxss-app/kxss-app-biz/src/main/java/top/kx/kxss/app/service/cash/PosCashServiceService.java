package top.kx.kxss.app.service.cash;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.entity.cash.refund.PosCashRefundPayment;
import top.kx.kxss.app.vo.pay.PayResponseVO;
import top.kx.kxss.app.vo.query.cash.PosCashPageQuery;
import top.kx.kxss.app.vo.query.cash.payment.PosCashPaymentPageQuery;
import top.kx.kxss.app.vo.result.cash.PosCashResultVO;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPayVo;
import top.kx.kxss.app.vo.save.cash.PosCashDelOrderQuery;
import top.kx.kxss.app.vo.save.cash.PosCashSaveVO;
import top.kx.kxss.app.vo.save.cash.payment.PosCashPaymentSaveVO;
import top.kx.kxss.app.vo.update.cash.PosCashUpdateVO;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.model.enumeration.app.BasePaymentTypeEnum;
import top.kx.kxss.model.enumeration.base.ServiceStaffTimeEnum;
import top.kx.kxss.pos.vo.service.ServiceTableResultVO;
import top.kx.kxss.wxapp.vo.query.statistics.PosCashDetailsQuery;

import java.math.BigDecimal;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;


/**
 * <p>
 * 业务接口
 * pos结算 含商品出库 服务 台费结算
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:04:53
 * @create [2023-04-19 14:04:53] [dou] [代码生成器生成]
 */
public interface PosCashServiceService extends SuperService<Long, PosCash, PosCashSaveVO,
        PosCashUpdateVO, PosCashPageQuery, PosCashResultVO> {

    /**
     * 根据会员查询信息
     *
     * @param memberId
     * @return
     */
    List<PosCashResultVO> getInfoByMemberId(Long memberId);

    /**
     * 订单详情
     *
     * @param aLong
     * @return
     */
    PosCashResultVO detail(Long aLong);

    /**
     * 根据会员id查询会员所拥有的的所有优惠券
     *
     * @param memberId
     * @return
     */
    Map<String, Object> memeberCouponByType(Long memberId, Long cashId);

    /**
     * 根据会员id与优惠券id查询会员所拥有的的优惠券详情
     *
     * @param memberId
     * @param couponId
     * @return
     */
    Map<String, Object> memebercCouponDetail(Long memberId, Long couponId);

    /**
     * 查看台桌账单详情
     *
     * @param param
     * @return
     */
    LinkedHashMap<String, Object> tablePosCash(Map<String, Object> param);

    /**
     * 台桌账单保存
     *
     * @param vo
     * @return
     */
    String paymentSave(PosCashPaymentSaveVO vo);

    /**
     * 台桌账单保存
     *
     * @param param
     * @return
     */
    String couponChecked(Map<String, Object> param);

    Boolean saveUsingCoupon(Map<String, Object> param);

    Map<String, Object> queryUsingEquity(Map<String, Object> param);

    String disabledCoupon(Map<String, Object> param);

    BigDecimal queryMemberBalance(Map<String, Object> param);

    PosCashPayVo doCashPay(PosCashPaymentSaveVO saveVO);

    String removeMl(PosCashPaymentPageQuery queryVo);

    String removePay(PosCashPaymentPageQuery queryVo);

    String roundPrice(PosCashPageQuery queryVo);

    PosCashPayVo doBalancePay(PosCashPaymentSaveVO saveVO);

    String removeBalancePay(PosCashPaymentPageQuery queryVo);

    PosCashPayment createPayment(PosCashPaymentSaveVO saveVO);

    PosCashPayVo savePayResult(PayResponseVO saveVO);

    Boolean createBasePayment(Long posCashId, BasePaymentTypeEnum typeEnum);

    Boolean createBasePayment(PosCash posCash, BasePaymentTypeEnum typeEnum, MemberInfo memberInfo);

    Boolean createBasePayment(Long posCashId, BasePaymentTypeEnum typeEnum, PosCashPayment posCashPayment, MemberInfo memberInfo);

    long count(LbQueryWrap<PosCash> eq);

    Boolean delOrder(PosCashDelOrderQuery vo);

    boolean updateById(PosCash posCash);

    boolean save(PosCash posCash);

    Boolean checkMemberIsUse(List<Long> longs);

    /**
     * 会员卡是否存在关联未完成或者已退款的订单
     *
     * @param memberCardId
     * @return
     */
    Boolean checkStatusByMemberCardId(Long memberCardId);

    boolean updateBatchById(List<PosCash> posCashList);

    boolean removeById(Long id);

    Boolean update(LambdaUpdateWrapper<PosCash> wrapper);

    Boolean createBasePayment(PosCash posCash, BasePaymentTypeEnum basePaymentTypeEnum,
                              List<PosCashRefundPayment> refundPaymentList, MemberInfo memberInfo);

    PosCash getOne(LbQueryWrap<PosCash> eq);

    List<PosCashResultVO> queryListByCardId(Long memberCardId);


    List<ServiceTableResultVO> selectPosCashByEmployeeId(List<Long> employeeIdList);

    List<ServiceTableResultVO> selectCashByEmployeeId(QueryWrapper<PosCash> wrapper);

    List<ServiceTableResultVO> selectPosCashByEmployeeIdAndServiceId(List<Long> employeeIdList);

    IPage<PosCashResultVO> findPageResultVO(PageParams<PosCashDetailsQuery> params);

    List<PosCashResultVO> findAllResultVO(PosCashDetailsQuery params);

    String mergeRemarks(String targetRemarks, String sourceRemarks);

    List<PosCash> selectPosCashWithConditions();

    /**
     * 服务时长描述
     * @param duration
     * @param defaultServiceStaffTime
     * @return
     */
    String serviceDurationDesc(Integer duration, ServiceStaffTimeEnum defaultServiceStaffTime);

    /**
     * 服务时长描述，- 导出时藐视，
     * @param duration
     * @return 1. ServiceStaffTimeEnum.HOUR_MINUTE -》 xx小时xx分钟
     *          2. ServiceStaffTimeEnum.MINUTE -》 xx.xx
     *          3. ServiceStaffTimeEnum.HOUR -》 xx
     */
    String serviceDurationExportDesc(Integer duration, ServiceStaffTimeEnum defaultServiceStaffTime);

    /**
     * 转换时间展示方式，- 导出的时候回需要，
     * @param duration
     * @return 1. xx小时xx分钟 -》 xx小时xx分钟
     *          2. xx.xx h -》 xx.xx
     *          3. xx分钟 -》 xx
     */
    String serviceDurationDesc(String duration);

    /**
     * 查询任意一个
     * @return
     */
    PosCash getAnyOne(Long id);
    List<PosCash> getAnyList(List<Long> ids);

    /**
     * 流程中，员工会错乱，现在单独对完成员工赋值
     */
    Boolean updateCompleteEmp(Long id, Long completeEmp);

    <V> List<V> listObjs(Wrapper<PosCash> queryWrapper, Function<? super Object, V> mapper);
}


