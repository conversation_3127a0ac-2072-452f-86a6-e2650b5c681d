package top.kx.kxss.app.mapper.thail;

import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.kxss.app.entity.thail.PosCashThail;
import top.kx.kxss.app.vo.result.ProfitResultVO;
import top.kx.kxss.app.vo.result.thail.PosCashThailAmountResultVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * 订单套餐信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 19:11:17
 * @create [2023-09-14 19:11:17] [dou] [代码生成器生成]
 */
@Repository
public interface PosCashThailMapper extends SuperMapper<PosCashThail> {

    ProfitResultVO findProfit(@Param(value = "posCashIdList") List<Long> posCashIdList);

    List<PosCashThailAmountResultVO> thailAmountList(@Param(Constants.WRAPPER) QueryWrap<PosCashThail> wrap);

    PosCashThailAmountResultVO thailAmountSum(@Param(Constants.WRAPPER) QueryWrap<PosCashThail> wrap);
}


