package top.kx.kxss.app.manager.cash.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.app.entity.cash.PosCashPackField;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.app.manager.cash.PosCashPackFieldManager;
import top.kx.kxss.app.mapper.cash.PosCashPackFieldMapper;

/**
 * <p>
 * 通用业务实现类
 * 包场信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-22 11:37:26
 * @create [2024-04-22 11:37:26] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashPackFieldManagerImpl extends SuperManagerImpl<PosCashPackFieldMapper, PosCashPackField> implements PosCashPackFieldManager {

}


