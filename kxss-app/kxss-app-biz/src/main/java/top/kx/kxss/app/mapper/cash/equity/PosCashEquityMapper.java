package top.kx.kxss.app.mapper.cash.equity;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.equity.AppEquity;
import top.kx.kxss.app.entity.cash.equity.PosCashEquity;
import top.kx.kxss.base.vo.NameValueVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * 结算单消费权益
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-06 18:27:08
 * @create [2023-05-06 18:27:08] [dou] [代码生成器生成]
 */
@Repository
public interface PosCashEquityMapper extends SuperMapper<PosCashEquity> {

    List<AppEquity> queryMemberCoupon(@Param(value = "cashId") Long cashId);

    List<NameValueVO> consumeTimes(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);
}


