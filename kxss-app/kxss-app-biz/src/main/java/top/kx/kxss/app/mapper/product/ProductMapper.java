package top.kx.kxss.app.mapper.product;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.app.vo.result.product.AppProductResultVo;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.vo.query.product.BaseProductPageQuery;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * 商品信息表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-03-10 23:00:01
 * @create [2023-03-10 23:00:01]
 */
@Repository
public interface ProductMapper extends SuperMapper<BaseProduct> {

    List<AppProductResultVo> queryProducts(@Param(value = "query") BaseProductPageQuery query);

}


