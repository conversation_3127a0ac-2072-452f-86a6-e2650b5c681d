package top.kx.kxss.app.manager.cash.payment;

import top.kx.basic.base.manager.SuperManager;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO;
import top.kx.kxss.wxapp.vo.query.statistics.ConsumeQuery;
import top.kx.kxss.wxapp.vo.query.statistics.RechargeQuery;

import java.util.List;

/**
 * <p>
 * 通用业务接口
 * 商品结算单收款子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:38:34
 * @create [2023-04-19 14:38:34] [dou] [代码生成器生成]
 */
public interface PosCashPaymentManager extends SuperManager<PosCashPayment> {

    List<PosCashPaymentResultVO> statisConsumeAmount(ConsumeQuery params);

}


