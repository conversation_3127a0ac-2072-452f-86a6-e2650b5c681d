package top.kx.kxss.app.service.cash.refund.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.kxss.app.entity.cash.refund.PosCashRefundPayment;
import top.kx.kxss.app.manager.cash.refund.PosCashRefundPaymentManager;
import top.kx.kxss.app.service.cash.refund.PosCashRefundPaymentService;
import top.kx.kxss.app.vo.query.cash.refund.PosCashRefundPaymentPageQuery;
import top.kx.kxss.app.vo.result.cash.refund.PosCashRefundPaymentResultVO;
import top.kx.kxss.app.vo.save.cash.refund.PosCashRefundPaymentSaveVO;
import top.kx.kxss.app.vo.update.cash.refund.PosCashRefundPaymentUpdateVO;
import top.kx.kxss.common.constant.DsConstant;

import java.util.List;

/**
 * <p>
 * 业务实现类
 * 退款单
 * </p>
 *
 * <AUTHOR>
 * @date 2023-11-13 16:05:52
 * @create [2023-11-13 16:05:52] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashRefundPaymentServiceImpl extends SuperServiceImpl<PosCashRefundPaymentManager, Long, PosCashRefundPayment, PosCashRefundPaymentSaveVO,
        PosCashRefundPaymentUpdateVO, PosCashRefundPaymentPageQuery, PosCashRefundPaymentResultVO> implements PosCashRefundPaymentService {

    @Override
    public Boolean updateById(PosCashRefundPayment build) {
        return superManager.updateById(build);
    }

    @Override
    public Boolean save(PosCashRefundPayment refundPayment) {
        if (StringUtils.isBlank(refundPayment.getSn())) {
            refundPayment.setSn(ContextUtil.getSn());
        }
        return superManager.save(refundPayment);
    }

    @Override
    public PosCashRefundPayment getByMchRefundNo(String mchRefundNo) {
        return superManager.getOne(Wraps.<PosCashRefundPayment>lbQ()
                .eq(PosCashRefundPayment::getMchRefundNo, mchRefundNo));
    }

    @Override
    public boolean saveOrUpdateBatch(List<PosCashRefundPayment> refundPaymentList) {
        return superManager.saveOrUpdateBatch(refundPaymentList);
    }
}


