package top.kx.kxss.app.manager.cash.equity;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import top.kx.basic.base.manager.SuperManager;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.equity.PosCashEquity;
import top.kx.kxss.base.vo.NameValueVO;

import java.util.List;

/**
 * <p>
 * 通用业务接口
 * 结算单消费权益
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-06 18:27:08
 * @create [2023-05-06 18:27:08] [dou] [代码生成器生成]
 */
public interface PosCashEquityManager extends SuperManager<PosCashEquity> {

    List<NameValueVO> consumeTimes(QueryWrapper<PosCash> wrapper);
}


