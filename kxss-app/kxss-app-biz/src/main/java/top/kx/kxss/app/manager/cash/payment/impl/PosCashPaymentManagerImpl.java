package top.kx.kxss.app.manager.cash.payment.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.app.manager.cash.payment.PosCashPaymentManager;
import top.kx.kxss.app.mapper.cash.payment.PosCashPaymentMapper;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO;
import top.kx.kxss.wxapp.vo.query.statistics.ConsumeQuery;
import top.kx.kxss.wxapp.vo.query.statistics.RechargeQuery;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 通用业务实现类
 * 商品结算单收款子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:38:34
 * @create [2023-04-19 14:38:34] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashPaymentManagerImpl extends SuperManagerImpl<PosCashPaymentMapper, PosCashPayment> implements PosCashPaymentManager {

    @Override
    public List<PosCashPaymentResultVO> statisConsumeAmount(ConsumeQuery params) {
        return baseMapper.statisConsumeAmount(params);
    }
}


