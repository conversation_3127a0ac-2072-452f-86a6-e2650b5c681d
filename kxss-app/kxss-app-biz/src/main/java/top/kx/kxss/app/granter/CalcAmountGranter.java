package top.kx.kxss.app.granter;


import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.vo.CalcAmountParamVO;
import top.kx.kxss.app.vo.pay.PaySuccessVO;
import top.kx.kxss.app.vo.pay.WxNotifyVO;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 计算价格
 * <AUTHOR>
 */
public interface CalcAmountGranter {

    /**
     * 获取用户信息
     */
    BigDecimal total(PosCash posCash, CalcAmountParamVO paramVO);

    /**
     * 支付成功
     *
     * @param successVO
     * @return
     */
    PosCash paymentSuccess(PaySuccessVO successVO);

    /**
     * 元转分，确保price保留两位有效数字
     *
     * @return
     */
    int changeY2F(BigDecimal amount);

    /**
     * 分转元，转换为bigDecimal在toString
     *
     * @return
     */
    BigDecimal changeF2Y(int price);


    /**
     * 微信回调
     *
     * @param notifyVO
     * @return
     */
    Map<String, Object> wxNotify(WxNotifyVO notifyVO);

    /**
     * 台桌价格
     *
     * @param posCash
     * @param paramVO
     * @return
     */
    BigDecimal tableAmount(PosCash posCash, CalcAmountParamVO paramVO);


    Map<String, Object> wxRefundNotify(WxNotifyVO notifyVO);
}
