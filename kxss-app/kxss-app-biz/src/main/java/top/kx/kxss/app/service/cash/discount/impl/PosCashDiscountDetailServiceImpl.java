package top.kx.kxss.app.service.cash.discount.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.app.entity.cash.discount.PosCashDiscountDetail;
import top.kx.kxss.app.manager.cash.discount.PosCashDiscountDetailManager;
import top.kx.kxss.app.service.cash.discount.PosCashDiscountDetailService;
import top.kx.kxss.app.vo.query.cash.discount.PosCashDiscountDetailPageQuery;
import top.kx.kxss.app.vo.result.cash.discount.PosCashDiscountDetailResultVO;
import top.kx.kxss.app.vo.save.cash.discount.PosCashDiscountDetailSaveVO;
import top.kx.kxss.app.vo.update.cash.discount.PosCashDiscountDetailUpdateVO;
import top.kx.kxss.common.constant.DsConstant;

import java.util.List;

/**
 * <p>
 * 业务实现类
 * 订单优惠明细
 * </p>
 *
 * <AUTHOR>
 * @date 2023-08-02 18:45:43
 * @create [2023-08-02 18:45:43] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashDiscountDetailServiceImpl extends SuperServiceImpl<PosCashDiscountDetailManager, Long, PosCashDiscountDetail, PosCashDiscountDetailSaveVO,
        PosCashDiscountDetailUpdateVO, PosCashDiscountDetailPageQuery, PosCashDiscountDetailResultVO> implements PosCashDiscountDetailService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByPosCashId(Long id) {
        superManager.deleteByPosCashId(id);
    }

    @Override
    public long count(LbQueryWrap<PosCashDiscountDetail> wrap) {
        return superManager.count(wrap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(PosCashDiscountDetail build) {
        if (StringUtils.isBlank(build.getSn())) {
            build.setSn(ContextUtil.getSn());
        }
        superManager.save(build);
    }

    @Override
    public void updateBatchById(List<PosCashDiscountDetail> discountDetailList) {
        superManager.updateBatchById(discountDetailList);
    }

    @Override
    public void update(LbUpdateWrap<PosCashDiscountDetail> eq) {
        superManager.update(eq);
    }
}


