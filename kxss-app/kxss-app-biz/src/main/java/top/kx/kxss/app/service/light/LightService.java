package top.kx.kxss.app.service.light;

import top.kx.kxss.app.vo.light.LightParamVO;

/**
 * <p>
 * 业务接口
 * 开关灯
 * </p>
 *
 * <AUTHOR>
 */
public interface LightService {
    /**
     * 开灯成功
     * @param lightParamVO
     * @return
     */
    Boolean success(LightParamVO lightParamVO);
    /**
     * 开灯失败
     * @param lightParamVO
     * @return
     */
    Boolean fail(LightParamVO lightParamVO);

    /**
     * 临时开灯
     * @param tableId
     * @return
     */
    Boolean tempOpenLight(Long tableId);

    /**
     * 临时关灯
     * @param tableId
     * @return
     */
    Boolean tempCloseLight(Long tableId);


    /**
     * 订单临时关灯 - 只关灯, 不修改任何状态
     * @param tableId
     * @return
     */
    Boolean cashTempCloseLight(Long tableId, Long cashId);


}


