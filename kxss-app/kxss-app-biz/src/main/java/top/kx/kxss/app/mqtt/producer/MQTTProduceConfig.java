package top.kx.kxss.app.mqtt.producer;

import cn.hutool.core.util.RandomUtil;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.mqtt.core.DefaultMqttPahoClientFactory;
import org.springframework.integration.mqtt.core.MqttPahoClientFactory;
import org.springframework.integration.mqtt.outbound.MqttPahoMessageHandler;
import org.springframework.integration.mqtt.support.DefaultPahoMessageConverter;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;
import top.kx.kxss.app.mqtt.properties.MQTTProperties;

/**
 * @Description MQTT生产端配置
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties(MQTTProperties.class)
public class MQTTProduceConfig {


    @Autowired
    private MQTTProperties mqttConfig;

    /**
     * 客户端与服务器之间的连接意外中断，服务器将发布客户端的“遗嘱”消息
     */
    private static final byte[] WILL_DATA;

    static {
        WILL_DATA = "offline".getBytes();
    }

    /**
     * @param
     * @Description 出站直连通道
     */
    @Bean("mqttOut")
    public MessageChannel mqttOutBoundChannel() {
        return new DirectChannel();
    }


    /**
     * @param
     * @Description 创建MqttPahoClientFactory 设置MQTT的broker的连接属性
     * @Throws
     */
    @Bean
    public MqttPahoClientFactory outClientFactory() {
        DefaultMqttPahoClientFactory factory = new DefaultMqttPahoClientFactory();
        String[] hosts = mqttConfig.getHost();
        MqttConnectOptions options = new MqttConnectOptions();
        //指标用来控制该数量，以避免网络拥塞
        options.setMaxInflight(mqttConfig.getMaxInflight());
        options.setServerURIs(hosts);
        options.setUserName(mqttConfig.getUsername());
        options.setPassword(mqttConfig.getPassword().toCharArray());
        options.setConnectionTimeout(mqttConfig.getTimeout());
        options.setKeepAliveInterval(mqttConfig.getKeepalive());
        options.setCleanSession(mqttConfig.getCleanSession());
        options.setAutomaticReconnect(mqttConfig.getAutomaticReconnect());
        options.setWill("willTopic", WILL_DATA, 2, false);
        factory.setConnectionOptions(options);
        return factory;
    }

    /**
     * @param
     * @Description 出站
     */
    @Bean
    @ServiceActivator(inputChannel = "mqttOut")
    public MessageHandler mqttOutbound() {
        MqttPahoMessageHandler messageHandler = new MqttPahoMessageHandler(mqttConfig.getClientId()
                + "_" + RandomUtil.randomString(8)
                , outClientFactory());
        //如果设置成true，即异步，发送消息时将不会阻塞。
        messageHandler.setAsync(true);
        //设置默认QoS
        messageHandler.setDefaultQos(mqttConfig.getQos());
        DefaultPahoMessageConverter defaultPahoMessageConverter = new DefaultPahoMessageConverter();
        //发送默认按字节类型发送消息
//        defaultPahoMessageConverter.setPayloadAsBytes(true);
        messageHandler.setConverter(defaultPahoMessageConverter);
        return messageHandler;
    }
}
