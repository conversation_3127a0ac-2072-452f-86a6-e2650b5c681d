package top.kx.kxss.app.statemachine.module.reserve;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.exception.BizException;
import top.kx.basic.exception.code.ExceptionCode;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.query.ReserveTableQuery;
import top.kx.kxss.app.statemachine.AbstractPosCashProcessor;
import top.kx.kxss.app.statemachine.annotation.PosCashProcessor;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.manager.table.BaseTableInfoManager;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.service.job.BaseJobInfoService;
import top.kx.kxss.base.service.table.BaseTableInfoService;
import top.kx.kxss.base.vo.query.SaveOrUpdateJobQuery;
import top.kx.kxss.base.vo.save.biz.BaseBizLogSaveVO;
import top.kx.kxss.common.api.HelperApi;
import top.kx.kxss.common.constant.ParameterKey;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.model.enumeration.base.BizLogModuleEnum;
import top.kx.kxss.model.enumeration.base.BizLogTypeEnum;
import top.kx.kxss.model.enumeration.base.TableStatus;
import top.kx.kxss.model.enumeration.pos.JobTypeEnum;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Map;

import static top.kx.basic.context.ContextUtil.getCurrentCompanyId;

/**
 * 预订台桌
 *
 * <AUTHOR>
 */
@Component
@PosCashProcessor
@Slf4j
public class ReserveTableProcessor extends AbstractPosCashProcessor {

    @Autowired
    private BaseTableInfoService tableInfoService;
    @Autowired
    private BaseTableInfoManager tableInfoManager;
    @Autowired
    private BaseBizLogService bizLogService;
    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private HelperApi helperApi;
    @Autowired
    private BaseJobInfoService baseJobInfoService;


    public ReserveTableProcessor() {
        super.setBillState(PosCashConstant.Event.RESERVE_TABLE.getEventId());
    }

    @Override
    public boolean process(Long posCashId, Object... params) {
        ReserveTableQuery query = (ReserveTableQuery) params[0];
        Long tableId = query.getTableId();
        boolean suc = false;
        boolean lock = false;
        try {
            lock = distributedLock.lock(tableId + PosCashConstant.Event.OPENING_TABLE.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            BaseTableInfo tableInfo = tableInfoService.getById(tableId);
            ArgumentAssert.notNull(tableInfo, "台桌异常");
            ArgumentAssert.isFalse(StrUtil.isNotBlank(tableInfo.getTableStatus())
                    && !ObjectUtil.equal(tableInfo.getTableStatus(), TableStatus.UNUSED.getCode()), "台桌已占用，请联系管理员！");
            //默认开灯成功
            tableInfo.setTableStatus(TableStatus.ORDERED.getCode());
            tableInfo.setUpdatedBy(ContextUtil.getUserId());
            tableInfo.setReserveRemarks(query.getReserveRemarks());
            tableInfo.setUpdatedTime(LocalDateTime.now());
            tableInfoManager.updateById(tableInfo);

            //新增操作日志
            bizLogService.save(BaseBizLogSaveVO.builder()
                    .orgId(getCurrentCompanyId())
                    .description(tableInfo.getName() + "预订【" + tableInfo.getReserveRemarks() + "】")
                    .bizModule(BizLogModuleEnum.RESERVE_TABLE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                    .employeeId(ContextUtil.getEmployeeId()).createdOrgId(getCurrentCompanyId())
                    .opearteTime(LocalDateTime.now()).sourceId(tableId).remarks("")
                    .build());
            Map<String, String> data = helperApi.findParams(Collections.singletonList(ParameterKey.RESERVE_TABLE_DURATION)).getData();
            if (CollUtil.isNotEmpty(data)) {
                String duration = data.get(ParameterKey.RESERVE_TABLE_DURATION);
                baseJobInfoService.saveOrUpdateJob(SaveOrUpdateJobQuery.builder()
                        .employeeId(ContextUtil.getEmployeeId())
                        .jobTypeEnum(JobTypeEnum.TABLE_RESERVE_TIMING).tenantId(ContextUtil.getTenantId())
                        .currentCompanyId(ContextUtil.getCurrentCompanyId()).userId(ContextUtil.getUserId())
                        .posCash(null).startTime(LocalDateTime.now())
                        .sn(ContextUtil.getSn())
                        .duration(StrUtil.isNotBlank(duration) ? Integer.parseInt(duration) : 30).name("【" + tableInfo.getName() + "】")
                        .desc("取消台桌预订").bizId(tableInfo.getId()).build());
            }
        } finally {
            if (lock) {
                distributedLock.releaseLock(tableId + PosCashConstant.Event.OPENING_TABLE.getCode());
            }
        }
        return suc;
    }
}
