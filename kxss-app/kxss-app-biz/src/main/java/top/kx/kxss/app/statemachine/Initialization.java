package top.kx.kxss.app.statemachine;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import top.kx.kxss.app.statemachine.annotation.PosCashProcessor;


/**
 * 初始化加载
 * <AUTHOR>
 */
@Component
public class Initialization implements BeanPostProcessor {
    @Autowired
    PosCashStateManager manager;

    @Nullable
    public Object postProcessAfterInitialization(Object bean, String beanName)
            throws BeansException {
        if (bean instanceof AbstractPosCashProcessor && bean.getClass().isAnnotationPresent(PosCashProcessor.class)) {
            AbstractPosCashProcessor posCashProcessor = (AbstractPosCashProcessor) bean;
            manager.posCashProcessorMap.put(posCashProcessor.getBillState(), posCashProcessor);
        }
        return bean;
    }
}
