package top.kx.kxss.app.service.thail.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.app.entity.thail.PosCashThail;
import top.kx.kxss.app.manager.thail.PosCashThailManager;
import top.kx.kxss.app.service.thail.PosCashThailService;
import top.kx.kxss.app.vo.query.thail.PosCashThailAmountQuery;
import top.kx.kxss.app.vo.query.thail.PosCashThailPageQuery;
import top.kx.kxss.app.vo.result.ProfitResultVO;
import top.kx.kxss.app.vo.result.thail.PosCashThailAmountResultVO;
import top.kx.kxss.app.vo.result.thail.PosCashThailResultVO;
import top.kx.kxss.app.vo.save.thail.PosCashThailSaveVO;
import top.kx.kxss.app.vo.update.thail.PosCashThailUpdateVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillTypeEnum;
import top.kx.kxss.wxapp.vo.query.statistics.PosCashDetailsQuery;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 业务实现类
 * 订单套餐信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 19:11:17
 * @create [2023-09-14 19:11:17] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashThailServiceImpl extends SuperServiceImpl<PosCashThailManager, Long, PosCashThail, PosCashThailSaveVO,
        PosCashThailUpdateVO, PosCashThailPageQuery, PosCashThailResultVO> implements PosCashThailService {


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateById(PosCashThail posCashThail) {
        posCashThail.setUpdatedTime(LocalDateTime.now());
       return superManager.updateById(posCashThail);
    }

    @Override
    public PosCashThail getOne(LbQueryWrap<PosCashThail> last) {
        return superManager.getOne(last);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(PosCashThail posCashThail) {
        if (StringUtils.isBlank(posCashThail.getSn())) {
            posCashThail.setSn(ContextUtil.getSn());
        }
        superManager.save(posCashThail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchById(List<PosCashThail> cashThailList) {
        return superManager.updateBatchById(cashThailList);
    }

    @Override
    public long count(LbQueryWrap<PosCashThail> eq) {
        return superManager.count(eq);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeBatchByIds(List<PosCashThail> cashThailList) {
        return superManager.removeBatchByIds(cashThailList);
    }

    @Override
    public ProfitResultVO findProfit(List<Long> posCashIdList) {
        return superManager.findProfit(posCashIdList);
    }


    @Override
    public boolean update(LbUpdateWrap<PosCashThail> eq) {
        return superManager.update(eq);
    }

    @Override
    public List<PosCashThailAmountResultVO> thailAmountList(PosCashThailAmountQuery query) {
        QueryWrap<PosCashThail> wrap = new QueryWrap<>();
        wrap.in("pc.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode()
                , PosCashBillStateEnum.PART_REFUND.getCode()))
                .in("pc.bill_type", Arrays.asList(PosCashBillTypeEnum.REGULAR_SINGLE.getCode(),
                        PosCashBillTypeEnum.REGISTRATION.getCode(), PosCashBillTypeEnum.COUNTER_CHECKOUT.getCode()))
                .eq("pc.delete_flag", 0).eq("t.delete_flag", 0);
        if (Objects.nonNull(query)) {
            wrap.in(CollUtil.isNotEmpty(query.getCashIds()),"pc.id", query.getCashIds());
            if (Objects.nonNull(query.getCompleteStartTime()) && Objects.nonNull(query.getCompleteEndTime())) {
                wrap.between("pc.complete_time", query.getCompleteStartTime(), query.getCompleteEndTime());
            } else if (Objects.nonNull(query.getCompleteStartTime())) {
                wrap.ge("pc.complete_time", query.getCompleteStartTime());
            } else if (Objects.nonNull(query.getCompleteEndTime())) {
                wrap.le("pc.complete_time", query.getCompleteEndTime());
            }
        }
        wrap.groupBy("pc.id");
        return superManager.thailAmountList(wrap);
    }

    @Override
    public PosCashThailAmountResultVO thailAmountSum(PosCashDetailsQuery model) {
        String insql = getInSql(model);
        QueryWrap<PosCashThail> wrap = new QueryWrap<>();
//        wrap.in("pc.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode()
//                        , PosCashBillStateEnum.PART_REFUND.getCode()))
//                .in("pc.bill_type", Arrays.asList(PosCashBillTypeEnum.REGULAR_SINGLE.getCode(),
//                        PosCashBillTypeEnum.REGISTRATION.getCode(), PosCashBillTypeEnum.COUNTER_CHECKOUT.getCode()))
        wrap.eq("pc.delete_flag", 0).eq("t.delete_flag", 0).inSql("pc.id", insql);
        return superManager.thailAmountSum(wrap);
    }

    @NotNull
    private static String getInSql(PosCashDetailsQuery model) {
        String insql = "SELECT DISTINCT p.id as id " +
                "FROM pos_cash p " +
                "         LEFT JOIN member_info m ON p.member_id = m.id " +
                "         LEFT JOIN pos_cash_commenter c ON c.cash_id = p.id " +
                "         LEFT JOIN base_table_info ti ON ti.id = p.table_id " +
                " where " +
                "  p.bill_state in ('2', '5', '6') " +
                " and p.bill_type in ('0', '3', '4') and p.delete_flag = 0";
        if (StringUtils.isNotBlank(model.getType())) {
            insql += " and p.type_ = '" + model.getType() + "'";
        }
        if (CollUtil.isNotEmpty(model.getTypeList())) {
            insql += " and p.type_ in ('" + CollUtil.join(model.getTypeList(), "','") + "')";
        }
        if (StringUtils.isNotBlank(model.getOrderSource())) {
            insql += " and p.order_source = '" + model.getOrderSource() + "'";
        }
        if (StringUtils.isNotBlank(model.getTableName())) {
            insql += " and p.table_name like '" + model.getTableName() + "'";
        }
        if (StringUtils.isNotBlank(model.getKeyword())) {
            insql += " and p.code like '" + model.getKeyword() + "'";
        }
        if (StringUtils.isNotBlank(model.getCode())) {
            insql += " and p.code like '" + model.getCode() + "'";
        }
        if (ObjectUtil.isNotNull(model.getEmployeeId())) {
            insql += " and p.employee_id = " + model.getEmployeeId();
        }
        if (CollUtil.isNotEmpty(model.getOrgIdList())) {
            insql += " and p.org_id in (" + CollUtil.join(model.getOrgIdList(), ",") + ")";
        }
        if (StringUtils.isNotBlank(model.getStartDate())
                && StringUtils.isNotBlank(model.getEndDate())) {
            insql += " and p.complete_time between '" + model.getStartDate() + "' and '" + model.getEndDate() + "'";
        }
        if (StringUtils.isNotBlank(model.getCompleteTime_st())
                && StringUtils.isNotBlank(model.getCompleteTime_ed())) {
            insql += " and p.complete_time between '" + model.getCompleteTime_st() + "' and '" + model.getCompleteTime_ed() + "'";
        }
        if (Objects.nonNull(model.getCommenter())) {
            insql += " and c.delete_flag = 0 and c.employee_id = " + model.getCommenter();
        }
        if (StringUtils.isNotBlank(model.getTableType())) {
            insql += " and ti.delete_flag = 0 and ti.table_type = '" + model.getTableType() + "'";
        }
        if (StringUtils.isNotBlank(model.getTableArea())) {
            insql += " and ti.delete_flag = 0 and ti.table_area = '" + model.getTableArea() + "'";
        }
        if (StringUtils.isNotBlank(model.getMemberName())) {
            insql += " and m.delete_flag = 0 and (m.name like '" + model.getMemberName() + "' or m.mobile like '" + model.getMemberName() + "')";
        }
        return insql;
    }

    @Override
    public List<PosCashThailAmountResultVO> thailAmountList(PosCashDetailsQuery query) {
        String insql = getInSql(query);
        QueryWrap<PosCashThail> wrap = new QueryWrap<>();
        wrap.eq("pc.delete_flag", 0).eq("t.delete_flag", 0).inSql("pc.id", insql).groupBy("pc.id");
        return superManager.thailAmountList(wrap);
    }

    @Override
    public Boolean checkThail(List<Long> thailIds) {
        return superManager.count(Wraps.<PosCashThail>lbQ()
                .in(PosCashThail::getCashThailId, thailIds)
                .inSql(PosCashThail::getCashId, "select id from pos_cash where delete_flag = 0" +
                        " and bill_type not in (" + String.join(",",
                        Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode(), PosCashBillTypeEnum.CHARGEBACK.getCode()))
                        + ") and bill_state not in (" + String.join(",",
                        Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(),
                                PosCashBillStateEnum.PART_REFUND.getCode()
                                , PosCashBillStateEnum.REFUNDED.getCode()))
                        + ") and org_id = " + ContextUtil.getCurrentCompanyId())) > 0;
    }
}


