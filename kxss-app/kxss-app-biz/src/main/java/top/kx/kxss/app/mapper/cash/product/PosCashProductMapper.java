package top.kx.kxss.app.mapper.cash.product;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.vo.result.ProfitResultVO;
import top.kx.kxss.app.vo.result.cash.product.PosCashProductResultVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * 结算单商品子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:40:33
 * @create [2023-04-19 14:40:33] [dou] [代码生成器生成]
 */
@Repository
public interface PosCashProductMapper extends SuperMapper<PosCashProduct> {

    List<Map<String, Object>> getProductByCashIds(@Param(value = "cashIds") List<Long> cashIds);


    ProfitResultVO findProfit(@Param(value = "posCashIdList") List<Long> posCashIdList);

    List<String> productRanking(@Param(value = "memberId") Long memberId);

    List<PosCashProductResultVO> selectDiscount();

    List<PosCashProduct> queryList(@Param(Constants.WRAPPER) Wrapper<PosCashProduct> wrapper);

}


