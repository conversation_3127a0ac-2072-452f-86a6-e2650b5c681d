package top.kx.kxss.app.service.cash.payment;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.entity.cash.payment.PosCashPaymentDetail;
import top.kx.kxss.app.entity.thail.PosCashThail;
import top.kx.kxss.app.vo.query.cash.payment.PosCashPaymentDetailPageQuery;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentDetailResultVO;
import top.kx.kxss.app.vo.save.cash.payment.PosCashPaymentDetailSaveVO;
import top.kx.kxss.app.vo.update.cash.payment.PosCashPaymentDetailUpdateVO;
import top.kx.kxss.base.entity.payment.BasePaymentType;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * 业务接口
 * 订单支付明细
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-12 20:20:48
 * @create [2024-07-12 20:20:48] [dou] [代码生成器生成]
 */
public interface PosCashPaymentDetailService extends SuperService<Long, PosCashPaymentDetail, PosCashPaymentDetailSaveVO,
        PosCashPaymentDetailUpdateVO, PosCashPaymentDetailPageQuery, PosCashPaymentDetailResultVO> {

    boolean update(LbUpdateWrap<PosCashPaymentDetail> wrap);

    boolean save(PosCashPaymentDetail build);

    boolean updateBatchById(List<PosCashPaymentDetail> paymentDetailList);

    List<PosCashPaymentDetail> detailRewrite(
            PosCash posCash, List<PosCashPaymentDetail> posCashPaymentDetailList,
            Map<Long, BasePaymentType> paymentTypeMap,
            List<PosCashPayment> posCashPaymentList, List<PosCashThail> thailList);

    List<String> getAssessedPayTypeIds();
}


