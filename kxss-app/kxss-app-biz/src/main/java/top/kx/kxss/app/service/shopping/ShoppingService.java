package top.kx.kxss.app.service.shopping;

import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.vo.query.cash.*;
import top.kx.kxss.app.vo.query.cash.product.PosCashProductNumVO;
import top.kx.kxss.app.vo.result.cash.QueryShopDetailVO;
import top.kx.kxss.app.vo.result.product.AppProductResultVo;
import top.kx.kxss.base.vo.query.product.BaseProductPageQuery;
import top.kx.kxss.base.vo.query.product.category.BaseProductCategoryPageQuery;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.member.coupon.MemberCouponResultVO;
import top.kx.kxss.base.vo.result.product.category.BaseProductCategoryResultVO;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;

import java.util.List;

/**
 * <p>
 * 业务接口
 * 购物相关接口
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-18 23:00:01
 * @create [2023-04-18 23:00:01] [zhou]
 */
public interface ShoppingService {

    /**
     * 根据手机号查询会员
     *
     * @param phone
     * @return
     */
    MemberInfoResultVO getMemberByPhone(String phone);

    /**
     * 查询商品信息
     *
     * @param query
     * @return
     */
    List<AppProductResultVo> queryProducts(BaseProductPageQuery query);

    /**
     * 查询商品分类
     *
     * @param query
     * @return
     */
    List<BaseProductCategoryResultVO> queryProductCategory(BaseProductCategoryPageQuery query);

    /**
     * 获取购物信息
     *
     * @param posCashId
     * @return
     */
    QueryShopDetailVO queryShopDetail(Long posCashId);

    /**
     * 新增商品
     *
     * @param numVO
     * @return
     */
    Boolean saveProduct(PosCashProductNumVO numVO);

    /**
     * 添加商品数量
     *
     * @param saveVO
     * @return
     */
    Boolean productNum(PosCashProductNumVO saveVO);

    /**
     * 删除商品
     *
     * @return
     */
    Boolean delProduct(Long id);
    /**
     * 绑定会员
     *
     * @return
     */
    Boolean bindMember(PosCashMemberQuery query);

    /**
     * 获取会员优惠劵信息
     * @param memberId
     * @return
     */
    List<MemberCouponResultVO> memberCoupon(Long memberId);

    /**
     * 单品备注
     * @param query
     * @return
     */
    Boolean singleRemark(PosCashSingleRemarkQuery query);

    /**
     * 单品优惠
     * @param query
     * @return
     */
    Boolean singleSale(PosCashSingleSaleQuery query);

    /**
     * 取消单品优惠
     * @param id
     * @return
     */
    Boolean disSale(Long id);

    /**
     * 整单操作
     * @param query
     * @return
     */
    Boolean completeOrder(PosCashOrderQuery query);

    /**
     * 取消整单优惠
     * @param query
     * @return
     */
    Boolean disCompleteOrder(PosCashIdQuery query);

    /**
     * 整单重置
     * @param query
     * @return
     */
    Boolean reset(PosCashIdQuery query);

    /**
     * 销售人员
     * @return
     */
    List<BaseEmployeeResultVO> empList();

    /**
     * 在线购物
     * @param posCash
     */
    void createdOnlineShopping(PosCash posCash);

}


