package top.kx.kxss.app.service.light.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.exception.ArgumentException;
import top.kx.basic.exception.BizException;
import top.kx.basic.exception.code.ExceptionCode;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.SpringUtils;
import top.kx.kxss.IotApi;
import top.kx.kxss.app.cash.MqttApi;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.event.LightEvent;
import top.kx.kxss.app.event.model.LightDTO;
import top.kx.kxss.app.mqtt.handler.MQTTGateway;
import top.kx.kxss.app.service.cash.PosCashServiceService;
import top.kx.kxss.app.service.light.LightService;
import top.kx.kxss.app.vo.light.LightParamVO;
import top.kx.kxss.app.vo.mqtt.MQTTMessage;
import top.kx.kxss.app.vo.mqtt.MQTTParams;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.service.common.BaseParameterService;
import top.kx.kxss.base.service.job.BaseJobInfoService;
import top.kx.kxss.base.service.table.BaseTableInfoService;
import top.kx.kxss.base.vo.query.SaveOrUpdateJobQuery;
import top.kx.kxss.base.vo.save.biz.BaseBizLogSaveVO;
import top.kx.kxss.base.vo.update.table.BaseTableInfoUpdateVO;
import top.kx.kxss.common.api.HelperApi;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.MqttConstant;
import top.kx.kxss.common.constant.ParameterKey;
import top.kx.kxss.iot.vo.ControlVO;
import top.kx.kxss.model.enumeration.app.MQTTTypeEnum;
import top.kx.kxss.model.enumeration.base.BizLogModuleEnum;
import top.kx.kxss.model.enumeration.base.BizLogTypeEnum;
import top.kx.kxss.model.enumeration.base.TableStatus;
import top.kx.kxss.model.enumeration.pos.JobTypeEnum;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static top.kx.basic.context.ContextUtil.getCurrentCompanyId;

/**
 * <p>
 * 业务实现类
 * 开关灯
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class LightServiceImpl implements LightService {

    @Autowired
    private PosCashServiceService posCashServiceService;
    @Autowired
    private BaseTableInfoService tableInfoService;
    @Autowired
    private MqttApi mqttApi;
    @Autowired
    private MQTTGateway mqttGateway;
    @Autowired
    private BaseBizLogService bizLogService;
    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private BaseJobInfoService baseJobInfoService;
    @Autowired
    private BaseParameterService baseParameterService;
    @Autowired
    public IotApi iotApi;
    @Autowired
    public HelperApi helperApi;

    @Override
    public Boolean success(LightParamVO lightParamVO) {
        try {
            ContextUtil.setTenantBasePoolName(lightParamVO.getTenantId());
            ContextUtil.setTenantId(lightParamVO.getTenantId());
            ContextUtil.setCurrentCompanyId(lightParamVO.getCurrentCompanyId());
            BaseTableInfo tableInfo = tableInfoService.getById(lightParamVO.getTableId());
            ArgumentAssert.notNull(tableInfo, "台桌不存在！");
            BaseTableInfoUpdateVO build = BaseTableInfoUpdateVO.builder()
                    .id(tableInfo.getId()).lightStatus("1")
                    .build();
            if (StrUtil.isNotBlank(lightParamVO.getTableStatus())) {
                build.setTableStatus(lightParamVO.getTableStatus());
            }
            tableInfoService.updateById(build);
            return true;
        } finally {
            ContextUtil.remove();
        }
    }

    @Override
    public Boolean fail(LightParamVO lightParamVO) {
        try {
            ContextUtil.setTenantBasePoolName(lightParamVO.getTenantId());
            ContextUtil.setTenantId(lightParamVO.getTenantId());
            ContextUtil.setCurrentCompanyId(lightParamVO.getCurrentCompanyId());
            BaseTableInfo tableInfo = tableInfoService.getById(lightParamVO.getTableId());
            ArgumentAssert.notNull(tableInfo, "台桌不存在！");
            BaseTableInfoUpdateVO build = BaseTableInfoUpdateVO.builder()
                    .id(tableInfo.getId()).lightStatus("0")
                    .build();
            if (StrUtil.isNotBlank(lightParamVO.getTableStatus())) {
                build.setTableStatus(lightParamVO.getTableStatus());
            }
            tableInfoService.updateById(build);
            return true;
        } finally {
            ContextUtil.remove();
        }
    }

    @Override
    public Boolean tempOpenLight(Long tableId) {
        // 判断是否需要控灯
        Boolean controlLights = baseParameterService.manualControlLights();
        boolean lock = false;
        try {
            lock = distributedLock.lock(tableId + "_LIGHT", 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            BaseTableInfo tableInfo = tableInfoService.getById(tableId);
            ArgumentAssert.notNull(tableInfo, "台桌异常");
            ArgumentAssert.notBlank(tableInfo.getLineNum(), "请配置灯控信息！");
            ArgumentAssert.isFalse(!ObjectUtil.equal(tableInfo.getTableStatus(), TableStatus.UNUSED.getCode()),
                    "此状态不可临时开灯！");
            //判断lightname和linenum，选择灯控的发送方式jin，判断lightname不包含.,且长度>10为三代无线灯控
            if (!controlLights) {
                log.warn("手动控制灯控，无需临时开关灯");
                if (!tableInfo.getLightName().contains(".") && tableInfo.getLightName().length() > 10) {
                    try {
                        LightDTO lightDTO = LightDTO.openLight(ContextUtil.getTenantId(), ContextUtil.getEmployeeId(), tableInfo);
                        SpringUtils.publishEvent(new LightEvent(lightDTO));
                        tableInfo.setLightStatus("1");
                    } catch (ArgumentException e) {
                        throw new BizException(e.getMessage());
                    } catch (Exception e) {
                        throw new BizException("iot服务异常");
                    }
                } else {
                    // 发送消息到指定主题
                    Map<String, Object> map = MapUtil.newHashMap();
                    map.put("tenantId", String.valueOf(ContextUtil.getTenantId()));
                    map.put("currentCompanyId", String.valueOf(ContextUtil.getCurrentCompanyId()));
                    map.put("tableId", String.valueOf(tableId));
                    map.put("lightName", tableInfo.getLightName());
                    map.put("lineNum", tableInfo.getLineNum());
                    map.put("tableStatus", TableStatus.TEMP_LIGHT.getCode());
                    MQTTMessage message = MQTTMessage.builder()
                            .content(JSON.toJSONString(MQTTParams.builder()
                                    .data(map).type(MQTTTypeEnum.OPEN_LIGHT.getCode())
                                    .build())).qos(2)
                            .topic(MqttConstant.BIZ_TOPIC.concat(ContextUtil.getTenantId() + "_" +
                                    ContextUtil.getCurrentCompanyId()))
                            .build();
                    mqttGateway.sendToMqtt(message.getTopic(), message.getQos(), message.getContent());
                    log.info("临时开灯请求");
                    //老的灯控默认开灯成功
                    tableInfo.setLightStatus("1");
                }
                tableInfo.setIsShowLight(false);
            }

            tableInfo.setTableStatus(TableStatus.TEMP_LIGHT.getCode());
            tableInfo.setUpdatedBy(ContextUtil.getUserId());
            tableInfoService.updateById(tableInfo);

            //新增操作日志
            bizLogService.save(BaseBizLogSaveVO.builder()
                    .orgId(getCurrentCompanyId()).description("临时开灯【" + tableInfo.getName() + "/线路号" + tableInfo.getLightName() + "】")
                    .bizModule(BizLogModuleEnum.TEMP_OPEN_LIGHT.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                    .employeeId(ContextUtil.getEmployeeId()).createdOrgId(getCurrentCompanyId())
                    .opearteTime(LocalDateTime.now()).sourceId(tableId).remarks("临时开灯")
                    .sn(ContextUtil.getSn())
                    .build());
            Map<String, String> data = helperApi.findParams(Collections.singletonList(ParameterKey.TEMP_LIGHT_DURATION)).getData();
            String duration = "";
            if (CollUtil.isNotEmpty(data)) {
                duration = data.get(ParameterKey.TEMP_LIGHT_DURATION);
            }
            baseJobInfoService.saveOrUpdateJob(SaveOrUpdateJobQuery.builder()
                    .employeeId(ContextUtil.getEmployeeId())
                    .jobTypeEnum(JobTypeEnum.CLOSE_LIGHT_TIMING).tenantId(ContextUtil.getTenantId())
                    .currentCompanyId(ContextUtil.getCurrentCompanyId()).userId(ContextUtil.getUserId())
                    .posCash(null).startTime(LocalDateTime.now())
                    .sn(ContextUtil.getSn())
                    .duration(StrUtil.isNotBlank(duration) ? Integer.parseInt(duration) : 5).name("【" + tableInfo.getName() + "】")
                    .desc("关灯定时").bizId(tableInfo.getId()).build());
        } finally {
            if (lock) {
                distributedLock.releaseLock(tableId + "_LIGHT");
            }
        }
        return true;
    }

    @Override
    public Boolean tempCloseLight(Long tableId) {
        // 判断是否需要控灯
        Boolean controlLights = baseParameterService.manualControlLights();
        boolean lock = false;
        try {
            lock = distributedLock.lock(tableId + "_LIGHT", 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            BaseTableInfo tableInfo = tableInfoService.getById(tableId);
            ArgumentAssert.notNull(tableInfo, "台桌异常");
            ArgumentAssert.notBlank(tableInfo.getLineNum(), "请配置灯控信息！");
            ArgumentAssert.isFalse(!ObjectUtil.equal(tableInfo.getTableStatus(), TableStatus.TEMP_LIGHT.getCode()),
                    "此状态不可临时关灯！");
            //判断lightname和linenum，选择灯控的发送方式jin，判断lightname不包含.,且长度>10为无线灯控
            if (!controlLights) {
                log.warn("手动控制灯控，无需开关灯");
                if (!tableInfo.getLightName().contains(".") && tableInfo.getLightName().length() > 10) {
                    try {
                        LightDTO lightDTO = LightDTO.closeLight(ContextUtil.getTenantId(), ContextUtil.getEmployeeId(), tableInfo);
                        SpringUtils.publishEvent(new LightEvent(lightDTO));
                        tableInfo.setLightStatus("0");
                    } catch (ArgumentException e) {
                        throw new BizException(e.getMessage());
                    } catch (Exception e) {
                        throw new BizException("iot服务异常");
                    }
                } else {
                    // 发送消息到指定主题
                    Map<String, Object> map = new HashMap<>();
                    map.put("tenantId", String.valueOf(ContextUtil.getTenantId()));
                    map.put("currentCompanyId", String.valueOf(ContextUtil.getCurrentCompanyId()));
                    map.put("tableId", String.valueOf(tableId));
                    map.put("lightName", tableInfo.getLightName());
                    map.put("lineNum", tableInfo.getLineNum());
                    map.put("tableStatus", TableStatus.STOP.getCode());
                    MQTTMessage message = MQTTMessage.builder()
                            .content(JSON.toJSONString(MQTTParams.builder()
                                    .data(map).type(MQTTTypeEnum.CLOSE_LIGHT.getCode())
                                    .build())).qos(2)
                            .topic(MqttConstant.BIZ_TOPIC.concat(ContextUtil.getTenantId() + "_" +
                                    ContextUtil.getCurrentCompanyId()))
                            .build();
                    mqttGateway.sendToMqtt(message.getTopic(), message.getQos(), message.getContent());
                    log.info("关灯请求");
                    //默认关灯成功
                    tableInfo.setLightStatus("0");
                }
                tableInfo.setIsShowLight(false);
            }
            tableInfo.setTableStatus(TableStatus.UNUSED.getCode());
            tableInfo.setUpdatedBy(ContextUtil.getUserId());
            tableInfoService.updateById(tableInfo);
            //新增操作日志
            bizLogService.save(BaseBizLogSaveVO.builder()
                    .orgId(getCurrentCompanyId()).description("临时关灯【" + tableInfo.getName() + "/线路号" + tableInfo.getLightName() + "】")
                    .bizModule(BizLogModuleEnum.CLOSE_LIGHT.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                    .employeeId(ContextUtil.getEmployeeId()).createdOrgId(getCurrentCompanyId())
                    .opearteTime(LocalDateTime.now()).sourceId(tableId).remarks("临时关灯")
                    .sn(ContextUtil.getSn())
                    .build());
            try {
                baseJobInfoService.stop(tableInfo.getId(), JobTypeEnum.CLOSE_LIGHT_TIMING);
                baseJobInfoService.stop(tableInfo.getId(), JobTypeEnum.TABLE_RESERVE_TIMING);
                mqttApi.send(MQTTMessage.builder()
                        .content(JSON.toJSONString(MQTTParams.builder()
                                .data(MapUtil.newHashMap())
                                .type(MQTTTypeEnum.HOME_REFRESH.getCode())
                                .build())).qos(2)
                        .topic(MqttConstant.BIZ_TOPIC.concat(ContextUtil.getTenantId() + "_" +
                                ContextUtil.getCurrentCompanyId()))
                        .build());
            } catch (Exception ignored) {
            }
        } finally {
            if (lock) {
                distributedLock.releaseLock(tableId + "_LIGHT");
            }
        }
        return true;
    }

    @Override
    public Boolean cashTempCloseLight(Long tableId, Long cashId) {
        // 判断是否需要控灯
        Boolean controlLights = baseParameterService.manualControlLights();
        boolean lock = false;
        try {
            lock = distributedLock.lock(tableId + "_LIGHT", 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            // 判断是否已经关闭过了,如果已经关闭过了,不用重复关闭
            PosCash posCash = posCashServiceService.getById(cashId);
            if (ObjectUtil.isNull(posCash) || !posCash.getIsTemporaryLights()) {
                try {
                    baseJobInfoService.stop(posCash, JobTypeEnum.CASH_CLOSE_LIGHT_TIMING);
                } catch (Exception e) {
                }
                return true;
            }

            BaseTableInfo tableInfo = tableInfoService.getById(tableId);
            ArgumentAssert.notNull(tableInfo, "台桌异常");
            ArgumentAssert.notBlank(tableInfo.getLineNum(), "请配置灯控信息！");
            //判断lightname和linenum，选择灯控的发送方式jin，判断lightname不包含.,且长度>10为无线灯控
            if (!controlLights) {
                log.warn("手动控制灯控，无需订单临时开关灯");
                if (!tableInfo.getLightName().contains(".") && tableInfo.getLightName().length() > 10) {
                    try {
                        String[] lineInfo = tableInfo.getLineNum().split(",");
                        String deviceId = lineInfo[0];
                        Integer outlet = 0;
                        if (lineInfo.length > 1) {
                            outlet = Integer.parseInt(lineInfo[1]);
                        }
                        R<String> control = iotApi.control(ControlVO.builder().mac(tableInfo.getLightName())
                                .deviceId(deviceId).outlet(outlet).switchState("off").qos(2).build());
                        ArgumentAssert.isFalse(!control.getIsSuccess(), control.getMsg());
                        tableInfo.setLightStatus("0");
                    } catch (ArgumentException e) {
                        throw new BizException(e.getMessage());
                    } catch (Exception e) {
                        throw new BizException("iot服务异常");
                    }
                } else {
                    // 发送消息到指定主题
                    Map<String, Object> map = new HashMap<>();
                    map.put("tenantId", String.valueOf(ContextUtil.getTenantId()));
                    map.put("currentCompanyId", String.valueOf(ContextUtil.getCurrentCompanyId()));
                    map.put("tableId", String.valueOf(tableId));
                    map.put("lightName", tableInfo.getLightName());
                    map.put("lineNum", tableInfo.getLineNum());
                    map.put("tableStatus", TableStatus.STOP.getCode());
                    MQTTMessage message = MQTTMessage.builder()
                            .content(JSON.toJSONString(MQTTParams.builder()
                                    .data(map).type(MQTTTypeEnum.CLOSE_LIGHT.getCode())
                                    .build())).qos(2)
                            .topic(MqttConstant.BIZ_TOPIC.concat(ContextUtil.getTenantId() + "_" +
                                    ContextUtil.getCurrentCompanyId()))
                            .build();
                    mqttGateway.sendToMqtt(message.getTopic(), message.getQos(), message.getContent());
                    log.info("关灯请求");
                }
                tableInfo.setIsShowLight(false);
            }
            tableInfoService.updateById(tableInfo);
            //新增操作日志
            bizLogService.save(BaseBizLogSaveVO.builder()
                    .orgId(getCurrentCompanyId()).description("订单临时关灯【" + tableInfo.getName() + "/线路号" + tableInfo.getLightName() + "】")
                    .bizModule(BizLogModuleEnum.CASH_TEMP_CLOSE_LIGHT.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                    .employeeId(ContextUtil.getEmployeeId()).createdOrgId(getCurrentCompanyId())
                    .opearteTime(LocalDateTime.now()).sourceId(cashId).remarks("订单临时关灯")
                    .sn(ContextUtil.getSn())
                    .build());
            try {
                baseJobInfoService.stop(posCash, JobTypeEnum.CASH_CLOSE_LIGHT_TIMING);
            } catch (Exception e) {
            }
        } finally {
            if (lock) {
                distributedLock.releaseLock(tableId + "_LIGHT");
            }
        }
        return true;
    }
}
