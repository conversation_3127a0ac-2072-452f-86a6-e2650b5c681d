package top.kx.kxss.app.mapper.service;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.app.vo.result.product.AppProductResultVo;
import top.kx.kxss.app.vo.result.service.AppServiceMemberResultVO;
import top.kx.kxss.app.vo.result.service.AppServiceResultVO;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.service.BaseService;
import top.kx.kxss.base.vo.query.product.BaseProductPageQuery;
import top.kx.kxss.base.vo.query.service.BaseServicePageQuery;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * 商品信息表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-03-10 23:00:01
 * @create [2023-03-10 23:00:01]
 */
@Repository
public interface ServiceMapper extends SuperMapper<BaseService> {

    List<AppServiceMemberResultVO> queryService(@Param(value = "query") BaseServicePageQuery query);

}


