package top.kx.kxss.app.service.recharge;

import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.vo.query.cash.PosCashIdQuery;
import top.kx.kxss.app.vo.query.cash.PosCashMemberQuery;
import top.kx.kxss.app.vo.result.recharge.DepositRuleResultVO;
import top.kx.kxss.app.vo.result.recharge.QueryRechargeDetailVO;
import top.kx.kxss.app.vo.save.recharge.RechargeEmployeeSaveVO;
import top.kx.kxss.app.vo.save.recharge.RechargeRegistrationSaveVO;
import top.kx.kxss.base.vo.query.member.grade.MemberGradeIdQuery;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;

import java.util.List;

/**
 * <p>
 * 业务接口
 * 充值相关接口
 * </p>
 *
 */
public interface RechargeService {

    /**
     * 根据手机号查询会员
     *
     * @param phone
     * @return
     */
    MemberInfoResultVO getMemberByPhone(String phone);

    /**
     * 充值详情
     * @param posCashId
     * @return
     */
    QueryRechargeDetailVO queryDetail(Long posCashId);

    /**
     * 储值信息
     * @return
     */
    List<DepositRuleResultVO> depositList(MemberGradeIdQuery query);

    /**
     * 挂单
     * @param model
     * @return
     */
    Boolean registration(RechargeRegistrationSaveVO model);

    /**
     * 选择储值
     * @param model
     * @return
     */
    Boolean saveDeposit(RechargeRegistrationSaveVO model);

    /**
     * 选择储值
     * @param model
     * @return
     */
    PosCash saveDepositNoCash(RechargeRegistrationSaveVO model);


    /**
     * 绑定会员
     * @param query
     * @return
     */
    Boolean bindMember(PosCashMemberQuery query);

    /**
     * 选择/清除销售人员
     * @param employeeSaveVO
     * @return
     */
    Boolean saveEmp(RechargeEmployeeSaveVO employeeSaveVO);

    /**
     * 移除储值信息
     * @param query
     * @return
     */
    Boolean delDeposit(PosCashIdQuery query);

    /**
     * 重置
     * @param query
     * @return
     */
    Boolean reset(PosCashIdQuery query);

    /**
     * 移除销售人员
     * @param query
     * @return
     */
    Boolean delEmp(PosCashIdQuery query);

    /**
     * 销售人员
     * @return
     */
    List<BaseEmployeeResultVO> empList();

}


