package top.kx.kxss.app.service.cash.extend;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.app.entity.cash.extend.PosCashExtend;
import top.kx.kxss.app.vo.save.cash.extend.PosCashExtendSaveVO;
import top.kx.kxss.app.vo.update.cash.extend.PosCashExtendUpdateVO;
import top.kx.kxss.app.vo.result.cash.extend.PosCashExtendResultVO;
import top.kx.kxss.app.vo.query.cash.extend.PosCashExtendPageQuery;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 订单续时
 * </p>
 *
 * <AUTHOR>
 * @date 2023-10-21 17:10:25
 * @create [2023-10-21 17:10:25] [dou] [代码生成器生成]
 */
public interface PosCashExtendService extends SuperService<Long, PosCashExtend, PosCashExtendSaveVO,
    PosCashExtendUpdateVO, PosCashExtendPageQuery, PosCashExtendResultVO> {

    PosCashExtend getOne(LbQueryWrap<PosCashExtend> eq);

    boolean save(PosCashExtend posCashExtend);

    Boolean removeById(Long id);

    boolean updateById(PosCashExtend posCashExtend);

    boolean updateBatchById(List<PosCashExtend> posCashExtendList);
}


