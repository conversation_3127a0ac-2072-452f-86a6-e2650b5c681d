package top.kx.kxss.app.properties;


import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import top.kx.basic.constant.Constants;


/**
 * <AUTHOR>
 */
@Setter
@Getter
@ConfigurationProperties(prefix = WxPayProperties.PREFIX)
@RefreshScope
public class WxPayProperties {
    public static final String PREFIX = Constants.PROJECT_PREFIX + ".wxpay";

    /**
     * 私钥key地址
     */
    private String privateKeyFromPath;
    /**
     * api证书序列号
     */
    private String merchantSerialNumber;
    /**
     * apiV3key
     */
    private String apiV3Key;
    /**
     * 回调地址
     */
    private String notifyUrl;

}
