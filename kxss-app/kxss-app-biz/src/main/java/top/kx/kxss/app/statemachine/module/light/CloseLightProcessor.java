package top.kx.kxss.app.statemachine.module.light;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.SpringUtils;
import top.kx.kxss.IotApi;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.event.LightEvent;
import top.kx.kxss.app.event.model.LightDTO;
import top.kx.kxss.app.mqtt.handler.MQTTGateway;
import top.kx.kxss.app.statemachine.AbstractPosCashProcessor;
import top.kx.kxss.app.statemachine.annotation.PosCashProcessor;
import top.kx.kxss.app.vo.mqtt.MQTTMessage;
import top.kx.kxss.app.vo.mqtt.MQTTParams;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.service.common.BaseParameterService;
import top.kx.kxss.base.service.table.BaseTableInfoService;
import top.kx.kxss.base.vo.save.biz.BaseBizLogSaveVO;
import top.kx.kxss.common.constant.MqttConstant;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.model.enumeration.app.MQTTTypeEnum;
import top.kx.kxss.model.enumeration.base.BizLogModuleEnum;
import top.kx.kxss.model.enumeration.base.BizLogTypeEnum;
import top.kx.kxss.model.enumeration.base.TableStatus;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

import static top.kx.basic.context.ContextUtil.getCurrentCompanyId;

/**
 * 关灯
 *
 * <AUTHOR>
 */
@Component
@PosCashProcessor
@Slf4j
public class CloseLightProcessor extends AbstractPosCashProcessor {

    @Autowired
    private MQTTGateway mqttGateway;
    @Autowired
    private BaseTableInfoService tableInfoService;
    @Autowired
    private BaseBizLogService bizLogService;
    @Autowired
    private BaseParameterService baseParameterService;
    @Autowired
    public IotApi iotApi;

    public CloseLightProcessor() {
        super.setBillState(PosCashConstant.Event.CLOSE_LIGHT.getEventId());
    }

    @Override
    public boolean process(Long posCashId, Object... params) {
        PosCash posCash = (PosCash) params[0];
        BaseTableInfo tableInfo = (BaseTableInfo) params[1];
        boolean suc = false;
        boolean lock = false;

        // 判断是否需要控灯
        Boolean controlLights = baseParameterService.manualControlLights();
        if (controlLights) {
            log.warn("手动控制灯控，无需开关灯");
            Long tenantId = ContextUtil.getTenantId();
            //YC(迁就煞笔客户 2024-12-04 12:51) 547502532314973184
            if (ObjectUtil.isNotNull(tableInfo)
                    && !ObjectUtil.equal(tenantId, 547502532314973184L)) {
                tableInfoService.updateById(tableInfo);
                return true;
            }
        }

        try {
            if (ObjectUtil.isNull(tableInfo)) {
                tableInfo = tableInfoService.getById(posCash.getTableId());
            }
            ArgumentAssert.notNull(tableInfo, "台桌异常");
            ArgumentAssert.notBlank(tableInfo.getLineNum(), "请配置灯控信息！");
            //判断lightname和linenum，选择灯控的发送方式jin，判断lightname不包含.,且长度>10为无线灯控
            if (!controlLights) {
                if (!tableInfo.getLightName().contains(".") && tableInfo.getLightName().length() > 10) {
                    LightDTO lightDTO = LightDTO.closeLight(ContextUtil.getTenantId(), ContextUtil.getEmployeeId(), tableInfo);
                    SpringUtils.publishEvent(new LightEvent(lightDTO));

                } else {
                    //老的灯控 发送消息到指定主题
                    Map<String, Object> map = new HashMap<>();
                    map.put("tenantId", String.valueOf(ContextUtil.getTenantId()));
                    map.put("currentCompanyId", String.valueOf(ContextUtil.getCurrentCompanyId()));
                    map.put("tableId", String.valueOf(posCash.getTableId()));
                    map.put("lightName", tableInfo.getLightName());
                    map.put("lineNum", tableInfo.getLineNum());
                    map.put("tableStatus", TableStatus.UNUSED.getCode());
                    MQTTMessage message = MQTTMessage.builder()
                            .content(JSON.toJSONString(MQTTParams.builder()
                                    .data(map).type(MQTTTypeEnum.CLOSE_LIGHT.getCode())
                                    .build())).qos(2)
                            .topic(MqttConstant.BIZ_TOPIC.concat(ContextUtil.getTenantId() + "_" +
                                    ContextUtil.getCurrentCompanyId()))
                            .build();
                    mqttGateway.sendToMqtt(message.getTopic(), message.getQos(), message.getContent());
                    log.info("关灯请求");
                }
                tableInfo.setIsShowLight(false);
            }
            tableInfo.setLightStatus("0");
            tableInfo.setUpdatedBy(ContextUtil.getUserId());
            // 关灯时,台桌颜色清除
            tableInfo.setBgColor("");
            tableInfoService.updateById(tableInfo);
            //新增操作日志
            bizLogService.save(BaseBizLogSaveVO.builder()
                    .orgId(getCurrentCompanyId()).description("关灯【" + tableInfo.getName() + "/线路号" + tableInfo.getLightName() + "】")
                    .bizModule(BizLogModuleEnum.CLOSE_LIGHT.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                    .employeeId(ContextUtil.getEmployeeId()).createdOrgId(getCurrentCompanyId())
                    .opearteTime(LocalDateTime.now()).sourceId(posCash.getId()).remarks("")
                    .build());
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
