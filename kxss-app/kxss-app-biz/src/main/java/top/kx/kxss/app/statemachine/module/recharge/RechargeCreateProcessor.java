package top.kx.kxss.app.statemachine.module.recharge;

import cn.hutool.core.util.ObjectUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.exception.BizException;
import top.kx.basic.exception.code.ExceptionCode;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.manager.cash.PosCashManager;
import top.kx.kxss.app.statemachine.AbstractPosCashProcessor;
import top.kx.kxss.app.statemachine.annotation.PosCashProcessor;
import top.kx.kxss.app.vo.query.cash.RechargeQuery;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.member.deposit.MemberDepositRule;
import top.kx.kxss.base.entity.store.BaseStore;
import top.kx.kxss.base.manager.member.MemberInfoManager;
import top.kx.kxss.base.manager.member.deposit.MemberDepositRuleManager;
import top.kx.kxss.base.manager.store.BaseStoreManager;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.service.table.BaseTableInfoService;
import top.kx.kxss.base.vo.save.biz.BaseBizLogSaveVO;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.model.enumeration.base.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static top.kx.basic.context.ContextUtil.getCurrentCompanyId;

/**
 * 开台
 *
 * <AUTHOR>
 */
@Component
@PosCashProcessor
public class RechargeCreateProcessor extends AbstractPosCashProcessor {

    @Autowired
    private PosCashManager posCashManager;
    @Autowired
    private BaseTableInfoService tableService;
    @Autowired
    private MemberInfoManager memberInfoManager;
    @Autowired
    private MemberDepositRuleManager depositRuleManager;
    @Autowired
    private BaseStoreManager baseStoreManager;
    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private BaseBizLogService bizLogService;


    public RechargeCreateProcessor() {
        super.setBillState(PosCashConstant.Event.RECHARGE_CREATED.getEventId());
    }

    @Override
    public boolean process(Long posCashId, Object... params) {
        RechargeQuery query = (RechargeQuery) params[0];
        PosCash posCash = (PosCash) params[1];
        boolean suc = false;
        boolean lock = false;
        MemberInfo memberInfo = null;
        try {
            //门店信息
            BaseStore baseStore = baseStoreManager.getOne(Wraps.<BaseStore>lbQ().eq(BaseStore::getId, query.getOrgId()));
            ArgumentAssert.notNull(baseStore, "门店不存在！");
            //充值规则
            MemberDepositRule depositRule = depositRuleManager.getById(query.getRechargeRuleId());
            ArgumentAssert.notNull(depositRule, "充值规则不存在！");
            //会员信息
            if (posCash.getMemberId() != null) {
                memberInfo = memberInfoManager.getOne(Wraps.<MemberInfo>lbQ().eq(MemberInfo::getId, posCash.getMemberId()));
            }
            if (ObjectUtil.isNull(memberInfo)) {
                memberInfo = memberInfoManager.getOne(Wraps.<MemberInfo>lbQ().eq(MemberInfo::getUserId, ContextUtil.getUserId())
                        .orderByDesc(MemberInfo::getCreatedTime).last("limit 1"));
            }
            ArgumentAssert.notNull(memberInfo, "用户异常！");
            ContextUtil.setCurrentCompanyId(query.getOrgId());
            lock = distributedLock.lock(memberInfo.getId().toString() + query.getRechargeRuleId() + PosCashConstant.Event.RECHARGE_CREATED.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            if (ObjectUtil.isNull(posCash)) {
                posCash = new PosCash();
            }
            // 类型
            posCash.setType(PosCashTypeEnum.RECHARGE.getCode());
            // 单据code
            posCash.setCode(tableService.randomOrderCode());
            // 单据日期
            posCash.setBillDate(LocalDate.now());
            // 单据状态
            posCash.setBillState(PosCashBillStateEnum.NO_PAY.getCode());
            posCash.setBillType(PosCashBillTypeEnum.REGULAR_SINGLE.getCode());
            // 门店id
            posCash.setOrgId(getCurrentCompanyId());
            posCash.setCreatedOrgId(getCurrentCompanyId());
            // 员工id
            posCash.setEmployeeId(null);
            posCash.setCreatedEmp(ContextUtil.getEmployeeId());
            // 支付名
            posCash.setPayName("创建充值，待支付");
            posCash.setUpdatedTime(null);
            //充值信息
            posCash.setAmount(depositRule.getRechargeAmount());
            if (ObjectUtil.isNull(depositRule.getRechargeAmount())) {
                posCash.setAmount(BigDecimal.ZERO);
            }
            posCash.setGiftAmount(depositRule.getGiftAmount());
            posCash.setDiscountAmount(BigDecimal.ZERO);
            posCash.setPayment(posCash.getAmount());
            posCash.setDepositRuleId(depositRule.getId());
            posCash.setUnpaid(posCash.getAmount());
            posCash.setPaid(BigDecimal.ZERO);
            if (ObjectUtil.isNull(posCash.getGiftAmount())) {
                posCash.setGiftAmount(BigDecimal.ZERO);
            }
            //会员ID
            posCash.setMemberId(memberInfo.getId());
            //订单来源
            posCash.setOrderSource(OrderSourceEnum.SELF.getCode());
            if (ObjectUtil.isNotNull(posCash.getId())) {
                posCash.setUpdatedTime(LocalDateTime.now());
                posCash.setUpdatedBy(ContextUtil.getUserId());
                suc = posCashManager.updateById(posCash);
            } else {
                posCash.setCreatedTime(LocalDateTime.now());
                posCash.setIsTurn(false);
                posCash.setCreatedBy(ContextUtil.getUserId());
                posCash.setRefundAmount(BigDecimal.ZERO);
                posCash.setSn(ContextUtil.getSn());
                suc = posCashManager.save(posCash);
            }
            ArgumentAssert.isFalse(!suc, "操作失败！");
            //新增操作日志
            bizLogService.save(BaseBizLogSaveVO.builder()
                    .orgId(getCurrentCompanyId()).description(memberInfo.getName() + "创建充值(" + depositRule.getRechargeAmount() + "),赠送金额：" + depositRule.getGiftAmount())
                    .bizModule(BizLogModuleEnum.POS_CASH.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                    .employeeId(ContextUtil.getEmployeeId()).createdOrgId(getCurrentCompanyId())
                    .opearteTime(LocalDateTime.now()).sourceId(posCash.getId()).remarks("创建充值")
                    .build());
        } finally {
            if (lock) {
                distributedLock.releaseLock(memberInfo.getId() + query.getRechargeRuleId() + PosCashConstant.Event.RECHARGE_CREATED.getCode());
            }
        }
        return suc;
    }
}
