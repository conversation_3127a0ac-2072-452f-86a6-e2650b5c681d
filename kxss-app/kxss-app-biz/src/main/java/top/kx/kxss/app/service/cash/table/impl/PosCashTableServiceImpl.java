package top.kx.kxss.app.service.cash.table.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.app.entity.cash.PackFieldSaveVO;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.manager.cash.table.PosCashTableManager;
import top.kx.kxss.app.service.cash.table.PosCashTableService;
import top.kx.kxss.app.vo.query.cash.table.PosCashTablePageQuery;
import top.kx.kxss.app.vo.result.ProfitResultVO;
import top.kx.kxss.app.vo.result.cash.table.PosCashTableResultVO;
import top.kx.kxss.app.vo.save.cash.table.PosCashTableSaveVO;
import top.kx.kxss.app.vo.update.cash.table.PosCashTableUpdateVO;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.app.CashTableStatusEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillTypeEnum;
import top.kx.kxss.model.enumeration.base.PosCashTypeEnum;
import top.kx.kxss.model.enumeration.pos.DiscountTypeEnum;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.function.Function;

/**
 * <p>
 * 业务实现类
 * 台桌计时费用
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:44:58
 * @create [2023-04-19 14:44:58] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashTableServiceImpl extends SuperServiceImpl<PosCashTableManager, Long, PosCashTable, PosCashTableSaveVO,
        PosCashTableUpdateVO, PosCashTablePageQuery, PosCashTableResultVO> implements PosCashTableService {

    @Override
    public boolean updateBatchById(List<PosCashTable> cashTableList) {
        return superManager.updateBatchById(cashTableList);
    }

    @Override
    public long count(LbQueryWrap<PosCashTable> eq) {
        return superManager.count(eq);
    }

    @Override
    public boolean removeBatchByIds(List<PosCashTable> cashTableList) {
        return superManager.removeBatchByIds(cashTableList);
    }

    @Override
    public boolean update(LbUpdateWrap<PosCashTable> eq) {
        return superManager.update(eq);
    }

    @Override
    public ProfitResultVO findProfit(List<Long> posCashIdList, Boolean thailIsNull) {
        return superManager.findProfit(posCashIdList, thailIsNull);
    }

    @Override
    public Boolean checkIsUse(List<Long> longs) {
        return superManager.count(Wraps.<PosCashTable>lbQ()
                .in(PosCashTable::getTableId, longs)
                .inSql(PosCashTable::getCashId,
                        " select p.id from pos_cash p where p.delete_flag = 0 "
                                + " and p.type_ != "
                                + PosCashTypeEnum.RECHARGE.getCode()
                                + " and p.bill_type not in ('"
                                + PosCashBillTypeEnum.CANCELLATION.getCode()
                                + "','" + PosCashBillTypeEnum.CHARGEBACK.getCode() + "') and p.bill_state not  in ('"
                                + PosCashBillStateEnum.COMPLETE.getCode() + "','"
                                + PosCashBillStateEnum.REFUNDED.getCode()
                                + "','" + PosCashBillStateEnum.PART_REFUND.getCode()
                                + "')")) > 0;
    }

    @Override
    public Boolean checkTableTypeIsUse(List<String> longs) {
        return superManager.count(Wraps.<PosCashTable>lbQ()
                .inSql(PosCashTable::getTableId, "select id from base_table_info where delete_flag = 0 " +
                        "and table_type in ('" + String.join("','", longs) + "')")
                .inSql(PosCashTable::getCashId,
                        " select p.id from pos_cash p where p.delete_flag = 0 "
                                + " and p.type_ != "
                                + PosCashTypeEnum.RECHARGE.getCode()
                                + " and p.bill_type not in ('"
                                + PosCashBillTypeEnum.CANCELLATION.getCode()
                                + "','" + PosCashBillTypeEnum.CHARGEBACK.getCode() + "') and p.bill_state not  in ('"
                                + PosCashBillStateEnum.COMPLETE.getCode() + "','"
                                + PosCashBillStateEnum.REFUNDED.getCode()
                                + "','" + PosCashBillStateEnum.PART_REFUND.getCode()
                                + "')")) > 0;
    }

    @Override
    public Boolean checkTableTypeIdIsUse(List<Long> longs) {
        return superManager.count(Wraps.<PosCashTable>lbQ()
                .inSql(PosCashTable::getTableId, "select id from base_table_info where delete_flag = 0 " +
                        "and table_type in (select key_ from base_dict where delete_flag = 0" +
                        " and parent_key = 'BASE_TABLE_TYPE' and id =" + longs.get(0) + ")")
                .inSql(PosCashTable::getCashId,
                        " select p.id from pos_cash p where p.delete_flag = 0 "
                                + " and p.type_ != "
                                + PosCashTypeEnum.RECHARGE.getCode()
                                + " and p.bill_type not in ('"
                                + PosCashBillTypeEnum.CANCELLATION.getCode()
                                + "','" + PosCashBillTypeEnum.CHARGEBACK.getCode() + "') and p.bill_state not  in ('"
                                + PosCashBillStateEnum.COMPLETE.getCode() + "','"
                                + PosCashBillStateEnum.REFUNDED.getCode()
                                + "','" + PosCashBillStateEnum.PART_REFUND.getCode()
                                + "')")) > 0;
    }

    @Override
    public PosCashTable getPackField(PosCash posCash, BaseTableInfo tableInfo, PackFieldSaveVO query) {
        PosCashTable build = PosCashTable.builder().tableId(tableInfo.getId())
                .cashThailId(null).amount(BigDecimal.ZERO)
                .discountAmount(BigDecimal.ZERO).isMerge(false).isTurn(false)
                .status(CashTableStatusEnum.TIMING.getCode())
                .isAccount(tableInfo.getIsAccount())
                .isDiscount(tableInfo.getIsDiscount())
                .isTableGiftPay(tableInfo.getIsGiftPay())
                .discountAmount(BigDecimal.ZERO)
                .freeAmount(BigDecimal.ZERO).discountType(DiscountTypeEnum.ORIGINAL.getCode())
                .freeDuration(0).chargingSettingId(0L).duration(0)
                .startTime(LocalDateTime.now().withSecond(0).withNano(0))
                .endTime(LocalDateTime.now().withSecond(0).withNano(0))
                .cashId(posCash.getId()).createdOrgId(ContextUtil.getCurrentCompanyId())
                .isPackField(true).assessedAmount(BigDecimal.ZERO).paid(BigDecimal.ZERO)
                .tableName(tableInfo.getName()).build();
        build.setCreatedTime(LocalDateTime.now());
        build.setUpdatedTime(LocalDateTime.now());
        build.setPrice(query.getPackFieldAmount());
        build.setOldPrice(query.getPackFieldAmount());
        // 原始价格
        build.setOrginPrice(query.getPackFieldAmount());
        build.setOldOrginPrice(query.getPackFieldAmount());
        build.setTableName(tableInfo.getName());
        build.setCycle(query.getPackFieldAmount().setScale(2, RoundingMode.HALF_UP).toPlainString()
                + "元/" + query.getPackFieldDuration() + "分钟");
        return build;
    }

    @Override
    public Boolean updateById(PosCashTable posCashTable) {
        return superManager.updateById(posCashTable);
    }

    @Override
    public PosCashTable getOne(LbQueryWrap<PosCashTable> eq) {
        return superManager.getOne(eq);
    }

    @Override
    public <V> List<V> listObjs(Wrapper<PosCashTable> queryWrapper, Function<? super Object, V> mapper) {
        return superManager.listObjs(queryWrapper, mapper);
    }

    @Override
    public Boolean save(PosCashTable cashTable) {
        if (StringUtils.isBlank(cashTable.getSn())) {
            cashTable.setSn(ContextUtil.getSn());
        }
        return superManager.save(cashTable);
    }

    @Override
    public Boolean saveOrUpdate(PosCashTable cashTable) {
        return superManager.saveOrUpdate(cashTable);
    }

    @Override
    public Boolean checkTableUsed(PosCash posCash) {
        return superManager.checkTableUsed(posCash);
    }

    @Override
    public Boolean checkTableStop(PosCash posCash) {
        return superManager.checkTableStop(posCash);
    }

    @Override
    public Boolean checkServiceActivity(List<Long> longs) {
        return superManager.count(Wraps.<PosCashTable>lbQ()
                .isNotNull(PosCashTable::getServiceActivityId)
                .eq(PosCashTable::getDeleteFlag, 0)
                .in(PosCashTable::getServiceActivityId, longs)
                .inSql(PosCashTable::getCashId,
                        " select p.id from pos_cash p where p.delete_flag = 0 "
                                + " and p.type_ != "
                                + PosCashTypeEnum.RECHARGE.getCode()
                                + " and p.bill_type not in ('"
                                + PosCashBillTypeEnum.CANCELLATION.getCode()
                                + "','" + PosCashBillTypeEnum.CHARGEBACK.getCode() + "') and p.bill_state not  in ('"
                                + PosCashBillStateEnum.COMPLETE.getCode() + "','"
                                + PosCashBillStateEnum.REFUNDED.getCode()
                                + "','" + PosCashBillStateEnum.PART_REFUND.getCode()
                                + "')")) > 0;
    }
}

