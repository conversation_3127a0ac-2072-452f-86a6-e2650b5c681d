package top.kx.kxss.app.service.cash;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.app.entity.cash.PosCashCommenter;
import top.kx.kxss.app.vo.save.cash.PosCashCommenterSaveVO;
import top.kx.kxss.app.vo.update.cash.PosCashCommenterUpdateVO;
import top.kx.kxss.app.vo.result.cash.PosCashCommenterResultVO;
import top.kx.kxss.app.vo.query.cash.PosCashCommenterPageQuery;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 订单相关提成人
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-16 19:15:27
 * @create [2024-04-16 19:15:27] [dou] [代码生成器生成]
 */
public interface PosCashCommenterService extends SuperService<Long, PosCashCommenter, PosCashCommenterSaveVO,
    PosCashCommenterUpdateVO, PosCashCommenterPageQuery, PosCashCommenterResultVO> {

    boolean remove(LbQueryWrap<PosCashCommenter> eq);

    Boolean batchSave(List<PosCashCommenterSaveVO> params);

    boolean updateBatchById(List<PosCashCommenter> posCashCommenterList);

}


