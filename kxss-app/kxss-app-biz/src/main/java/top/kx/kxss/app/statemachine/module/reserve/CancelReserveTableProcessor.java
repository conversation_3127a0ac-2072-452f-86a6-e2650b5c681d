package top.kx.kxss.app.statemachine.module.reserve;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.exception.BizException;
import top.kx.basic.exception.code.ExceptionCode;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.statemachine.AbstractPosCashProcessor;
import top.kx.kxss.app.statemachine.annotation.PosCashProcessor;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.manager.table.BaseTableInfoManager;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.service.job.BaseJobInfoService;
import top.kx.kxss.base.service.table.BaseTableInfoService;
import top.kx.kxss.base.vo.save.biz.BaseBizLogSaveVO;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.model.enumeration.base.BizLogModuleEnum;
import top.kx.kxss.model.enumeration.base.BizLogTypeEnum;
import top.kx.kxss.model.enumeration.base.TableStatus;
import top.kx.kxss.model.enumeration.pos.JobTypeEnum;

import java.time.LocalDateTime;

import static top.kx.basic.context.ContextUtil.getCurrentCompanyId;

/**
 * 关灯
 *
 * <AUTHOR>
 */
@Component
@PosCashProcessor
@Slf4j
public class CancelReserveTableProcessor extends AbstractPosCashProcessor {

    @Autowired
    private BaseTableInfoService tableInfoService;
    @Autowired
    private BaseTableInfoManager tableInfoManager;
    @Autowired
    private BaseBizLogService bizLogService;
    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private BaseJobInfoService baseJobInfoService;

    public CancelReserveTableProcessor() {
        super.setBillState(PosCashConstant.Event.CANCEL_RESERVE_TABLE.getEventId());
    }

    @Override
    public boolean process(Long posCashId, Object... params) {
        Long tableId = (Long) params[0];
        boolean suc = false;
        boolean lock = false;
        try {
            lock = distributedLock.lock(tableId + PosCashConstant.Event.OPENING_TABLE.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            BaseTableInfo tableInfo = tableInfoService.getById(tableId);
            ArgumentAssert.notNull(tableInfo, "台桌异常");
            ArgumentAssert.isFalse(StrUtil.isBlank(tableInfo.getTableStatus())
                    || !ObjectUtil.equal(tableInfo.getTableStatus(), TableStatus.ORDERED.getCode()), "台桌非预订状态，请联系管理员！");

            //默认
            tableInfo.setTableStatus(TableStatus.UNUSED.getCode());
            tableInfo.setReserveRemarks(null);
            tableInfo.setUpdatedBy(ContextUtil.getUserId());
            //新增操作日志
            bizLogService.save(BaseBizLogSaveVO.builder()
                    .orgId(getCurrentCompanyId())
                    .description(tableInfo.getName() + "取消预订【" + tableInfo.getReserveRemarks() + "】")
                    .bizModule(BizLogModuleEnum.CANCEL_RESERVE_TABLE.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                    .employeeId(ContextUtil.getEmployeeId()).createdOrgId(getCurrentCompanyId())
                    .opearteTime(LocalDateTime.now()).sourceId(tableId).remarks("")
                    .build());
            tableInfo.setUpdatedTime(LocalDateTime.now());
            tableInfoManager.updateById(tableInfo);
            baseJobInfoService.stop(tableInfo.getId(), JobTypeEnum.TABLE_RESERVE_TIMING);
        } finally {
            if (lock) {
                distributedLock.releaseLock(tableId + PosCashConstant.Event.OPENING_TABLE.getCode());
            }
        }
        return suc;
    }
}
