package top.kx.kxss.app.service.cash.payment.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.entity.cash.payment.PosCashPaymentDetail;
import top.kx.kxss.app.entity.thail.PosCashThail;
import top.kx.kxss.app.manager.cash.payment.PosCashPaymentDetailManager;
import top.kx.kxss.app.service.cash.payment.PosCashPaymentDetailService;
import top.kx.kxss.app.vo.query.cash.payment.PosCashPaymentDetailPageQuery;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentDetailResultVO;
import top.kx.kxss.app.vo.save.cash.payment.PosCashPaymentDetailSaveVO;
import top.kx.kxss.app.vo.update.cash.payment.PosCashPaymentDetailUpdateVO;
import top.kx.kxss.base.entity.payment.BasePaymentType;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.base.EquityTypeEnum;
import top.kx.kxss.model.enumeration.base.PaymentBizTypeEnum;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 订单支付明细
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-12 20:20:48
 * @create [2024-07-12 20:20:48] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashPaymentDetailServiceImpl extends SuperServiceImpl<PosCashPaymentDetailManager, Long, PosCashPaymentDetail, PosCashPaymentDetailSaveVO,
        PosCashPaymentDetailUpdateVO, PosCashPaymentDetailPageQuery, PosCashPaymentDetailResultVO> implements PosCashPaymentDetailService {


    @Override
    public boolean update(LbUpdateWrap<PosCashPaymentDetail> wrap) {
        return superManager.update(wrap);
    }

    @Override
    public boolean save(PosCashPaymentDetail build) {
        return superManager.save(build);
    }

    @Override
    public boolean updateBatchById(List<PosCashPaymentDetail> paymentDetailList) {
        return superManager.updateBatchById(paymentDetailList);
    }

    @Override
    public List<PosCashPaymentDetail> detailRewrite(PosCash posCash, List<PosCashPaymentDetail> posCashPaymentDetailList,
                                                    Map<Long, BasePaymentType> paymentTypeMap,
                                                    List<PosCashPayment> posCashPaymentList,
                                                    List<PosCashThail> thailList) {
        if (CollUtil.isEmpty(posCashPaymentList)) {
            return Collections.emptyList();
        }
        if (CollUtil.isEmpty(paymentTypeMap)) {
            return Collections.emptyList();
        }

        if (CollUtil.isNotEmpty(posCashPaymentDetailList)) {
            superManager.removeByIds(posCashPaymentDetailList.stream()
                    .filter(v -> {
                        BasePaymentType basePaymentType = paymentTypeMap.get(v.getPayTypeId());
                        if (ObjectUtil.isNull(basePaymentType)) {
                            return false;
                        }
                        if (basePaymentType.getBizType().equals(PaymentBizTypeEnum.MEITUAN.getCode())
                                || basePaymentType.getBizType().equals(PaymentBizTypeEnum.DOUYIN.getCode())) {
                            return true;
                        }
                        if (!getAssessedPayTypeIds().contains(basePaymentType.getBizType())) {
                            return true;
                        }
                        return v.getId() != null || !posCash.getId().equals(v.getCashId());
                    }).map(PosCashPaymentDetail::getId)
                    .filter(ObjectUtil::isNotNull).collect(Collectors.toList()));
        }

        posCashPaymentDetailList.removeIf(v -> {
            BasePaymentType basePaymentType = paymentTypeMap.get(v.getPayTypeId());
            if (ObjectUtil.isNull(basePaymentType)) {
                return true;
            }
            if (basePaymentType.getBizType().equals(PaymentBizTypeEnum.MEITUAN.getCode())
                    || basePaymentType.getBizType().equals(PaymentBizTypeEnum.DOUYIN.getCode())) {
                return true;
            }
            if (!getAssessedPayTypeIds().contains(basePaymentType.getBizType())) {
                return true;
            }
            return !posCash.getId().equals(v.getCashId());
        });
        if (CollUtil.isEmpty(posCashPaymentDetailList)) {
            posCashPaymentDetailList = Lists.newArrayList();
        }
        for (PosCashPayment posCashPayment : posCashPaymentList) {
            BasePaymentType basePaymentType = paymentTypeMap.get(posCashPayment.getPayTypeId());
            if (ObjectUtil.isNull(basePaymentType)) {
                continue;
            }
            if (!basePaymentType.getBizType().equals(PaymentBizTypeEnum.MEITUAN.getCode())
                    && !basePaymentType.getBizType().equals(PaymentBizTypeEnum.DOUYIN.getCode())) {
                continue;
            }
            PosCashThail cashDetail = thailList.stream().filter(v ->
                            v.getIsCheckSecurities() != null && v.getIsCheckSecurities()
                                    && v.getSecuritiesNumber().equals(posCashPayment.getSecuritiesNumber())).findFirst()
                    .orElse(null);
            PosCashPaymentDetail paymentDetail = PosCashPaymentDetail.builder().cashId(posCash.getId())
                    .payBizType(basePaymentType.getBizType()).cashPaymentId(posCashPayment.getId())
                    .bizType(EquityTypeEnum.THAIL.getCode()).payTypeId(posCashPayment.getPayTypeId())
                    .createdOrgId(ContextUtil.getCurrentCompanyId()).sourceId(cashDetail.getId())
                    .giftAmount(BigDecimal.ZERO).payName(posCashPayment.getPayName())
                    .amount(cashDetail.getAmount())
                    .build();
            posCashPaymentDetailList.add(paymentDetail);
        }
        return posCashPaymentDetailList;
    }

    @Override
    public List<String> getAssessedPayTypeIds() {
        return Arrays.asList(PaymentBizTypeEnum.ACCOUNT.getCode(), PaymentBizTypeEnum.VOUCHER_COUPON.getCode()
                , PaymentBizTypeEnum.STORED.getCode(), PaymentBizTypeEnum.MEITUAN.getCode(), PaymentBizTypeEnum.DOUYIN.getCode());
    }
}


