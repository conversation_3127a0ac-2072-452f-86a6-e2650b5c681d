package top.kx.kxss.app.manager.cash.payment.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.app.entity.cash.payment.PosCashPaymentDetail;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.app.manager.cash.payment.PosCashPaymentDetailManager;
import top.kx.kxss.app.mapper.cash.payment.PosCashPaymentDetailMapper;

/**
 * <p>
 * 通用业务实现类
 * 订单支付明细
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-12 20:20:48
 * @create [2024-07-12 20:20:48] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashPaymentDetailManagerImpl extends SuperManagerImpl<PosCashPaymentDetailMapper, PosCashPaymentDetail> implements PosCashPaymentDetailManager {

}


