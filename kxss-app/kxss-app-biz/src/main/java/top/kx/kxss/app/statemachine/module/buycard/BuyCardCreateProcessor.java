package top.kx.kxss.app.statemachine.module.buycard;

import cn.hutool.core.util.ObjectUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.exception.BizException;
import top.kx.basic.exception.code.ExceptionCode;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.manager.cash.PosCashManager;
import top.kx.kxss.app.statemachine.AbstractPosCashProcessor;
import top.kx.kxss.app.statemachine.annotation.PosCashProcessor;
import top.kx.kxss.app.vo.query.cash.BuyCardQuery;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.store.BaseStore;
import top.kx.kxss.base.manager.member.MemberInfoManager;
import top.kx.kxss.base.manager.store.BaseStoreManager;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.service.table.BaseTableInfoService;
import top.kx.kxss.base.vo.save.biz.BaseBizLogSaveVO;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.model.enumeration.base.*;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static top.kx.basic.context.ContextUtil.getCurrentCompanyId;

/**
 * 购物创建
 *
 * <AUTHOR>
 */
@Component
@PosCashProcessor
public class BuyCardCreateProcessor extends AbstractPosCashProcessor {

    @Autowired
    private PosCashManager posCashManager;
    @Autowired
    private BaseTableInfoService tableService;
    @Autowired
    private MemberInfoManager memberInfoManager;
    @Autowired
    private BaseStoreManager baseStoreManager;
    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private BaseBizLogService bizLogService;

    public BuyCardCreateProcessor() {
        super.setBillState(PosCashConstant.Event.BUY_CARD_CREATED.getEventId());
    }

    @Override
    public boolean process(Long posCashId, Object... params) {
        BuyCardQuery query = (BuyCardQuery) params[0];
        PosCash posCash = (PosCash) params[1];
        boolean lock = false;
        try {
            lock = distributedLock.lock(query.getOrgId() + PosCashConstant.Event.BUY_CARD_CREATED.getCode(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            //门店信息
            BaseStore baseStore = baseStoreManager.getOne(Wraps.<BaseStore>lbQ().eq(BaseStore::getId, query.getOrgId()));
            ArgumentAssert.notNull(baseStore, "门店不存在！");
            if (ObjectUtil.isNull(posCash)) {
                posCash = new PosCash();
            }
            //会员信息
            if (ObjectUtil.isNotNull(query.getMemberId())) {
                MemberInfo memberInfo = memberInfoManager.getById(query.getMemberId());
                ArgumentAssert.notNull(memberInfo, "用户不存在！");
                posCash.setMemberId(memberInfo.getId());
            }
            ContextUtil.setCurrentCompanyId(query.getOrgId());
            // 类型
            posCash.setType(PosCashTypeEnum.CARD_COUPON.getCode());
            // 单据code
            posCash.setCode(tableService.randomOrderCode());
            // 单据日期
            posCash.setBillDate(LocalDate.now());
            // 单据状态
            posCash.setBillState(PosCashBillStateEnum.NO_PAY.getCode());
            posCash.setBillType(PosCashBillTypeEnum.REGULAR_SINGLE.getCode());
            // 门店id
            posCash.setOrgId(getCurrentCompanyId());
            posCash.setCreatedOrgId(getCurrentCompanyId());
            // 员工id
            posCash.setEmployeeId(null);
            posCash.setCreatedEmp(ContextUtil.getEmployeeId());
            // 支付名
            posCash.setPayName("购卡订单，待支付");
            posCash.setUpdatedTime(null);
            //充值信息
            posCash.setAmount(BigDecimal.ZERO);
            posCash.setGiftAmount(BigDecimal.ZERO);
            posCash.setDiscountAmount(BigDecimal.ZERO);
            posCash.setPayment(posCash.getAmount());
            posCash.setUnpaid(posCash.getAmount());
            posCash.setPaid(BigDecimal.ZERO);
            //订单来源
            posCash.setOrderSource(OrderSourceEnum.POS.getCode());
            posCash.setCreatedTime(LocalDateTime.now());
            posCash.setIsTurn(false);
            posCash.setCreatedBy(ContextUtil.getUserId());
            posCash.setRefundAmount(BigDecimal.ZERO);
            posCash.setIsAutoRound(false);
            posCash.setAutoRoundAmount(BigDecimal.ZERO);
            posCash.setSn(ContextUtil.getSn());
            boolean suc = posCashManager.save(posCash);
            ArgumentAssert.isFalse(!suc, "操作失败！");
            //新增操作日志
            bizLogService.save(BaseBizLogSaveVO.builder()
                    .orgId(getCurrentCompanyId()).description("创建购卡订单")
                    .bizModule(BizLogModuleEnum.POS_CASH.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                    .employeeId(ContextUtil.getEmployeeId()).createdOrgId(getCurrentCompanyId())
                    .opearteTime(LocalDateTime.now()).sourceId(posCash.getId()).remarks("创建购卡订单")
                    .sn(ContextUtil.getSn())
                    .build());
            return true;
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getOrgId() + PosCashConstant.Event.BUY_CARD_CREATED.getCode());
            }
        }

    }
}
