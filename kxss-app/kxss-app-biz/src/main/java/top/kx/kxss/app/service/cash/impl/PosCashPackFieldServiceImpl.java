package top.kx.kxss.app.service.cash.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.app.entity.cash.PosCashPackField;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.manager.cash.PosCashPackFieldManager;
import top.kx.kxss.app.service.cash.PosCashPackFieldService;
import top.kx.kxss.app.vo.query.cash.PosCashPackFieldPageQuery;
import top.kx.kxss.app.vo.result.cash.PosCashPackFieldResultVO;
import top.kx.kxss.app.vo.save.cash.PosCashPackFieldSaveVO;
import top.kx.kxss.app.vo.update.cash.PosCashPackFieldUpdateVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.app.CashTableStatusEnum;
import top.kx.kxss.model.enumeration.base.ReformPriceTypeEnum;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 包场信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-22 11:37:26
 * @create [2024-04-22 11:37:26] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashPackFieldServiceImpl extends SuperServiceImpl<PosCashPackFieldManager, Long, PosCashPackField, PosCashPackFieldSaveVO,
        PosCashPackFieldUpdateVO, PosCashPackFieldPageQuery, PosCashPackFieldResultVO> implements PosCashPackFieldService {

    @Override
    public boolean save(PosCashPackField packField) {
        return superManager.save(packField);
    }

    @Override
    public PosCashPackField getOne(LbQueryWrap<PosCashPackField> eq) {
        return superManager.getOne(eq);
    }

    @Override
    public boolean updateBatchById(List<PosCashPackField> posCashPackFieldList) {
        return superManager.updateBatchById(posCashPackFieldList);
    }

    @Override
    public List<PosCashTable> refreshTable(List<PosCashTable> tableList, Boolean isStop,
                                           List<PosCashPackField> packFieldList) {

        if (CollUtil.isEmpty(tableList) || CollUtil.isEmpty(packFieldList)) {
            return Lists.newArrayList();
        }
        Map<Long, PosCashTable> tableMap = tableList.stream()
                .filter(t -> ObjectUtil.isNotNull(t.getPackFieldId()))
                .collect(Collectors.toMap(PosCashTable::getPackFieldId, t -> t));
        if (CollUtil.isEmpty(tableMap)) {
            return Lists.newArrayList();
        }
        Map<Long, PosCashPackField> packFieldMap = packFieldList.stream().collect(Collectors.toMap(PosCashPackField::getId, t -> t));
        if (CollUtil.isEmpty(packFieldMap)) {
            return Lists.newArrayList();
        }
        for (Long packFieldId : tableMap.keySet()) {
            PosCashTable posCashTable = tableMap.get(packFieldId);
            PosCashPackField packField = packFieldMap.get(packFieldId);
            if (ObjectUtil.isNull(packField)) {
                continue;
            }
            posCashTable.setOrginPrice(BigDecimal.ZERO);
            posCashTable.setAmount(BigDecimal.ZERO);
            posCashTable.setDiscountAmount(BigDecimal.ZERO);
            posCashTable.setFreeAmount(BigDecimal.ZERO);
            posCashTable.setFreeDuration(0);
            if (posCashTable.getStatus().equals(CashTableStatusEnum.TIMING.getCode())) {
                posCashTable.setEndTime(LocalDateTime.now().withSecond(0).withNano(0));
                long startMinutes = DateUtils.calDifMinutes(posCashTable.getStartTime(), posCashTable.getEndTime());
                posCashTable.setDuration(Integer.valueOf(startMinutes + ""));
                if (isStop != null && isStop) {
                    posCashTable.setStatus(CashTableStatusEnum.STOP.getCode());
                }
            }
            posCashTable.setCycle(packField.getPackFieldAmount().setScale(2, RoundingMode.HALF_UP).toPlainString()
                    + "元/" + packField.getPackFieldDuration() + "分钟");
            posCashTable.setPrice(packField.getPackFieldAmount());
            posCashTable.setAmount(packField.getPackFieldAmount());
            // 原始价格
            posCashTable.setOrginPrice(packField.getPackFieldAmount());
            //改单价
            if (StrUtil.isNotBlank(posCashTable.getReformPriceType())
                    && posCashTable.getReformPriceType().equals(ReformPriceTypeEnum.SINGLE.getCode())) {
                posCashTable.setOldPrice(posCashTable.getPrice());
                posCashTable.setOldOrginPrice(posCashTable.getOldOrginPrice());
                posCashTable.setPrice(posCashTable.getReformPrice());
                posCashTable.setAmount(posCashTable.getReformPrice());
                posCashTable.setOrginPrice(posCashTable.getReformPrice());
                if (StrUtil.isNotBlank(posCashTable.getCycle())
                        && posCashTable.getCycle().split("/").length > 1) {
                    posCashTable.setCycle(posCashTable.getPrice().setScale(2, RoundingMode.HALF_UP)
                            .toPlainString() + "元/"
                            + posCashTable.getCycle().split("/")[1]);
                }

            }
            //改总价
            if (StrUtil.isNotBlank(posCashTable.getReformPriceType())
                    && posCashTable.getReformPriceType().equals(ReformPriceTypeEnum.TOTAL.getCode())) {
                posCashTable.setOldOrginPrice(posCashTable.getOldOrginPrice());
                posCashTable.setAmount(posCashTable.getReformPrice());
                posCashTable.setOrginPrice(posCashTable.getReformPrice());
                if (StrUtil.isNotBlank(posCashTable.getCycle())
                        && posCashTable.getCycle().split("/").length > 1) {
                    posCashTable.setCycle(posCashTable.getPrice().setScale(2, RoundingMode.HALF_UP)
                            .toPlainString() + "元/"
                            + posCashTable.getCycle().split("/")[1]);
                }
            }
        }

        return tableList;
    }

}


