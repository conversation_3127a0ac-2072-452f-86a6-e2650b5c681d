package top.kx.kxss.app.mqtt.properties;


import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import top.kx.basic.constant.Constants;


/**
 * <AUTHOR>
 */
@Setter
@Getter
@ConfigurationProperties(prefix = MQTTProperties.PREFIX)
@RefreshScope
public class MQTTProperties {
    public static final String PREFIX = Constants.PROJECT_PREFIX + ".mqtt";

    /**
     * host 服务器地址配置
     */
    private String[] host;
    /**
     * clientId
     */
    private String clientId;
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;
    /**
     * 主题
     */
    private String[] topics;
    /**
     * 连接超时时长
     */
    private Integer timeout;

    /**
     * keep Alive时间(心跳检测)
     */
    private Integer keepalive;

    /**
     * 控制该数量，以避免网络拥塞
     */
    private Integer maxInflight;

    /**
     * 遗嘱消息 QoS
     */
    private Integer qos;

    /**
     * false为建立持久会话
     */
    private Boolean cleanSession;

    /**
     * 断开后重新连接
     */
    private Boolean automaticReconnect;

}
