package top.kx.kxss.app.service.table;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.app.vo.save.cash.PosCashSaveVO;
import top.kx.kxss.app.vo.save.cash.table.PosCashExchangeTableSaveVO;
import top.kx.kxss.app.vo.save.cash.table.PosCashMergeTableSaveVO;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.vo.query.table.BaseTableInfoPageQuery;
import top.kx.kxss.base.vo.result.table.BaseTableInfoResultVO;
import top.kx.kxss.base.vo.save.table.BaseTableInfoSaveVO;
import top.kx.kxss.base.vo.update.table.BaseTableInfoUpdateVO;

import java.util.Map;


/**
 * <p>
 * 业务接口
 * 台桌操作业务
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-18 23:00:01
 * @create [2023-04-18 23:00:01] [zhou]
 */
public interface TableOperateService extends SuperService<Long, BaseTableInfo, BaseTableInfoSaveVO, BaseTableInfoUpdateVO, BaseTableInfoPageQuery, BaseTableInfoResultVO> {

    Map<String, Object> mergeTable(PosCashMergeTableSaveVO posCashMergeTableSaveVO);

    Map<String, Object> exchangeTable(PosCashExchangeTableSaveVO posCashExchangeTableSaveVO);

    Map<String, Object> orderCancel(PosCashSaveVO posCashSaveVO);

    Map<String, Object> singleRemark(Map<String, Object> param);

    Map<String, Object> singleSale(Map<String, Object> param);

    Map<String, Object> completeOrder(Map<String, Object> param);

    Map<String, Object> unGive(Map<String, Object> param);

    Map<String, Object> disSale(Map<String, Object> param);

    Map<String, Object> disCompleteOrder(Map<String, Object> param);
}


