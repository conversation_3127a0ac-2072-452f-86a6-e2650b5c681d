package top.kx.kxss.app.mapper.cash;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.stereotype.Repository;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.vo.result.cash.*;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentDetailResultVO;
import top.kx.kxss.base.entity.accounting.BaseAccountingInfo;
import top.kx.kxss.base.entity.biz.BaseBizLog;
import top.kx.kxss.base.vo.NameValueVO;
import top.kx.kxss.pos.vo.service.ServiceTableResultVO;
import top.kx.kxss.wxapp.vo.query.statistics.ConsumeQuery;
import top.kx.kxss.wxapp.vo.query.statistics.StatisServiceRankingQuery;
import top.kx.kxss.wxapp.vo.result.statistics.*;
import top.kx.kxss.wxapp.vo.result.statistics.recharge.StatisStaffRechargeResultVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * Mapper 接口
 * pos结算 含商品出库 服务 台费结算
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:04:53
 * @create [2023-04-19 14:04:53] [dou] [代码生成器生成]
 */
@Repository
public interface PosCashMapper extends SuperMapper<PosCash> {


    void deletePosCash(Long id);

    List<AmountResultVO> selectAmount(@Param("field") String field, @Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectListAmount(@Param("field") String field, @Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    AmountResultVO selectOneAmount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<OrderSourceAmountResultVO> selectOrderSourceCount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<OrderSourceAmountResultVO> amountSourceResultVOList(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectAmountByTable(@Param("field") String field, @Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectAmountByPower(@Param("field") String field, @Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectMemAmount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectBySalesType(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<PosCashPaymentDetailResultVO> selectPaymentDetailAmount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectThailBySecurities(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectByPayType(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectByPayTypeCashCount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);


    List<AmountResultVO> selectByMonth(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    Long selectCountByPayType(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    Long selectRefundCountByPayType(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectRefundByPayType(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    Long selectAmountByCashId(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectByDiscountType(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectByTable(@Param("field") String field, @Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectTopByTable(@Param("field") String field, @Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<StatisResultVO> selectByProduct(@Param("field") String field, @Param("fieldName") String fieldName,
                                         @Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<StatisResultVO> selectByService(@Param("field") String field, @Param("fieldName") String fieldName,
                                         @Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<StatisResultVO> selectStatisServiceList(@Param("field") String field, @Param("fieldName") String fieldName,
                                                 @Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    IPage<StatisResultVO> selectByServiceListPage(@Param("page") IPage<StatisResultVO> page, @Param("field") String field, @Param("fieldName") String fieldName,
                                              @Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    IPage<StatisResultVO> getServiceListPage(@Param("page") IPage<StatisResultVO> page, @Param("field") String field, @Param("fieldName") String fieldName,
                                                  @Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<StatisResultVO> selectByServiceList(@Param("field") String field, @Param("fieldName") String fieldName,
                                              @Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    StatisResultVO selectByServiceListSum(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);


    List<StatisResultVO> selectDurationByService(@Param("field") String field, @Param("fieldName") String fieldName,
                                                 @Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<StatisOrderDetailsResultVO> getOrderDetails(@Param("posCashId") Long posCashId);

    List<StatisActualPaymentResultVO> getActualPayment(@Param("posCashId") Long posCashId);

    List<PosCashTagsResultVO> cashTags(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    Long sensitiveCount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<SensitiveVO> sensitiveList(@Param(Constants.WRAPPER) Wrapper<BaseBizLog> wrapper);

    List<SensitiveVO> sensitiveMemberList(@Param(Constants.WRAPPER) Wrapper<BaseBizLog> wrapper);

    List<SensitiveVO> sensitiveMemberBalanceList(@Param(Constants.WRAPPER) Wrapper<BaseBizLog> wrapper);

    Long rechargeCount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectThailAmount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectTableTypeAmount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectServiceAmount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    String selectName(LbQueryWrap<PosCashProduct> queryWrap);

    BigDecimal getAmountByPaymentType(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectAccountInfo(@Param(Constants.WRAPPER) Wrapper<BaseAccountingInfo> wrapper);

    List<AmountResultVO> selectOperateAmount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectTimesByTable(@Param("field") String field, @Param(Constants.WRAPPER) QueryWrapper<PosCash> wrapper);

    List<AmountResultVO> selectBuyCardAmount(@Param(Constants.WRAPPER) QueryWrapper<PosCash> wrap);

    List<AmountResultVO> selectRefundAmount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<StatisStaffRechargeResultVO> selectStaffRecharge(@Param(Constants.WRAPPER) QueryWrapper<PosCash> wrapper);

    List<StatisServiceRankingResultVO> durationRanking(@Param("model") StatisServiceRankingQuery model);

    IPage<StatisPerformanceMemberResultVO> selectPageMemberRanking(@Param("page") IPage<StatisPerformanceMemberResultVO> page, @Param(Constants.WRAPPER) QueryWrapper<PosCash> wrapper);

    List<ServiceTableResultVO> selectPosCashByEmployeeId(List<Long> employeeIdList);

    List<ServiceTableResultVO> selectCashByEmployeeId(@Param(Constants.WRAPPER) QueryWrapper<PosCash> wrapper);

    List<ServiceTableResultVO> selectPosCashByEmployeeIdAndServiceId(List<Long> employeeIdList);

    IPage<PosCashResultVO> selectPageResultVO(IPage<PosCash> page, @Param(Constants.WRAPPER) QueryWrap<PosCash> wrap);

    List<PosCashResultVO> selectPageResultVO(@Param(Constants.WRAPPER) QueryWrap<PosCash> wrap);

    StatisConsumeResultVO consumeSum(@Param("model") ConsumeQuery model);

    /**
     * 开台时长
     *
     * @param wrap
     * @return
     */
    Integer tableDurationSum(@Param(Constants.WRAPPER) QueryWrapper<PosCash> wrap);

    /**
     * 开台时长详细统计（包含套餐和非套餐分别统计）
     *
     * @param wrap
     * @return AmountResultVO 包含总时长、套餐开台时长、非套餐开台时长
     */
    AmountResultVO tableDurationDetailSum(@Param(Constants.WRAPPER) QueryWrapper<PosCash> wrap);

    AmountResultVO selectProductAmount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    AmountResultVO selectTableAmount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);


    /**
     * 按桌台类型统计
     *
     * @param wrapper
     * @return 订单数, 开台时长, 订单实收
     */
    List<AmountResultVO> selectTableAmountByTableType(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);


    List<AmountResultVO> selectTableDuration(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectServiceAmountByEmployee(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    AmountResultVO selectOneServiceAmount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<AmountResultVO> selectProductAmountByProduct(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    MemberAmountResultVO selectOneMemberAmount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    MemberAmountResultVO selectOneMemberProductAmount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    MemberAmountResultVO selectOneMemberServiceAmount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<NameValueVO> newCustomersCount(@Param(Constants.WRAPPER) QueryWrapper<PosCash> wrap);

    PosCash queryOne(Long id);

    List<PosCash> getAnyList(List<Long> ids);

    AmountResultVO selectFreeChangeAmount(@Param(Constants.WRAPPER) QueryWrapper<PosCash> wrap);

//    CounterCheckoutResultVO selectCounterCheckout(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    @Select("SELECT pc.* " +
            "FROM pos_cash pc " +
            "JOIN base_table_info bti ON pc.table_id = bti.id AND bti.delete_flag = 0" +
            " and bti.created_org_id= #{orgId}" +
            " WHERE pc.bill_state IN ('0', '3','4') " +
            "  AND pc.bill_type = '0' and pc.type_ = '0' " +
            "  AND pc.delete_flag = 0 and pc.org_id =#{orgId}")
    List<PosCash> selectPosCashWithConditions(@Param("orgId") Long orgId);


    // 刷新订单的完成人， 如果完成员工不对， 就刷新
    Integer refreshCompleteEmp(@Param("completeEmp") Long completeEmp, @Param("startTime") LocalDateTime startTime, @Param("orgId") Long orgId);

    /**
     * 强制刷新订单完成人
     *
     * @param completeEmp
     * @param startTime
     * @param orgId
     * @return
     */
    Integer forceRefreshCompleteEmp(@Param("completeEmp") Long completeEmp, @Param("startTime") LocalDateTime startTime, @Param("orgId") Long orgId);

    List<NewCustomersStatisResultVO> newCustomersCountList(@Param(Constants.WRAPPER) QueryWrapper<PosCash> wrap, @Param("hour") Integer hour);

    List<AmountResultVO> duration(@Param(Constants.WRAPPER) QueryWrapper<PosCash> wrap);

    List<AmountResultVO> cashDuration(@Param("field") String field, @Param(Constants.WRAPPER) QueryWrapper<PosCash> wrap);


}


