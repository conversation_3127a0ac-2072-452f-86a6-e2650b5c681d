package top.kx.kxss.app.service.cash.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.PhoneUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.jackson.JsonUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.app.entity.cash.PosCashCommenter;
import top.kx.kxss.app.manager.cash.PosCashCommenterManager;
import top.kx.kxss.app.query.PerformanceCommissionConsumeQuery;
import top.kx.kxss.app.service.cash.PosCashCommenterService;
import top.kx.kxss.app.vo.query.cash.PosCashCommenterPageQuery;
import top.kx.kxss.app.vo.result.cash.PosCashCommenterResultVO;
import top.kx.kxss.app.vo.save.cash.PosCashCommenterSaveVO;
import top.kx.kxss.app.vo.update.cash.PosCashCommenterUpdateVO;
import top.kx.kxss.base.entity.biz.BaseBizLog;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.manager.user.BaseEmployeeManager;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.common.cache.CacheKeyTable;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.common.constant.RabbitMqConstant;
import top.kx.kxss.model.enumeration.base.BizLogModuleEnum;
import top.kx.kxss.model.enumeration.base.BizLogTypeEnum;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 订单相关提成人
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-16 19:15:27
 * @create [2024-04-16 19:15:27] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class PosCashCommenterServiceImpl extends SuperServiceImpl<PosCashCommenterManager, Long, PosCashCommenter, PosCashCommenterSaveVO,
        PosCashCommenterUpdateVO, PosCashCommenterPageQuery, PosCashCommenterResultVO> implements PosCashCommenterService {

    @Autowired
    private RabbitTemplate template;
    @Autowired
    private BaseBizLogService baseBizLogManager;
    @Autowired
    private BaseEmployeeManager baseEmployeeManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean remove(LbQueryWrap<PosCashCommenter> eq) {
        return superManager.remove(eq);
    }

    @Override
    public Boolean batchSave(List<PosCashCommenterSaveVO> params) {
        // 只允许设置一种类型
        ArgumentAssert.isTrue(params.size() == 1, "只允许设置一个订单的提成人");
        List<PosCashCommenter> cashCommenterList = superManager.list(Wraps.<PosCashCommenter>lbQ()
                .in(PosCashCommenter::getType, params.stream().map(PosCashCommenterSaveVO::getType).distinct().collect(Collectors.toList()))
                .in(PosCashCommenter::getCashId, params.stream().map(PosCashCommenterSaveVO::getCashId).distinct().collect(Collectors.toList()))
                .in(PosCashCommenter::getSourceId, params.stream().map(PosCashCommenterSaveVO::getSourceId).distinct().collect(Collectors.toList())));
        List<Long> cashCommenterIds = cashCommenterList.stream().map(PosCashCommenter::getEmployeeId).distinct().collect(Collectors.toList());
        List<Long> paramEmployeeIds = params.get(0).getEmployeeIdList();
        List<Long> allEmployeeIds = new ArrayList<>();
        allEmployeeIds.addAll(paramEmployeeIds);
        allEmployeeIds.addAll(cashCommenterIds);
        List<BaseEmployeeResultVO> employeeResultVOList = baseEmployeeManager.findList(Wraps.<BaseEmployee>lbQ().in(BaseEmployee::getId, allEmployeeIds));
        for (PosCashCommenterSaveVO param : params) {
            superManager.update(Wraps.<PosCashCommenter>lbU().set(PosCashCommenter::getDeleteFlag, 1)
                    .eq(PosCashCommenter::getDeleteFlag, 0)
                    .eq(PosCashCommenter::getType, param.getType())
                    .eq(PosCashCommenter::getSourceId, param.getSourceId())
                    .eq(PosCashCommenter::getCashId, param.getCashId()));
        }
        List<PosCashCommenter> beanList = new ArrayList<>();
        for (PosCashCommenterSaveVO param : params) {
            for (Long employeeId : param.getEmployeeIdList()) {
                PosCashCommenter commenter = BeanPlusUtil.toBean(param, PosCashCommenter.class);
                commenter.setEmployeeId(employeeId);
                beanList.add(commenter);
            }
        }
        boolean b = super.saveBatch(beanList);

        Long cashId = params.get(0).getCashId();
        String type = params.get(0).getType();

        String cashCommenterStr = "-";
        if (CollUtil.isNotEmpty(cashCommenterIds)) {
            cashCommenterStr = employeeResultVOList.stream().filter(s-> cashCommenterIds.contains(s.getId())).map(BaseEmployeeResultVO::getName).distinct().collect(Collectors.joining( ","));
        }
        String paramEmployeeStr = "-";
        if (CollUtil.isNotEmpty(paramEmployeeIds)) {
            paramEmployeeStr = employeeResultVOList.stream().filter(s-> paramEmployeeIds.contains(s.getId())).map(BaseEmployeeResultVO::getName).distinct().collect(Collectors.joining( ","));
        }


        baseBizLogManager.createBizLog(BaseBizLog.builder()
                .tenantId(ContextUtil.getTenantId())
                .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                .description("修改" + (StringUtils.equals(type, PosCashConstant.Event.ORDER.getCode()) ? "整单" : "单品") + "提成人" + "【" + cashCommenterStr + ">>>" + paramEmployeeStr + "】")
                .bizModule(BizLogModuleEnum.SETTING_COMMENTER.getCode())
                .type(BizLogTypeEnum.CREATED.getCode())
                .sourceId(cashId).remarks("-")
                .sn(ContextUtil.getSn())
                .build());

        template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.PERFORMANCE_COMMISSION,
                JsonUtil.toJson(PerformanceCommissionConsumeQuery.builder()
                        .posCashIds(Collections.singletonList(cashId))
                        .orgId(ContextUtil.getCurrentCompanyId())
                        .tenantId(ContextUtil.getTenantId())
                        .build()));
        return b;
    }

    @Override
    public boolean updateBatchById(List<PosCashCommenter> posCashCommenterList) {
        return superManager.updateBatchById(posCashCommenterList);
    }
}


