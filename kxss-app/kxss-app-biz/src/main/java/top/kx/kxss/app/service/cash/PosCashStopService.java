package top.kx.kxss.app.service.cash;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.app.entity.cash.PosCashStop;
import top.kx.kxss.app.vo.save.cash.PosCashStopSaveVO;
import top.kx.kxss.app.vo.update.cash.PosCashStopUpdateVO;
import top.kx.kxss.app.vo.result.cash.PosCashStopResultVO;
import top.kx.kxss.app.vo.query.cash.PosCashStopPageQuery;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 明细停止记录
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-14 13:42:52
 * @create [2024-10-14 13:42:52] [dou] [代码生成器生成]
 */
public interface PosCashStopService extends SuperService<Long, PosCashStop, PosCashStopSaveVO,
    PosCashStopUpdateVO, PosCashStopPageQuery, PosCashStopResultVO> {

    Boolean save(PosCashStop cashStop);

    Boolean updateBatchById(List<PosCashStop> posCashStopList);

}


