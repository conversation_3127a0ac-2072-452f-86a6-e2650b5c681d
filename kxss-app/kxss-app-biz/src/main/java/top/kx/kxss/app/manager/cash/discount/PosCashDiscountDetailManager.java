package top.kx.kxss.app.manager.cash.discount;

import top.kx.basic.base.manager.SuperManager;
import top.kx.kxss.app.entity.cash.discount.PosCashDiscountDetail;

/**
 * <p>
 * 通用业务接口
 * 订单优惠明细
 * </p>
 *
 * <AUTHOR>
 * @date 2023-08-02 18:45:43
 * @create [2023-08-02 18:45:43] [dou] [代码生成器生成]
 */
public interface PosCashDiscountDetailManager extends SuperManager<PosCashDiscountDetail> {

    void deleteByPosCashId(Long id);
}


