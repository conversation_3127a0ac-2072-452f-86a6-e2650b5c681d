package top.kx.kxss.app.manager.cash.discount.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.app.entity.cash.discount.PosCashDiscountDetail;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.app.manager.cash.discount.PosCashDiscountDetailManager;
import top.kx.kxss.app.mapper.cash.discount.PosCashDiscountDetailMapper;

/**
 * <p>
 * 通用业务实现类
 * 订单优惠明细
 * </p>
 *
 * <AUTHOR>
 * @date 2023-08-02 18:45:43
 * @create [2023-08-02 18:45:43] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashDiscountDetailManagerImpl extends SuperManagerImpl<PosCashDiscountDetailMapper, PosCashDiscountDetail> implements PosCashDiscountDetailManager {

    @Override
    public void deleteByPosCashId(Long posCashId) {
        baseMapper.deleteByPosCashId(posCashId);
    }
}


