package top.kx.kxss.app.service.pay.impl;

import cn.hutool.core.util.ObjectUtil;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.payments.jsapi.JsapiServiceExtension;
import com.wechat.pay.java.service.payments.jsapi.model.QueryOrderByOutTradeNoRequest;
import com.wechat.pay.java.service.payments.model.Transaction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.AesUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.properties.WxPayProperties;
import top.kx.kxss.app.service.pay.WxPayService;
import top.kx.kxss.base.properties.WxComponentProperties;
import top.kx.kxss.system.entity.system.DefApplet;
import top.kx.kxss.system.entity.system.DefClient;
import top.kx.kxss.system.service.system.DefAppletService;
import top.kx.kxss.system.service.system.DefClientService;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;

/**
 * <p>
 * 业务实现类
 * 微信支付接口
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@EnableConfigurationProperties({WxPayProperties.class, WxComponentProperties.class})
public class WxPayServiceImpl implements WxPayService {
    @Autowired
    private DefAppletService defAppletService;
    @Autowired
    private DefClientService defClientService;
    private final WxPayProperties wxPayProperties;
    private final WxComponentProperties wxComponentProperties;

    @Override
    public Transaction queryOrderByOutTradeNo(Long posCashId) {
        Config config = getConfig();
        JsapiServiceExtension service = new JsapiServiceExtension.Builder().config(config).build();
        QueryOrderByOutTradeNoRequest request = new QueryOrderByOutTradeNoRequest();
        request.setMchid(config.createCredential().getMerchantId());
        request.setOutTradeNo(posCashId.toString());
        Transaction transaction = service.queryOrderByOutTradeNo(request);
        if (ObjectUtil.isNull(transaction) || !ObjectUtil.equal(transaction.getTradeState(), Transaction.TradeStateEnum.SUCCESS)) {
            return null;
        }
        return transaction;
    }

    public Config getConfig() {
        //应用信息
        DefClient defClient = defClientService.getById(Wraps.<DefClient>lbQ()
                .eq(DefClient::getClientId, ContextUtil.getClientId()).eq(DefClient::getState, true));
        DefApplet defApplet = defAppletService.getOne(Wraps.<DefApplet>lbQ()
                .eq(DefApplet::getDeleteFlag, 0).eq(DefApplet::getClientId, defClient.getId())
                .apply("JSON_CONTAINS(tenant_id, '" + ContextUtil.getTenantId() + "')"));
        ArgumentAssert.notNull(defApplet, "商户未绑定应用!");
        return new RSAAutoCertificateConfig.Builder()
                .merchantId(defApplet.getMchId() + "").privateKeyFromPath(wxPayProperties.getPrivateKeyFromPath())
                .merchantSerialNumber(wxPayProperties.getMerchantSerialNumber()).apiV3Key(wxPayProperties.getApiV3Key())
                .build();
    }

    /**
     * 解析回调报文
     *
     * @param associatedData
     * @param nonce
     * @param ciphertext
     * @return
     */
    @Override
    public String decryptToString(String associatedData, String nonce, String ciphertext) {
        String cert = null;
        try {
            //简化
            AesUtil aesUtil = new AesUtil(wxPayProperties.getApiV3Key().getBytes(StandardCharsets.UTF_8));
            cert = aesUtil.decryptToString(associatedData.getBytes(StandardCharsets.UTF_8), nonce.getBytes(StandardCharsets.UTF_8), ciphertext);
        } catch (GeneralSecurityException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return cert;
    }

    @Override
    public String componentDecryptToString(String associatedData, String nonce, String ciphertext) {
        String cert = null;
        try {
            //简化
            AesUtil aesUtil = new AesUtil(wxComponentProperties.getApiV3Key().getBytes(StandardCharsets.UTF_8));
            cert = aesUtil.decryptToString(associatedData.getBytes(StandardCharsets.UTF_8), nonce.getBytes(StandardCharsets.UTF_8), ciphertext);
        } catch (GeneralSecurityException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return cert;
    }
}
