package top.kx.kxss.app.service.cash.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.app.entity.cash.PosCashStop;
import top.kx.kxss.app.manager.cash.PosCashStopManager;
import top.kx.kxss.app.service.cash.PosCashStopService;
import top.kx.kxss.app.vo.query.cash.PosCashStopPageQuery;
import top.kx.kxss.app.vo.result.cash.PosCashStopResultVO;
import top.kx.kxss.app.vo.save.cash.PosCashStopSaveVO;
import top.kx.kxss.app.vo.update.cash.PosCashStopUpdateVO;
import top.kx.kxss.common.constant.DsConstant;

import java.util.List;

/**
 * <p>
 * 业务实现类
 * 明细停止记录
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-14 13:42:52
 * @create [2024-10-14 13:42:52] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashStopServiceImpl extends SuperServiceImpl<PosCashStopManager, Long, PosCashStop, PosCashStopSaveVO,
    PosCashStopUpdateVO, PosCashStopPageQuery, PosCashStopResultVO> implements PosCashStopService {


    @Override
    public Boolean save(PosCashStop cashStop) {
        return superManager.save(cashStop);
    }

    @Override
    public Boolean updateBatchById(List<PosCashStop> posCashStopList) {
        return superManager.updateBatchById(posCashStopList);
    }
}


