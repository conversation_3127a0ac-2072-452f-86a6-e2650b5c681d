package top.kx.kxss.app.manager.cash.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.app.entity.cash.PosCashStop;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.app.manager.cash.PosCashStopManager;
import top.kx.kxss.app.mapper.cash.PosCashStopMapper;

/**
 * <p>
 * 通用业务实现类
 * 明细停止记录
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-14 13:42:52
 * @create [2024-10-14 13:42:52] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashStopManagerImpl extends SuperManagerImpl<PosCashStopMapper, PosCashStop> implements PosCashStopManager {

}


