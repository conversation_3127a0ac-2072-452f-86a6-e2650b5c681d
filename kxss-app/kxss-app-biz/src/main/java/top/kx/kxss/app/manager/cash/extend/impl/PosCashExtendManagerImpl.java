package top.kx.kxss.app.manager.cash.extend.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.app.entity.cash.extend.PosCashExtend;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.app.manager.cash.extend.PosCashExtendManager;
import top.kx.kxss.app.mapper.cash.extend.PosCashExtendMapper;

/**
 * <p>
 * 通用业务实现类
 * 订单续时
 * </p>
 *
 * <AUTHOR>
 * @date 2023-10-21 17:10:25
 * @create [2023-10-21 17:10:25] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashExtendManagerImpl extends SuperManagerImpl<PosCashExtendMapper, PosCashExtend> implements PosCashExtendManager {

}


