package top.kx.kxss.app.manager.cash;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.kx.basic.base.manager.SuperManager;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.vo.result.cash.PosCashResultVO;
import top.kx.kxss.pos.vo.service.ServiceTableResultVO;
import top.kx.kxss.wxapp.vo.query.statistics.ConsumeQuery;
import top.kx.kxss.wxapp.vo.result.statistics.StatisConsumeResultVO;

import java.util.List;

/**
 * <p>
 * 通用业务接口
 * pos结算 含商品出库 服务 台费结算
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:04:53
 * @create [2023-04-19 14:04:53] [dou] [代码生成器生成]
 */
public interface PosCashManager extends SuperManager<PosCash> {

    void deletePosCash(Long id);

    List<ServiceTableResultVO> selectPosCashByEmployeeId(List<Long> employeeIdList);

    List<ServiceTableResultVO> selectCashByEmployeeId(QueryWrapper<PosCash> wrapper);

    List<ServiceTableResultVO> selectPosCashByEmployeeIdAndServiceId(List<Long> employeeIdList);

    IPage<PosCashResultVO> selectPageResultVO(IPage<PosCash> page, QueryWrap<PosCash> wrap);

    List<PosCashResultVO> selectAllResultVO(QueryWrap<PosCash> posCashQueryWrap);

    StatisConsumeResultVO consumeSum(ConsumeQuery params);

    /**
     * 删除的也会查出
     * @param id
     * @return
     */
    PosCash queryOne(Long id);

    List<PosCash> getAnyList(List<Long> ids);

    List<PosCash> selectPosCashWithConditions();
}


