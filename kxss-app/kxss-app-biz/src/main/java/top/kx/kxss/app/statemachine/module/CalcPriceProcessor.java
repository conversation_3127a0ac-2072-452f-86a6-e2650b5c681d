package top.kx.kxss.app.statemachine.module;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.exception.BizException;
import top.kx.basic.exception.code.ExceptionCode;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.manager.cash.PosCashManager;
import top.kx.kxss.app.service.cash.table.PosCashTableService;
import top.kx.kxss.app.statemachine.AbstractPosCashProcessor;
import top.kx.kxss.app.statemachine.annotation.PosCashProcessor;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.vo.save.biz.BaseBizLogSaveVO;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.model.enumeration.app.CashTableStatusEnum;
import top.kx.kxss.model.enumeration.base.BizLogModuleEnum;
import top.kx.kxss.model.enumeration.base.BizLogTypeEnum;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;

import static top.kx.basic.context.ContextUtil.getCurrentCompanyId;

/**
 * 计算价格
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@PosCashProcessor
public class CalcPriceProcessor extends AbstractPosCashProcessor {

    @Autowired
    private PosCashManager posCashManager;
    @Autowired
    private PosCashTableService posCashTableService;
    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private BaseBizLogService bizLogService;


    public CalcPriceProcessor() {
        super.setBillState(PosCashConstant.Event.CALC_PRICE.getEventId());
    }

    @Override
    public boolean process(Long posCashId, Object... params) {
        PosCash posCash = (PosCash) params[0];
        Boolean lock = false;
        try {
            lock = distributedLock.lock(posCashId + PosCashConstant.Event.CALC_PRICE.getCode(),0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            BigDecimal tableAmount = BigDecimal.ZERO;
            List<PosCashTable> cashTableList = posCashTableService.list(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, posCashId)
                    .eq(PosCashTable::getStatus, CashTableStatusEnum.STOP.getCode()));
            //计算总价格
            double sum = cashTableList.stream().mapToDouble(v -> v.getAmount().doubleValue()).sum();
            tableAmount = BigDecimal.valueOf(sum).setScale(2, RoundingMode.UP);
            //计算台桌价格
            posCash.setTableAmount(tableAmount);
            posCash.setAmount(tableAmount);
            posCash.setUnpaid(posCash.getAmount());
            posCash.setPayment(posCash.getAmount());
            //停止计时
            posCash.setUpdatedTime(LocalDateTime.now());
            posCash.setUpdatedBy(ContextUtil.getUserId());
            posCashManager.updateById(posCash);
            //新增操作日志
            bizLogService.save(BaseBizLogSaveVO.builder()
                    .orgId(getCurrentCompanyId()).description("计算价格")
                    .bizModule(BizLogModuleEnum.POS_CASH.getCode()).type(BizLogTypeEnum.UPDATED.getCode())
                    .employeeId(ContextUtil.getEmployeeId()).createdOrgId(getCurrentCompanyId())
                    .opearteTime(LocalDateTime.now()).sourceId(posCash.getId()).remarks("计算价格")
                    .build());
        } finally {
            if (lock) {
                distributedLock.releaseLock(posCashId + PosCashConstant.Event.CALC_PRICE.getCode());
            }
        }
        return true;
    }

}
