package top.kx.kxss.app.manager.cash.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.app.entity.cash.PosCashCommenter;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.app.manager.cash.PosCashCommenterManager;
import top.kx.kxss.app.mapper.cash.PosCashCommenterMapper;

/**
 * <p>
 * 通用业务实现类
 * 订单相关提成人
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-16 19:15:27
 * @create [2024-04-16 19:15:27] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashCommenterManagerImpl extends SuperManagerImpl<PosCashCommenterMapper, PosCashCommenter> implements PosCashCommenterManager {

}


