package top.kx.kxss.app.service.shopping.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.exception.BizException;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.equity.PosCashEquity;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.manager.cash.PosCashManager;
import top.kx.kxss.app.manager.cash.equity.PosCashEquityManager;
import top.kx.kxss.app.manager.cash.product.PosCashProductManager;
import top.kx.kxss.app.mapper.member.MemberMapper;
import top.kx.kxss.app.mapper.product.ProductMapper;
import top.kx.kxss.app.service.cash.PosCashServiceService;
import top.kx.kxss.app.service.shopping.ShoppingService;
import top.kx.kxss.app.service.table.CalculateBizService;
import top.kx.kxss.app.vo.query.cash.*;
import top.kx.kxss.app.vo.query.cash.product.PosCashProductNumVO;
import top.kx.kxss.app.vo.result.cash.PosCashAmountVO;
import top.kx.kxss.app.vo.result.cash.QueryShopDetailVO;
import top.kx.kxss.app.vo.result.cash.product.PosCashProductResultVO;
import top.kx.kxss.app.vo.result.product.AppProductResultVo;
import top.kx.kxss.base.entity.coupon.BaseCouponRange;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.member.coupon.MemberCoupon;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.product.category.BaseProductCategory;
import top.kx.kxss.base.entity.stock.BaseProductStock;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.entity.warehouse.BaseWarehouse;
import top.kx.kxss.base.manager.coupon.BaseCouponRangeManager;
import top.kx.kxss.base.manager.member.MemberInfoManager;
import top.kx.kxss.base.manager.member.coupon.MemberCouponManager;
import top.kx.kxss.base.manager.product.BaseProductManager;
import top.kx.kxss.base.manager.product.category.BaseProductCategoryManager;
import top.kx.kxss.base.manager.stock.BaseProductStockManager;
import top.kx.kxss.base.manager.user.BaseEmployeeManager;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.service.table.BaseTableInfoService;
import top.kx.kxss.base.service.warehouse.BaseWarehouseService;
import top.kx.kxss.base.vo.query.product.BaseProductPageQuery;
import top.kx.kxss.base.vo.query.product.category.BaseProductCategoryPageQuery;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.member.coupon.MemberCouponResultVO;
import top.kx.kxss.base.vo.result.product.category.BaseProductCategoryResultVO;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.base.vo.save.biz.BaseBizLogSaveVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.base.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static top.kx.basic.context.ContextUtil.getCurrentCompanyId;

/**
 * <p>
 * 业务实现类
 * 购物相关接口
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-18 23:00:01
 * @create [2023-04-18 23:00:01] [zhou]
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class ShoppingServiceImpl implements ShoppingService {

    @Autowired
    private ProductMapper productMapper;
    @Autowired
    private BaseProductCategoryManager baseProductCategoryManager;
    @Autowired
    private BaseProductManager baseProductManager;
    @Autowired
    private MemberInfoManager memberInfoManager;
    @Autowired
    private MemberMapper memberMapper;
    @Autowired
    private PosCashManager posCashManager;
    @Autowired
    private PosCashProductManager posCashProductManager;
    @Autowired
    private BaseProductStockManager baseProductStockManager;
    @Autowired
    private CalculateBizService calculateBizService;
    @Autowired
    private BaseTableInfoService tableService;
    @Autowired
    private MemberCouponManager memberCouponManager;
    @Autowired
    private BaseCouponRangeManager baseCouponRangeManager;
    @Autowired
    private PosCashEquityManager cashEquityManager;
    @Autowired
    private PosCashServiceService posCashServiceService;
    @Autowired
    private BaseEmployeeManager employeeManager;
    @Autowired
    private BaseWarehouseService baseWarehouseService;
    @Autowired
    private BaseBizLogService bizLogService;

    @Override
    public MemberInfoResultVO getMemberByPhone(String phone) {
        ArgumentAssert.notBlank(phone, "请选择会员");
        MemberInfo memberInfo = memberInfoManager.getOne(Wraps.<MemberInfo>lbQ().eq(MemberInfo::getMobile, phone).last("limit 1"));
        if (null == memberInfo) {
            return null;
        }
        MemberInfoResultVO resultVO = BeanUtil.copyProperties(memberInfo, MemberInfoResultVO.class);
        return resultVO;
    }

    @Override
    @DS(DsConstant.BASE_TENANT)
    public List<AppProductResultVo> queryProducts(BaseProductPageQuery query) {
        List<AppProductResultVo> products = productMapper.queryProducts(query);
        return products;
    }

    @Override
    @DS(DsConstant.BASE_TENANT)
    public List<BaseProductCategoryResultVO> queryProductCategory(BaseProductCategoryPageQuery query) {
        List<BaseProductCategory> categories = baseProductCategoryManager.list(Wraps.<BaseProductCategory>lbQ().eq(BaseProductCategory::getState, "1").eq(BaseProductCategory::getTreeGrade, 1).orderByDesc(BaseProductCategory::getSortValue));
        return BeanUtil.copyToList(categories, BaseProductCategoryResultVO.class);
    }

    @Override
    public QueryShopDetailVO queryShopDetail(Long posCashId) {
        QueryShopDetailVO q = QueryShopDetailVO.builder().build();
        // 1. 购物结算信息 pos_cash
        PosCash posCash = posCashManager.getById(posCashId);
        try {
            if (null == posCash) {
                posCash = posCashManager.getOne(Wraps.<PosCash>lbQ().eq(PosCash::getOrgId, ContextUtil.getCurrentCompanyId())
                        .eq(PosCash::getType, PosCashTypeEnum.SHOPPING.getCode())
                        .eq(PosCash::getBillType, PosCashBillTypeEnum.REGULAR_SINGLE.getCode())
                        .eq(PosCash::getBillState, PosCashBillStateEnum.NO_SETTLED.getCode()));
            }
        } catch (Exception e) {
            log.error("posCash中存在多个已记录");
            throw new BizException("购物信息异常");
        } finally {
            if (null == posCash) {
                return q;
            }
        }
        q.setPosCash(posCash);
        // 2. 获取会员信息
        if (null != posCash.getMemberId()) {
            Map<String, Object> memberInfo = memberMapper.getMemberInfo(posCash.getMemberId());
            BigDecimal giftAmount = null == memberInfo.get("giftAmount") ? new BigDecimal(0) : (BigDecimal) memberInfo.get("giftAmount");
            BigDecimal rechargeAmount = null == memberInfo.get("rechargeAmount") ? new BigDecimal(0) : (BigDecimal) memberInfo.get("rechargeAmount");
            memberInfo.put("balance", giftAmount.add(rechargeAmount));
            q.setMemberInfo(memberInfo);
        }

        BigDecimal orgTotal = BigDecimal.ZERO;
        BigDecimal amountTotal = BigDecimal.ZERO;
        //3. 获取商品
        List<PosCashProduct> products = posCashProductManager.list(Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getCashId, posCash.getId()).orderByDesc(PosCashProduct::getCreatedTime));
        // 获取商品最低价
        List<Long> productIds = products.stream().map(PosCashProduct::getProductId).collect(Collectors.toList());
        List<BaseProduct> baseProducts = baseProductManager.list(Wraps.<BaseProduct>lbQ().in(BaseProduct::getId, productIds));
        Map<Long, BaseProduct> baseProductMap = baseProducts.stream().collect(Collectors.toMap(BaseProduct::getId, e -> e));
        List<PosCashProductResultVO> productResultVOS = BeanUtil.copyToList(products, PosCashProductResultVO.class);
        for (PosCashProductResultVO vo : productResultVOS) {
            orgTotal = orgTotal.add(vo.getOrginPrice());
            amountTotal = amountTotal.add(vo.getAmount());
            if (null != baseProductMap.get(vo.getProductId())) {
                if (null != baseProductMap.get(vo.getProductId()).getMinPrice()) {
                    vo.setMinPrice(baseProductMap.get(vo.getProductId()).getMinPrice());
                }
            }
        }
        q.setProductResultVOS(productResultVOS);
        // 获取优惠劵数量
        if (null != posCash.getMemberId()) {
            Long couponCnt = memberCouponManager.count(Wraps.<MemberCoupon>lbQ().eq(MemberCoupon::getMemberId, posCash.getMemberId()).eq(MemberCoupon::getStatus, "1"));
            q.setCouponCnt(couponCnt);
        }
        // 调用结算接口 展示费用信息 TODO
        q.setOrgTotal(orgTotal);
        q.setAmountTotal(amountTotal);
        // 8. 调用结算接口 展示费用信息 TODO
        Map<String, Object> param = new HashMap<>();
        param.put("cashId", posCash.getId());
        param.put("needUpdate", false);
        LinkedHashMap<String, Object> posResult = posCashServiceService.tablePosCash(param);
        q.setPosResult(posResult);
        PosCashAmountVO totalVo = (PosCashAmountVO) posResult.get("totalCash");
        if (posCash.getAmount() == null || posCash.getAmount().compareTo(totalVo.getAmount()) != 0) {
            posCash.setAmount(totalVo.getAmount());
            posCash.setDiscountAmount(totalVo.getCouponAmount());
            posCash.setPayment(totalVo.getResultAmount());
            posCash.setUpdatedBy(ContextUtil.getUserId());
            posCash.setUpdatedTime(LocalDateTime.now());
            posCashManager.updateById(posCash);
        }
        return q;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveProduct(PosCashProductNumVO numVO) {
        Long productId = numVO.getId();
        Long posCashId = numVO.getPosCashId();
        PosCash posCash = getShopPosCash(posCashId);
        posCashId = ObjectUtil.isNull(posCashId) ? posCash.getId() : posCashId;
        BaseProduct product = baseProductManager.getById(productId);
        // 判断是否可以购买(库存量)
        BaseWarehouse baseWarehouse = baseWarehouseService.getOneBySn();
        ArgumentAssert.notNull(baseWarehouse, "当前设备未选择销售仓库,请选择后重试");
        BaseProductStock baseProductStock = baseProductStockManager.getOne(Wraps.<BaseProductStock>lbQ()
                .eq(BaseProductStock::getProductId, productId).eq(BaseProductStock::getWarehouseId, baseWarehouse.getId()));
        ArgumentAssert.isFalse(null != baseProductStock && 0 > baseProductStock.getNum() - 1
                && !product.getIsNegative(), "该商品库存不足");
        // 计算商品现价
        BigDecimal price = calculateBizService.calProductAmount(product, posCash.getMemberId());
        Long orgId = ContextUtil.getCurrentCompanyId();

        // 查看是否已有 discount为空 或者 discount为0的数据的 该商品
        PosCashProduct preProduct = posCashProductManager.getOne(Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getCashId, posCash.getId()).eq(PosCashProduct::getProductId, productId).and(e -> e.isNull(PosCashProduct::getDiscount).or().eq(PosCashProduct::getDiscount, 0.00).or().eq(PosCashProduct::getDiscount, 0)));
        if (null == preProduct) {
            // 如果没有 discount为空 或者 discount为0的数据，则插入一条新订单商品
            // 保存至cash_product
            PosCashProduct posCashProduct = new PosCashProduct();
            posCashProduct.setCashId(posCashId);
            posCashProduct.setProductId(productId);
            posCashProduct.setProductName(product.getName());
            posCashProduct.setNum(1);
            posCashProduct.setPrice(price);
            posCashProduct.setOrginPrice(price.multiply(new BigDecimal(posCashProduct.getNum())));
            posCashProduct.setAmount(price.multiply(new BigDecimal(posCashProduct.getNum())));
            posCashProduct.setIsGift(false);
            posCashProduct.setCreatedOrgId(orgId);
            posCashProduct.setCreatedTime(LocalDateTime.now());
            posCashProduct.setUpdatedTime(null);
            posCashProduct.setSn(ContextUtil.getSn());
            posCashProduct.setWarehouseId(baseWarehouse.getId());
            boolean suc = posCashProductManager.save(posCashProduct);
            ArgumentAssert.isFalse(!suc, "商品添加失败");
        } else {
            // 有 则直接增加商品数量
            preProduct.setNum(preProduct.getNum() + 1);
            preProduct.setAmount(price.multiply(new BigDecimal(preProduct.getNum())));
            preProduct.setOrginPrice(price.multiply(new BigDecimal(preProduct.getNum())));
            preProduct.setUpdatedTime(LocalDateTime.now());
            boolean suc = posCashProductManager.updateById(preProduct);
            ArgumentAssert.isFalse(!suc, "商品添加失败");
        }

        // 更新 base_product_stock
        if (null != baseProductStock) {
            baseProductStock.setNum(baseProductStock.getNum() - 1);
            baseProductStock.setUpdatedTime(LocalDateTime.now());
            boolean suc = baseProductStockManager.updateById(baseProductStock);
            ArgumentAssert.isFalse(!suc, "库存更新失败");
        }
        // 调用计算接口
        if (null != posCash.getPayment() && posCash.getPayment().compareTo(new BigDecimal(0)) != 0) {
            posCash.setPayment(posCash.getPayment().add(price));
            posCash.setUpdatedBy(ContextUtil.getUserId());
            posCash.setUpdatedTime(LocalDateTime.now());
            posCashManager.updateById(posCash);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean productNum(PosCashProductNumVO saveVO) {
        ArgumentAssert.isFalse(saveVO.getNum() <= 0, "修改数量必须大于0");
        PosCashProduct posCashProduct = posCashProductManager.getById(saveVO.getId());
        int preStock = posCashProduct.getNum();
        // 判断库存
        long productId = posCashProduct.getProductId();
        BaseProduct baseProduct = baseProductManager.getById(productId);
        BaseProductStock baseProductStock = baseProductStockManager.getOne(Wraps.<BaseProductStock>lbQ().eq(BaseProductStock::getProductId, posCashProduct.getProductId()));
        ArgumentAssert.isFalse(null != baseProductStock && 0 > baseProductStock.getNum() - saveVO.getNum() && !baseProduct.getIsNegative(), "该商品库存不足");

        //重新计算金额
        BigDecimal orginPrice = posCashProduct.getPrice().multiply(new BigDecimal(saveVO.getNum()));
        BigDecimal amount = calculateBizService.orginPriceCountAmount(orginPrice, posCashProduct.getType(), posCashProduct.getDiscount());

        posCashProduct.setOrginPrice(orginPrice);
        posCashProduct.setAmount(amount);
        posCashProduct.setNum(saveVO.getNum());
        posCashProduct.setUpdatedTime(LocalDateTime.now());
        boolean suc = posCashProductManager.updateById(posCashProduct);
        if (suc) {
            // 减少库存
            int stock = -1;
            if (saveVO.getNum() > preStock) {
                stock = 1;
            }
            // 更新 base_product_stock
            if (null != baseProductStock) {
                baseProductStock.setNum(baseProductStock.getNum() - stock);
                baseProductStock.setUpdatedTime(LocalDateTime.now());
                suc = baseProductStockManager.updateById(baseProductStock);
                ArgumentAssert.isFalse(!suc, "库存更新失败");
            }
            return suc;
        } else {
            throw new BizException("商品数量更新失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delProduct(Long id) {
        // 删除pos_cash_product
        PosCashProduct cashProduct = posCashProductManager.getById(id);
        ArgumentAssert.notNull(cashProduct, "商品不存在");
        // 增加回库存
        int num = cashProduct.getNum();
        long productId = cashProduct.getProductId();
        boolean suc = posCashProductManager.removeById(id);
        ArgumentAssert.isFalse(!suc, "删除失败");
        BaseProductStock baseProductStock = baseProductStockManager.getOne(Wraps.<BaseProductStock>lbQ().eq(BaseProductStock::getProductId, productId));
        if (null != baseProductStock) {
            baseProductStock.setNum(baseProductStock.getNum() + num);
            baseProductStock.setUpdatedTime(LocalDateTime.now());
            suc = baseProductStockManager.updateById(baseProductStock);
            ArgumentAssert.isFalse(!suc, "库存更新失败");
            return suc;
        }
        return false;
    }

    @Override
    public Boolean bindMember(PosCashMemberQuery query) {
        Long memberId = query.getMemberId();
        Long posCashId = query.getPosCashId();
        PosCash posCash = getShopPosCash(posCashId);
        posCashId = ObjectUtil.isNull(posCashId) ? posCash.getId() : posCashId;
        posCash.setMemberId(memberId);
        posCash.setUpdatedBy(ContextUtil.getUserId());
        posCash.setUpdatedTime(LocalDateTime.now());
        boolean suc = posCashManager.updateById(posCash);
        ArgumentAssert.isFalse(!suc, "结算表更新失败");

        // 商品
        List<PosCashProduct> cashProducts = posCashProductManager.list(Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getCashId, posCashId).eq(PosCashProduct::getIsGift, "0"));
        if (null != cashProducts && 0 != cashProducts.size()) {
            List<Long> productIds = cashProducts.stream().map(PosCashProduct::getProductId).collect(Collectors.toList());
            List<BaseProduct> products = baseProductManager.list(Wraps.<BaseProduct>lbQ().in(BaseProduct::getId, productIds));
            Map<Long, BaseProduct> productMap = products.stream().collect(Collectors.toMap(BaseProduct::getId, e -> e));
            for (PosCashProduct cashProduct : cashProducts) {
                BaseProduct product = productMap.get(cashProduct.getProductId());
                BigDecimal price = calculateBizService.calProductAmount(product, posCash.getMemberId());
                cashProduct.setPrice(price);
                // 计算总价
                BigDecimal amount = price.multiply(new BigDecimal(cashProduct.getNum())).setScale(2, BigDecimal.ROUND_HALF_UP);
                cashProduct.setAmount(amount);
            }
            suc = posCashProductManager.updateBatchById(cashProducts);
            ArgumentAssert.isFalse(!suc, "会员添加失败");
        }
        return true;
    }

    @Override
    public List<MemberCouponResultVO> memberCoupon(Long memberId) {
        List<MemberCoupon> coupons = memberCouponManager.list(Wraps.<MemberCoupon>lbQ().eq(MemberCoupon::getMemberId, memberId)
                .eq(MemberCoupon::getStatus, MemberCouponStatusEnum.NO_USE.getCode()).apply("DATE_FORMAT(expires_time,'%Y-%m-%d %H:%i:%s') > DATE_FORMAT(now,'%Y-%m-%d %H:%i:%s')")
                .orderByAsc(MemberCoupon::getName));
        List<MemberCouponResultVO> memberCouponResultVOS = BeanUtil.copyToList(coupons, MemberCouponResultVO.class);
        List<Long> couponIds = memberCouponResultVOS.stream().map(MemberCouponResultVO::getCouponId).collect(Collectors.toList());
        // 获取优惠劵使用范围
        List<BaseCouponRange> ranges = baseCouponRangeManager.list(Wraps.<BaseCouponRange>lbQ().in(BaseCouponRange::getCouponId, couponIds));
        Map<Long, List<BaseCouponRange>> rangeMap = ranges.stream().collect(Collectors.groupingBy(BaseCouponRange::getCouponId));
        for (MemberCouponResultVO vo : memberCouponResultVOS) {
            List<BaseCouponRange> range = rangeMap.get(vo.getCouponId());
            vo.setRanges(range);
        }
        return memberCouponResultVOS;
    }

    @Override
    public Boolean singleRemark(PosCashSingleRemarkQuery query) {
        PosCashProduct posCashProduct = posCashProductManager.getById(query.getId());
        JSONObject remarksObj = convert2JSON(posCashProduct.getRemarks());
        // 单品备注
        remarksObj.put("productRemarks", query.getRemarks());
        posCashProduct.setRemarks(remarksObj.toJSONString());
        ArgumentAssert.isFalse(!posCashProductManager.update(posCashProduct, Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getId, query.getId())), "添加单品备注失败");
        return true;
    }


    @Override
    public Boolean singleSale(PosCashSingleSaleQuery query) {
        Boolean suc = false;
        // 计算价格
        if ("1".equals(query.getType())) {
            PosCashProduct posCashProduct = posCashProductManager.getById(query.getId());
            PosCashProduct updatePosCashProduct = new PosCashProduct();
            // 打折优惠后价格
            BigDecimal amountAfter = posCashProduct.getAmount().multiply(query.getValue().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.UP);
            // 更改金额记录
            JSONObject productDiscountRemakrs = convert2JSON(posCashProduct.getRemarks());
            productDiscountRemakrs.put("productDiscountRemakrs", StringUtils.isEmpty(productDiscountRemakrs.getString("productDiscountRemakrs")) ? "商品打折优惠，打折优惠前:" + posCashProduct.getAmount() + ",打折优惠后:" + amountAfter : productDiscountRemakrs.getString("productDiscountRemakrs") + "商品打折优惠，打折优惠前:" + posCashProduct.getAmount() + ",打折优惠后:" + amountAfter);
            productDiscountRemakrs.put("remarks", query.getRemarks());
            updatePosCashProduct.setRemarks(productDiscountRemakrs.toJSONString());
            updatePosCashProduct.setType(query.getType());
            // 更改原金额
            updatePosCashProduct.setAmount(amountAfter);
            updatePosCashProduct.setDiscount(query.getValue());
            suc = posCashProductManager.update(updatePosCashProduct, Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getId, query.getId()));
        } else if ("2".equals(query.getType())) {
            PosCashProduct posCashProduct = posCashProductManager.getById(query.getId());

            PosCashProduct updatePosCashProduct = new PosCashProduct();
            // 减免优惠后价格
            BigDecimal amountAfter = posCashProduct.getAmount().subtract(query.getValue()).setScale(2, RoundingMode.UP);

            // 更改金额记录
            JSONObject productSaleRemakrs = convert2JSON(posCashProduct.getRemarks());
            productSaleRemakrs.put("productDerateRemakrs", StringUtils.isEmpty(productSaleRemakrs.getString("productDerateRemakrs")) ? "商品减免优惠，减免优惠前:" + posCashProduct.getAmount() + ",减免优惠后:" + amountAfter : productSaleRemakrs.getString("productDerateRemakrs") + ";商品减免优惠，减免优惠前:" + posCashProduct.getAmount() + ",减免优惠后:" + amountAfter);
            productSaleRemakrs.put("remarks", query.getRemarks());
            updatePosCashProduct.setType(query.getType());
            updatePosCashProduct.setDiscount(query.getValue());
            // 更改原金额
            updatePosCashProduct.setAmount(amountAfter);
            updatePosCashProduct.setRemarks(productSaleRemakrs.toJSONString());
            suc = posCashProductManager.update(updatePosCashProduct, Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getId, query.getId()));
        }
        ArgumentAssert.isFalse(!suc, "单品优惠失败！");
        return suc;
    }

    @Override
    public Boolean disSale(Long id) {
        PosCashProduct posCashProduct = posCashProductManager.getById(id);
        ArgumentAssert.notNull(posCashProduct, "单品优惠不存在");
        // 打折优惠后价格
        BigDecimal amountAfter = posCashProduct.getAmount();
        // 根据优惠类型,还原价格
        if ("1".equals(posCashProduct.getType())) {
            amountAfter = posCashProduct.getAmount().divide(posCashProduct.getDiscount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.UP);

            JSONObject productDiscountRemakrs = convert2JSON(posCashProduct.getRemarks());
            productDiscountRemakrs.put("productDiscountRemakrs", StringUtils.isEmpty(productDiscountRemakrs.getString("productDiscountRemakrs")) ? "取消打折优惠" : productDiscountRemakrs.getString("productDiscountRemakrs") + ";取消打折优惠");
            // 单品优惠取消,字段置空
            productDiscountRemakrs.remove("remarks");
            posCashProduct.setRemarks(productDiscountRemakrs.toJSONString());

        } else if ("2".equals(posCashProduct.getType())) {
            amountAfter = posCashProduct.getAmount().add(posCashProduct.getDiscount()).setScale(2, RoundingMode.UP);

            JSONObject productDiscountRemakrs = convert2JSON(posCashProduct.getRemarks());
            productDiscountRemakrs.put("productDerateRemakrs", StringUtils.isEmpty(productDiscountRemakrs.getString("productDerateRemakrs")) ? "取消减免优惠" : productDiscountRemakrs.getString("productDerateRemakrs") + ";取消减免优惠");
            // 单品优惠取消,字段置空
            productDiscountRemakrs.remove("remarks");
            posCashProduct.setRemarks(productDiscountRemakrs.toJSONString());
        }
        // 还原原金额，是否使用了卡权益
        // 查询库中是否有使用中的卡
        PosCashEquity posCashEquity = cashEquityManager.getOne(Wraps.<PosCashEquity>lbQ().eq(PosCashEquity::getCashId, posCashProduct.getCashId()).eq(PosCashEquity::getType, 1));
        if (posCashEquity == null) {
            posCashProduct.setDiscount(BigDecimal.ZERO);
            posCashProduct.setType("");
            posCashProduct.setAmount(amountAfter);
        }
        Boolean suc = posCashProductManager.saveOrUpdate(posCashProduct);
        ArgumentAssert.isFalse(!suc, "取消优惠失败");
        return suc;
    }


    @Override
    public Boolean completeOrder(PosCashOrderQuery query) {
        // ID
        Long id = query.getPosCashId();
        // 备注
        String remark = query.getRemarks();
        // 打折/减免值
        BigDecimal value = query.getValue();
        // 优惠类型: 1-打折 2-减免 3-赠送
        String type = query.getType();
        // 计算价格
        PosCash posCash = posCashManager.getById(id);
        ArgumentAssert.notNull(posCash, "订单不存在！");
        posCash.setDiscountType(type);
        posCash.setDiscount(value);
        BigDecimal middle = posCash.getAmount();
//        BigDecimal middle = null != posCash.getUnpaid() ? posCash.getUnpaid() : posCash.getPayment();


        Boolean suc = false;

        if ("1".equals(type)) {
            ArgumentAssert.isFalse(ObjectUtil.isNull(value), "请输入打折值");
            // 打折后金额
            BigDecimal amountAfter = middle.multiply(value.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP)).setScale(2, RoundingMode.UP);
            // 优惠金额
            BigDecimal yhAmount = posCash.getAmount().subtract(amountAfter);
            BigDecimal discountAmount = yhAmount.add(null == posCash.getRoundAmount() ? new BigDecimal(0) : posCash.getRoundAmount());

            posCash.setDiscountAmount(discountAmount);

            // 更给改金额记录
            JSONObject remakrsObj = convert2JSON(posCash.getRemarks());
            remakrsObj.put("discountRemakrs", StringUtils.isEmpty(remakrsObj.getString("discountRemakrs")) ? "手动打折优惠，打折优惠前:" + posCash.getAmount() + ",打折优惠后:" + amountAfter : remakrsObj.getString("discountRemakrs") + ";手动打折优惠，打折优惠前:" + posCash.getAmount() + ",打折优惠后:" + amountAfter);
            remakrsObj.put("remarks", remark);
            posCash.setPayment(posCash.getAmount().subtract(discountAmount));
            posCash.setUnpaid(posCash.getAmount().subtract(discountAmount));
            posCash.setRemarks(remakrsObj.toJSONString());
            suc = posCashManager.saveOrUpdate(posCash);
        } else if ("2".equals(type)) {
            ArgumentAssert.isFalse(ObjectUtil.isNull(value), "请输入减免值");
            BigDecimal amountAfter = middle.subtract(value).setScale(2, RoundingMode.UP);
//            if (null != posCash.getRoundAmount()) {
//                amountAfter = amountAfter.subtract(posCash.getRoundAmount());
//            }
            posCash.setDiscountAmount(value.add(null == posCash.getRoundAmount() ? new BigDecimal(0) : posCash.getRoundAmount()));
            BigDecimal aa = posCash.getAmount().subtract(value.add(null == posCash.getRoundAmount() ? new BigDecimal(0) : posCash.getRoundAmount()));
            JSONObject remakrsObj = convert2JSON(posCash.getRemarks());
            remakrsObj.put("derateRemakrs", StringUtils.isEmpty(remakrsObj.getString("derateRemakrs")) ? "手动减免优惠，减免优惠前:" + posCash.getAmount() + ",减免优惠后:" + amountAfter : remakrsObj.getString("derateRemakrs") + ";手动减免优惠，减免优惠前:" + posCash.getAmount() + ",减免优惠后:" + amountAfter);
            remakrsObj.put("remarks", remark);
            posCash.setPayment(aa);
            posCash.setUnpaid(aa);
            posCash.setRemarks(remakrsObj.toJSONString());
            suc = posCashManager.saveOrUpdate(posCash);
        } else if ("3".equals(type)) {
            JSONObject remakrsObj = convert2JSON(posCash.getRemarks());
            remakrsObj.put("derateRemakrs", StringUtils.isEmpty(remakrsObj.getString("derateRemakrs")) ? "免单" : remakrsObj.getString("derateRemakrs") + ";免单");
            remakrsObj.put("remarks", remark);
            posCash.setPayment(BigDecimal.ZERO);
            posCash.setUnpaid(BigDecimal.ZERO);
            posCash.setRemarks(remakrsObj.toJSONString());
            // TODO 更改结算表台桌状态
            posCash.setBillState("6");
            suc = posCashManager.saveOrUpdate(posCash);
        }
        ArgumentAssert.isFalse(!suc, "操作失败");
        return suc;
    }

    @Override
    public Boolean disCompleteOrder(PosCashIdQuery query) {
        // ID
        Long id = query.getPosCashId();
        // 计算价格
        PosCash posCash = posCashManager.getById(id);
        BigDecimal amountBefore = BigDecimal.ZERO;
        JSONObject remakrsObj = convert2JSON(posCash.getRemarks());
        if ("1".equals(posCash.getDiscountType())) {
            // 打折前金额
//            amountBefore = posCash.getPayment().divide(posCash.getDiscount().divide(new BigDecimal(100), 2, RoundingMode.UP), 2, RoundingMode.UP).setScale(2, RoundingMode.UP);
            amountBefore = posCash.getPayment().add(posCash.getDiscountAmount()).setScale(2, RoundingMode.UP);
//            if (null != posCash.getRoundAmount()) {
//                amountBefore = amountBefore.subtract(posCash.getRoundAmount());
//            }
            // 更给改金额记录
            remakrsObj.put("discountRemakrs", StringUtils.isEmpty(remakrsObj.getString("discountRemakrs")) ? "取消整单打折操作" : remakrsObj.getString("discountRemakrs") + ";取消整单打折操作");

        } else if ("2".equals(posCash.getDiscountType())) {
            // 打折前金额
            amountBefore = posCash.getPayment().add(posCash.getDiscountAmount()).setScale(2, RoundingMode.UP);
//            if (null != posCash.getRoundAmount()) {
//                amountBefore = amountBefore.subtract(posCash.getRoundAmount());
//            }
            // 更给改金额记录
            remakrsObj.put("derateRemakrs", StringUtils.isEmpty(remakrsObj.getString("derateRemakrs")) ? "取消整单减免操作" : remakrsObj.getString("derateRemakrs") + ";取消整单减免操作");
        }
        posCash.setRemarks(remakrsObj.toJSONString());
//        posCash.setAmount(amountBefore);
        posCash.setPayment(amountBefore);
        posCash.setDiscountType("");
        posCash.setDiscount(BigDecimal.ZERO);
        posCash.setDiscountAmount(BigDecimal.ZERO);
        posCash.setUnpaid(posCash.getPayment().subtract(posCash.getDiscount()));
        Boolean suc = posCashManager.saveOrUpdate(posCash);
        ArgumentAssert.isFalse(!suc, "操作失败");
        return suc;
    }

    private JSONObject convert2JSON(String text) {
        try {
            return StringUtils.isNotEmpty(text) ? JSONObject.parseObject(text) : new JSONObject();
        } catch (Exception e) {
            log.error("备注数据格式错误,error:{}", e);
            return new JSONObject();
        }
    }

    private PosCash getShopPosCash(Long posCashId) {
        PosCash posCash = null;
        // 1. 保存pos_cash
        if (ObjectUtil.isNotNull(posCashId)) {
            posCash = posCashManager.getById(posCashId);
        }
        if (ObjectUtil.isNull(posCash)) {
            posCash = posCashManager.getOne(Wraps.<PosCash>lbQ().eq(PosCash::getOrgId, ContextUtil.getCurrentCompanyId())
                    .eq(PosCash::getType, PosCashTypeEnum.SHOPPING.getCode())
                    .eq(PosCash::getBillType, PosCashBillTypeEnum.REGULAR_SINGLE.getCode())
                    .eq(PosCash::getBillState, PosCashBillStateEnum.NO_SETTLED.getCode()));
        }
        if (ObjectUtil.isNull(posCash)) {
            posCash = new PosCash();
            // 类型
            posCash.setType(PosCashTypeEnum.SHOPPING.getCode());
            // 单据code
            posCash.setCode(tableService.randomOrderCode());
            // 单据日期
            posCash.setBillDate(LocalDate.now());
            // 单据状态
            posCash.setBillState(PosCashBillStateEnum.NO_SETTLED.getCode());
            posCash.setBillType(PosCashBillTypeEnum.REGULAR_SINGLE.getCode());
            // 门店id
            posCash.setOrgId(getCurrentCompanyId());
            posCash.setCreatedOrgId(getCurrentCompanyId());
            // 员工id
            posCash.setEmployeeId(null);
            // 支付名
            posCash.setPayName("已在购物，待下单");
            posCash.setCreatedTime(LocalDateTime.now());
            posCash.setUpdatedTime(null);
            posCash.setOrderSource(OrderSourceEnum.POS.getCode());
            posCash.setIsTurn(false);
            posCash.setCreatedBy(ContextUtil.getUserId());
            posCash.setRefundAmount(BigDecimal.ZERO);
            posCash.setSn(ContextUtil.getSn());
            boolean suc = posCashManager.save(posCash);
            ArgumentAssert.isFalse(!suc, "操作失败！");
        }
        return posCash;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean reset(PosCashIdQuery query) {
        PosCash posCash = posCashManager.getOne(Wraps.<PosCash>lbQ().eq(PosCash::getOrgId, getCurrentCompanyId())
                .eq(PosCash::getType, PosCashTypeEnum.SHOPPING.getCode())
                .eq(PosCash::getBillType, PosCashBillTypeEnum.REGULAR_SINGLE.getCode())
                .eq(PosCash::getBillState, PosCashBillStateEnum.NO_SETTLED.getCode()));
        ArgumentAssert.notNull(posCash, "操作失败！");
        //订单删除
        posCashManager.deletePosCash(posCash.getId());
        //对应商品删除
        posCashProductManager.remove(Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getCashId, query.getPosCashId()));

        return true;
    }

    @Override
    public List<BaseEmployeeResultVO> empList() {
        return employeeManager.list(Wraps.<BaseEmployee>lbQ().inSql(BaseEmployee::getId, "select distinct employee_id from base_employee_org_rel where  delete_flag = 0 and org_id =" + ContextUtil.getCurrentCompanyId()))
                .stream().map(v -> BeanUtil.copyProperties(v, BaseEmployeeResultVO.class)).collect(Collectors.toList());
    }

    @Override
    public void createdOnlineShopping(PosCash posCash) {
// 类型
        posCash.setType(PosCashTypeEnum.SHOPPING.getCode());
        // 单据code
        posCash.setCode(tableService.randomOrderCode());
        // 单据日期
        posCash.setBillDate(LocalDate.now());
        // 单据状态
        posCash.setBillState(Optional.ofNullable(posCash.getBillState()).orElse(PosCashBillStateEnum.NO_PAY.getCode()));
        posCash.setBillType(StrUtil.isBlank(posCash.getBillType()) ? PosCashBillTypeEnum.REGULAR_SINGLE.getCode()
                : posCash.getBillType());
        // 门店id
        posCash.setOrgId(getCurrentCompanyId());
        posCash.setCreatedOrgId(getCurrentCompanyId());
        // 员工id
        posCash.setEmployeeId(null);
        posCash.setCreatedEmp(ContextUtil.getEmployeeId());
        // 支付名
        posCash.setPayName("在线购物");
        posCash.setUpdatedTime(null);
        //信息
        posCash.setAmount(Optional.ofNullable(posCash.getAmount()).orElse(BigDecimal.ZERO));
        posCash.setPayment(Optional.ofNullable(posCash.getPayment()).orElse(BigDecimal.ZERO));
        posCash.setPaid(Optional.ofNullable(posCash.getPaid()).orElse(BigDecimal.ZERO));
        posCash.setUnpaid(Optional.ofNullable(posCash.getUnpaid()).orElse(BigDecimal.ZERO));
        posCash.setProductAmount(Optional.ofNullable(posCash.getProductAmount()).orElse(BigDecimal.ZERO));
        posCash.setServiceAmount(Optional.ofNullable(posCash.getServiceAmount()).orElse(BigDecimal.ZERO));
        posCash.setTableAmount(Optional.ofNullable(posCash.getTableAmount()).orElse(BigDecimal.ZERO));
        posCash.setThailAmount(Optional.ofNullable(posCash.getThailAmount()).orElse(BigDecimal.ZERO));
        posCash.setPowerAmount(Optional.ofNullable(posCash.getPowerAmount()).orElse(BigDecimal.ZERO));
        posCash.setBuyCardAmount(Optional.ofNullable(posCash.getBuyCardAmount()).orElse(BigDecimal.ZERO));
        posCash.setChangeAmount(Optional.ofNullable(posCash.getChangeAmount()).orElse(BigDecimal.ZERO));
        posCash.setFreeChangeAmount(Optional.ofNullable(posCash.getFreeChangeAmount()).orElse(BigDecimal.ZERO));
        posCash.setGiftAmount(BigDecimal.ZERO);
        posCash.setDiscountAmount(BigDecimal.ZERO);
        //订单来源
        posCash.setOrderSource(StrUtil.isNotBlank(posCash.getOrderSource()) ? posCash.getOrderSource()
                : OrderSourceEnum.POS.getCode());
        posCash.setCreatedTime(LocalDateTime.now());
        posCash.setIsTurn(false);
        posCash.setCreatedBy(ContextUtil.getUserId());
        posCash.setRefundAmount(BigDecimal.ZERO);
        posCash.setIsAutoRound(false);
        posCash.setIsFree(false);
        posCash.setIsTemporaryLights(false);
        posCash.setIsMemberDiscount(false);
        posCash.setDiscount(BigDecimal.ZERO);
        posCash.setAutoRoundAmount(BigDecimal.ZERO);
        posCash.setSn(ContextUtil.getSn());
        boolean suc = posCashManager.save(posCash);
        ArgumentAssert.isFalse(!suc, "操作失败！");
        //新增操作日志
        bizLogService.save(BaseBizLogSaveVO.builder()
                .orgId(getCurrentCompanyId()).description("创建购物订单")
                .bizModule(BizLogModuleEnum.POS_CASH.getCode()).type(BizLogTypeEnum.CREATED.getCode())
                .employeeId(ContextUtil.getEmployeeId()).createdOrgId(getCurrentCompanyId())
                .opearteTime(LocalDateTime.now()).sourceId(posCash.getId()).remarks("创建充值")
                .build());
    }
}
