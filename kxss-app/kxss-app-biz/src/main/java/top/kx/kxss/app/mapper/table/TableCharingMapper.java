package top.kx.kxss.app.mapper.table;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.kxss.base.entity.tableCharging.setting.BaseTableChargingSetting;

/**
 * <p>
 * Mapper 接口
 * 台桌计费表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-03-10 23:00:01
 * @create [2023-03-10 23:00:01] [zhou]
 */
@Repository
public interface TableCharingMapper {

    /**
     * 获取当前时间下的台费数据
     *
     * @param tableId
     */
    BaseTableChargingSetting getNowChargingSet(@Param(value = "tableId") Long tableId);

    /**
     * 获取当前时间下的台费数据
     *
     * @param tableId
     */
    BaseTableChargingSetting getOrgNowChargingSet(@Param(value = "tableId") Long tableId, @Param(value = "orgId") Long orgId);


}
