package top.kx.kxss.app.mqtt.handler;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHandler;
import org.springframework.messaging.MessagingException;
import org.springframework.stereotype.Component;

/**
 * 信息处理工具(消费端消息处理)
 * <AUTHOR>
 */
@AllArgsConstructor
@Component
@Slf4j
public class MQTTMessageHandler implements MessageHandler {


    /**
     * @param message
     * @Description 消息处理(在这实现接收消息处理业务, 资源注入不进来, 可以手动注入)
     */
    @Override
    public void handleMessage(Message<?> message) throws MessagingException {
        log.info("收到的完整消息为--->{}", message);
        log.info("----------------------");
        log.info("message:" + message.getPayload());
        log.info("Id:" + message.getHeaders().getId());
        log.info("receivedQos:" + message.getHeaders().get(MqttHeaders.RECEIVED_QOS));
        String topic = (String) message.getHeaders().get(MqttHeaders.RECEIVED_TOPIC);
        log.info("topic:" + topic);
        log.info("----------------------");
    }
}
