package top.kx.kxss.app.manager.cash.payresult.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.app.entity.cash.payresult.PosCashPayResult;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.app.manager.cash.payresult.PosCashPayResultManager;
import top.kx.kxss.app.mapper.cash.payresult.PosCashPayResultMapper;

/**
 * <p>
 * 通用业务实现类
 * 结算单支付结果
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-19 17:25:46
 * @create [2023-05-19 17:25:46] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashPayResultManagerImpl extends SuperManagerImpl<PosCashPayResultMapper, PosCashPayResult> implements PosCashPayResultManager {

}


