package top.kx.kxss.app.event.listen;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import top.kx.basic.base.R;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.IotApi;
import top.kx.kxss.app.event.LightEvent;
import top.kx.kxss.app.event.model.LightDTO;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.service.common.BaseParameterService;
import top.kx.kxss.iot.vo.ControlVO;

/**
 * 登录事件监听，用于记录登录日志
 *
 * <AUTHOR>
 * @date 2020年03月18日17:39:59
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class LightListener {
    private final IotApi iotApi;
    private final BaseParameterService baseParameterService;

    @Async
    @EventListener({LightEvent.class})
    public void operate(LightEvent event) {
        LightDTO loginStatus = (LightDTO) event.getSource();
        try {
            ContextUtil.setTenantId(loginStatus.getTenantId());
            ContextUtil.setTenantBasePoolName(loginStatus.getTenantId());
            ContextUtil.setCurrentCompanyId(loginStatus.getOrgId());
            ContextUtil.setEmployeeId(loginStatus.getEmployeeId());

            // 判断是否需要控灯
            Boolean controlLights = baseParameterService.manualControlLights();
            if (controlLights) {
                log.warn("手动控制灯控，无需开关灯");
                return;
            }

            BaseTableInfo tableInfo = loginStatus.getTableInfo();
            String[] lineInfo = tableInfo.getLineNum().split(",");
            String deviceId = lineInfo[0];
            int outlet = 0;
            if (lineInfo.length > 1) {
                outlet = Integer.parseInt(lineInfo[1]);
            }
            if (loginStatus.getStatus() == 1) {
                R<String> control =
                        iotApi.control(ControlVO.builder().mac(tableInfo.getLightName())
                                .deviceId(deviceId).outlet(outlet).switchState("on").qos(2).build());
                Boolean b = control.getIsSuccess();
                if (!b) {
                    log.error("无线开灯异常：{}，租户,{}", control.getMsg(), ContextUtil.getTenantId());
                }
            } else {
                R<String> control =
                        iotApi.control(ControlVO.builder().mac(tableInfo.getLightName()).deviceId(deviceId).outlet(outlet).switchState(
                                "off").qos(2).build());
                Boolean b = control.getIsSuccess();
                if (!b) {
                    log.error("无线关灯异常：{}，租户,{}", control.getMsg(), ContextUtil.getTenantId());
                }
            }
        } finally {
            ContextUtil.remove();
        }
    }

}
