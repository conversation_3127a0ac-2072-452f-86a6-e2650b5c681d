package top.kx.kxss.app.mqtt.handler;

import org.springframework.integration.annotation.MessagingGateway;
import org.springframework.integration.mqtt.support.MqttHeaders;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.stereotype.Component;

/**
 * 网关接口MqttGateway
 *
 * <AUTHOR>
 */
@MessagingGateway(defaultRequestChannel = "mqttOut")
@Component
public interface MQTTGateway {

    /**
     * @param message 消息
     *                定义重载方法，用于消息发送
     */
    void sendToMqtt(String message);

    /**
     * @param topic   主题
     * @param message 消息
     *                指定topic进行消息发送
     */
    void sendToMqtt(@Header(MqttHeaders.TOPIC) String topic, String message);

    /**
     * @param topic   主题
     * @param qos     qos
     * @param message 消息
     *                指定topic和qos进行消息发送
     */
    void sendToMqtt(@Header(MqttHeaders.TOPIC) String topic, @Header(MqttHeaders.QOS) int qos, String message);

    /**
     * @param topic   主题
     * @param qos     qos
     * @param message 消息(字节数字类型)
     *                指定topic和qos进行消息发送
     */
    void sendToMqtt(@Header(MqttHeaders.TOPIC) String topic, @Header(MqttHeaders.QOS) int qos, byte[] message);
}
