package top.kx.kxss.app.service.cash.equity.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.equity.PosCashEquity;
import top.kx.kxss.app.manager.cash.equity.PosCashEquityManager;
import top.kx.kxss.app.service.cash.equity.PosCashEquityService;
import top.kx.kxss.app.vo.query.cash.equity.PosCashEquityPageQuery;
import top.kx.kxss.app.vo.result.cash.equity.PosCashEquityResultVO;
import top.kx.kxss.app.vo.save.cash.equity.PosCashEquitySaveVO;
import top.kx.kxss.app.vo.update.cash.equity.PosCashEquityUpdateVO;
import top.kx.kxss.base.vo.NameValueVO;
import top.kx.kxss.common.constant.DsConstant;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 业务实现类
 * 结算单消费权益
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-06 18:27:08
 * @create [2023-05-06 18:27:08] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class PosCashEquityServiceImpl extends SuperServiceImpl<PosCashEquityManager, Long, PosCashEquity, PosCashEquitySaveVO,
        PosCashEquityUpdateVO, PosCashEquityPageQuery, PosCashEquityResultVO> implements PosCashEquityService {

    @Override
    public PosCashEquity getOne(LbQueryWrap<PosCashEquity> eq) {
        return superManager.getOne(eq);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(PosCashEquity build) {
        if (StringUtils.isBlank(build.getSn())) {
            build.setSn(ContextUtil.getSn());
        }
        return superManager.save(build);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(LbUpdateWrap<PosCashEquity> eq) {
        return superManager.update(eq);
    }

    @Override
    public List<NameValueVO> consumeTimes(QueryWrapper<PosCash> wrapper) {
        return superManager.consumeTimes(wrapper);
    }

    @Override
    public long count(LbQueryWrap<PosCashEquity> eq) {
        return superManager.count(eq);
    }

    @Override
    public List<PosCashEquityResultVO> queryList(List<Long> bizIdList, String type) {
        List<PosCashEquity> equityList = superManager.list(Wraps.<PosCashEquity>lbQ().eq(SuperEntity::getDeleteFlag, 0)
                .eq(PosCashEquity::getType, type).in(PosCashEquity::getBizId, bizIdList));
        return BeanPlusUtil.toBeanList(equityList, PosCashEquityResultVO.class);
    }
}


