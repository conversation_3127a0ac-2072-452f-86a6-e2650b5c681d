package top.kx.kxss.app.service.cash.equity;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.equity.PosCashEquity;
import top.kx.kxss.app.vo.query.cash.equity.PosCashEquityPageQuery;
import top.kx.kxss.app.vo.result.cash.equity.PosCashEquityResultVO;
import top.kx.kxss.app.vo.save.cash.equity.PosCashEquitySaveVO;
import top.kx.kxss.app.vo.update.cash.equity.PosCashEquityUpdateVO;
import top.kx.kxss.base.vo.NameValueVO;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 结算单消费权益
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-06 18:27:08
 * @create [2023-05-06 18:27:08] [dou] [代码生成器生成]
 */
public interface PosCashEquityService extends SuperService<Long, PosCashEquity, PosCashEquitySaveVO,
    PosCashEquityUpdateVO, PosCashEquityPageQuery, PosCashEquityResultVO> {

    PosCashEquity getOne(LbQueryWrap<PosCashEquity> eq);

    boolean save(PosCashEquity build);

    boolean update(LbUpdateWrap<PosCashEquity> eq);

    List<NameValueVO> consumeTimes(QueryWrapper<PosCash> wrapper);

    long count(LbQueryWrap<PosCashEquity> eq);

    List<PosCashEquityResultVO> queryList(List<Long> bizIdList, String type);

}


