package top.kx.kxss.app.manager.cash.table.impl;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.manager.cash.table.PosCashTableManager;
import top.kx.kxss.app.mapper.cash.table.PosCashTableMapper;
import top.kx.kxss.app.statemachine.PosCashStateManager;
import top.kx.kxss.app.vo.result.ProfitResultVO;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.manager.table.BaseTableInfoManager;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.model.enumeration.app.CashTableStatusEnum;
import top.kx.kxss.model.enumeration.base.TableStatus;

import java.util.List;

/**
 * <p>
 * 通用业务实现类
 * 台桌计时费用
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:44:58
 * @create [2023-04-19 14:44:58] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashTableManagerImpl extends SuperManagerImpl<PosCashTableMapper, PosCashTable> implements PosCashTableManager {

    @Autowired
    private BaseTableInfoManager tableInfoManager;
    @Autowired
    private PosCashStateManager cashStateManager;

    @Override
    public Boolean checkTableStop(PosCash posCash) {
        long count = count(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, posCash.getId()).eq(PosCashTable::getStatus, CashTableStatusEnum.TIMING.getCode()));
        if (count > 0 || ObjectUtil.isNull(posCash.getTableId())) {
            return false;
        }
        BaseTableInfo tableInfo = tableInfoManager.getById(posCash.getTableId());
        if (ObjectUtil.isNull(tableInfo)) {
            return false;
        }
        tableInfo.setTableStatus(TableStatus.STOP.getCode());
        //产品经理强制要求停止计时 释放灯
        cashStateManager.handleBooleanEvent(posCash.getId(), PosCashConstant.Event.CLOSE_LIGHT, posCash, tableInfo);
        return true;
    }

    @Override
    public ProfitResultVO findProfit(List<Long> posCashIdList, Boolean thailIsNull) {
        return baseMapper.findProfit(posCashIdList, thailIsNull);
    }

    @Override
    public Boolean checkTableUsed(PosCash posCash) {
        long count = count(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, posCash.getId()).eq(PosCashTable::getStatus, CashTableStatusEnum.TIMING.getCode()));
        if (count <= 0 || ObjectUtil.isNull(posCash.getTableId())) {
            return false;
        }
        BaseTableInfo tableInfo = tableInfoManager.getById(posCash.getTableId());
        if (ObjectUtil.isNull(tableInfo)) {
            return false;
        }
        tableInfo.setTableStatus(TableStatus.USING.getCode());
        //产品经理强制要求继续计时 开灯
        cashStateManager.handleBooleanEvent(posCash.getId(), PosCashConstant.Event.OPEN_LIGHT, posCash, tableInfo);
        return true;
    }
}


