package top.kx.kxss.app.granter;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.wechat.pay.java.service.payments.model.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import top.kx.basic.cache.repository.CacheOps;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.cash.PosCashPaymentApi;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.service.cash.PosCashServiceService;
import top.kx.kxss.app.service.cash.payment.PosCashPaymentService;
import top.kx.kxss.app.service.cash.table.PosCashTableService;
import top.kx.kxss.app.service.pay.WxPayService;
import top.kx.kxss.app.statemachine.PosCashStateManager;
import top.kx.kxss.app.vo.CalcAmountParamVO;
import top.kx.kxss.app.vo.pay.NotificationVO;
import top.kx.kxss.app.vo.pay.PaySuccessVO;
import top.kx.kxss.app.vo.pay.RefundNotificationVO;
import top.kx.kxss.app.vo.pay.WxNotifyVO;
import top.kx.kxss.app.vo.update.cash.PosCashUpdateVO;
import top.kx.kxss.base.service.transaction.BaseWxTransactionService;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashTypeEnum;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 计算开台金额Granter
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractCalcAmountGranter implements CalcAmountGranter {

    @Autowired
    private PosCashServiceService posCashService;
    @Autowired
    private PosCashTableService posCashTableService;
    @Autowired
    private PosCashStateManager cashStateManager;
    @Autowired
    private WxPayService wxPayService;
    @Autowired
    private BaseWxTransactionService baseWxTransactionService;
    @Autowired
    private PosCashPaymentApi posPaymentApi;
    @Autowired
    private PosCashPaymentService posCashPaymentService;
    @Autowired
    private CacheOps cacheOps;


    @Override
    public BigDecimal total(PosCash posCash, CalcAmountParamVO paramVO) {
        return tableAmount(posCash, paramVO);
    }

    /**
     * 台桌金额
     */
    @Override
    public BigDecimal tableAmount(PosCash posCash, CalcAmountParamVO paramVO) {
        List<PosCashTable> cashTableList = posCashTableService.list(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, posCash.getId()));
        if (CollUtil.isEmpty(cashTableList)) {
            return BigDecimal.ZERO;
        }
        //计算总价格
        double sum = cashTableList.stream().mapToDouble(v -> v.getAmount().doubleValue()).sum();
        return BigDecimal.valueOf(sum).setScale(2, RoundingMode.UP);
    }

    @Override
    public PosCash paymentSuccess(PaySuccessVO successVO) {
        try {
            //验证微信订单是否存在
            Transaction transaction = wxPayService.queryOrderByOutTradeNo(successVO.getPosCashId());
            ArgumentAssert.notNull(transaction, "支付订单不存在或支付失败！");
            Map map = JSON.parseObject(transaction.getAttach(), Map.class);
            ContextUtil.setTenantBasePoolName(map.get("tenantId").toString());
            ContextUtil.setCurrentCompanyId(map.get("currentCompanyId").toString());
            ContextUtil.setClientId(map.get("clientId").toString());
            ContextUtil.setTenantId(map.get("tenantId").toString());
            //操作订单
            PosCash posCash = posCashService.getById(successVO.getPosCashId());
            ArgumentAssert.notNull(posCash, "订单不存在！");
            ArgumentAssert.notNull(posCash.getAmount().compareTo(posCash.getUnpaid()) != 0, "支付金额与订单金额不符！");
            //订单结束
            PosCashUpdateVO posCashUpdateVO = BeanUtil.copyProperties(posCash, PosCashUpdateVO.class);
//        posCashUpdateVO.setBillState(PosCashBillStateEnum.COMPLETE.getCode());
            posCashUpdateVO.setPaid(changeF2Y(transaction.getAmount().getTotal()));
            posCashUpdateVO.setUnpaid(posCash.getAmount().subtract(changeF2Y(transaction.getAmount().getTotal())));
            posCash.setUpdatedTime(LocalDateTime.now());
            posCash = posCashService.updateById(posCashUpdateVO);
            return posCash;
        } finally {
            ContextUtil.remove();
        }
    }

    /**
     * 元转分，确保price保留两位有效数字
     */
    @Override
    public int changeY2F(BigDecimal amount) {
        BigDecimal bigDecimal = amount.setScale(2, RoundingMode.HALF_UP);
        return bigDecimal.multiply(new BigDecimal(100)).intValue();
    }

    /**
     * 分转元，转换为bigDecimal在toString
     *
     * @param price
     * @return
     */
    @Override
    public BigDecimal changeF2Y(int price) {
        return BigDecimal.valueOf(price).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
    }

    @Override
    public Map<String, Object> wxNotify(WxNotifyVO notifyVO) {
        try {
            Map<String, Object> map = MapUtil.newHashMap();
            map.put("code", "FAIL");
            if (ObjectUtil.isNull(notifyVO)) {
                return map;
            }
            //支付失败
            if (!ObjectUtil.equal(notifyVO.getEventType(), "TRANSACTION.SUCCESS") && !ObjectUtil.equal(notifyVO.getResourceType(), "encrypt-resource")) {
                return map;
            }
            String s = wxPayService.decryptToString(notifyVO.getResource().getAssociatedData(), notifyVO.getResource().getNonce(), notifyVO.getResource().getCiphertext());
            log.info("回调信息：{}", s);
            //支付成功修改订单状态
            NotificationVO notificationVO = JSON.parseObject(s, NotificationVO.class);
            Map attachMap = JSON.parseObject(notificationVO.getAttach(), Map.class);
            ContextUtil.setTenantBasePoolName(attachMap.get("tenantId").toString());
            ContextUtil.setCurrentCompanyId(attachMap.get("currentCompanyId").toString());
            ContextUtil.setClientId(attachMap.get("clientId").toString());
            ContextUtil.setTenantId(attachMap.get("tenantId").toString());
            PosCash posCash = posCashService.getById(Long.parseLong(notificationVO.getOutTradeNo()));
            if (ObjectUtil.isNull(posCash)) {
                return map;
            }
            map.put("code", "SUCCESS");
            if (ObjectUtil.equal(posCash.getBillState(), PosCashBillStateEnum.COMPLETE.getCode())) {
                return map;
            }
            if (ObjectUtil.equal(posCash.getType(), PosCashTypeEnum.START_TABLE.getCode())) {
                cashStateManager.handleEvent(Long.parseLong(notificationVO.getOutTradeNo()), PosCashConstant.Event.PAYMENT_SUCCESS, posCash);
            } else if (ObjectUtil.equal(posCash.getType(), PosCashTypeEnum.RECHARGE.getCode())) {
                cashStateManager.handleEvent(Long.parseLong(notificationVO.getOutTradeNo()), PosCashConstant.Event.RECHARGE_SUCCESS, posCash);
            }
            return map;
        } finally {
            ContextUtil.remove();
        }
    }

    @Override
    public Map<String, Object> wxRefundNotify(WxNotifyVO notifyVO) {
        Map<String, Object> map = MapUtil.newHashMap();
        map.put("code", "FAIL");
        if (ObjectUtil.isNull(notifyVO)) {
            return map;
        }
        //支付失败
        if (!ObjectUtil.equal(notifyVO.getEventType(), "REFUND.SUCCESS") && !ObjectUtil.equal(notifyVO.getResourceType(), "encrypt-resource")) {
            return map;
        }
        String s = wxPayService.componentDecryptToString(notifyVO.getResource().getAssociatedData(), notifyVO.getResource().getNonce(), notifyVO.getResource().getCiphertext());
        log.info("退款回调信息：{}", s);
        try {
            //支付成功修改订单状态
            RefundNotificationVO notificationVO = JSON.parseObject(s, RefundNotificationVO.class);
            log.info("退款信息：{}", JSON.toJSONString(notificationVO));
            map.put("code", "SUCCESS");
        } catch (NumberFormatException ignored) {
            map.put("code", "SUCCESS");
        }
        return map;
    }
}
