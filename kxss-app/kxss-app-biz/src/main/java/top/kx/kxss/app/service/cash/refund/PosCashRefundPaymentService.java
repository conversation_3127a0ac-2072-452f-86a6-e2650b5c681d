package top.kx.kxss.app.service.cash.refund;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.app.entity.cash.refund.PosCashRefundPayment;
import top.kx.kxss.app.vo.save.cash.refund.PosCashRefundPaymentSaveVO;
import top.kx.kxss.app.vo.update.cash.refund.PosCashRefundPaymentUpdateVO;
import top.kx.kxss.app.vo.result.cash.refund.PosCashRefundPaymentResultVO;
import top.kx.kxss.app.vo.query.cash.refund.PosCashRefundPaymentPageQuery;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 退款单
 * </p>
 *
 * <AUTHOR>
 * @date 2023-11-13 16:05:52
 * @create [2023-11-13 16:05:52] [dou] [代码生成器生成]
 */
public interface PosCashRefundPaymentService extends SuperService<Long, PosCashRefundPayment, PosCashRefundPaymentSaveVO,
    PosCashRefundPaymentUpdateVO, PosCashRefundPaymentPageQuery, PosCashRefundPaymentResultVO> {

    Boolean updateById(PosCashRefundPayment build);

    Boolean save(PosCashRefundPayment refundPayment);

    PosCashRefundPayment getByMchRefundNo(String mchRefundNo);

    boolean saveOrUpdateBatch(List<PosCashRefundPayment> refundPaymentList);

}


