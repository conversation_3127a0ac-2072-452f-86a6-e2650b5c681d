package top.kx.kxss.app.service.thail;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.app.entity.thail.PosCashThail;
import top.kx.kxss.app.vo.query.thail.PosCashThailAmountQuery;
import top.kx.kxss.app.vo.query.thail.PosCashThailPageQuery;
import top.kx.kxss.app.vo.result.ProfitResultVO;
import top.kx.kxss.app.vo.result.thail.PosCashThailAmountResultVO;
import top.kx.kxss.app.vo.result.thail.PosCashThailResultVO;
import top.kx.kxss.app.vo.save.thail.PosCashThailSaveVO;
import top.kx.kxss.app.vo.update.thail.PosCashThailUpdateVO;
import top.kx.kxss.wxapp.vo.query.statistics.PosCashDetailsQuery;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 订单套餐信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 19:11:17
 * @create [2023-09-14 19:11:17] [dou] [代码生成器生成]
 */
public interface PosCashThailService extends SuperService<Long, PosCashThail, PosCashThailSaveVO,
    PosCashThailUpdateVO, PosCashThailPageQuery, PosCashThailResultVO> {

    Boolean checkThail(List<Long> thailIds);

    boolean updateById(PosCashThail posCashThail);

    PosCashThail getOne(LbQueryWrap<PosCashThail> last);

    void save(PosCashThail posCashThail);

    boolean updateBatchById(List<PosCashThail> cashThailList);

    long count(LbQueryWrap<PosCashThail> eq);

    boolean removeBatchByIds(List<PosCashThail> cashThailList);

    ProfitResultVO findProfit(List<Long> posCashIdList);

    boolean update(LbUpdateWrap<PosCashThail> eq);

    /**
     * 查询团购套餐和店内套餐金额,
     */
    List<PosCashThailAmountResultVO> thailAmountList(PosCashThailAmountQuery query);

    PosCashThailAmountResultVO thailAmountSum(PosCashDetailsQuery model);

    List<PosCashThailAmountResultVO> thailAmountList(PosCashDetailsQuery query);
}


