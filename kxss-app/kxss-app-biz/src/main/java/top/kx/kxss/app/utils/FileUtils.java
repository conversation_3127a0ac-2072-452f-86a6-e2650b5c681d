package top.kx.kxss.app.utils;

import cn.hutool.core.io.IoUtil;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;

public class FileUtils {

    public static MultipartFile getMultipartFile(java.io.File file, String fileName) {
        FileItemFactory factory = new DiskFileItemFactory(16, null);
        FileItem item = factory.createItem("tempFileItem", "text/plain", true, fileName);
        FileInputStream fis = null;
        OutputStream os = null;
        int bytesRead;
        byte[] buffer = new byte[8192];
        try {
            fis = new FileInputStream(file);
            os = item.getOutputStream();
            while ((bytesRead = fis.read(buffer, 0, 8192)) != -1) {
                os.write(buffer, 0, bytesRead);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            IoUtil.close(fis);
            IoUtil.close(os);
        }
        return new CommonsMultipartFile(item);
    }
}
