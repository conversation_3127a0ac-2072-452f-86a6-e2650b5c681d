package top.kx.kxss.app.manager.cash.product.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.manager.cash.product.PosCashProductManager;
import top.kx.kxss.app.mapper.cash.product.PosCashProductMapper;
import top.kx.kxss.app.vo.result.ProfitResultVO;
import top.kx.kxss.app.vo.result.cash.product.PosCashProductResultVO;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 通用业务实现类
 * 结算单商品子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:40:33
 * @create [2023-04-19 14:40:33] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashProductManagerImpl extends SuperManagerImpl<PosCashProductMapper, PosCashProduct> implements PosCashProductManager {

    @Override
    public ProfitResultVO findProfit(List<Long> posCashIdList) {
        return baseMapper.findProfit(posCashIdList);
    }

    @Override
    public List<String> productRanking(Long memberId) {
        return baseMapper.productRanking(memberId);
    }

    @Override
    public List<PosCashProductResultVO> selectDiscount() {
        return baseMapper.selectDiscount();
    }

    @Override
    public List<PosCashProduct> queryList(LbQueryWrap<PosCashProduct> queryWrap) {
        return baseMapper.queryList(queryWrap);
    }
}


