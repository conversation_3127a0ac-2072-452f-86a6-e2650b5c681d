package top.kx.kxss.app.service.cash.payment;

import com.baomidou.mybatisplus.core.metadata.IPage;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.vo.member.AccountDeductResultVO;
import top.kx.kxss.app.vo.query.cash.payment.PosCashPaymentPageQuery;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO;
import top.kx.kxss.app.vo.save.cash.payment.PosCashPaymentSaveVO;
import top.kx.kxss.app.vo.update.cash.payment.PosCashPaymentUpdateVO;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.member.grade.MemberGrade;
import top.kx.kxss.wxapp.vo.query.payment.PaymentTypeQuery;
import top.kx.kxss.wxapp.vo.result.payment.PaymentTypeStatisticsResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.PaymentDetailsResultVO;

import java.math.BigDecimal;
import java.util.List;


/**
 * <p>
 * 业务接口
 * 商品结算单收款子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:38:34
 * @create [2023-04-19 14:38:34] [dou] [代码生成器生成]
 */
public interface PosCashPaymentService extends SuperService<Long, PosCashPayment, PosCashPaymentSaveVO,
        PosCashPaymentUpdateVO, PosCashPaymentPageQuery, PosCashPaymentResultVO> {

    void save(PosCashPayment posCashPayment);

    Boolean updateById(PosCashPayment posCashPayment);

    PosCashPayment getOne(LbQueryWrap<PosCashPayment> eq);

    long count(LbQueryWrap<PosCashPayment> wrap);

    AccountDeductResultVO accountDeduct(MemberGrade memberGrade, MemberInfo memberInfo,
                                        BigDecimal giftPayAmount, BigDecimal amount,
                                        BigDecimal rechargePayAmount,
                                        BigDecimal singleRechargePayAmount, BigDecimal singleGiftPayAmount,
                                        List<PosCashPayment> paymentList
    );

    boolean updateBatchById(List<PosCashPayment> paymentList);

    boolean removeById(Long id);

    /**
     * 聚合支付-支付方式汇总
     *
     * @param params
     * @return
     */
    PaymentTypeStatisticsResultVO statisticsPolymerize(PaymentTypeQuery params);

    /**
     * 聚合支付-支付方式明细
     *
     * @param params
     * @return
     */
    IPage<PaymentDetailsResultVO> polymerizeCashPaymentPage(PageParams<PaymentTypeQuery> params);

    /**
     * 聚合支付列表
     *
     * @param params
     * @return
     */
    List<PaymentDetailsResultVO> polymerizeCashPaymentList(PaymentTypeQuery params);

    /**
     * 聚合支付-支付方式明细-异步导出
     *
     * @param params
     * @return
     */
    Boolean cashPaymentExport(PaymentTypeQuery params);

    List<String> getMchNoListByCurOrg();

    void calcFeeRate(PosCashPayment cashPayment, BigDecimal feeRate);

    BigDecimal calcFeeRate(BigDecimal amount, BigDecimal feeRate);
}


