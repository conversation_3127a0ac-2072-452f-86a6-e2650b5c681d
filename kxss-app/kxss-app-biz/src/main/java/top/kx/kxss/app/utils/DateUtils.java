package top.kx.kxss.app.utils;

import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class DateUtils {

    public static int dateCompare(String from, Date to) {
        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm:ss");
        try {
            Date fromTime = sdf.parse(from + ":00");
            Date toTime = sdf.parse(sdf.format(to));
            return fromTime.compareTo(toTime);
        } catch (Exception e) {
            return 0;
        }
    }

    public static String getDate(String format) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(format);
        LocalDateTime localDateTime = LocalDateTime.now();
        String time = localDateTime.format(DateTimeFormatter.ofPattern(format));
        return time;
    }

    public static LocalDateTime getLocalDateTime(String format, String strLocalTime) {
        DateTimeFormatter df = DateTimeFormatter.ofPattern(format);
        return LocalDateTime.parse(strLocalTime, df);
    }


    public static Long calDifMinutes(LocalDateTime from, LocalDateTime to) {
        Duration duration = Duration.between(from, to);
        if (to.getSecond() != 0) {
            return duration.toMinutes() + 1;
        }
        return duration.toMinutes();
    }

    public static Long calDifDay(LocalDateTime from, LocalDateTime to) {
        Duration duration = Duration.between(from, to);
        return duration.toDays();
    }

    public static void main(String[] args) {
//        LocalDateTime from = LocalDateTime.now();
//        LocalDateTime to = DateUtils.getLocalDateTime("yyyy-MM-dd HH:mm:ss", "2023-04-30 18:37:20");
        System.out.println(calDifMinutes(LocalDateTime.of(2023, Month.JULY, 12, 19, 0, 0), LocalDateTime.now()));
    }

    public static List<LocalDate> getDatesBetween(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> dates = new ArrayList<>();
        long numOfDaysBetween = ChronoUnit.DAYS.between(startDate, endDate);
        for (int i = 0; i <= numOfDaysBetween; i++) {
            LocalDate date = startDate.plusDays(i);
            dates.add(date);
        }
        return dates;
    }
}
