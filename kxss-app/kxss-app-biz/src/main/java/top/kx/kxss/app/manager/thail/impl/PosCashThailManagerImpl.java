package top.kx.kxss.app.manager.thail.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.kxss.app.entity.thail.PosCashThail;
import top.kx.kxss.app.manager.thail.PosCashThailManager;
import top.kx.kxss.app.mapper.thail.PosCashThailMapper;
import top.kx.kxss.app.vo.result.ProfitResultVO;
import top.kx.kxss.app.vo.result.thail.PosCashThailAmountResultVO;

import java.util.List;

/**
 * <p>
 * 通用业务实现类
 * 订单套餐信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 19:11:17
 * @create [2023-09-14 19:11:17] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashThailManagerImpl extends SuperManagerImpl<PosCashThailMapper, PosCashThail> implements PosCashThailManager {


    @Override
    public ProfitResultVO findProfit(List<Long> posCashIdList) {
        return baseMapper.findProfit(posCashIdList);
    }

    @Override
    public List<PosCashThailAmountResultVO> thailAmountList(QueryWrap<PosCashThail> wrap) {
        return baseMapper.thailAmountList(wrap);
    }

    @Override
    public PosCashThailAmountResultVO thailAmountSum(QueryWrap<PosCashThail> wrap) {
        return baseMapper.thailAmountSum(wrap);
    }
}


