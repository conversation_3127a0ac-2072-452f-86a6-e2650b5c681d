package top.kx.kxss.app.service.bizcache.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.google.common.collect.Lists;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.cache.redis2.CacheResult;
import top.kx.basic.cache.repository.CachePlusOps;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.model.cache.CacheHashKey;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.query.BizCacheConsumeQuery;
import top.kx.kxss.common.cache.base.bizcache.BizCacheKeyBuilder;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.RabbitMqConstant;
import top.kx.kxss.model.enumeration.BizCacheEnum;
import top.kx.kxss.app.service.bizcache.BizCacheService;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@DS(DsConstant.BASE_TENANT)
@Service("bizCacheService")
public class BizCacheServiceImpl<T> implements BizCacheService<T> {


    @Autowired
    private CachePlusOps cachePlusOps;
    @Autowired
    private RabbitTemplate template;

    @Override
    public List<T> cacheList(T t, BizCacheEnum bizCacheEnum) {
        CacheHashKey cacheHashKey = BizCacheKeyBuilder.builder(bizCacheEnum.getCode()
                , bizCacheEnum.getCode()
                        + "_" + ContextUtil.getCurrentCompanyId());
        if (cacheHashKey == null) {
            template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.BIZ_CACHE,
                    JSON.toJSONString(BizCacheConsumeQuery.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId())
                            .bizCacheEnum(bizCacheEnum)
                            .build()));
            log.info("bizCache,{}", bizCacheEnum.getDesc());
            ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "请刷新后重试");
            return Lists.newArrayList();
        }
        CacheResult<List<T>> cacheResult = cachePlusOps.hGet(cacheHashKey);
        if (!cacheResult.isNull() && !cacheResult.isNullVal()) {
            return cacheResult.getValue();
        } else {
            template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.BIZ_CACHE,
                    JSON.toJSONString(BizCacheConsumeQuery.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId())
                            .bizCacheEnum(bizCacheEnum)
                            .build()));
            log.info("bizCache,{}", bizCacheEnum.getDesc());
            ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "请刷新后重试");
            return Lists.newArrayList();
        }
    }

    @Override
    public List<T> cacheList(T t, BizCacheEnum bizCacheEnum, Long memberId) {
        CacheHashKey cacheHashKey = BizCacheKeyBuilder.builder(bizCacheEnum.getCode()
                , bizCacheEnum.getCode()
                        + "_" + ContextUtil.getCurrentCompanyId() + "_" + memberId);
        if (cacheHashKey == null) {
            template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.BIZ_CACHE,
                    JSON.toJSONString(BizCacheConsumeQuery.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId())
                            .bizCacheEnum(bizCacheEnum)
                            .build()));
            log.info("bizCache,{}", bizCacheEnum.getDesc());
            return Lists.newArrayList();
        }
        CacheResult<List<T>> cacheResult = cachePlusOps.hGet(cacheHashKey);
        if (!cacheResult.isNull() && !cacheResult.isNullVal()) {
            return cacheResult.getValue();
        } else {
            template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.BIZ_CACHE,
                    JSON.toJSONString(BizCacheConsumeQuery.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId())
                            .bizCacheEnum(bizCacheEnum)
                            .build()));
            log.info("bizCache,{}", bizCacheEnum.getDesc());
            return Lists.newArrayList();
        }
    }
}

