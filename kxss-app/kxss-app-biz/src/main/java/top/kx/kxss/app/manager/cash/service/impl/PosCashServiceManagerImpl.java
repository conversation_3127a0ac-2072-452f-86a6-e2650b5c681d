package top.kx.kxss.app.manager.cash.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.app.manager.cash.service.PosCashServiceManager;
import top.kx.kxss.app.mapper.cash.service.PosCashServiceMapper;
import top.kx.kxss.app.vo.result.ProfitResultVO;

import java.util.List;

/**
 * <p>
 * 通用业务实现类
 * 收银-服务子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:44:58
 * @create [2023-04-19 14:44:58] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashServiceManagerImpl extends SuperManagerImpl<PosCashServiceMapper, PosCashService> implements PosCashServiceManager {


    @Override
    public ProfitResultVO findProfit(List<Long> posCashIdList) {
        return baseMapper.findProfit(posCashIdList);
    }
}


