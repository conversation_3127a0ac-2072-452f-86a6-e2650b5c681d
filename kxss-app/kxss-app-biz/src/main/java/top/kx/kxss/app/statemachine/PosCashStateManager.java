package top.kx.kxss.app.statemachine;

import org.springframework.stereotype.Component;
import top.kx.kxss.common.event.EventModel;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 订单
 * <AUTHOR>
 */
@Component
public class PosCashStateManager {
    Map<String, AbstractPosCashProcessor> posCashProcessorMap = new HashMap<String, AbstractPosCashProcessor>();

    public PosCashStateManager() {
    }

    /**
     * 状态流转方法
     *
     * @param posCashId 订单id
     * @param event     流转的订单操作事件
     *                  扭转后的订单状态
     */
    public void handleEvent(final Long posCashId, EventModel event, Object... params) {
        // 得到结果状态，在对应的processor中处理订单数据及其相关信息
        AbstractPosCashProcessor posCashProcessor = this.getPosCashProcessor(event.getEventId());
        if (!posCashProcessor.process(posCashId, params)) {
            throw new IllegalStateException(String.format("订单状态流转失败，订单id:%s", posCashId));
        }
    }

    /**
     * 状态流转方法
     *
     * @param posCashId 订单id
     * @param event     流转的订单操作事件
     * @return 扭转后的订单状态
     */
    public boolean handleBooleanEvent(final Long posCashId, EventModel event, Object... params) {
        // 得到结果状态，在对应的processor中处理订单数据及其相关信息
        AbstractPosCashProcessor orderProcessor = this.getPosCashProcessor(event.getEventId());
        return orderProcessor.process(posCashId, params);
    }

    /**
     * 根据入参状态枚举实例获取对应的状态后处理器
     *
     * @param event event
     */
    private AbstractPosCashProcessor getPosCashProcessor(String event) {
        AbstractPosCashProcessor processor = null;
        for (Map.Entry<String, AbstractPosCashProcessor> entry : posCashProcessorMap.entrySet()) {
            if (Objects.equals(entry.getKey(), event)) {
                processor = entry.getValue();
                break;
            }
        }
        if (null == processor) {
            throw new IllegalArgumentException(
                    String.format("can't find proper processor. The parameter state :%s", event));
        }
        return processor;
    }
}
