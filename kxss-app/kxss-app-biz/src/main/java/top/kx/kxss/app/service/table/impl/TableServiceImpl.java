package top.kx.kxss.app.service.table.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.exception.BizException;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.entity.cash.table.PosCashTableCash;
import top.kx.kxss.app.manager.cash.PosCashManager;
import top.kx.kxss.app.manager.cash.product.PosCashProductManager;
import top.kx.kxss.app.manager.cash.service.PosCashServiceManager;
import top.kx.kxss.app.manager.cash.table.PosCashTableManager;
import top.kx.kxss.app.mapper.cash.product.PosCashProductMapper;
import top.kx.kxss.app.mapper.cash.service.PosCashServiceMapper;
import top.kx.kxss.app.mapper.cash.table.PosCashTableMapper;
import top.kx.kxss.app.mapper.member.MemberMapper;
import top.kx.kxss.app.mapper.product.ProductMapper;
import top.kx.kxss.app.mapper.service.ServiceMapper;
import top.kx.kxss.app.mapper.table.TableCharingMapper;
import top.kx.kxss.app.mapper.table.TableMapper;
import top.kx.kxss.app.service.cash.PosCashServiceService;
import top.kx.kxss.app.service.table.TableService;
import top.kx.kxss.app.utils.DateUtils;
import top.kx.kxss.app.vo.query.cash.PosCashPageQuery;
import top.kx.kxss.app.vo.query.cash.product.PosCashProductPageQuery;
import top.kx.kxss.app.vo.query.cash.service.PosCashServicePageQuery;
import top.kx.kxss.app.vo.result.MenuPerResultVo;
import top.kx.kxss.app.vo.result.cash.PosCashAmountVO;
import top.kx.kxss.app.vo.result.cash.product.PosCashProductResultVO;
import top.kx.kxss.app.vo.result.cash.table.PosCashTableResultVO;
import top.kx.kxss.app.vo.result.product.AppProductResultVo;
import top.kx.kxss.app.vo.result.service.AppServiceMemberResultVO;
import top.kx.kxss.app.vo.result.service.AppServiceResultVO;
import top.kx.kxss.app.vo.result.table.charing.AppTableCharingResultVo;
import top.kx.kxss.app.vo.result.table.settings.AppTableChargingSettingResultVO;
import top.kx.kxss.app.vo.result.table.tableInfo.AppTableDetailResultVo;
import top.kx.kxss.app.vo.result.table.tableInfo.AppTableInfoResultVo;
import top.kx.kxss.app.vo.save.cash.PosCashSaveVO;
import top.kx.kxss.app.vo.save.cash.service.PosCashServiceSaveVO;
import top.kx.kxss.base.entity.common.BaseDict;
import top.kx.kxss.base.entity.coupon.BaseCouponRange;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.member.coupon.MemberCoupon;
import top.kx.kxss.base.entity.member.deposit.MemberDeposit;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.product.category.BaseProductCategory;
import top.kx.kxss.base.entity.service.BaseService;
import top.kx.kxss.base.entity.service.category.BaseServiceCategory;
import top.kx.kxss.base.entity.stock.BaseProductStock;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.entity.tableArea.BaseTableArea;
import top.kx.kxss.base.entity.tableCharging.BaseTableCharging;
import top.kx.kxss.base.entity.tableCharging.setting.BaseTableChargingSetting;
import top.kx.kxss.base.entity.tableType.BaseTableType;
import top.kx.kxss.base.entity.warehouse.BaseWarehouse;
import top.kx.kxss.base.manager.common.BaseDictManager;
import top.kx.kxss.base.manager.coupon.BaseCouponRangeManager;
import top.kx.kxss.base.manager.member.MemberInfoManager;
import top.kx.kxss.base.manager.member.coupon.MemberCouponManager;
import top.kx.kxss.base.manager.member.deposit.MemberDepositManager;
import top.kx.kxss.base.manager.product.BaseProductManager;
import top.kx.kxss.base.manager.product.category.BaseProductCategoryManager;
import top.kx.kxss.base.manager.service.category.BaseServiceCategoryManager;
import top.kx.kxss.base.manager.stock.BaseProductStockManager;
import top.kx.kxss.base.manager.system.BaseServiceManager;
import top.kx.kxss.base.manager.table.BaseTableInfoManager;
import top.kx.kxss.base.manager.tableArea.BaseTableAreaManager;
import top.kx.kxss.base.manager.tableCharging.BaseTableChargingManager;
import top.kx.kxss.base.manager.tableCharging.setting.BaseTableChargingSettingManager;
import top.kx.kxss.base.manager.tableType.BaseTableTypeManager;
import top.kx.kxss.base.service.table.BaseTableInfoService;
import top.kx.kxss.base.service.warehouse.BaseWarehouseService;
import top.kx.kxss.base.vo.query.product.BaseProductPageQuery;
import top.kx.kxss.base.vo.query.product.category.BaseProductCategoryPageQuery;
import top.kx.kxss.base.vo.query.service.BaseServicePageQuery;
import top.kx.kxss.base.vo.query.table.BaseTableInfoPageQuery;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.member.coupon.MemberCouponResultVO;
import top.kx.kxss.base.vo.result.product.category.BaseProductCategoryResultVO;
import top.kx.kxss.base.vo.result.service.category.BaseServiceCategoryResultVO;
import top.kx.kxss.base.vo.result.table.BaseTableInfoResultVO;
import top.kx.kxss.base.vo.save.table.BaseTableInfoSaveVO;
import top.kx.kxss.base.vo.update.table.BaseTableInfoUpdateVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.base.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static top.kx.basic.context.ContextUtil.getCurrentCompanyId;

/**
 * <p>
 * 业务实现类
 * 台桌数据
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-18 23:00:01
 * @create [2023-04-18 23:00:01] [zhou]
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class TableServiceImpl extends SuperServiceImpl<BaseTableInfoManager, Long, BaseTableInfo, BaseTableInfoSaveVO, BaseTableInfoUpdateVO, BaseTableInfoPageQuery, BaseTableInfoResultVO> implements TableService {

    @Autowired
    private BaseTableTypeManager tableTypeManager;
    @Autowired
    private BaseTableAreaManager tableAreaManager;
    @Autowired
    private TableMapper tableMapper;
    @Autowired
    private BaseTableChargingSettingManager chargingSettingManager;
    @Autowired
    private BaseTableChargingManager chargingManager;
    @Autowired
    private PosCashManager posCashManager;
    @Autowired
    private TableCharingMapper tableCharingMapper;
    @Autowired
    private PosCashTableManager posCashTableManager;
    @Autowired
    private PosCashTableMapper posCashTableMapper;
    @Autowired
    private PosCashServiceMapper posCashServiceMapper;
    @Autowired
    private PosCashProductMapper posCashProductMapper;
    @Autowired
    private MemberInfoManager memberInfoManager;
    @Autowired
    private BaseTableInfoManager baseTableInfoManager;
    @Autowired
    private MemberMapper memberMapper;
    @Autowired
    private PosCashProductManager posCashProductManager;
    @Autowired
    private MemberCouponManager memberCouponManager;
    @Autowired
    private BaseProductManager baseProductManager;
    @Autowired
    private ProductMapper productMapper;
    @Autowired
    private BaseProductCategoryManager baseProductCategoryManager;
    @Autowired
    private BaseCouponRangeManager baseCouponRangeManager;
    @Autowired
    private CalculateBizServiceImpl calculateBizService;
    @Autowired
    private BaseProductStockManager baseProductStockManager;
    @Autowired
    private BaseServiceManager baseServiceManager;
    @Autowired
    private ServiceMapper serviceMapper;
    @Autowired
    private BaseServiceCategoryManager baseServiceCategoryManager;
    @Autowired
    private PosCashServiceManager posCashServiceManager;

    @Autowired
    private PosCashServiceService posCashServiceService;
    @Autowired
    private MemberDepositManager depositManager;
    @Autowired
    private BaseDictManager baseDictManager;
    @Autowired
    private BaseTableInfoService tableInfoService;
    @Autowired
    private BaseWarehouseService baseWarehouseService;
    @Autowired
    private EchoService echoService;


    @Override
    public MenuPerResultVo menuPer() {
        return MenuPerResultVo.builder()
                .isRecharge(depositManager.count(Wraps.<MemberDeposit>lbQ().eq(MemberDeposit::getCreatedOrgId, ContextUtil.getCurrentCompanyId()).eq(MemberDeposit::getIsDesposit, true)) > 0)
                .build();
    }

    @Override
    @DS(DsConstant.BASE_TENANT)
    public Map<String, Object> listAll(BaseTableInfoPageQuery query) {
        Map<String, Object> result = new HashMap<>();
        // 获取所有台桌
        List<BaseTableInfo> tblList = superManager.list(Wraps.<BaseTableInfo>lbQ().eq(BaseTableInfo::getDisplay, "1").eq(BaseTableInfo::getTableArea, query.getTableArea()).eq(BaseTableInfo::getTableType, query.getTableType()).eq(BaseTableInfo::getTableStatus, query.getTableStatus()).orderByAsc(BaseTableInfo::getOrderNum));
        List<AppTableInfoResultVo> appTableInfoResultVos = BeanUtil.copyToList(tblList, AppTableInfoResultVo.class);

        // 获取所有台桌类型
        // tableType修改
//        List<BaseTableType> types = tableTypeManager.list(Wraps.<BaseTableType>lbQ().orderByAsc(BaseTableType::getSortValue));
        List<BaseDict> types = baseDictManager.list(Wraps.<BaseDict>lbQ().eq(BaseDict::getParentKey, "BASE_TABLE_TYPE").orderByAsc(BaseDict::getSortValue));
        result.put("types", types);
        // 获取区域
        List<BaseTableArea> areas = tableAreaManager.list(Wraps.<BaseTableArea>lbQ().orderByAsc(BaseTableArea::getSortValue));
        result.put("areas", areas);

        // 获取状态列表以及数量
        List<Map<String, Object>> statusList = tableMapper.countWithStatus();
        result.put("statusList", statusList);

        // 获取所有台桌数量
        long totalCnt = superManager.count(Wraps.<BaseTableInfo>lbQ().eq(BaseTableInfo::getDisplay, "1"));
        result.put("totalCnt", totalCnt);

        // 获取台桌的计费规则（当前时间段）
        List<BaseTableCharging> chargings = chargingManager.list(Wraps.<BaseTableCharging>lbQ().isNotNull(BaseTableCharging::getTableType));
        List<AppTableCharingResultVo> chargingResultVOS = BeanUtil.copyToList(chargings, AppTableCharingResultVo.class);

        // 获取台桌计费配置
        List<BaseTableChargingSetting> settings = chargingSettingManager.list(Wraps.<BaseTableChargingSetting>lbQ().orderByAsc(BaseTableChargingSetting::getChargingId, BaseTableChargingSetting::getStartTime));
        List<AppTableChargingSettingResultVO> settingResultVOS = BeanUtil.copyToList(settings, AppTableChargingSettingResultVO.class);
        Map<Long, List<AppTableChargingSettingResultVO>> settingsMap = settingResultVOS.stream().collect(Collectors.groupingBy(e -> e.getChargingId(), LinkedHashMap::new, Collectors.toList()));
        chargingResultVOS.forEach(e -> {
            e.setSettingList(settingsMap.get(e.getId()));
        });

        // 获取台桌是否有会员
        List<PosCashTableCash> posCashMembers = posCashTableMapper.queryStartTablesWithMember();
        Map<Long, PosCashTableCash> posCashMemberMap = posCashMembers.stream().collect(Collectors.toMap(PosCashTableCash::getTableId, e -> e));

        // 获取所有开台的cash_id
        List<Long> cashIds = posCashMembers.stream().map(PosCashTableCash::getCashId).collect(Collectors.toList());
        Map<Long, Long> servicesMap = null;
        Map<Long, Long> productsMap = null;
        if (0 != cashIds.size()) {
            // 查询当前有服务的台桌
            List<Map<String, Object>> services = posCashServiceMapper.getServiceByCashIds(cashIds);
            servicesMap = services.stream().collect(Collectors.toMap(e -> (Long) e.get("tableId"), e -> (Long) e.get("serviceCnt")));
            // 查询当前有商品的台桌
            List<Map<String, Object>> products = posCashProductMapper.getProductByCashIds(cashIds);
            productsMap = products.stream().collect(Collectors.toMap(e -> (Long) e.get("tableId"), e -> (Long) e.get("productCnt")));
        }
        // 组装台桌信息
        // tableType修改
        Map<String, AppTableCharingResultVo> chargingMap = chargingResultVOS.stream().collect(Collectors.toMap(AppTableCharingResultVo::getTableType, e -> e));

        for (AppTableInfoResultVo e : appTableInfoResultVos) {
            // 计价设置
            AppTableCharingResultVo charingResultVo = chargingMap.get(e.getTableType());
            if (null != charingResultVo) {
                e.setCharingResultVo(charingResultVo);
                // 计算当前时间段的单价
                List<AppTableChargingSettingResultVO> settingList = charingResultVo.getSettingList();
                // 获取最大、最小区间值
                List<BigDecimal> priceList = settingList.stream().map(AppTableChargingSettingResultVO::getCustomerPrice).collect(Collectors.toList());
                BigDecimal max = Collections.max(priceList);
                BigDecimal min = Collections.min(priceList);
                e.setPriceRange(min.compareTo(max) == 0 ? max.toString() : min.toString() + "-" + max.toString());
                // 获取当前计费周期价格
                for (AppTableChargingSettingResultVO vo : settingList) {
                    if (DateUtils.dateCompare(vo.getStartTime(), new Date()) <= 0 && DateUtils.dateCompare(vo.getEndTime(), new Date()) >= 0) {
                        e.setUnitPrice(vo.getCustomerPrice());
                        break;
                    }
                }
            }
            // 是否有会员
            e.setMemberId(null == posCashMemberMap.get(e.getId()) ? null : posCashMemberMap.get(e.getId()).getMemberId());
            e.setMemberName(null == posCashMemberMap.get(e.getId()) ? null : posCashMemberMap.get(e.getId()).getMemberName());
            // 是否有服务
            if (null != servicesMap) {
                e.setHasService(null != servicesMap.get(e.getId()) && 0 != servicesMap.get(e.getId()) ? true : false);
            }

            // 是否有商品
            if (null != productsMap) {
                e.setHasProduct(null != productsMap.get(e.getId()) && 0 != productsMap.get(e.getId()) ? true : false);
            }

        }
        result.put("tblList", appTableInfoResultVos);

        // 刷新台桌
        refreshTables(appTableInfoResultVos);
        return result;
    }

    @Override
    @DS(DsConstant.BASE_TENANT)
    public Map<String, Object> getCache() {
        // 获取所有台桌
        Map<String, Object> result = new HashMap<>();
        List<BaseTableInfo> tblList = superManager.list(Wraps.<BaseTableInfo>lbQ().eq(BaseTableInfo::getDisplay, "1").orderByAsc(BaseTableInfo::getOrderNum));
        List<AppTableInfoResultVo> appTableInfoResultVos = BeanUtil.copyToList(tblList, AppTableInfoResultVo.class);


        // 获取所有台桌类型
        List<BaseTableType> types = tableTypeManager.list(Wraps.<BaseTableType>lbQ().orderByAsc(BaseTableType::getSortValue));
        result.put("types", types);

        // 获取区域
        List<BaseTableArea> areas = tableAreaManager.list(Wraps.<BaseTableArea>lbQ().orderByAsc(BaseTableArea::getSortValue));
        result.put("areas", areas);

        // 获取状态列表以及数量
        List<Map<String, Object>> statusList = tableMapper.countWithStatus();
        result.put("statusList", statusList);

        // 获取所有台桌数量
        long totalCnt = superManager.count(Wraps.<BaseTableInfo>lbQ().eq(BaseTableInfo::getDisplay, "1"));
        result.put("totalCnt", totalCnt);

        // 获取台桌的计费规则（当前时间段）
        List<BaseTableCharging> chargings = chargingManager.list(Wraps.<BaseTableCharging>lbQ().isNotNull(BaseTableCharging::getTableType));
        List<AppTableCharingResultVo> chargingResultVOS = BeanUtil.copyToList(chargings, AppTableCharingResultVo.class);

        // 获取台桌计费配置
        List<BaseTableChargingSetting> settings = chargingSettingManager.list();
        List<AppTableChargingSettingResultVO> settingResultVOS = BeanUtil.copyToList(settings, AppTableChargingSettingResultVO.class);
        Map<Long, List<AppTableChargingSettingResultVO>> settingsMap = settingResultVOS.stream().collect(Collectors.groupingBy(e -> e.getChargingId()));
        chargingResultVOS.forEach(e -> {
            e.setSettingList(settingsMap.get(e.getId()));
        });

        // 组装台桌信息
        Map<String, AppTableCharingResultVo> chargingMap = chargingResultVOS.stream().collect(Collectors.toMap(AppTableCharingResultVo::getTableType, e -> e));
        appTableInfoResultVos.stream().forEach(e -> {
            AppTableCharingResultVo charingResultVo = chargingMap.get(e.getTableType());
            if (null != charingResultVo) {
                e.setCharingResultVo(charingResultVo);
            }
        });
        result.put("tblList", appTableInfoResultVos);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS(DsConstant.BASE_TENANT)
    public Map<String, Object> startTable(PosCashSaveVO posCashSaveVO) {
        Map<String, Object> result = new HashMap<>();
        // 1. 保存pos_cash
        PosCash posCash = new PosCash();
        BeanUtils.copyProperties(posCashSaveVO, posCash);
        // 类型
        posCash.setType(PosCashTypeEnum.START_TABLE.getCode());
        // 单据code
        posCash.setCode(tableInfoService.randomOrderCode());
        // 单据日期
        posCash.setBillDate(LocalDate.now());
        // 单据状态
        posCash.setBillState(PosCashBillStateEnum.NO_SETTLED.getCode());
        posCash.setBillType(PosCashBillTypeEnum.REGULAR_SINGLE.getCode());
        // 门店id
        posCash.setOrgId(getCurrentCompanyId());
        posCash.setCreatedOrgId(getCurrentCompanyId());
        // 员工id
        posCash.setEmployeeId(null);
        posCash.setCreatedEmp(ContextUtil.getEmployeeId());
        // 台桌id
        posCash.setTableId(posCashSaveVO.getTableId());
        // 支付名
        posCash.setPayName("已开台，待支付");
        posCash.setCreatedTime(LocalDateTime.now());
        posCash.setUpdatedTime(null);
        posCash.setOrderSource(OrderSourceEnum.POS.getCode());
        posCash.setIsTurn(false);
        posCash.setCreatedBy(ContextUtil.getUserId());
        posCash.setRefundAmount(BigDecimal.ZERO);
        posCash.setSn(ContextUtil.getSn());
        boolean suc = posCashManager.save(posCash);
        if (!suc) {
            throw new BizException("开台失败！");
        }
        // 2. 保存cash_table
        PosCashTable posCashTable = new PosCashTable();
        // cashId
        posCashTable.setCashId(posCash.getId());
        // 台桌Id
        posCashTable.setTableId(posCashSaveVO.getTableId());
        // 开始时间
        posCashTable.setStartTime(LocalDateTime.now().withSecond(0).withNano(0));
        // 获取当前时间段单价
//        BaseTableInfo baseTableInfo = baseTableInfoManager.getById(posCashSaveVO.getTableId());
//        BaseDict baseDict = baseDictManager.getOne(Wraps.<BaseDict>lbQ().eq(BaseDict::getKey, baseTableInfo.getTableType()));
        // tableType修改
        BaseTableChargingSetting chargingSetting = tableCharingMapper.getNowChargingSet(posCashSaveVO.getTableId());
        posCashTable.setChargingSettingId(chargingSetting.getId());
        // 状态 计时中
        posCashTable.setStatus("0");
        // 单价
        if (null != posCashSaveVO.getMemberId()) {
            // 会员时使用会员价格
            BigDecimal memberPrice = chargingSetting.getMemberPrice();
            // 会员折扣价格
            Map<String, Object> memberTableDiscount = memberMapper.getMemberDiscount(posCash.getMemberId(), "1");
            BigDecimal tableDiscount = new BigDecimal(10);
            if (ObjectUtil.isNotNull(memberTableDiscount)) {
                tableDiscount = (BigDecimal) memberTableDiscount.get("discount");
            }

            BigDecimal disPrice = chargingSetting.getCustomerPrice().multiply(tableDiscount.divide(new BigDecimal(10), 2, RoundingMode.UP)).setScale(2, RoundingMode.UP);
            BigDecimal price = null;
            if (memberPrice.compareTo(disPrice) >= 0) {
                price = disPrice;
            } else {
                price = memberPrice;
            }
            posCashTable.setPrice(price);
            // 原始价格
            posCashTable.setOrginPrice(price);
        } else {
            // 非会员使用散客价格
            posCashTable.setPrice(chargingSetting.getCustomerPrice());
            posCashTable.setOrginPrice(chargingSetting.getCustomerPrice());
        }

        // 提醒时间
        if (!StringUtils.isEmpty(posCashSaveVO.getRemindTime())) {
            if (posCashSaveVO.getRemindTime().contains(":")) {
                // 设置截至时间
                String nowTime = DateUtils.getDate("yyyy-MM-dd") + " " + posCashSaveVO.getRemindTime() + ":59";
                posCashTable.setRemindTime(DateUtils.getLocalDateTime("yyyy-MM-dd HH:mm:ss", nowTime));
            } else {
                // 设置分钟
                LocalDateTime time = LocalDateTime.now();
                posCashTable.setRemindTime(time.plusMinutes(Long.parseLong(posCashSaveVO.getRemindTime())));
            }
        }

        posCashTable.setCreatedTime(LocalDateTime.now());
        posCashTable.setUpdatedTime(null);
        posCashTable.setSn(ContextUtil.getSn());

        // 保存
        suc = posCashTableManager.save(posCashTable);
        if (!suc) {
            throw new BizException("开台失败！");
        }
        // 更新tableInfo状态
        BaseTableInfo baseTableInfo = baseTableInfoManager.getById(posCashSaveVO.getTableId());
        baseTableInfo.setTableStatus(TableStatus.USING.getCode());
        baseTableInfo.setUpdatedTime(LocalDateTime.now());
        suc = baseTableInfoManager.updateById(baseTableInfo);
        if (!suc) {
            throw new BizException("开台失败！");
        }
        return result;
    }

    @Override
    public List<MemberInfoResultVO> queryMember(Map<String, Object> query) {
        String serchName = (String) query.get("member");
        List<MemberInfo> memberInfo = memberInfoManager.list(Wraps.<MemberInfo>lbQ().like(MemberInfo::getMobile, serchName).or().like(MemberInfo::getName, serchName));
        if (null == memberInfo || 0 == memberInfo.size()) {
            return null;
        }
        List<MemberInfoResultVO> resultVO = BeanUtil.copyToList(memberInfo, MemberInfoResultVO.class);
        return resultVO;
    }

    @Override
    @DS(DsConstant.BASE_TENANT)
    public Map<String, Object> queryTableDetail(BaseTableInfoPageQuery query) {
        Map<String, Object> result = new HashMap<>();
        // 1. 桌台信息
        BaseTableInfo tableInfo = baseTableInfoManager.getById(query.getId());
        AppTableDetailResultVo tableResultVo = BeanUtil.copyProperties(tableInfo, AppTableDetailResultVo.class);
        result.put("tableResult", tableResultVo);

        // 2. 获取开台结算信息 pos_cash
        PosCash posCash = null;
        try {
            posCash = posCashManager.getOne(Wraps.<PosCash>lbQ().eq(PosCash::getTableId, query.getId())
                    .eq(PosCash::getBillState, PosCashBillStateEnum.NO_SETTLED.getCode())
                    .eq(PosCash::getBillType, PosCashBillTypeEnum.REGULAR_SINGLE.getCode()));
        } catch (Exception e) {
            log.error("posCash中存在相同台桌的多个已开台记录");
            throw new BizException("开台信息异常");
        } finally {
            if (null == posCash) {
                log.error("posCash中不存在台桌的已开台记录");
                throw new BizException("开台信息异常");
            }
        }
        result.put("posCash", posCash);


        // 3. 获取台费结算记录 pos_cash_table
        BigDecimal orgTotal = new BigDecimal(0);
        BigDecimal amountTotal = new BigDecimal(0);
        List<PosCashTable> posCashTables = posCashTableManager.list(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, posCash.getId()).orderByAsc(PosCashTable::getStartTime));
        List<PosCashTableResultVO> cashTableResultVOS = BeanUtil.copyToList(posCashTables, PosCashTableResultVO.class);
        // 3.1 获取当前table_charging
        BaseTableCharging charging = chargingManager.getOne(Wraps.<BaseTableCharging>lbQ().eq(BaseTableCharging::getTableType, tableInfo.getTableType()));
        // 3.2 计算时长
        for (PosCashTableResultVO pct : cashTableResultVOS) {
            LocalDateTime endTime = null == pct.getEndTime() ? LocalDateTime.now() : pct.getEndTime();
            long startMinutes = DateUtils.calDifMinutes(pct.getStartTime(), endTime);
            pct.setDuration(Integer.valueOf(startMinutes + ""));
            // endTime为空时，计算当前金额
            if (null == pct.getEndTime()) {
                PosCashTableCash posCashTableCash = BeanUtil.copyProperties(pct, PosCashTableCash.class);
                posCashTableCash.setEndTime(endTime);
                AppTableCharingResultVo chargingResultVO = BeanUtil.copyProperties(charging, AppTableCharingResultVo.class);
                calAmount(posCashTableCash, null, chargingResultVO, null != posCash.getMemberId());
                BigDecimal amount = posCashTableCash.getAmount();
                if (!"".equals(posCashTableCash.getType())) {
                    if ("6".equals(posCashTableCash.getType())) {
                        long dicountTime = posCashTableCash.getDiscount().setScale(0, RoundingMode.DOWN).longValue();
                        amount = calculateBizService.orginPriceCountAmountByType6(posCashTableCash.getPrice(), posCashTableCash.getDuration().longValue(), dicountTime, charging.getPeriod(), charging.getOvertime());
                    } else {
                        amount = calculateBizService.orginPriceCountAmount(posCashTableCash.getAmount(), posCashTableCash.getType(), posCashTableCash.getDiscount());
                    }
                }

                pct.setOrginPrice(posCashTableCash.getAmount());
                pct.setStatus("0");
                pct.setUnitPriceTime(chargingResultVO.getPeriod());

                orgTotal = orgTotal.add(posCashTableCash.getAmount());
                amountTotal = amountTotal.add(amount);

                // 计算剩余分钟 tableInfo
                if (null != posCashTableCash.getRemindTime()) {
                    Long countdownMinutes = DateUtils.calDifMinutes(LocalDateTime.now(), posCashTableCash.getRemindTime());
                    pct.setCountdownMinutes(countdownMinutes);
                }
            } else {
                orgTotal = orgTotal.add(pct.getOrginPrice());
                amountTotal = amountTotal.add(pct.getAmount());
                pct.setStatus("1");
                pct.setUnitPriceTime(charging.getPeriod());
            }
        }
        result.put("cashTableResult", cashTableResultVOS);

        // 4. 获取会员信息
        if (null != posCash.getMemberId()) {
            Map<String, Object> memberInfo = memberMapper.getMemberInfo(posCash.getMemberId());
            BigDecimal giftAmount = null == memberInfo.get("giftAmount") ? new BigDecimal(0) : (BigDecimal) memberInfo.get("giftAmount");
            BigDecimal rechargeAmount = null == memberInfo.get("rechargeAmount") ? new BigDecimal(0) : (BigDecimal) memberInfo.get("rechargeAmount");
            memberInfo.put("balance", giftAmount.add(rechargeAmount));
            result.put("memberInfo", memberInfo);
        } else {
            result.put("memberInfo", null);
        }

        //5. 获取商品
        List<PosCashProduct> products = posCashProductManager.list(Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getCashId, posCash.getId()).orderByDesc(PosCashProduct::getCreatedTime));
        // 获取商品最低价
        List<Long> productIds = products.stream().map(PosCashProduct::getProductId).collect(Collectors.toList());
        List<BaseProduct> baseProducts = baseProductManager.list(Wraps.<BaseProduct>lbQ().in(BaseProduct::getId, productIds));
        Map<Long, BaseProduct> baseProductMap = baseProducts.stream().collect(Collectors.toMap(BaseProduct::getId, e -> e));
        List<PosCashProductResultVO> productResultVOS = BeanUtil.copyToList(products, PosCashProductResultVO.class);
        for (PosCashProductResultVO vo : productResultVOS) {
            orgTotal = orgTotal.add(vo.getOrginPrice());
            amountTotal = amountTotal.add(vo.getAmount());
            if (null != baseProductMap.get(vo.getProductId())) {
                if (null != baseProductMap.get(vo.getProductId()).getMinPrice()) {
                    vo.setMinPrice(baseProductMap.get(vo.getProductId()).getMinPrice());
                }
            }
        }
        result.put("productResultVOS", productResultVOS);

        //6 获取服务
        List<Map<String, Object>> posCashServices = posCashServiceMapper.queryServices(posCash.getId());
        // 6.1 计算服务是否结束
        for (Map<String, Object> service : posCashServices) {
            if (null == service.get("minPrice")) {
                service.put("minPrice", null);
            }
//            if (ObjectUtil.isNotNull(service.get("duration"))) {
            LocalDateTime endTime = (LocalDateTime) service.get("endTime");
            LocalDateTime startTime = (LocalDateTime) service.get("startTime");
            if (null == endTime) {
                // 计算时长
                Long startMinutes = DateUtils.calDifMinutes(startTime, LocalDateTime.now());
                service.put("startMinutes", startMinutes);
                // 计算金额
                Integer duration = (Integer) service.get("billingCycle");
                Integer timeoutPeriod = (Integer) service.get("timeoutPeriod");

                BigDecimal orginPrice = new BigDecimal(0);
                BigDecimal amount = new BigDecimal(0);
                if (0 != startMinutes) {
//                BigDecimal keepMin = new BigDecimal(startMinutes).divide(new BigDecimal(duration), 2, BigDecimal.ROUND_HALF_UP);
//                BigDecimal amount = keepMin.multiply((BigDecimal) service.get("price")).setScale(2, BigDecimal.ROUND_HALF_UP);
//                BigDecimal amount = calServiceAmount(startMinutes.intValue(), duration, (BigDecimal) service.get("price"));
                    orginPrice = calculateBizService.overTimeCash((BigDecimal) service.get("price"), startMinutes, duration, timeoutPeriod);
//                BigDecimal amount = calculateBizService.orginPriceCountAmount(orginPrice, null == service.get("type") ? "" : service.get("type").toString(), null == service.get("discount") ? null : (BigDecimal) service.get("discount"));

                    amount = orginPrice;
                    String type = null == service.get("type") ? "" : service.get("type").toString();
                    if (!"".equals(type)) {
                        BigDecimal discount = (BigDecimal) service.get("discount");
                        if ("6".equals(type)) {
                            long dicountTime = discount.setScale(0, RoundingMode.DOWN).longValue();
                            amount = calculateBizService.orginPriceCountAmountByType6((BigDecimal) service.get("price"), startMinutes, dicountTime, duration, timeoutPeriod);
                        } else {
                            amount = calculateBizService.orginPriceCountAmount(orginPrice, type, discount);
                        }
                    }
                }

                service.put("orginPrice", orginPrice);
                orgTotal = orgTotal.add(orginPrice);
                amountTotal = amountTotal.add(amount);
            } else {
                orgTotal = orgTotal.add(new BigDecimal(service.get("orginPrice").toString()));
                amountTotal = amountTotal.add(new BigDecimal(service.get("amount").toString()));
            }
            LocalDateTime remindTime = (LocalDateTime) service.get("remindTime");
            if (null != remindTime) {
                // 计算倒计时
                Long countdownMinutes = DateUtils.calDifMinutes(LocalDateTime.now(), remindTime);
                service.put("countdownMinutes", countdownMinutes);
            }
//            }
        }
        result.put("posCashServices", posCashServices);

        // 7. 获取优惠劵数量
        Map<String, Long> cntMap = new HashMap<>();
        if (null != posCash.getMemberId()) {
            Long couponCnt = memberCouponManager.count(Wraps.<MemberCoupon>lbQ().eq(MemberCoupon::getMemberId, posCash.getMemberId()).eq(MemberCoupon::getStatus, "1"));
            cntMap.put("couponCnt", couponCnt);
        } else {
            cntMap.put("couponCnt", null);
        }

        result.put("cntMap", cntMap);
        // 8. 调用结算接口 展示费用信息 TODO
        Map<String, Object> param = new HashMap<>();
        param.put("tableId", query.getId());
        param.put("cashId", posCash.getId());
        param.put("needUpdate", false);
        LinkedHashMap<String, Object> posResult = posCashServiceService.tablePosCash(param);
        result.put("posResult", posResult);
        result.put("orgTotal", orgTotal);
        result.put("amountTotal", amountTotal);
        return result;
    }

    @Override
    @DS(DsConstant.BASE_TENANT)
    public List<AppProductResultVo> queryProducts(BaseProductPageQuery query) {
        List<AppProductResultVo> products = productMapper.queryProducts(query);
        return products;
    }

    @Override
    @DS(DsConstant.BASE_TENANT)
    public List<BaseProductCategoryResultVO> queryProductCategory(BaseProductCategoryPageQuery query) {
        List<BaseProductCategory> categories = baseProductCategoryManager.list(Wraps.<BaseProductCategory>lbQ().eq(BaseProductCategory::getState, "1").eq(BaseProductCategory::getTreeGrade, 1).orderByDesc(BaseProductCategory::getSortValue));
        return BeanUtil.copyToList(categories, BaseProductCategoryResultVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveProduct(Map<String, Object> query) {
        String productId = (String) query.get("id");
        String posCashId = (String) query.get("posCashId");
        PosCash posCash = posCashManager.getById(posCashId);
        BaseProduct product = baseProductManager.getById(productId);
        // 判断是否可以购买(库存量)
//        if(0 >= product.getCurrentStock()) {
//            if("0".equals(product.getIsNegative())) {
//                return "库存不足，无法购买";
//            }
//        }
        BaseWarehouse baseWarehouse = baseWarehouseService.getOneBySn();
        ArgumentAssert.notNull(baseWarehouse, "当前设备未选择销售仓库,请选择后重试");

        BaseProductStock baseProductStock = baseProductStockManager.getOne(Wraps.<BaseProductStock>lbQ()
                .eq(BaseProductStock::getProductId, productId)
                .eq(BaseProductStock::getWarehouseId, baseWarehouse.getId()));

        if (null != baseProductStock && 0 > baseProductStock.getNum() - 1 && !product.getIsNegative()) {
            return "该商品库存不足";
        }
        // 计算商品现价
        BigDecimal price = calculateBizService.calProductAmount(product, posCash.getMemberId());
        Long orgId = ContextUtil.getCurrentCompanyId();

        // 查看是否已有 discount为空 或者 discount为0的数据的 该商品
        PosCashProduct preProduct = posCashProductManager.getOne(Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getCashId, posCash.getId()).eq(PosCashProduct::getProductId, Long.parseLong(productId)).eq(PosCashProduct::getDeleteFlag, '0').and(e -> e.isNull(PosCashProduct::getDiscount).or().eq(PosCashProduct::getDiscount, 0.00).or().eq(PosCashProduct::getDiscount, 0)));
        if (null == preProduct) {
            // 如果没有 discount为空 或者 discount为0的数据，则插入一条新订单商品
            // 保存至cash_product
            PosCashProduct posCashProduct = new PosCashProduct();
            posCashProduct.setCashId(Long.parseLong(posCashId));
            posCashProduct.setProductId(Long.parseLong(productId));
            posCashProduct.setProductName(product.getName());
            posCashProduct.setNum(1);
            posCashProduct.setPrice(price);
            posCashProduct.setOrginPrice(price.multiply(new BigDecimal(posCashProduct.getNum())));
            posCashProduct.setAmount(price.multiply(new BigDecimal(posCashProduct.getNum())));
            posCashProduct.setIsGift(false);
            posCashProduct.setCreatedOrgId(orgId);
            posCashProduct.setCreatedTime(LocalDateTime.now());
            posCashProduct.setUpdatedTime(null);
            posCashProduct.setSn(ContextUtil.getSn());
            posCashProduct.setWarehouseId(baseWarehouse.getId());
            boolean suc = posCashProductManager.save(posCashProduct);
            if (!suc) {
                return "商品添加失败";
            }
        } else {
            // 有 则直接增加商品数量
            preProduct.setNum(preProduct.getNum() + 1);
            preProduct.setAmount(price.multiply(new BigDecimal(preProduct.getNum())));
            preProduct.setOrginPrice(price.multiply(new BigDecimal(preProduct.getNum())));
            preProduct.setUpdatedTime(LocalDateTime.now());
            boolean suc = posCashProductManager.updateById(preProduct);
            if (!suc) {
                return "商品添加失败";
            }
        }

        // 更新 base_product_stock
        if (null != baseProductStock) {
            baseProductStock.setNum(baseProductStock.getNum() - 1);
            baseProductStock.setUpdatedTime(LocalDateTime.now());
            Boolean suc = baseProductStockManager.updateById(baseProductStock);
            if (!suc) {
                throw new BizException("库存更新失败");
            }
        }
        // 调用计算接口
        if (null != posCash.getPayment() && posCash.getPayment().compareTo(new BigDecimal(0)) != 0) {
            posCash.setPayment(posCash.getPayment().add(price));
            posCash.setUpdatedBy(ContextUtil.getUserId());
            posCash.setUpdatedTime(LocalDateTime.now());
            posCashManager.updateById(posCash);
        }
        return null;
    }

    @Override
    public List<MemberCouponResultVO> queryMemberCoupon(Map<String, Object> query) {
        Long memberId = (Long) query.get("memberId");
        List<MemberCoupon> coupons = memberCouponManager.list(Wraps.<MemberCoupon>lbQ().eq(MemberCoupon::getMemberId, memberId).eq(MemberCoupon::getStatus, "1").apply("DATE_FORMAT(expires_time,'%Y-%m-%d %H:%i:%s') > DATE_FORMAT(now,'%Y-%m-%d %H:%i:%s')").orderByAsc(MemberCoupon::getName));
        List<MemberCouponResultVO> memberCouponResultVOS = BeanUtil.copyToList(coupons, MemberCouponResultVO.class);
        List<Long> couponIds = memberCouponResultVOS.stream().map(MemberCouponResultVO::getCouponId).collect(Collectors.toList());
        // 获取优惠劵使用范围
        List<BaseCouponRange> ranges = baseCouponRangeManager.list(Wraps.<BaseCouponRange>lbQ().in(BaseCouponRange::getCouponId, couponIds));
        Map<Long, List<BaseCouponRange>> rangeMap = ranges.stream().collect(Collectors.groupingBy(BaseCouponRange::getCouponId));
        for (MemberCouponResultVO vo : memberCouponResultVOS) {
            List<BaseCouponRange> range = rangeMap.get(vo.getCouponId());
            vo.setRanges(range);
        }
        return memberCouponResultVOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveProductCount(PosCashProductPageQuery saveVO) {
        PosCashProduct posCashProduct = posCashProductManager.getById(saveVO.getId());
        int preStock = posCashProduct.getNum();
        // 判断库存
        long productId = posCashProduct.getProductId();
        BaseProduct baseProduct = baseProductManager.getById(productId);
        BaseProductStock baseProductStock = baseProductStockManager.getOne(Wraps.<BaseProductStock>lbQ().eq(BaseProductStock::getProductId, posCashProduct.getProductId()));
        if (null != baseProductStock && 0 > baseProductStock.getNum() - saveVO.getNum() && !baseProduct.getIsNegative()) {
            return "该商品库存不足";
        }
        //重新计算金额
        BigDecimal orginPrice = posCashProduct.getPrice().multiply(new BigDecimal(saveVO.getNum()));
        BigDecimal amount = calculateBizService.orginPriceCountAmount(orginPrice, posCashProduct.getType(), posCashProduct.getDiscount());

        posCashProduct.setOrginPrice(orginPrice);
        posCashProduct.setAmount(amount);
        posCashProduct.setNum(saveVO.getNum());
        posCashProduct.setUpdatedTime(LocalDateTime.now());
        boolean suc = posCashProductManager.updateById(posCashProduct);
        if (suc) {
            // 减少库存
            int stock = -1;
            if (saveVO.getNum() > preStock) {
                stock = 1;
            }
//            baseProduct.setCurrentStock(baseProduct.getCurrentStock() - stock);
//            suc = baseProductManager.updateById(baseProduct);
//            if(!suc) {
//                throw new BizException("库存更新失败");
//            }
            // 更新 base_product_stock
            if (null != baseProductStock) {
                baseProductStock.setNum(baseProductStock.getNum() - stock);
                baseProductStock.setUpdatedTime(LocalDateTime.now());
                suc = baseProductStockManager.updateById(baseProductStock);
                if (!suc) {
                    throw new BizException("库存更新失败");
                }
            }

        } else {
            return "商品数量更新失败";
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String deleteProduct(PosCashProductPageQuery saveVO) {
        // 删除pos_cash_product
        boolean suc = posCashProductManager.removeById(saveVO.getId());
        if (!suc) {
            return "删除失败";
        }
        // 增加回库存
        int num = saveVO.getNum();
        long productId = saveVO.getProductId();
        BaseProduct baseProduct = baseProductManager.getById(productId);
//        baseProduct.setCurrentStock(baseProduct.getCurrentStock() + num);
//        suc = baseProductManager.updateById(baseProduct);
//        if (!suc) {
//            throw new BizException("库存更新失败");
//        }
        BaseProductStock baseProductStock = baseProductStockManager.getOne(Wraps.<BaseProductStock>lbQ().eq(BaseProductStock::getProductId, productId));
        if (null != baseProductStock) {
            baseProductStock.setNum(baseProductStock.getNum() + num);
            baseProductStock.setUpdatedTime(LocalDateTime.now());
            suc = baseProductStockManager.updateById(baseProductStock);
            if (!suc) {
                throw new BizException("库存更新失败");
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String updateCashMember(Map<String, Object> query) {
        String memberId = (String) query.get("memberId");
        String posCashId = (String) query.get("posCashId");
        PosCash posCash = posCashManager.getById(posCashId);
        posCash.setMemberId(Long.parseLong(memberId));
        posCash.setUpdatedBy(ContextUtil.getUserId());
        posCash.setUpdatedTime(LocalDateTime.now());
        boolean suc = posCashManager.updateById(posCash);
        if (!suc) {
            return "结算表更新失败";
        }

        List<PosCashTable> posCashTables = posCashTableManager.list(Wraps.<PosCashTable>lbQ().eq(PosCashTable::getCashId, posCashId));
        // 获取tableSetting
        BaseTableInfo baseTableInfo = baseTableInfoManager.getById(posCash.getTableId());
        BaseTableCharging baseTableCharging = chargingManager.getOne(Wraps.<BaseTableCharging>lbQ().eq(BaseTableCharging::getTableType, baseTableInfo.getTableType()));
        List<BaseTableChargingSetting> settings = chargingSettingManager.list(Wraps.<BaseTableChargingSetting>lbQ().eq(BaseTableChargingSetting::getChargingId, baseTableCharging.getId()));
        Map<Long, BaseTableChargingSetting> settingsMap = settings.stream().collect(Collectors.toMap(e -> e.getId(), e -> e));

        // 获取会员折扣
        // 会员的时候，获取会员的折扣权益
        Map<String, Object> memberDiscount = memberMapper.getMemberDiscount(Long.parseLong(memberId), "1");

        // 更新 并重新计算price
        for (PosCashTable posCashTable : posCashTables) {
            // 获取当前记录的setting
            BaseTableChargingSetting set = settingsMap.get(posCashTable.getChargingSettingId());
            AppTableChargingSettingResultVO todaySet = BeanUtil.copyProperties(set, AppTableChargingSettingResultVO.class);
            // 计算
            AppTableCharingResultVo charing = BeanUtil.copyProperties(baseTableCharging, AppTableCharingResultVo.class);
            PosCashTableCash pct = BeanUtil.copyProperties(posCashTable, PosCashTableCash.class);
            BigDecimal price = calculateBizService.calTableAmount(todaySet, Long.parseLong(memberId), memberDiscount);
            pct.setPrice(price);
//            pct.setPrice(set.getMemberPrice());
            calAmount(pct, null, charing, true);
//            posCashTable.setPrice(set.getMemberPrice());
            posCashTable.setPrice(price);
            posCashTable.setAmount(pct.getAmount());
        }
        // 更新
        suc = posCashTableManager.updateBatchById(posCashTables);
        if (!suc) {
            throw new BizException("会员添加失败");
        }

        // 商品
        List<PosCashProduct> cashProducts = posCashProductManager.list(Wraps.<PosCashProduct>lbQ().eq(PosCashProduct::getCashId, posCashId).eq(PosCashProduct::getIsGift, "0"));
        if (null != cashProducts && 0 != cashProducts.size()) {
            List<Long> productIds = cashProducts.stream().map(PosCashProduct::getProductId).collect(Collectors.toList());
            List<BaseProduct> products = baseProductManager.list(Wraps.<BaseProduct>lbQ().in(BaseProduct::getId, productIds));
            Map<Long, BaseProduct> productMap = products.stream().collect(Collectors.toMap(BaseProduct::getId, e -> e));
            for (PosCashProduct cashProduct : cashProducts) {
                BaseProduct product = productMap.get(cashProduct.getProductId());
                BigDecimal price = calculateBizService.calProductAmount(product, posCash.getMemberId());
                cashProduct.setPrice(price);
                // 计算总价
                BigDecimal amount = price.multiply(new BigDecimal(cashProduct.getNum())).setScale(2, BigDecimal.ROUND_HALF_UP);
                cashProduct.setAmount(amount);
            }
            suc = posCashProductManager.updateBatchById(cashProducts);
            if (!suc) {
                throw new BizException("会员添加失败");
            }
        }


        // 服务
        List<PosCashService> posCashServices = posCashServiceManager.list(Wraps.<PosCashService>lbQ().eq(PosCashService::getCashId, posCashId));
        if (null != posCashServices && 0 != posCashServices.size()) {
            List<Long> serviceIds = posCashServices.stream().map(PosCashService::getServiceId).collect(Collectors.toList());
            List<BaseService> services = baseServiceManager.list(Wraps.<BaseService>lbQ().in(BaseService::getId, serviceIds));
            Map<Long, BaseService> serviceMap = services.stream().collect(Collectors.toMap(e -> e.getId(), e -> e));
            for (PosCashService posCashService : posCashServices) {
                if (null != posCashService.getType() && !"3".equals(posCashService.getType())) {
                    BaseService service = serviceMap.get(posCashService.getServiceId());
                    BigDecimal price = calculateBizService.calServiceAmount(service, posCash.getMemberId());
                    posCashService.setPrice(price);
                    // 如果结束了 计算总价
                    if (null != posCashService.getEndTime()) {
                        BigDecimal amount = calServiceAmount(posCashService.getDuration(), service.getDuration(), price);
                        posCashService.setAmount(amount);
                    }

                }
            }
            suc = posCashServiceManager.updateBatchById(posCashServices);
            if (!suc) {
                throw new BizException("会员添加失败");
            }
        }

        return "";
    }

    @Override
    public String saveCashTableOperate(Map<String, Object> query) {
        String cashTableId = (String) query.get("posCashTableId");
        Boolean countDown = (Boolean) query.get("countDown");
        Integer countDownMinute = (Integer) query.get("countDownMinute");
        Boolean time = (Boolean) query.get("time");
        Integer timeMinute = (Integer) query.get("timeMinute");
        Boolean stop = (Boolean) query.get("stop");
        // poscashTable详情
        PosCashTable posCashTable = posCashTableManager.getById(cashTableId);
        PosCash posCash = posCashManager.getById(posCashTable.getCashId());
        BaseTableInfo tableInfo = baseTableInfoManager.getById(posCashTable.getTableId());
        if (countDown) {
            // 设置倒计时 （分钟）
            if (null == posCashTable.getRemindTime()) {
                posCashTable.setRemindTime(LocalDateTime.now());
            }
            LocalDateTime newTime = posCashTable.getRemindTime().plusMinutes(countDownMinute);
            posCashTable.setRemindTime(newTime);
        }
        if (time && null != posCashTable.getEndTime()) {
            // 减少/增加时间
            LocalDateTime newTime = posCashTable.getEndTime().plusMinutes(timeMinute);
            posCashTable.setEndTime(newTime);
            long startMinutes = DateUtils.calDifMinutes(posCashTable.getStartTime(), newTime);
            posCashTable.setDuration(Integer.valueOf(startMinutes + ""));
            BaseTableCharging charging = chargingManager.getOne(Wraps.<BaseTableCharging>lbQ().eq(BaseTableCharging::getTableType, tableInfo.getTableType()));

            PosCashTableCash posCashTableCash = BeanUtil.copyProperties(posCashTable, PosCashTableCash.class);
            AppTableCharingResultVo chargingResultVO = BeanUtil.copyProperties(charging, AppTableCharingResultVo.class);
            calAmount(posCashTableCash, null, chargingResultVO, null != posCash.getMemberId());

            BigDecimal orginPrice = posCashTableCash.getAmount();
            BigDecimal amount = orginPrice;
            if (!"".equals(posCashTableCash.getType())) {
                if ("6".equals(posCashTableCash.getType())) {
                    long dicountTime = posCashTableCash.getDiscount().setScale(0, RoundingMode.DOWN).longValue();
                    amount = calculateBizService.orginPriceCountAmountByType6(posCashTableCash.getPrice(), posCashTableCash.getDuration().longValue(), dicountTime, charging.getPeriod(), charging.getOvertime());
                } else {
                    amount = calculateBizService.orginPriceCountAmount(posCashTableCash.getAmount(), posCashTableCash.getType(), posCashTableCash.getDiscount());
                }
            }

            posCashTable.setOrginPrice(orginPrice);
            posCashTable.setAmount(amount);
        }
        if (stop) {
            // 计算时长 并且计算金额 并且 设置停止
            posCashTable.setStatus("1");
            posCashTable.setEndTime(LocalDateTime.now());
            long startMinutes = DateUtils.calDifMinutes(posCashTable.getStartTime(), posCashTable.getEndTime());
            posCashTable.setDuration(Integer.valueOf(startMinutes + ""));

            BigDecimal orginPrice = new BigDecimal(0);
            BigDecimal amount = new BigDecimal(0);
            if (0 != startMinutes) {
                BaseTableCharging charging = chargingManager.getOne(Wraps.<BaseTableCharging>lbQ().eq(BaseTableCharging::getTableType, tableInfo.getTableType()));

                PosCashTableCash posCashTableCash = BeanUtil.copyProperties(posCashTable, PosCashTableCash.class);
                AppTableCharingResultVo chargingResultVO = BeanUtil.copyProperties(charging, AppTableCharingResultVo.class);
                calAmount(posCashTableCash, null, chargingResultVO, null != posCash.getMemberId());

                orginPrice = posCashTableCash.getAmount();
                amount = orginPrice;
                if (!"".equals(posCashTableCash.getType())) {
                    if ("6".equals(posCashTableCash.getType())) {
                        long dicountTime = posCashTableCash.getDiscount().setScale(0, RoundingMode.DOWN).longValue();
                        amount = calculateBizService.orginPriceCountAmountByType6(posCashTableCash.getPrice(), posCashTableCash.getDuration().longValue(), dicountTime, charging.getPeriod(), charging.getOvertime());
                    } else {
                        amount = calculateBizService.orginPriceCountAmount(posCashTableCash.getAmount(), posCashTableCash.getType(), posCashTableCash.getDiscount());
                    }
                }
            }


            posCashTable.setOrginPrice(orginPrice);
            posCashTable.setAmount(amount);
        }
        // 更新
        boolean suc = posCashTableManager.updateById(posCashTable);
        if (!suc) {
            return "更新失败";
        }
        return null;
    }

    @Override
    public List<AppServiceResultVO> queryService(BaseServicePageQuery query) {
        // 获取服务列表
        List<AppServiceMemberResultVO> resultVOS = serviceMapper.queryService(query);
        // 组装展示内容
        Map<String, List<AppServiceMemberResultVO>> memberResultMap = resultVOS.stream().collect(Collectors.groupingBy(e -> e.getServiceName(), LinkedHashMap::new, Collectors.toList()));
        List<AppServiceResultVO> serviceResultVOList = new ArrayList<>();
        for (String key : memberResultMap.keySet()) {
            AppServiceResultVO service = new AppServiceResultVO();
            service.setMemberResultVOS(memberResultMap.get(key));
//            String[] keys = key.split(",");
//            service.setCategoryId(Long.parseLong(keys[0]));
            service.setCategoryName(key);
            serviceResultVOList.add(service);
        }

        return serviceResultVOList;
    }

    @Override
    public List<BaseServiceCategoryResultVO> queryServiceCategory(BaseServicePageQuery query) {
        List<BaseServiceCategory> categories = baseServiceCategoryManager.list(Wraps.<BaseServiceCategory>lbQ().eq(BaseServiceCategory::getState, "1").eq(BaseServiceCategory::getParentId, 0).orderByAsc(BaseServiceCategory::getSortValue));
        return BeanUtil.copyToList(categories, BaseServiceCategoryResultVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveService(PosCashServiceSaveVO saveVO) {
        // 查询主表
        PosCash posCash = posCashManager.getById(saveVO.getCashId());
        // 服务表
        BaseService baseService = baseServiceManager.getById(saveVO.getServiceId());

        // 计算价格
        BigDecimal price = calculateBizService.calServiceAmount(baseService, posCash.getMemberId());

        PosCashService posCashService = BeanUtil.copyProperties(saveVO, PosCashService.class);
        posCashService.setPrice(price);
//        // 计次直接更新总价格，计时需实时计算
//        if (null != baseService.getDuration()) {
//            posCashService.setAmount(price);
//        }
        // 原始价格
        posCashService.setOrginPrice(price);
        posCashService.setStartTime(LocalDateTime.now().withSecond(0).withNano(0));
        posCashService.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        posCashService.setSn(ContextUtil.getSn());
        boolean suc = posCashServiceManager.save(posCashService);
        if (!suc) {
            return "服务添加失败";
        }
        return null;
    }

    @Override
    public String deleteService(PosCashServicePageQuery saveVO) {
        PosCashService posCashService = posCashServiceManager.getById(saveVO.getId());
        posCashServiceManager.removeById(posCashService);
        return "删除成功";
    }

    @Override
    public String saveCashServiceOperate(Map<String, Object> query) {
        String posCashServiceId = (String) query.get("posCashServiceId");
        Boolean countDown = (Boolean) query.get("countDown");
        Integer countDownMinute = (Integer) query.get("countDownMinute");
        Boolean time = (Boolean) query.get("time");
        Integer timeMinute = (Integer) query.get("timeMinute");
        Boolean stop = (Boolean) query.get("stop");
        // poscashTable详情
        PosCashService posCashService = posCashServiceManager.getById(posCashServiceId);
        BigDecimal price = posCashService.getPrice();
        PosCash posCash = posCashManager.getById(posCashService.getCashId());
        BaseService baseService = baseServiceManager.getById(posCashService.getServiceId());

        // 计时服务更新
//        if (null == baseService.getDuration()) {
        if (countDown) {
            LocalDateTime remindTime = null == posCashService.getRemindTime() ? LocalDateTime.now() : posCashService.getRemindTime();
            // 设置倒计时 （分钟）
            LocalDateTime newTime = remindTime.plusMinutes(countDownMinute);
            posCashService.setRemindTime(newTime);
        }
        if (time && null != posCashService.getEndTime()) {
            // 减少/增加时间
            LocalDateTime newTime = posCashService.getEndTime().plusMinutes(timeMinute);
            posCashService.setEndTime(newTime);
            Long startMinutes = DateUtils.calDifMinutes(posCashService.getStartTime(), newTime);
            posCashService.setDuration(Integer.valueOf(startMinutes + ""));
            // 计算金额
            // 计算总价
//                BigDecimal amount = calServiceAmount(startMinutes.intValue(), baseService.getDuration(), price);
            BigDecimal orginPrice = calculateBizService.overTimeCash(price, startMinutes, baseService.getBillingCycle(), baseService.getTimeoutPeriod());
            BigDecimal amount = orginPrice;
            if (!"".equals(posCashService.getType())) {
                if ("6".equals(posCashService.getType())) {
                    long dicountTime = posCashService.getDiscount().setScale(0, RoundingMode.DOWN).longValue();
                    amount = calculateBizService.orginPriceCountAmountByType6(posCashService.getPrice(), posCashService.getDuration().longValue(), dicountTime, baseService.getBillingCycle(), baseService.getTimeoutPeriod());
                } else {
                    amount = calculateBizService.orginPriceCountAmount(orginPrice, posCashService.getType(), posCashService.getDiscount());
                }
            }

            posCashService.setOrginPrice(orginPrice);
            posCashService.setAmount(amount);
        }
        if (stop) {
            // 计算时长 并且计算金额 并且 设置停止
            posCashService.setEndTime(LocalDateTime.now());
            Long startMinutes = DateUtils.calDifMinutes(posCashService.getStartTime(), posCashService.getEndTime());
            posCashService.setDuration(Integer.valueOf(startMinutes + ""));

            BigDecimal orginPrice = new BigDecimal(0);
            BigDecimal amount = new BigDecimal(0);
            if (0 != startMinutes) {
                // 计算金额
                // 计算总价
//                BigDecimal amount = calServiceAmount(startMinutes.intValue(), baseService.getDuration(), price);
                orginPrice = calculateBizService.overTimeCash(price, startMinutes, baseService.getBillingCycle(), baseService.getTimeoutPeriod());
                amount = orginPrice;
                if (!"".equals(posCashService.getType())) {
                    if ("6".equals(posCashService.getType())) {
                        long dicountTime = posCashService.getDiscount().setScale(0, RoundingMode.DOWN).longValue();
                        amount = calculateBizService.orginPriceCountAmountByType6(posCashService.getPrice(), posCashService.getDuration().longValue(), dicountTime, baseService.getBillingCycle(), baseService.getTimeoutPeriod());
                    } else {
                        amount = calculateBizService.orginPriceCountAmount(orginPrice, posCashService.getType(), posCashService.getDiscount());
                    }
                }
            }

            posCashService.setOrginPrice(orginPrice);
            posCashService.setAmount(amount);
        }
        // 更新
        boolean suc = posCashServiceManager.updateById(posCashService);
        if (!suc) {
            return "更新失败";
        }
//        }

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String saveStopAll(PosCashPageQuery query) {
        PosCash posCash = posCashManager.getById(query.getId());
        // 1. 查询未停止的台桌
        List<PosCashTable> posCashTables = posCashTableManager.list(Wraps.<PosCashTable>lbQ().isNull(PosCashTable::getEndTime));
        if (null != posCashTables && 0 != posCashTables.size()) {
            BaseTableInfo tableInfo = baseTableInfoManager.getById(posCash.getTableId());
            for (PosCashTable posCashTable : posCashTables) {
                // 计算金额和时间
//                posCashTable.setStatus("1");
                posCashTable.setEndTime(LocalDateTime.now());
                long startMinutes = DateUtils.calDifMinutes(posCashTable.getStartTime(), posCashTable.getEndTime());
                posCashTable.setDuration(Integer.valueOf(startMinutes + ""));

                BigDecimal orginPrice = new BigDecimal(0);
                BigDecimal amount = new BigDecimal(0);
                if (0 != startMinutes) {
                    BaseTableCharging charging = chargingManager.getOne(Wraps.<BaseTableCharging>lbQ().eq(BaseTableCharging::getTableType, tableInfo.getTableType()));

                    PosCashTableCash posCashTableCash = BeanUtil.copyProperties(posCashTable, PosCashTableCash.class);
                    AppTableCharingResultVo chargingResultVO = BeanUtil.copyProperties(charging, AppTableCharingResultVo.class);
                    calAmount(posCashTableCash, null, chargingResultVO, null != posCash.getMemberId());
                    orginPrice = posCashTableCash.getAmount();
                    amount = orginPrice;
                    if (!"".equals(posCashTableCash.getType())) {
                        if ("6".equals(posCashTableCash.getType())) {
                            long dicountTime = posCashTableCash.getDiscount().setScale(0, RoundingMode.DOWN).longValue();
                            amount = calculateBizService.orginPriceCountAmountByType6(posCashTableCash.getPrice(), posCashTableCash.getDuration().longValue(), dicountTime, charging.getPeriod(), charging.getOvertime());
                        } else {
                            amount = calculateBizService.orginPriceCountAmount(orginPrice, posCashTableCash.getType(), posCashTableCash.getDiscount());
                        }
                    }
                }

                posCashTable.setOrginPrice(orginPrice);
                posCashTable.setAmount(amount);
                posCashTable.setStatus("1");
            }
            boolean suc = posCashTableManager.updateBatchById(posCashTables);
            if (!suc) {
                return "台桌费用更新失败";
            }
        }
        // 2. 查询未停止的服务
        List<PosCashService> posCashServices = posCashServiceManager.list(Wraps.<PosCashService>lbQ().isNull(PosCashService::getEndTime));
        List<Long> serviceIds = posCashServices.stream().map(PosCashService::getServiceId).collect(Collectors.toList());
        List<BaseService> baseServices = baseServiceManager.list(Wraps.<BaseService>lbQ().in(BaseService::getId, serviceIds));
        Map<Long, BaseService> serviceMap = baseServices.stream().collect(Collectors.toMap(BaseService::getId, e -> e));
        if (null != posCashServices && 0 != posCashServices.size()) {
            // 计算时长 并且计算金额 并且 设置停止
            for (PosCashService posCashService : posCashServices) {
                BaseService baseService = serviceMap.get(posCashService.getServiceId());
                posCashService.setEndTime(LocalDateTime.now());
                Long startMinutes = DateUtils.calDifMinutes(posCashService.getStartTime(), posCashService.getEndTime());
                posCashService.setDuration(Integer.valueOf(startMinutes + ""));
                // 计算金额
                BigDecimal orginPrice = new BigDecimal(0);
                BigDecimal amount = new BigDecimal(0);
                if (0 != startMinutes) {
                    orginPrice = calculateBizService.overTimeCash(posCashService.getPrice(), startMinutes, baseService.getBillingCycle(), baseService.getTimeoutPeriod());
                    amount = orginPrice;
                    if (!"".equals(posCashService.getType())) {
                        if ("6".equals(posCashService.getType())) {
                            long dicountTime = posCashService.getDiscount().setScale(0, RoundingMode.DOWN).longValue();
                            amount = calculateBizService.orginPriceCountAmountByType6(posCashService.getPrice(), posCashService.getDuration().longValue(), dicountTime, baseService.getBillingCycle(), baseService.getTimeoutPeriod());
                        } else {
                            amount = calculateBizService.orginPriceCountAmount(orginPrice, posCashService.getType(), posCashService.getDiscount());
                        }
                    }
                }

                posCashService.setOrginPrice(orginPrice);
                posCashService.setAmount(amount);

            }
            boolean suc = posCashServiceManager.updateBatchById(posCashServices);
            if (!suc) {
                throw new BizException("服务费用更新失败");
            }
        }
        // 全部停止后计算总金额
        Map<String, Object> param = new HashMap<>();
        param.put("tableId", posCash.getTableId());
        param.put("cashId", posCash.getId());
        param.put("needUpdate", true);
        LinkedHashMap<String, Object> posResult = posCashServiceService.tablePosCash(param);
        PosCashAmountVO totalVo = (PosCashAmountVO) posResult.get("totalCash");
        posCash.setAmount(totalVo.getAmount());
        posCash.setDiscountAmount(totalVo.getCouponAmount());
        posCash.setPayment(totalVo.getResultAmount());
        posCash.setUpdatedBy(ContextUtil.getUserId());
        posCash.setUpdatedTime(LocalDateTime.now());
        boolean suc = posCashManager.updateById(posCash);
        if (!suc) {
            throw new BizException("金额更新失败");
        }
        // 计算总金额 和 折扣金额 TODO
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String pendingOrder(PosCashPageQuery query) {
        PosCash posCash = posCashManager.getById(query.getId());
        // 1. 查询未停止的台桌
        List<PosCashTable> posCashTables = posCashTableManager.list(Wraps.<PosCashTable>lbQ().isNull(PosCashTable::getEndTime));
        if (null != posCashTables && 0 != posCashTables.size()) {
            BaseTableInfo tableInfo = baseTableInfoManager.getById(posCash.getTableId());
            for (PosCashTable posCashTable : posCashTables) {
                // 计算金额和时间
                posCashTable.setStatus("1");
                posCashTable.setEndTime(LocalDateTime.now());
                long startMinutes = DateUtils.calDifMinutes(posCashTable.getStartTime(), posCashTable.getEndTime());
                posCashTable.setDuration(Integer.valueOf(startMinutes + ""));
                BaseTableCharging charging = chargingManager.getOne(Wraps.<BaseTableCharging>lbQ().eq(BaseTableCharging::getTableType, tableInfo.getTableType()));

                PosCashTableCash posCashTableCash = BeanUtil.copyProperties(posCashTable, PosCashTableCash.class);
                AppTableCharingResultVo chargingResultVO = BeanUtil.copyProperties(charging, AppTableCharingResultVo.class);
                calAmount(posCashTableCash, null, chargingResultVO, null != posCash.getMemberId());
                BigDecimal orginPrice = posCashTableCash.getAmount();
                BigDecimal amount = orginPrice;
                if (!"".equals(posCashTableCash.getType())) {
                    if ("6".equals(posCashTableCash.getType())) {
                        long dicountTime = posCashTableCash.getDiscount().setScale(0, RoundingMode.DOWN).longValue();
                        amount = calculateBizService.orginPriceCountAmountByType6(posCashTableCash.getPrice(), posCashTableCash.getDuration().longValue(), dicountTime, charging.getPeriod(), charging.getOvertime());
                    } else {
                        amount = calculateBizService.orginPriceCountAmount(orginPrice, posCashTableCash.getType(), posCashTableCash.getDiscount());
                    }
                }
                posCashTable.setOrginPrice(posCashTableCash.getAmount());
                posCashTable.setAmount(amount);
                posCashTable.setStatus("1");
            }
            boolean suc = posCashTableManager.updateBatchById(posCashTables);
            if (!suc) {
                return "台桌费用更新失败";
            }
        }
        // 2. 查询未停止的服务
        List<PosCashService> posCashServices = posCashServiceManager.list(Wraps.<PosCashService>lbQ().isNull(PosCashService::getEndTime));
        if (null != posCashServices && 0 != posCashServices.size()) {
            BaseService baseService = baseServiceManager.getById(posCashServices.get(0).getServiceId());
            // 计算时长 并且计算金额 并且 设置停止
            for (PosCashService posCashService : posCashServices) {
                posCashService.setEndTime(LocalDateTime.now());
                Long startMinutes = DateUtils.calDifMinutes(posCashService.getStartTime(), posCashService.getEndTime());
                posCashService.setDuration(Integer.valueOf(startMinutes + ""));
                // 计算金额
                // 获取单价
//                BigDecimal price = calculateBizService.calServiceAmount(baseService, posCash.getMemberId());
                // 计算总价
                BigDecimal orginPrice = calculateBizService.overTimeCash(posCashService.getPrice(), startMinutes, baseService.getBillingCycle(), baseService.getTimeoutPeriod());
                BigDecimal amount = orginPrice;
                if (!"".equals(posCashService.getType())) {
                    if ("6".equals(posCashService.getType())) {
                        long dicountTime = posCashService.getDiscount().setScale(0, RoundingMode.DOWN).longValue();
                        amount = calculateBizService.orginPriceCountAmountByType6(posCashService.getPrice(), posCashService.getDuration().longValue(), dicountTime, baseService.getBillingCycle(), baseService.getTimeoutPeriod());
                    } else {
                        amount = calculateBizService.orginPriceCountAmount(orginPrice, posCashService.getType(), posCashService.getDiscount());
                    }
                }
                posCashService.setOrginPrice(orginPrice);
                posCashService.setAmount(amount);
            }
            boolean suc = posCashServiceManager.updateBatchById(posCashServices);
            if (!suc) {
                throw new BizException("服务费用更新失败");
            }
        }

        // 3. 更新poscash状态为挂单
        posCash.setBillState("1");
        posCash.setUpdatedTime(LocalDateTime.now());
        // 计算总金额 和 折扣金额 TODO
        boolean suc = posCashManager.updateById(posCash);
        if (!suc) {
            throw new BizException("订单更新失败");
        }
        if (!ObjectUtil.equal(posCash.getType(), PosCashTypeEnum.SHOPPING.getCode())) {
            // 更新放开台桌状态
            BaseTableInfo baseTableInfo = baseTableInfoManager.getById(posCash.getTableId());
            if (ObjectUtil.isNotNull(baseTableInfo)) {
                baseTableInfo.setTableStatus(TableStatus.UNUSED.getCode());
                suc = baseTableInfoManager.updateById(baseTableInfo);
                if (!suc) {
                    throw new BizException("台桌状态更新失败");
                }
            }
        }
        return null;
    }

    @Override
    public String saveChangeLight(BaseTableInfoPageQuery query) {
        BaseTableInfo baseTableInfo = baseTableInfoManager.getById(query.getId());
        if (!StringUtils.isEmpty(query.getLightStatus())) {
            baseTableInfo.setLightStatus(query.getLightStatus());
        } else {
            baseTableInfo.setLightStatus(null == baseTableInfo.getLightStatus() || "0".equals(baseTableInfo.getLightStatus()) ? "1" : "0");
        }
        baseTableInfoManager.updateById(baseTableInfo);
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String doChangeProductCount(PosCashProductPageQuery saveVO) {
        PosCashProduct posCashProduct = posCashProductManager.getById(saveVO.getId());
        int preNum = posCashProduct.getNum();
        // 判断库存
        long productId = posCashProduct.getProductId();
        BaseProduct baseProduct = baseProductManager.getById(productId);
        BaseProductStock baseProductStock = baseProductStockManager.getOne(Wraps.<BaseProductStock>lbQ().eq(BaseProductStock::getProductId, posCashProduct.getProductId()));
        if (null != baseProductStock && 0 > baseProductStock.getNum() - saveVO.getNum() && !baseProduct.getIsNegative()) {
            return "该商品库存不足";
        }
        //重新计算金额
        BigDecimal orginPrice = posCashProduct.getPrice().multiply(new BigDecimal(saveVO.getNum()));
        BigDecimal amount = calculateBizService.orginPriceCountAmount(orginPrice, posCashProduct.getType(), posCashProduct.getDiscount());

        posCashProduct.setOrginPrice(orginPrice);
        posCashProduct.setAmount(amount);
        posCashProduct.setNum(saveVO.getNum());
        boolean suc = posCashProductManager.updateById(posCashProduct);
        if (suc) {
            // 减少库存
            int stock = saveVO.getNum() - preNum;
            // 更新 base_product_stock
            if (null != baseProductStock) {
                baseProductStock.setNum(baseProductStock.getNum() - stock);
                suc = baseProductStockManager.updateById(baseProductStock);
                if (!suc) {
                    throw new BizException("库存更新失败");
                }
            }

        } else {
            return "商品数量更新失败";
        }
        return null;
    }

    @Override
    public List<MemberInfoResultVO> queryMemberByPhone(Map<String, Object> query) {
        String phone = (String) query.get("phoneNum");
        List<MemberInfo> memberInfo = memberInfoManager.list(Wraps.<MemberInfo>lbQ().eq(MemberInfo::getMobile, phone));
        if (null == memberInfo || 0 == memberInfo.size()) {
            return null;
        }
        List<MemberInfoResultVO> resultVO = BeanUtil.copyToList(memberInfo, MemberInfoResultVO.class);
        echoService.action(resultVO);
        return resultVO;
    }

    @Override
    @DS(DsConstant.BASE_TENANT)
    public String reflushTable(BaseTableInfoPageQuery query) {
        // 获取所有台桌
        BaseTableInfo baseTableInfo = superManager.getById(query.getId());
        List<AppTableInfoResultVo> appTableInfoResultVos = new ArrayList<>();
        AppTableInfoResultVo appTableInfoResultVo = BeanUtil.copyProperties(baseTableInfo, AppTableInfoResultVo.class);
        appTableInfoResultVos.add(appTableInfoResultVo);
        // 获取台桌的计费规则（当前时间段）
        List<BaseTableCharging> chargings = chargingManager.list(Wraps.<BaseTableCharging>lbQ().isNotNull(BaseTableCharging::getTableType));
        List<AppTableCharingResultVo> chargingResultVOS = BeanUtil.copyToList(chargings, AppTableCharingResultVo.class);

        // 获取台桌计费配置
        List<BaseTableChargingSetting> settings = chargingSettingManager.list(Wraps.<BaseTableChargingSetting>lbQ().orderByAsc(BaseTableChargingSetting::getChargingId, BaseTableChargingSetting::getStartTime));
        List<AppTableChargingSettingResultVO> settingResultVOS = BeanUtil.copyToList(settings, AppTableChargingSettingResultVO.class);
        Map<Long, List<AppTableChargingSettingResultVO>> settingsMap = settingResultVOS.stream().collect(Collectors.groupingBy(e -> e.getChargingId(), LinkedHashMap::new, Collectors.toList()));
        chargingResultVOS.forEach(e -> {
            e.setSettingList(settingsMap.get(e.getId()));
        });

        // 获取台桌是否有会员
        List<PosCashTableCash> posCashMembers = posCashTableMapper.queryStartTablesWithMember();
        Map<Long, PosCashTableCash> posCashMemberMap = posCashMembers.stream().collect(Collectors.toMap(PosCashTableCash::getTableId, e -> e));

        // 组装台桌信息
        Map<String, AppTableCharingResultVo> chargingMap = chargingResultVOS.stream().collect(Collectors.toMap(AppTableCharingResultVo::getTableType, e -> e));

        for (AppTableInfoResultVo e : appTableInfoResultVos) {
            // 计价设置
            AppTableCharingResultVo charingResultVo = chargingMap.get(e.getTableType());
            if (null != charingResultVo) {
                e.setCharingResultVo(charingResultVo);
                // 计算当前时间段的单价
                List<AppTableChargingSettingResultVO> settingList = charingResultVo.getSettingList();
                for (AppTableChargingSettingResultVO vo : settingList) {
                    if (DateUtils.dateCompare(vo.getStartTime(), new Date()) <= 0 && DateUtils.dateCompare(vo.getEndTime(), new Date()) >= 0) {
                        e.setUnitPrice(vo.getMemberPrice());
                        break;
                    }
                }
            }
            // 是否有会员
            e.setMemberId(null == posCashMemberMap.get(e.getId()) ? null : posCashMemberMap.get(e.getId()).getMemberId());
            e.setMemberName(null == posCashMemberMap.get(e.getId()) ? null : posCashMemberMap.get(e.getId()).getMemberName());
        }
        // 刷新台桌
        refreshTables(appTableInfoResultVos);
        return null;
    }

    private BigDecimal calServiceAmount(int startMinutes, int duration, BigDecimal price) {
        BigDecimal keepMin = new BigDecimal(startMinutes).divide(new BigDecimal(duration), 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal amount = keepMin.multiply(price).setScale(2, BigDecimal.ROUND_HALF_UP);
        return amount;
    }

    private void refreshTables(List<AppTableInfoResultVo> appTableInfoResultVos) {
        long cashId = 0L;
        // 获取所有计时中的cash_table
        List<PosCashTableCash> posCashTables = posCashTableMapper.queryStartTables();
        Map<Long, List<PosCashTableCash>> posCashTableMap = posCashTables.stream().collect(Collectors.groupingBy(PosCashTableCash::getTblId, LinkedHashMap::new, Collectors.toList()));
        // 获取已开台的开台时间
        for (AppTableInfoResultVo vo : appTableInfoResultVos) {
            // 计费中的计时
            if (null == vo.getCharingResultVo()) {
                continue;
            }
            if (TableStatus.USING.getCode().equals(vo.getTableStatus())) {
                // 1. 计算正向、逆向时长
                // key:tableId  获取所有已保存的cash_table数据
                List<PosCashTableCash> cashTables = posCashTableMap.get(vo.getId());
                if (null == cashTables) {
                    continue;
                }
                cashId = cashTables.get(0).getCashId();
                // 判断台桌是否有停止的数据 TODO
                PosCashTable stopTable = cashTables.stream().filter(e -> null != e.getStatus() && "1".equals(e.getStatus())).findFirst().orElse(null);

                if (null != cashTables) {
                    PosCashTableCash posCashTable = cashTables.get(0);
                    // 计算正向时长 单位分钟
                    Long startMinutes = DateUtils.calDifMinutes(posCashTable.getStartTime(), LocalDateTime.now());
                    vo.setStartMinutes(startMinutes);
                    // 计算提醒的逆向计时
                    if (null != posCashTable.getRemindTime()) {
                        Long countdownMinutes = DateUtils.calDifMinutes(LocalDateTime.now(), posCashTable.getRemindTime());
                        vo.setCountdownMinutes(countdownMinutes);
                        // 倒计时结束 提醒
                        vo.setCountdownEnding(countdownMinutes <= 0);
                    }
                }
                // 2. 计算拆单 -》 cash_table
                List<AppTableChargingSettingResultVO> settings = vo.getCharingResultVo().getSettingList();
                AppTableChargingSettingResultVO nowSettings = null;
                // 2.1 遍历获取当前时间段对应的计费规则设置
                AppTableChargingSettingResultVO preSettings = null;

                // 组装cash_table 的 map
//                Map<Long, PosCashTableCash> posCashTableCashMap = cashTables.stream().collect(Collectors.toMap(PosCashTableCash::getChargingSettingId, e -> e));

                // ======================================  分割线 ==================================
                // 以下代码为 没有定时任务时，不全今日的cash_table
                LocalDateTime startTime = cashTables.get(0).getStartTime();
                LocalDateTime nowDateTime = LocalDateTime.now();
                // 计算开始到现在一共多少天
                long difDay = DateUtils.getDatesBetween(startTime.toLocalDate(), nowDateTime.toLocalDate()).size() - 1;
//                long difDay = DateUtils.calDifDay(startTime, nowDateTime);

                // 2.2 判断开台时间是否为当天
//                String startYmd = startTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//                String nowYmd = nowDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//                LocalTime todayStart = null;
//                if(startYmd.equals(nowYmd)) {
//                    // 今日开台  开始时间为开台的时间
//                    todayStart = startTime.toLocalTime();
//                } else {
//                    // 不是今日开台  开始时间为第一个计时周期开始时间
//                    todayStart = LocalTime.parse(settings.get(0).getStartTime() + ":00");
//                }
                LocalTime nowTime = nowDateTime.toLocalTime();
                // 遍历settings 获取今日所有的计时周期
                List<AppTableChargingSettingResultVO> todaySettings = new ArrayList<>();
                for (int i = 0; i <= difDay; i++) {
                    // 遍历开台到今日的所有天数
                    LocalTime loopDayStart = null;
                    // 当前loop的时间
                    LocalDateTime loopNowDay = startTime.plusDays(i);

                    // 遍历settings 将从开台 到  现在 所有的时间都组装到集合中
                    for (AppTableChargingSettingResultVO set : settings) {
                        if (0 == i) {
                            // 开台当天 从startTime开始计算
                            loopDayStart = startTime.toLocalTime();
                        } else {
                            loopDayStart = LocalTime.parse(settings.get(0).getStartTime() + ":00");
                        }
//                        if(nowTime.compareTo(loopDayStart) < 0){
//                            // 只计算当前时间 >= 开始时间的 setting数据
//                            continue;
//                        }
                        AppTableChargingSettingResultVO todaySet = BeanUtil.copyProperties(set, AppTableChargingSettingResultVO.class);
                        // 设置当前天 的 当前时间段start和end   yyyy-MM-dd HH:mm
                        todaySet.setIsNow(false);
                        todaySet.setStart(loopNowDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " " + set.getStartTime() + ":00");
                        todaySet.setEnd(loopNowDay.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + " " + set.getEndTime() + ":00");
                        DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                        if (LocalDateTime.parse(todaySet.getEnd(), df).compareTo(startTime) < 0 && LocalDateTime.parse(todaySet.getStart(), df).compareTo(startTime) < 0) {
                            // 配置表的开始时间 小于 cash的开始时间 跳过
                            continue;
                        }
                        if (i == difDay) {
                            // 最后一天
                            String endTime = set.getEndTime() + ":00";
                            if ("24:00".equals(set.getEndTime())) {
                                endTime = "23:59:59";
                            }
                            if (nowTime.compareTo(LocalTime.parse(endTime)) <= 0 && nowTime.compareTo(LocalTime.parse(set.getStartTime() + ":00")) >= 0) {
                                // 当前时间在结算周期内了，break
                                todaySet.setIsNow(true);
                                todaySettings.add(todaySet);
                                break;
                            }
                        }
                        todaySettings.add(todaySet);
                    }
                }
                if (null != stopTable) {
                    // 有停止的状态  就不要再计算了  TODO
                    continue;
                }
                // 获取会员折扣
                // 会员的时候，获取会员的折扣权益
                Map<String, Object> memberDiscount = null;
                if (null != vo.getMemberId()) {
                    memberDiscount = memberMapper.getMemberDiscount(vo.getMemberId(), "1");
                }
                List<PosCashTable> updList = new ArrayList<>();
                List<PosCashTable> addList = new ArrayList<>();
                // 遍历todaySetting
                for (AppTableChargingSettingResultVO todaySet : todaySettings) {
                    // 获取poscashtable中开始时间在  settings的 start - end 中间的数据
                    DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                    LocalDateTime start = LocalDateTime.parse(todaySet.getStart(), df);
                    LocalDateTime end = LocalDateTime.parse(todaySet.getEnd(), df);
                    PosCashTableCash pct = cashTables.stream().filter(e -> e.getStartTime().compareTo(start) >= 0 && e.getStartTime().compareTo(end) <= 0).findFirst().orElse(null);
                    if (null == pct) {
                        // 如果取不到这个时间范围内的cash
                        // 新增一条cash_table
                        PosCashTableCash posCashTable = new PosCashTableCash();
                        // cashId
                        posCashTable.setCashId(cashTables.get(0).getCashId());
                        // 台桌Id
                        posCashTable.setTableId(cashTables.get(0).getTableId());
                        // 开始时间
                        posCashTable.setStartTime(start.withSecond(0).withNano(0));

                        // 获取当前时间段单价
                        posCashTable.setChargingSettingId(todaySet.getId());
                        // 单价
                        BigDecimal price = calculateBizService.calTableAmount(todaySet, vo.getMemberId(), memberDiscount);
                        posCashTable.setPrice(price);
                        // 原始价格，orginPrice变为原始整单价格
//                        posCashTable.setOrginPrice(todaySet.getCustomerPrice());
//                        if (null != vo.getMemberId()) {
//                            // 会员时使用会员价格
//                            posCashTable.setPrice(todaySet.getMemberPrice());
//                        } else {
//                            // 非会员使用散客价格
//                            posCashTable.setPrice(todaySet.getCustomerPrice());
//                        }
                        // 提醒时间
                        posCashTable.setRemindTime(cashTables.get(0).getRemindTime());
                        // 结束时间
                        if (!todaySet.getIsNow()) {
                            posCashTable.setEndTime(end);
                            // 计算价格
                            calAmount(posCashTable, todaySet, vo.getCharingResultVo(), null != vo.getMemberId());
                        } else {
                            calAmount(posCashTable, todaySet, vo.getCharingResultVo(), null != vo.getMemberId());
                        }
                        BigDecimal orginPrice = posCashTable.getAmount();
                        BigDecimal amount = orginPrice;
                        if (!"".equals(posCashTable.getType())) {
                            if ("6".equals(posCashTable.getType())) {
                                long dicountTime = posCashTable.getDiscount().setScale(0, RoundingMode.DOWN).longValue();
                                amount = calculateBizService.orginPriceCountAmountByType6(posCashTable.getPrice(), posCashTable.getDuration().longValue(), dicountTime, vo.getCharingResultVo().getPeriod(), vo.getCharingResultVo().getOvertime());
                            } else {
                                amount = calculateBizService.orginPriceCountAmount(orginPrice, posCashTable.getType(), posCashTable.getDiscount());
                            }
                        }

                        posCashTable.setOrginPrice(orginPrice);
                        posCashTable.setAmount(amount);

                        PosCashTable prePosCashTable = new PosCashTable();
                        BeanUtils.copyProperties(posCashTable, prePosCashTable);
                        prePosCashTable.setSn(ContextUtil.getSn());
                        addList.add(prePosCashTable);
                    } else {
                        // 能取到cash
                        if (null == pct.getEndTime() && !todaySet.getIsNow()) {
                            // 不是当前时间 并且结束时间为空
                            // 更新当前记录 并计算价格
                            pct.setEndTime(end);
                            calAmount(pct, todaySet, vo.getCharingResultVo(), null != vo.getMemberId());
                            BigDecimal orginPrice = pct.getAmount();
                            BigDecimal amount = orginPrice;
                            if (!"".equals(pct.getType())) {
                                if ("6".equals(pct.getType())) {
                                    long dicountTime = pct.getDiscount().setScale(0, RoundingMode.DOWN).longValue();
                                    amount = calculateBizService.orginPriceCountAmountByType6(pct.getPrice(), pct.getDuration().longValue(), dicountTime, vo.getCharingResultVo().getPeriod(), vo.getCharingResultVo().getOvertime());
                                } else {
                                    amount = calculateBizService.orginPriceCountAmount(orginPrice, pct.getType(), pct.getDiscount());
                                }
                            }

                            pct.setOrginPrice(orginPrice);
                            pct.setAmount(amount);
                            // 计算时长
                            long startMinutes = DateUtils.calDifMinutes(pct.getStartTime(), end);
                            pct.setDuration(Integer.valueOf(startMinutes + ""));

                            PosCashTable prePosCashTable = new PosCashTable();
                            BeanUtils.copyProperties(pct, prePosCashTable);
                            updList.add(pct);
                        }
                        if (todaySet.getIsNow()) {
                            // 当前日期 只更新金额
                            calAmount(pct, todaySet, vo.getCharingResultVo(), null != vo.getMemberId());

                            BigDecimal orginPrice = pct.getAmount();
                            BigDecimal amount = orginPrice;
                            if (!"".equals(pct.getType())) {
                                if ("6".equals(pct.getType())) {
                                    long dicountTime = pct.getDiscount().setScale(0, RoundingMode.DOWN).longValue();
                                    amount = calculateBizService.orginPriceCountAmountByType6(pct.getPrice(), pct.getDuration().longValue(), dicountTime, vo.getCharingResultVo().getPeriod(), vo.getCharingResultVo().getOvertime());
                                } else {
                                    amount = calculateBizService.orginPriceCountAmount(orginPrice, pct.getType(), pct.getDiscount());
                                }
                            }

                            pct.setOrginPrice(orginPrice);
                            pct.setAmount(amount);
                            // 计算时长
                            long startMinutes = DateUtils.calDifMinutes(pct.getStartTime(), end);
                            pct.setDuration(Integer.valueOf(startMinutes + ""));
                            PosCashTable prePosCashTable = new PosCashTable();
                            BeanUtils.copyProperties(pct, prePosCashTable);
                            updList.add(pct);
                        }
                    }
                }
                if (0 != updList.size()) {
                    posCashTableManager.updateBatchById(updList);
                }
                if (0 != addList.size()) {
                    posCashTableManager.saveBatch(addList);
                }

                // 以下代码为 如果有定时任务 可用 TODO
//                int cnt = 0;
//                for (AppTableChargingSettingResultVO rvo : settings) {
//                    if (DateUtils.dateCompare(rvo.getStartTime(), new Date()) <= 0 && DateUtils.dateCompare(rvo.getEndTime(), new Date()) >= 0) {
//                        nowSettings = rvo;
//                        if(0 == cnt) {
//                            preSettings = settings.get(settings.size() - 1);
//                        } else {
//                            preSettings = settings.get(cnt - 1);
//                        }
//                        break;
//                    }
//                    cnt++;
//                }
//                if (null == nowSettings) {
//                    // 没有当前时段计费规则 continue
//                    continue;
//                }
//                // 2.2 判断cash_table中存不存在当前时间的计费规则id
//                boolean hasSetting = false;
//                for (PosCashTableCash pct : cashTables) {
//                    if (null != pct.getChargingSettingId() && pct.getChargingSettingId().equals(nowSettings.getId())) {
//                        hasSetting = true;
//                        break;
//                    }
//                }
//                if (!hasSetting) {
//                    //  cash_table 加入当前时间段数据
//                    PosCashTable posCashTable = new PosCashTable();
//                    // cashId
//                    posCashTable.setCashId(cashTables.get(0).getCashId());
//                    // 台桌Id
//                    posCashTable.setTableId(cashTables.get(0).getTableId());
//                    // 开始时间
//                    posCashTable.setStartTime(LocalDateTime.now());
//                    // 获取当前时间段单价
//                    posCashTable.setChargingSettingId(nowSettings.getId());
//                    // 单价
//                    if (null != vo.getMemberId()) {
//                        // 会员时使用会员价格
//                        posCashTable.setPrice(nowSettings.getMemberPrice());
//                    } else {
//                        // 非会员使用散客价格
//                        posCashTable.setPrice(nowSettings.getCustomerPrice());
//                    }
//                    // 提醒时间
//                    posCashTable.setRemindTime(cashTables.get(0).getRemindTime());
//                    // 保存
//                    boolean suc = posCashTableManager.save(posCashTable);
//                    if (!suc) {
//                        throw new BizException("开台失败！");
//                    }
//                    // 更新上个时段的cash_table数据
//                    if (null != preSettings) {
//                        final Long preSetId = preSettings.getId();
//                        PosCashTableCash prePct = cashTables.stream().filter(s -> s.getChargingSettingId().equals(preSetId)).findFirst().orElse(null);
//                        if (null != prePct) {
//                            // 计算金额 并 更新
//                            calAmount(prePct, preSettings, vo.getCharingResultVo(), null != vo.getMemberId());
//                            PosCashTable prePosCashTable = new PosCashTable();
//                            BeanUtils.copyProperties(prePct, prePosCashTable);
//                            prePosCashTable.setEndTime(LocalDateTime.now());
//                            posCashTableManager.updateById(prePosCashTable);
//                        }
//                    }
//                }
            }
        }
    }

    private void calAmount(PosCashTableCash pct, AppTableChargingSettingResultVO preSettings, AppTableCharingResultVo charing, boolean isMember) {
        // 计算分钟差
        LocalDateTime end = null == pct.getEndTime() ? LocalDateTime.now() : pct.getEndTime();
        long difMinite = DateUtils.calDifMinutes(pct.getStartTime(), end);
        if (difMinite > charing.getPeriod()) {
            // 超时开始计费
            long ys = difMinite / charing.getPeriod();
            BigDecimal ysAmount = new BigDecimal(ys).multiply(pct.getPrice());
            // 计算超时
            long overMinite = difMinite % charing.getPeriod();
            // 超时周期
            long voerRange = 0L;
            if (0 != overMinite % charing.getOvertime()) {
                voerRange = overMinite / charing.getOvertime() + 1;
            } else {
                voerRange = overMinite / charing.getOvertime();
            }
            // 超时费用
            BigDecimal overAmount = new BigDecimal(voerRange).multiply(pct.getPrice());
            BigDecimal amount = ysAmount.add(overAmount);
//            long calTime = difMinite - Long.parseLong(charing.getOvertime().toString());
//            BigDecimal amount = pct.getPrice().add(new BigDecimal(calTime).divide(new BigDecimal(charing.getPeriod()), 0, BigDecimal.ROUND_HALF_UP).multiply(pct.getPrice()));
            if ("1".equals(charing.getIsCapping())) {
                // 有封顶 TODO 有问题，同一个时间段怎么会存在封顶价格
//                BigDecimal topAmount = isMember ? charing.getMemberCapping() : charing.getCustomerCapping();
//                if(amount.compareTo(topAmount) > 0) {
//                    pct.setAmount(topAmount);
//                } else {
//                    pct.setAmount(amount);
//                }
                pct.setAmount(amount);
            } else {
                pct.setAmount(amount);
            }
        } else if (0 < difMinite && difMinite <= charing.getPeriod()) {
            pct.setAmount(pct.getPrice());
        } else if (0 == difMinite) {
            pct.setAmount(new BigDecimal(0));
        }
    }

    @Override
    public String durationDesc(Integer duration) {
        if (duration == null) {
            return null;
        }
        if (duration == 0) {
            return String.valueOf(duration) + "分钟";
        }
        int hours = (int) Math.floor(duration / 60);
        int minute = duration % 60;
        return (hours == 0 ? "" : String.valueOf(hours) + "小时")
                + (minute == 0 ? "" : String.valueOf(minute) + "分钟");
    }

}
