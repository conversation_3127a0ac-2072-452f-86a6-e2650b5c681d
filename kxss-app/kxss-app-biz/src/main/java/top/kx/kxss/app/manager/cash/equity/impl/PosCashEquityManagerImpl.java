package top.kx.kxss.app.manager.cash.equity.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.equity.PosCashEquity;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.app.manager.cash.equity.PosCashEquityManager;
import top.kx.kxss.app.mapper.cash.equity.PosCashEquityMapper;
import top.kx.kxss.base.vo.NameValueVO;

import java.util.List;

/**
 * <p>
 * 通用业务实现类
 * 结算单消费权益
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-06 18:27:08
 * @create [2023-05-06 18:27:08] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class PosCashEquityManagerImpl extends SuperManagerImpl<PosCashEquityMapper, PosCashEquity> implements PosCashEquityManager {


    @Override
    public List<NameValueVO> consumeTimes(QueryWrapper<PosCash> wrapper) {
        return baseMapper.consumeTimes(wrapper);
    }
}


