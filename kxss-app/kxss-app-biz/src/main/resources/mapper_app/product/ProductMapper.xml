<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.app.mapper.product.ProductMapper">

    <resultMap id="BaseResultMap" type="top.kx.kxss.app.vo.result.product.AppProductResultVo">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="category_id" property="categoryId" />
        <result column="state" property="state" />
        <result column="code" property="code" />
        <result column="measuring_unit" property="measuringUnit" />
        <result column="spec" property="spec" />
        <result column="retail_price" property="retailPrice" />
        <result column="member_price" property="memberPrice" />
        <result column="negative_stock" property="negativeStock" />
        <result column="buying_price" property="buyingPrice" />
        <result column="initial_stock" property="initialStock" />
        <result column="max_stock" property="maxStock" />
        <result column="min_stock" property="minStock" />
        <result column="expiration_date" property="expirationDate" />
        <result column="discount" property="discount" />
        <result column="min_price" property="minPrice" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="product_image" property="productImage"/>
        <result column="total_sales_count" property="totalSalesCount"/>
        <result column="current_stock" property="currentStock"/>
        <result column="categoryName" property="categoryName"/>
        <result column="imageUrl" property="imageUrl"/>
        <result column="stockNum" property="stockNum"/>
    </resultMap>

    <select id="queryProducts" resultMap="BaseResultMap">
        SELECT
            base_product.*,
            base_product_category.NAME AS categoryName,
            com_file.url as imageUrl,
            base_product_stock.num as stockNum
        FROM
            base_product
                JOIN base_product_category ON base_product.category_id = base_product_category.id
                JOIN base_product_stock on base_product.id = base_product_stock.product_id
                left JOIN com_file on base_product.product_image = com_file.id
        WHERE
            0 = 0
            and base_product.delete_flag = 0
            and base_product_category.delete_flag = 0
            and base_product_category.state = 1
        <if test="query.name != null and query.name != ''">
            and base_product.name like concat('%',#{query.name},'%')
        </if>
        <if test="query.categoryId != null">
            and base_product.category_id = #{query.categoryId}
        </if>
        order by base_product.category_id, base_product.name
    </select>


</mapper>
