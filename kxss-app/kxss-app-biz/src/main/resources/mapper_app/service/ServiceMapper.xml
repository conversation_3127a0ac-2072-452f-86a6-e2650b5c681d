<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.app.mapper.service.ServiceMapper">


    <select id="queryService" resultType="top.kx.kxss.app.vo.result.service.AppServiceMemberResultVO">
        SELECT
        base_employee.real_name AS employeeName,
        base_employee.id as employeeId,
        com_file.url AS imageUrl,
        base_service.id AS serviceId,
        base_service.billing_cycle AS duration,
        base_service.category_id AS categoryId,
        base_service.timing_customer_price AS price,
        base_service.timing_member_price AS memberPrice,
        base_service.remarks,
        base_service_category.name as categoryName,
        base_service.name as serviceName,
        base_service_personal.remarks as persionRemark
        FROM
        base_service_personal
        JOIN base_service ON base_service_personal.service_id = base_service.id
        JOIN base_employee ON base_service_personal.employee_id = base_employee.id
        JOIN base_service_category on base_service.category_id = base_service_category.id
        LEFT JOIN com_file ON base_employee.photo_id = com_file.id
        WHERE
        base_service.delete_flag = 0
        and base_service_personal.delete_flag = 0
        and base_service_category.state = '1'
        and base_service_category.delete_flag = 0
        <if test="query.categoryId != null">
            and base_service.category_id = #{query.categoryId}
        </if>
        order by base_service_category.sort_value, base_service.sort_value, base_service_personal.id
    </select>


</mapper>
