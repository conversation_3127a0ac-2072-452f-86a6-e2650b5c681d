<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.app.mapper.table.TableCharingMapper">

    <select id="getNowChargingSet" resultType="top.kx.kxss.base.entity.tableCharging.setting.BaseTableChargingSetting">
        SELECT
        base_table_charging_setting.*
        FROM
        base_table_charging_setting
        JOIN base_table_charging ON base_table_charging_setting.charging_id = base_table_charging.id
        JOIN base_table_info ON base_table_charging.table_type = base_table_info.table_type
        WHERE
        base_table_info.id = #{tableId}  and base_table_charging_setting.delete_flag = 0 and base_table_charging.delete_flag = 0
          and base_table_info.delete_flag = 0
          AND base_table_charging_setting.start_time &lt;= DATE_FORMAT( now(), '%H:%i:%s' )
          AND base_table_charging_setting.end_time &gt;= DATE_FORMAT( now(), '%H:%i:%s' )
    </select>

    <select id="getOrgNowChargingSet" resultType="top.kx.kxss.base.entity.tableCharging.setting.BaseTableChargingSetting">
        SELECT
            base_table_charging_setting.*
        FROM
            base_table_charging_setting
                JOIN base_table_charging ON base_table_charging_setting.charging_id = base_table_charging.id
                JOIN base_table_info ON base_table_charging.table_type = base_table_info.table_type
        WHERE
            base_table_info.id = #{tableId} and  base_table_info.created_org_id = #{orgId} and base_table_charging_setting.delete_flag = 0 and base_table_charging.delete_flag = 0
          and base_table_charging.created_org_id = #{orgId} and base_table_info.delete_flag = 0
          and base_table_charging_setting.created_org_id = #{orgId}
          AND base_table_charging_setting.start_time &lt;= DATE_FORMAT( now(), '%H:%i:%s' )
          AND base_table_charging_setting.end_time &gt;= DATE_FORMAT( now(), '%H:%i:%s' )
    </select>



</mapper>
