<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.app.mapper.table.TableMapper">

    <select id="countWithStatus" resultType="map">
        SELECT
            count(1) AS tblCnt,
            table_status AS tableStatus,
            CASE
                WHEN table_status = '10' THEN
                    '空闲'
                WHEN table_status = '20' THEN
                    '使用中'
                WHEN table_status = '30' THEN
                    '已预订'
                END AS tableStatusName
        FROM
            base_table_info
        WHERE
            display = '1'
        GROUP BY
            table_status
        ORDER BY
            table_status
    </select>

    <select id="getSettingsByTable" resultType="map">
        select
            base_table_charging_setting.*
        from
            base_table_charging_setting join base_table_charging on base_table_charging.id = base_table_charging_setting.charging_id
                                        join base_table_info on base_table_charging.table_type_id = base_table_info.table_type
        where
            base_table_info.id = #{tableId}  and base_table_charging_setting.delete_flag = 0 and  base_table_charging.delete_flag = 0 and base_table_info.delete_flag = 0

    </select>


</mapper>
