<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.app.mapper.cash.service.PosCashServiceMapper">
<!--
    代码生成器 by 2023-04-19 14:44:58
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.app.entity.cash.service.PosCashService">
        <id column="id" property="id" />
        <result column="cash_id" property="cashId" />
        <result column="service_id" property="serviceId" />
        <result column="employee_id" property="employeeId" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="duration" property="duration" />
        <result column="price" property="price" />
        <result column="amount" property="amount" />
        <result column="remarks" property="remarks" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_org_id" property="createdOrgId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, cash_id, service_id, employee_id, start_time, end_time,
        duration, price, amount, remarks, created_time, created_by,
        updated_time, updated_by, created_org_id
    </sql>

    <select id="getServiceByCashIds" resultType="map">
        SELECT
            pos_cash.table_id AS tableId,
            count( pos_cash_service.id ) AS serviceCnt
        FROM
            pos_cash_service
                JOIN pos_cash ON pos_cash_service.cash_id = pos_cash.id
        WHERE
            pos_cash.table_id is not null
            and pos_cash_service.cash_id in
        <foreach collection="cashIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
            pos_cash.table_id
    </select>

    <select id="queryServices" resultType="map">
        SELECT
            pos_cash_service.id AS id,
            pos_cash_service.start_time AS startTime,
            pos_cash_service.end_time AS endTime,
            pos_cash_service.price,
            pos_cash_service.cash_id as cashId,
            pos_cash_service.remarks,
            pos_cash_service.duration as startMinutes,
            pos_cash_service.orgin_price as orginPrice,
            pos_cash_service.amount,
            pos_cash_service.discount,
            pos_cash_service.remind_time as remindTime,
            pos_cash_service.type,
            pos_cash_service.cycle_num as cycleNum,
            base_service.id AS serviceId,
            base_service.NAME AS serviceName,
            base_employee.real_name AS employeeName,
            com_file.url,
            base_service.duration,
            base_service.billing_cycle AS billingCycle,
            base_service.timeout_period AS timeoutPeriod,
            base_service.timing_member_price as memberPrice,
            base_service.min_price as minPrice
        FROM
            pos_cash_service
                JOIN base_service ON pos_cash_service.service_id = base_service.id
                JOIN base_employee ON pos_cash_service.employee_id = base_employee.id
                LEFT JOIN com_file ON base_employee.photo_id = com_file.id
        WHERE
            base_employee.state = '1'
            and pos_cash_service.delete_flag = '0'
            and pos_cash_service.cash_id = #{id}
    </select>
    <select id="queryServiceList"
            resultType="top.kx.kxss.app.vo.result.cash.service.PosCashServiceDetailResultVO">
        select t.id              as id,
               t.cash_id         as cardId,
               t.service_id      as serviceId,
               t.employee_id     as employeeId,
               e.real_name       as employeeName,
               t.start_time      as startTime,
               t.end_time        as endTime,
               t.orgin_price     as orginPrice,
               t.amount          as amount,
               t.assessed_amount as assessedAmount,
               t.status          as `status`,
               t.clock_type      as clockType,
               t.created_org_id  as createdOrgId,
               t.duration        as duration,
               p.code            as code,
               p.bill_type       as billType,
               p.bill_state      as billState,
               p.type_           as type,
               p.complete_time   as orderCompleteTime,
               p.created_time    as orderCreatedTime,
               t.cash_thail_id   as cashThailId,
               t.cycle_num    as cycleNum,
               t.thail_assessed_amount    as thailAssessedAmount,
               t.refund_amount    as refundAmount
        FROM pos_cash_service t
                 inner JOIN pos_cash p ON p.id = t.cash_id
                 LEFT JOIN base_employee e on e.id = t.employee_id
            ${ew.customSqlSegment}
    </select>

    <select id="queryTotal"
            resultType="top.kx.kxss.wxapp.vo.result.cash.WxPosCashServiceTotalResultVO">
        SELECT
            IFNULL( SUM( IFNULL( t.orgin_price, 0 )), 0 ) AS totalOrginPrice,
            IFNULL( SUM( IFNULL( t.amount, 0 )), 0 ) - IFNULL( SUM( IFNULL( t.assessed_amount, 0 )), 0 ) AS totalActualAmount,
            IFNULL( SUM( IFNULL( t.duration, 0 )), 0 ) AS totalDuration,
            IFNULL( SUM( IFNULL( t.cycle_num, 0 )), 0 ) AS totalCycleNum,
            IFNULL(
                    sum(CASE
                            WHEN t.cycle IS NULL
                                OR t.cycle = '' THEN
                                0
                            WHEN instr( t.cycle, '元/' ) <![CDATA[ <= ]]> 0 THEN 0 WHEN instr( t.cycle, '元/小时' ) > 0 THEN
                                (  IFNULL( t.cycle_num, 0 ) * 60 )
                            WHEN instr( t.cycle, '元/小时' ) <![CDATA[ <= ]]> 0 AND instr( t.cycle, '小时' ) > 0 THEN
                                (
                                    IFNULL( t.cycle_num, 0 ) * 60 * SUBSTRING_INDEX( SUBSTRING_INDEX( t.cycle, '/', -1 ), '小时',  1 ))
                            WHEN instr( t.cycle, '元/分钟' ) > 0 THEN
                                IFNULL( t.cycle_num, 0 )
                            WHEN instr( t.cycle, '元/分钟' ) <![CDATA[ <= ]]> 0 AND instr( t.cycle, '分钟' ) > 0 THEN
                                (
                                    IFNULL( t.cycle_num, 0 ) * SUBSTRING_INDEX( SUBSTRING_INDEX( t.cycle, '/', -1 ), '分钟',  1 )) ELSE 0
                        END),
                    0
            ) as totalChargingDuration
        FROM
            pos_cash_service t
                JOIN pos_cash p ON p.id = t.cash_id
                LEFT JOIN base_employee e ON e.id = t.employee_id
            ${ew.customSqlSegment}
    </select>
    <select id="findProfit" resultType="top.kx.kxss.app.vo.result.ProfitResultVO">
        SELECT
        SUM(
        IFNULL( amount, 0 )) - SUM(
        IFNULL( assessed_amount, 0 )) AS amount,
        SUM(
        IFNULL( profit_price, 0 )) AS profitAmount,
        SUM(
        IFNULL( num, 0 )) AS num,
        SUM(
        IFNULL( duration, 0 )) AS duration
        FROM
        pos_cash_service
        where delete_flag = 0
        <if test="posCashIdList != null and posCashIdList.size() > 0">
            and cash_id in
            <foreach collection="posCashIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>
