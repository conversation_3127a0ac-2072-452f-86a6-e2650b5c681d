<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.app.mapper.cash.table.PosCashTableMapper">
<!--
    代码生成器 by 2023-04-19 14:44:58
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.app.entity.cash.table.PosCashTable">
        <id column="id" property="id" />
        <result column="table_id" property="tableId" />
        <result column="cash_id" property="cashId" />
        <result column="charging_setting_id" property="chargingSettingId" />
        <result column="duration" property="duration" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="price" property="price" />
        <result column="amount" property="amount" />
        <result column="remarks" property="remarks" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_org_id" property="createdOrgId" />
    </resultMap>

    <resultMap id="posCashTableCashMap" type="top.kx.kxss.app.entity.cash.table.PosCashTableCash">
        <id column="id" property="id" />
        <result column="table_id" property="tableId" />
        <result column="cash_id" property="cashId" />
        <result column="charging_setting_id" property="chargingSettingId" />
        <result column="duration" property="duration" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="price" property="price" />
        <result column="amount" property="amount" />
        <result column="remarks" property="remarks" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="member_id" property="memberId" />
        <result column="memberName" property="memberName" />
        <result column="tblId" property="tblId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, table_id, duration, start_time, end_time, price,
        amount, remarks, created_time, created_by, updated_time, updated_by,
        created_org_id
    </sql>

    <select id="queryStartTables" resultMap="posCashTableCashMap">
        SELECT
            pos_cash_table.*,
            pos_cash.member_id,
            pos_cash.table_id as tblId
        FROM
            pos_cash
                JOIN pos_cash_table ON pos_cash_table.cash_id = pos_cash.id
        WHERE
            pos_cash.bill_state = 9
        ORDER BY
            table_id,
            start_time
    </select>

    <select id="queryStartTablesWithMember" resultMap="posCashTableCashMap">
        SELECT
            pos_cash_table.table_id,
            pos_cash.member_id,
            pos_cash_table.cash_id,
            member_info.name as memberName
        FROM
            pos_cash_table
                JOIN pos_cash ON pos_cash_table.cash_id = pos_cash.id
                JOIN member_info on pos_cash.member_id = member_info.id
        WHERE
            pos_cash.bill_state = 9
          AND pos_cash.member_id IS NOT NULL
        GROUP BY
            pos_cash_table.table_id
    </select>
    <select id="findProfit" resultType="top.kx.kxss.app.vo.result.ProfitResultVO">
        SELECT
        SUM(
        IFNULL( amount, 0 )) - SUM(
        IFNULL( assessed_amount, 0 )) AS amount,
        0 AS profitAmount,
        SUM(
        IFNULL( duration, 0 )) as duration
        FROM
        pos_cash_table
        where delete_flag = 0
        <if test="posCashIdList != null and posCashIdList.size() > 0">
            and cash_id in
            <foreach collection="posCashIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="thailIsNull">
            and cash_thail_id is null
        </if>
    </select>

</mapper>
