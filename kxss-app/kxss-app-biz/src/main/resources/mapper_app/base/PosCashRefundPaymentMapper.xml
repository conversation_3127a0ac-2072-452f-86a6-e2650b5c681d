<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.app.mapper.cash.refund.PosCashRefundPaymentMapper">
<!--
    代码生成器 by 2023-11-13 16:05:52
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.app.entity.cash.refund.PosCashRefundPayment">
        <id column="id" property="id" />
        <result column="cash_id" property="cashId" />
        <result column="pay_type_id" property="payTypeId" />
        <result column="status" property="status" />
        <result column="amount" property="amount" />
        <result column="pay_time" property="payTime" />
        <result column="recharge_amount" property="rechargeAmount" />
        <result column="gift_amount" property="giftAmount" />
        <result column="remarks" property="remarks" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="order_source" property="orderSource" />
        <result column="change_amount" property="changeAmount" />
        <result column="pay_name" property="payName" />
        <result column="platform_id" property="platformId" />
        <result column="order_id" property="orderId" />
        <result column="securities_number" property="securitiesNumber" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, cash_id, pay_type_id, status, amount, pay_time, 
        recharge_amount, gift_amount, remarks, created_time, created_by, updated_time, 
        updated_by, created_org_id, delete_flag, order_source, change_amount, pay_name, 
        platform_id, order_id, securities_number
    </sql>

</mapper>
