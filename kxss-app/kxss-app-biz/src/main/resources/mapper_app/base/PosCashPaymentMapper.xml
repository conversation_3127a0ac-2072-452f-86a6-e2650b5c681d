<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.app.mapper.cash.payment.PosCashPaymentMapper">
<!--
    代码生成器 by 2023-04-19 14:38:34
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.app.entity.cash.payment.PosCashPayment">
        <id column="id" property="id" />
        <result column="cash_id" property="cashId" />
        <result column="pay_type_id" property="payTypeId" />
        <result column="amount" property="amount" />
        <result column="round_amount" property="roundAmount" />
        <result column="pay_time" property="payTime" />
        <result column="remarks" property="remarks" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_org_id" property="createdOrgId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, cash_id, pay_type_id, amount, round_amount, pay_time,
        remarks, created_time, created_by, updated_time, updated_by, created_org_id

    </sql>

    <select id="paymentDetails" resultType="top.kx.kxss.wxapp.vo.result.statistics.PaymentDetailsResultVO">
        select *
        from (select pc.complete_time completeTime,
        pc.code code,
        pc.created_time createdTime,
        pc.id id,
        pcp.amount amount,
        pc.table_name tableName,
        pcp.pay_time payTime,
        bpt.name paymentType,
        bgb.name groupBuyName
        from pos_cash_payment pcp
        LEFT JOIN pos_cash pc on pcp.cash_id = pc.id
        left join base_payment_type bpt on pcp.pay_type_id = bpt.id
        left join base_group_buy bgb on pc.group_buy_id = bgb.id
        where pcp.delete_flag = 0
        <if test="model.payTypeId != null">
            and pcp.pay_type_id = #{model.payTypeId}
        </if>
        <if test="model.startDate != null and model.startDate != '' and model.orderQueryType = 'COMPLETE_TIME'">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != '' and model.orderQueryType = 'COMPLETE_TIME'">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.startDate != null and model.startDate != '' and model.orderQueryType = 'PAY_TIME'">
            and pcp.pay_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != '' and model.orderQueryType = 'PAY_TIME'">
            and pcp.pay_time &lt;= #{model.endDate}
        </if>
        <if test="model.paymentQueryType != null and model.paymentQueryType != '' and model.paymentQueryType == 'REFUND_SUCCESS'">
            and pcp.refund_amount is null
        </if>
        <if test="model.createdOrgId != null">
            and pcp.created_org_id =#{model.createdOrgId}
        </if>
        union all
        select pc.complete_time completeTime,
        pc.code code,
        pc.created_time createdTime,
        pc.id id,
        - pcp.refund_amount amount,
        pc.table_name tableName,
        pcp.pay_time payTime,
        bpt.name paymentType,
        bgb.name groupBuyName
        from pos_cash_payment pcp
        LEFT JOIN pos_cash pc on pcp.cash_id = pc.id
        left join base_payment_type bpt on pcp.pay_type_id = bpt.id
        left join base_group_buy bgb on pc.group_buy_id = bgb.id
        where pcp.delete_flag = 0
        and pcp.refund_amount > 0
        <if test="model.payTypeId != null">
            and pcp.pay_type_id = #{model.payTypeId}
        </if>
        <if test="model.startDate != null and model.startDate != '' and model.orderQueryType == 'COMPLETE_TIME'">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != '' and model.orderQueryType == 'COMPLETE_TIME'">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.startDate != null and model.startDate != '' and model.orderQueryType == 'PAY_TIME'">
            and pcp.pay_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != '' and model.orderQueryType =='PAY_TIME'">
            and pcp.pay_time &lt;= #{model.endDate}
        </if>
        <if test="model.paymentQueryType != null and model.paymentQueryType != '' and model.paymentQueryType == 'PAY_SUCCESS'">
            and pcp.refund_amount = 0
        </if>
        <if test="model.createdOrgId != null">
            and pcp.created_org_id =#{model.createdOrgId}
        </if>
        ) p
        order by p.createdTime desc
    </select>

    <select id="polymerizeCashPaymentPage" resultType="top.kx.kxss.wxapp.vo.result.statistics.PaymentDetailsResultVO">
        select pc.complete_time completeTime,
                pc.code          code,
                pc.created_time  createdTime,
                pc.id            id,
                (pcp.amount - pcp.refund_amount)  amount,
                pc.table_name    tableName,
                pcp.pay_time     payTime,
                bpt.name         paymentType
        from pos_cash_payment pcp
        LEFT JOIN pos_cash pc on pcp.cash_id = pc.id
        left join base_payment_type bpt on pcp.pay_type_id = bpt.id
        where pcp.delete_flag = 0 and bpt.biz_type = '2' and pc.delete_flag = 0 and pcp.status= 2
        <if test="model.payTypeId != null">
            and pcp.pay_type_id = #{model.payTypeId}
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and pcp.pay_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pcp.pay_time &lt;= #{model.endDate}
        </if>
        <if test="model.createdOrgId != null">
            and pcp.created_org_id =#{model.createdOrgId}
        </if>
        order by pcp.pay_time desc
    </select>

    <select id="polymerizeCashPaymentList" resultType="top.kx.kxss.wxapp.vo.result.statistics.PaymentDetailsResultVO">
        select pc.complete_time completeTime,
                pc.code          code,
                pc.created_time  createdTime,
                pc.id            id,
                (pcp.amount - pcp.refund_amount)  amount,
                pc.table_name    tableName,
                pcp.pay_time     payTime,
                pcp.order_id     orderId,
                bpt.name         paymentType
        from pos_cash_payment pcp
            LEFT JOIN pos_cash pc on pcp.cash_id = pc.id
            left join base_payment_type bpt on pcp.pay_type_id = bpt.id
        where pcp.delete_flag = 0 and bpt.biz_type = '2' and pc.delete_flag = 0 and pcp.status = 2
        <if test="model.payTypeId != null">
            and pcp.pay_type_id = #{model.payTypeId}
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and pcp.pay_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pcp.pay_time &lt;= #{model.endDate}
        </if>
        <if test="model.createdOrgId != null">
            and pcp.created_org_id =#{model.createdOrgId}
        </if>
        order by pcp.pay_time desc
    </select>

    <select id="paymentDetailsOverviewAmount" resultType="top.kx.kxss.wxapp.vo.result.statistics.PaymentDetailsOverviewResultVO">
        select
        sum(pcp.amount) amount,
        count(*) num
        from pos_cash_payment pcp
        LEFT JOIN pos_cash pc on pcp.cash_id = pc.id
        where pcp.delete_flag = 0
        <if test="payTypeId != null">
            and pcp.pay_type_id = #{payTypeId}
        </if>
        <if test="startDate != null and startDate != '' and orderQueryType == 'COMPLETE_TIME'">
            and pc.complete_time >= #{startDate}
        </if>
        <if test="endDate != null and endDate != '' and orderQueryType == 'COMPLETE_TIME'">
            and pc.complete_time &lt;= #{endDate}
        </if>
        <if test="startDate != null and startDate != '' and orderQueryType == 'PAY_TIME'">
            and pcp.pay_time >= #{startDate}
        </if>
        <if test="endDate != null and endDate != '' and orderQueryType == 'PAY_TIME'">
            and pcp.pay_time &lt;= #{endDate}
        </if>
        <if test="createdOrgId != null">
            and pcp.created_org_id =#{createdOrgId}
        </if>
    </select>

    <select id="paymentDetailsOverviewRefundAmount"  resultType="top.kx.kxss.wxapp.vo.result.statistics.PaymentDetailsOverviewResultVO">
        select
            sum(pcp.refund_amount) refundAmount,
            count(*)              refundNum
        from pos_cash_payment pcp
            LEFT JOIN pos_cash pc on pcp.cash_id = pc.id
        where pcp.delete_flag = 0
            and pcp.refund_amount > 0
            <if test="payTypeId != null">
                and pcp.pay_type_id = #{payTypeId}
            </if>
            <if test="startDate != null and startDate != '' and orderQueryType == 'COMPLETE_TIME'">
                and pc.complete_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != '' and orderQueryType == 'COMPLETE_TIME'">
                and pc.complete_time &lt;= #{endDate}
            </if>
            <if test="startDate != null and startDate != '' and orderQueryType == 'PAY_TIME'">
                and pcp.pay_time >= #{startDate}
            </if>
            <if test="endDate != null and endDate != '' and orderQueryType == 'PAY_TIME'">
                and pcp.pay_time &lt;= #{endDate}
            </if>
        <if test="createdOrgId != null">
            and pcp.created_org_id =#{createdOrgId}
        </if>
    </select>


    <select id="statisticsPolymerize" resultType="top.kx.kxss.wxapp.vo.result.payment.PaymentTypeStatisticsDetailsResultVO">
        select round(sum(pcp.amount - pcp.refund_amount), 2) as amount,
               round(sum(ifnull(pcp.mch_fee_amount, 0.00)), 2) as mchFeeAmount,
               pcp.pay_type_id           as payTypeId,
               bpt.name                  as payTypeName,
               bpt.icon                  as icon
        from pos_cash_payment pcp
                 inner join pos_cash pc on pcp.cash_id = pc.id and pc.delete_flag = 0
                 left join base_payment_type bpt on pcp.pay_type_id = bpt.id
        where pcp.delete_flag = 0
          and bpt.biz_type = '2'
          and pcp.status = '2'
        <if test="payTypeId != null">
            and pcp.pay_type_id = #{payTypeId}
        </if>
        <if test="startDate != null and startDate != ''">
            and pcp.pay_time >= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and pcp.pay_time &lt;= #{endDate}
        </if>
        <if test="createdOrgId != null">
            and pcp.created_org_id =#{createdOrgId}
        </if>
        group by pcp.pay_type_id
    </select>

    <select id="paymentDataReportPage" resultType="java.util.Map">
        SELECT DATE_FORMAT(pc.complete_time, '%Y-%m-%d') as completeTime
        FROM pos_cash_payment pcp
                 left join pos_cash pc on pcp.cash_id = pc.id
        WHERE pcp.delete_flag = 0
          AND pc.bill_type not in (1, 2)
          and pc.delete_flag = 0
          and pc.bill_state in (2, 5, 6)
          AND pcp.status = '2'
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.payTime_st != null and model.payTime_st != ''">
            and pcp.pay_time >= #{model.payTime_st}
        </if>
        <if test="model.payTime_ed != null and model.payTime_ed != ''">
            and pcp.pay_time &lt;= #{model.payTime_ed}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.payTypeIds != null and model.payTypeIds.size() > 0">
            and pcp.pay_type_id IN
            <foreach item="payTypeId" collection="model.payTypeIds" open="(" separator="," close=")">
                #{payTypeId}
            </foreach>
        </if>
        <if test="model.payTypeId != null">
            and pcp.pay_type_id = #{model.payTypeId}
        </if>
        GROUP BY completeTime
        order by completeTime
    </select>

    <select id="paymentDataReport" resultType="top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO">
        SELECT pc.complete_time completeTime,
               pcp.id as id,
               pc.id as cashId,
               pcp.pay_type_id as payTypeId,
               pcp.amount as amount,
               pcp.round_amount as roundAmount,
               pcp.recharge_amount as rechargeAmount,
               (ifnull(pcp.amount, 0) - ifnull(pcp.refund_amount, 0) - ifnull(pcp.change_amount, 0)) as payment
        FROM pos_cash_payment pcp
                 left join pos_cash pc on pcp.cash_id = pc.id
        WHERE pcp.delete_flag = 0
          AND pc.bill_type not in (1, 2)
          and pc.delete_flag = 0
          and pc.bill_state in (2, 5, 6)
          AND pcp.status = '2'
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.payTime_st != null and model.payTime_st != ''">
            and pcp.pay_time >= #{model.payTime_st}
        </if>
        <if test="model.payTime_ed != null and model.payTime_ed != ''">
            and pcp.pay_time &lt;= #{model.payTime_ed}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        <if test="model.payTypeIds != null and model.payTypeIds.size() > 0">
            and pcp.pay_type_id IN
            <foreach item="payTypeId" collection="model.payTypeIds" open="(" separator="," close=")">
                #{payTypeId}
            </foreach>
        </if>
        <if test="model.payTypeId != null">
            and pcp.pay_type_id = #{model.payTypeId}
        </if>
    </select>

    <select id="statisConsumeAmount" resultType="top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO">
        select pcp.pay_type_id as payTypeId,
               ROUND(sum(ifnull(pcp.amount, 0) - ifnull(pcp.refund_amount, 0) - ifnull(pcp.change_amount, 0)), 2) as payment,
            ROUND(sum(ifnull(pcp.recharge_amount, 0)), 2) as rechargeAmount,
            ROUND(sum(ifnull(pcp.gift_amount, 0)), 2) as giftAmount
        from pos_cash_payment pcp
                 left join pos_cash pc on pcp.cash_id = pc.id
                 left join member_info mi on pc.member_id = mi.id
        WHERE pcp.delete_flag = 0
          and pcp.status = '2'
          and pc.bill_state in ('2', '5')
          and pc.bill_type not in ('1', '2')
          and pc.delete_flag = 0
          and pc.member_id is not null
          and pc.type_ != '3'
        <if test="model.gradeId != null">
            and mi.grade_id = #{model.gradeId}
        </if>
        <if test="model.gradeIds != null and model.gradeIds.size() > 0">
            and mi.grade_id in
            <foreach item="gradeId" collection="model.gradeIds" open="(" separator="," close=")">
                #{gradeId}
            </foreach>
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(mi.name, #{model.keyword}) or instr(mi.mobile, #{model.keyword}))
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        group by pcp.pay_type_id
    </select>

    <select id="selectOneAmount" resultType="top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO">
        select COUNT(DISTINCT p.id)                                                                                          as num,
               IFNULL(ROUND(sum(ifnull(pcp.amount, 0) - ifnull(pcp.refund_amount, 0) - ifnull(pcp.change_amount, 0)), 2),
                      0)                                                                                                     as payment,
               IFNULL(ROUND(sum(ifnull(pcp.amount, 0)), 2), 0)                                                               as amount,
               IFNULL(ROUND(sum(ifnull(pcp.refund_amount, 0)), 2), 0)                           as refundAmount,
               IFNULL(ROUND(SUM(CASE
                                    WHEN p.type_ = '3' THEN (IFNULL(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
                                                             IFNULL(pcp.change_amount, 0)) END),
                            2),
                      0)                                                                                                     as rechargeAmount,
               IFNULL(ROUND(SUM(CASE
                                    WHEN p.type_ != '3' THEN (IFNULL(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
                                                              IFNULL(pcp.change_amount, 0)) END),
                            2),
                      0)                                                                                                     as turnoverAmount,
               IFNULL(SUM(IF(IFNULL(pcp.refund_amount, 0) > 0 and IFNULL(pcp.refund_amount, 0) > IFNULL(pcp.gift_amount, 0),
                             IFNULL(pcp.recharge_amount, 0) - (IFNULL(pcp.refund_amount, 0) - IFNULL(pcp.gift_amount, 0)),
                             IFNULL(pcp.recharge_amount, 0))), 0)                                                                as rechargePayment,
               IFNULL(SUM(IF(IFNULL(pcp.refund_amount, 0) > 0 and IFNULL(pcp.refund_amount, 0) > IFNULL(pcp.gift_amount, 0),
                             0, IFNULL(pcp.gift_amount, 0) - IFNULL(pcp.refund_amount, 0))), 0)                              as giftPayment,
               IFNULL(ROUND(SUM(IF(bpt.biz_type = '2', (IFNULL(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
                                                        IFNULL(pcp.change_amount, 0)), 0)),
                            2),
                      0)                                                                        as polymerizationPayment,
               IFNULL(ROUND(SUM(IF(bpt.biz_type != '2', (IFNULL(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
                                                        IFNULL(pcp.change_amount, 0)), 0)),
                            2),
                      0)                                                                        as otherPayment,
               IFNULL(ROUND(SUM(IF(bpt.biz_type = '5', (IFNULL(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
                                                        IFNULL(pcp.change_amount, 0)), 0)),
                            2),
                      0)                                                                        as meituanPayment,
               IFNULL(ROUND(SUM(IF(bpt.biz_type = '6', (IFNULL(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
                                                        IFNULL(pcp.change_amount, 0)), 0)),
                            2),
                      0)                                                                        as douyinPayment
        from pos_cash_payment pcp
                 inner join pos_cash p on pcp.cash_id = p.id
                 inner join base_payment_type bpt on pcp.pay_type_id = bpt.id
            ${ew.customSqlSegment}
    </select>

    <select id="selectGiftPaymentByOrderSource"
            resultType="top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO">
        select  p.order_source     as  orderSource,
               IFNULL(SUM(IF(IFNULL(pcp.refund_amount, 0) > 0 and IFNULL(pcp.refund_amount, 0) > IFNULL(pcp.gift_amount, 0),
                             0, IFNULL(pcp.gift_amount, 0) - IFNULL(pcp.refund_amount, 0))), 0)                              as giftPayment
        from pos_cash_payment pcp
                 inner join pos_cash p on pcp.cash_id = p.id
                 inner join base_payment_type bpt on pcp.pay_type_id = bpt.id
            ${ew.customSqlSegment}
        group by p.order_source
    </select>

    <select id="selectListAmountBySource" resultType="top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO">
        select p.order_source                                                                                                as orderSource,
               COUNT(DISTINCT p.id)                                                                                          as num,
               IFNULL(ROUND(sum(ifnull(pcp.amount, 0) - ifnull(pcp.refund_amount, 0) - ifnull(pcp.change_amount, 0)
                   - ifnull(pcp.refund_amount, 0)), 2),
                      0)                                                                                                     as payment,
               IFNULL(ROUND(sum(ifnull(pcp.amount, 0)- ifnull(pcp.refund_amount, 0)), 2), 0)     as amount,
               IFNULL(ROUND(SUM(CASE
                                    WHEN p.type_ = '3' THEN (IFNULL(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
                                                             IFNULL(pcp.change_amount, 0)) END),
                            2),
                      0)                                                                                                     as rechargeAmount,
               IFNULL(ROUND(SUM(CASE
                                    WHEN p.type_ != '3' THEN (IFNULL(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
                                                              IFNULL(pcp.change_amount, 0)) END),
                            2),
                      0)                                                                                                     as turnoverAmount,
               IFNULL(SUM(IF(IFNULL(pcp.refund_amount, 0) > 0 and IFNULL(pcp.refund_amount, 0) > IFNULL(pcp.gift_amount, 0),
                             IFNULL(pcp.recharge_amount, 0) - (IFNULL(pcp.refund_amount, 0) - IFNULL(pcp.gift_amount, 0)),
                             IFNULL(pcp.recharge_amount, 0))), 0)                                                                as rechargePayment,
               IFNULL(SUM(IF(IFNULL(pcp.refund_amount, 0) > 0 and IFNULL(pcp.refund_amount, 0) > IFNULL(pcp.gift_amount, 0),
                             0, IFNULL(pcp.gift_amount, 0) - IFNULL(pcp.refund_amount, 0))), 0)                                    as giftPayment
        from pos_cash_payment pcp
                 inner join pos_cash p on pcp.cash_id = p.id
                 inner join base_payment_type bpt on pcp.pay_type_id = bpt.id
            ${ew.customSqlSegment}
        group by p.order_source
    </select>

    <select id="selectListAmountByType" resultType="top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO">
        select p.type_                                                                                                as type,
               COUNT(DISTINCT p.id)                                                                                          as num,
               IFNULL(ROUND(sum(ifnull(pcp.amount, 0) - ifnull(pcp.refund_amount, 0) - ifnull(pcp.change_amount, 0)), 2),
                      0)                                                                                                     as payment,
               IFNULL(ROUND(sum(ifnull(pcp.amount, 0)), 2), 0)                                                               as amount,
               IFNULL(ROUND(SUM(CASE
                                    WHEN p.type_ = '3' THEN (IFNULL(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
                                                             IFNULL(pcp.change_amount, 0)) END),
                            2),
                      0)                                                                                                     as rechargeAmount,
               IFNULL(ROUND(SUM(CASE
                                    WHEN p.type_ != '3' THEN (IFNULL(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
                                                              IFNULL(pcp.change_amount, 0)) END),
                            2),
                      0)                                                                                                     as turnoverAmount,
               IFNULL(SUM(IF(IFNULL(pcp.refund_amount, 0) > 0 and IFNULL(pcp.refund_amount, 0) > IFNULL(pcp.gift_amount, 0),
                             IFNULL(pcp.recharge_amount, 0) - (IFNULL(pcp.refund_amount, 0) - IFNULL(pcp.gift_amount, 0)),
                             IFNULL(pcp.recharge_amount, 0))), 0)                                                                as rechargePayment,
               IFNULL(SUM(IF(IFNULL(pcp.refund_amount, 0) > 0 and IFNULL(pcp.refund_amount, 0) > IFNULL(pcp.gift_amount, 0),
                             0, IFNULL(pcp.gift_amount, 0) - IFNULL(pcp.refund_amount, 0))), 0)                                    as giftPayment
        from pos_cash_payment pcp
                 inner join pos_cash p on pcp.cash_id = p.id
                 inner join base_payment_type bpt on pcp.pay_type_id = bpt.id
            ${ew.customSqlSegment}
        group by p.type_
    </select>

    <select id="selectAmountByPayTypeId" resultType="top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO">
        select pcp.pay_type_id                                                                                    AS payTypeId,
               bpt.biz_type                                                                                       as bizType,
               bpt.type                                                                                           as type,
               COUNT(DISTINCT p.id)                                                                               as num,
               COUNT(DISTINCT pcp.id)                                                                             as payNum,
               IFNULL(ROUND(SUM(IFNULL(pcp.amount, 0) - ifnull(pcp.refund_amount, 0) - IFNULL(pcp.change_amount, 0)), 2), 0) as payment,
               SUM(ROUND(IF(bpt.fee_rate is null, 0,
                            (IFNULL(pcp.amount, 0) - ifnull(pcp.refund_amount, 0) - IFNULL(pcp.change_amount, 0)) *
                            bpt.fee_rate /
                            100), 2))                                                                        as feePayment,
               IFNULL(ROUND(sum(ifnull(pcp.amount, 0)), 2), 0)                                                               as amount,
               IFNULL(ROUND(SUM(CASE
                                    WHEN p.type_ = '3' THEN (IFNULL(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
                                                             IFNULL(pcp.change_amount, 0)) END), 2), 0)                      as rechargeAmount,
               IFNULL(ROUND(SUM(CASE
                                    WHEN p.type_ != '3' THEN (IFNULL(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
                                                              IFNULL(pcp.change_amount, 0)) END), 2), 0)                     as turnoverAmount,
               IFNULL(SUM(IF(IFNULL(pcp.refund_amount, 0) > 0 and IFNULL(pcp.refund_amount, 0) > IFNULL(pcp.gift_amount, 0),
                             IFNULL(pcp.recharge_amount, 0) - (IFNULL(pcp.refund_amount, 0) - IFNULL(pcp.gift_amount, 0)),
                             IFNULL(pcp.recharge_amount, 0))), 0)                                                                as rechargePayment,
               IFNULL(SUM(IF(IFNULL(pcp.refund_amount, 0) > 0 and IFNULL(pcp.refund_amount, 0) > IFNULL(pcp.gift_amount, 0),
                             0, IFNULL(pcp.gift_amount, 0) - IFNULL(pcp.refund_amount, 0))), 0)                                      as giftPayment
        from pos_cash_payment pcp
                 inner join pos_cash p on pcp.cash_id = p.id
                 inner join base_payment_type bpt on pcp.pay_type_id = bpt.id
            ${ew.customSqlSegment}
        GROUP BY concat(pcp.pay_type_id,'_',pcp.is_change)
    </select>


    <select id="selectRefundByPayTypeId" resultType="top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO">
        select pcp.pay_type_id        AS payTypeId,
               COUNT(DISTINCT pcp.id) as payNum
        from pos_cash_refund_payment pcp
                 inner join pos_cash p on pcp.cash_id = p.id
                 inner join base_payment_type bpt on pcp.pay_type_id = bpt.id
            ${ew.customSqlSegment}
        GROUP BY pcp.pay_type_id
    </select>

</mapper>
