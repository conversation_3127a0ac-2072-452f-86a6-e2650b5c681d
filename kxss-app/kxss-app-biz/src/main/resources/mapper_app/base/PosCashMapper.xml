<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.app.mapper.cash.PosCashMapper">
    <!--
        代码生成器 by 2023-04-19 14:04:53
        自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
    -->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.app.entity.cash.PosCash">
        <id column="id" property="id"/>
        <result column="type_" property="type"/>
        <result column="code" property="code"/>
        <result column="bill_date" property="billDate"/>
        <result column="bill_state" property="billState"/>
        <result column="bill_type" property="billType"/>
        <result column="org_id" property="orgId"/>
        <result column="employee_id" property="employeeId"/>
        <result column="member_id" property="memberId"/>
        <result column="amount" property="amount"/>
        <result column="discount_amount" property="discountAmount"/>
        <result column="payment" property="payment"/>
        <result column="pay_name" property="payName"/>
        <result column="remarks" property="remarks"/>
        <result column="created_time" property="createdTime"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="created_org_id" property="createdOrgId"/>
        <result column="source_id" property="sourceId"/>
        <result column="chargeback_id" property="chargebackId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , type_, code, bill_date, bill_state, org_id,
        employee_id, member_id, amount, discount_amount, payment, pay_name,
        remarks, created_time, created_by, updated_time, updated_by, created_org_id,
        source_id, chargeback_id
    </sql>
    <select id="newCustomersCountList" resultType="top.kx.kxss.wxapp.vo.result.statistics.NewCustomersStatisResultVO">
        SELECT DATE_FORMAT(DATE_ADD(t.created_time, INTERVAL -#{hour} HOUR), '%Y-%m-%d') AS dateStr,
               SUM(CASE WHEN t.pay_type = 'WX' THEN 1 ELSE 0 END)                                          AS wxCount,
               SUM(CASE WHEN t.pay_type = 'ALI' THEN 1 ELSE 0 END)                                         AS aliCount
        FROM pos_cash_payment t
                 INNER JOIN pos_cash p ON t.cash_id = p.id
            ${ew.customSqlSegment}
        GROUP BY dateStr
        ORDER BY dateStr desc
    </select>
    <update id="deletePosCash">
        update pos_cash
        set delete_flag  = 1,
            updated_time = now()
        where id = #{id}
    </update>
    <select id="selectAmount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT (CASE
                    WHEN ${field} = created_time THEN
                        DATE_FORMAT(NULLIF(created_time, complete_time), '%Y-%m-%d')
                    ELSE ${field}
            END)         AS                                                                    field,
               IFNULL(COUNT(id), 0)                                                            num,
               IFNULL(SUM(IFNULL(amount, 0)), 0) - IFNULL(SUM(IFNULL(refund_amount, 0)), 0)    amount,
               (IFNULL(SUM(IFNULL(payment, 0)), 0) - IFNULL(SUM(IFNULL(refund_amount, 0)), 0)) payment,
               IFNULL(SUM(IFNULL(discount_amount, 0)), 0)                                      discountAmount,
               IFNULL(SUM(IFNULL(refund_amount, 0)), 0)                                        refundAmount,
               IFNULL(SUM(IFNULL(gift_amount, 0)), 0)                                          giftAmount,
               IFNULL(SUM(IFNULL(paid, 0)), 0)                                                 paid,
               IFNULL(SUM(IFNULL(unpaid, 0)), 0)                                               unpaid,
               bill_type as                                                                    billType,
               (CASE
                    WHEN type_ = 3 THEN
                        IFNULL(SUM(IFNULL(payment, 0)), 0)
                    ELSE 0
                   END)  AS                                                                    rechargeAmount
        FROM pos_cash ${ew.customSqlSegment}
        GROUP BY field, bill_type
    </select>

    <select id="selectListAmount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT (CASE
                    WHEN ${field} = created_time THEN
                        DATE_FORMAT(NULLIF(created_time, complete_time), '%Y-%m-%d')
                    ELSE ${field}
            END)        AS                                                                     field,
               IFNULL(COUNT(id), 0)                                                            num,
               IFNULL(SUM(IFNULL(amount, 0)), 0) - IFNULL(SUM(IFNULL(refund_amount, 0)), 0)    amount,
               (IFNULL(SUM(IFNULL(payment, 0)), 0) - IFNULL(SUM(IFNULL(refund_amount, 0)), 0)) payment,
               IFNULL(SUM(IFNULL(discount_amount, 0)), 0)                                      discountAmount,
               IFNULL(SUM(IFNULL(refund_amount, 0)), 0)                                        refundAmount,
               IFNULL(SUM(IFNULL(gift_amount, 0)), 0)                                          giftAmount,
               IFNULL(SUM(IFNULL(paid, 0)), 0)                                                 paid,
               IFNULL(SUM(IFNULL(unpaid, 0)), 0)                                               unpaid,
               (CASE
                    WHEN type_ = 3 THEN
                        IFNULL(SUM(IFNULL(payment, 0)), 0)
                    ELSE 0
                   END) AS                                                                     rechargeAmount
        FROM pos_cash ${ew.customSqlSegment}
        GROUP BY field
    </select>
    <select id="selectOneAmount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT IFNULL(COUNT(id), 0)                                                            num,
               COUNT(DISTINCT CASE WHEN type_ = '3' THEN id END)                               rechargeNum,
               COUNT(DISTINCT CASE WHEN type_ = '0' THEN id END)                      AS       tableNum,
               COUNT(DISTINCT CASE WHEN type_ = '0' AND thail_id IS NULL THEN id END) AS       noThailTableNum,
               COUNT(DISTINCT CASE WHEN type_ = '0' AND thail_id IS NOT NULL THEN id END) AS   thailTableNum,
               IFNULL(SUM(IFNULL(amount, 0)), 0) - IFNULL(SUM(IFNULL(refund_amount, 0)), 0)   amount,
               (IFNULL(SUM(IFNULL(payment, 0)), 0) - IFNULL(SUM(IFNULL(refund_amount, 0)), 0)) payment,
               IFNULL(SUM(IFNULL(discount_amount, 0)), 0)                                      discountAmount,
               IFNULL(SUM(IFNULL(refund_amount, 0)), 0)                                        refundAmount,
               IFNULL(SUM(IFNULL(gift_amount, 0)), 0)                                          giftAmount,
               IFNULL(SUM(IFNULL(paid, 0)), 0)                                                 paid,
               IFNULL(SUM(IFNULL(unpaid, 0)), 0)                                               unpaid,
               bill_type                                                              as       billType,
               COUNT(DISTINCT CASE WHEN is_first_recharge THEN member_id END)         AS       firstRechargeNum,
               COUNT(DISTINCT CASE WHEN is_first_recharge = false THEN member_id END) AS       continueRechargeNum,
               SUM(IF(type_ = '3', IFNULL(payment, 0), 0)) AS rechargeAmount
        FROM pos_cash p ${ew.customSqlSegment}
    </select>

    <select id="selectOrderSourceCount" resultType="top.kx.kxss.app.vo.result.cash.OrderSourceAmountResultVO">
        select order_source as orderSource, count(id) as num
        from pos_cash p
            ${ew.customSqlSegment}
        group by order_source
    </select>

    <select id="amountSourceResultVOList" resultType="top.kx.kxss.app.vo.result.cash.OrderSourceAmountResultVO">
        SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)       amount,
               p.order_source   orderSource
        FROM pos_cash_thail t
                 JOIN pos_cash p ON p.id = t.cash_id and t.delete_flag = 0
            ${ew.customSqlSegment}
        GROUP BY p.order_source
        UNION ALL
        (SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)       amount,
                p.order_source   orderSource
         FROM pos_cash_table t
                  inner JOIN pos_cash p ON p.id = t.cash_id
                  inner join base_table_info `table`
                             on `table`.id = t.table_id and t.cash_thail_id is null and p.table_id is not null and
                                p.type_ = '0' and t.delete_flag = 0 and t.status = '1'
            ${ew.customSqlSegment}
         GROUP BY p.order_source)
        UNION ALL
        (SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)       amount,
                p.order_source   orderSource
         FROM pos_cash_product t
                  JOIN pos_cash p ON p.id = t.cash_id and t.delete_flag = 0 and t.cash_thail_id is null
            ${ew.customSqlSegment}
         GROUP BY p.order_source)
        UNION ALL
        (SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)       amount,
                p.order_source   orderSource
         FROM pos_cash_service t
                  JOIN pos_cash p ON p.id = t.cash_id and t.delete_flag = 0 and t.cash_thail_id is null and t.status = '1'
            ${ew.customSqlSegment}
         GROUP BY p.order_source)
        UNION ALL
        (SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)       amount,
                p.order_source   orderSource
         FROM pos_cash_card t
                  JOIN pos_cash p ON p.id = t.cash_id and t.delete_flag = 0 and t.cash_thail_id is null
            ${ew.customSqlSegment}
         GROUP BY p.order_source)
    </select>
    <select id="selectBySalesType" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)     amount,
               IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)                                   payment,
               IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0) +
               IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)                                       discountAmount,
               IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0)                                           giftAmount,
               IFNULL(SUM(IFNULL(p.paid, 0)), 0)                                                  paid,
               IFNULL(SUM(IFNULL(p.unpaid, 0)), 0)                                                unpaid,
               IFNULL(COUNT(p.id), 0)                                                             num,
               '套餐'      AS                                                                     field,
               p.bill_type as                                                                     billType,
               'THAIL'     AS                                                                     type
        FROM pos_cash_thail t
                 JOIN pos_cash p ON p.id = t.cash_id
            and t.delete_flag = 0
            ${ew.customSqlSegment}
        GROUP BY
            field,
            bill_type
        UNION ALL
        (
        SELECT
            IFNULL( SUM( IFNULL( t.orgin_price, 0 )), 0 ) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0) amount,
            IFNULL( SUM( IFNULL( t.amount, 0 )), 0 ) - IFNULL( SUM( IFNULL( t.assessed_amount, 0 )), 0 )
            - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)       payment,
            IFNULL( SUM( IFNULL( t.discount_amount, 0 )), 0 ) + IFNULL( SUM( IFNULL( t.assessed_amount, 0 )), 0 ) discountAmount,
            IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0) giftAmount,
            IFNULL(SUM(IFNULL(p.paid, 0)), 0) paid,
            IFNULL(SUM(IFNULL(p.unpaid, 0)), 0) unpaid,
            IFNULL(count(p.id), 0) num,
            '台费' AS field, p.bill_type as billType,
            'TABLE' AS `type`
        FROM
            pos_cash_table t
            inner JOIN pos_cash p
        ON p.id = t.cash_id
            inner join base_table_info `table` on `table`.id = t.table_id
            and t.cash_thail_id is null and p.table_id is not null
            and p.type_ = '0' and t.delete_flag = 0
            and t.status = '1'
            ${ew.customSqlSegment}
        GROUP BY
            field,
            bill_type)
        UNION ALL
        (
        SELECT
            IFNULL( SUM( IFNULL( t.orgin_price, 0 )), 0 ) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0) amount,
            IFNULL( SUM( IFNULL( t.amount, 0 )), 0 ) -IFNULL( SUM( IFNULL( t.assessed_amount, 0 )), 0 )
            - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)    payment, IFNULL( SUM( IFNULL( t.discount_amount, 0 )), 0 )+ IFNULL( SUM( IFNULL( t.assessed_amount, 0 )), 0 ) discountAmount, IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0) giftAmount, IFNULL(SUM(IFNULL(p.paid, 0)), 0) paid, IFNULL(SUM(IFNULL(p.unpaid, 0)), 0) unpaid, IFNULL(count(p.id), 0) num, '商品' AS field, p.bill_type as billType, 'PRODUCT' AS `type`
        FROM
            pos_cash_product t
            JOIN pos_cash p
        ON p.id = t.cash_id and t.delete_flag = 0
            and t.cash_thail_id is null
            ${ew.customSqlSegment}
        GROUP BY
            field,
            bill_type
            )
        UNION ALL
        (
        SELECT
            IFNULL( SUM( IFNULL( t.orgin_price, 0 )), 0 ) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0) amount,
            IFNULL( SUM( IFNULL( t.amount, 0 )), 0 ) -IFNULL( SUM( IFNULL( t.assessed_amount, 0 )), 0 )
            - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)    payment, IFNULL( SUM( IFNULL( t.discount_amount, 0 )), 0 )+ IFNULL( SUM( IFNULL( t.assessed_amount, 0 )), 0 ) discountAmount, IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0) giftAmount, IFNULL(SUM(IFNULL(p.paid, 0)), 0) paid, IFNULL(SUM(IFNULL(p.unpaid, 0)), 0) unpaid, IFNULL(count(p.id), 0) num, '服务' AS field, p.bill_type as billType, 'SERVICE' AS `type`
        FROM
            pos_cash_service t
            JOIN pos_cash p
        ON p.id = t.cash_id and t.delete_flag = 0
            and t.cash_thail_id is null and t.status = '1'
            ${ew.customSqlSegment}
        GROUP BY
            field,
            bill_type
            )
        UNION ALL
        (
        SELECT
            IFNULL( SUM( IFNULL( t.orgin_price, 0 )), 0 ) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0) amount,
            IFNULL( SUM( IFNULL( t.amount, 0 )), 0 ) -IFNULL( SUM( IFNULL( t.assessed_amount, 0 )), 0 )
            - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)   payment, IFNULL( SUM( IFNULL( t.discount_amount, 0 )), 0 )+ IFNULL( SUM( IFNULL( t.assessed_amount, 0 )), 0 ) discountAmount, IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0) giftAmount, IFNULL(SUM(IFNULL(p.paid, 0)), 0) paid, IFNULL(SUM(IFNULL(p.unpaid, 0)), 0) unpaid, IFNULL(count(p.id), 0) num, '购卡' AS field, p.bill_type as billType, 'BUY_CARD' AS `type`
        FROM
            pos_cash_card t
            JOIN pos_cash p
        ON p.id = t.cash_id and t.delete_flag = 0
            and t.cash_thail_id is null
            ${ew.customSqlSegment}
        GROUP BY
            field,
            bill_type
            )
        union all
        (
        SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0)
            - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)     amount,
            IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)
            - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)                                   payment,
            IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0) +
            IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)                                       discountAmount,
            IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0)                                           giftAmount,
            IFNULL(SUM(IFNULL(p.paid, 0)), 0)                                                  paid,
            IFNULL(SUM(IFNULL(p.unpaid, 0)), 0)                                                unpaid,
            IFNULL(COUNT(p.id), 0)                                                             num,
            '充电'      AS                                                                     field,
            p.bill_type as                                                                     billType,
            'POWER'     AS                                                                     type
        FROM pos_cash_power t
            JOIN pos_cash p ON p.id = t.cash_id
            and t.delete_flag = 0
            ${ew.customSqlSegment}
        GROUP BY
            field,
            bill_type

        )

    </select>

    <select id="selectThailBySecurities" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)     amount,
               IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0) -
               IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0) payment,
               IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0) +
               IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0) discountAmount,
               IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0)     giftAmount,
               IFNULL(SUM(IFNULL(p.paid, 0)), 0)            paid,
               IFNULL(SUM(IFNULL(p.unpaid, 0)), 0)          unpaid,
               t.is_check_securities                        isCheckSecurities,
               IFNULL(COUNT(p.id), 0)                       num,
               '套餐'      AS                               field,
               p.bill_type as                               billType,
               'THAIL'     AS                               type
        FROM pos_cash_thail t
                 JOIN pos_cash p ON p.id = t.cash_id
            and t.delete_flag = 0
            ${ew.customSqlSegment}
        group by t.is_check_securities
    </select>

    <select id="selectByPayType" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT (IFNULL(SUM(IFNULL(t.amount, 0)), 0)
            - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)
            - IFNULL(SUM(IFNULL(t.change_amount, 0)), 0))                                   amount,
               (IFNULL(SUM(IFNULL(t.amount, 0)), 0)
                   - SUM(if(t.is_prepaid is not null and t.is_prepaid = 1, IFNULL(t.refund_amount, 0),
                            0))
                   - IFNULL(SUM(IFNULL(t.change_amount, 0)), 0))                            payment,
               SUM(ROUND(IF(bpt.fee_rate is null, 0,
                            (IFNULL(t.amount, 0) - ifnull(t.refund_amount, 0) - IFNULL(t.change_amount, 0)) *
                            bpt.fee_rate /
                            100), 2))                                                       feePayment,
               t.pay_type_id   AS                                                           field,
               min(t.pay_name) as                                                           name,
               IFNULL(SUM(IF(IFNULL(t.refund_amount, 0) > 0 and IFNULL(t.refund_amount, 0) > IFNULL(t.gift_amount, 0),
                             0, IFNULL(t.gift_amount, 0) - IFNULL(t.refund_amount, 0))), 0) giftAmount,
               IFNULL(SUM(IF(IFNULL(t.refund_amount, 0) > 0 and IFNULL(t.refund_amount, 0) > IFNULL(t.gift_amount, 0),
                             IFNULL(t.recharge_amount, 0) - (IFNULL(t.refund_amount, 0) - IFNULL(t.gift_amount, 0)),
                             IFNULL(t.recharge_amount, 0))), 0)
                                                                                            rechargeAmount,
               IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)                                   refundAmount,
               IFNULL(count(t.pay_type_id), 0)                                              num,
               IFNULL(SUM(IFNULL(t.change_amount, 0)), 0)                                   changeAmount
        FROM pos_cash_payment t
                 JOIN pos_cash p ON p.id = t.cash_id
                 LEFT JOIN base_payment_type bpt on t.pay_type_id = bpt.id
            ${ew.customSqlSegment}
        GROUP BY
            t.pay_type_id

    </select>
    <select id="selectByPayTypeCashCount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT (IFNULL(SUM(IFNULL(t.amount, 0)), 0)
            - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)
            - IFNULL(SUM(IFNULL(t.change_amount, 0)), 0))                                   amount,
               (IFNULL(SUM(IFNULL(t.amount, 0)), 0)
                   - SUM(if(t.is_prepaid is not null and t.is_prepaid = 1, IFNULL(t.refund_amount, 0),
                            0))
                   - IFNULL(SUM(IFNULL(t.change_amount, 0)), 0))                            payment,
               SUM(ROUND(IF(bpt.fee_rate is null, 0,
                            (IFNULL(t.amount, 0) - ifnull(t.refund_amount, 0) - IFNULL(t.change_amount, 0)) *
                            bpt.fee_rate /
                            100), 2))                                                       feePayment,
               t.pay_type_id   AS                                                           field,
               min(t.pay_name) as                                                           name,
               IFNULL(SUM(IF(IFNULL(t.refund_amount, 0) > 0 and IFNULL(t.refund_amount, 0) > IFNULL(t.gift_amount, 0),
                             0, IFNULL(t.gift_amount, 0) - IFNULL(t.refund_amount, 0))), 0) giftAmount,
               IFNULL(SUM(IF(IFNULL(t.refund_amount, 0) > 0 and IFNULL(t.refund_amount, 0) > IFNULL(t.gift_amount, 0),
                             IFNULL(t.recharge_amount, 0) - (IFNULL(t.refund_amount, 0) - IFNULL(t.gift_amount, 0)),
                             IFNULL(t.recharge_amount, 0))), 0)
                                                                                            rechargeAmount,
               IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)                                   refundAmount,
               IFNULL(count(DISTINCT t.cash_id), 0)                                              num,
               IFNULL(SUM(IFNULL(t.change_amount, 0)), 0)                                   changeAmount
        FROM pos_cash_payment t
                 JOIN pos_cash p ON p.id = t.cash_id
                 LEFT JOIN base_payment_type bpt on t.pay_type_id = bpt.id
            ${ew.customSqlSegment}
        GROUP BY
            t.pay_type_id

    </select>

    <select id="selectByMonth" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT DATE_FORMAT(p.complete_time, '%Y年%m月')                                            field,
               ROUND(IFNULL(SUM(IFNULL(t.amount, 0)), 0)
                         - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)
                         - IFNULL(SUM(IFNULL(t.change_amount, 0)), 0), 2)                          amount,
               ROUND(IFNULL(SUM(IFNULL(t.amount, 0)), 0)
                         - IFNULL(SUM(IFNULL(t.change_amount, 0)), 0), 2)                          payment,
               IFNULL(SUM(IF(IFNULL(t.refund_amount, 0) > 0 and IFNULL(t.refund_amount, 0) > IFNULL(t.gift_amount, 0),
                             0, IFNULL(t.gift_amount, 0) - IFNULL(t.refund_amount, 0))), 0)        giftAmount,
               IFNULL(SUM(IF(IFNULL(t.refund_amount, 0) > 0 and IFNULL(t.refund_amount, 0) > IFNULL(t.gift_amount, 0),
                             IFNULL(t.recharge_amount, 0) - (IFNULL(t.refund_amount, 0) - IFNULL(t.gift_amount, 0)),
                             IFNULL(t.recharge_amount, 0))), 0)                                    rechargeAmount,
               IFNULL(SUM(IF(IFNULL(t.refund_amount, 0) > 0, 0, IFNULL(t.recharge_amount, 0))), 0) rechargeAmount,
               IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)                                          refundAmount,
               IFNULL(SUM(IFNULL(t.change_amount, 0)), 0)                                          changeAmount
        FROM pos_cash_payment t
                 JOIN pos_cash p ON p.id = t.cash_id
            ${ew.customSqlSegment}
        GROUP BY
            field
    </select>

    <select id="selectCountByPayType" resultType="java.lang.Long">
        select SUM(a.count)
        from (SELECT count(distinct p.id) as `count`
              FROM pos_cash_payment t
                       JOIN pos_cash p ON p.id = t.cash_id
                  ${ew.customSqlSegment}
              GROUP BY
                  p.id) a
    </select>
    <select id="selectRefundCountByPayType" resultType="java.lang.Long">
        select SUM(a.count)
        from (SELECT count(p.id) as `count`
              FROM pos_cash_refund_payment t
                       JOIN pos_cash p ON p.id = t.cash_id
                  ${ew.customSqlSegment}
              GROUP BY
                  p.id) a
    </select>
    <select id="selectRefundByPayType" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT (IFNULL(SUM(IFNULL(t.amount, 0)), 0)
            - IFNULL(SUM(IFNULL(t.change_amount, 0)), 0))        amount,
               (IFNULL(SUM(IFNULL(t.amount, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.change_amount, 0)), 0)) payment,
               t.pay_type_id   AS                                field,
               max(t.pay_name) as                                name,
               IFNULL(SUM(IFNULL(t.gift_amount, 0)), 0)          giftAmount,
               IFNULL(SUM(IFNULL(t.recharge_amount, 0)), 0)      rechargeAmount,
               IFNULL(count(t.pay_type_id), 0)                   num,
               IFNULL(SUM(IFNULL(t.change_amount, 0)), 0)        changeAmount
        FROM pos_cash_refund_payment t
                 JOIN pos_cash p ON p.id = t.cash_id
            ${ew.customSqlSegment}
        GROUP BY
            t.pay_type_id

    </select>
    <select id="selectAmountByCashId" resultType="java.lang.Long">
        SELECT count(1)
        from (SELECT t.cash_id
              FROM pos_cash_refund_payment t
                       JOIN pos_cash p ON p.id = t.cash_id
                  ${ew.customSqlSegment}
              GROUP BY
                  t.cash_id) a

    </select>
    <select id="selectByDiscountType" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT (0 - IFNULL(SUM(IFNULL(t.price_change, 0)), 0)) amount,
               t.discount_type      AS                         field,
               min(t.discount_type) as                         name,
               IFNULL(count(t.id), 0)                          num
        FROM pos_cash_discount_detail t
                 JOIN pos_cash p ON p.id = t.pos_cash_id
            ${ew.customSqlSegment}
        GROUP BY
            t.discount_type

    </select>
    <select id="selectMemAmount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT IF(member_id, 1, 0) AS                          field,
               IFNULL(COUNT(id), 0)                            num,
               IFNULL(SUM(IFNULL(amount, 0)), 0) - IFNULL(SUM(IFNULL(refund_amount, 0)), 0)   amount,
               (IFNULL(SUM(IFNULL(payment, 0)), 0) - IFNULL(SUM(IFNULL(refund_amount, 0)), 0)) payment,
               IFNULL(SUM(IFNULL(discount_amount, 0)), 0)      discountAmount,
               IFNULL(SUM(IFNULL(refund_amount, 0)), 0)        refundAmount,
               IFNULL(SUM(IFNULL(gift_amount, 0)), 0)          giftAmount,
               IFNULL(SUM(IFNULL(paid, 0)), 0)                 paid,
               IFNULL(SUM(IFNULL(unpaid, 0)), 0)               unpaid,
               bill_type           as                          billType,
               (CASE
                    WHEN type_ = 3 THEN
                        IFNULL(SUM(IFNULL(payment, 0)), 0)
                    ELSE 0
                   END)            AS                          rechargeAmount
        FROM pos_cash ${ew.customSqlSegment}
        GROUP BY field, bill_type
    </select>

    <select id="selectByTable" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT ${field}                                                                     AS field,
               IFNULL(COUNT(p.id), 0)                                                          num,
               IFNULL(SUM(IFNULL(p.amount, 0)), 0)                                             amount,
               (IFNULL(SUM(IFNULL(p.payment, 0)), 0)
                   - IFNULL(SUM(IFNULL(p.refund_amount, 0)), 0))                               payment,
               IFNULL(SUM(IFNULL(p.discount_amount, 0)), 0)                                    discountAmount,
               IFNULL(SUM(IFNULL(p.refund_amount, 0)), 0)                                      refundAmount,
               IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0)                                        giftAmount,
               IFNULL(SUM(IFNULL(p.paid, 0)), 0)                                               paid,
               IFNULL(SUM(IFNULL(p.unpaid, 0)), 0)                                             unpaid,
               p.bill_type                                                                  AS billType,
               (CASE WHEN p.type_ = 3 THEN IFNULL(SUM(IFNULL(p.payment, 0)), 0) ELSE 0 END) AS rechargeAmount
        FROM pos_cash p
                 join base_table_info t on t.id = p.table_id and t.delete_flag = 0
            ${ew.customSqlSegment}
        GROUP BY
            field,
            p.bill_type

    </select>
    <select id="selectTopByTable" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT ${field}                                                                     AS field,
               IFNULL(COUNT(p.id), 0)                                                          num,
               IFNULL(SUM(IFNULL(p.amount, 0)), 0)                                             amount,
               IFNULL(SUM(IFNULL(p.payment, 0)), 0)                                            payment,
               IFNULL(SUM(IFNULL(p.discount_amount, 0)), 0)                                    discountAmount,
               IFNULL(SUM(IFNULL(p.refund_amount, 0)), 0)                                      refundAmount,
               IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0)                                        giftAmount,
               IFNULL(SUM(IFNULL(p.paid, 0)), 0)                                               paid,
               IFNULL(SUM(IFNULL(p.unpaid, 0)), 0)                                             unpaid,
               p.bill_type                                                                  AS billType,
               (CASE WHEN p.type_ = 3 THEN IFNULL(SUM(IFNULL(p.payment, 0)), 0) ELSE 0 END) AS rechargeAmount
        FROM pos_cash p
                 join base_table_info t on t.id = p.table_id and t.delete_flag = 0
            ${ew.customSqlSegment}
        GROUP BY
            field,
            p.bill_type
        ORDER BY num
            limit 5
    </select>
    <select id="selectByProduct" resultType="top.kx.kxss.app.vo.result.cash.StatisResultVO">
        SELECT ${field}                                            AS     field,
               ${fieldName}                                        AS     fieldName,
               IFNULL(SUM(IFNULL(IF
                                 (t.cash_thail_id IS NOT NULL AND t.cash_thail_id != '', 0, t.orgin_price), 0)),
                      0) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)    amount,
               IFNULL(SUM(IFNULL(IF
                                 (t.cash_thail_id IS NOT NULL AND t.cash_thail_id != '', 0, t.amount), 0)), 0) -
               IFNULL(SUM(IFNULL(IF
                                 (t.cash_thail_id IS NOT NULL AND t.cash_thail_id != '', 0, t.assessed_amount), 0)),
                      0) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)  payment,
               IFNULL(SUM(IFNULL(IF
                                 (t.cash_thail_id IS NOT NULL AND t.cash_thail_id != '', 0, t.discount_amount), 0)),
                      0) + IFNULL(SUM(IFNULL(IF
                                             (t.cash_thail_id IS NOT NULL AND t.cash_thail_id != '', 0,
                                              t.assessed_amount), 0)), 0) discountAmount,
               IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0)                   giftAmount,
               p.bill_type                                         as     billType,
               Min(c.name)                                         as     cateName,
               count(${field})                                     as     num,
               IFNULL(SUM(IFNULL(t.num, 0)), 0)
                   -IFNULL(SUM(IFNULL(t.refund_num, 0)), 0)  as     salesNum,
               IFNULL(pro.buying_price, 0)                         as     costPrice,
               sum(IFNULL(pro.buying_price, 0) * (IFNULL(t.num, 0)-IFNULL(t.refund_num, 0))) as     totalCostPrice
        FROM pos_cash_product t
                 JOIN pos_cash p ON p.id = t.cash_id and t.delete_flag = 0
                 LEFT JOIN base_product pro on pro.id = t.product_id
                 LEFT JOIN base_product_category c on c.id = pro.category_id
            ${ew.customSqlSegment}
        GROUP BY
            field,
            bill_type
        order by num desc

    </select>
    <select id="selectByService" resultType="top.kx.kxss.app.vo.result.cash.StatisResultVO">
        SELECT ${field}          AS                                                               field,
               ${fieldName}      AS                                                               fieldName,
               IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0) as amount,
               IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0) as  payment,
               IFNULL(IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0) - SUM(IFNULL(t.amount, 0)), 0)
                   + IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)                                 discountAmount,
               IFNULL(SUM(IFNULL(t.duration, 0)), 0)                                              duration,
               IFNULL(
                       sum(CASE
                               WHEN t.cycle IS NULL
                                   OR t.cycle = '' THEN
                                   0
                               WHEN instr(t.cycle, '元/') <![CDATA[ <= ]]> 0 THEN 0
                               WHEN instr(t.cycle, '元/小时') > 0 THEN
                                   (IFNULL(t.cycle_num, 0) * 60)
                               WHEN instr(t.cycle, '元/小时') <![CDATA[ <= ]]> 0 AND instr(t.cycle, '小时') > 0 THEN
                                   (
                                       IFNULL(t.cycle_num, 0) * 60 *
                                       SUBSTRING_INDEX(SUBSTRING_INDEX(t.cycle, '/', -1), '小时', 1))
                               WHEN instr(t.cycle, '元/分钟') > 0 THEN
                                   IFNULL(t.cycle_num, 0)
                               WHEN instr(t.cycle, '元/分钟') <![CDATA[ <= ]]> 0 AND instr(t.cycle, '分钟') > 0 THEN
                                   (
                                       IFNULL(t.cycle_num, 0) *
                                       SUBSTRING_INDEX(SUBSTRING_INDEX(t.cycle, '/', -1), '分钟', 1))
                               ELSE 0
                           END),
                       0
               )                 as                                                               chargingDuration,
               IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0)                                           giftAmount,
               IFNULL(SUM(IFNULL(t.cycle_num, 0)), 0)                                             cycleNum,
               p.bill_type       as                                                               billType,
               max(pro.name)     as                                                               serviceName,
               e.name     as                                                               name,
               e.group_id        as                                                            groupId,
               max(e.number)     as                                                               number,
               max(t.cycle)      as                                                               cycle,
               max(t.clock_type) as                                                               clock_type,
               max(e.number)     as                                                               number,
               count(${field})   as                                                               `num`,
               bo.name           AS                                                               org
        FROM pos_cash_service t
                 JOIN pos_cash p ON p.id = t.cash_id
                 LEFT JOIN base_service pro on pro.id = t.service_id
                 LEFT JOIN base_org bo on pro.created_org_id = bo.id
                 LEFT JOIN base_employee e on e.id = t.employee_id
            ${ew.customSqlSegment}
        GROUP BY
            field,
            bill_type

    </select>

    <select id="selectStatisServiceList" resultType="top.kx.kxss.app.vo.result.cash.StatisResultVO">
        SELECT ${field}          AS                                                               field,
               ${fieldName}      AS                                                               fieldName,
               IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0) as amount,
               round(IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0), 2) as  payment,
               round(IFNULL(IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0) - SUM(IFNULL(t.amount, 0)), 0)
                   + IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0), 2)                                discountAmount,
               IFNULL(SUM(IFNULL(t.duration, 0)), 0)                                              duration,
               IFNULL(
                       sum(CASE
                               WHEN t.cycle IS NULL
                                   OR t.cycle = '' THEN
                                   0
                               WHEN instr(t.cycle, '元/') <![CDATA[ <= ]]> 0 THEN 0
                               WHEN instr(t.cycle, '元/小时') > 0 THEN
                                   (IFNULL(t.cycle_num, 0) * 60)
                               WHEN instr(t.cycle, '元/小时') <![CDATA[ <= ]]> 0 AND instr(t.cycle, '小时') > 0 THEN
                                   (
                                       IFNULL(t.cycle_num, 0) * 60 *
                                       SUBSTRING_INDEX(SUBSTRING_INDEX(t.cycle, '/', -1), '小时', 1))
                               WHEN instr(t.cycle, '元/分钟') > 0 THEN
                                   IFNULL(t.cycle_num, 0)
                               WHEN instr(t.cycle, '元/分钟') <![CDATA[ <= ]]> 0 AND instr(t.cycle, '分钟') > 0 THEN
                                   (
                                       IFNULL(t.cycle_num, 0) *
                                       SUBSTRING_INDEX(SUBSTRING_INDEX(t.cycle, '/', -1), '分钟', 1))
                               ELSE 0
                           END),
                       0
               )                 as                                                               chargingDuration,
               IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0)                                           giftAmount,
               IFNULL(SUM(IFNULL(t.cycle_num, 0)), 0)                                             cycleNum,
               p.bill_type       as                                                               billType,
               max(pro.name)     as                                                               serviceName,
               max(e.number)     as                                                               number,
               max(t.cycle)      as                                                               cycle,
               max(t.clock_type) as                                                               clock_type,
               max(e.number)     as                                                               number,
               count(${field})   as                                                               `num`,
               count(distinct p.id)   as                                                               cashNum,
               bo.name           AS                                                               org
        FROM pos_cash_service t
                 JOIN pos_cash p ON p.id = t.cash_id
                 LEFT JOIN base_service pro on pro.id = t.service_id
                 LEFT JOIN base_org bo on pro.created_org_id = bo.id
                 LEFT JOIN base_employee e on e.id = t.employee_id
            ${ew.customSqlSegment}
        GROUP BY
            field
    </select>

    <select id="selectByServiceList" resultType="top.kx.kxss.app.vo.result.cash.StatisResultVO">
        SELECT ${field}             AS                                                            field,
               ${fieldName}         AS                                                            fieldName,
               IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0)- IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)     amount,
               IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)  payment,
               IFNULL(IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0) - SUM(IFNULL(t.amount, 0)), 0)
                   + IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)                                 discountAmount,
               IFNULL(SUM(IFNULL(t.duration, 0)), 0)                                              duration,
               IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0)                                           giftAmount,
               IFNULL(SUM(IFNULL(t.cycle_num, 0)), 0)                                             cycleNum,
               p.bill_type          as                                                            billType,
               max(pro.name)        as                                                            serviceName,
               max(e.number)        as                                                            number,
               e.name        as                                                            name,
               e.group_id        as                                                            groupId,
               max(t.cycle)         as                                                            cycle,
               IFNULL(
                       sum(CASE
                               WHEN t.cycle IS NULL
                                   OR t.cycle = '' THEN
                                   0
                               WHEN instr(t.cycle, '元/') <![CDATA[ <= ]]> 0 THEN 0
                               WHEN instr(t.cycle, '元/小时') > 0 THEN
                                   (IFNULL(t.cycle_num, 0) * 60)
                               WHEN instr(t.cycle, '元/小时') <![CDATA[ <= ]]> 0 AND instr(t.cycle, '小时') > 0 THEN
                                   (
                                       IFNULL(t.cycle_num, 0) * 60 *
                                       SUBSTRING_INDEX(SUBSTRING_INDEX(t.cycle, '/', -1), '小时', 1))
                               WHEN instr(t.cycle, '元/分钟') > 0 THEN
                                   IFNULL(t.cycle_num, 0)
                               WHEN instr(t.cycle, '元/分钟') <![CDATA[ <= ]]> 0 AND instr(t.cycle, '分钟') > 0 THEN
                                   (
                                       IFNULL(t.cycle_num, 0) *
                                       SUBSTRING_INDEX(SUBSTRING_INDEX(t.cycle, '/', -1), '分钟', 1))
                               ELSE 0
                           END),
                       0
               )                    as                                                            chargingDuration,
               max(t.clock_type)    as                                                            clock_type,
               count(${field})      as                                                            `num`,
               IFNULL(bo.name, '-') AS                                                            org
        FROM pos_cash_service t
                 JOIN pos_cash p ON p.id = t.cash_id
                 LEFT JOIN base_service pro on pro.id = t.service_id
                 LEFT JOIN base_org bo on pro.created_org_id = bo.id
                 LEFT JOIN base_employee e on e.id = t.employee_id
            ${ew.customSqlSegment}
        GROUP BY
            field,
            bill_type
    </select>
    <select id="selectDurationByService" resultType="top.kx.kxss.app.vo.result.cash.StatisResultVO">
        SELECT ${field}          AS                                                               field,
               ${fieldName}      AS                                                               fieldName,
               IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)       amount,
               IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)  payment,
               IFNULL(IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0) - SUM(IFNULL(t.amount, 0)), 0)
                   + IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)                                 discountAmount,
               IFNULL(SUM(IFNULL(t.duration, 0)), 0)                                              duration,
               IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0)                                           giftAmount,
               p.bill_type       as                                                               billType,
               max(pro.name)     as                                                               serviceName,
               max(e.number)     as                                                               number,
               max(t.cycle)      as                                                               cycle,
               max(t.clock_type) as                                                               clock_type,
               max(e.number)     as                                                               number,
               count(${field})   as                                                               `num`
        FROM pos_cash_service t
                 JOIN pos_cash p ON p.id = t.cash_id
                 LEFT JOIN base_service pro on pro.id = t.service_id
                 LEFT JOIN base_service_category c on c.id = pro.category_id
                 LEFT JOIN base_employee e on e.id = t.employee_id
            ${ew.customSqlSegment}
        GROUP BY
            field,
            bill_type
            limit 8

    </select>
    <select id="getOrderDetails" resultType="top.kx.kxss.app.vo.result.cash.StatisOrderDetailsResultVO">
        (SELECT IFNULL(SUM(t.orgin_price), 0)                   amount,
                IFNULL(SUM(t.amount), 0)                        payment,
                IFNULL((SUM(t.orgin_price) - SUM(t.amount)), 0) discountAmount,
                IFNULL(i.NAME, '台费') AS                       `name`,
                IFNULL(SUM(duration), 0)                        num,
                '1'                    as                       `type`,
                '分钟'                 as                       unit
         FROM pos_cash_table t
                  JOIN base_table_info i
                       ON i.id = t.table_id
         WHERE t.delete_flag = 0
           AND t.`status` = 1
           and t.cash_id = #{posCashId}
         GROUP BY t.table_id)
        UNION ALL
        (SELECT IFNULL(SUM(t.orgin_price), 0)                   amount,
                IFNULL(SUM(t.amount), 0)                        payment,
                IFNULL((SUM(t.orgin_price) - SUM(t.amount)), 0) discountAmount,
                IFNULL(i.NAME, '商品') AS                       `name`,
                IFNULL(count(num), 0)                           num,
                '2'                    as                       `type`,
                i.measuring_unit       as                       unit
         FROM pos_cash_product t
                  JOIN base_product i
                       ON i.id = t.product_id
         WHERE t.delete_flag = 0
           and t.cash_id = #{posCashId}
         GROUP BY t.product_id)
        UNION ALL
        (SELECT IFNULL(SUM(t.orgin_price), 0)                   amount,
                IFNULL(SUM(t.amount), 0)                        payment,
                IFNULL((SUM(t.orgin_price) - SUM(t.amount)), 0) discountAmount,
                IFNULL(i.real_name, '服务') AS                  `name`,
                IFNULL(SUM(duration), 0)                        num,
                '3'                         as                  `type`,
                '分钟'                      as                  unit
         FROM pos_cash_service t
                  JOIN base_employee i
                       ON i.id = t.employee_id
         WHERE t.delete_flag = 0
           and t.cash_id = #{posCashId}
         GROUP BY t.employee_id)
    </select>
    <select id="getActualPayment" resultType="top.kx.kxss.app.vo.result.cash.StatisActualPaymentResultVO">
        SELECT pay_type_id            as payName,
               IFNULL(sum(amount), 0) as amount,
               min(pay_name)          as name
        FROM pos_cash_payment
        WHERE delete_flag = 0
          AND `status` = 2
          and pay_name is not null
          and cash_id = #{posCashId}
        GROUP BY pay_type_id
    </select>
    <select id="cashTags" resultType="top.kx.kxss.app.vo.result.cash.PosCashTagsResultVO">
        SELECT p.id AS id,
               concat_ws(
                       ',',
                       IF
                       (MAX(p.member_id), '0', NULL),
                       IF(min(s.delete_flag) = 0, IF(MAX(s.id), '1', NULL), NULL),
                       IF(min(c.delete_flag) = 0, IF(MAX(c.id), '2', NULL), NULL),
                       IF(max(p.is_pack_field) = 1, IF(MAX(p.id), '3', NULL), NULL)
               )    AS tags
        FROM pos_cash p
                 LEFT JOIN pos_cash_product c ON c.cash_id = p.id
            AND c.delete_flag = 0
                 LEFT JOIN pos_cash_service s ON s.cash_id = p.id
            AND p.delete_flag = 0
            ${ew.customSqlSegment}
        GROUP BY p.id
    </select>

    <select id="sensitiveCount" resultType="java.lang.Long">
        SELECT count(1)
        FROM base_biz_log log
                 inner join pos_cash p ON log.source_id = p.id
            AND log.delete_flag = 0
            ${ew.customSqlSegment}
    </select>
    <select id="rechargeCount" resultType="java.lang.Long">
        select count(a.member_id)
        from (SELECT member_id
              FROM pos_cash ${ew.customSqlSegment}
              GROUP BY member_id) a
    </select>
    <select id="sensitiveList" resultType="top.kx.kxss.wxapp.vo.result.statistics.SensitiveVO">
        select s.key              as name,
               s.key              as `key`,
               count(s.source_id) as `value`,
               'ORDER'            as `type`
        from (select b.biz_module as name,
                     b.biz_module as `key`,
                     b.source_id
              FROM base_biz_log b
                       inner join pos_cash p on p.id = b.source_id
                  ${ew.customSqlSegment}) s
        group by s.key
    </select>
    <select id="sensitiveMemberList" resultType="top.kx.kxss.wxapp.vo.result.statistics.SensitiveVO">
        select s.key              as name,
               s.key              as `key`,
               count(s.source_id) as `value`,
               'MEMBER'           as `type`
        from (select b.biz_module as name,
                     b.biz_module as `key`,
                     b.source_id
              FROM base_biz_log b
                       inner join member_info p on p.id = b.source_id
                  ${ew.customSqlSegment}) s
        group by s.key
    </select>
    <select id="sensitiveMemberBalanceList" resultType="top.kx.kxss.wxapp.vo.result.statistics.SensitiveVO">
        select s.key              as name,
               s.key              as `key`,
               count(s.source_id) as `value`,
               'BALANCE_CHANGE'   as `type`
        from (select b.biz_module as name,
                     b.biz_module as `key`,
                     b.source_id
              FROM base_biz_log b
                       inner join member_balance_change p on p.id = b.source_id
                  ${ew.customSqlSegment}) s
        group by s.key
    </select>
    <select id="selectThailAmount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        select t.thail_id   as                                    field,
               t.thail_name as                                    `name`,
               IFNULL(COUNT(t.thail_id), 0)                       num,
               IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0)           amount,
               IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0) payment,
               IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0)       discountAmount,
               IFNULL(SUM(IFNULL(thail.cost_price, 0)), 0)        profitAmount,
               IFNULL(t.group_buy_type, 0)                        groupBuyType
        FROM pos_cash_thail t
                 inner join pos_cash p on p.id = t.cash_id
                 left join base_thail thail on thail.id = t.thail_id
            ${ew.customSqlSegment}
        GROUP BY concat(t.thail_name, '_', t.amount, '_', IFNULL(t.group_buy_type, 0))
    </select>


    <select id="selectTableTypeAmount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        select bd.key_      as                                                                    field,
               min(bd.name) as                                                                    `name`,
               IFNULL(COUNT(t.table_id), 0)                                                       num,
               IFNULL(SUM(IFNULL(t.duration, 0)), 0)                                              duration,
               IFNULL(SUM(IFNULL(t.amount, 0)), 0)                                                amount,
               IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0) payment,
               IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0)                                       discountAmount
        FROM pos_cash_table t
                 inner join pos_cash p on p.id = t.cash_id
                 left join base_table_info bti on bti.id = t.table_id
                 left join base_dict bd
                           on bti.table_type = bd.key_ and bd.parent_key = 'BASE_TABLE_TYPE' and bd.delete_flag = 0
            ${ew.customSqlSegment}
        GROUP BY bti.table_type
    </select>

    <select id="selectServiceAmount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        select bs.id        as                                                                    field,
               min(bs.name) as                                                                    `name`,
               IFNULL(COUNT(t.service_id), 0)                                                     num,
               IFNULL(SUM(IFNULL(t.duration, 0)), 0)                                              duration,
               IFNULL(SUM(IFNULL(t.amount, 0)), 0)                                                amount,
               IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0) payment,
               IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0)                                       discountAmount
        FROM pos_cash_service t
                 inner join pos_cash p on p.id = t.cash_id
                 left join base_service bs on bs.id = t.service_id
            ${ew.customSqlSegment}
        GROUP BY t.service_id
    </select>

    <select id="selectName" resultType="java.lang.String">
        select a.name
        from (select product_id, min(product_name) as name, SUM(num) as num
              from pos_cash_product ${ew.customSqlSegment}
              GROUP BY product_id
              ORDER BY num
                      desc limit 1) a
    </select>
    <select id="getAmountByPaymentType" resultType="java.math.BigDecimal">
        select IFNULL(SUM(IFNULL(t.amount, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0) amount
        FROM pos_cash_payment t
                 inner join pos_cash p on p.id = t.cash_id and t.delete_flag = 0
            ${ew.customSqlSegment}
        GROUP BY t.pay_type_id limit 1
    </select>
    <select id="selectAmountByTable" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)       amount,
               IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)  payment,
               IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0) +
               IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)                                       discountAmount,
               IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0)                                           giftAmount,
               IFNULL(SUM(IFNULL(p.paid, 0)), 0)                                                  paid,
               IFNULL(SUM(IFNULL(p.unpaid, 0)), 0)                                                unpaid,
               IFNULL(SUM(IFNULL(t.duration, 0)), 0)                                              duration,
               IFNULL(COUNT(p.id), 0)                                                             num,
               ${field}                  AS                                                       field,
               IFNULL(`table`.name, '-') AS                                                       name
        FROM pos_cash p
                 left JOIN pos_cash_table t ON p.id = t.cash_id
                 left join base_table_info `table` on `table`.id = t.table_id
            ${ew.customSqlSegment}
        GROUP BY ${field}
    </select>
    <select id="selectAmountByPower" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)       amount,
               IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)  payment,
               IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0) +
               IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)                                       discountAmount,
               IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0)                                           giftAmount,
               IFNULL(SUM(IFNULL(p.paid, 0)), 0)                                                  paid,
               IFNULL(SUM(IFNULL(p.unpaid, 0)), 0)                                                unpaid,
               IFNULL(SUM(IFNULL(t.duration, 0)), 0)                                              duration,
               IFNULL(COUNT(distinct p.id), 0)                                                             num,
               ${field}                  AS                                                       field,
               IFNULL(`table`.name, '-') AS                                                       name
        FROM pos_cash p
                 left JOIN pos_cash_power t ON p.id = t.cash_id
                 left join base_table_info `table` on `table`.id = t.table_id
            ${ew.customSqlSegment}
        GROUP BY ${field}
    </select>
    <select id="selectAccountInfo" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT IFNULL(SUM(IFNULL(info.amount, 0)), 0) amount,
               info.pay_type_id AS                    field,
               max(p.`name`)    as                    name,
               0                                      giftAmount,
               0                                      rechargeAmount,
               0                                      refundAmount,
               IFNULL(count(info.pay_type_id), 0)     num,
               0                                      changeAmount
        FROM base_accounting_info info
                 LEFT JOIN base_payment_type p ON p.id = info.pay_type_id
            ${ew.customSqlSegment}
        GROUP BY
            info.pay_type_id
    </select>
    <select id="selectOperateAmount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT DATE_FORMAT(complete_time, '%m/%d') AS                                          field,
               IFNULL(COUNT(id), 0)                                                            num,
               IFNULL(SUM(IFNULL(amount, 0)), 0)                                               amount,
               (IFNULL(SUM(IFNULL(payment, 0)), 0) - IFNULL(SUM(IFNULL(refund_amount, 0)), 0)) payment,
               IFNULL(SUM(IFNULL(discount_amount, 0)), 0)                                      discountAmount,
               IFNULL(SUM(IFNULL(refund_amount, 0)), 0)                                        refundAmount,
               IFNULL(SUM(IFNULL(gift_amount, 0)), 0)                                          giftAmount,
               IFNULL(SUM(IFNULL(paid, 0)), 0)                                                 paid,
               IFNULL(SUM(IFNULL(unpaid, 0)), 0)                                               unpaid,
               (CASE
                    WHEN type_ = 3 THEN
                        IFNULL(SUM(IFNULL(payment, 0)), 0)
                    ELSE 0
                   END)                            AS                                          rechargeAmount
        FROM pos_cash ${ew.customSqlSegment}
        GROUP BY DATE_FORMAT(complete_time, '%m/%d')
    </select>
    <select id="selectTimesByTable" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        select count(p.id)                           as num,
               ${field}                              as field,
               IFNULL(sum(IFNULL(t.duration, 0)), 0) as duration
        from pos_cash_table t
                 inner join pos_cash p on p.id = t.cash_id
                 inner join base_table_info `table` on `table`.id = t.table_id
            ${ew.customSqlSegment}
        GROUP BY ${field}
    </select>
    <select id="selectBuyCardAmount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        select t.card_id   as                               field,
               min(t.name) as                               `name`,
               IFNULL(COUNT(t.card_id), 0)                  num,
               IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0)     amount,
               IFNULL(SUM(IFNULL(t.amount, 0)), 0)          payment,
               IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0) discountAmount
        FROM pos_cash_card t
                 inner join pos_cash p on p.id = t.cash_id
            ${ew.customSqlSegment}
        GROUP BY t.card_id

    </select>
    <select id="selectRefundAmount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT IFNULL(COUNT(id), 0)                                                            num,
               IFNULL(SUM(IFNULL(amount, 0)), 0) - IFNULL(SUM(IFNULL(refund_amount, 0)), 0)    amount,
               (IFNULL(SUM(IFNULL(payment, 0)), 0) - IFNULL(SUM(IFNULL(refund_amount, 0)), 0)) payment,
               IFNULL(SUM(IFNULL(discount_amount, 0)), 0)                                      discountAmount,
               IFNULL(SUM(IFNULL(refund_amount, 0)), 0)                                        refundAmount,
               IFNULL(SUM(IFNULL(gift_amount, 0)), 0)                                          giftAmount
        FROM pos_cash ${ew.customSqlSegment}
    </select>
    <select id="selectStaffRecharge"
            resultType="top.kx.kxss.wxapp.vo.result.statistics.recharge.StatisStaffRechargeResultVO">
        select IFNULL(SUM(IFNULL(p.payment, 0)), 0)                                            as amount,
               IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0)                                        as giftAmount,
               IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0) + IFNULL(SUM(IFNULL(p.payment, 0)), 0) as storedAmount,
               count(p.id)                                                                     as num,
               IFNULL(p.employee_id, 0)                                                        as employeeId,
               IFNULL(e.real_name, '未归属')                                                   as employeeName
        from pos_cash p
                 left join base_employee e on e.id = p.employee_id
            ${ew.customSqlSegment}
    </select>

    <select id="durationRanking" resultType="top.kx.kxss.wxapp.vo.result.statistics.StatisServiceRankingResultVO">
        select IFNULL(IF(be.delete_flag = 1, concat(be.real_name, '(已删除)'), be.real_name), '-') as name,
               be.group_id                                                                        as groupId,
               be.id                                                                        as employeeId,
        ifnull(sum(ifnull(t.duration, 0)), 0) as duration,
        IFNULL(
        sum(CASE
        WHEN t.cycle IS NULL
        OR t.cycle = '' THEN
        0
        WHEN instr( t.cycle, '元/' ) <![CDATA[ <= ]]> 0 THEN 0 WHEN instr( t.cycle, '元/小时' ) > 0 THEN
        ( IFNULL( t.cycle_num, 0 ) * 60 )
        WHEN instr( t.cycle, '元/小时' ) <![CDATA[ <= ]]> 0 AND instr( t.cycle, '小时' ) > 0 THEN
        (
        IFNULL( t.cycle_num, 0 ) * 60 * SUBSTRING_INDEX( SUBSTRING_INDEX( t.cycle, '/', -1 ), '小时', 1 ))
        WHEN instr( t.cycle, '元/分钟' ) > 0 THEN
        IFNULL( t.cycle_num, 0 )
        WHEN instr( t.cycle, '元/分钟' ) <![CDATA[ <= ]]> 0 AND instr( t.cycle, '分钟' ) > 0 THEN
        (
        IFNULL( t.cycle_num, 0 ) * SUBSTRING_INDEX( SUBSTRING_INDEX( t.cycle, '/', -1 ), '分钟', 1 )) ELSE 0
        END),
        0
        ) as chargingDuration,
        cf.url as avatarFile
        FROM pos_cash_service t
        LEFT JOIN pos_cash p ON p.id = t.cash_id
        LEFT JOIN base_employee be on t.employee_id = be.id
        LEFT JOIN com_file cf on cf.id = be.photo_id
        where p.delete_flag = 0
        and t.delete_flag = 0
        AND p.bill_type IN ('0', '3', '4')
        AND p.bill_state IN ('2', '5')
        and t.status = '1'
        <if test="model.startDate != null and model.startDate != ''">
            and p.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and instr(be.real_name, #{model.keyword})
        </if>
        <if test="model.groupId != null">
            and be.group_id = #{model.groupId}
        </if>
        <if test="model.groupIds != null and model.groupIds.size() > 0">
            and be.group_id in
            <foreach collection="model.groupIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.clockTypes != null and model.clockTypes.size() > 0">
            and t.clock_type in
            <foreach collection="model.clockTypes" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.serviceIds != null and model.serviceIds.size() > 0">
            and t.service_id in
            <foreach collection="model.serviceIds" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and p.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        group by t.employee_id
        <choose>
            <when test="model.sort != null and model.sort !='' and model.sort == 'duration'">
                order by duration desc, t.employee_id
            </when>
            <when test="model.sort != null and model.sort !='' and model.sort == 'chargingDuration'">
                order by chargingDuration desc, t.employee_id
            </when>
            <otherwise>
                order by duration desc, t.employee_id
            </otherwise>
        </choose>
    </select>
    <select id="selectPageMemberRanking"
            resultType="top.kx.kxss.wxapp.vo.result.statistics.StatisPerformanceMemberResultVO">
        SELECT IFNULL(m.`name`, '非会员') as `name`,
               IFNULL(g.`name`, '非会员') AS gradeName,
               sum(pro.duration)          AS duration,
               m.mobile                   AS mobile,
               m.last_consume_time        AS lastConsumeTime,
               IFNULL(
                       sum(CASE
                               WHEN pro.cycle IS NULL
                                   OR pro.cycle = '' THEN
                                   0
                               WHEN instr(pro.cycle, '元/') <![CDATA[ <= ]]> 0 THEN 0
                               WHEN instr(pro.cycle, '元/小时') > 0 THEN
                                   (IFNULL(pro.cycle_num, 0) * 60)
                               WHEN instr(pro.cycle, '元/小时') <![CDATA[ <= ]]> 0 AND instr(pro.cycle, '小时') > 0 THEN
                                   (
                                       IFNULL(pro.cycle_num, 0) * 60 *
                                       SUBSTRING_INDEX(SUBSTRING_INDEX(pro.cycle, '/', -1), '小时', 1))
                               WHEN instr(pro.cycle, '元/分钟') > 0 THEN
                                   IFNULL(pro.cycle_num, 0)
                               WHEN instr(pro.cycle, '元/分钟') <![CDATA[ <= ]]> 0 AND instr(pro.cycle, '分钟') > 0 THEN
                                   (
                                       IFNULL(pro.cycle_num, 0) *
                                       SUBSTRING_INDEX(SUBSTRING_INDEX(pro.cycle, '/', -1), '分钟', 1))
                               ELSE 0
                           END),
                       0
               )                          as cycleDuration,
               count(pro.id)              AS times,
               CONVERT(
                       sum(pro.discount_amount + pro.assessed_amount),
                       DECIMAL (10, 2))   AS discountAmount,
               sum(pro.amount)            AS orderPayment,
               sum(pro.orgin_price)       AS orderAmount,
               '-'                        as number
        FROM pos_cash_service pro
                 INNER JOIN pos_cash p ON p.id = pro.cash_id
                 LEFT JOIN member_info m ON m.id = p.member_id
                 LEFT JOIN member_grade g ON g.id = m.grade_id
            ${ew.customSqlSegment}
    </select>
    <select id="selectPosCashByEmployeeIdAndServiceId" resultType="top.kx.kxss.pos.vo.service.ServiceTableResultVO">
        SELECT
        CONCAT(s.employee_id,'_',s.service_id) AS employeeId,
        group_concat( DISTINCT p.table_name SEPARATOR ',' ) AS tableName
        FROM
        pos_cash_service s
        INNER JOIN pos_cash p ON p.id = s.cash_id
        WHERE
        p.type_ = 0
        AND s.`status` = 0
        AND p.bill_state IN ( '0', '3' )
        AND p.bill_type = '0'
        AND p.delete_flag = 0
        AND s.delete_flag = 0
        <if test="employeeIdList != null and employeeIdList.size() > 0">
            and s.employee_id in
            <foreach collection="employeeIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY
        CONCAT(s.employee_id,'_',s.service_id)
    </select>

    <select id="consumeSum" resultType="top.kx.kxss.wxapp.vo.result.statistics.StatisConsumeResultVO">
        select sum(ifnull(pc.payment, 0)) as payment,
        sum(ifnull(pc.table_amount, 0)) as tableAmount,
        sum(ifnull(pc.product_amount, 0)) as productAmount,
        sum(ifnull(pc.service_amount, 0)) as serviceAmount,
        sum(ifnull(pc.thail_amount, 0)) as thailAmount
        from pos_cash pc
        left join member_info mi on pc.member_id = mi.id
        WHERE pc.bill_state in ('2', '5')
        and pc.bill_type not in ('1', '2')
        and pc.delete_flag = 0
        and pc.member_id is not null
        and pc.type_ != '3'
        <if test="model.gradeId != null">
            and mi.grade_id = #{model.gradeId}
        </if>
        <if test="model.gradeIds != null and model.gradeIds.size() > 0">
            and mi.grade_id in
            <foreach item="gradeId" collection="model.gradeIds" open="(" separator="," close=")">
                #{gradeId}
            </foreach>
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and (instr(mi.name, #{model.keyword}) or instr(mi.mobile, #{model.keyword}))
        </if>
        <if test="model.startDate != null and model.startDate != ''">
            and pc.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and pc.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and pc.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
    </select>
    <select id="selectPosCashByEmployeeId" resultType="top.kx.kxss.pos.vo.service.ServiceTableResultVO">
        SELECT s.employee_id                                      AS employeeId,
               group_concat(DISTINCT p.table_name SEPARATOR ',' ) AS tableName
        FROM pos_cash_service s
                 INNER JOIN pos_cash p ON p.id = s.cash_id
        WHERE p.type_ = 0
          AND s.`status` = 0
          AND p.bill_state IN ('0', '3', '4')
          AND p.bill_type = '0'
          AND p.delete_flag = 0
          AND s.delete_flag = 0
        GROUP BY s.employee_id
    </select>
    <select id="selectPageResultVO" resultType="top.kx.kxss.app.vo.result.cash.PosCashResultVO">
        SELECT DISTINCT p.id                           as id,
                        p.type_                        as type,
                        t.`name`                       as tableName,
                        p.`code`                       as `code`,
                        p.`bill_date`                  as billDate,
                        p.`bill_state`                 as billState,
                        p.`bill_type`                  as billType,
                        p.`org_id`                     as orgId,
                        p.`employee_id`                as employeeId,
                        p.`created_emp`                as createdEmp,
                        p.`complete_emp`               as completeEmp,
                        p.`member_id`                  as memberId,
                        p.`table_id`                   as tableId,
                        p.`order_source`               as orderSource,
                        IFNULL(p.`amount`, 0)          as amount,
                        IFNULL(p.`discount_amount`, 0) as discountAmount,
                        IFNULL(p.`payment`, 0)         as payment,
                        IFNULL(p.`paid`, 0)            as paid,
                        IFNULL(p.`unpaid`, 0)          as unpaid,
                        IFNULL(p.`round_amount`, 0)    as roundAmount,
                        IFNULL(p.`refund_amount`, 0)   as refundAmount,
                        IFNULL(p.`product_amount`, 0)  as productAmount,
                        IFNULL(p.`service_amount`, 0)  as serviceAmount,
                        IFNULL(p.`thail_amount`, 0)    as thailAmount,
                        IFNULL(p.`table_amount`, 0)    as tableAmount,
                        IFNULL(p.`buy_card_amount`, 0) as buyCardAmount,
                        IFNULL(p.`power_amount`, 0) as powerAmount,
                        p.`remarks`                    as remarks,
                        p.`created_org_id`             as createdOrgId,
                        p.`source_id`                  as sourceId,
                        p.`chargeback_id`              as chargebackId,
                        t.`table_type`                 as tableType,
                        t.`table_area`                 as tableArea,
                        p.`created_time`               as createdTime,
                        p.`refund_time`               as refundTime,
                        p.`complete_time`              as completeTime
        FROM pos_cash p
                 LEFT JOIN member_info m ON p.member_id = m.id
                 LEFT JOIN pos_cash_commenter c ON c.cash_id = p.id
                 LEFT JOIN base_table_info t ON t.id = p.table_id
            ${ew.customSqlSegment}
    </select>

    <select id="tableDurationSum" resultType="java.lang.Integer">
        select sum(t.duration)
        from pos_cash_table t
                 inner join pos_cash p on t.cash_id = p.id
            ${ew.customSqlSegment}
    </select>

    <select id="tableDurationDetailSum" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT IFNULL(SUM(IFNULL(t.duration, 0)), 0) AS duration,
               IFNULL(SUM(CASE WHEN t.cash_thail_id IS NULL THEN IFNULL(t.duration, 0) ELSE 0 END), 0) AS thailDuration,
               IFNULL(SUM(CASE WHEN t.cash_thail_id IS NOT NULL THEN IFNULL(t.duration, 0) ELSE 0 END), 0) AS noThailDuration
        FROM pos_cash_table t
                 INNER JOIN pos_cash p ON t.cash_id = p.id
            ${ew.customSqlSegment}
    </select>

    <select id="selectProductAmount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0) - IFNULL(t.refund_amount, 0)), 0)                                                     amount,
               ROUND(IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)
                         - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0), 2) payment,
               round(IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0) + IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0),
                     2)                                                                                     discountAmount,
               IFNULL(SUM(IFNULL(t.profit_price, 0)), 0)                    profitPrice,
               IFNULL(SUM(IFNULL(p.paid, 0)), 0)                                                            paid,
               IFNULL(SUM(IFNULL(p.unpaid, 0)), 0)                                                          unpaid,
               IFNULL(sum(ifnull(t.num, 0) - ifnull(t.refund_num, 0)), 0)                                   productNum,
               IFNULL(COUNT(distinct p.id), 0)                                                              num,
               IFNULL(COUNT(distinct t.cash_id), 0)                                                         productOrderNum,
               IFNULL(COUNT(distinct CASE WHEN p.type_ = '1' THEN p.id END), 0)                             shoppingOrderNum,
               ROUND(IFNULL(sum(CASE WHEN p.type_ = '0' THEN (IFNULL(t.amount, 0) - IFNULL(t.refund_amount, 0)
                                                                  - IFNULL(t.assessed_amount, 0)) END), 0), 2)                                                                          tableProductAmount
        FROM pos_cash p
                 LEFT JOIN pos_cash_product t ON p.id = t.cash_id and t.delete_flag = 0
            ${ew.customSqlSegment}
    </select>

    <select id="selectTableAmount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT IFNULL(SUM(if(t.cash_thail_id IS null, IFNULL(t.orgin_price, 0), IFNULL(t.thail_assessed_amount, 0))), 0)                                                     amount,
               ROUND(IFNULL(SUM(if(t.cash_thail_id IS NULL, IFNULL(t.amount, 0), IFNULL(t.thail_assessed_amount, 0))), 0) - IFNULL(SUM(if(t.cash_thail_id IS NULL,IFNULL(t.assessed_amount, 0), 0)), 0) - sum(ifnull(t.refund_amount, 0)), 2) payment,
               ROUND(IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0) +
                     IFNULL(SUM(if(t.cash_thail_id is null, IFNULL(t.assessed_amount, 0), 0)), 0),
                     2)                                                                                     discountAmount,
               0                                           giftAmount,
               IFNULL(SUM(IFNULL(t.duration, 0)), 0)                                                        duration,
               IFNULL(COUNT(distinct p.id), 0)                                                              num,
               ROUND(IFNULL(SUM(CASE WHEN t.cash_thail_id IS NULL THEN IFNULL(t.orgin_price, 0) ELSE 0 END), 0), 2) noThailTableAmount,
               ROUND(IFNULL(SUM(CASE WHEN t.cash_thail_id IS NOT NULL THEN IFNULL(t.thail_assessed_amount, 0) ELSE 0 END), 0), 2) thailTableAmount,
               ROUND(IFNULL(SUM(CASE WHEN t.cash_thail_id IS NULL THEN IFNULL(t.amount, 0) - IFNULL(t.assessed_amount, 0) - ifnull(t.refund_amount, 0) ELSE 0 END), 0), 2) noThailTablePayment,
               ROUND(IFNULL(SUM(CASE WHEN t.cash_thail_id IS NOT NULL THEN IFNULL(t.thail_assessed_amount, 0) - IFNULL(t.refund_amount, 0) ELSE 0 END), 0), 2) thailTablePayment
        FROM pos_cash_table t
                 INNER JOIN pos_cash p ON p.id = t.cash_id
      --           LEFT JOIN pos_cash_payment_detail d
      --                     on t.id = d.source_id and t.cash_id = d.cash_id and d.delete_flag = 0 and
      --                        d.pay_biz_type = '1'
            ${ew.customSqlSegment}
    </select>

    <select id="selectTableAmountByTableType" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        select i.table_type                                           AS type,
               COUNT(DISTINCT p.id)                                   AS num,
               SUM(IFNULL(p.payment, 0) - IFNULL(p.refund_amount, 0)) AS payment
        FROM pos_cash p
                 LEFT JOIN base_table_info i ON p.table_id = i.id
            ${ew.customSqlSegment}
        GROUP BY i.table_type
    </select>

    <select id="selectTableDuration" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        select i.table_type    AS type,
               SUM(t.duration) AS duration
        FROM pos_cash_table t
                 LEFT JOIN pos_cash p ON t.cash_id = p.id
                 LEFT JOIN base_table_info i ON p.table_id = i.id
            ${ew.customSqlSegment}
        GROUP BY i.table_type
    </select>

    <select id="selectServiceAmountByEmployee" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0)                                                     amount,
               ROUND(IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0), 2) payment,
               ROUND(IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0) + IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0),
                     2)                                                                                     discountAmount,
               IFNULL(SUM(IFNULL(p.paid, 0)), 0)                                                            paid,
               IFNULL(SUM(IFNULL(t.duration, 0)), 0)                                                        duration,
               IFNULL(
                       sum(CASE
                               WHEN t.cycle IS NULL
                                   OR t.cycle = '' THEN
                                   0
                               WHEN instr(t.cycle, '元/') <![CDATA[ <= ]]> 0 THEN 0
                               WHEN instr(t.cycle, '元/小时') > 0 THEN
                                   (IFNULL(t.cycle_num, 0) * 60)
                               WHEN instr(t.cycle, '元/小时') <![CDATA[ <= ]]> 0 AND instr(t.cycle, '小时') > 0 THEN
                                   (
                                       IFNULL(t.cycle_num, 0) * 60 *
                                       SUBSTRING_INDEX(SUBSTRING_INDEX(t.cycle, '/', -1), '小时', 1))
                               WHEN instr(t.cycle, '元/分钟') > 0 THEN
                                   IFNULL(t.cycle_num, 0)
                               WHEN instr(t.cycle, '元/分钟') <![CDATA[ <= ]]> 0 AND instr(t.cycle, '分钟') > 0 THEN
                                   (
                                       IFNULL(t.cycle_num, 0) *
                                       SUBSTRING_INDEX(SUBSTRING_INDEX(t.cycle, '/', -1), '分钟', 1))
                               ELSE 0
                           END),
                       0
               ) as                                                                                         cycleDuration,
               IFNULL(SUM(IFNULL(p.unpaid, 0)), 0)                                                          unpaid,
               IFNULL(COUNT(distinct p.id), 0)                                                              num,
               IFNULL(COUNT(distinct t.employee_id), 0)                                                     employeeNum,
               t.employee_name                                                                              employeeName
        FROM pos_cash_service t
                 INNER JOIN pos_cash p ON p.id = t.cash_id
            ${ew.customSqlSegment}
        GROUP BY t.employee_id
    </select>

    <select id="selectOneServiceAmount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0)                                                     amount,
               ROUND(IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0), 2) payment,
               ROUND(IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0) + IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0),
                     2)                                                                                     discountAmount,
               IFNULL(SUM(IFNULL(p.paid, 0)), 0)                                                            paid,
               IFNULL(SUM(IFNULL(t.duration, 0)), 0)                                                        duration,
               IFNULL(
                       sum(CASE
                               WHEN t.cycle IS NULL
                                   OR t.cycle = '' THEN
                                   0
                               WHEN instr(t.cycle, '元/') <![CDATA[ <= ]]> 0 THEN 0
                               WHEN instr(t.cycle, '元/小时') > 0 THEN
                                   (IFNULL(t.cycle_num, 0) * 60)
                               WHEN instr(t.cycle, '元/小时') <![CDATA[ <= ]]> 0 AND instr(t.cycle, '小时') > 0 THEN
                                   (
                                       IFNULL(t.cycle_num, 0) * 60 *
                                       SUBSTRING_INDEX(SUBSTRING_INDEX(t.cycle, '/', -1), '小时', 1))
                               WHEN instr(t.cycle, '元/分钟') > 0 THEN
                                   IFNULL(t.cycle_num, 0)
                               WHEN instr(t.cycle, '元/分钟') <![CDATA[ <= ]]> 0 AND instr(t.cycle, '分钟') > 0 THEN
                                   (
                                       IFNULL(t.cycle_num, 0) *
                                       SUBSTRING_INDEX(SUBSTRING_INDEX(t.cycle, '/', -1), '分钟', 1))
                               ELSE 0
                           END),
                       0
               ) as                                                                                         cycleDuration,
               IFNULL(SUM(IFNULL(p.unpaid, 0)), 0)                                                          unpaid,
               IFNULL(COUNT(distinct p.id), 0)                                                              num,
               IFNULL(COUNT(distinct t.employee_id), 0)                                                     employeeNum,
               t.employee_name                                                                              employeeName
        FROM pos_cash_service t
                 INNER JOIN pos_cash p ON p.id = t.cash_id
            ${ew.customSqlSegment}
    </select>

    <select id="selectProductAmountByProduct" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT t.product_name                                                                               name,
               IFNULL(SUM(IFNULL(t.orgin_price, 0)-IFNULL(t.refund_amount, 0)), 0)                          amount,
               ROUND(IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0), 2) payment,
               ROUND(IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0) + IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0),
                     2)                                                                                     discountAmount,
               IFNULL(SUM(IFNULL(t.profit_price, 0)), 0)                                                    profitPrice,
               IFNULL(SUM(IFNULL(p.paid, 0)), 0)                                                            paid,
               IFNULL(SUM(IFNULL(p.unpaid, 0)), 0)                                                          unpaid,
               IFNULL(sum(t.num-IFNULL(t.refund_num, 0)), 0)                                          productNum,
               IFNULL(COUNT(distinct p.id), 0)                                                              num,
               IFNULL(COUNT(distinct CASE WHEN p.type_ = '1' THEN p.id END), 0)                             shoppingOrderNum,
               ROUND(IFNULL(sum(CASE WHEN p.type_ = '0' THEN (IFNULL(t.amount, 0) - IFNULL(t.assessed_amount, 0)) END),
                            0), 2)                                                                          tableProductAmount
        FROM pos_cash_product t
                 INNER JOIN pos_cash p ON p.id = t.cash_id and t.delete_flag = 0
            ${ew.customSqlSegment}
        group by t.product_id
    </select>

    <select id="selectOneMemberAmount" resultType="top.kx.kxss.app.vo.result.cash.MemberAmountResultVO">
        SELECT IFNULL(SUM(p.payment - p.refund_amount), 0.00)                                            as payment,
               IFNULL(SUM(CASE WHEN p.type_ != '3' and p.member_id IS NOT NULL THEN p.amount END),
                      0.00)                                                                              as memberAmount,
               IFNULL(SUM(CASE WHEN p.type_ != '3' and p.member_id IS NULL THEN p.amount END),
                      0.00)                                                                              as nonMemberAmount,
               IFNULL(SUM(CASE WHEN p.type_ != '3' and p.member_id IS NOT NULL THEN (p.payment - p.refund_amount) END),
                      0.00)                                                                              as memberPayment,
               IFNULL(SUM(CASE WHEN p.type_ != '3' and p.member_id IS NULL THEN (p.payment - p.refund_amount) END),
                      0.00)                                                                              as nonMemberPayment,
               IFNULL(SUM(CASE WHEN p.type_ = '3' THEN IFNULL(p.payment, 0) ELSE 0 END),
                      0)                                                                                 AS rechargeAmount,
               COUNT(DISTINCT CASE WHEN p.type_ = '3' THEN p.member_id END)                              AS rechargeMemberNum,
               COUNT(DISTINCT CASE WHEN p.type_ != '3' and p.member_id IS NOT NULL THEN p.id
                     END)                                                                                as memberOrderNum,
               COUNT(DISTINCT CASE WHEN p.type_ != '3' and p.member_id IS NULL THEN p.id
                     END)                                                                                as nonMemberOrderNum,
               COUNT(DISTINCT CASE
                                  WHEN p.type_ = '0' and p.member_id IS NOT NULL
                                      THEN p.id END)                                                     as memberOpenOrderNum,
               COUNT(DISTINCT CASE
                                  WHEN p.type_ = '0' and p.member_id IS NULL
                                      THEN p.id END)                                                     as nonMemberOpenOrderNum,
               COUNT(DISTINCT CASE WHEN p.type_ = '3' AND p.is_first_recharge = TRUE THEN p.member_id END) AS firstRechargeMemberNum,
               COUNT(DISTINCT CASE WHEN p.type_ = '3' AND p.is_first_recharge = FALSE THEN p.member_id END) AS noFirstRechargeMemberNum
        FROM pos_cash p
            ${ew.customSqlSegment}
    </select>

    <select id="selectOneMemberProductAmount" resultType="top.kx.kxss.app.vo.result.cash.MemberAmountResultVO">
        SELECT IFNULL(SUM(CASE WHEN p.member_id IS NOT NULL THEN t.num END), 0.00) as memberProductNum,
               IFNULL(SUM(CASE WHEN p.member_id IS NULL THEN t.num END), 0.00)     as nonMemberProductNum,
               ROUND(IFNULL(SUM(CASE WHEN p.member_id IS NOT NULL THEN (t.amount - t.assessed_amount) END),
                            0.00), 2)                                              as memberProductPayment,
               round(IFNULL(SUM(CASE WHEN p.member_id IS NULL THEN (t.amount - t.assessed_amount) END),
                            0.00), 2)                                              as nonMemberProductPayment

        FROM pos_cash p
                 left join pos_cash_product t on p.id = t.cash_id and t.delete_flag = 0
            ${ew.customSqlSegment}
    </select>

    <select id="selectOneMemberServiceAmount" resultType="top.kx.kxss.app.vo.result.cash.MemberAmountResultVO">
        SELECT IFNULL(SUM(CASE WHEN p.member_id IS NOT NULL THEN t.duration END), 0.00) as memberServiceDuration,
               IFNULL(SUM(CASE WHEN p.member_id IS NULL THEN t.duration END), 0.00)     as nonMemberServiceDuration,
               ROUND(IFNULL(SUM(CASE WHEN p.member_id IS NOT NULL THEN (t.amount - t.assessed_amount) END),
                            0.00), 2)                                                   as memberServicePayment,
               round(IFNULL(SUM(CASE WHEN p.member_id IS NULL THEN (t.amount - t.assessed_amount) END),
                            0.00), 2)                                                   as nonMemberServicePayment

        FROM pos_cash p
                 left join pos_cash_service t on p.id = t.cash_id
            ${ew.customSqlSegment}
    </select>

    <select id="newCustomersCount" resultType="top.kx.kxss.base.vo.NameValueVO">
        select t.pay_type      as name,
               count(t.pay_type) as `value`
        from pos_cash_payment t
                 inner join pos_cash p on t.cash_id = p.id
            ${ew.customSqlSegment}
        group by t.pay_type
    </select>

    <select id="queryOne" resultType="top.kx.kxss.app.entity.cash.PosCash">
        select id,
               type_,
               code,
               bill_date,
               bill_state,
               bill_type,
               org_id,
               employee_id,
               member_id,
               amount,
               discount_amount,
               payment,
               paid,
               unpaid,
               round_amount,
               table_amount,
               service_amount,
               product_amount,
               pay_name,
               remarks,
               discount_type,
               discount,
               created_time,
               created_by,
               updated_time,
               updated_by,
               created_org_id,
               source_id,
               chargeback_id,
               table_id,
               table_name,
               delete_flag,
               deposit_rule_id,
               recharge_amount,
               gift_amount,
               is_first_recharge,
               order_source,
               refund_amount,
               is_round,
               timing_duration,
               is_free,
               shop_num,
               change_amount,
               is_turn,
               complete_time,
               created_emp,
               thail_id,
               thail_amount,
               refund_time,
               is_member_verified,
               discount_template_id,
               registration_time,
               is_split,
               split_time,
               auto_round_amount,
               is_auto_round,
               registration_by,
               split_by,
               buy_card_amount,
               is_pack_field,
               is_temporary_lights,
               group_buy_id,
               is_thail_extend,
               sn,
               prepaid_refund_amount,
               prepaid_refund_time,
               is_member_discount,
               pay_discount_amount,
               counter_checkout_time,
               counter_checkout_by
        from pos_cash
        where id = #{id}
    </select>

    <select id="getAnyList" resultType="top.kx.kxss.app.entity.cash.PosCash">
        select id,
               type_,
               code,
               bill_date,
               bill_state,
               bill_type,
               org_id,
               employee_id,
               member_id,
               amount,
               discount_amount,
               payment,
               paid,
               unpaid,
               round_amount,
               table_amount,
               service_amount,
               product_amount,
               pay_name,
               remarks,
               discount_type,
               discount,
               created_time,
               created_by,
               updated_time,
               updated_by,
               created_org_id,
               source_id,
               chargeback_id,
               table_id,
               table_name,
               delete_flag,
               deposit_rule_id,
               recharge_amount,
               gift_amount,
               is_first_recharge,
               order_source,
               refund_amount,
               is_round,
               timing_duration,
               is_free,
               shop_num,
               change_amount,
               is_turn,
               complete_time,
               created_emp,
               thail_id,
               thail_amount,
               refund_time,
               is_member_verified,
               discount_template_id,
               registration_time,
               is_split,
               split_time,
               auto_round_amount,
               is_auto_round,
               registration_by,
               split_by,
               buy_card_amount,
               is_pack_field,
               is_temporary_lights,
               group_buy_id,
               is_thail_extend,
               sn,
               prepaid_refund_amount,
               prepaid_refund_time,
               is_member_discount,
               pay_discount_amount,
               counter_checkout_time,
               counter_checkout_by
        from pos_cash
        where id in
        <foreach collection="ids" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    <select id="selectFreeChangeAmount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        select SUM(IFNULL(p.free_change_amount, 0)) as amount,
               count(distinct p.id)                 as `num`
        from pos_cash p
            ${ew.customSqlSegment}
    </select>
    <select id="selectPaymentDetailAmount" resultType="top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentDetailResultVO">
        select pc.id         as cashId,
               pcpd.biz_type as bizType,
               sum(ifnull(pcpd.gift_amount, 0)) as giftAmount,
               sum(ifnull(pcpd.amount, 0))         as rechargeAmount,
               sum(ifnull(pcpd.amount, 0) + ifnull(pcpd.gift_amount, 0)) as amount
        from pos_cash pc
                 inner join pos_cash_payment_detail pcpd on pc.id = pcpd.cash_id and pcpd.delete_flag = 0
            ${ew.customSqlSegment}
        group by pc.id, pcpd.biz_type
    </select>
    <select id="selectCashByEmployeeId" resultType="top.kx.kxss.pos.vo.service.ServiceTableResultVO">
        SELECT s.employee_id                                      AS employeeId,
        group_concat(DISTINCT p.table_name SEPARATOR ',' ) AS tableName
        FROM pos_cash_service s
            INNER JOIN pos_cash p ON p.id = s.cash_id
            ${ew.customSqlSegment}
        GROUP BY s.employee_id
    </select>
    <select id="duration" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0) amount,
               IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)                                      payment,
               IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0) +
               IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)                                          discountAmount,
               IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0)                                              giftAmount,
               IFNULL(SUM(IFNULL(p.paid, 0)), 0)                                                     paid,
               IFNULL(SUM(IFNULL(p.unpaid, 0)), 0)                                                   unpaid,
               IFNULL(count(p.id), 0)                                                                num,
               p.bill_type as                                                                        billType,
               'TABLE'     AS                                                                        `type`
        FROM pos_cash_table t
                 left JOIN pos_cash p ON p.id = t.cash_id
                 left join base_table_info `table` on `table`.id = t.table_id
            and p.type_ = '0' and t.delete_flag = 0
            and t.status = '1'
            ${ew.customSqlSegment}
        GROUP BY
            t.table_id
    </select>
    <select id="cashDuration" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0) amount,
               IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)
                   - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)                                      payment,
               IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0) +
               IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)                                          discountAmount,
               IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0)                                              giftAmount,
               IFNULL(SUM(IFNULL(t.duration, 0)), 0)                                              duration,
               IFNULL(SUM(IFNULL(p.paid, 0)), 0)                                                     paid,
               IFNULL(SUM(IFNULL(p.unpaid, 0)), 0)                                                   unpaid,
               IFNULL(count(p.id), 0)                                                                num,
               p.bill_type as                                                                        billType,
               'TABLE'     AS                                                                        `type`,
               ${field} as                                                                   field

        FROM pos_cash_table t
                 left JOIN pos_cash p ON p.id = t.cash_id
                 left join base_table_info `table` on `table`.id = t.table_id
            ${ew.customSqlSegment}
        GROUP BY
            field
        UNION ALL(
        SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0) amount,
            IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)
            - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)                                      payment,
            IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0) +
            IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)                                          discountAmount,
            IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0)                                              giftAmount,
            IFNULL(SUM(IFNULL(t.duration, 0)), 0)                                              duration,
            IFNULL(SUM(IFNULL(p.paid, 0)), 0)                                                     paid,
            IFNULL(SUM(IFNULL(p.unpaid, 0)), 0)                                                   unpaid,
            IFNULL(count(p.id), 0)                                                                num,
            p.bill_type as                                                                        billType,
            'THAIL'     AS                                                                        `type`,
            ${field} as                                                                   field
        FROM pos_cash_thail t
            left JOIN pos_cash p ON p.id = t.cash_id
            left join base_table_info `table` on `table`.id = p.table_id
            ${ew.customSqlSegment}
        GROUP BY
            field
        )
        UNION ALL(
        SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0) - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0) amount,
            IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)
            - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)                                      payment,
            IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0) +
            IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)                                          discountAmount,
            IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0)                                              giftAmount,
            IFNULL(SUM(IFNULL(t.duration, 0)), 0)                                              duration,
            IFNULL(SUM(IFNULL(p.paid, 0)), 0)                                                     paid,
            IFNULL(SUM(IFNULL(p.unpaid, 0)), 0)                                                   unpaid,
            IFNULL(count(p.id), 0)                                                                num,
            p.bill_type as                                                                        billType,
            'POWER'     AS                                                                        `type`,
            ${field} as                                                                   field
        FROM pos_cash_power t
            left JOIN pos_cash p ON p.id = t.cash_id
            left join base_table_info `table` on `table`.id = t.table_id
            ${ew.customSqlSegment}
        GROUP BY
            field
            )
    </select>

    <!--    <select id="selectCounterCheckout" resultType="top.kx.kxss.app.vo.result.cash.CounterCheckoutResultVO">
            select max(counter_checkout_time) as counterCheckoutTime,
                   count(distinct p.id)                 as num
            from pos_cash p
                ${ew.customSqlSegment}
        </select>-->

    <update id="refreshCompleteEmp">
        update pos_cash pc
        set pc.complete_emp = #{completeEmp}
        where (pc.complete_emp not in (select be.id from base_employee be where be.delete_flag = 0 )
            or pc.complete_emp is null)
          and pc.bill_state IN ('2', '5')
          AND pc.bill_type NOT IN ('2', '1')
          AND pc.delete_flag = 0
          and pc.org_id = #{orgId}
                  and pc.complete_time >= #{startTime}
    </update>
    <update id="forceRefreshCompleteEmp">
        update pos_cash pc
        set pc.complete_emp = #{completeEmp}
        where pc.bill_state IN ('2', '5')
          AND pc.bill_type NOT IN ('2', '1')
          AND pc.delete_flag = 0
          and pc.org_id = #{orgId}
          and pc.complete_time >= #{startTime}
    </update>


</mapper>
