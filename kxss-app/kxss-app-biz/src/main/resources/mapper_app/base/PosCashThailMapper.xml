<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.app.mapper.thail.PosCashThailMapper">
<!--
    代码生成器 by 2023-09-14 19:17:23
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.app.entity.thail.PosCashThail">
        <id column="id" property="id" />
        <result column="cash_id" property="cashId" />
        <result column="duration" property="duration" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="orgin_price" property="orginPrice" />
        <result column="price" property="price" />
        <result column="amount" property="amount" />
        <result column="remind_time" property="remindTime" />
        <result column="discount" property="discount" />
        <result column="remarks" property="remarks" />
        <result column="status" property="status" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="discount_amount" property="discountAmount" />
        <result column="discount_type" property="discountType" />
        <result column="discount_remarks" property="discountRemarks" />
        <result column="is_merge" property="isMerge" />
        <result column="is_turn" property="isTurn" />
        <result column="thail_id" property="thailId" />
        <result column="thail_name" property="thailName" />
        <result column="review_status" property="reviewStatus" />
        <result column="review_emp" property="reviewEmp" />
        <result column="review_time" property="reviewTime" />
        <result column="review_remark" property="reviewRemark" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, cash_id, duration, start_time, end_time, orgin_price,
        price, amount, remind_time, discount, remarks, status,
        created_time, created_by, updated_time, updated_by, created_org_id, delete_flag,
        discount_amount, discount_type, discount_remarks, is_merge, is_turn, thail_id,
        thail_name, review_status, review_emp, review_time, review_remark
    </sql>
    <select id="findProfit" resultType="top.kx.kxss.app.vo.result.ProfitResultVO">
        SELECT
        SUM(
        IFNULL( amount, 0 )) - SUM(
        IFNULL( assessed_amount, 0 )) AS amount,
        SUM(
        IFNULL( profit_price, 0 )) AS profitAmount
        FROM
        pos_cash_thail
        where delete_flag = 0
        <if test="posCashIdList != null and posCashIdList.size() > 0">
            and cash_id in
            <foreach collection="posCashIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="thailAmountList" resultType="top.kx.kxss.app.vo.result.thail.PosCashThailAmountResultVO">
        SELECT pc.id as                                                                          cashId,
               ROUND(SUM(if(t.is_check_securities, IFNULL(t.amount, 0), 0)) -
                     SUM(if(t.is_check_securities, IFNULL(t.refund_amount, 0), 0)) -
                     SUM(if(t.is_check_securities, IFNULL(t.assessed_amount, 0), 0)),
                     2)                                                                          groupBuyAmount,
               ROUND(SUM(if(t.is_check_securities = false, IFNULL(t.amount, 0), 0)) -
                     SUM(if(t.is_check_securities = false, IFNULL(t.refund_amount, 0), 0)) -
                     SUM(if(t.is_check_securities = false, IFNULL(t.assessed_amount, 0), 0)), 2) thailAmount,
               COUNT(distinct CASE WHEN t.is_check_securities THEN pc.id END)                    groupBuyNum,
               COUNT(distinct CASE WHEN NOT t.is_check_securities THEN pc.id END)                thailNum
        FROM pos_cash_thail t
                 inner JOIN pos_cash pc ON pc.id = t.cash_id
            ${ew.customSqlSegment}
    </select>

    <select id="thailAmountSum" resultType="top.kx.kxss.app.vo.result.thail.PosCashThailAmountResultVO">
        SELECT
               ROUND(SUM(if(t.is_check_securities, IFNULL(t.amount, 0), 0)) -
                     SUM(if(t.is_check_securities, IFNULL(t.refund_amount, 0), 0)) -
                     SUM(if(t.is_check_securities, IFNULL(t.assessed_amount, 0), 0)),
                     2)                                                                          groupBuyAmount,
               ROUND(SUM(if(t.is_check_securities = false, IFNULL(t.amount, 0), 0)) -
                     SUM(if(t.is_check_securities = false, IFNULL(t.refund_amount, 0), 0)) -
                     SUM(if(t.is_check_securities = false, IFNULL(t.assessed_amount, 0), 0)), 2) thailAmount,
               COUNT(distinct CASE WHEN t.is_check_securities THEN pc.id END)                    groupBuyNum,
               COUNT(distinct CASE WHEN NOT t.is_check_securities THEN pc.id END)                thailNum
        FROM pos_cash_thail t
                 inner JOIN pos_cash pc ON pc.id = t.cash_id
            ${ew.customSqlSegment}
    </select>

</mapper>
