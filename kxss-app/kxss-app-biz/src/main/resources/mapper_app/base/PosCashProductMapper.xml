<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.app.mapper.cash.product.PosCashProductMapper">
    <!--
        代码生成器 by 2023-04-19 14:40:33
        自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
    -->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.app.entity.cash.product.PosCashProduct">
        <id column="id" property="id"/>
        <result column="cash_id" property="cashId"/>
        <result column="hh" property="hh"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="num" property="num"/>
        <result column="price" property="price"/>
        <result column="orgin_price" property="orginPrice"/>
        <result column="discount_amount" property="discountAmount"/>
        <result column="discount" property="discount"/>
        <result column="discount_type" property="discountType"/>
        <result column="amount" property="amount"/>
        <result column="remarks" property="remarks"/>
        <result column="is_gift" property="isGift"/>
        <result column="created_time" property="createdTime"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="created_org_id" property="createdOrgId"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , cash_id, hh, product_id, product_name, num,
        price, orgin_price, amount, remarks, is_gift, created_time,
        created_by, updated_time, updated_by, created_org_id,discount_amount,
        discount,discount_type
    </sql>

    <select id="getProductByCashIds" resultType="map">
        SELECT
        pos_cash.table_id AS tableId,
        count( pos_cash_product.id ) AS productCnt
        FROM
        pos_cash_product
        JOIN pos_cash ON pos_cash_product.cash_id = pos_cash.id
        WHERE
        pos_cash.table_id is not null
        and pos_cash_product.cash_id in
        <foreach collection="cashIds" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
        pos_cash.table_id
    </select>
    <select id="findProfit" resultType="top.kx.kxss.app.vo.result.ProfitResultVO">
        SELECT
        SUM(
        IFNULL( amount, 0 )) - SUM(
        IFNULL( assessed_amount, 0 )) AS amount,
        SUM(
        IFNULL( profit_price, 0 )) AS profitAmount
        FROM
        pos_cash_product
        where delete_flag = 0
        <if test="posCashIdList != null and posCashIdList.size() > 0">
            and cash_id in
            <foreach collection="posCashIdList" index="index" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="productRanking" resultType="java.lang.String">
        select Max(product_name)
        from pos_cash_product d
                 inner join pos_cash p on p.id = d.cash_id
        where p.delete_flag = 0
          and d.delete_flag = 0
          and p.bill_state in ('2', '5', '6')
          and p.bill_type not in ('2', '1')
          and p.member_id = #{memberId}
        GROUP BY d.product_id
        ORDER BY sum(d.num) desc limit 5
    </select>
    <select id="selectDiscount"
            resultMap="BaseResultMap">
        select pro.*
        from pos_cash_product pro
                 INNER JOIN pos_cash p on p.id = pro.cash_id
        where pro.delete_flag = 0
          and p.delete_flag = 0
          and p.bill_state = '2'
          and p.bill_type in ('0', '3', '4')
          and pro.discount_amount <![CDATA[ > ]]>  0
          and pro.discount_type in ('6', '7')
          and pro.id not in (select ext_id
                             from pos_cash_discount_detail de
                             where de.delete_flag = 0
                               and discount_type = '18'
                               and ext_id is not null)
        GROUP BY pro.id
    </select>

    <select id="queryList" resultType="top.kx.kxss.app.entity.cash.product.PosCashProduct">
        select id,
               cash_id,
               hh,
               product_id,
               product_name,
               num,
               price,
               orgin_price,
               amount,
               discount_amount,
               remarks,
               is_gift,
               type,
               discount,
               created_time,
               created_by,
               updated_time,
               updated_by,
               created_org_id,
               delete_flag,
               discount_type,
               coupon_id,
               discount_remarks,
               deduct_num,
               is_turn,
               is_merge,
               cash_thail_id,
               is_discount,
               assessed_amount,
               paid,
               merge_cash_id,
               is_split,
               split_cash_id,
               is_account,
               return_num,
               profit_price,
               cost_price,
               card_deduct_amount,
               member_card_id,
               stored_card_id,
               discount_template_id,
               discount_desc,
               reform_price_type,
               reform_price,
               old_price,
               old_orgin_price,
               sn,
               warehouse_id,
               refund_amount,
               attribute_setting,
               attribute_setting_desc,
               attribute_price,
               attribute_amount,
               thail_assessed_proportion,
               thail_assessed_amount,
               thail_detail_id,
               refund_num
        from pos_cash_product
                 ${ew.customSqlSegment}
    </select>
</mapper>
