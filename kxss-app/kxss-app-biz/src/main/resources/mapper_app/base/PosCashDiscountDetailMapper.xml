<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.app.mapper.cash.discount.PosCashDiscountDetailMapper">
<!--
    代码生成器 by 2023-08-02 18:52:33
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.app.entity.cash.discount.PosCashDiscountDetail">
        <id column="id" property="id" />
        <result column="discount_type" property="discountType" />
        <result column="ext_id" property="extId" />
        <result column="pre_price" property="prePrice" />
        <result column="price_change" property="priceChange" />
        <result column="step_desc" property="stepDesc" />
        <result column="pos_cash_id" property="posCashId" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="curr_price" property="currPrice" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, discount_type, ext_id, pre_price, price_change, step_desc,
        pos_cash_id, created_time, created_by, updated_time, updated_by, created_org_id,
        delete_flag, curr_price
    </sql>
    <delete id="deleteByPosCashId">
        delete
        from pos_cash_discount_detail
        where pos_cash_id = #{posCashId}
          and delete_flag = 0
    </delete>

    <select id="selectByDiscountType" resultType="top.kx.kxss.app.vo.result.cash.discount.PosCashDiscountDetailResultVO">
        SELECT t.discount_type     as discountType,
               (0 - IFNULL(SUM(IFNULL(t.price_change, 0)), 0))  as priceChange
        FROM pos_cash_discount_detail t
                 inner join pos_cash p ON p.id = t.pos_cash_id
            ${ew.customSqlSegment}
        GROUP BY
            t.discount_type
    </select>

    <select id="selectDiscountDetailByOrderSource" resultType="top.kx.kxss.app.vo.result.cash.discount.PosCashDiscountDetailResultVO">
        SELECT p.order_source    as orderSource,
               (0 - IFNULL(SUM(IFNULL(t.price_change, 0)), 0))  as priceChange
        FROM pos_cash_discount_detail t
                 inner join pos_cash p ON p.id = t.pos_cash_id
            ${ew.customSqlSegment}
        GROUP BY
            p.order_source
    </select>

</mapper>
