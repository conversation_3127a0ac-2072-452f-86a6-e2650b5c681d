<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.app.mapper.cash.PosCashCommenterMapper">
<!--
    代码生成器 by 2024-04-16 19:15:27
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.app.entity.cash.PosCashCommenter">
        <id column="id" property="id" />
        <result column="type_" property="type" />
        <result column="source_id" property="sourceId" />
        <result column="cash_id" property="cashId" />
        <result column="employee_id" property="employeeId" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type_, source_id, cash_id, employee_id, created_time, 
        created_by, updated_time, updated_by, created_org_id, delete_flag
    </sql>

</mapper>
