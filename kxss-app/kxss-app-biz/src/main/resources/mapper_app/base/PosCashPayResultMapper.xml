<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.app.mapper.cash.payresult.PosCashPayResultMapper">
<!--
    代码生成器 by 2023-05-19 17:25:46
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.app.entity.cash.payresult.PosCashPayResult">
        <id column="id" property="id" />
        <result column="result_code" property="resultCode" />
        <result column="result_msg" property="resultMsg" />
        <result column="app_type" property="appType" />
        <result column="trans_type" property="transType" />
        <result column="mis_id" property="misId" />
        <result column="order_id" property="orderId" />
        <result column="business_id" property="businessId" />
        <result column="platform_id" property="platformId" />
        <result column="platform" property="platform" />
        <result column="amount" property="amount" />
        <result column="amount1" property="amount1" />
        <result column="amount2" property="amount2" />
        <result column="amount3" property="amount3" />
        <result column="trans_date" property="transDate" />
        <result column="trans_time" property="transTime" />
        <result column="voucher_num" property="voucherNum" />
        <result column="batch_num" property="batchNum" />
        <result column="reference_num" property="referenceNum" />
        <result column="card_num" property="cardNum" />
        <result column="issuer" property="issuer" />
        <result column="acquirer" property="acquirer" />
        <result column="operator_id" property="operatorId" />
        <result column="card_type" property="cardType" />
        <result column="account_type" property="accountType" />
        <result column="model" property="model" />
        <result column="version" property="version" />
        <result column="terminal_id" property="terminalId" />
        <result column="merchant_id" property="merchantId" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, result_code, result_msg, app_type, trans_type, mis_id, 
        order_id, business_id, platform_id, platform, amount, amount1, 
        amount2, amount3, trans_date, trans_time, voucher_num, batch_num, 
        reference_num, card_num, issuer, acquirer, operator_id, card_type, 
        account_type, model, version, terminal_id, merchant_id, created_by, 
        created_time, updated_by, updated_time, created_org_id, delete_flag
    </sql>

</mapper>
