<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.app.mapper.cash.equity.PosCashEquityMapper">
<!--
    代码生成器 by 2023-05-06 18:27:08
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.app.entity.cash.equity.PosCashEquity">
        <id column="id" property="id" />
        <result column="cash_id" property="cashId" />
        <result column="type_" property="type" />
        <result column="biz_id" property="bizId" />
        <result column="remarks" property="remarks" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_org_id" property="createdOrgId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, cash_id, type_, biz_id, remarks, created_time,
        created_by, updated_time, updated_by, created_org_id
    </sql>

    <select id="queryMemberCoupon" resultType="top.kx.kxss.app.entity.cash.equity.AppEquity">
        select
            pos_cash_equity.id as equityId,
            pos_cash_equity.biz_id as bizId,
            base_coupon_info.id as baseId,
            member_coupon.type_ as type,
            member_coupon.coupon_name as name,
            base_coupon_info.original_price as price
        from
            pos_cash_equity join member_coupon on pos_cash_equity.biz_id = member_coupon.id
                             join base_coupon_info on member_coupon.coupon_id = base_coupon_info.id
        where
            pos_cash_equity.type_ = '2'
          and pos_cash_equity.cash_id = #{cashId}
    </select>
    <select id="consumeTimes" resultType="top.kx.kxss.base.vo.NameValueVO">
        select e.biz_id         as name,
               count(e.cash_id) as `value`
        from pos_cash_equity e
                 INNER JOIN pos_cash p on p.id = e.cash_id
            ${ew.customSqlSegment}
        GROUP BY e.biz_id
    </select>

</mapper>
