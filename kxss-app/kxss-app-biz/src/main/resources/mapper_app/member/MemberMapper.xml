<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.app.mapper.member.MemberMapper">

    <select id="getMemberInfo" resultType="map">
        select
            member_info.code,
            member_info.name,
            member_info.mobile,
            member_info.recharge_amount as rechargeAmount,
            member_info.gift_amount as giftAmount,
            member_info.grade_id as gradeId,
            member_grade.name as gradeName,
            member_info.growth_value as growthValue
        from
            member_info left join member_grade on member_info.grade_id = member_grade.id
        where
            member_info.id = #{id} and member_info.delete_flag = 0
    </select>

    <select id="getMemberDiscount" resultType="map">
        select
            member_grade_discount.*
        from
            member_grade_discount join member_info on member_info.grade_id = member_grade_discount.id
        where
            member_info.id = #{memberId}
            and member_grade_discount.type_ = #{type} and member_grade_discount.delete_flag = 0
    </select>

</mapper>
