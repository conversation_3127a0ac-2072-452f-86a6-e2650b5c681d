<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.app.mapper.member.AppMemberCouponMapper">
<!--
    代码生成器 by 2023-04-18 15:36:12
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.base.entity.member.coupon.MemberCoupon">
        <id column="id" property="id" />
        <result column="member_id" property="memberId" />
        <result column="coupon_id" property="couponId" />
        <result column="coupon_name" property="couponName" />
        <result column="type_" property="type" />
        <result column="status" property="status" />
        <result column="usage_time" property="usageTime" />
        <result column="expires_time" property="expiresTime" />
        <result column="remarks" property="remarks" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_org_id" property="createdOrgId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, member_id, coupon_id, coupon_name, type_, status,
        usage_time, expires_time, remarks, created_time, created_by, updated_time,
        updated_by, created_org_id
    </sql>

    <select id="getUseingMemberCoupon" resultMap="BaseResultMap">
        SELECT
            member_coupon.*
        FROM
            pos_cash_equity
                JOIN member_coupon ON pos_cash_equity.biz_id = member_coupon.id
        WHERE
            pos_cash_equity.type_ = '2'
          AND pos_cash_equity.cash_id = #{cashId}
    </select>

</mapper>
