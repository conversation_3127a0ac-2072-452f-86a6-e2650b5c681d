package top.kx.kxss.app.controller.cash.payment;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.app.service.cash.payment.PosCashPaymentDetailService;
import top.kx.kxss.app.entity.cash.payment.PosCashPaymentDetail;
import top.kx.kxss.app.vo.save.cash.payment.PosCashPaymentDetailSaveVO;
import top.kx.kxss.app.vo.update.cash.payment.PosCashPaymentDetailUpdateVO;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentDetailResultVO;
import top.kx.kxss.app.vo.query.cash.payment.PosCashPaymentDetailPageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 订单支付明细
 * </p>
 *
 * <AUTHOR>
 * @date 2024-07-12 20:20:48
 * @create [2024-07-12 20:20:48] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/posCashPaymentDetail")
@Api(value = "PosCashPaymentDetail", tags = "订单支付明细")
public class PosCashPaymentDetailController extends SuperController<PosCashPaymentDetailService, Long, PosCashPaymentDetail, PosCashPaymentDetailSaveVO,
    PosCashPaymentDetailUpdateVO, PosCashPaymentDetailPageQuery, PosCashPaymentDetailResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


