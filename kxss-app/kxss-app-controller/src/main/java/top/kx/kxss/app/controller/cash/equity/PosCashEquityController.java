package top.kx.kxss.app.controller.cash.equity;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import top.kx.basic.base.R;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.app.service.cash.equity.PosCashEquityService;
import top.kx.kxss.app.entity.cash.equity.PosCashEquity;
import top.kx.kxss.app.vo.save.cash.equity.PosCashEquitySaveVO;
import top.kx.kxss.app.vo.update.cash.equity.PosCashEquityUpdateVO;
import top.kx.kxss.app.vo.result.cash.equity.PosCashEquityResultVO;
import top.kx.kxss.app.vo.query.cash.equity.PosCashEquityPageQuery;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * 结算单消费权益
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-06 18:27:08
 * @create [2023-05-06 18:27:08] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/posCashEquity")
@Api(value = "PosCashEquity", tags = "结算单消费权益")
public class PosCashEquityController extends SuperController<PosCashEquityService, Long, PosCashEquity, PosCashEquitySaveVO,
    PosCashEquityUpdateVO, PosCashEquityPageQuery, PosCashEquityResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @ApiOperation(value = "列表", notes = "列表")
    @PostMapping("/list")
    public R<List<PosCashEquityResultVO>> queryList(@RequestParam("bizIdList") List<Long> bizIdList, @RequestParam("type") String type) {
        return super.success(superService.queryList(bizIdList, type));
    }

}


