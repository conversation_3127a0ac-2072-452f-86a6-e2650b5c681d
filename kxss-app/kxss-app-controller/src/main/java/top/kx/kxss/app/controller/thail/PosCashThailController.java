package top.kx.kxss.app.controller.thail;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.app.entity.thail.PosCashThail;
import top.kx.kxss.app.service.thail.PosCashThailService;
import top.kx.kxss.app.vo.query.thail.PosCashThailPageQuery;
import top.kx.kxss.app.vo.result.thail.PosCashThailResultVO;
import top.kx.kxss.app.vo.save.thail.PosCashThailSaveVO;
import top.kx.kxss.app.vo.update.thail.PosCashThailUpdateVO;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * 订单套餐信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-14 19:11:17
 * @create [2023-09-14 19:11:17] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/posCashThail")
@Api(value = "PosCashThail", tags = "订单套餐信息")
public class PosCashThailController extends SuperController<PosCashThailService, Long, PosCashThail, PosCashThailSaveVO,
        PosCashThailUpdateVO, PosCashThailPageQuery, PosCashThailResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @ApiOperation(value = "检测套餐是否在使用", notes = "检测套餐是否在使用")
    @PostMapping("/checkThail")
    public R<Boolean> checkThail(@RequestBody List<Long> thailIds) {
        return success(superService.checkThail(thailIds));
    }


}


