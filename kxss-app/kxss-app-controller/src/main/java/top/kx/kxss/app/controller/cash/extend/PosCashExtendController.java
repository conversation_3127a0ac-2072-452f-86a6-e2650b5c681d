package top.kx.kxss.app.controller.cash.extend;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.app.service.cash.extend.PosCashExtendService;
import top.kx.kxss.app.entity.cash.extend.PosCashExtend;
import top.kx.kxss.app.vo.save.cash.extend.PosCashExtendSaveVO;
import top.kx.kxss.app.vo.update.cash.extend.PosCashExtendUpdateVO;
import top.kx.kxss.app.vo.result.cash.extend.PosCashExtendResultVO;
import top.kx.kxss.app.vo.query.cash.extend.PosCashExtendPageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 订单续时
 * </p>
 *
 * <AUTHOR>
 * @date 2023-10-21 17:10:25
 * @create [2023-10-21 17:10:25] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/posCashExtend")
@Api(value = "PosCashExtend", tags = "订单续时")
public class PosCashExtendController extends SuperController<PosCashExtendService, Long, PosCashExtend, PosCashExtendSaveVO,
    PosCashExtendUpdateVO, PosCashExtendPageQuery, PosCashExtendResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


