package top.kx.kxss.app.controller.table;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.app.service.table.TableOperateService;
import top.kx.kxss.app.vo.save.cash.PosCashSaveVO;
import top.kx.kxss.app.vo.save.cash.product.PosCashProductSaveVO;
import top.kx.kxss.app.vo.save.cash.table.PosCashExchangeTableSaveVO;
import top.kx.kxss.app.vo.save.cash.table.PosCashMergeTableSaveVO;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.vo.query.table.BaseTableInfoPageQuery;
import top.kx.kxss.base.vo.result.table.BaseTableInfoResultVO;
import top.kx.kxss.base.vo.save.table.BaseTableInfoSaveVO;
import top.kx.kxss.base.vo.update.table.BaseTableInfoUpdateVO;

import java.util.Map;

/**
 * <p>
 * 前端控制器
 * 台桌操作接口
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-18 23:00:01
 * @create [2023-04-18 23:00:01] [liu]
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/table/operate")
@Api(value = "/app/table/operate", tags = "操作台桌信息")
public class TableOperateController extends SuperController<TableOperateService, Long, BaseTableInfo, BaseTableInfoSaveVO, BaseTableInfoUpdateVO, BaseTableInfoPageQuery, BaseTableInfoResultVO> {

    @ApiOperation(value = "台桌并桌操作", notes = "台桌并桌操作")
    @PostMapping("/mergeTable")
    public R<Map<String, Object>> mergeTable(@RequestBody PosCashMergeTableSaveVO posCashMergeTableSaveVO) {
        return success(superService.mergeTable(posCashMergeTableSaveVO));
    }

    @ApiOperation(value = "台桌换桌操作", notes = "台桌换桌操作")
    @PostMapping("/exchangeTable")
    public R<Map<String, Object>> exchangeTable(@RequestBody PosCashExchangeTableSaveVO posCashExchangeTableSaveVO) {
        return success(superService.exchangeTable(posCashExchangeTableSaveVO));
    }

    @ApiOperation(value = "整单取消操作", notes = "整单取消操作")
    @PostMapping("/orderCancel")
    public R<Map<String, Object>> cancel(@RequestBody PosCashSaveVO posCashSaveVO) {
        return success(superService.orderCancel(posCashSaveVO));
    }

    @ApiOperation(value = "单品备注", notes = "单品备注")
    @PostMapping("/singleRemark")
    public R<Map<String, Object>> singleRemark(@RequestBody Map<String, Object> param) {
        return success(superService.singleRemark(param));
    }

    @ApiOperation(value = "单品优惠/赠送", notes = "单品优惠/赠送")
    @PostMapping("/singleSale")
    public R<Map<String, Object>> singleSale(@RequestBody Map<String, Object> param) {
        return success(superService.singleSale(param));
    }

    @ApiOperation(value = "取消赠送", notes = "取消赠送")
    @PostMapping("/unGive")
    public R<Map<String, Object>> unGive(@RequestBody Map<String, Object> param) {
        return success(superService.unGive(param));
    }

    @ApiOperation(value = "整单操作打折/减免/免单", notes = "整单操作打折/减免/免单")
    @PostMapping("/completeOrder")
    public R<Map<String, Object>> completeOrder(@RequestBody Map<String, Object> param) {
        return success(superService.completeOrder(param));
    }

    @ApiOperation(value = "取消打折/减免", notes = "取消打折/减免")
    @PostMapping("/disSale")
    public R<Map<String, Object>> disSale(@RequestBody Map<String, Object> param) {
        return success(superService.disSale(param));
    }

    @ApiOperation(value = "整单取消打折/减免", notes = "整单取消打折/减免")
    @PostMapping("/disCompleteOrder")
    public R<Map<String, Object>> disCompleteOrder(@RequestBody Map<String, Object> param) {
        return success(superService.disCompleteOrder(param));
    }
}




