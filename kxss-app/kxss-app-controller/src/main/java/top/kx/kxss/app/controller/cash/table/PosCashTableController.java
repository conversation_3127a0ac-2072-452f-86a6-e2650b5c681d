package top.kx.kxss.app.controller.cash.table;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.service.cash.table.PosCashTableService;
import top.kx.kxss.app.vo.query.cash.table.PosCashTablePageQuery;
import top.kx.kxss.app.vo.result.cash.table.PosCashTableResultVO;
import top.kx.kxss.app.vo.save.cash.table.PosCashTableSaveVO;
import top.kx.kxss.app.vo.update.cash.table.PosCashTableUpdateVO;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * 台桌计时费用
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:44:58
 * @create [2023-04-19 14:44:58] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/posCashTable")
@Api(value = "PosCashTable", tags = "台桌计时费用")
public class PosCashTableController extends SuperController<PosCashTableService, Long, PosCashTable, PosCashTableSaveVO,
    PosCashTableUpdateVO, PosCashTablePageQuery, PosCashTableResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }


    @ApiOperation(value = "检查台桌是否在使用", notes = "检查台桌是否在使用")
    @PostMapping("/checkIsUse")
    public R<Boolean> checkIsUse(@RequestBody List<Long> longs){
        return success(superService.checkIsUse(longs));
    }
    @ApiOperation(value = "检查台桌类型是否在使用", notes = "检查台桌类型是否在使用")
    @PostMapping("/checkTableTypeIsUse")
    public R<Boolean> checkTableTypeIsUse(@RequestBody List<String> longs){
        return success(superService.checkTableTypeIsUse(longs));
    }
    @ApiOperation(value = "检查台桌类型是否在使用", notes = "检查台桌类型是否在使用")
    @PostMapping("/checkTableTypeIdIsUse")
    public R<Boolean> checkTableTypeIdIsUse(@RequestBody List<Long> longs){
        return success(superService.checkTableTypeIdIsUse(longs));
    }

    @ApiOperation(value = "检查服务活动是否在使用", notes = "检查服务活动是否在使用")
    @PostMapping("/checkServiceActivity")
    public R<Boolean> checkServiceActivity(@RequestBody List<Long> longs){
        return success(superService.checkServiceActivity(longs));
    }
}


