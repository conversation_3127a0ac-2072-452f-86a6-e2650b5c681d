package top.kx.kxss.app.controller.order;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.app.service.cash.payment.PosCashPaymentService;
import top.kx.kxss.base.entity.payment.BasePayment;
import top.kx.kxss.base.service.payment.BasePaymentService;
import top.kx.kxss.base.vo.query.payment.BasePaymentPageQuery;
import top.kx.kxss.base.vo.result.payment.BasePaymentResultVO;
import top.kx.kxss.base.vo.save.payment.BasePaymentSaveVO;
import top.kx.kxss.base.vo.update.payment.BasePaymentUpdateVO;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * 收款单
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 13:33:00
 * @create [2023-04-19 13:33:00] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/payment")
@Api(value = "Payment", tags = "收款单")
public class PaymentController extends SuperController<BasePaymentService, Long, BasePayment, BasePaymentSaveVO,
        BasePaymentUpdateVO, BasePaymentPageQuery, BasePaymentResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    private final PosCashPaymentService posCashPaymentService;


    @ApiOperation(value = "分页查询会员账户明细", notes = "分页查询会员账户明细")
    @PostMapping("/paymentPage")
    @WebLog(value = "'分页查询会员收银记录:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<BasePaymentResultVO>> memberPage(@RequestBody PageParams<BasePaymentPageQuery> params) {
        BasePaymentPageQuery pageQuery = params.getModel();
        if (pageQuery.getMemberId() == null) {
            return R.fail("请选择会员");
        }
        return super.page(params);
    }

    @ApiOperation(value = "获取商户号", notes = "获取商户号")
    @PostMapping("/getMchNoListByCurOrg")
    public List<String> getMchNoListByCurOrg() {
        return posCashPaymentService.getMchNoListByCurOrg();
    }
}


