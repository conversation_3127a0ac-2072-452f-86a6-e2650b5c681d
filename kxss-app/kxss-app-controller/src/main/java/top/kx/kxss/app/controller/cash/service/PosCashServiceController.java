package top.kx.kxss.app.controller.cash.service;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.app.service.cash.service.PosCashServiceService;
import top.kx.kxss.app.vo.query.cash.service.PosCashServicePageQuery;
import top.kx.kxss.app.vo.result.cash.service.PosCashServiceResultVO;
import top.kx.kxss.app.vo.save.cash.service.PosCashServiceSaveVO;
import top.kx.kxss.app.vo.update.cash.service.PosCashServiceUpdateVO;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * 收银-服务子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:44:58
 * @create [2023-04-19 14:44:58] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/posCashService")
@Api(value = "PosCashService", tags = "收银-服务子表")
public class PosCashServiceController extends SuperController<PosCashServiceService, Long, PosCashService, PosCashServiceSaveVO,
        PosCashServiceUpdateVO, PosCashServicePageQuery, PosCashServiceResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @ApiOperation(value = "检查服务是否在使用", notes = "检查服务是否在使用")
    @PostMapping("/checkIsUse")
    public R<Boolean> checkIsUse(@RequestBody List<Long> longs) {
        return success(superService.checkIsUse(longs));
    }

    @ApiOperation(value = "检查服务人员是否在使用", notes = "检查服务是否在使用")
    @PostMapping("/checkEmpIsUse")
    public R<Boolean> checkEmpIsUse(@RequestBody List<Long> longs) {
        return success(superService.checkEmpIsUse(longs));
    }

    @ApiOperation(value = "检查服务活动是否在使用", notes = "检查服务活动是否在使用")
    @PostMapping("/checkServiceActivity")
    public R<Boolean> checkServiceActivity(@RequestBody List<Long> longs) {
        return success(superService.checkServiceActivity(longs));
    }

    @ApiOperation(value = "更新上钟记录", notes = "更新上钟记录")
    @PostMapping("/posCashService/updateServiceWraps")
    Boolean updateServiceWraps(@RequestParam Long oldEmployeeId,
                               @RequestParam Long newEmployeeId) {
        return superService.update(Wraps.<PosCashService>lbU()
                .set(PosCashService::getEmployeeId, newEmployeeId)
                .eq(PosCashService::getEmployeeId, oldEmployeeId)
                .eq(PosCashService::getDeleteFlag, 0)
                .eq(PosCashService::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
        );
    }

}


