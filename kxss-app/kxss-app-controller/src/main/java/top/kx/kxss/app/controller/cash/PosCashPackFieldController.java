package top.kx.kxss.app.controller.cash;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.app.service.cash.PosCashPackFieldService;
import top.kx.kxss.app.entity.cash.PosCashPackField;
import top.kx.kxss.app.vo.save.cash.PosCashPackFieldSaveVO;
import top.kx.kxss.app.vo.update.cash.PosCashPackFieldUpdateVO;
import top.kx.kxss.app.vo.result.cash.PosCashPackFieldResultVO;
import top.kx.kxss.app.vo.query.cash.PosCashPackFieldPageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 包场信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-22 11:37:26
 * @create [2024-04-22 11:37:26] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/posCashPackField")
@Api(value = "PosCashPackField", tags = "包场信息")
public class PosCashPackFieldController extends SuperController<PosCashPackFieldService, Long, PosCashPackField, PosCashPackFieldSaveVO,
    PosCashPackFieldUpdateVO, PosCashPackFieldPageQuery, PosCashPackFieldResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


