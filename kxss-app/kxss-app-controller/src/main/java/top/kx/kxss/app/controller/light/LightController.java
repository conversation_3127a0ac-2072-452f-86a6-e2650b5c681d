package top.kx.kxss.app.controller.light;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.kxss.app.service.light.LightService;
import top.kx.kxss.app.vo.light.LightParamVO;
import top.kx.kxss.base.service.table.BaseTableInfoService;
import top.kx.kxss.model.enumeration.system.subscription.FeatureCodeEnum;
import top.kx.kxss.system.annotation.SubscriptionFeatureCheck;


/**
 * <p>
 * 前端控制器
 * 开关灯
 * </p>
 *
 * <AUTHOR>
 */

@Api(value = "/app/light", tags = "开关灯相关API")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/light")
public class LightController {


    @Autowired
    private LightService lightService;
    @Autowired
    private BaseTableInfoService tableInfoService;
    @ApiOperation(value = "开灯成功", notes = "开灯成功")
    @PostMapping("/success")
    public R<Boolean> success(@RequestBody @Validated LightParamVO lightParamVO) {
        return R.success(lightService.success(lightParamVO));
    }

    @ApiOperation(value = "开灯失败", notes = "开灯成功")
    @PostMapping("/fail")
    public R<Boolean> fail(@RequestBody @Validated LightParamVO lightParamVO) {
        return R.success(lightService.fail(lightParamVO));
    }

    @ApiOperation(value = "临时开灯", notes = "临时开灯")
    @PostMapping("/tempOpenLight")
//    @SubscriptionFeatureCheck(featureCode = {FeatureCodeEnum.TEMP_LIGHT})
    public R<Boolean> tempOpenLight(@RequestParam Long tableId) {
        return R.success(lightService.tempOpenLight(tableId));
    }

    @ApiOperation(value = "临时关灯", notes = "临时关灯")
    @PostMapping("/tempCloseLight")
    public R<Boolean> tempCloseLight(@RequestParam Long tableId) {
        return R.success(lightService.tempCloseLight(tableId));
    }

}
