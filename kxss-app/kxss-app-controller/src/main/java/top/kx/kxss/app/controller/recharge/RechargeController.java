package top.kx.kxss.app.controller.recharge;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;
import top.kx.basic.annotation.constraints.Idempotent;
import top.kx.basic.base.R;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.service.recharge.RechargeService;
import top.kx.kxss.app.vo.member.MemberPhoneQuery;
import top.kx.kxss.app.vo.query.cash.PosCashIdQuery;
import top.kx.kxss.app.vo.query.cash.PosCashMemberQuery;
import top.kx.kxss.app.vo.result.recharge.DepositRuleResultVO;
import top.kx.kxss.app.vo.result.recharge.QueryRechargeDetailVO;
import top.kx.kxss.app.vo.save.recharge.RechargeEmployeeSaveVO;
import top.kx.kxss.app.vo.save.recharge.RechargeRegistrationSaveVO;
import top.kx.kxss.base.vo.query.member.grade.MemberGradeIdQuery;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * 充值相关API
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/recharge")
@Api(value = "Recharge", tags = "充值相关API")
public class RechargeController {

    @Autowired
    private RechargeService rechargeService;

    @ApiOperation(value = "手机号搜索会员", notes = "手机号搜索会员")
    @PostMapping("/getMemberByPhone")
    public R<MemberInfoResultVO> getMemberByPhone(@RequestBody @Validated MemberPhoneQuery query) {
        return R.success(rechargeService.getMemberByPhone(query.getPhone()));
    }

    @ApiOperation(value = "绑定会员", notes = "绑定会员")
    @PostMapping("/bindMember")
    public R<Boolean> bindMember(@RequestBody @Validated PosCashMemberQuery query) {
        return R.success(rechargeService.bindMember(query));
    }

    @ApiOperation(value = "储值列表", notes = "储值列表")
    @PostMapping("/depositList")
    public R<List<DepositRuleResultVO>> depositList(@RequestBody MemberGradeIdQuery query) {
        return R.success(rechargeService.depositList(query));
    }

    @ApiOperation(value = "保存储值", notes = "保存储值")
    @PostMapping("/saveDeposit")
    public R<Boolean> saveDeposit(@RequestBody @Validated RechargeRegistrationSaveVO model) {
        return R.success(rechargeService.saveDeposit(model));
    }

    @ApiOperation(value = "保存储值无订单ID", notes = "保存储值")
    @PostMapping("/saveDepositNoCash")
    @Idempotent(key = "#model.memberId",expire = 4, isDelKey = false)
    public R<PosCash> saveDepositNoCash(@RequestBody @Validated RechargeRegistrationSaveVO model) {
        return R.success(rechargeService.saveDepositNoCash(model));
    }

    @ApiOperation(value = "移除储值", notes = "移除储值")
    @PostMapping("/delDeposit")
    public R<Boolean> delDeposit(@RequestBody @Validated PosCashIdQuery query) {
        return R.success(rechargeService.delDeposit(query));
    }

    @ApiOperation(value = "销售人员列表", notes = "销售人员列表")
    @PostMapping("/empList")
    public R<List<BaseEmployeeResultVO>> empList() {
        return R.success(rechargeService.empList());
    }

    @ApiOperation(value = "选择销售人员", notes = "选择销售人员")
    @PostMapping("/saveEmp")
    public R<Boolean> saveEmp(@RequestBody @Validated RechargeEmployeeSaveVO employeeSaveVO) {
        return R.success(rechargeService.saveEmp(employeeSaveVO));
    }

    @ApiOperation(value = "移除销售人员", notes = "移除销售人员")
    @PostMapping("/delEmp")
    public R<Boolean> delEmp(@RequestBody @Validated PosCashIdQuery query) {
        return R.success(rechargeService.delEmp(query));
    }

    @ApiOperation(value = "挂单", notes = "挂单")
    @PostMapping("/registration")
    @ApiIgnore
    public R<Boolean> registration(@RequestBody @Validated RechargeRegistrationSaveVO model) {
        return R.success(rechargeService.registration(model));
    }

    @ApiOperation(value = "下单详情", notes = "下单详情")
    @PostMapping("/queryDetail")
    public R<QueryRechargeDetailVO> queryDetail(@RequestBody PosCashIdQuery query) {
        return R.success(rechargeService.queryDetail(query.getPosCashId()));
    }

    @ApiOperation(value = "重置", notes = "重置")
    @PostMapping("/reset")
    public R<Boolean> reset(@RequestBody @Validated PosCashIdQuery query) {
        return R.success(rechargeService.reset(query));
    }

}


