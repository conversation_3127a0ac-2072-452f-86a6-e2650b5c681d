package top.kx.kxss.app.controller.employee;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperCacheController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.base.biz.user.BaseEmployeeBiz;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.vo.query.user.BaseEmployeePageQuery;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.base.vo.save.user.BaseEmployeeSaveVO;
import top.kx.kxss.base.vo.update.user.BaseEmployeeUpdateVO;
import top.kx.kxss.datascope.DataScopeHelper;
import top.kx.kxss.file.vo.param.FileParamVO;
import top.kx.kxss.system.service.tenant.DefUserService;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * 员工
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/employee")
@Api(value = "Employee", tags = "员工")
public class EmployeeController extends SuperCacheController<BaseEmployeeService, Long, BaseEmployee, BaseEmployeeSaveVO, BaseEmployeeUpdateVO,
        BaseEmployeePageQuery, BaseEmployeeResultVO> {

    private final EchoService echoService;
    private final BaseEmployeeBiz baseEmployeeBiz;
    private final DefUserService defUserService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Override
    public R<IPage<BaseEmployeeResultVO>> page(@RequestBody @Validated PageParams<BaseEmployeePageQuery> params) {
        IPage<BaseEmployeeResultVO> page = baseEmployeeBiz.findPageResultVO(params);
        handlerResult(page);
        return R.success(page);
    }

    @Override
    public R<BaseEmployeeResultVO> get(@PathVariable Long id) {
        return success(baseEmployeeBiz.getEmployeeUserById(id));
    }

    @Override
    public R<BaseEmployee> handlerSave(BaseEmployeeSaveVO model) {
        FileParamVO photoIdFile = model.getPhotoIdFile();
        if (StrUtil.isBlank(model.getUsername())) {
            model.setUsername(defUserService.genLoginAccount());
        }
        if (ObjectUtil.isNotNull(photoIdFile)) {
            model.setPhotoId(photoIdFile.getId());
        }
        FileParamVO healthCodeIdFile = model.getHealthCodeIdFile();
        if (ObjectUtil.isNotNull(healthCodeIdFile)) {
            model.setHealthCodeId(healthCodeIdFile.getId());
        }
        return R.success(baseEmployeeBiz.save(model));
    }

    /**
     * 自定义更新
     *
     * @param baseEmployeeUpdateVO 修改VO
     * @return 返回SUCCESS_RESPONSE, 调用默认更新, 返回其他不调用默认更新
     */
    @Override
    public R<BaseEmployee> handlerUpdate(BaseEmployeeUpdateVO baseEmployeeUpdateVO) {
        FileParamVO photoIdFile = baseEmployeeUpdateVO.getPhotoIdFile();
        if (ObjectUtil.isNotNull(photoIdFile)) {
            baseEmployeeUpdateVO.setPhotoId(photoIdFile.getId());
        }
        FileParamVO healthCodeIdFile = baseEmployeeUpdateVO.getHealthCodeIdFile();
        if (ObjectUtil.isNotNull(healthCodeIdFile)) {
            baseEmployeeUpdateVO.setHealthCodeId(healthCodeIdFile.getId());
        }
        return super.handlerUpdate(baseEmployeeUpdateVO);
    }

    @Override
    public R<Boolean> handlerDelete(List<Long> ids) {
        return R.success(baseEmployeeBiz.delete(ids));
    }

    @Override
    public IPage<BaseEmployee> query(PageParams<BaseEmployeePageQuery> params) {
        DataScopeHelper.startDataScope("base_employee");
        return super.query(params);
    }
}


