package top.kx.kxss.app.controller.order;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.exception.code.ExceptionCode;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.service.cash.PosCashServiceService;
import top.kx.kxss.app.vo.query.cash.PosCashPageQuery;
import top.kx.kxss.app.vo.result.cash.PosCashResultVO;
import top.kx.kxss.app.vo.save.cash.PosCashSaveVO;
import top.kx.kxss.app.vo.update.cash.PosCashUpdateVO;
import top.kx.kxss.base.manager.order.OrderManager;


/**
 * <p>
 * 前端控制器
 * pos结算 含商品出库 服务 台费结算
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:04:53
 * @create [2023-04-19 14:04:53] [dou] [代码生成器生成]
 */

@Api(value = "/app/order", tags = "订单记录")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/order")
public class OrderController extends SuperController<PosCashServiceService, Long, PosCash, PosCashSaveVO, PosCashUpdateVO, PosCashPageQuery, PosCashResultVO> {

    private final OrderManager orderManager;
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }


    @ApiOperation(value = "分页查询订单记录", notes = "分页查询订单")
    @PostMapping("/page")
    @WebLog(value = "'分页查询会员收银记录:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<PosCashResultVO>> page(@RequestBody @Validated PageParams<PosCashPageQuery> params) {
        params.getModel().setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return super.page(params);
    }

    @Override
    public R<PosCashResultVO> getDetail(Long aLong) {
        return success(superService.detail(aLong));
    }

    @ApiOperation(value = "分页查询会员消费记录", notes = "分页查询会员消费记录")
    @PostMapping("/orderPage")
    @WebLog(value = "'分页查询会员收银记录:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<Page<PosCashResultVO>> orderList(@RequestBody @Validated PageParams<PosCashPageQuery> params) {
        if (ObjectUtil.isNull(params.getModel().getMemberId())) {
            return R.fail(ExceptionCode.ILLEGAL_ARGUMENT_EX);
        }
        return orderManager.page(params);
    }


    @ApiOperation(value = "部分退款", notes = "部分退款")
    @PostMapping("/partialRefund")
    public R<Boolean> partialRefund() {
        return success(orderManager.partialRefund());
    }

    @ApiOperation(value = "整单退款", notes = "整单退款")
    @PostMapping("/refund")
    public R<Boolean> refund() {
        return success(true);
    }


}
