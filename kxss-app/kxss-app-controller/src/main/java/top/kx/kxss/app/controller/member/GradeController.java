package top.kx.kxss.app.controller.member;

import cn.hutool.core.bean.BeanUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.base.entity.member.grade.MemberGrade;
import top.kx.kxss.base.service.member.grade.MemberGradeService;
import top.kx.kxss.base.vo.query.member.grade.MemberGradePageQuery;
import top.kx.kxss.base.vo.result.member.grade.MemberGradeResultVO;
import top.kx.kxss.datascope.DataScopeBizHelper;
import top.kx.kxss.datascope.model.DataScopeBizEnum;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 会员等级信息
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/grade")
@Api(value = "/app/grade", tags = "会员等级相关API")
public class GradeController {

    @Autowired
    private MemberGradeService memberGradeService;

    @ApiOperation(value = "会员等级列表", notes = "会员等级列表")
    @PostMapping("/list")
    public R<List<MemberGradeResultVO>> query(@RequestBody MemberGradePageQuery query) {
        LbQueryWrap<MemberGrade> queryWrap = Wraps.<MemberGrade>lbQ();
//        queryWrap.eq(MemberGrade::getCreatedOrgId, ContextUtil.getCurrentCompanyId());
        queryWrap.eq(MemberGrade::getDeleteFlag, 0);
        queryWrap.eq(MemberGrade::getIsScan, false);
        if (Objects.isNull(query) || Objects.isNull(query.getIsContainStop()) || !query.getIsContainStop()) {
            queryWrap.eq(MemberGrade::getStatus, true);
        }
        DataScopeBizHelper.startDataScope(DataScopeBizEnum.MEMBER,"member_grade");
        List<MemberGradeResultVO> collect = memberGradeService.list(queryWrap).stream().map(v -> BeanUtil.copyProperties(v, MemberGradeResultVO.class)).collect(Collectors.toList());
        return R.success(collect);
    }
}


