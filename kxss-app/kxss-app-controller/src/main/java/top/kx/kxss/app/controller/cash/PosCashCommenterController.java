package top.kx.kxss.app.controller.cash;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.app.service.cash.PosCashCommenterService;
import top.kx.kxss.app.entity.cash.PosCashCommenter;
import top.kx.kxss.app.vo.query.cash.PosCashPageQuery;
import top.kx.kxss.app.vo.result.cash.PosCashResultVO;
import top.kx.kxss.app.vo.save.cash.PosCashCommenterSaveVO;
import top.kx.kxss.app.vo.update.cash.PosCashCommenterUpdateVO;
import top.kx.kxss.app.vo.result.cash.PosCashCommenterResultVO;
import top.kx.kxss.app.vo.query.cash.PosCashCommenterPageQuery;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * 订单相关提成人
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-16 19:15:27
 * @create [2024-04-16 19:15:27] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/posCashCommenter")
@Api(value = "PosCashCommenter", tags = "订单相关提成人")
public class PosCashCommenterController extends SuperController<PosCashCommenterService, Long, PosCashCommenter, PosCashCommenterSaveVO,
    PosCashCommenterUpdateVO, PosCashCommenterPageQuery, PosCashCommenterResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }


    @ApiOperation(value = "批量新增", notes = "批量新增,建议每次只能加一次类型")
    @PostMapping("/batchSave")
    public R<Boolean> batchSave(@RequestBody List<PosCashCommenterSaveVO> params) {
        return R.success(superService.batchSave(params));
    }

}


