package top.kx.kxss.app.controller.cash.refund;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.app.service.cash.refund.PosCashRefundPaymentService;
import top.kx.kxss.app.entity.cash.refund.PosCashRefundPayment;
import top.kx.kxss.app.vo.save.cash.refund.PosCashRefundPaymentSaveVO;
import top.kx.kxss.app.vo.update.cash.refund.PosCashRefundPaymentUpdateVO;
import top.kx.kxss.app.vo.result.cash.refund.PosCashRefundPaymentResultVO;
import top.kx.kxss.app.vo.query.cash.refund.PosCashRefundPaymentPageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 退款单
 * </p>
 *
 * <AUTHOR>
 * @date 2023-11-13 16:05:52
 * @create [2023-11-13 16:05:52] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/posCashRefundPayment")
@Api(value = "PosCashRefundPayment", tags = "退款单")
public class PosCashRefundPaymentController extends SuperController<PosCashRefundPaymentService, Long, PosCashRefundPayment, PosCashRefundPaymentSaveVO,
    PosCashRefundPaymentUpdateVO, PosCashRefundPaymentPageQuery, PosCashRefundPaymentResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


