package top.kx.kxss.app.controller.cash;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.app.service.cash.PosCashStopService;
import top.kx.kxss.app.entity.cash.PosCashStop;
import top.kx.kxss.app.vo.save.cash.PosCashStopSaveVO;
import top.kx.kxss.app.vo.update.cash.PosCashStopUpdateVO;
import top.kx.kxss.app.vo.result.cash.PosCashStopResultVO;
import top.kx.kxss.app.vo.query.cash.PosCashStopPageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 明细停止记录
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-14 13:42:52
 * @create [2024-10-14 13:42:52] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/posCashStop")
@Api(value = "PosCashStop", tags = "明细停止记录")
public class PosCashStopController extends SuperController<PosCashStopService, Long, PosCashStop, PosCashStopSaveVO,
    PosCashStopUpdateVO, PosCashStopPageQuery, PosCashStopResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


