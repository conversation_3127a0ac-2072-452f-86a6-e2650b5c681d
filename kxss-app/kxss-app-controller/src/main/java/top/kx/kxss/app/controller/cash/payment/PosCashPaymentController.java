package top.kx.kxss.app.controller.cash.payment;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.service.cash.payment.PosCashPaymentService;
import top.kx.kxss.app.vo.query.cash.payment.PosCashPaymentPageQuery;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO;
import top.kx.kxss.app.vo.save.cash.payment.PosCashPaymentSaveVO;
import top.kx.kxss.app.vo.update.cash.payment.PosCashPaymentUpdateVO;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.service.tenant.DefUserService;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 商品结算单收款子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:38:34
 * @create [2023-04-19 14:38:34] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/posCashPayment")
@Api(value = "PosCashPayment", tags = "商品结算单收款子表")
public class PosCashPaymentController extends SuperController<PosCashPaymentService, Long, PosCashPayment, PosCashPaymentSaveVO,
        PosCashPaymentUpdateVO, PosCashPaymentPageQuery, PosCashPaymentResultVO> {
    private final EchoService echoService;
    private final DefUserService userService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @ApiOperation(value = "分页查询订单交易记录", notes = "分页查询订单交易记录")
    @PostMapping("/paymentPage")
    @WebLog(value = "'分页查询订单交易记录:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<PosCashPaymentResultVO>> paymentPage(@RequestBody PageParams<PosCashPaymentPageQuery> params) {
        PosCashPaymentPageQuery pageQuery = params.getModel();
        if (pageQuery.getCashId() == null) {
            return R.fail("订单ID不为空");
        }
        pageQuery.setStatus(PosCashPaymentStatusEnum.PAY_SUCCESS.getCode());
        R<IPage<PosCashPaymentResultVO>> page = super.page(params);
        List<PosCashPaymentResultVO> records = page.getData().getRecords();
        List<Long> updateByIds = records.stream().map(PosCashPaymentResultVO::getUpdatedBy).collect(Collectors.toList());
        //获取操作人信息信息
        Map<Long, DefUser> updateByMap = CollUtil.isNotEmpty(updateByIds) ? userService.list(Wraps.<DefUser>lbQ().in(DefUser::getId, updateByIds))
                .stream().collect(Collectors.toMap(DefUser::getId, k -> k)) : new HashMap<>();
        for (PosCashPaymentResultVO record : page.getData().getRecords()) {
            DefUser user = updateByMap.get(record.getUpdatedBy());
            record.getEchoMap().put("updatedBy", user != null ? user.getNickName() : "");
        }
        return page;
    }

    @ApiOperation(value = "分页查询会员订单交易记录", notes = "分页查询会员订单交易记录")
    @PostMapping("/paymentMemberPage")
    @WebLog(value = "'分页查询会员订单交易记录:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<PosCashPaymentResultVO>> paymentMemberPage(@RequestBody PageParams<PosCashPaymentPageQuery> params) {
        PosCashPaymentPageQuery pageQuery = params.getModel();
        if (pageQuery.getMemberId() == null) {
            return R.fail("会员ID不为空");
        }
        pageQuery.setStatus(PosCashPaymentStatusEnum.PAY_SUCCESS.getCode());
        IPage<PosCashPayment> posCashPaymentIPage = params.buildPage(PosCashPayment.class);
        LbQueryWrap<PosCashPayment> wrap = Wraps.lbQ();
        wrap.eq(StrUtil.isNotBlank(pageQuery.getOrderSource()), PosCashPayment::getOrderSource, pageQuery.getOrderSource());
        wrap.eq(StrUtil.isNotBlank(pageQuery.getStatus()), PosCashPayment::getStatus, pageQuery.getStatus());
        wrap.inSql(ObjectUtil.isNotNull(pageQuery.getMemberId()), PosCashPayment::getCashId,
                "select id from pos_cash where delete_flag = 0 and member_id = " + pageQuery.getMemberId());
        IPage<PosCashPayment> posCashPaymentIPage1 = superService.page(posCashPaymentIPage, wrap);
        IPage<PosCashPaymentResultVO> page = BeanPlusUtil.toBeanPage(posCashPaymentIPage1, PosCashPaymentResultVO.class);
        List<PosCashPaymentResultVO> records = page.getRecords();
        List<Long> updateByIds = records.stream().map(PosCashPaymentResultVO::getUpdatedBy).collect(Collectors.toList());
        //获取操作人信息信息
        Map<Long, DefUser> updateByMap = CollUtil.isNotEmpty(updateByIds) ? userService.list(Wraps.<DefUser>lbQ().in(DefUser::getId, updateByIds))
                .stream().collect(Collectors.toMap(DefUser::getId, k -> k)) : new HashMap<>();
        for (PosCashPaymentResultVO record : page.getRecords()) {
            DefUser user = updateByMap.get(record.getUpdatedBy());
            record.getEchoMap().put("updatedBy", user != null ? user.getNickName() : "");
        }
        echoService.action(page);
        return R.success(page);
    }

}


