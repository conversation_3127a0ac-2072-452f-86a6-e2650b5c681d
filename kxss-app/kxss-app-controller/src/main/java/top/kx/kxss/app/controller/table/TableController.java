package top.kx.kxss.app.controller.table;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.app.service.table.TableService;
import top.kx.kxss.app.vo.query.cash.PosCashPageQuery;
import top.kx.kxss.app.vo.query.cash.product.PosCashProductPageQuery;
import top.kx.kxss.app.vo.query.cash.service.PosCashServicePageQuery;
import top.kx.kxss.app.vo.result.MenuPerResultVo;
import top.kx.kxss.app.vo.result.product.AppProductResultVo;
import top.kx.kxss.app.vo.result.service.AppServiceResultVO;
import top.kx.kxss.app.vo.save.cash.PosCashSaveVO;
import top.kx.kxss.app.vo.save.cash.service.PosCashServiceSaveVO;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.vo.query.product.BaseProductPageQuery;
import top.kx.kxss.base.vo.query.product.category.BaseProductCategoryPageQuery;
import top.kx.kxss.base.vo.query.service.BaseServicePageQuery;
import top.kx.kxss.base.vo.query.table.BaseTableInfoPageQuery;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.member.coupon.MemberCouponResultVO;
import top.kx.kxss.base.vo.result.product.category.BaseProductCategoryResultVO;
import top.kx.kxss.base.vo.result.service.category.BaseServiceCategoryResultVO;
import top.kx.kxss.base.vo.result.table.BaseTableInfoResultVO;
import top.kx.kxss.base.vo.save.table.BaseTableInfoSaveVO;
import top.kx.kxss.base.vo.update.table.BaseTableInfoUpdateVO;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * 台桌数据接口
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-18 23:00:01
 * @create [2023-04-18 23:00:01] [zhou]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/table")
@Api(value = "/app/table", tags = "台桌信息")
public class TableController extends SuperController<TableService, Long, BaseTableInfo, BaseTableInfoSaveVO,
        BaseTableInfoUpdateVO, BaseTableInfoPageQuery, BaseTableInfoResultVO> {


    @ApiOperation(value = "获取是否拥有菜单", notes = "获取是否拥有菜单")
    @PostMapping("/menuPer")
    public R<MenuPerResultVo> menuPer() {
        return success(superService.menuPer());
    }
    @ApiOperation(value = "获取所有台桌", notes = "获取所有台桌")
    @PostMapping("/listAll")
    public R<Map<String, Object>> listAll(@RequestBody BaseTableInfoPageQuery query) {
        return success(superService.listAll(query));
    }

    @ApiOperation(value = "获取缓存", notes = "获取缓存")
    @PostMapping("/getCache")
    public R<Map<String, Object>> getCache(@RequestBody BaseTableInfoPageQuery query) {
        return success(superService.getCache());
    }


    @ApiOperation(value = "开台", notes = "开台")
    @PostMapping("/startTable")
    public R<Map<String, Object>> startTable(@RequestBody PosCashSaveVO saveVO) {
        return success(superService.startTable(saveVO));
    }

    @ApiOperation(value = "搜索会员", notes = "搜索会员")
    @PostMapping("/queryMember")
    public R<List<MemberInfoResultVO>> queryMember(@RequestBody Map<String, Object> query) {
        return success(superService.queryMember(query));
    }

    @ApiOperation(value = "台桌详情", notes = "台桌详情")
    @PostMapping("/queryTableDetail")
    public R<Map<String, Object>> queryTableDetail(@RequestBody BaseTableInfoPageQuery query) {
        return success(superService.queryTableDetail(query));
    }

    @ApiOperation(value = "商品列表", notes = "商品列表")
    @PostMapping("/queryProducts")
    public R<List<AppProductResultVo>> queryProducts(@RequestBody BaseProductPageQuery query) {
        return success(superService.queryProducts(query));
    }

    @ApiOperation(value = "商品类型列表", notes = "商品类型列表")
    @PostMapping("/queryProductCategory")
    public R<List<BaseProductCategoryResultVO>> queryProductCategory(@RequestBody BaseProductCategoryPageQuery query) {
        return success(superService.queryProductCategory(query));
    }

    @ApiOperation(value = "商品类型列表", notes = "商品类型列表")
    @PostMapping("/saveProduct")
    public R<String> saveProduct(@RequestBody Map<String, Object> query) {
        String msg = superService.saveProduct(query);
        if(StringUtils.isEmpty(msg)) {
            return success("");
        } else {
            return fail(msg);
        }
    }

    @ApiOperation(value = "修改商品数量", notes = "修改商品数量")
    @PostMapping("/saveProductCount")
    public R<String> saveProductCount(@RequestBody PosCashProductPageQuery saveVO) {
        String msg = superService.saveProductCount(saveVO);
        if(StringUtils.isEmpty(msg)) {
            return success("");
        } else {
            return fail(msg);
        }
    }

    @ApiOperation(value = "获取会员优惠券", notes = "获取会员优惠券")
    @PostMapping("/queryMemberCoupon")
    public R<List<MemberCouponResultVO>> queryMemberCoupon(@RequestBody Map<String, Object> query) {
        return success(superService.queryMemberCoupon(query));
    }

    @ApiOperation(value = "修改商品数量", notes = "修改商品数量")
    @PostMapping("/deleteProduct")
    public R<String> deleteProduct(@RequestBody PosCashProductPageQuery saveVO) {
        String msg = superService.deleteProduct(saveVO);
        if(StringUtils.isEmpty(msg)) {
            return success("");
        } else {
            return fail(msg);
        }
    }

    @ApiOperation(value = "会员登录", notes = "会员登录")
    @PostMapping("/updateCashMember")
    public R<String> updateCashMember(@RequestBody Map<String, Object> query) {
        return success(superService.updateCashMember(query));
    }

    @ApiOperation(value = "保存台桌设置", notes = "保存台桌设置")
    @PostMapping("/saveCashTableOperate")
    public R<String> saveCashTableOperate(@RequestBody Map<String, Object> query) {
        return success(superService.saveCashTableOperate(query));
    }

    @ApiOperation(value = "服务列表", notes = "服务列表")
    @PostMapping("/queryService")
    public R<List<AppServiceResultVO>> queryService(@RequestBody BaseServicePageQuery query) {
        return success(superService.queryService(query));
    }

    @ApiOperation(value = "服务类型", notes = "服务类型")
    @PostMapping("/queryServiceCategory")
    public R<List<BaseServiceCategoryResultVO>> queryServiceCategory(@RequestBody BaseServicePageQuery query) {
        return success(superService.queryServiceCategory(query));
    }

    @ApiOperation(value = "添加服务", notes = "添加服务")
    @PostMapping("/saveService")
    public R<String> saveService(@RequestBody PosCashServiceSaveVO saveVO) {
        return success(superService.saveService(saveVO));
    }

    @ApiOperation(value = "添加服务", notes = "添加服务")
    @PostMapping("/deleteService")
    public R<String> deleteService(@RequestBody PosCashServicePageQuery query) {
        return success(superService.deleteService(query));
    }

    @ApiOperation(value = "保存服务设置", notes = "保存服务设置")
    @PostMapping("/saveCashServiceOperate")
    public R<String> saveCashServiceOperate(@RequestBody Map<String, Object> query) {
        return success(superService.saveCashServiceOperate(query));
    }

    @ApiOperation(value = "全部停止计时", notes = "全部停止计时")
    @PostMapping("/saveStopAll")
    public R<String> saveStopAll(@RequestBody PosCashPageQuery query) {
        return success(superService.saveStopAll(query));
    }

    @ApiOperation(value = "挂单", notes = "挂单")
    @PostMapping("/pendingOrder")
    public R<String> pendingOrder(@RequestBody PosCashPageQuery query) {
        return success(superService.pendingOrder(query));
    }

    @ApiOperation(value = "更新台桌灯状态", notes = "更新台桌灯状态")
    @PostMapping("/saveChangeLight")
    public R<String> saveChangeLight(@RequestBody BaseTableInfoPageQuery query) {
        return success(superService.saveChangeLight(query));
    }

    @ApiOperation(value = "修改商品数量_新", notes = "修改商品数量")
    @PostMapping("/doChangeProductCount")
    public R<String> doChangeProductCount(@RequestBody PosCashProductPageQuery saveVO) {
        String msg = superService.doChangeProductCount(saveVO);
        if(StringUtils.isEmpty(msg)) {
            return success("");
        } else {
            return fail(msg);
        }
    }

    @ApiOperation(value = "手机号搜索会员", notes = "手机号搜索会员")
    @PostMapping("/queryMemberByPhone")
    public R<List<MemberInfoResultVO>> queryMemberByPhone(@RequestBody Map<String, Object> query) {
        return success(superService.queryMemberByPhone(query));
    }

    @ApiOperation(value = "刷新单张台桌", notes = "刷新单张台桌")
    @PostMapping("/reflushTable")
    public R<String> reflushTable(@RequestBody BaseTableInfoPageQuery query) {
        return success(superService.reflushTable(query));
    }
}


