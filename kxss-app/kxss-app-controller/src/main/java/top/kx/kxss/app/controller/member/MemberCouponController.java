package top.kx.kxss.app.controller.member;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.member.coupon.MemberCoupon;
import top.kx.kxss.base.service.member.coupon.MemberCouponService;
import top.kx.kxss.base.vo.query.member.coupon.MemberCouponPageQuery;
import top.kx.kxss.base.vo.result.coupon.BaseCouponInfoResultVO;
import top.kx.kxss.base.vo.result.member.coupon.MemberCouponResultVO;
import top.kx.kxss.base.vo.save.member.coupon.MemberCouponSaveVO;
import top.kx.kxss.base.vo.update.member.coupon.MemberCouponUpdateVO;
import top.kx.kxss.model.enumeration.base.CouponStatusEnum;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * 会员绑定优惠券信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-18 15:36:12
 * @create [2023-04-18 15:36:12] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/memberCoupon")
@Api(value = "MemberCoupon", tags = "会员绑定优惠券信息")
public class MemberCouponController extends SuperController<MemberCouponService, Long, MemberCoupon, MemberCouponSaveVO,
        MemberCouponUpdateVO, MemberCouponPageQuery, MemberCouponResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Override
    public R<MemberCoupon> handlerSave(MemberCouponSaveVO model) {
        model.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return super.handlerSave(model);
    }

    /**
     * 查询会员优惠券
     *
     * @param memberId
     * @return
     */
    @ApiOperation(value = "查询会员优惠券", notes = "查询会员优惠券")
    @GetMapping("/couponList")
    public R<List<BaseCouponInfoResultVO>> couponList(@RequestParam Long memberId, @RequestParam(required = false) String status) {
        List<BaseCouponInfoResultVO> couponResultVOList = superService.getListByMemberId(memberId, status);
        echoService.action(couponResultVOList);
        return success(couponResultVOList);
    }

    @Override
    public R<Boolean> handlerDelete(List<Long> longs) {
        long count = superService.count(Wraps.<MemberCoupon>lbQ()
                .in(MemberCoupon::getId, longs)
                .eq(MemberCoupon::getStatus, CouponStatusEnum.LOCK.getCode())
                .eq(MemberCoupon::getDeleteFlag, 0));
        ArgumentAssert.isFalse(count > 0, "优惠券正在使用，请取消后再试");
        return super.handlerDelete(longs);
    }
}


