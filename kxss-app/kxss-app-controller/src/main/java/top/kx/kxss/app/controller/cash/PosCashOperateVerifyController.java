package top.kx.kxss.app.controller.cash;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.app.service.cash.PosCashOperateVerifyService;
import top.kx.kxss.app.entity.cash.PosCashOperateVerify;
import top.kx.kxss.app.vo.save.cash.PosCashOperateVerifySaveVO;
import top.kx.kxss.app.vo.update.cash.PosCashOperateVerifyUpdateVO;
import top.kx.kxss.app.vo.result.cash.PosCashOperateVerifyResultVO;
import top.kx.kxss.app.vo.query.cash.PosCashOperateVerifyPageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * pos操作验证记录
 * </p>
 *
 * <AUTHOR>
 * @date 2023-12-21 12:18:16
 * @create [2023-12-21 12:18:16] [yh] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/posCashOperateVerify")
@Api(value = "PosCashOperateVerify", tags = "pos操作验证记录")
public class PosCashOperateVerifyController extends SuperController<PosCashOperateVerifyService, Long, PosCashOperateVerify, PosCashOperateVerifySaveVO,
    PosCashOperateVerifyUpdateVO, PosCashOperateVerifyPageQuery, PosCashOperateVerifyResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


