package top.kx.kxss.app.controller.dict;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.model.vo.result.Option;
import top.kx.kxss.oauth.api.DictApi;
import top.kx.kxss.oauth.vo.param.CodeQueryVO;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * 前端控制器
 * 字段
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/dict")
@Api(value = "Dict", tags = "字典数据")
public class DictController {

    @Autowired
    private DictApi dictApi;

    @ApiOperation(value = "根据类型编码查询字典项,并排除指定项", notes = "根据类型编码查询字典项")
    @PostMapping("/findDictMapItemListByKey")
    public R<Map<String, List<Option>>> findDictMapItemListByKey(@RequestBody List<CodeQueryVO> codeQueryVO) {
        return dictApi.findDictMapItemListByKey(codeQueryVO);
    }


}


