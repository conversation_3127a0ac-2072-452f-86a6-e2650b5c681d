package top.kx.kxss.app.controller.tag;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.tag.BaseTag;
import top.kx.kxss.base.service.tag.BaseTagService;
import top.kx.kxss.base.vo.query.tag.BaseTagPageQuery;
import top.kx.kxss.base.vo.result.tag.BaseTagResultVO;
import top.kx.kxss.base.vo.save.tag.BaseTagSaveVO;
import top.kx.kxss.base.vo.update.tag.BaseTagUpdateVO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 标签表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-03-25 09:55:50
 * @create [2023-03-25 09:55:50] [Wang] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/tag")
@Api(value = "Tag", tags = "标签表")
public class TagController extends SuperController<BaseTagService, Long, BaseTag, BaseTagSaveVO,
        BaseTagUpdateVO, BaseTagPageQuery, BaseTagResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Override
    public R<BaseTag> handlerSave(BaseTagSaveVO model) {
        model.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return super.handlerSave(model);
    }

    @Override
    public IPage<BaseTag> query(PageParams<BaseTagPageQuery> params) {
        ArgumentAssert.notBlank(params.getModel().getBizType(), "请输入业务类型");
        return super.query(params);
    }

    @ApiOperation(value = "标签列表", notes = "标签列表")
    @PostMapping("/list")
    public R<List<BaseTagResultVO>> query(BaseTagPageQuery query) {
        ArgumentAssert.notBlank(query.getBizType(), "请输入业务类型");
        LbQueryWrap<BaseTag> queryWrap = Wraps.<BaseTag>lbQ();
        queryWrap.eq(BaseTag::getBizType, query.getBizType());
        queryWrap.eq(BaseTag::getCreatedOrgId, ContextUtil.getCurrentCompanyId());
        List<BaseTagResultVO> collect = superService.list(queryWrap).stream().map(v -> BeanUtil.copyProperties(v, BaseTagResultVO.class)).collect(Collectors.toList());
        echoService.action(collect);
        return R.success(collect);
    }
}


