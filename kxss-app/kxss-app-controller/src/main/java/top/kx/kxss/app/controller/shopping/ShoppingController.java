package top.kx.kxss.app.controller.shopping;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.app.service.shopping.ShoppingService;
import top.kx.kxss.app.vo.IdQuery;
import top.kx.kxss.app.vo.member.MemberIdQuery;
import top.kx.kxss.app.vo.member.MemberPhoneQuery;
import top.kx.kxss.app.vo.query.cash.*;
import top.kx.kxss.app.vo.query.cash.product.PosCashProductNumVO;
import top.kx.kxss.app.vo.result.cash.QueryShopDetailVO;
import top.kx.kxss.app.vo.result.product.AppProductResultVo;
import top.kx.kxss.base.vo.query.product.BaseProductPageQuery;
import top.kx.kxss.base.vo.query.product.category.BaseProductCategoryPageQuery;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.member.coupon.MemberCouponResultVO;
import top.kx.kxss.base.vo.result.product.category.BaseProductCategoryResultVO;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * 购物相关API
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/shopping")
@Api(value = "Shopping", tags = "购物相关API")
public class ShoppingController {

    @Autowired
    private ShoppingService shoppingService;

    @ApiOperation(value = "手机号搜索会员", notes = "手机号搜索会员")
    @PostMapping("/getMemberByPhone")
    public R<MemberInfoResultVO> getMemberByPhone(@RequestBody @Validated MemberPhoneQuery query) {
        return R.success(shoppingService.getMemberByPhone(query.getPhone()));
    }

    @ApiOperation(value = "商品列表", notes = "商品列表")
    @PostMapping("/queryProducts")
    public R<List<AppProductResultVo>> queryProducts(@RequestBody BaseProductPageQuery query) {
        return R.success(shoppingService.queryProducts(query));
    }

    @ApiOperation(value = "商品类型列表", notes = "商品类型列表")
    @PostMapping("/queryProductCategory")
    public R<List<BaseProductCategoryResultVO>> queryProductCategory(@RequestBody BaseProductCategoryPageQuery query) {
        return R.success(shoppingService.queryProductCategory(query));
    }

    @ApiOperation(value = "下单详情", notes = "下单详情")
    @PostMapping("/queryShopDetail")
    public R<QueryShopDetailVO> queryShopDetail(@RequestBody PosCashIdQuery query) {
        return R.success(shoppingService.queryShopDetail(query.getPosCashId()));
    }

    @ApiOperation(value = "保存商品", notes = "保存商品")
    @PostMapping("/saveProduct")
    public R<Boolean> saveProduct(@RequestBody @Validated PosCashProductNumVO numVO) {
        return R.success(shoppingService.saveProduct(numVO));
    }

    @ApiOperation(value = "修改商品数量", notes = "修改商品数量")
    @PostMapping("/productNum")
    public R<Boolean> productNum(@RequestBody @Validated PosCashProductNumVO saveVO) {
        return R.success(shoppingService.productNum(saveVO));
    }

    @ApiOperation(value = "删除商品", notes = "删除商品")
    @PostMapping("/delProduct")
    public R<Boolean> delProduct(@RequestBody PosCashProductNumVO numVO) {
        return R.success(shoppingService.delProduct(numVO.getId()));
    }

    @ApiOperation(value = "绑定会员", notes = "绑定会员")
    @PostMapping("/bindMember")
    public R<Boolean> bindMember(@RequestBody @Validated PosCashMemberQuery query) {
        return R.success(shoppingService.bindMember(query));
    }

    @ApiOperation(value = "获取会员优惠劵", notes = "获取会员优惠劵")
    @PostMapping("/memberCoupon")
    public R<List<MemberCouponResultVO>> memberCoupon(@RequestBody @Validated MemberIdQuery query) {
        return R.success(shoppingService.memberCoupon(query.getMemberId()));
    }

    @ApiOperation(value = "单品备注", notes = "单品备注")
    @PostMapping("/singleRemark")
    public R<Boolean> singleRemark(@RequestBody @Validated PosCashSingleRemarkQuery query) {
        return R.success(shoppingService.singleRemark(query));
    }

    @ApiOperation(value = "单品优惠", notes = "单品优惠")
    @PostMapping("/singleSale")
    public R<Boolean> singleSale(@RequestBody @Validated PosCashSingleSaleQuery query) {
        return R.success(shoppingService.singleSale(query));
    }

    @ApiOperation(value = "取消单品优惠", notes = "取消单品优惠")
    @PostMapping("/disSale")
    public R<Boolean> disSale(@RequestBody @Validated IdQuery query) {
        return R.success(shoppingService.disSale(query.getId()));
    }

    @ApiOperation(value = "销售人员列表", notes = "销售人员列表")
    @PostMapping("/empList")
    public R<List<BaseEmployeeResultVO>> empList() {
        return R.success(shoppingService.empList());
    }

    @ApiOperation(value = "整单操作打折/减免/免单", notes = "整单操作打折/减免/免单")
    @PostMapping("/completeOrder")
    public R<Boolean> completeOrder(@RequestBody @Validated PosCashOrderQuery query) {
        return R.success(shoppingService.completeOrder(query));
    }

    @ApiOperation(value = "整单取消打折/减免", notes = "整单取消打折/减免")
    @PostMapping("/disCompleteOrder")
    public R<Boolean> disCompleteOrder(@RequestBody @Validated PosCashIdQuery query) {
        return R.success(shoppingService.disCompleteOrder(query));
    }

    @ApiOperation(value = "整单重置", notes = "整单重置")
    @PostMapping("/reset")
    public R<Boolean> reset(@RequestBody @Validated PosCashIdQuery query) {
        return R.success(shoppingService.reset(query));
    }

}


