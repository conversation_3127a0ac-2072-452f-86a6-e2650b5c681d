package top.kx.kxss.app.controller.pay;

import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.app.granter.CalcAmountGranter;
import top.kx.kxss.app.vo.pay.WxNotifyVO;

import java.time.LocalDateTime;
import java.util.Map;


/**
 * <p>
 * 回调接口
 * </p>
 *
 * <AUTHOR>
 */

@Api(value = "/app/callback", tags = "回调接口")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/callback")
public class CallbackController {

    private final CalcAmountGranter calcAmountGranter;

    @ApiOperation(value = "微信回调", notes = "微信回调")
    @PostMapping("/wx_notify")
    public R<Map<String, Object>> wxNotify(@RequestBody WxNotifyVO notifyVO) {
        log.info("回调信息：{}，时间：{}", JSON.toJSONString(notifyVO), DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
        return R.success(calcAmountGranter.wxNotify(notifyVO));
    }

    @ApiOperation(value = "微信服务商退款回调", notes = "微信服务商退款回调")
    @PostMapping("/wx_refund_notify")
    public R<Map<String, Object>> wxRefundNotify(@RequestBody WxNotifyVO notifyVO) {
        log.info("回调信息：{}，时间：{}", JSON.toJSONString(notifyVO), DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
        return R.success(calcAmountGranter.wxRefundNotify(notifyVO));
    }
}
