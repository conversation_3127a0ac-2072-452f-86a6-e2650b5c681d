package top.kx.kxss.app.controller.cash;

import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.service.cash.PosCashServiceService;
import top.kx.kxss.app.vo.pay.PayResponseVO;
import top.kx.kxss.app.vo.query.cash.PosCashPageQuery;
import top.kx.kxss.app.vo.query.cash.payment.PosCashPaymentPageQuery;
import top.kx.kxss.app.vo.result.cash.PosCashResultVO;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPayVo;
import top.kx.kxss.app.vo.save.cash.PosCashDelOrderQuery;
import top.kx.kxss.app.vo.save.cash.PosCashSaveVO;
import top.kx.kxss.app.vo.save.cash.payment.PosCashPaymentSaveVO;
import top.kx.kxss.app.vo.update.cash.PosCashUpdateVO;
import top.kx.kxss.model.enumeration.base.BillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillTypeEnum;
import top.kx.kxss.model.enumeration.base.PosCashTypeEnum;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 前端控制器
 * pos结算 含商品出库 服务 台费结算
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:04:53
 * @create [2023-04-19 14:04:53] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/posCash")
@Api(value = "/app/posCash", tags = "pos结算 含商品出库 服务 台费结算")
public class PosCashController extends SuperController<PosCashServiceService, Long, PosCash, PosCashSaveVO, PosCashUpdateVO, PosCashPageQuery, PosCashResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Override
    public R<PosCash> handlerSave(PosCashSaveVO model) {
        model.setBillState(BillStateEnum.NORMAL.getCode());
        model.setBillDate(LocalDate.now());
        model.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return super.handlerSave(model);
    }

    @Override
    public R<PosCashResultVO> getDetail(Long aLong) {
        return success(superService.detail(aLong));
    }


    @GetMapping("/getInfoByMemberId/{memberId}")
    @WebLog("根据会员查询记录")
    @ApiOperation("根据会员查询记录")
    public R<List<PosCashResultVO>> getInfoByMemberId(@PathVariable(value = "memberId") Long memberId) {
        return super.success(superService.getInfoByMemberId(memberId));
    }


    @ApiOperation(value = "分页查询会员收银记录", notes = "分页查询会员收银记录")
    @PostMapping("/memberPage")
    @WebLog(value = "'分页查询会员收银记录:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<PosCashResultVO>> memberPage(@RequestBody PageParams<PosCashPageQuery> params) {
        PosCashPageQuery pageQuery = params.getModel();
        if (pageQuery.getMemberId() == null) {
            return R.fail("请选择会员");
        }
        return super.page(params);
    }

    @ApiOperation(value = "根据会员id查询会员所持有的所有优惠券", notes = "根据会员id查询会员所持有的所有优惠券")
    @GetMapping("/memeberCouponByType")
    public R<Map<String, Object>> memeberCouponByType(@RequestParam(value = "memberId") Long memberId, @RequestParam(value = "cashId") Long cashId) {
        return super.success(superService.memeberCouponByType(memberId, cashId));
    }

    @ApiOperation(value = "根据会员id，优惠券id查询会员优惠券详情", notes = "根据会员id，优惠券id查询会员优惠券详情")
    @GetMapping("/memebercCouponDetail")
    public R<Map<String, Object>> memebercCouponDetail(@RequestParam(value = "memberId") Long memberId, @RequestParam(value = "couponId") Long couponId) {
        return super.success(superService.memebercCouponDetail(memberId, couponId));
    }


    @ApiOperation(value = "根据台桌id查询支付与会员信息", notes = "根据台桌id查询支付与会员信息")
    @PostMapping("/tablePosCash")
    public R<Map<String, Object>> tablePosCash(@RequestBody Map<String, Object> param) {
        return super.success(superService.tablePosCash(param));
    }

    @ApiOperation(value = "订单数据", notes = "订单数据")
    @PostMapping("/findList")
    public R<List<PosCash>> findList(@RequestBody List<Long> ids) {
        return R.success(superService.list(Wraps.<PosCash>lbQ()
                .eq(PosCash::getDeleteFlag, 0)
                .in(PosCash::getId, ids)));
    }


    @ApiOperation(value = "根据订单id更新payment表", notes = "根据台桌id查询支付与会员信息")
    @PostMapping("/paymentSave")
    public R<String> paymentSave(@RequestBody PosCashPaymentSaveVO vo) {
        String msg = superService.paymentSave(vo);
        if (StringUtils.isEmpty(msg)) {
            return success("");
        } else {
            return fail(msg);
        }
    }


    @ApiOperation(value = "根据订单id判断优惠券是否可用", notes = "根据台桌id查询支付与会员信息")
    @PostMapping("/couponChecked")
    public R<String> couponChecked(@RequestBody Map<String, Object> param) {
        String msg = superService.couponChecked(param);
        if (StringUtils.isEmpty(msg)) {
            return success("");
        } else {
            return fail(msg);
        }
    }

    @ApiOperation(value = "根据台桌id查询支付与会员信息", notes = "根据台桌id查询支付与会员信息")
    @PostMapping("/saveUsingCoupon")
    public R<Boolean> saveUsingCoupon(@RequestBody Map<String, Object> param) {
        return super.success(superService.saveUsingCoupon(param));
    }

    @ApiOperation(value = "获取使用的优惠券和卡", notes = "获取使用的优惠券")
    @PostMapping("/queryUsingEquity")
    public R<Map<String, Object>> queryUsingEquity(@RequestBody Map<String, Object> param) {
        return super.success(superService.queryUsingEquity(param));
    }

    @ApiOperation(value = "取消使用的优惠券和卡", notes = "获取使用的优惠券")
    @PostMapping("/disabledCoupon")
    public R<String> disabledCoupon(@RequestBody Map<String, Object> param) {
        return super.success(superService.disabledCoupon(param));
    }

    @ApiOperation(value = "取消使用的优惠券和卡", notes = "获取使用的优惠券")
    @PostMapping("/queryMemberBalance")
    public R<BigDecimal> queryMemberBalance(@RequestBody Map<String, Object> param) {
        return super.success(superService.queryMemberBalance(param));
    }

    @ApiOperation(value = "现金支付", notes = "现金支付")
    @PostMapping("/doCashPay")
    public R<PosCashPayVo> doCashPay(@RequestBody PosCashPaymentSaveVO saveVO) {
        return super.success(superService.doCashPay(saveVO));
    }

    @ApiOperation(value = "取消抹零", notes = "取消抹零")
    @PostMapping("/removeMl")
    public R<String> removeMl(@RequestBody PosCashPaymentPageQuery queryVo) {
        String msg = superService.removeMl(queryVo);
        if (StringUtils.isEmpty(msg)) {
            return success("");
        } else {
            return fail(msg);
        }
    }

    @ApiOperation(value = "取消支付", notes = "取消支付")
    @PostMapping("/removePay")
    public R<String> removePay(@RequestBody PosCashPaymentPageQuery queryVo) {
        String msg = superService.removePay(queryVo);
        if (StringUtils.isEmpty(msg)) {
            return success("");
        } else {
            return fail(msg);
        }
    }

    @ApiOperation(value = "抹零", notes = "抹零")
    @PostMapping("/roundPrice")
    public R<String> roundPrice(@RequestBody PosCashPageQuery queryVo) {
        String msg = superService.roundPrice(queryVo);
        if (StringUtils.isEmpty(msg)) {
            return success("");
        } else {
            return fail(msg);
        }
    }

    @ApiOperation(value = "余额支付", notes = "余额支付")
    @PostMapping("/doBalancePay")
    public R<PosCashPayVo> doBalancePay(@RequestBody PosCashPaymentSaveVO saveVO) {
        PosCashPayVo result = superService.doBalancePay(saveVO);
        if (result.getSuccess()) {
            return super.success(result);
        } else {
            return fail(result.getMsg());
        }
    }

    @ApiOperation(value = "取消余额支付", notes = "取消余额支付")
    @PostMapping("/removeBalancePay")
    public R<String> removeBalancePay(@RequestBody PosCashPaymentPageQuery queryVo) {
        String msg = superService.removeBalancePay(queryVo);
        if (StringUtils.isEmpty(msg)) {
            return success("");
        } else {
            return fail(msg);
        }
    }

    @ApiOperation(value = "聚合支付 生成payment", notes = "聚合支付 生成payment")
    @PostMapping("/createPayment")
    public R<PosCashPayment> createPayment(@RequestBody PosCashPaymentSaveVO saveVO) {
        PosCashPayment result = superService.createPayment(saveVO);
        if (null != result) {
            return success(result);
        } else {
            return fail("支付失败");
        }
    }

    @ApiOperation(value = "聚合支付保存结果", notes = "聚合支付保存结果")
    @PostMapping("/savePayResult")
    public R<PosCashPayVo> savePayResult(@RequestBody PayResponseVO saveVO) {
        PosCashPayVo result = superService.savePayResult(saveVO);
        if (result.getSuccess()) {
            return success(result);
        } else {
            return fail("支付失败");
        }
    }

    @ApiOperation(value = "清空订单数据", notes = "清空订单数据")
    @PostMapping("/delOrder")
    public R<Boolean> delOrder(@RequestBody @Validated PosCashDelOrderQuery vo) {
        return super.success(superService.delOrder(vo));
    }

    @ApiOperation(value = "检验会员是否在使用中", notes = "检验会员是否在使用中")
    @PostMapping("/checkMemberIsUse")
    public R<Boolean> checkMemberIsUse(@RequestBody @Validated List<Long> longs) {
        return super.success(superService.checkMemberIsUse(longs));
    }


    @ApiOperation(value = "校验卡是否关联除了已完成和已退款的订单", notes = "校验卡是否关联除了已完成和已退款的订单")
    @PostMapping("/checkStatusByMemberCardId")
    public R<Boolean> checkStatusByMemberCardId(@RequestParam Long memberCardId) {
        return super.success(superService.checkStatusByMemberCardId(memberCardId));
    }


    @ApiOperation(value = "根据卡id查询关联的订单", notes = "根据卡id查询关联的订单")
    @GetMapping("/queryListByCardId")
    public R<List<PosCashResultVO>> queryListByCardId(@RequestParam(value = "memberCardId") Long memberCardId) {
        List<PosCashResultVO> posCashResultVOS = superService.queryListByCardId(memberCardId);
        echoService.action(posCashResultVOS);
        return super.success(posCashResultVOS);
    }

    @ApiOperation(value = "根据卡id查询关联的订单", notes = "根据卡id查询关联的订单")
    @GetMapping("/queryListByTableId")
    public R<List<PosCashResultVO>> listByTableId(@RequestParam(value = "tableId") Long tableId) {
        List<PosCash> posCashList = superService.list(Wraps.<PosCash>lbQ().eq(PosCash::getDeleteFlag, 0)
                .eq(PosCash::getBillType, PosCashBillTypeEnum.REGULAR_SINGLE.getCode())
                .notIn(PosCash::getBillState, Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(),
                        PosCashBillStateEnum.PART_REFUND.getCode(), PosCashBillStateEnum.REFUNDED.getCode()))
                .eq(PosCash::getOrgId, ContextUtil.getCurrentCompanyId())
                .eq(PosCash::getType, PosCashTypeEnum.START_TABLE.getCode())
                .eq(PosCash::getTableId, tableId));
        List<PosCashResultVO> resultVOList = BeanPlusUtil.toBeanList(posCashList, PosCashResultVO.class);
        echoService.action(resultVOList);
        return super.success(resultVOList);
    }

}


