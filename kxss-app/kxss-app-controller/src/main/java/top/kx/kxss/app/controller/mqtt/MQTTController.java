package top.kx.kxss.app.controller.mqtt;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.app.mqtt.handler.MQTTGateway;
import top.kx.kxss.app.vo.mqtt.MQTTMessage;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mqtt")
public class MQTTController {

    @Autowired
    private MQTTGateway mqttGateWay;

    @PostMapping("/send")
    public R<String> send(@RequestBody MQTTMessage message) {
        // 发送消息到指定主题
        if (StrUtil.isNotBlank(message.getContent())) {
            Map parse = JSON.parseObject(message.getContent(), Map.class);
            if (!parse.containsKey("time")) {
                parse.put("time", DateUtil.format(LocalDateTime.now(), "yyyy-MM-dd HH:mm:ss"));
            }
            message.setContent(JSON.toJSONString(parse));
        }
        mqttGateWay.sendToMqtt(message.getTopic(), message.getQos(), message.getContent());
        return R.success("send topic: " + message.getTopic() + ", message : " + message.getContent());
    }
}
