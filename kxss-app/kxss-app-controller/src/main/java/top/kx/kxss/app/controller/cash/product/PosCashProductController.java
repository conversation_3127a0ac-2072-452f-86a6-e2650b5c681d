package top.kx.kxss.app.controller.cash.product;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.service.cash.product.PosCashProductService;
import top.kx.kxss.app.vo.query.cash.product.PosCashProductPageQuery;
import top.kx.kxss.app.vo.result.cash.product.PosCashProductResultVO;
import top.kx.kxss.app.vo.save.cash.product.PosCashProductSaveVO;
import top.kx.kxss.app.vo.update.cash.product.PosCashProductUpdateVO;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * 结算单商品子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-19 14:40:33
 * @create [2023-04-19 14:40:33] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/posCashProduct")
@Api(value = "PosCashProduct", tags = "结算单商品子表")
public class PosCashProductController extends SuperController<PosCashProductService, Long, PosCashProduct, PosCashProductSaveVO,
    PosCashProductUpdateVO, PosCashProductPageQuery, PosCashProductResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @ApiOperation(value = "检查商品是否在使用", notes = "检查商品是否在使用")
    @PostMapping("/checkProductIsUse")
    public R<Boolean> checkProductIsUse(@RequestBody List<Long> longs){
        return success(superService.checkProductIsUse(longs));
    }
}


