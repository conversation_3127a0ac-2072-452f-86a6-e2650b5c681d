package top.kx.kxss.app.controller.member;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.jackson.JsonUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.query.BalanceChangesQuery;
import top.kx.kxss.app.query.GrantCouponQuery;
import top.kx.kxss.app.service.cash.PosCashServiceService;
import top.kx.kxss.app.service.cash.payment.PosCashPaymentService;
import top.kx.kxss.app.vo.member.MemberIdQuery;
import top.kx.kxss.app.vo.member.MemberPhoneQuery;
import top.kx.kxss.app.vo.query.cash.payment.PosCashPaymentPageQuery;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO;
import top.kx.kxss.app.vo.update.score.MemberScoreUpdateVO;
import top.kx.kxss.base.entity.biz.BaseBizLog;
import top.kx.kxss.base.entity.member.MemberCabinet;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.base.entity.member.card.MemberCard;
import top.kx.kxss.base.entity.member.coupon.MemberCoupon;
import top.kx.kxss.base.entity.member.grade.MemberGrade;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.service.member.MemberCabinetService;
import top.kx.kxss.base.service.member.MemberInfoChangeRecordService;
import top.kx.kxss.base.service.member.MemberInfoService;
import top.kx.kxss.base.service.member.card.MemberCardService;
import top.kx.kxss.base.service.member.coupon.MemberCouponService;
import top.kx.kxss.base.service.member.coupon.MemberGradeCouponService;
import top.kx.kxss.base.service.member.grade.MemberGradeCardService;
import top.kx.kxss.base.service.member.grade.MemberGradeService;
import top.kx.kxss.base.vo.query.member.MemberInfoMobileQuery;
import top.kx.kxss.base.vo.query.member.MemberInfoPageQuery;
import top.kx.kxss.base.vo.query.user.UserPasswordResetVO;
import top.kx.kxss.base.vo.query.user.UserPasswordUpdateVO;
import top.kx.kxss.base.vo.result.member.MemberConsumeResultVO;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.product.BaseProductResultVO;
import top.kx.kxss.base.vo.save.member.MemberCabinetBindVO;
import top.kx.kxss.base.vo.save.member.MemberInfoChangeRecordSaveVO;
import top.kx.kxss.base.vo.save.member.MemberInfoSaveVO;
import top.kx.kxss.base.vo.update.member.MemberInfoExpireDateUpdateVO;
import top.kx.kxss.base.vo.update.member.MemberInfoGradeUpdateVO;
import top.kx.kxss.base.vo.update.member.MemberInfoUpdateVO;
import top.kx.kxss.common.api.HelperApi;
import top.kx.kxss.common.constant.BizConstant;
import top.kx.kxss.common.constant.ParameterKey;
import top.kx.kxss.datascope.DataScopeBizHelper;
import top.kx.kxss.datascope.model.DataScopeBizEnum;
import top.kx.kxss.file.entity.File;
import top.kx.kxss.file.service.FileService;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.model.enumeration.base.*;
import top.kx.kxss.system.entity.tenant.DefTenantMember;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.service.tenant.DefTenantMemberService;
import top.kx.kxss.system.service.tenant.DefUserService;
import top.kx.kxss.system.vo.update.tenant.CheckPasswordVO;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static top.kx.basic.utils.ValidatorUtil.REGEX_MOBILE;

/**
 * <p>
 * 前端控制器
 * 会员信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-13 20:16:54
 * @create [2023-04-13 20:16:54] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/member")
@Api(value = "Member", tags = "会员相关API")
public class MemberController extends SuperController<MemberInfoService, Long, MemberInfo, MemberInfoSaveVO,
        MemberInfoUpdateVO, MemberInfoPageQuery, MemberInfoResultVO> {
    private final EchoService echoService;

    @Autowired
    private HelperApi helperApi;

    @Autowired
    private BaseBizLogService baseBizLogManager;
    @Autowired
    private MemberCabinetService memberCabinetService;
    @Autowired
    private PosCashServiceService posCashServiceService;
    @Autowired
    private DefTenantMemberService defTenantMemberService;
    @Autowired
    private MemberGradeService memberGradeService;
    @Autowired
    private MemberCardService memberCardService;
    @Autowired
    private MemberCouponService memberCouponService;
    @Autowired
    private MemberInfoChangeRecordService memberInfoChangeRecordService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    private final FileService fileService;
    private final PosCashPaymentService posCashPaymentService;
    private final DefUserService defUserService;
    private final MemberGradeCouponService memberGradeCouponService;
    private final MemberGradeCardService memberGradeCardService;

    @ApiOperation(value = "手机号搜索会员", notes = "手机号搜索会员")
    @PostMapping("/getMemberByPhone")
    public R<MemberInfoResultVO> getMemberByPhone(@RequestBody @Validated MemberPhoneQuery query) {
        return R.success(superService.getMemberByPhone(query.getPhone(), null));
    }

    @ApiOperation(value = "消费信息", notes = "消费信息")
    @PostMapping("/consumeInfo")
    public R<MemberConsumeResultVO> consumeInfo(@RequestBody @Validated MemberIdQuery query) {
        return R.success(superService.consumeInfo(query));
    }

    // 修改积分
    @ApiOperation(value = "修改积分", notes = "修改积分")
    @PostMapping("/updateScore")
    public R<Boolean> updateScore(@RequestBody @Validated MemberScoreUpdateVO updateVO) {
        return R.success(superService.updateScore(updateVO));
    }

    @Override
    public R<MemberInfo> handlerSave(MemberInfoSaveVO model) {
        superService.saveBeforeProcess(model);
        return super.handlerSave(model);
    }

    @Override
    public R<MemberInfo> save(MemberInfoSaveVO saveVO) {
        //个性参数 是否手机号唯一
        Map<String, String> data = helperApi.findParams(Collections.singletonList(ParameterKey.IS_MEMBER_MOBILE)).getData();
        log.info("个性参数：{}", JsonUtil.toJson(data));
        int isMemberMobile = 0;
        if (CollUtil.isNotEmpty(data)) {
            String distributionTime = data.get(ParameterKey.IS_MEMBER_MOBILE);
            if (StrUtil.isNotBlank(distributionTime)) {
                isMemberMobile = Integer.parseInt(distributionTime);
            }
        }
        // 校验会员等级状态
        if (ObjectUtil.isNotNull(saveVO.getGradeId())) {
            MemberGrade memberGrade = memberGradeService.getById(saveVO.getGradeId());
            ArgumentAssert.notNull(memberGrade, "会员等级不存在");
            ArgumentAssert.isTrue(StringUtils.equals(memberGrade.getStatus(), "1"), "会员等级已禁用, 无法新增该等级会员");
        }

        if (isMemberMobile == 1 && StrUtil.isNotBlank(saveVO.getMobile())) {
            MemberInfoResultVO memberByPhone = superService.getMemberByPhone(saveVO.getMobile(), null);
            ArgumentAssert.isNull(memberByPhone, "手机号已存在");
        }
        if (ObjectUtil.isNull(saveVO.getValidityDate())) {
            saveVO.setValidityDate(memberGradeService.validityDate(saveVO.getGradeId()));
        }
        R<MemberInfo> save = super.save(saveVO);
        MemberInfo memberInfo = save.getData();
        superService.saveAfterProcess(memberInfo, saveVO.getCabinetList());
        return save;
    }

    @ApiOperation(value = "新增", notes = "新增")
    @PostMapping("/add")
//    @SubscriptionFeatureCheck(featureCode = {FeatureCodeEnum.ADD_MEMBER})
    public R<MemberInfoResultVO> add(@RequestBody @Validated MemberInfoSaveVO saveVO) {
        R<MemberInfo> save = save(saveVO);
        MemberInfo memberInfo = save.getData();
        MemberInfoResultVO memberInfoResultVO = BeanUtil.copyProperties(memberInfo, MemberInfoResultVO.class);
        if (ObjectUtil.isNotNull(memberInfoResultVO.getAvatarId())) {
            memberInfoResultVO.setAvatarFile(fileService.getById(memberInfoResultVO.getAvatarId()));
        }
        if (ObjectUtil.isNotNull(memberInfoResultVO)) {
            memberInfoResultVO.setRechargeAmount(memberInfoResultVO.getRechargeAmount() == null ? BigDecimal.ZERO : memberInfoResultVO.getRechargeAmount());
            memberInfoResultVO.setGiftAmount(memberInfoResultVO.getGiftAmount() == null ? BigDecimal.ZERO : memberInfoResultVO.getGiftAmount());
            memberInfoResultVO.setAccountBalance(memberInfoResultVO.getRechargeAmount().add(memberInfoResultVO.getGiftAmount()));
        }
        List<MemberCabinet> cabinetList = memberCabinetService.list(Wraps.<MemberCabinet>lbQ().eq(MemberCabinet::getMemberId, memberInfoResultVO.getId()));
        if (CollUtil.isNotEmpty(cabinetList)) {
            memberInfoResultVO.setCabinetList(cabinetList.stream().map(MemberCabinet::getCabinetId).collect(Collectors.toList()));
        }
        echoService.action(memberInfoResultVO);
        //更新平台会员
        defTenantMemberService.saveOrUpdate(memberInfo, ContextUtil.getTenantId(),
                ContextUtil.getCurrentCompanyId());
        return R.success(memberInfoResultVO);
    }

    @ApiOperation(value = "更新", notes = "更新")
    @PutMapping("/modify")
    public R<MemberInfoResultVO> modify(@RequestBody @Validated MemberInfoUpdateVO memberInfoUpdateVO) {
        // 校验会员等级
        MemberInfo info = superService.getById(memberInfoUpdateVO.getId());
        if (!ObjectUtil.equal(memberInfoUpdateVO.getGradeId(), info.getGradeId())) {
            MemberGrade memberGrade = memberGradeService.getById(memberInfoUpdateVO.getGradeId());
            ArgumentAssert.notNull(memberGrade, "会员等级不存在");
            ArgumentAssert.isTrue(StringUtils.equals(memberGrade.getStatus(), "1"), "会员等级已禁用, 无法调整为该等级会员");
        }
        R<MemberInfo> update = update(memberInfoUpdateVO);
        MemberInfo byId = update.getData();
        MemberInfoResultVO memberInfo = BeanUtil.copyProperties(byId, MemberInfoResultVO.class);
        if (ObjectUtil.isNotNull(memberInfo.getAvatarId())) {
            memberInfo.setAvatarFile(fileService.getById(memberInfo.getAvatarId()));
        }
        if (ObjectUtil.isNotNull(memberInfo)) {
            memberInfo.setRechargeAmount(memberInfo.getRechargeAmount() == null ? BigDecimal.ZERO : memberInfo.getRechargeAmount());
            memberInfo.setGiftAmount(memberInfo.getGiftAmount() == null ? BigDecimal.ZERO : memberInfo.getGiftAmount());
            memberInfo.setAccountBalance(memberInfo.getRechargeAmount().add(memberInfo.getGiftAmount()));
        }
        List<MemberCabinet> cabinetList = memberCabinetService.list(Wraps.<MemberCabinet>lbQ().eq(MemberCabinet::getMemberId, memberInfo.getId()));
        if (CollUtil.isNotEmpty(cabinetList)) {
            memberInfo.setCabinetList(cabinetList.stream().map(MemberCabinet::getCabinetId).collect(Collectors.toList()));
        }
        echoService.action(memberInfo);
        //更新平台会员
        defTenantMemberService.saveOrUpdate(byId, ContextUtil.getTenantId(),
                ContextUtil.getCurrentCompanyId());
        return R.success(memberInfo);
    }

    @ApiOperation(value = "更新会员有效期", notes = "更新会员有效期")
    @PutMapping("/validityDate")
    public R<Boolean> modifyGrade(@RequestBody @Validated MemberInfoExpireDateUpdateVO updateVO) {
        MemberInfo byId = superService.getById(updateVO.getId());
        ArgumentAssert.notNull(byId, "会员不存在");
        MemberInfo build = MemberInfo.builder()
                .validityDate(updateVO.getValidityDate()).build();
        build.setId(byId.getId());
        memberInfoChangeRecordService.save(MemberInfoChangeRecordSaveVO.builder()
                .memberId(byId.getId()).createdOrgId(ContextUtil.getCurrentCompanyId())
                .deleteFlag(0).desc("修改会员有效期【" + (byId.getValidityDate() == null ? "空" : byId.getValidityDate().toString()) + "】".concat("更改为")
                        .concat("【" + (updateVO.getValidityDate() == null ? "空" : updateVO.getValidityDate().toString()) + "】"))
                .oldGradeId(byId.getGradeId())
                .gradeId(byId.getGradeId()).type("20").employeeId(ContextUtil.getEmployeeId())
                .build());
        return R.success(superService.updateById(build));
    }

    @ApiOperation(value = "更新会员等级", notes = "更新会员等级")
    @PutMapping("/modifyGrade")
    public R<MemberInfoResultVO> modifyGrade(@RequestBody @Validated MemberInfoGradeUpdateVO updateVO) {
        MemberInfo byId = superService.getById(updateVO.getId());
        ArgumentAssert.isFalse(ObjectUtil.equal(byId.getGradeId(), updateVO.getGradeId()),
                "修改的会员等级与原等级一致,请勿重复操作");
        Long gradeId = byId.getGradeId();
        byId.setGradeId(updateVO.getGradeId());
        //新增操作日志
        MemberGrade gradeById = memberGradeService.getGradeById(byId.getGradeId());
        MemberGrade gradeById1 = memberGradeService.getGradeById(gradeId);

        ArgumentAssert.notNull(gradeById, "会员等级不存在");
        ArgumentAssert.isTrue(StringUtils.equals(gradeById.getStatus(), "1"), "会员等级已禁用, 无法调整为该等级会员");

        superService.updateById(byId);
        memberGradeCouponService.grantCoupon(byId.getId(), updateVO.getGradeId());
        //发放等级权益
        memberGradeCardService.grantCard(byId.getId(), updateVO.getGradeId());
        baseBizLogManager.createBizLog(BaseBizLog.builder()
                .tenantId(ContextUtil.getTenantId())
                .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                .description("修改会员等级【" + byId.getName() + "/" + PhoneUtil.subAfter(byId.getMobile()) + "】")
                .bizModule(BizLogModuleEnum.UPDATE_MEMBER_GRADE.getCode())
                .type(BizLogTypeEnum.UPDATED.getCode())
                .sourceId(byId.getId()).remarks(gradeById1.getName() + ">>>" + gradeById.getName())
                .sn(ContextUtil.getSn())
                .businessAuthId(updateVO.getBusinessAuthId())
                .build());

        memberInfoChangeRecordService.save(MemberInfoChangeRecordSaveVO.builder()
                .memberId(byId.getId()).createdOrgId(ContextUtil.getCurrentCompanyId())
                .deleteFlag(0).desc("【" + gradeById1.getName() + "】".concat("更改为")
                        .concat("【" + gradeById.getName() + "】")).oldGradeId(byId.getGradeId())
                .gradeId(gradeId).type("10").employeeId(ContextUtil.getEmployeeId())
                .build());

        MemberInfoResultVO memberInfo = BeanUtil.copyProperties(byId, MemberInfoResultVO.class);
        if (ObjectUtil.isNotNull(memberInfo.getAvatarId())) {
            memberInfo.setAvatarFile(fileService.getById(memberInfo.getAvatarId()));
        }
        if (ObjectUtil.isNotNull(memberInfo)) {
            memberInfo.setRechargeAmount(memberInfo.getRechargeAmount() == null ? BigDecimal.ZERO : memberInfo.getRechargeAmount());
            memberInfo.setGiftAmount(memberInfo.getGiftAmount() == null ? BigDecimal.ZERO : memberInfo.getGiftAmount());
            memberInfo.setAccountBalance(memberInfo.getRechargeAmount().add(memberInfo.getGiftAmount()));
        }
        List<MemberCabinet> cabinetList = memberCabinetService.list(Wraps.<MemberCabinet>lbQ().eq(MemberCabinet::getMemberId, memberInfo.getId()));
        if (CollUtil.isNotEmpty(cabinetList)) {
            memberInfo.setCabinetList(cabinetList.stream().map(MemberCabinet::getCabinetId).collect(Collectors.toList()));
        }
        echoService.action(memberInfo);
        //更新平台会员
        defTenantMemberService.saveOrUpdate(byId, ContextUtil.getTenantId(),
                ContextUtil.getCurrentCompanyId());
        return R.success(memberInfo);
    }

    @Override
    public R<MemberInfoResultVO> get(Long aLong) {
        return R.success(superService.getMemberInfo(aLong));
    }

    @Override
    public R<MemberInfoResultVO> getDetail(Long aLong) {
        return R.success(superService.getMemberInfo(aLong));
    }

    @Override
    public R<IPage<MemberInfoResultVO>> page(PageParams<MemberInfoPageQuery> params) {
        params.setSort("");
        params.setOrder("");
        return super.page(params);
    }

    @ApiOperation(value = "mobileList", notes = "获取手机号列表")
    @PostMapping("/mobileList")
    public R<List<String>> modify(@RequestBody MemberInfoMobileQuery query) {
        return R.success(superService.mobileList(query));
    }


    @Override
    public R<MemberInfo> handlerUpdate(MemberInfoUpdateVO memberInfoUpdateVO) {
        Long gradeId = memberInfoUpdateVO.getGradeId();
        MemberInfo byId = superService.getById(memberInfoUpdateVO.getId());
        //个性参数 是否手机号唯一
        Map<String, String> data = helperApi.findParams(Collections.singletonList(ParameterKey.IS_MEMBER_MOBILE)).getData();
        log.info("个性参数：{}", JsonUtil.toJson(data));
        int isMemberMobile = 0;
        memberInfoUpdateVO.setType(MemberTypeEnum.VIP.getCode());
        if (ObjectUtil.equal(gradeId, BizConstant.DEFAULT_GRADE)) {
            memberInfoUpdateVO.setType(MemberTypeEnum.INDIVIDUAL.getCode());
        }
        if (CollUtil.isNotEmpty(data)) {
            String distributionTime = data.get(ParameterKey.IS_MEMBER_MOBILE);
            if (StrUtil.isNotBlank(distributionTime)) {
                isMemberMobile = Integer.parseInt(distributionTime);
            }
        }
        if (isMemberMobile == 1 && ObjectUtil.isNotNull(byId)
                && StrUtil.isNotBlank(memberInfoUpdateVO.getMobile())) {
            MemberInfoResultVO memberByPhone = superService.getMemberByPhone(memberInfoUpdateVO.getMobile(), byId.getId());
            ArgumentAssert.isNull(memberByPhone, "手机号已存在");
        }
        memberInfoUpdateVO.setSalt(byId.getSalt());
        String password = memberInfoUpdateVO.getPassword();
        if (StrUtil.isNotBlank(memberInfoUpdateVO.getPassword())
                && memberInfoUpdateVO.getPassword().equals("hyszhysz")) {
            memberInfoUpdateVO.setPassword(byId.getPassword());
        }
        if (StrUtil.isNotBlank(memberInfoUpdateVO.getPassword())
                && !password.equals("hyszhysz")) {
            Pattern pattern = Pattern.compile("[0-9]*");
            Matcher isNum = pattern.matcher(memberInfoUpdateVO.getPassword());
            if (!isNum.matches()) {
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "请输入纯数字密码");
            }
            ArgumentAssert.notBlank(memberInfoUpdateVO.getConfirmPassword(), "请输入确认密码");
            ArgumentAssert.equals(memberInfoUpdateVO.getPassword(), memberInfoUpdateVO.getConfirmPassword(), "两次密码输入不一致");
            if (StrUtil.isNotBlank(memberInfoUpdateVO.getPassword())) {
                String s = byId.getSalt();
                if (StrUtil.isBlank(s)) {
                    s = RandomUtil.randomString(20);
                }
                String defPassword = SecureUtil.sha256(memberInfoUpdateVO.getPassword() + s);
                memberInfoUpdateVO.setPassword(defPassword);
                memberInfoUpdateVO.setSalt(s);
            }
        }
        if (StrUtil.isNotBlank(memberInfoUpdateVO.getMobile())
                && memberInfoUpdateVO.getMobile().contains("****")) {
            memberInfoUpdateVO.setMobile(byId.getMobile());
        }
        if (StrUtil.isNotBlank(memberInfoUpdateVO.getMobile())) {
            Pattern pattern = Pattern.compile(REGEX_MOBILE);
            Matcher isNum = pattern.matcher(memberInfoUpdateVO.getMobile());
            if (!isNum.matches()) {
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1), "请输入11位手机号码");
            }
        }

        //绑定杆柜
        memberCabinetService.checkBind(byId.getId());
        memberCabinetService.remove(Wraps.<MemberCabinet>lbU().eq(MemberCabinet::getMemberId, byId.getId()));
        if (CollUtil.isNotEmpty(memberInfoUpdateVO.getCabinetList())) {
            memberCabinetService.bind(MemberCabinetBindVO.builder()
                    .memberId(byId.getId()).cabinetList(memberInfoUpdateVO.getCabinetList())
                    .build());
        }
        //发放等级权益
        if (ObjectUtil.isNotNull(byId)) {
            if (StrUtil.isNotBlank(memberInfoUpdateVO.getMobile())
                    && !byId.getMobile().equals(memberInfoUpdateVO.getMobile())) {
                baseBizLogManager.createBizLog(BaseBizLog.builder()
                        .tenantId(ContextUtil.getTenantId())
                        .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                        .description("修改会员手机号【" + byId.getName() + "/" + PhoneUtil.hideBetween(memberInfoUpdateVO.getMobile()) + "】")
                        .bizModule(BizLogModuleEnum.UPDATE_MEMBER_MOBILE.getCode())
                        .type(BizLogTypeEnum.UPDATED.getCode())
                        .sourceId(byId.getId()).remarks(byId.getMobile() + "更改为" + memberInfoUpdateVO.getMobile())
                        .sn(ContextUtil.getSn())
                        .build());
            }
            if (!ObjectUtil.equal(gradeId, byId.getGradeId())) {
                memberGradeCouponService.grantCoupon(byId.getId(), memberInfoUpdateVO.getGradeId());
                //发放等级权益
                memberGradeCardService.grantCard(byId.getId(), memberInfoUpdateVO.getGradeId());
                //新增操作日志
                MemberGrade gradeById = memberGradeService.getGradeById(gradeId);
                MemberGrade gradeById1 = memberGradeService.getGradeById(byId.getGradeId());
                baseBizLogManager.createBizLog(BaseBizLog.builder()
                        .tenantId(ContextUtil.getTenantId())
                        .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                        .description("修改会员等级【" + byId.getName() + "/" + PhoneUtil.subAfter(byId.getMobile()) + "】")
                        .bizModule(BizLogModuleEnum.UPDATE_MEMBER_GRADE.getCode())
                        .type(BizLogTypeEnum.UPDATED.getCode())
                        .sourceId(byId.getId()).remarks(gradeById1.getName() + ">>>" + gradeById.getName())
                        .sn(ContextUtil.getSn())
                        .build());


                memberInfoChangeRecordService.save(MemberInfoChangeRecordSaveVO.builder()
                        .memberId(byId.getId()).createdOrgId(ContextUtil.getCurrentCompanyId())
                        .deleteFlag(0).desc("【" + gradeById1.getName() + "】".concat("更改为")
                                .concat("【" + gradeById.getName() + "】")).oldGradeId(byId.getGradeId())
                        .gradeId(gradeId).type("10").employeeId(ContextUtil.getEmployeeId())
                        .build());

            }
            Boolean b = memberInfoChangeRecordService.checkValidityDate(memberInfoUpdateVO.getValidityDate(), byId.getValidityDate());
            if (b) {
                memberInfoChangeRecordService.save(MemberInfoChangeRecordSaveVO.builder()
                        .memberId(byId.getId()).createdOrgId(ContextUtil.getCurrentCompanyId())
                        .deleteFlag(0).desc("修改会员有效期【" + (byId.getValidityDate() == null ? "空" : byId.getValidityDate().toString()) + "】".concat("更改为")
                                .concat("【" + (memberInfoUpdateVO.getValidityDate() == null ? "空" : memberInfoUpdateVO.getValidityDate().toString()) + "】")).oldGradeId(byId.getGradeId())
                        .gradeId(gradeId).type("20").employeeId(ContextUtil.getEmployeeId())
                        .build());
            }
            if (!ObjectUtil.equal(byId.getPassword(), memberInfoUpdateVO.getPassword())) {
                baseBizLogManager.createBizLog(BaseBizLog.builder()
                        .tenantId(ContextUtil.getTenantId())
                        .orgId(ContextUtil.getCurrentCompanyId()).employeeId(ContextUtil.getEmployeeId())
                        .description((StrUtil.isNotBlank(memberInfoUpdateVO.getPassword()) ?
                                "修改" : "置空") + "会员密码【" + byId.getName() + "/" + PhoneUtil.subAfter(byId.getMobile()) + "】")
                        .bizModule(BizLogModuleEnum.UPDATE_MEMBER_PASSWORD.getCode())
                        .type(BizLogTypeEnum.UPDATED.getCode())
                        .sourceId(byId.getId()).remarks("")
                        .sn(ContextUtil.getSn())
                        .build());
            }
            //更新平台会员
            defTenantMemberService.saveOrUpdate(byId, ContextUtil.getTenantId(),
                    ContextUtil.getCurrentCompanyId());
        }
        return super.handlerUpdate(memberInfoUpdateVO);
    }

    @ApiOperation(value = "检测会员编码是否可用", notes = "检测会员编码是否可用")
    @GetMapping("/checkCode")
    public R<Boolean> checkCode(@RequestParam String code, @RequestParam Long id) {
        return success(superService.checkMemberCode(code, id));
    }

    @ApiOperation(value = "查询会员详情", notes = "查询会员详情")
    @GetMapping("/getMemberInfo")
    public R<MemberInfoResultVO> getMemberInfo(@RequestParam("id") Long id) {
        MemberInfoResultVO memberInfo = superService.getMemberInfo(id);

        if (ObjectUtil.isNotNull(memberInfo)) {
            if (ObjectUtil.isNotNull(memberInfo.getAvatarId())) {
                memberInfo.setAvatarFile(fileService.getById(memberInfo.getAvatarId()));
            }
            memberInfo.setRechargeAmount(memberInfo.getRechargeAmount() == null ? BigDecimal.ZERO : memberInfo.getRechargeAmount());
            memberInfo.setGiftAmount(memberInfo.getGiftAmount() == null ? BigDecimal.ZERO : memberInfo.getGiftAmount());
            memberInfo.setAccountBalance(memberInfo.getRechargeAmount().add(memberInfo.getGiftAmount()));
            List<MemberCabinet> cabinetList = memberCabinetService.list(Wraps.<MemberCabinet>lbQ().eq(MemberCabinet::getMemberId, memberInfo.getId()));
            if (CollUtil.isNotEmpty(cabinetList)) {
                memberInfo.setCabinetList(cabinetList.stream().map(MemberCabinet::getCabinetId).collect(Collectors.toList()));
            }
            echoService.action(memberInfo);
        }
        return success(memberInfo);
    }


    @ApiOperation(value = "会员信息-导出", notes = "会员信息-导出")
    @RequestMapping(value = "/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void memberExport(@RequestBody @Validated MemberInfoPageQuery query, HttpServletResponse response) {
        QueryWrap<MemberInfo> wrap = new QueryWrap<>();
        handlerQueryWrap(query, wrap);
        List<MemberInfo> list = superService.list(wrap);
        List<MemberInfoResultVO> resultVOList = BeanPlusUtil.toBeanList(list, MemberInfoResultVO.class);
        echoService.action(resultVOList);
        for (MemberInfoResultVO memberInfoResultVO : resultVOList) {
            memberInfoResultVO.setSexName(ObjectUtil.isNotNull(memberInfoResultVO.getEchoMap().get("sex")) ? memberInfoResultVO.getEchoMap().get("sex").toString() : "-");
            memberInfoResultVO.setGradeName(ObjectUtil.isNotNull(memberInfoResultVO.getEchoMap().get("gradeId")) ? memberInfoResultVO.getEchoMap().get("gradeId").toString() : "-");
            memberInfoResultVO.setStoredValueAmount(memberInfoResultVO.getTotalRechargeAmount().add(memberInfoResultVO.getTotalGiftAmount()));
//            memberInfoResultVO.setMobile(PhoneUtil.hideBetween(memberInfoResultVO.getMobile()).toString());
            memberInfoResultVO.setAccountBalance(memberInfoResultVO.getRechargeAmount().add(memberInfoResultVO.getGiftAmount()));
        }
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=Member.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, MemberInfoResultVO.class)
                    .sheet("sheet1")
                    .doWrite(resultVOList);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }


    @Override
    public QueryWrap<MemberInfo> handlerWrapper(MemberInfo model, PageParams<MemberInfoPageQuery> params) {
        QueryWrap<MemberInfo> wrap = super.handlerWrapper(model, params);
        MemberInfoPageQuery queryModel = params.getModel();
        handlerQueryWrap(queryModel, wrap);
        DataScopeBizHelper.startDataScope(DataScopeBizEnum.MEMBER, "member_info");
        return wrap;
    }

    private void handlerQueryWrap(MemberInfoPageQuery queryModel, QueryWrap<MemberInfo> wrap) {
        if (StrUtil.isNotBlank(queryModel.getKeyword())) {
            wrap.lambda().and(StrUtil.isNotBlank(queryModel.getKeyword()), wrapper -> wrapper
                    .like(MemberInfo::getName, queryModel.getKeyword())
                    .or().like(MemberInfo::getMobile, queryModel.getKeyword()));
        }
        // 有效期 0 已过期 1 -今天, 2-明天, 3-近7天, 4-近30天
        String today = LocalDate.now().toString();
        if (StringUtils.equals(queryModel.getValidDateType(), "0")) {
            wrap.lambda().isNotNull(MemberInfo::getValidityDate);
            wrap.lambda().lt(MemberInfo::getValidityDate, today);
        } else if (StringUtils.equals(queryModel.getValidDateType(), "1")) {
            wrap.lambda().isNotNull(MemberInfo::getValidityDate);
            wrap.lambda().eq(MemberInfo::getValidityDate, today);
        } else if (StringUtils.equals(queryModel.getValidDateType(), "2")) {
            String tomorrow = LocalDate.now().plusDays(1).toString();
            wrap.lambda().between(MemberInfo::getValidityDate, today, tomorrow);
        } else if (StringUtils.equals(queryModel.getValidDateType(), "3")) {
            String tomorrow = LocalDate.now().plusDays(6).toString();
            wrap.lambda().between(MemberInfo::getValidityDate, today, tomorrow);
        } else if (StringUtils.equals(queryModel.getValidDateType(), "4")) {
            String tomorrow = LocalDate.now().plusDays(29).toString();
            wrap.lambda().between(MemberInfo::getValidityDate, today, tomorrow);
        }

        // 生日 1 -今天, 2-明天, 3-近7天, 4-近30天
        if (StringUtils.equals(queryModel.getBirthdayType(), "1")) {
            today = DateUtil.format(new Date(), "-MM-dd");
            wrap.lambda().isNotNull(MemberInfo::getBirth);
            wrap.lambda().likeLeft(MemberInfo::getBirth, today);
        }
        if (StringUtils.equals(queryModel.getBirthdayType(), "2")) {
            String sql = "DATE_FORMAT(STR_TO_DATE(birth, '%Y-%m-%d'), '%m-%d') >= DATE_FORMAT(CURDATE(), '%m-%d') " +
                    "  AND " +
                    "  DATE_FORMAT(STR_TO_DATE(birth, '%Y-%m-%d'), '%m-%d') <= DATE_FORMAT(DATE_ADD(CURDATE(), INTERVAL 1 DAY), '%m-%d')";
            wrap.lambda().apply(sql);
        }
        if (StringUtils.equals(queryModel.getBirthdayType(), "3")) {
            String sql = "DATE_FORMAT(STR_TO_DATE(birth, '%Y-%m-%d'), '%m-%d') >= DATE_FORMAT(CURDATE(), '%m-%d') " +
                    "  AND " +
                    "  DATE_FORMAT(STR_TO_DATE(birth, '%Y-%m-%d'), '%m-%d') <= DATE_FORMAT(DATE_ADD(CURDATE(), INTERVAL 7 DAY), '%m-%d')";
            wrap.lambda().apply(sql);
        }
        if (StringUtils.equals(queryModel.getBirthdayType(), "4")) {
            String sql = "DATE_FORMAT(STR_TO_DATE(birth, '%Y-%m-%d'), '%m-%d') >= DATE_FORMAT(CURDATE(), '%m-%d') " +
                    "  AND " +
                    "  DATE_FORMAT(STR_TO_DATE(birth, '%Y-%m-%d'), '%m-%d') <= DATE_FORMAT(DATE_ADD(CURDATE(), INTERVAL 30 DAY), '%m-%d')";
            wrap.lambda().apply(sql);
        }


        wrap.lambda()
                .ge(ObjectUtil.isNotNull(queryModel.getMinConsumeTimes()),
                        MemberInfo::getConsumeTimes, queryModel.getMinConsumeTimes())
                .le(ObjectUtil.isNotNull(queryModel.getMaxConsumeTimes()),
                        MemberInfo::getConsumeTimes, queryModel.getMaxConsumeTimes())
                .ge(ObjectUtil.isNotNull(queryModel.getMinConsumeRechargeAmount()),
                        MemberInfo::getConsumeRechargeAmount, queryModel.getMinConsumeRechargeAmount())
                .le(ObjectUtil.isNotNull(queryModel.getMaxConsumeRechargeAmount()),
                        MemberInfo::getConsumeRechargeAmount, queryModel.getMaxConsumeRechargeAmount())
                .ge(ObjectUtil.isNotNull(queryModel.getMinConsumeTableAmount()),
                        MemberInfo::getConsumeTableAmount, queryModel.getMinConsumeTableAmount())
                .le(ObjectUtil.isNotNull(queryModel.getMaxConsumeTableAmount()),
                        MemberInfo::getConsumeTableAmount, queryModel.getMaxConsumeTableAmount())
                .ge(ObjectUtil.isNotNull(queryModel.getMinConsumeServiceAmount()),
                        MemberInfo::getConsumeServiceAmount, queryModel.getMinConsumeServiceAmount())
                .le(ObjectUtil.isNotNull(queryModel.getMaxConsumeServiceAmount()),
                        MemberInfo::getConsumeServiceAmount, queryModel.getMaxConsumeServiceAmount())
                .ge(ObjectUtil.isNotNull(queryModel.getCreateTimeStart()),
                        MemberInfo::getCreatedTime, queryModel.getCreateTimeStart())
                .le(ObjectUtil.isNotNull(queryModel.getCreateTimeEnd()),
                        MemberInfo::getCreatedTime, queryModel.getCreateTimeEnd())
                .le(StringUtils.equals(queryModel.getLastConsumeTimeType(), "1"),
                        MemberInfo::getLastConsumeTime, LocalDateTime.now().minusDays(7))
                .le(StringUtils.equals(queryModel.getLastConsumeTimeType(), "2"),
                        MemberInfo::getLastConsumeTime, LocalDateTime.now().minusDays(30))
                .le(StringUtils.equals(queryModel.getLastConsumeTimeType(), "3"),
                        MemberInfo::getLastConsumeTime, LocalDateTime.now().minusMonths(6))
                .le(StringUtils.equals(queryModel.getLastConsumeTimeType(), "4"),
                        MemberInfo::getLastConsumeTime, LocalDateTime.now().minusYears(1));
        wrap.lambda().eq(ObjectUtil.isNotNull(queryModel.getGradeId()), MemberInfo::getGradeId, queryModel.getGradeId());
        //账户余额为0的会员包含无可用权益卡和储值卡，无账户余额
        if (queryModel.getIsAccountExpire() != null
                && queryModel.getIsAccountExpire()) {
            List<Long> collect = Lists.newArrayList();
            DataScopeBizHelper.startDataScope(DataScopeBizEnum.MEMBER, "member_coupon");
            List<Long> collect1 = memberCouponService.list(Wraps.<MemberCoupon>lbQ()
                            .in(MemberCoupon::getStatus, Arrays.asList(CouponStatusEnum.LOCK.getCode(),
                                    CouponStatusEnum.RUNNING.getCode()
                            ))
                            .eq(MemberCoupon::getDeleteFlag, 0))
                    .stream().map(MemberCoupon::getMemberId).distinct().collect(Collectors.toList());

            if (CollUtil.isNotEmpty(collect1)) {
                collect.addAll(collect1);
            }
            DataScopeBizHelper.startDataScope(DataScopeBizEnum.MEMBER, "member_card");
            collect1 = memberCardService.list(Wraps.<MemberCard>lbQ()
                            .in(MemberCard::getStatus, Arrays.asList(CashCardStatusEnum.NO_ACTIVATE.getCode(),
                                    CashCardStatusEnum.IN_USE.getCode()
                            ))
                            .eq(MemberCard::getDeleteFlag, 0))
                    .stream().map(MemberCard::getMemberId).distinct().collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect1)) {
                collect.addAll(collect1);
            }
            wrap.lambda().notIn(CollUtil.isNotEmpty(collect), MemberInfo::getId, collect.stream().distinct().collect(Collectors.toList()));
            wrap.apply(" IFNULL(recharge_amount,0) + IFNULL(gift_amount,0) <= 0");
        }

        if (CollUtil.isNotEmpty(queryModel.getTagList())) {
            StringBuffer sb = new StringBuffer();
            sb.append("JSON_CONTAINS(tags,JSON_ARRAY(");
            for (int i = 0; i < queryModel.getTagList().size(); i++) {
                if (i != 0) {
                    sb.append(",");
                }
                sb.append(Long.parseLong(queryModel.getTagList().get(i)));
            }
            sb.append("))");
            wrap.lambda().apply(sb.toString());
        }
        if (StringUtils.equalsIgnoreCase(queryModel.getOrder(), "DESC")) {
            // 排序字段
            if (StringUtils.equalsIgnoreCase(queryModel.getSort(), "lastConsumeTime")) {
                wrap.lambda().orderByDesc(MemberInfo::getLastConsumeTime);
            } else if (StringUtils.equalsIgnoreCase(queryModel.getSort(), "createdTime")) {
                wrap.lambda().orderByDesc(MemberInfo::getCreatedTime);
            } else if (StringUtils.equalsIgnoreCase(queryModel.getSort(), "amount")) {
                wrap.orderByDesc("recharge_amount + gift_amount");
            } else if (StringUtils.equalsIgnoreCase(queryModel.getSort(), "totalAmount")) {
                wrap.orderByDesc("total_recharge_amount + total_gift_amount");
            } else if (StringUtils.equalsIgnoreCase(queryModel.getSort(), "totalRechargeAmount")) {
                wrap.lambda().orderByDesc(MemberInfo::getTotalRechargeAmount);
            } else if (StringUtils.equalsIgnoreCase(queryModel.getSort(), "totalGiftAmount")) {
                wrap.lambda().orderByDesc(MemberInfo::getTotalGiftAmount);
            } else if (StringUtils.equalsIgnoreCase(queryModel.getSort(), "consumeAmount")) {
                wrap.lambda().orderByDesc(MemberInfo::getConsumeAmount);
            } else if (StringUtils.equalsIgnoreCase(queryModel.getSort(), "consumeRechargeAmount")) {
                wrap.lambda().orderByDesc(MemberInfo::getConsumeRechargeAmount);
            } else if (StringUtils.equalsIgnoreCase(queryModel.getSort(), "consumeGiftAmount")) {
                wrap.lambda().orderByDesc(MemberInfo::getConsumeGiftAmount);
            }
        } else if (StringUtils.equalsIgnoreCase(queryModel.getOrder(), "ASC")) {
            // 排序字段
            if (StringUtils.equalsIgnoreCase(queryModel.getSort(), "lastConsumeTime")) {
                wrap.lambda().orderByAsc(MemberInfo::getLastConsumeTime);
            } else if (StringUtils.equalsIgnoreCase(queryModel.getSort(), "createdTime")) {
                wrap.lambda().orderByAsc(MemberInfo::getCreatedTime);
            } else if (StringUtils.equalsIgnoreCase(queryModel.getSort(), "amount")) {
                wrap.orderByAsc("recharge_amount + gift_amount");
            } else if (StringUtils.equalsIgnoreCase(queryModel.getSort(), "totalAmount")) {
                wrap.orderByAsc("total_recharge_amount + total_gift_amount");
            } else if (StringUtils.equalsIgnoreCase(queryModel.getSort(), "totalRechargeAmount")) {
                wrap.lambda().orderByAsc(MemberInfo::getTotalRechargeAmount);
            } else if (StringUtils.equalsIgnoreCase(queryModel.getSort(), "totalGiftAmount")) {
                wrap.lambda().orderByAsc(MemberInfo::getTotalGiftAmount);
            } else if (StringUtils.equalsIgnoreCase(queryModel.getSort(), "consumeAmount")) {
                wrap.lambda().orderByAsc(MemberInfo::getConsumeAmount);
            } else if (StringUtils.equalsIgnoreCase(queryModel.getSort(), "consumeRechargeAmount")) {
                wrap.lambda().orderByAsc(MemberInfo::getConsumeRechargeAmount);
            } else if (StringUtils.equalsIgnoreCase(queryModel.getSort(), "consumeGiftAmount")) {
                wrap.lambda().orderByAsc(MemberInfo::getConsumeGiftAmount);
            }
        } else {
            wrap.lambda().orderByDesc(MemberInfo::getCreatedTime);
        }
        DataScopeBizHelper.startDataScope(DataScopeBizEnum.MEMBER, "member_info");
    }

    @Override
    public void handlerResult(IPage<MemberInfoResultVO> page) {
        List<MemberInfoResultVO> records = page.getRecords();
//        //权益卡数量
//        List<Long> memberIds = records.stream().map(MemberInfoResultVO::getId).collect(Collectors.toList());
//        Map<Long, List<MemberCard>> memberCardMap = CollUtil.isEmpty(memberIds) ? new HashMap<>() :
//                cardService.list(Wraps.<MemberCard>lbQ().in(MemberCard::getMemberId, memberIds)).stream().collect(Collectors.groupingBy(MemberCard::getMemberId));
        //获取图片信息
        List<Long> backgroundImages = records.stream().map(MemberInfoResultVO::getAvatarId).collect(Collectors.toList());
        Map<Long, File> fileMap = CollectionUtil.isNotEmpty(backgroundImages) ? fileService.list(Wraps.<File>lbQ().in(File::getId, backgroundImages))
                .stream().collect(Collectors.toMap(File::getId, k -> k)) : new HashMap<>();

        for (MemberInfoResultVO record : records) {
//            List<MemberCard> memberCards = memberCardMap.get(record.getId());
//            record.setCardNum(CollUtil.isEmpty(memberCards) ? 0 : memberCards.size());


            if (ObjectUtil.isNotNull(record.getAvatarId())) {
                record.setAvatarFile(CollUtil.isNotEmpty(fileMap) ? fileMap.get(record.getAvatarId()) : null);
            }
            record.setRechargeAmount(record.getRechargeAmount() == null ? BigDecimal.ZERO : record.getRechargeAmount());
            record.setGiftAmount(record.getGiftAmount() == null ? BigDecimal.ZERO : record.getGiftAmount());
            record.setAccountBalance(record.getRechargeAmount().add(record.getGiftAmount()).setScale(2, RoundingMode.HALF_UP));
        }
        super.handlerResult(page);
    }

    @Override
    public IPage<MemberInfo> query(PageParams<MemberInfoPageQuery> params) {
        DataScopeBizHelper.startDataScope(DataScopeBizEnum.MEMBER, "member_info");
        return super.query(params);
    }

    @ApiOperation(value = "分页查询订单交易记录", notes = "分页查询订单交易记录")
    @PostMapping("/paymentPage")
    @WebLog(value = "'分页查询订单交易记录:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<PosCashPaymentResultVO>> paymentPage(@RequestBody PageParams<PosCashPaymentPageQuery> params) {
        PosCashPaymentPageQuery pageQuery = params.getModel();
        pageQuery.setStatus(PosCashPaymentStatusEnum.PAY_SUCCESS.getCode());
        LbQueryWrap<PosCashPayment> wrap = Wraps.<PosCashPayment>lbQ();
        wrap.eq(PosCashPayment::getStatus, PosCashPaymentStatusEnum.PAY_SUCCESS.getCode());
        if (ObjectUtil.isNotNull(pageQuery.getMemberId())) {
            wrap.inSql(PosCashPayment::getCashId,
                    "select id from pos_cash where delete_flag = 0 and member_id = " + pageQuery.getMemberId());
        }
        IPage<PosCashPayment> iPage = posCashPaymentService.page(params.buildPage(BaseProductResultVO.class), wrap);
        IPage<PosCashPaymentResultVO> resultVOIPage = BeanPlusUtil.toBeanPage(iPage, PosCashPaymentResultVO.class);
        List<PosCashPaymentResultVO> records = resultVOIPage.getRecords();
        List<Long> updateByIds = records.stream().map(PosCashPaymentResultVO::getUpdatedBy).collect(Collectors.toList());
        //获取操作人信息信息
        Map<Long, DefUser> updateByMap = CollUtil.isNotEmpty(updateByIds) ? defUserService.list(Wraps.<DefUser>lbQ().in(DefUser::getId, updateByIds))
                .stream().collect(Collectors.toMap(DefUser::getId, k -> k)) : new HashMap<>();
        for (PosCashPaymentResultVO record : resultVOIPage.getRecords()) {
            DefUser user = updateByMap.get(record.getUpdatedBy());
            record.getEchoMap().put("updatedBy", user != null ? user.getNickName() : "");
        }
        return R.success(resultVOIPage);
    }

    /**
     * 修改密码
     *
     * @param data 修改实体
     * @return 是否成功
     */
    @ApiOperation(value = "修改密码", notes = "修改密码")
    @PutMapping("/password")
    @WebLog("'修改密码:' + #data.id")
    public R<Boolean> updatePassword(@RequestBody @Validated UserPasswordUpdateVO data) {
        return R.success(superService.updatePassword(data));
    }


    /**
     * 重置密码
     *
     * @param data 修改实体
     * @return 是否成功
     */
    @ApiOperation(value = "重置密码", notes = "重置密码")
    @PutMapping("/resetPassword")
    @WebLog("'修改密码:' + #data.id")
    public R<Boolean> resetPassword(@RequestBody @Validated UserPasswordResetVO data) {
        return success(superService.resetPassword(data));
    }

    /**
     * 校验密码
     *
     * @param data 修改实体
     * @return 是否成功
     */
    @ApiOperation(value = "校验密码", notes = "校验密码")
    @PutMapping("/checkPassword")
    @WebLog("'校验密码:' + #data.id")
    public R<Boolean> checkPassword(@RequestBody @Validated CheckPasswordVO data) {
        return success(superService.checkPassword(data));
    }

    /**
     * 余额变动
     */
    @ApiOperation(value = "余额变动", notes = "余额变动")
    @PostMapping("/balanceChanges")
    @WebLog("'余额变动:' + #query.memberId")
    public R<Boolean> balanceChanges(@RequestBody @Validated BalanceChangesQuery query) {
        return success(superService.balanceChanges(query));
    }

    @Override
    public R<Boolean> handlerDelete(List<Long> longs) {
        //判断会员是否在使用中
        Boolean b = posCashServiceService.checkMemberIsUse(longs);
        ArgumentAssert.isFalse(b, "当前会员正在打球中...");
        if (CollUtil.isNotEmpty(longs)) {
            defTenantMemberService.update(Wraps.<DefTenantMember>lbU()
                    .set(DefTenantMember::getDeleteFlag, 1)
                    .in(DefTenantMember::getMemberId, longs)
                    .eq(DefTenantMember::getDeleteFlag, 0)
                    .eq(DefTenantMember::getTenantId, ContextUtil.getTenantId())
                    .eq(DefTenantMember::getOrgId, ContextUtil.getCurrentCompanyId())
            );
        }
        return super.handlerDelete(longs);
    }

    @ApiOperation(value = "手动发放优惠劵", notes = "手动发放优惠劵")
    @PostMapping("/grantCoupon")
    public R<Boolean> grantCoupon(@RequestBody @Validated GrantCouponQuery query) {
        query.setIsHandGrand(true);
        return R.success(memberGradeCouponService.grantCoupon(query));
    }

    @ApiOperation(value = "根据手机号查询所有会员", notes = "根据手机号查询所有会员")
    @GetMapping("/memberListByMobile")
    public R<List<MemberInfoResultVO>> memberListByMobile(@RequestParam String mobile) {
        return R.success(superService.memberListByMobile(mobile));
    }
}


