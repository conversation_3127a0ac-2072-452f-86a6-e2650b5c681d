package top.kx.kxss.app.controller.cash.payresult;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.app.service.cash.payresult.PosCashPayResultService;
import top.kx.kxss.app.entity.cash.payresult.PosCashPayResult;
import top.kx.kxss.app.vo.save.cash.payresult.PosCashPayResultSaveVO;
import top.kx.kxss.app.vo.update.cash.payresult.PosCashPayResultUpdateVO;
import top.kx.kxss.app.vo.result.cash.payresult.PosCashPayResultResultVO;
import top.kx.kxss.app.vo.query.cash.payresult.PosCashPayResultPageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 结算单支付结果
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-19 17:25:46
 * @create [2023-05-19 17:25:46] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/posCashPayResult")
@Api(value = "PosCashPayResult", tags = "结算单支付结果")
public class PosCashPayResultController extends SuperController<PosCashPayResultService, Long, PosCashPayResult, PosCashPayResultSaveVO,
    PosCashPayResultUpdateVO, PosCashPayResultPageQuery, PosCashPayResultResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


