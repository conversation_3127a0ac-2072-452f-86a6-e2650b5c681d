package top.kx.kxss.oauth.api;


import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.system.entity.tenant.DefUser;

/**
 * 参数API
 *
 * <AUTHOR>
 * @date 2020年04月02日22:53:56
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.oauth-server:kxss-oauth-server}", path = "/anyone")
public interface AnyoneApi {
    /**
     * 检查指定接口是否有访问权限
     *
     * @param path   请求路径
     * @param method 请求方法
     * @return 是否有权限
     */
    @GetMapping("/checkUri")
    R<Boolean> checkUri(@RequestParam("path") String path, @RequestParam("method") String method);

    /**
     * 查询员工是否拥有指定应用
     *
     * @param employeeId    员工id
     * @param applicationId 应用ID
     * @return 是否有权限
     */
    @GetMapping("/checkApplication")
    R<Boolean> checkApplication(@RequestParam("applicationId") Long applicationId, @RequestParam("employeeId") Long employeeId);

    @PostMapping("/getUserByToken")
    R<DefUser> getUserByToken();
}
