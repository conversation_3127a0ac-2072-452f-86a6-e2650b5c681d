package top.kx.kxss.oauth.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.oauth.vo.param.LoginParamVO;
import top.kx.kxss.oauth.vo.result.LoginResultVO;

/**
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.oauth-server:kxss-oauth-server}")
public interface WxAppApi {

    @PostMapping(value = "/anyTenant/wxLogin")
    R<LoginResultVO> wxLogin(@Validated @RequestBody LoginParamVO login);

    @PutMapping("/anyone/switchTenantAndStore")
    R<LoginResultVO> switchTenantAndStore(@RequestParam Long tenantId,
                                          @RequestParam(required = false) Long companyId,
                                          @RequestParam(required = false) Long deptId);

    @PostMapping(value = "/anyUser/logout")
    R<Boolean> logout(String token);

}
