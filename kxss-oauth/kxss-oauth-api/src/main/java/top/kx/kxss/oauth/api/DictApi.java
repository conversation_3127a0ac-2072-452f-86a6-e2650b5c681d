package top.kx.kxss.oauth.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.model.vo.result.Option;
import top.kx.kxss.oauth.vo.param.CodeQueryVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2022/9/29 11:05 PM
 * @create [2022/9/29 11:05 PM ] [tangyh] [初始创建]
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.oauth-server:kxss-oauth-server}", path = "/anyUser")
public interface DictApi {
    @PostMapping(value = "/dict/findDictMapItemListByKey")
    R<Map<String, List<Option>>> findDictMapItemListByKey(@RequestBody List<CodeQueryVO> codeQueryVO);
}
