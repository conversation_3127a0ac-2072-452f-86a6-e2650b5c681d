#FROM openjdk:8-jre
FROM registry.cn-hangzhou.aliyuncs.com/hyszcm/openjdk:8-jdk-alpine
MAINTAINER Jin

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone

ARG PROJECT_DIR

COPY ${PROJECT_DIR}/target/kxss-system-server.jar /app.jar
ENV SPRING_PROFILES_ACTIVE="prod"
ENV JAVA_OPTS="-javaagent:/usr/local/skywalking-agent/skywalking-agent.jar"


ENTRYPOINT ["sh", "-c","java -Xmx512m ${JAVA_OPTS} -Djava.security.egd=file:/dev/./urandom -Ddruid.mysql.usePingMethod=false -jar /app.jar"]
CMD ["--spring.profiles.active=dev"]

#ENTRYPOINT ["java", "-Xmx512m","-Djava.security.egd=file:/dev/./urandom", "-Ddruid.mysql.usePingMethod=false", "-jar", "/app.jar"]
#CMD ["--spring.profiles.active=dev"]
