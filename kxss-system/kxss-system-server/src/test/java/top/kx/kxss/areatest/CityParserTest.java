package top.kx.kxss.areatest;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.TimeInterval;
import cn.hutool.log.StaticLog;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.system.entity.system.DefArea;

import java.util.List;

@SpringBootTest
@ExtendWith(SpringExtension.class)
@Slf4j
public class CityParserTest {

    @Autowired
    CityParser cityParser;
    @Autowired
    SqlCityParserDecorator sqlCityParserDecorator;

    @BeforeEach
    public void setTenant() {
        ContextUtil.setDefTenantId();
    }


    /**
     * 实时爬取最新的地区数据，请执行该方法
     */
    @Test
    public void pullArea() {

        TimeInterval timer = DateUtil.timer();
        List<DefArea> list = cityParser.parseProvinces(2);
        long interval = timer.interval();// 花费毫秒数
        long intervalMinute = timer.intervalMinute();// 花费分钟数
        StaticLog.error("爬取数据 花费毫秒数: {} ,   花费分钟数:{} . ", interval, intervalMinute);

//        System.out.println(JSONObject.toJSONString(list, true));

        TimeInterval timer2 = DateUtil.timer();
        // 持久化
        sqlCityParserDecorator.batchSave(list);

        // ---------------------------------
        long interval2 = timer2.interval();// 花费毫秒数
        long intervalMinute2 = timer2.intervalMinute();// 花费分钟数
        StaticLog.error("保存数据 花费毫秒数: {} ,   花费分钟数:{} . ", interval2, intervalMinute2);
    }

}
