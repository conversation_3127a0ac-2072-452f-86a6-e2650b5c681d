package top.kx.kxss.system.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import top.kx.basic.boot.config.BaseConfig;
import top.kx.basic.log.event.SysLogListener;
import top.kx.kxss.common.api.LogApi;

/**
 * <AUTHOR>
 * @date 2017-12-15 14:42
 */
@Configuration
public class SystemWebConfiguration extends BaseConfig {

    /**
     * lamp.log.enabled = true 并且 lamp.log.type=DB时实例该类
     */
    @Bean
    @ConditionalOnExpression("${lamp.log.enabled:true} && 'DB'.equals('${lamp.log.type:LOGGER}')")
    public SysLogListener sysLogListener(LogApi logApi) {
        return new SysLogListener(logApi::save);
    }
}
