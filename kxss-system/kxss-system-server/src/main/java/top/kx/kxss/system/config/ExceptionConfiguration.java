package top.kx.kxss.system.config;

import com.alibaba.csp.sentinel.util.StringUtil;
import io.seata.common.util.IOUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.exceptions.PersistenceException;
import org.mybatis.spring.MyBatisSystemException;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Configuration;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.stereotype.Controller;
import org.springframework.validation.BindException;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;
import org.springframework.web.multipart.MultipartException;
import org.springframework.web.multipart.support.MissingServletRequestPartException;
import org.springframework.web.servlet.DispatcherServlet;
import top.kx.basic.base.R;
import top.kx.basic.boot.handler.AbstractGlobalExceptionHandler;
import top.kx.basic.boot.utils.WebUtils;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.exception.ArgumentException;
import top.kx.basic.exception.BizException;
import top.kx.basic.exception.ForbiddenException;
import top.kx.basic.exception.UnauthorizedException;
import top.kx.kxss.common.constant.RabbitMqConstant;
import top.kx.kxss.system.entity.tenant.DefTenant;
import top.kx.kxss.system.service.tenant.DefTenantService;

import javax.annotation.Resource;
import javax.servlet.Servlet;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolationException;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.sql.SQLException;

/**
 * 全局异常处理
 *
 * <AUTHOR>
 * @date 2020年01月02日17:19:27
 */
@Configuration
@ConditionalOnClass({Servlet.class, DispatcherServlet.class})
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
@RestControllerAdvice(annotations = {RestController.class, Controller.class})
@Slf4j
public class ExceptionConfiguration extends AbstractGlobalExceptionHandler {

    @Resource
    private RabbitTemplate template;
    @Resource
    private DefTenantService defTenantService;


    @Override
    public R<?> bizException(BizException ex) {
        errorMsg(ex);
        return super.bizException(ex);
    }

    @Override
    public R<?> bizException(ArgumentException ex) {
        return super.bizException(ex);
    }


    @ExceptionHandler({IOException.class})
    public R<?> handleIOExceptionException(IOException ex) {
        log.warn("IOException:", ex);
        if (ex.getMessage().contains("Broken pipe")) {
            // 处理 Broken pipe 异常
            System.err.println("连接已关闭，无法发送数据");
            return R.success();
        } else {
            return super.otherExceptionHandler(ex);
        }
    }

    @Override
    public R<?> forbiddenException(ForbiddenException ex) {
        errorMsg(ex);
        return super.forbiddenException(ex);
    }

    @Override
    public R<?> unauthorizedException(UnauthorizedException ex) {
        errorMsg(ex);
        return super.unauthorizedException(ex);
    }

    @Override
    public R<?> httpMessageNotReadableException(HttpMessageNotReadableException ex) {
        errorMsg(ex);
        return super.httpMessageNotReadableException(ex);
    }

    @Override
    public R<?> bindException(BindException ex) {
        return super.bindException(ex);
    }

    @Override
    public R<?> methodArgumentTypeMismatchException(MethodArgumentTypeMismatchException ex) {
        errorMsg(ex);
        return super.methodArgumentTypeMismatchException(ex);
    }

    @Override
    public R<?> illegalStateException(IllegalStateException ex) {
        errorMsg(ex);
        return super.illegalStateException(ex);
    }

    @Override
    public R<?> missingServletRequestParameterException(MissingServletRequestParameterException ex) {
        errorMsg(ex);
        return super.missingServletRequestParameterException(ex);
    }

    @Override
    public R<?> nullPointerException(NullPointerException ex) {
        errorMsg(ex);
        return super.nullPointerException(ex);
    }

    @Override
    public R<?> illegalArgumentException(IllegalArgumentException ex) {
        errorMsg(ex);
        return super.illegalArgumentException(ex);
    }

    @Override
    public R<?> httpMediaTypeNotSupportedException(HttpMediaTypeNotSupportedException ex) {
        errorMsg(ex);
        return super.httpMediaTypeNotSupportedException(ex);
    }

    @Override
    public R<?> missingServletRequestPartException(MissingServletRequestPartException ex) {
        errorMsg(ex);
        return super.missingServletRequestPartException(ex);
    }

    @Override
    public R<?> servletException(ServletException ex) {
        errorMsg(ex);
        return super.servletException(ex);
    }

    @Override
    public R<?> multipartException(MultipartException ex) {
        errorMsg(ex);
        return super.multipartException(ex);
    }

    @Override
    public R<?> constraintViolationException(ConstraintViolationException ex) {
        errorMsg(ex);
        return super.constraintViolationException(ex);
    }

    @Override
    public R<?> methodArgumentNotValidException(MethodArgumentNotValidException ex) {
        return super.methodArgumentNotValidException(ex);
    }

    @Override
    public R<?> otherExceptionHandler(Exception ex) {
        errorMsg(ex);
        return super.otherExceptionHandler(ex);
    }

    @Override
    public R<?> handleHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException ex) {
        errorMsg(ex);
        return super.handleHttpRequestMethodNotSupportedException(ex);
    }

    @Override
    public R<?> persistenceException(PersistenceException ex) {
        errorMsg(ex);
        return super.persistenceException(ex);
    }

    @Override
    public R<?> myBatisSystemException(MyBatisSystemException ex) {
        errorMsg(ex);
        return super.myBatisSystemException(ex);
    }

    @Override
    public R<?> sqlException(SQLException ex) {
        errorMsg(ex);
        return super.sqlException(ex);
    }

    @Override
    public R<?> dataIntegrityViolationException(DataIntegrityViolationException ex) {
        errorMsg(ex);
        return super.dataIntegrityViolationException(ex);
    }

    private void errorMsg(Exception ex) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        try {
            ex.printStackTrace(pw);
            String stackTraceString = errorPrefix() + sw.getBuffer().toString();
            template.convertAndSend(RabbitMqConstant.FANOUT_EXCHANGE, RabbitMqConstant.DING_DING_ALARM, stackTraceString);
        } finally {
            IOUtil.close(pw);
            IOUtil.close(sw);
        }
    }

    private String errorPrefix() {
        DefTenant defTenant = defTenantService.getByIdCache(ContextUtil.getTenantId());
        String stackTraceString = "【system】";
        if (defTenant != null) {
            stackTraceString += defTenant.getName();
        }
        HttpServletRequest request = WebUtils.request();
        if (request == null) {
            return stackTraceString;
        }
        String requestUrl = request.getRequestURL().toString();
        if (StringUtil.isNotBlank(requestUrl)) {
            stackTraceString += "[" + requestUrl + "]\r\n";
        }
        return stackTraceString;
    }

}
