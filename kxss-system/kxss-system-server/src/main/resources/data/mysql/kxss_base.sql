-- 内置角色
INSERT INTO `base_role`(`id`, `category`, `type_`, `name`, `code`, `remarks`, `state`, `readonly_`, `created_by`,
                        `created_time`, `updated_by`, `updated_time`, `created_org_id`, `delete_flag`)
VALUES (1452496398934081536, '10', '10', '老板', 'TENANT_ADMIN', '老板', 1, 1, 2,
        '2021-10-25 12:45:02', 2, '2021-10-26 18:25:50', 1, 0);

INSERT INTO `base_role`(`id`, `category`, `type_`, `name`, `code`, `remarks`, `state`, `readonly_`, `created_by`,
                        `created_time`, `updated_by`, `updated_time`, `created_org_id`, `delete_flag`)
VALUES (1452944729753780224, '10', '20', '老板(数据权限)', 'ORG_ADMIN', '老板(数据权限一般无需设置)', 1,1, 2,
        '2021-10-26 18:26:33', 2, '2021-10-26 18:26:33', 1, 0);

-- 服务分类
INSERT INTO `base_service_category` (`id`, `name`, `parent_id`, `tree_grade`, `sort_value`, `state`, `remarks`,
                                     `created_time`, `created_by`, `updated_time`, `updated_by`, `created_org_id`,
                                     delete_flag)
VALUES (0, '全部', NULL, 0, 1, 1, NULL, '2023-04-22 16:09:53', 1452186486253289472, '2023-04-22 16:09:53',
        1452186486253289472, 1, 0);

-- 商品分类
INSERT INTO `base_product_category` (`id`, `name`, `parent_id`, `tree_grade`, `sort_value`, `state`, `remarks`,
                                     `created_time`, `created_by`, `updated_time`, `updated_by`, `created_org_id`,
                                     delete_flag)
VALUES (0, '全部', NULL, 0, 1, 1, NULL, '2023-04-22 16:09:53', 1452186486253289472, '2023-04-22 16:09:53',
        1452186486253289472, 1, 0);
