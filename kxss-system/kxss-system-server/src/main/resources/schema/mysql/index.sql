

ALTER TABLE pos_cash
    ADD INDEX table_id_index (table_id);

ALTER TABLE pos_cash
    ADD INDEX created_time_index (created_time desc);


ALTER TABLE pos_cash
    ADD INDEX member_id_index (member_id desc);


ALTER TABLE pos_cash
    ADD INDEX complete_time_index (complete_time desc);


ALTER TABLE pos_cash
    ADD INDEX registration_time_index (registration_time desc);


ALTER TABLE pos_cash
    ADD INDEX bill_state_index (bill_state);


ALTER TABLE pos_cash
    ADD INDEX bill_type_index (bill_type);


ALTER TABLE pos_cash
    ADD INDEX code_index (code);

-- -------------------------
ALTER TABLE pos_cash_table
    ADD INDEX cash_id_index (cash_id);


ALTER TABLE pos_cash_table
    ADD INDEX table_id_index (table_id);


ALTER TABLE pos_cash_table
    ADD INDEX cash_thail_id_index (cash_thail_id);



ALTER TABLE pos_cash_table
    ADD INDEX created_time_index (created_time desc);
-- --------------------------

ALTER TABLE pos_cash_product
    ADD INDEX cash_id_index (cash_id);


ALTER TABLE pos_cash_product
    ADD INDEX product_id_index (product_id);


ALTER TABLE pos_cash_product
    ADD INDEX cash_thail_id_index (cash_thail_id);



ALTER TABLE pos_cash_product
    ADD INDEX created_time_index (created_time desc);
-- --------------------------

ALTER TABLE pos_cash_service
    ADD INDEX cash_id_index (cash_id);


ALTER TABLE pos_cash_service
    ADD INDEX service_id_index (service_id);


ALTER TABLE pos_cash_service
    ADD INDEX employee_id_index (employee_id);


ALTER TABLE pos_cash_service
    ADD INDEX cash_thail_id_index (cash_thail_id);


ALTER TABLE pos_cash_service
    ADD INDEX created_time_index (created_time desc);
-- --------------------------

ALTER TABLE pos_cash_thail
    ADD INDEX cash_id_index (cash_id);


ALTER TABLE pos_cash_thail
    ADD INDEX thail_id_index (thail_id);


ALTER TABLE pos_cash_thail
    ADD INDEX cash_thail_id_index (cash_thail_id);



ALTER TABLE pos_cash_thail
    ADD INDEX created_time_index (created_time desc);
-- --------------------------
ALTER TABLE pos_cash_payment
    ADD INDEX cash_id_index (cash_id);


ALTER TABLE pos_cash_payment
    ADD INDEX pay_type_id_index (pay_type_id);


ALTER TABLE pos_cash_payment
    ADD INDEX created_time_index (created_time desc);
-- --------------------------
ALTER TABLE pos_cash_discount_detail
    ADD INDEX pos_cash_id_index (pos_cash_id);


ALTER TABLE pos_cash_discount_detail
    ADD INDEX discount_type_index (discount_type);
-- --------------------------
