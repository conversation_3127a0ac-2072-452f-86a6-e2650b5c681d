lamp:
  swagger:
    version: '@project.version@'
  nacos:
    ip: ${NACOS_IP:@nacos.ip@}
    port: ${NACOS_PORT:@nacos.port@}
    namespace: ${NACOS_NAMESPACE:@nacos.namespace@}
    username: ${NACOS_USERNAME:@nacos.username@}
    password: ${NACOS_PASSWORD:@nacos.password@}
  seata:
    ip: ${SEATA_IP:@seata.ip@}
    port: ${SEATA_PORT:@seata.port@}
    namespace: ${SEATA_NAMESPACE:@seata.namespace@}
  sentinel:
    dashboard: ${SENTINEL_DASHBOARD:@sentinel.dashboard@}
spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: '@project.artifactId@'
    # lamp自定义配置，用于kxss-scan-starter扫描接口时，拼接uri前缀。 需要和网关配置的前缀一致
    path: /system
  profiles:
    active: '@profile.active@'
  cloud:
    inetutils:
      preferred-networks: 10.8.*.*
    sentinel:
      enabled: true
      filter:
        enabled: true
      eager: true
      transport:
        dashboard: ${lamp.sentinel.dashboard}
    nacos:
      config:
        server-addr: ${lamp.nacos.ip}:${lamp.nacos.port}
        file-extension: yml
        namespace: ${lamp.nacos.namespace}
        shared-configs:
          - dataId: common.yml
            refresh: true
          - dataId: redis.yml
            refresh: false
          - dataId: database.yml
            refresh: true
          - dataId: rabbitmq.yml
            refresh: false
        enabled: true
        username: ${lamp.nacos.username}
        password: ${lamp.nacos.password}
      discovery:
        username: ${lamp.nacos.username}
        password: ${lamp.nacos.password}
        server-addr: ${lamp.nacos.ip}:${lamp.nacos.port}
        namespace: ${lamp.nacos.namespace}
        metadata: # 元数据，用于权限服务实时获取各个服务的所有接口
          management.context-path: ${server.servlet.context-path:}${spring.mvc.servlet.path:}${management.endpoints.web.base-path:}
          gray_version: ${gray_version:@gray_version@}

# 只能配置在bootstrap.yml ，否则会生成 log.path_IS_UNDEFINED 文件夹
# window会自动在 代码所在盘 根目录下自动创建文件夹，  如： D:/data/projects/logs
logging:
  file:
    # 为什么要用绝对路径，而非相对路径？ 正式环境服务器磁盘通常是外挂到某个目录的，所以需要指定这个路径。 开发环境将所有系统日志存放在一起，便于后期清理日志文件，不用一个项目一个项目删除。
    path: '@logging.file.path@'
    name: ${logging.file.path}/${spring.application.name}/root.log
  level:
    com.baomidou.dynamic: info
  config: classpath:logback-spring.xml

# 用于/actuator/info
info:
  name: '@project.name@'
  description: '@project.description@'
  version: '@project.version@'
