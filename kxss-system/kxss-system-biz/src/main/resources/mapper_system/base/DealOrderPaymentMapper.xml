<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.deal.DealOrderPaymentMapper">
<!--
    代码生成器 by 2024-10-24 14:08:50
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.deal.DealOrderPayment">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="amount" property="amount" />
        <result column="order_source" property="orderSource" />
        <result column="platform_id" property="platformId" />
        <result column="refund_amount" property="refundAmount" />
        <result column="employee_id" property="employeeId" />
        <result column="mch_order_no" property="mchOrderNo" />
        <result column="pay_type" property="payType" />
        <result column="mch_fee_rate" property="mchFeeRate" />
        <result column="mch_fee_amount" property="mchFeeAmount" />
        <result column="sn" property="sn" />
        <result column="remarks" property="remarks" />
        <result column="tenant_id" property="tenantId" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, amount, order_source, platform_id, refund_amount, 
        employee_id, mch_order_no, pay_type, mch_fee_rate, mch_fee_amount, sn, 
        remarks, tenant_id, created_org_id, created_time, created_by, updated_time, 
        updated_by, delete_flag
    </sql>

</mapper>
