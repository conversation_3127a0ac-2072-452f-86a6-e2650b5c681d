<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.system.DefNorthStarStoreMapper">
<!--
    代码生成器 by 2023-11-02 10:09:45
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.system.DefNorthStarStore">
        <id column="id" property="id" />
        <result column="open_shop_uuid" property="openShopUuid" />
        <result column="shop_name" property="shopName" />
        <result column="shop_address" property="shopAddress" />
        <result column="city_name" property="cityName" />
        <result column="branch_name" property="branchName" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="tenant_id" property="tenantId" />
        <result column="org_id" property="orgId" />
        <result column="token" property="token" />
        <result column="refresh_token" property="refreshToken" />
        <result column="expires_milli" property="expiresMilli" />
        <result column="is_bind" property="isBind" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, open_shop_uuid, shop_name, shop_address, city_name, branch_name, 
        created_by, created_time, updated_by, updated_time, delete_flag, tenant_id, 
        org_id, token, refresh_token, expires_milli, is_bind
    </sql>

</mapper>
