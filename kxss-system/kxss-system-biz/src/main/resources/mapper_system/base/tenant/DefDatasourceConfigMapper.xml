<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.tenant.DefDatasourceConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.tenant.DefDatasourceConfig">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="url" jdbcType="VARCHAR" property="url"/>
        <result column="driver_class_name" jdbcType="VARCHAR" property="driverClassName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,created_time,created_by,updated_time,updated_by,
        name, username, password, url, driver_class_name
    </sql>

</mapper>
