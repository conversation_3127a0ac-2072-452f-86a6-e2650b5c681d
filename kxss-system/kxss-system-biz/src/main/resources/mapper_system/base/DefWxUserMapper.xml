<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.system.DefWxUserMapper">
<!--
    代码生成器 by 2023-12-14 09:44:13
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.system.DefWxUser">
        <id column="id" property="id" />
        <result column="app_type" property="appType" />
        <result column="subscribe_scene" property="subscribeScene" />
        <result column="subscribe_time" property="subscribeTime" />
        <result column="cancel_subscribe_time" property="cancelSubscribeTime" />
        <result column="open_id" property="openId" />
        <result column="phone" property="phone" />
        <result column="union_id" property="unionId" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="is_subscribe" property="isSubscribe" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, app_type, subscribe_scene, subscribe_time, cancel_subscribe_time, open_id, 
        phone, union_id, created_by, created_time, updated_by, updated_time, 
        delete_flag, is_subscribe
    </sql>

</mapper>
