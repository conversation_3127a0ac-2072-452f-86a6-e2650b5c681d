<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.system.DealTenantOrgTimeMapper">
<!--
    代码生成器 by 2024-10-21 15:48:08
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.system.DealTenantOrgTime">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="org_id" property="orgId" />
        <result column="expire_time" property="expireTime" />
        <result column="total_recharge_days" property="totalRechargeDays" />
        <result column="total_rechagr_amount" property="totalRechagrAmount" />
        <result column="total_recharge_times" property="totalRechargeTimes" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, org_id, expire_time, total_recharge_days, total_rechagr_amount,
        total_recharge_times, created_by, created_time, updated_by, updated_time, delete_flag

    </sql>

</mapper>
