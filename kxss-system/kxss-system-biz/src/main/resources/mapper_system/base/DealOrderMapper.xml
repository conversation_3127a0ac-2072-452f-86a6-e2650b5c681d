<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.deal.DealOrderMapper">
<!--
    代码生成器 by 2024-10-23 10:19:46
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.deal.DealOrder">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="status" property="status" />
        <result column="payment" property="payment" />
        <result column="original_price" property="originalPrice" />
        <result column="paid" property="paid" />
        <result column="unpaid" property="unpaid" />
        <result column="refund_time" property="refundTime" />
        <result column="refund_amount" property="refundAmount" />
        <result column="is_first" property="isFirst" />
        <result column="remarks" property="remarks" />
        <result column="complete_time" property="completeTime" />
        <result column="tenant_id" property="tenantId" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, code, status, payment, original_price, 
        paid, unpaid, refund_time, refund_amount, is_first, remarks, 
        complete_time, tenant_id, created_org_id, created_by, created_time, updated_by, 
        updated_time, delete_flag
    </sql>

</mapper>
