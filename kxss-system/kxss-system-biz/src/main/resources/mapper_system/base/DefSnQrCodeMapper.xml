<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.system.DefSnQrCodeMapper">
<!--
    代码生成器 by 2024-06-04 11:22:59
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.system.DefSnQrCode">
        <id column="id" property="id" />
        <result column="scene" property="scene" />
        <result column="page" property="page" />
        <result column="sn" property="sn" />
        <result column="params" property="params" />
        <result column="qr_code_id" property="qrCodeId" />
        <result column="times" property="times" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="status" property="status" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, scene, page, sn, params, qr_code_id, 
        times, created_by, created_time, updated_by, updated_time, delete_flag, 
        status
    </sql>

</mapper>
