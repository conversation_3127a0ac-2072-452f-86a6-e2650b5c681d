<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.clear.DefTenantOrgClearMapper">
<!--
    代码生成器 by 2025-06-20 17:43:48
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.clear.DefTenantOrgClear">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="org_id" property="orgId" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="remarks" property="remarks" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, org_id, start_time, end_time, remarks, 
        created_by, created_time, updated_by, updated_time, created_org_id, delete_flag
        
    </sql>

    <!--
  安全警告：使用 ${} 动态表名需确保参数经过校验
  要求：
  1. tenantId 必须是纯数字
  2. tableName 只能包含 [a-zA-Z0-9_]
-->
    <update id="copyTableWithData">
        CREATE TABLE kxss_base_${tenantId}.${newTableName} AS SELECT * FROM kxss_base_${tenantId}.${oldTableName} where created_org_id = #{orgId}
    </update>

    <delete id="markDataDeletedByTimeRange">
        DELETE FROM kxss_base_${tenantId}.${tableName}
        WHERE created_time BETWEEN #{startTime} AND #{endTime}
          AND created_org_id = #{orgId}
    </delete>

</mapper>
