<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.subscription.SubscriptionFeatureMapper">
<!--
    代码生成器 by 2025-05-07 10:17:58
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.subscription.SubscriptionFeature">
        <id column="id" property="id" />
        <result column="type_" property="type" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="module" property="module" />
        <result column="permission" property="permission" />
        <result column="enabled" property="enabled" />
        <result column="is_limit_count" property="isLimitCount" />
        <result column="sort" property="sort" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type_, code, name, description, module, 
        permission, enabled, is_limit_count, sort, created_time, created_by, 
        updated_time, updated_by, delete_flag
    </sql>

</mapper>
