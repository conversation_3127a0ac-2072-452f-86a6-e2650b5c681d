<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.system.DefSunmiTransactionMapper">
<!--
    代码生成器 by 2023-10-14 14:50:41
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.system.DefSunmiTransaction">
        <id column="id" property="id" />
        <result column="pos_cash_id" property="posCashId" />
        <result column="order_id" property="orderId" />
        <result column="merchant_no" property="merchantNo" />
        <result column="total_amount" property="totalAmount" />
        <result column="pay_scene" property="payScene" />
        <result column="pay_type" property="payType" />
        <result column="mini_app_id" property="miniAppId" />
        <result column="user_id" property="userId" />
        <result column="app_version" property="appVersion" />
        <result column="order_title" property="orderTitle" />
        <result column="order_detail" property="orderDetail" />
        <result column="expire_time" property="expireTime" />
        <result column="terminal_ip" property="terminalIp" />
        <result column="notify_url" property="notifyUrl" />
        <result column="attach" property="attach" />
        <result column="status" property="status" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, pos_cash_id, order_id, merchant_no, total_amount, pay_scene, 
        pay_type, mini_app_id, user_id, app_version, order_title, order_detail, 
        expire_time, terminal_ip, notify_url, attach, status, created_by, 
        created_time, updated_by, updated_time, created_org_id, delete_flag
    </sql>

</mapper>
