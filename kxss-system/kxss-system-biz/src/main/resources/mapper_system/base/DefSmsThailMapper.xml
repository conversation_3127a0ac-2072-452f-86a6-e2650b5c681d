<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.sms.DefSmsThailMapper">
<!--
    代码生成器 by 2024-04-28 18:12:22
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.sms.DefSmsThail">
        <id column="id" property="id" />
        <result column="total" property="total" />
        <result column="price" property="price" />
        <result column="single_price" property="singlePrice" />
        <result column="is_recommend" property="isRecommend" />
        <result column="sort_" property="sort" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, total, price, single_price, is_recommend, sort_, 
        created_by, created_time, updated_by, updated_time, delete_flag
    </sql>

</mapper>
