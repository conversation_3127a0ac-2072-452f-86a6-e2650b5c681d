<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.subscription.SubscriptionTenantTemplateMapper">
<!--
    代码生成器 by 2025-05-08 15:33:09
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplate">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="tenant_id" property="tenantId" />
        <result column="tmp_id" property="tmpId" />
        <result column="tmp_name" property="tmpName" />
        <result column="tmp_description" property="tmpDescription" />
        <result column="tmp_type" property="tmpType" />
        <result column="tmp_price" property="tmpPrice" />
        <result column="tmp_is_default" property="tmpIsDefault" />
        <result column="tmp_billing_type" property="tmpBillingType" />
        <result column="tmp_days" property="tmpDays" />
        <result column="price" property="price" />
        <result column="discount_price" property="discountPrice" />
        <result column="actual_price" property="actualPrice" />
        <result column="start_time" property="startTime" />
        <result column="expiration_time" property="expirationTime" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="admin_assigned" property="adminAssigned" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, tenant_id, tmp_id, tmp_name, tmp_description, 
        tmp_type, tmp_price, tmp_is_default, tmp_billing_type, tmp_days, price, 
        discount_price, actual_price, start_time, expiration_time, status, remark, 
        admin_assigned, created_time, created_by, updated_time, updated_by, delete_flag
        
    </sql>

</mapper>
