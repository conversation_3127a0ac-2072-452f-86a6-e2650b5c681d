<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.template.DefQueryWhereMapper">
<!--
    代码生成器 by 2024-01-06 17:20:29
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.template.DefQueryWhere">
        <id column="id" property="id" />
        <result column="company_id" property="companyId" />
        <result column="query_template_id" property="queryTemplateId" />
        <result column="row_no" property="rowNo" />
        <result column="logic" property="logic" />
        <result column="left_parenthesis" property="leftParenthesis" />
        <result column="field" property="field" />
        <result column="name" property="name" />
        <result column="consult" property="consult" />
        <result column="condition" property="condition" />
        <result column="value" property="value" />
        <result column="right_parenthesis" property="rightParenthesis" />
        <result column="need" property="need" />
        <result column="parm" property="parm" />
        <result column="fix" property="fix" />
        <result column="fix_show" property="fixShow" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, company_id, query_template_id, row_no, logic, left_parenthesis, 
        field, name, consult, condition, value, right_parenthesis, 
        need, parm, fix, fix_show, created_time, created_by, 
        updated_time, updated_by, created_org_id, delete_flag
    </sql>

</mapper>
