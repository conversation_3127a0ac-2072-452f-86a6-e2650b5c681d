<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.scan.DefScanMapper">
<!--
    代码生成器 by 2024-08-29 15:20:38
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.scan.DefScan">
        <id column="id" property="id" />
        <result column="status" property="status" />
        <result column="error_msg" property="errorMsg" />
        <result column="scene" property="scene" />
        <result column="page" property="page" />
        <result column="tenant_id" property="tenantId" />
        <result column="org_id" property="orgId" />
        <result column="biz_type" property="bizType" />
        <result column="biz_id" property="bizId" />
        <result column="qr_code_id" property="qrCodeId" />
        <result column="times" property="times" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="applet_id" property="appletId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, status, error_msg, scene, page, tenant_id, 
        org_id, biz_type, biz_id, qr_code_id, times, created_by, 
        created_time, updated_by, updated_time, delete_flag, applet_id
    </sql>

</mapper>
