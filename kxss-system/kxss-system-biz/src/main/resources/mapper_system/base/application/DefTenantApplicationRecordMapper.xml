<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.application.DefTenantApplicationRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.application.DefTenantApplicationRecord">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="tenant_application_rel_id" jdbcType="BIGINT" property="tenantApplicationRelId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="application_id" jdbcType="BIGINT" property="applicationId"/>
        <result column="application_name" jdbcType="VARCHAR" property="applicationName"/>
        <result column="tenant_name" jdbcType="VARCHAR" property="tenantName"/>
        <result column="operate_by_name" jdbcType="VARCHAR" property="operateByName"/>
        <result column="grant_type" jdbcType="CHAR" property="grantType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,created_by,created_time,updated_by,updated_time,
        tenant_application_rel_id, tenant_id, application_id, application_name, tenant_name, operate_by_name, grant_type
    </sql>

</mapper>
