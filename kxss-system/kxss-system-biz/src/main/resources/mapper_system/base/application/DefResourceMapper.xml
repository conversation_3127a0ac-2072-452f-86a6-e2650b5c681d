<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.application.DefResourceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.application.DefResource">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="parent_id" jdbcType="BIGINT" property="parentId"/>
        <result column="sort_value" jdbcType="INTEGER" property="sortValue"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="application_id" jdbcType="BIGINT" property="applicationId"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="resource_type" jdbcType="CHAR" property="resourceType"/>
        <result column="describe_" jdbcType="VARCHAR" property="describe"/>
        <result column="path" jdbcType="VARCHAR" property="path"/>
        <result column="open_with" jdbcType="VARCHAR" property="openWith"/>
        <result column="component" jdbcType="VARCHAR" property="component"/>
        <result column="redirect" jdbcType="VARCHAR" property="redirect"/>
        <result column="icon" jdbcType="VARCHAR" property="icon"/>
        <result column="is_general" jdbcType="BIT" property="isGeneral"/>
        <result column="state" jdbcType="BIT" property="state"/>
        <result column="sub_group" jdbcType="VARCHAR" property="subGroup"/>
        <result column="field_is_secret" jdbcType="BIT" property="fieldIsSecret"/>
        <result column="field_is_edit" jdbcType="BIT" property="fieldIsEdit"/>
        <result column="data_scope" jdbcType="CHAR" property="dataScope"/>
        <result column="custom_class" jdbcType="VARCHAR" property="customClass"/>
        <result column="is_def" jdbcType="BIT" property="isDef"/>
        <result column="meta_json" jdbcType="VARCHAR" property="metaJson"/>
        <result column="tree_grade" jdbcType="INTEGER" property="treeGrade"/>
        <result column="tree_path" jdbcType="VARCHAR" property="treePath"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,name,parent_id,sort_value,created_by,created_time,updated_by,updated_time,
        application_id, code, resource_type, describe_, open_with, path, component, redirect, icon, is_general, state, sub_group,
        field_is_secret, field_is_edit, data_scope, custom_class, is_def, meta_json, tree_grade, tree_path
    </sql>


</mapper>
