<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.system.DefAppletMapper">
<!--
    代码生成器 by 2023-07-06 15:48:44
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.system.DefApplet">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="app_key" property="appKey" />
        <result column="app_secret" property="appSecret" />
        <result column="wechat_original" property="wechatOriginal" />
        <result column="principal_name" property="principalName" />
        <result column="mch_id" property="mchId" />
        <result column="mch_key" property="mchKey" />
        <result column="tenant_id" property="tenantId" />
        <result column="state" property="state" />
        <result column="client_id" property="clientId" />
        <result column="remarks" property="remarks" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, app_key, app_secret, wechat_original, principal_name,
        mch_id, mch_key, tenant_id, state, client_id, remarks,
        created_time, created_by, updated_time, updated_by, delete_flag
    </sql>

</mapper>
