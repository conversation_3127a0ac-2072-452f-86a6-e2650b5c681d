<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.system.DefTaskMapper">
<!--
    代码生成器 by 2024-12-12 17:42:51
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.system.DefTask">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="org_id" property="orgId" />
        <result column="module" property="module" />
        <result column="type" property="type" />
        <result column="status" property="status" />
        <result column="desc_" property="desc" />
        <result column="execution_time" property="executionTime" />
        <result column="completion_time" property="completionTime" />
        <result column="employee_id" property="employeeId" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, tenant_id, org_id, module, type, status, 
        desc_, execution_time, completion_time, employee_id, created_time, created_by, 
        updated_time, updated_by, delete_flag
    </sql>

</mapper>
