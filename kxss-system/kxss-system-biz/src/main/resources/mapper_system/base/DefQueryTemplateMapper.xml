<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.template.DefQueryTemplateMapper">
<!--
    代码生成器 by 2024-01-06 17:11:55
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.template.DefQueryTemplate">
        <id column="id" property="id" />
        <result column="category_id" property="categoryId" />
        <result column="code" property="code" />
        <result column="name" property="name" />
        <result column="query_table" property="queryTable" />
        <result column="query_field" property="queryField" />
        <result column="query_where" property="queryWhere" />
        <result column="query_end" property="queryEnd" />
        <result column="pk_name" property="pkName" />
        <result column="is_distinct" property="isDistinct" />
        <result column="connection" property="connection" />
        <result column="cross_factor" property="crossFactor" />
        <result column="link_page" property="linkPage" />
        <result column="detail_sql" property="detailSql" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, category_id, code, name, query_table, query_field, 
        query_where, query_end, pk_name, is_distinct, connection, cross_factor, 
        link_page, detail_sql, created_time, created_by, updated_time, updated_by, 
        created_org_id, delete_flag
    </sql>

</mapper>
