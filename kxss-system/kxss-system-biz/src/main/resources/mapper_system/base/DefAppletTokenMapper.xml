<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.system.DefAppletTokenMapper">
<!--
    代码生成器 by 2024-04-27 10:41:58
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.system.DefAppletToken">
        <id column="id" property="id" />
        <result column="component_appid" property="componentAppid" />
        <result column="component_access_token" property="componentAccessToken" />
        <result column="authorizer_appid" property="authorizerAppid" />
        <result column="authorizer_access_token" property="authorizerAccessToken" />
        <result column="expires_in" property="expiresIn" />
        <result column="authorizer_refresh_token" property="authorizerRefreshToken" />
        <result column="func_info" property="funcInfo" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, component_appid, component_access_token, authorizer_appid, authorizer_access_token, expires_in, 
        authorizer_refresh_token, func_info, created_by, created_time, updated_by, updated_time, 
        delete_flag
    </sql>

</mapper>
