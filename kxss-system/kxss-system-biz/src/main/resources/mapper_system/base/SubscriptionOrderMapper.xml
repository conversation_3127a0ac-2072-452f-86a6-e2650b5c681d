<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.subscription.order.SubscriptionOrderMapper">
<!--
    代码生成器 by 2025-06-09 17:25:13
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.subscription.order.SubscriptionOrder">
        <id column="id" property="id" />
        <result column="code" property="code" />
        <result column="tenant_id" property="tenantId" />
        <result column="org_id" property="orgId" />
        <result column="plan_name" property="planName" />
        <result column="price" property="price" />
        <result column="discount_price" property="discountPrice" />
        <result column="actual_price" property="actualPrice" />
        <result column="status" property="status" />
        <result column="remark" property="remark" />
        <result column="pay_order_id" property="payOrderId" />
        <result column="admin_assigned" property="adminAssigned" />
        <result column="cancel_time" property="cancelTime" />
        <result column="expire_time" property="expireTime" />
        <result column="complete_time" property="completeTime" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, tenant_id, org_id, plan_name, price, 
        discount_price, actual_price, status, remark, pay_order_id, admin_assigned, 
        cancel_time, expire_time, complete_time, created_time, created_by, updated_time, 
        updated_by, delete_flag
    </sql>

</mapper>
