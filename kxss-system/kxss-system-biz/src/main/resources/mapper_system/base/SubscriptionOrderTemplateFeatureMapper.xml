<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.subscription.order.SubscriptionOrderTemplateFeatureMapper">
<!--
    代码生成器 by 2025-06-09 19:04:22
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.subscription.order.SubscriptionOrderTemplateFeature">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="feature_id" property="featureId" />
        <result column="feature_type" property="featureType" />
        <result column="feature_code" property="featureCode" />
        <result column="feature_name" property="featureName" />
        <result column="feature_description" property="featureDescription" />
        <result column="feature_module" property="featureModule" />
        <result column="feature_permission" property="featurePermission" />
        <result column="feature_is_limit_count" property="featureIsLimitCount" />
        <result column="feature_limit_count" property="featureLimitCount" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="order_tmp_id" property="orderTmpId" />
        <result column="tmp_id" property="tmpId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, feature_id, feature_type, feature_code, feature_name, 
        feature_description, feature_module, feature_permission, feature_is_limit_count, feature_limit_count, created_by, 
        created_time, updated_time, updated_by, delete_flag, order_tmp_id, tmp_id
        
    </sql>

</mapper>
