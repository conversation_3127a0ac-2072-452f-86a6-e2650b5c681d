<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.system.DefLoginLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.system.DefLoginLog">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="employee_id" jdbcType="BIGINT" property="employeeId"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <result column="request_ip" jdbcType="VARCHAR" property="requestIp"/>
        <result column="nick_name" jdbcType="VARCHAR" property="nickName"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="login_date" jdbcType="CHAR" property="loginDate"/>
        <result column="ua" jdbcType="VARCHAR" property="ua"/>
        <result column="browser" jdbcType="VARCHAR" property="browser"/>
        <result column="browser_version" jdbcType="VARCHAR" property="browserVersion"/>
        <result column="operating_system" jdbcType="VARCHAR" property="operatingSystem"/>
        <result column="location" jdbcType="VARCHAR" property="location"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , created_time, created_by, updated_time, updated_by,
        tenant_id, employee_id, user_id, request_ip, nick_name, username, description, login_date, ua, browser, browser_version, operating_system, location
    </sql>

</mapper>
