<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.system.DefClientMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.system.DefClient">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="created_by" jdbcType="BIGINT" property="createdBy"/>
        <result column="created_time" jdbcType="TIMESTAMP" property="createdTime"/>
        <result column="updated_by" jdbcType="BIGINT" property="updatedBy"/>
        <result column="updated_time" jdbcType="TIMESTAMP" property="updatedTime"/>
        <result column="client_id" jdbcType="VARCHAR" property="clientId"/>
        <result column="client_secret" jdbcType="VARCHAR" property="clientSecret"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="type" jdbcType="CHAR" property="type"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="state" jdbcType="BIT" property="state"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        ,created_by,created_time,updated_by,updated_time,
        client_id, client_secret, name, type, remarks, state
    </sql>

</mapper>
