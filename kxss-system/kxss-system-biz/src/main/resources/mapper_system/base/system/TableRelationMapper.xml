<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.system.TableRelationMapper">
<!--
    代码生成器 by 2023-05-11 11:11:51
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.system.TableRelation">
        <id column="id" property="id" />
        <result column="main_table" property="mainTable" />
        <result column="quote_table" property="quoteTable" />
        <result column="main_filed" property="mainFiled" />
        <result column="quote_filed" property="quoteFiled" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, main_table, quote_table, main_filed, quote_filed, delete_flag, 
        created_by, created_time, updated_by, updated_time
    </sql>

</mapper>
