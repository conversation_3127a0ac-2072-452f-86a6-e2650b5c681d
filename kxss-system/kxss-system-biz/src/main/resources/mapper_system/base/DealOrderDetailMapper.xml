<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.deal.DealOrderDetailMapper">
<!--
    代码生成器 by 2024-10-24 14:08:24
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.deal.DealOrderDetail">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="name" property="name" />
        <result column="days" property="days" />
        <result column="gift_days" property="giftDays" />
        <result column="line_price" property="linePrice" />
        <result column="price" property="price" />
        <result column="is_unit_price" property="isUnitPrice" />
        <result column="first_discount_price" property="firstDiscountPrice" />
        <result column="remarks" property="remarks" />
        <result column="tenant_id" property="tenantId" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, name, days, gift_days, line_price, 
        price, is_unit_price, first_discount_price, remarks, tenant_id, created_org_id, 
        created_by, created_time, updated_by, updated_time, delete_flag
    </sql>

</mapper>
