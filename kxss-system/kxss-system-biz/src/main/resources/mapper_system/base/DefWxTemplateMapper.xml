<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.system.mapper.system.DefWxTemplateMapper">
<!--
    代码生成器 by 2023-12-12 11:17:48
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.system.entity.system.DefWxTemplate">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="content" property="content" />
        <result column="type" property="type" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="wx_template_id" property="wxTemplateId" />
        <result column="return_url" property="returnUrl" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, content, type, created_by, created_time, 
        updated_by, updated_time, delete_flag, wx_template_id, return_url
    </sql>

</mapper>
