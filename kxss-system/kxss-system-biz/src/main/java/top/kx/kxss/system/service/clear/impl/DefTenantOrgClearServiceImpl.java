package top.kx.kxss.system.service.clear.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.store.BaseStore;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.store.api.BaseStoreApi;
import top.kx.kxss.system.entity.clear.DefTenantOrgClear;
import top.kx.kxss.system.entity.clear.DefTenantOrgClearTable;
import top.kx.kxss.system.entity.tenant.DefTenant;
import top.kx.kxss.system.manager.clear.DefTenantOrgClearManager;
import top.kx.kxss.system.service.clear.DefTenantOrgClearService;
import top.kx.kxss.system.service.clear.DefTenantOrgClearTableService;
import top.kx.kxss.system.service.tenant.DefTenantService;
import top.kx.kxss.system.vo.query.clear.DefTenantOrgClearPageQuery;
import top.kx.kxss.system.vo.result.clear.DefTenantOrgClearResultVO;
import top.kx.kxss.system.vo.save.clear.DefTenantOrgClearSaveVO;
import top.kx.kxss.system.vo.update.clear.DefTenantOrgClearUpdateVO;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 数据清空记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-20 17:43:48
 * @create [2025-06-20 17:43:48] [yan] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class DefTenantOrgClearServiceImpl extends SuperServiceImpl<DefTenantOrgClearManager, Long, DefTenantOrgClear, DefTenantOrgClearSaveVO,
    DefTenantOrgClearUpdateVO, DefTenantOrgClearPageQuery, DefTenantOrgClearResultVO> implements DefTenantOrgClearService {

    private final DefTenantOrgClearTableService defTenantOrgClearTableService;
    private final DefTenantService defTenantService;
    private final BaseStoreApi baseStoreApi;

    @Override
    public DefTenantOrgClear save(DefTenantOrgClearSaveVO defTenantOrgClearSaveVO) {
        defTenantOrgClearSaveVO.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        DefTenant defTenant = defTenantService.getById(defTenantOrgClearSaveVO.getTenantId());
        ArgumentAssert.isTrue(Objects.nonNull(defTenant), "租户信息不存在");
        defTenantOrgClearSaveVO.setTenantName(defTenant.getName());

        try {
            ContextUtil.setTenantId(defTenantOrgClearSaveVO.getTenantId());
            ContextUtil.setTenantBasePoolName(defTenantOrgClearSaveVO.getTenantId());

            BaseStore baseStore = baseStoreApi.getById(defTenantOrgClearSaveVO.getOrgId());
            ArgumentAssert.isTrue(Objects.nonNull(baseStore), "门店信息不存在");
            defTenantOrgClearSaveVO.setOrgName(baseStore.getShortName());
        } finally {
            ContextUtil.remove();
        }

        try {
            ContextUtil.setDefTenantId();

            DefTenantOrgClear save = super.save(defTenantOrgClearSaveVO);
            if (CollUtil.isEmpty(defTenantOrgClearSaveVO.getTableList())) {
                return save;
            }
            String format = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
            List<DefTenantOrgClearTable> clearTableList = defTenantOrgClearSaveVO.getTableList().stream()
                    .map(s -> DefTenantOrgClearTable.builder()
                            .clearId(save.getId())
                            .tableBackup(s + "_" + format)
                            .tableName(s)
                            .createdOrgId(ContextUtil.getCurrentCompanyId())
                            .build()).collect(Collectors.toList());
            defTenantOrgClearTableService.saveBatch(clearTableList);
            for (DefTenantOrgClearTable defTenantOrgClearTable : clearTableList) {
                // 复制表
                superManager.copyTableWithData(defTenantOrgClearTable.getTableName(), defTenantOrgClearTable.getTableBackup(), save.getTenantId(), save.getOrgId());
                // 删除表
                superManager.markDataDeletedByTimeRange(save.getTenantId(), save.getOrgId(), defTenantOrgClearTable.getTableName(), save.getStartTime(), save.getEndTime());
            }
            return save;
        } finally {
            ContextUtil.remove();
        }
    }
}


