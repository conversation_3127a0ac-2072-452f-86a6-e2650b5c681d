package top.kx.kxss.system.annotation;

/**
 * <AUTHOR>
 * @date 2025/5/21 15:57
 */

import top.kx.kxss.model.enumeration.system.subscription.FeatureCodeEnum;
import top.kx.kxss.model.enumeration.system.subscription.SubscriptionFeatureTypeEnum;

import java.lang.annotation.*;

/**
 * 订阅功能权限检查注解
 *
 * <AUTHOR>
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface SubscriptionFeatureCheck {
    /**
     * 功能类型
     */
    SubscriptionFeatureTypeEnum type() default SubscriptionFeatureTypeEnum.TEMPLATE;

    /**
     * 功能代码
     */
    FeatureCodeEnum[] featureCode();

    /**
     * 无权限时的错误消息
     */
    String errorMessage() default "";

    /**
     * 是否检查数量限制
     */
    boolean checkLimit() default false;

    /**
     * 超出限制时的错误消息
     */
    String limitErrorMessage() default "";
}
