package top.kx.kxss.system.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.jackson.JsonUtil;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.app.query.PerformanceCommissionConsumeQuery;
import top.kx.kxss.app.query.TenantSubscriptionQuery;
import top.kx.kxss.app.query.YearEndConsumeQuery;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.RabbitMqConstant;
import top.kx.kxss.model.enumeration.system.DefTaskModuleEnum;
import top.kx.kxss.model.enumeration.system.DefTaskStatusEnum;
import top.kx.kxss.model.enumeration.system.DefTaskTypeEnum;
import top.kx.kxss.model.enumeration.system.subscription.SubscriptionStatusEnum;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplate;
import top.kx.kxss.system.entity.system.DefTask;
import top.kx.kxss.system.entity.system.DefTaskDetails;
import top.kx.kxss.system.service.subscription.SubscriptionTenantTemplateService;
import top.kx.kxss.system.service.system.DefTaskDetailsService;
import top.kx.kxss.system.service.system.DefTaskService;
import top.kx.kxss.system.vo.update.system.DefTaskUpdateVO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 短信套餐
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-28 17:22:09
 * @create [2024-04-28 17:22:09] [yan] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class TaskService {

    @Autowired
    private DefTaskService defTaskService;
    @Autowired
    private DefTaskDetailsService defTaskDetailsService;
    @Autowired
    private SubscriptionTenantTemplateService tenantTemplateService;
    @Autowired
    private RabbitTemplate template;

    public void task(String taskModule, int taskSize) {
        // 查询是否存在进行中的任务
        List<DefTask> defTaskList = defTaskService.list(Wraps.<DefTask>lbQ().eq(SuperEntity::getDeleteFlag, 0)
                .eq(DefTask::getModule, taskModule)
                .eq(DefTask::getStatus, DefTaskStatusEnum.RUNNING.getCode()));
        if (CollUtil.isNotEmpty(defTaskList)
                && defTaskList.size() >= taskSize) {
            return;
        }
        // 查询最新的一条, 未处理的
        defTaskList = defTaskService.list(Wraps.<DefTask>lbQ().eq(SuperEntity::getDeleteFlag, 0)
                .eq(DefTask::getModule, taskModule)
                .eq(DefTask::getStatus, DefTaskStatusEnum.INIT.getCode())
                .orderByAsc(SuperEntity::getCreatedTime)
                .last(" limit 1"));
        if (CollUtil.isEmpty(defTaskList)) {
            return;
        }
        DefTask defTask = defTaskList.get(0);
        defTask.setStatus(DefTaskStatusEnum.RUNNING.getCode());
        defTask.setExecutionTime(LocalDateTime.now());
        switch (DefTaskModuleEnum.get(defTask.getModule())) {
            case COMMISSION:
                commission(defTask);
                break;
            case YEAR_END_24:
                yearEnd24(defTask);
                break;
            default:
                log.info("未知的任务类型: {}", defTask.getModule());
                defTask.setDesc("未知的任务类型");
                defTask.setStatus(DefTaskStatusEnum.ERROR.getCode());
                break;

        }
        // 任务失败的, 暂时不重试
        defTaskService.updateById(BeanPlusUtil.toBean(defTask, DefTaskUpdateVO.class));
    }


    /**
     * 年终
     *
     * @param defTask
     */
    private void yearEnd24(DefTask defTask) {
        template.convertAndSend(RabbitMqConstant.REPORT_EXCHANGE, RabbitMqConstant.YEAR_END,
                JsonUtil.toJson(YearEndConsumeQuery.builder().taskId(defTask.getId())
                        .orgId(defTask.getOrgId()).tenantId(defTask.getTenantId())
                        .build()));
    }

    /**
     * 计算提成
     *
     * @param defTask
     */
    private void commission(DefTask defTask) {
//        List<DefTaskDetails> taskDetailsList = defTaskDetailsService.list(Wraps.<DefTaskDetails>lbQ().eq(DefTaskDetails::getTaskId, defTask.getId()));
//        if (CollUtil.isEmpty(taskDetailsList)) {
//            defTaskService.updateById(BeanPlusUtil.toBean(defTask, DefTaskUpdateVO.class));
//            return;
//        }
//        // 判断提成方式
//        if (StringUtils.equals(defTask.getType(), DefTaskTypeEnum.STORE.getCode())) {
//            for (DefTaskDetails taskDetails : taskDetailsList) {
//                PerformanceCommissionConsumeQuery commissionConsumeQuery = JsonUtil.parse(taskDetails.getParam(), PerformanceCommissionConsumeQuery.class);
//                commissionConsumeQuery.setTaskId(defTask.getId());
//                template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.PERFORMANCE_COMMISSION,
//                        JsonUtil.toJson(commissionConsumeQuery));
//            }
//        }
//        if (StringUtils.equals(defTask.getType(), DefTaskTypeEnum.ORDER.getCode())) {
//            for (DefTaskDetails taskDetails : taskDetailsList) {
//                PerformanceCommissionConsumeQuery commissionConsumeQuery = JsonUtil.parse(taskDetails.getParam(), PerformanceCommissionConsumeQuery.class);
//                commissionConsumeQuery.setTaskId(defTask.getId());
//                template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.PERFORMANCE_COMMISSION,
//                        JsonUtil.toJson(commissionConsumeQuery));
//            }
//        }
    }

    public void tenantSubscriptionTask() {
        List<SubscriptionTenantTemplate> tenantTemplates = tenantTemplateService.list(Wraps.<SubscriptionTenantTemplate>lbQ().eq(SubscriptionTenantTemplate::getDeleteFlag, 0)
                .eq(SubscriptionTenantTemplate::getStatus, SubscriptionStatusEnum.ACTIVE.getCode())
                .isNotNull(SubscriptionTenantTemplate::getExpirationTime)
                .le(SubscriptionTenantTemplate::getExpirationTime, DateUtils.format(LocalDateTime.now()
                        .withNano(0), DateUtils.DEFAULT_DATE_TIME_FORMAT))
        );
        if (CollUtil.isEmpty(tenantTemplates)) {
            return;
        }
        //tenantTemplates根据数量拆分多个list
        List<List<SubscriptionTenantTemplate>> tenantTemplatesList = CollUtil.split(tenantTemplates, 200);
        for (List<SubscriptionTenantTemplate> subscriptionTenantTemplates : tenantTemplatesList) {
            template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.TENANT_SUBSCRIPTION,
                    JsonUtil.toJson(TenantSubscriptionQuery.builder().tenantIdList(subscriptionTenantTemplates.stream()
                                    .map(SubscriptionTenantTemplate::getTenantId).collect(Collectors.toList()))
                            .uuid(UUID.randomUUID().toString())
                            .build()));
        }
    }
}


