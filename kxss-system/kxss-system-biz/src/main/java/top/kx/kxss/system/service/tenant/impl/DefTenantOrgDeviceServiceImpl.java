package top.kx.kxss.system.service.tenant.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.tenant.DefTenantOrgDevice;
import top.kx.kxss.system.manager.tenant.DefTenantOrgDeviceManager;
import top.kx.kxss.system.service.tenant.DefTenantOrgDeviceService;
import top.kx.kxss.system.vo.query.tenant.DefTenantOrgDevicePageQuery;
import top.kx.kxss.system.vo.result.tenant.DefTenantOrgDeviceResultVO;
import top.kx.kxss.system.vo.save.tenant.DefTenantOrgDeviceSaveVO;
import top.kx.kxss.system.vo.update.tenant.DefTenantOrgDeviceUpdateVO;

import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 业务实现类
 * 商户门店设备信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-08-17 10:28:19
 * @create [2023-08-17 10:28:19] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class DefTenantOrgDeviceServiceImpl extends SuperServiceImpl<DefTenantOrgDeviceManager, Long, DefTenantOrgDevice, DefTenantOrgDeviceSaveVO,
        DefTenantOrgDeviceUpdateVO, DefTenantOrgDevicePageQuery, DefTenantOrgDeviceResultVO> implements DefTenantOrgDeviceService {


    @Override
    public DefTenantOrgDevice getOne(LbQueryWrap<DefTenantOrgDevice> eq) {
        return superManager.getOne(eq);
    }

    @Override
    public boolean save(DefTenantOrgDevice build) {
        return superManager.save(build);
    }

    @Override
    public List<DefTenantOrgDevice> list(Wrapper<DefTenantOrgDevice> queryWrapper) {
        return superManager.list(queryWrapper);
    }

    @Override
    public String getRandomTerminalNum(Long ignoreId) {
        String upperCase = RandomUtil.randomString(12).toUpperCase();
        // 判断是否存在
        List<DefTenantOrgDevice> tenantOrgDeviceList = superManager.list(Wraps.<DefTenantOrgDevice>lbQ()
                .eq(DefTenantOrgDevice::getTerminalNum, upperCase)
                .ne(Objects.nonNull(ignoreId), DefTenantOrgDevice::getId, ignoreId));
        if (CollUtil.isEmpty(tenantOrgDeviceList)) {
            return upperCase;
        }
        return getRandomTerminalNum(ignoreId);
    }

    @Override
    public void update(DefTenantOrgDeviceUpdateVO build) {
        DefTenantOrgDevice tenantOrgDevice = superManager.getOne(Wraps.<DefTenantOrgDevice>lbQ().eq(DefTenantOrgDevice::getTenantId, build.getTenantId())
                .eq(DefTenantOrgDevice::getOrgId, build.getOrgId()).eq(DefTenantOrgDevice::getSn, build.getTenantId()
                        + "_" + build.getOrgId())
                .eq(DefTenantOrgDevice::getDeleteFlag, 0));
        if (ObjectUtil.isNotNull(tenantOrgDevice)) {
            tenantOrgDevice.setName(build.getName());
            tenantOrgDevice.setOrgState(build.getOrgState());
            superManager.updateById(tenantOrgDevice);
        }
    }

    @Override
    public IPage<DefTenantOrgDeviceResultVO> pageList(PageParams<DefTenantOrgDevicePageQuery> params) {
        DefTenantOrgDevicePageQuery query = params.getModel();
        params.setSort("");
        params.setOrder("");
        IPage<DefTenantOrgDevice> page = params.buildPage(DefTenantOrgDevice.class);
        LbQueryWrap<DefTenantOrgDevice> wrap = Wraps.<DefTenantOrgDevice>lbQ();
        wrap.eq(DefTenantOrgDevice::getDeleteFlag, 0);
        wrap.eq(ObjectUtil.isNotNull(query.getTenantState()), DefTenantOrgDevice::getTenantState, query.getTenantState());
        wrap.eq(ObjectUtil.isNotNull(query.getTenantId()), DefTenantOrgDevice::getTenantId, query.getTenantId());
        wrap.eq(ObjectUtil.isNotNull(query.getOrgId()), DefTenantOrgDevice::getOrgId, query.getOrgId());
        wrap.eq(StrUtil.isNotBlank(query.getType()), DefTenantOrgDevice::getType, query.getType());
        wrap.eq(StrUtil.isNotBlank(query.getSn()), DefTenantOrgDevice::getSn, query.getSn());
        wrap.like(StrUtil.isNotBlank(query.getSnName()), DefTenantOrgDevice::getSnName, query.getSnName());
        wrap.like(StrUtil.isNotBlank(query.getName()), DefTenantOrgDevice::getName, query.getName());
        wrap.like(StrUtil.isNotBlank(query.getBrand()), DefTenantOrgDevice::getBrand, query.getBrand());
        wrap.like(StrUtil.isNotBlank(query.getModel()), DefTenantOrgDevice::getModel, query.getModel());
        wrap.eq(StrUtil.isNotBlank(query.getTerminalNum()), DefTenantOrgDevice::getTerminalNum, query.getTerminalNum());
        wrap.orderByAsc(DefTenantOrgDevice::getCreatedTime);
        IPage<DefTenantOrgDevice> paged = page(page, wrap);
        if (CollUtil.isEmpty(paged.getRecords())) {
            return new Page<>(params.getCurrent(), params.getSize());
        }
        return BeanPlusUtil.toBeanPage(paged, DefTenantOrgDeviceResultVO.class);
    }

    @Override
    public List<DefTenantOrgDevice> queryList(LbQueryWrap<DefTenantOrgDevice> queryWrap) {
        return superManager.list(queryWrap);
    }
}


