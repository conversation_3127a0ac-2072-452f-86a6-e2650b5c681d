package top.kx.kxss.system.manager.subscription.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.subscription.SubscriptionTemplateFeature;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.subscription.SubscriptionTemplateFeatureManager;
import top.kx.kxss.system.mapper.subscription.SubscriptionTemplateFeatureMapper;

/**
 * <p>
 * 通用业务实现类
 * 订阅模版与功能权益
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-07 16:01:29
 * @create [2025-05-07 16:01:29] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SubscriptionTemplateFeatureManagerImpl extends SuperManagerImpl<SubscriptionTemplateFeatureMapper, SubscriptionTemplateFeature> implements SubscriptionTemplateFeatureManager {

}


