package top.kx.kxss.system.service.system.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperCacheServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.system.ParamTypeEnum;
import top.kx.kxss.system.entity.system.DefParameter;
import top.kx.kxss.system.manager.system.DefParameterManager;
import top.kx.kxss.system.service.system.DefParameterService;
import top.kx.kxss.system.vo.query.system.DefParameterPageQuery;
import top.kx.kxss.system.vo.result.system.DefParameterResultVO;
import top.kx.kxss.system.vo.save.system.DefParameterSaveVO;
import top.kx.kxss.system.vo.update.system.DefParameterUpdateVO;

/**
 * <p>
 * 业务实现类
 * 参数配置
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
@DS(DsConstant.DEFAULTS)
public class DefParameterServiceImpl extends SuperCacheServiceImpl<DefParameterManager, Long, DefParameter, DefParameterSaveVO, DefParameterUpdateVO, DefParameterPageQuery, DefParameterResultVO> implements DefParameterService {

    @Override
    protected DefParameter saveBefore(DefParameterSaveVO defParameterSaveVO) {
        DefParameter defParameter = super.saveBefore(defParameterSaveVO);
        if (StrUtil.isBlank(defParameterSaveVO.getParamType())) {
            defParameter.setParamType(ParamTypeEnum.SYSTEM.getCode());
        }
        return defParameter;
    }

    @Override
    public Boolean checkKey(String key, Long id) {
        ArgumentAssert.notEmpty(key, "请填写参数健");
        return superManager.count(Wraps.<DefParameter>lbQ().eq(DefParameter::getKey, key).ne(DefParameter::getId, id)) > 0;
    }
}
