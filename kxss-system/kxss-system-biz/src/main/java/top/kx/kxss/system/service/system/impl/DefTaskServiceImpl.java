package top.kx.kxss.system.service.system.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.system.DefTask;
import top.kx.kxss.system.entity.system.DefTaskDetails;
import top.kx.kxss.system.manager.system.DefTaskManager;
import top.kx.kxss.system.service.system.DefTaskDetailsService;
import top.kx.kxss.system.service.system.DefTaskService;
import top.kx.kxss.system.vo.query.system.DefTaskPageQuery;
import top.kx.kxss.system.vo.result.system.DefTaskResultVO;
import top.kx.kxss.system.vo.save.system.DefTaskSaveVO;
import top.kx.kxss.system.vo.update.system.DefTaskUpdateVO;

import java.util.List;

/**
 * <p>
 * 业务实现类
 * 任务表
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-12 17:42:51
 * @create [2024-12-12 17:42:51] [yan] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class DefTaskServiceImpl extends SuperServiceImpl<DefTaskManager, Long, DefTask, DefTaskSaveVO,
        DefTaskUpdateVO, DefTaskPageQuery, DefTaskResultVO> implements DefTaskService {

    private final DefTaskDetailsService defTaskDetailsService;

    @Override
    public DefTask save(DefTaskSaveVO defTaskSaveVO) {
        defTaskSaveVO.setTenantId(ContextUtil.getTenantId());
        defTaskSaveVO.setOrgId(ContextUtil.getCurrentCompanyId());
        defTaskSaveVO.setEmployeeId(ContextUtil.getEmployeeId());
        defTaskSaveVO.setStatus("1");
        DefTask defTask = super.save(defTaskSaveVO);
        defTaskSaveVO.getDetailsSaveVOList().forEach(s -> {
            s.setTaskId(defTask.getId());
        });
        List<DefTaskDetails> saveList = BeanPlusUtil.toBeanList(defTaskSaveVO.getDetailsSaveVOList(), DefTaskDetails.class);
        defTaskDetailsService.saveBatch(saveList);
        return defTask;
    }

    @Override
    public Boolean updateById(DefTask defTask) {
        return superManager.updateById(defTask);
    }

    @Override
    public Boolean save(DefTask build) {
        return superManager.save(build);
    }
}


