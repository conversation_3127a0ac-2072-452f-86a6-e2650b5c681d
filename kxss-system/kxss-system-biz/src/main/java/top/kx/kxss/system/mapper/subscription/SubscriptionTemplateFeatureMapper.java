package top.kx.kxss.system.mapper.subscription;

import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.system.entity.subscription.SubscriptionTemplateFeature;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * Mapper 接口
 * 订阅模版与功能权益
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-07 16:01:29
 * @create [2025-05-07 16:01:29] [dou] [代码生成器生成]
 */
@Repository
public interface SubscriptionTemplateFeatureMapper extends SuperMapper<SubscriptionTemplateFeature> {

}


