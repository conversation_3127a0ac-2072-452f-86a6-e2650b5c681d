package top.kx.kxss.system.service.system.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.service.system.DefPrintTemplateService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.system.manager.system.DefPrintTemplateManager;
import top.kx.kxss.system.entity.system.DefPrintTemplate;
import top.kx.kxss.system.vo.save.system.DefPrintTemplateSaveVO;
import top.kx.kxss.system.vo.update.system.DefPrintTemplateUpdateVO;
import top.kx.kxss.system.vo.result.system.DefPrintTemplateResultVO;
import top.kx.kxss.system.vo.query.system.DefPrintTemplatePageQuery;

/**
 * <p>
 * 业务实现类
 * 打印模板
 * </p>
 *
 * <AUTHOR>
 * @date 2023-12-08 12:25:11
 * @create [2023-12-08 12:25:11] [yh] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.DEFAULTS)
@Transactional(readOnly = true)
public class DefPrintTemplateServiceImpl extends SuperServiceImpl<DefPrintTemplateManager, Long, DefPrintTemplate, DefPrintTemplateSaveVO,
    DefPrintTemplateUpdateVO, DefPrintTemplatePageQuery, DefPrintTemplateResultVO> implements DefPrintTemplateService {


}


