package top.kx.kxss.system.service.subscription;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.system.entity.subscription.SubscriptionTemplateFeature;
import top.kx.kxss.system.vo.save.subscription.SubscriptionTemplateFeatureSaveVO;
import top.kx.kxss.system.vo.update.subscription.SubscriptionTemplateFeatureUpdateVO;
import top.kx.kxss.system.vo.result.subscription.SubscriptionTemplateFeatureResultVO;
import top.kx.kxss.system.vo.query.subscription.SubscriptionTemplateFeaturePageQuery;


/**
 * <p>
 * 业务接口
 * 订阅模版与功能权益
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-07 16:01:29
 * @create [2025-05-07 16:01:29] [dou] [代码生成器生成]
 */
public interface SubscriptionTemplateFeatureService extends SuperService<Long, SubscriptionTemplateFeature, SubscriptionTemplateFeatureSaveVO,
    SubscriptionTemplateFeatureUpdateVO, SubscriptionTemplateFeaturePageQuery, SubscriptionTemplateFeatureResultVO> {

    boolean remove(LbUpdateWrap<SubscriptionTemplateFeature> eq);
}


