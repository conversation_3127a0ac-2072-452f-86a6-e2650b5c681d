package top.kx.kxss.system.service.subscription;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.subscription.SubscriptionFeature;
import top.kx.kxss.system.vo.query.subscription.SubscriptionFeaturePageQuery;
import top.kx.kxss.system.vo.result.subscription.SubscriptionFeatureResultVO;
import top.kx.kxss.system.vo.save.subscription.SubscriptionFeatureSaveVO;
import top.kx.kxss.system.vo.update.subscription.SubscriptionFeatureUpdateVO;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 功能权益
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-07 10:17:58
 * @create [2025-05-07 10:17:58] [dou] [代码生成器生成]
 */
public interface SubscriptionFeatureService extends SuperService<Long, SubscriptionFeature, SubscriptionFeatureSaveVO,
        SubscriptionFeatureUpdateVO, SubscriptionFeaturePageQuery, SubscriptionFeatureResultVO> {

    Boolean updateState(Long id, Boolean state);

    Boolean updateIsLimitCount(Long id, Boolean isLimitCount);

    boolean checkCode(String code, Long id);

    boolean checkName(String name, Long id);

    List<SubscriptionFeatureResultVO> queryTemplateFeature();

    Boolean deleteByIds(List<Long> ids);
}


