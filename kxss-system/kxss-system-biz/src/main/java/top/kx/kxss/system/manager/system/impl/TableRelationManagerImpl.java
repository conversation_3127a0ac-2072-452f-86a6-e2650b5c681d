package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.TableRelation;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.TableRelationManager;
import top.kx.kxss.system.mapper.system.TableRelationMapper;

/**
 * <p>
 * 通用业务实现类
 * 表间关联关系表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-11 11:11:51
 * @create [2023-05-11 11:11:51] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TableRelationManagerImpl extends SuperManagerImpl<TableRelationMapper, TableRelation> implements TableRelationManager {

}


