package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DefSunmiTransaction;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DefSunmiTransactionManager;
import top.kx.kxss.system.mapper.system.DefSunmiTransactionMapper;

/**
 * <p>
 * 通用业务实现类
 * 商米流水记录
 * </p>
 *
 * <AUTHOR>
 * @date 2023-10-14 14:50:41
 * @create [2023-10-14 14:50:41] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefSunmiTransactionManagerImpl extends SuperManagerImpl<DefSunmiTransactionMapper, DefSunmiTransaction> implements DefSunmiTransactionManager {

}


