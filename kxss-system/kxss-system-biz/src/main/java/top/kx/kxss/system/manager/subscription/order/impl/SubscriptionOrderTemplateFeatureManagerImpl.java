package top.kx.kxss.system.manager.subscription.order.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.subscription.order.SubscriptionOrderTemplateFeature;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.subscription.order.SubscriptionOrderTemplateFeatureManager;
import top.kx.kxss.system.mapper.subscription.order.SubscriptionOrderTemplateFeatureMapper;

/**
 * <p>
 * 通用业务实现类
 * 订单订阅模版与功能权益
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-09 18:56:23
 * @create [2025-06-09 18:56:23] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SubscriptionOrderTemplateFeatureManagerImpl extends SuperManagerImpl<SubscriptionOrderTemplateFeatureMapper, SubscriptionOrderTemplateFeature> implements SubscriptionOrderTemplateFeatureManager {

}


