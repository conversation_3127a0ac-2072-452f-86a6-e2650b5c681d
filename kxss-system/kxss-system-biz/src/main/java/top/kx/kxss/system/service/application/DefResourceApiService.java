package top.kx.kxss.system.service.application;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.application.DefResourceApi;
import top.kx.kxss.system.vo.save.application.DefResourceApiSaveVO;
import top.kx.kxss.system.vo.update.DefResourceApiUpdateVO;
import top.kx.kxss.system.vo.result.DefResourceApiResultVO;
import top.kx.kxss.system.vo.query.DefResourceApiPageQuery;


/**
 * <p>
 * 业务接口
 * 资源API接口
 * </p>
 *
 * <AUTHOR>
 * @date 2023-03-18 10:45:36
 * @create [2023-03-18 10:45:36] [<PERSON>] [代码生成器生成]
 */
public interface DefResourceApiService extends SuperService<Long, DefResourceApi, DefResourceApiSaveVO,
    DefResourceApiUpdateVO, DefResourceApiPageQuery, DefResourceApiResultVO> {

}


