package top.kx.kxss.system.mapper.subscription;

import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplateFeature;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * Mapper 接口
 * 租户订阅模版与功能权益
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-21 10:23:54
 * @create [2025-05-21 10:23:54] [dou] [代码生成器生成]
 */
@Repository
public interface SubscriptionTenantTemplateFeatureMapper extends SuperMapper<SubscriptionTenantTemplateFeature> {

}


