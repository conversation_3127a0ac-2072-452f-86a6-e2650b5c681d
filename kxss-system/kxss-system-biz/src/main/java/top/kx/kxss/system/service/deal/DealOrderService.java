package top.kx.kxss.system.service.deal;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.deal.DealOrder;
import top.kx.kxss.system.vo.save.deal.DealOrderSaveVO;
import top.kx.kxss.system.vo.update.deal.DealOrderUpdateVO;
import top.kx.kxss.system.vo.result.deal.DealOrderResultVO;
import top.kx.kxss.system.vo.query.deal.DealOrderPageQuery;


/**
 * <p>
 * 业务接口
 * 订单记录
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-23 10:19:46
 * @create [2024-10-23 10:19:46] [dou] [代码生成器生成]
 */
public interface DealOrderService extends SuperService<Long, DealOrder, DealOrderSaveVO,
    DealOrderUpdateVO, DealOrderPageQuery, DealOrderResultVO> {

    DealOrder defaultDealOrder();

    boolean save(DealOrder dealOrder);

    boolean updateById(DealOrder dealOrder);
}


