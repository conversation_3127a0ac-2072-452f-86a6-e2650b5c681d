package top.kx.kxss.system.service.subscription.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.basic.exception.BizException;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.utils.TemplateCodeGenerator;
import top.kx.kxss.model.enumeration.system.subscription.SubscriptionBillingTypeEnum;
import top.kx.kxss.model.enumeration.system.subscription.SubscriptionFeatureTypeEnum;
import top.kx.kxss.system.entity.subscription.SubscriptionFeature;
import top.kx.kxss.system.entity.subscription.SubscriptionTemplate;
import top.kx.kxss.system.entity.subscription.SubscriptionTemplateFeature;
import top.kx.kxss.system.manager.subscription.SubscriptionFeatureManager;
import top.kx.kxss.system.manager.subscription.SubscriptionTemplateManager;
import top.kx.kxss.system.service.subscription.SubscriptionTemplateFeatureService;
import top.kx.kxss.system.service.subscription.SubscriptionTemplateService;
import top.kx.kxss.system.vo.query.subscription.SubscriptionTemplatePageQuery;
import top.kx.kxss.system.vo.result.subscription.SubscriptionTemplateFeatureResultVO;
import top.kx.kxss.system.vo.result.subscription.SubscriptionTemplateResultVO;
import top.kx.kxss.system.vo.save.subscription.SubscriptionTemplateFeatureSaveVO;
import top.kx.kxss.system.vo.save.subscription.SubscriptionTemplateSaveVO;
import top.kx.kxss.system.vo.update.subscription.SubscriptionTemplateUpdateVO;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 订阅模版
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-07 11:17:35
 * @create [2025-05-07 11:17:35] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class SubscriptionTemplateServiceImpl extends SuperServiceImpl<SubscriptionTemplateManager, Long, SubscriptionTemplate, SubscriptionTemplateSaveVO,
        SubscriptionTemplateUpdateVO, SubscriptionTemplatePageQuery, SubscriptionTemplateResultVO> implements SubscriptionTemplateService {

    private final SubscriptionTemplateFeatureService templateFeatureService;
    private final SubscriptionFeatureManager featureManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateIsDefault(Long id, Boolean isDefault) {
        if (isDefault) {
            superManager.update(Wraps.<SubscriptionTemplate>lbU()
                    .set(SubscriptionTemplate::getIsDefault, false)
                    .eq(SubscriptionTemplate::getDeleteFlag, false)
                    .eq(SubscriptionTemplate::getIsDefault, true));
        }
        SubscriptionTemplate build = SubscriptionTemplate.builder().isDefault(isDefault).build();
        build.setId(id);
        return superManager.updateById(build);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateState(Long id, Boolean state) {
        SubscriptionTemplate byId = superManager.getById(id);
        ArgumentAssert.notNull(byId, "信息不存在");
        if (state) {
            superManager.update(Wraps.<SubscriptionTemplate>lbU()
                    .set(SubscriptionTemplate::getEnabled, false)
                    .eq(SubscriptionTemplate::getType, byId.getType())
                    .eq(SubscriptionTemplate::getDeleteFlag, false)
                    .eq(SubscriptionTemplate::getEnabled, true));
        }
        SubscriptionTemplate build = SubscriptionTemplate.builder().enabled(state).build();
        build.setId(id);
        return superManager.updateById(build);
    }

    @Override
    public boolean checkCode(String code, Long id) {
        return superManager.count(Wraps.<SubscriptionTemplate>lbQ().ne(SubscriptionTemplate::getId, id)
                .eq(SubscriptionTemplate::getCode, code)) > 0;
    }

    @Override
    public boolean checkName(String name, Long id) {
        return superManager.count(Wraps.<SubscriptionTemplate>lbQ().ne(SubscriptionTemplate::getId, id)
                .eq(SubscriptionTemplate::getName, name)) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(LbUpdateWrap<SubscriptionTemplate> eq) {
        return superManager.update(eq);
    }

    @Override
    public String getCode(String serviceType) {
        String code = TemplateCodeGenerator.generateTemplateCode(codePrefix(serviceType));
        if (checkCode(code, null)) {
            return getCode(serviceType);
        }
        return code;
    }

    private String codePrefix(String serviceType) {
        SubscriptionFeatureTypeEnum type = SubscriptionFeatureTypeEnum.get(serviceType);
        switch (type) {
            case TEMPLATE:
                return "TP_";
            case VALUE_ADDED:
                return "VA_";
            default:
                return "";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SubscriptionTemplate saveTemplate(SubscriptionTemplateSaveVO model) {
        SubscriptionTemplate template = save(model);
        saveTemplateFeature(template, model.getTemplateFeatureList());
        return template;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SubscriptionTemplate updateTemplate(SubscriptionTemplateUpdateVO model) {
        SubscriptionTemplate template = updateById(model);
        saveTemplateFeature(template, model.getTemplateFeatureList());
        return template;
    }

    private void saveTemplateFeature(SubscriptionTemplate template, List<SubscriptionTemplateFeatureSaveVO> templateFeatureList) {
        List<Long> featureIds = templateFeatureList.stream().map(SubscriptionTemplateFeatureSaveVO::getFeatureId)
                .collect(Collectors.toList());
        Map<Long, SubscriptionFeature> featureMap = featureManager.list(Wraps.<SubscriptionFeature>lbQ()
                        .in(SubscriptionFeature::getId, featureIds))
                .stream().collect(Collectors.toMap(SubscriptionFeature::getId, v -> v));
        if (CollUtil.isEmpty(featureMap)) {
            throw new BizException("功能不存在");
        }
        templateFeatureService.remove(Wraps.<SubscriptionTemplateFeature>lbU()
                .eq(SubscriptionTemplateFeature::getTmpId, template.getId())
                .eq(SubscriptionTemplateFeature::getDeleteFlag, 0));
        templateFeatureService.saveBatch(templateFeatureList.stream().map(v -> {
            if (!featureMap.containsKey(v.getFeatureId())) {
                throw new BizException("功能不存在");
            }
            SubscriptionFeature feature = featureMap.get(v.getFeatureId());
            return SubscriptionTemplateFeature.builder()
                    .featureId(feature.getId()).featureCode(feature.getCode())
                    .featureModule(feature.getModule()).featureName(feature.getName())
                    .featureId(feature.getId()).featureDescription(feature.getDescription())
                    .featurePermission(feature.getPermission()).featureType(feature.getType())
                    .tmpId(template.getId()).isLimitCount(v.getIsLimitCount())
                    .limitCount(v.getLimitCount() == null ? 0 : v.getLimitCount())
                    .build();
        }).collect(Collectors.toList()));
    }

    @Override
    public void tmpResultVO(SubscriptionTemplateResultVO record, Map<Long, List<SubscriptionTemplateFeature>> templateFeatureMap) {
        if (CollUtil.isNotEmpty(templateFeatureMap)
                && templateFeatureMap.containsKey(record.getId())) {
            List<SubscriptionTemplateFeature> templateFeatureList = templateFeatureMap.get(record.getId());
            List<SubscriptionTemplateFeatureResultVO> featureResultVOList = BeanPlusUtil.copyToList(templateFeatureList, SubscriptionTemplateFeatureResultVO.class);
            record.setTemplateFeatureList(featureResultVOList);
            record.setBenefits(templateFeatureList.stream().map(SubscriptionTemplateFeature::getFeatureId)
                    .distinct().collect(Collectors.toList()));
        }
        record.setBillingTypeDesc(billingTypeDesc(record.getBillingType(), record.getDays()));
    }

    @Override
    public String billingTypeDesc(String billingType, Integer days) {
        String billingTypeDesc = "";
        if (StrUtil.isNotBlank(billingType)) {
            SubscriptionBillingTypeEnum billingTypeEnum = SubscriptionBillingTypeEnum.get(billingType);
            billingTypeDesc = billingTypeEnum.getDesc();
            if (billingTypeEnum.getCode().equals(SubscriptionBillingTypeEnum.DAYS.getCode())) {
                billingTypeDesc = billingTypeDesc.concat("[" + days + "天]");
            }
        }
        return billingTypeDesc;
    }

    @Override
    public SubscriptionTemplate getOne(LbQueryWrap<SubscriptionTemplate> eq) {
        return superManager.getOne(eq);
    }
}


