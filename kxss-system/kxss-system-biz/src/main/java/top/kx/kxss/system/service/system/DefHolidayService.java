package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.system.DefHoliday;
import top.kx.kxss.system.vo.save.system.DefHolidaySaveVO;
import top.kx.kxss.system.vo.update.system.DefHolidayUpdateVO;
import top.kx.kxss.system.vo.result.system.DefHolidayResultVO;
import top.kx.kxss.system.vo.query.system.DefHolidayPageQuery;

import java.time.LocalDate;


/**
 * <p>
 * 业务接口
 * 节假日日期
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-10 18:24:54
 * @create [2024-04-10 18:24:54] [dou] [代码生成器生成]
 */
public interface DefHolidayService extends SuperService<Long, DefHoliday, DefHolidaySaveVO,
    DefHolidayUpdateVO, DefHolidayPageQuery, DefHolidayResultVO> {

    Boolean sync(Integer year);

    Boolean isHoliday(LocalDate localDate);
}


