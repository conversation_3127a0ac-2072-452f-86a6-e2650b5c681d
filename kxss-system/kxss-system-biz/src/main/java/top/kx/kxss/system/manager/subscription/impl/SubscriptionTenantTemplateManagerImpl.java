package top.kx.kxss.system.manager.subscription.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplate;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.subscription.SubscriptionTenantTemplateManager;
import top.kx.kxss.system.mapper.subscription.SubscriptionTenantTemplateMapper;

/**
 * <p>
 * 通用业务实现类
 * 租户订阅模板
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-08 15:33:09
 * @create [2025-05-08 15:33:09] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SubscriptionTenantTemplateManagerImpl extends SuperManagerImpl<SubscriptionTenantTemplateMapper, SubscriptionTenantTemplate> implements SubscriptionTenantTemplateManager {

}


