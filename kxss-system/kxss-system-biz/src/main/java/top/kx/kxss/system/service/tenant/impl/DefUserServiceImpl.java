package top.kx.kxss.system.service.tenant.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.impl.SuperCacheServiceImpl;
import top.kx.basic.cache.redis2.CacheResult;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.basic.exception.BizException;
import top.kx.basic.jackson.JsonUtil;
import top.kx.basic.model.cache.CacheKey;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.vo.save.user.EmployeeUpdateMobileByCodeVO;
import top.kx.kxss.common.cache.tenant.base.DefUserEmailCacheKeyBuilder;
import top.kx.kxss.common.cache.tenant.base.DefUserIdCardCacheKeyBuilder;
import top.kx.kxss.common.cache.tenant.base.DefUserMobileCacheKeyBuilder;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.RabbitMqConstant;
import top.kx.kxss.common.constant.RedisConstant;
import top.kx.kxss.common.properties.SystemProperties;
import top.kx.kxss.employee.EmployeeApi;
import top.kx.kxss.file.service.DefAppendixService;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.entity.tenant.DefUserTenantRel;
import top.kx.kxss.system.manager.tenant.DefUserManager;
import top.kx.kxss.system.manager.tenant.DefUserTenantRelManager;
import top.kx.kxss.system.service.tenant.DefUserService;
import top.kx.kxss.system.vo.query.tenant.DefUserPageQuery;
import top.kx.kxss.system.vo.result.tenant.DefUserResultVO;
import top.kx.kxss.system.vo.save.tenant.DefUserSaveVO;
import top.kx.kxss.system.vo.update.tenant.*;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 用户
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-09
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DS(DsConstant.DEFAULTS)
public class DefUserServiceImpl extends SuperCacheServiceImpl<DefUserManager, Long, DefUser, DefUserSaveVO, DefUserUpdateVO, DefUserPageQuery, DefUserResultVO>
        implements DefUserService {

    private final DefAppendixService appendixService;
    private final DefUserTenantRelManager defUserTenantRelManager;
    private final SystemProperties systemProperties;
    private final EmployeeApi employeeApi;
    private final RabbitTemplate template;

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        return superManager.findByIds(ids.stream().map(Convert::toLong).collect(Collectors.toSet()));
    }

    @Override
    public boolean checkUsername(String value, Long id) {
        return superManager.count(Wraps.<DefUser>lbQ().eq(DefUser::getUsername, value).ne(DefUser::getId, id)) > 0;
    }

    @Override
    public boolean checkEmail(String value, Long id) {
        return superManager.count(Wraps.<DefUser>lbQ().eq(DefUser::getEmail, value).ne(DefUser::getId, id)) > 0;
    }

    @Override
    public boolean checkMobile(String value, Long id) {
        return superManager.count(Wraps.<DefUser>lbQ().eq(DefUser::getMobile, value).ne(DefUser::getId, id)) > 0;
    }

    @Override
    public boolean checkIdCard(String value, Long id) {
        return superManager.count(Wraps.<DefUser>lbQ().eq(DefUser::getIdCard, value).ne(DefUser::getId, id)) > 0;
    }

    @Override
    public DefUser getUserByMobile(String mobile) {
        return superManager.getUserByMobile(mobile);
    }

    @Override
    public DefUser getUserByEmail(String email) {
        return superManager.getUserByEmail(email);
    }

    @Override
    public DefUser getUserByIdCard(String idCard) {
        return superManager.getUserByIdCard(idCard);
    }

    @Override
    public DefUser getUserByUsername(String username) {
        return superManager.getUserByUsername(username);
    }

    @Override
    protected DefUser saveBefore(DefUserSaveVO saveVO) {
        ArgumentAssert.isFalse(checkUsername(saveVO.getUsername(), null), "用户名：{}已经存在", saveVO.getUsername());
        if (StrUtil.isNotEmpty(saveVO.getEmail())) {
            ArgumentAssert.isFalse(checkEmail(saveVO.getEmail(), null), "邮箱：{}已经存在", saveVO.getEmail());
        }
        if (StrUtil.isNotEmpty(saveVO.getMobile())) {
            ArgumentAssert.isFalse(checkMobile(saveVO.getMobile(), null), "手机号：{}已经存在", saveVO.getMobile());
        }
        if (StrUtil.isNotEmpty(saveVO.getIdCard())) {
            ArgumentAssert.isFalse(checkIdCard(saveVO.getIdCard(), null), "身份证号：{}已经存在", saveVO.getIdCard());
        }
        DefUser defUser = BeanUtil.toBean(saveVO, DefUser.class);
        defUser.setSalt(RandomUtil.randomString(20));
        if (StrUtil.isEmpty(defUser.getPassword())) {
            if (StrUtil.isNotBlank(saveVO.getMobile()) && saveVO.getMobile().length() > 6) {
                String sub = StrUtil.sub(saveVO.getMobile(), saveVO.getMobile().length() - 6, saveVO.getMobile().length());
                defUser.setPassword(sub);
            } else {
                defUser.setPassword(systemProperties.getDefPwd());
            }
        }
        defUser.setPassword(SecureUtil.sha256(defUser.getPassword() + defUser.getSalt()));
        defUser.setPasswordErrorNum(0);
        defUser.setReadonly(false);
        defUser.setState(true);
        return defUser;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String register(DefUser defUser) {
        ArgumentAssert.isFalse(checkMobile(defUser.getMobile(), null), "手机号：{}已经存在", defUser.getMobile());
        setDefUser(defUser);
        defUser.setNickName(defUser.getMobile());

        superManager.save(defUser);
        return defUser.getMobile();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public String registerByEmail(DefUser defUser) {
        ArgumentAssert.isFalse(checkMobile(defUser.getEmail(), null), "邮箱：{}已经存在", defUser.getMobile());
        setDefUser(defUser);
        defUser.setNickName(defUser.getEmail());

        superManager.save(defUser);
        return defUser.getEmail();
    }

    private void setDefUser(DefUser defUser) {
        defUser.setSalt(RandomUtil.randomString(20));
        defUser.setPassword(SecureUtil.sha256(defUser.getPassword() + defUser.getSalt()));
        defUser.setPasswordErrorNum(0);
        defUser.setReadonly(false);
        defUser.setState(true);
        defUser.setUsername(UUID.fastUUID().toString(true));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public DefUserTenantRel saveUserAndEmployee(Long tenantId, DefUserSaveVO defUserSaveVO) {
        DefUser entity = getUserByMobile(defUserSaveVO.getMobile());
        if (ObjectUtil.isNull(entity)) {
            entity = saveBefore(defUserSaveVO);
            entity.setState(true);
            this.getSuperManager().save(entity);
        } else {
            entity.setState(true);
            this.getSuperManager().updateById(entity);
        }
        log.info("entity={}", JsonUtil.toJson(entity));
        DefUserTenantRel defUserTenantRel = new DefUserTenantRel();
        defUserTenantRel.setUserId(entity.getId());
        defUserTenantRel.setTenantId(tenantId);
        defUserTenantRel.setState(defUserSaveVO.getState());
        defUserTenantRel.setIsDefault(true);
        log.info("defUserTenantRel={}", JsonUtil.toJson(defUserTenantRel));
        defUserTenantRelManager.save(defUserTenantRel);
        return defUserTenantRel;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DefUserTenantRel saveUserAndUpdateEmployee(Long employeeId, DefUserSaveVO defUserSaveVO) {
        DefUser entity = getUserByMobile(defUserSaveVO.getMobile());
        if (ObjectUtil.isNull(entity)) {
            entity = saveBefore(defUserSaveVO);
            entity.setState(true);
            this.getSuperManager().save(entity);
        } else {
            entity.setState(true);
            this.getSuperManager().updateById(entity);
        }
        log.info("entity={}", JsonUtil.toJson(entity));
        DefUserTenantRel defUserTenantRel = defUserTenantRelManager.getById(employeeId);
        if (defUserTenantRel == null) {
            defUserTenantRel = new DefUserTenantRel();
            defUserTenantRel.setTenantId(ContextUtil.getTenantId());
            defUserTenantRel.setState(defUserSaveVO.getState());
            defUserTenantRel.setIsDefault(true);
        }
        defUserTenantRel.setUserId(entity.getId());
        log.info("defUserTenantRel={}", JsonUtil.toJson(defUserTenantRel));
        defUserTenantRelManager.saveOrUpdate(defUserTenantRel);
        return defUserTenantRel;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean resetPassword(DefUserPasswordResetVO data) {
        if (data.getIsUseSystemPassword()) {
            data.setPassword(systemProperties.getDefPwd());
        } else {
            ArgumentAssert.notEmpty(data.getConfirmPassword(), "请输入确认密码");
            ArgumentAssert.notEmpty(data.getPassword(), "请输入密码");
            ArgumentAssert.equals(data.getConfirmPassword(), data.getPassword(), "密码和确认密码不一致");
        }
        DefUser user = superManager.getById(data.getId());
        ArgumentAssert.notNull(user, "您要重置密码的用户不存在");

        boolean b = updateUserPassword(user.getId(), data.getPassword(), user.getSalt());
        if (b) {
            //清除账号登录
            template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.FORCE_LOGOUT,
                    user.getMobile());
        }
        return b;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateState(Long id, Boolean state) {
        // 演示环境专用标识，用于WriteInterceptor拦截器判断演示环境需要禁止用户执行sql，若您无需搭建演示环境，可以删除下面一行代码
        ContextUtil.setStop();
        return superManager.updateById(DefUser.builder().state(state).id(id).build());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateAvatar(DefUserAvatarUpdateVO data) {
        ArgumentAssert.isFalse(data.getAppendixAvatar() == null, "请上传或选择头像");
        boolean flag = appendixService.save(data.getId(), data.getAppendixAvatar());
        superManager.delCache(data.getId());
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePassword(DefUserPasswordUpdateVO data) {
        ArgumentAssert.notEmpty(data.getOldPassword(), "请输入旧密码");
        DefUser user = superManager.getById(data.getId());
        ArgumentAssert.notNull(user, "用户不存在");
        ArgumentAssert.equals(user.getId(), ContextUtil.getUserId(), "只能修改自己的密码");
        String oldPassword = SecureUtil.sha256(data.getOldPassword() + user.getSalt());
        ArgumentAssert.equals(user.getPassword(), oldPassword, "旧密码错误");

        boolean b = updateUserPassword(user.getId(), data.getPassword(), user.getSalt());
        if (b) {
            //清除账号登录
            template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.FORCE_LOGOUT,
                    user.getMobile());
        }
        return b;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updatePasswordByCode(DefUserPasswordUpdateByCodeVO data) {
        CacheKey cacheKey = new CacheKey(RedisConstant.UPDATE_PASSWORD_MSG + data.getKey());
        CacheResult<String> cacheResult = cacheOps.get(cacheKey);
        ArgumentAssert.notEmpty(cacheResult.getValue(), "验证码已失效～");
        ArgumentAssert.notEmpty(data.getConfirmPassword(), "请输入确认密码");
        ArgumentAssert.notEmpty(data.getPassword(), "请输入密码");
        ArgumentAssert.equals(data.getConfirmPassword(), data.getPassword(), "密码和确认密码不一致");
        ArgumentAssert.equals(data.getCode(), cacheResult.getValue(), "验证码输入错误");
        DefUser user = superManager.getUserByMobile(data.getMobile());
        ArgumentAssert.notNull(user, "用户不存在");
//        ArgumentAssert.equals(user.getId(), ContextUtil.getUserId(), "只能修改自己的密码");
        ArgumentAssert.equals(user.getMobile(), data.getMobile(), "只能修改自己的密码");
        boolean b = updateUserPassword(user.getId(), data.getPassword(), user.getSalt());
        if (b) {
            cacheOps.del(cacheKey);
            cacheKey = new CacheKey(RedisConstant.UPDATE_PASSWORD_MSG + data.getMobile());
            cacheOps.del(cacheKey);
            //清除账号登录
            template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.FORCE_LOGOUT,
                    user.getMobile());
        }
        return b;
    }

    @Override
    @GlobalTransactional
    public Boolean updateMobileByCode(DefUserMobileUpdateByCodeVO data) {
//        CacheKey cacheKey = new CacheKey(RedisConstant.UPDATE_PASSWORD_MSG + data.getKey());
//        CacheResult<String> cacheResult = cacheOps.get(cacheKey);
//        ArgumentAssert.notEmpty(cacheResult.getValue(), "验证码已失效～");
//        ArgumentAssert.equals(data.getCode(), cacheResult.getValue(), "验证码输入错误");
//        DefUser user = superManager.getById(data.getId());
//        ArgumentAssert.notNull(user, "用户不存在");
//        ArgumentAssert.isFalse(!ObjectUtil.equal(user.getId(), ContextUtil.getUserId()), "只能修改自己的手机号码");
//        ArgumentAssert.isFalse(ObjectUtil.equal(user.getMobile(), data.getMobile()), "手机号相同无需修改");
//        DefUser userByMobile = getUserByMobile(data.getMobile());
//        if (userByMobile != null && ContextUtil.getTenantId() != null) {
//            defUserTenantRelManager.update(Wraps.<DefUserTenantRel>lbU()
//                    .set(DefUserTenantRel::getUserId, userByMobile.getId())
//                    .eq(DefUserTenantRel::getUserId, user.getId())
//                    .eq(DefUserTenantRel::getTenantId, ContextUtil.getTenantId())
//                    .eq(DefUserTenantRel::getDeleteFlag, 0)
//            );
//            employeeApi.updateUserId(user.getId(), userByMobile.getId());
//        } else {
//            updateMobile(DefUserMobileUpdateVO.builder()
//                    .mobile(data.getMobile())
//                    .build());
//        }
//        cacheOps.del(cacheKey);
//        cacheKey = new CacheKey(RedisConstant.UPDATE_PASSWORD_MSG + data.getMobile());
//        cacheOps.del(cacheKey);
//        //清除账号登录
//        template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.FORCE_LOGOUT,
//                data.getMobile());
        R<Boolean> booleanR = employeeApi.mobileByCode(EmployeeUpdateMobileByCodeVO.builder()
                .code(data.getCode()).mobile(data.getMobile())
                .id(ContextUtil.getEmployeeId()).key(data.getKey())
                .build());
        return booleanR.getIsSuccess() && booleanR.getData();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateMobile(DefUserMobileUpdateVO data) {
        Long id = ContextUtil.getUserId();
        DefUser user = superManager.getById(id);
        ArgumentAssert.notNull(user, "用户不存在");
        // 淘汰旧手机缓存
        cacheOps.del(DefUserMobileCacheKeyBuilder.builder(user.getMobile()));
        //清除账号登录
        template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.FORCE_LOGOUT,
                user.getMobile());
        user.setMobile(data.getMobile());
        user.setSalt(RandomUtil.randomString(20));
        String sub = StrUtil.sub(data.getMobile(), data.getMobile().length() - 6, data.getMobile().length());
        String defPassword = SecureUtil.sha256(sub + user.getSalt());
        user.setPassword(defPassword);
        user.setUpdatedTime(LocalDateTime.now());
        superManager.updateById(user);
        // 淘汰旧手机缓存
        cacheOps.del(DefUserMobileCacheKeyBuilder.builder(user.getMobile()));
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateEmail(DefUserEmailUpdateVO data) {
        Long id = ContextUtil.getUserId();
        DefUser user = superManager.getById(id);
        ArgumentAssert.notNull(user, "用户不存在");
        user.setEmail(data.getEmail());
        user.setUpdatedTime(LocalDateTime.now());
        superManager.updateById(user);
        cacheOps.del(DefUserEmailCacheKeyBuilder.builder(user.getEmail()));
        return true;
    }

    private boolean updateUserPassword(Long id, String password, String salt) {
        if (StrUtil.isEmpty(salt)) {
            salt = RandomUtil.randomString(20);
        }
        String defPassword = SecureUtil.sha256(password + salt);
        boolean flag = superManager.update(Wrappers.<DefUser>lambdaUpdate()
                .set(DefUser::getPassword, defPassword)
                .set(DefUser::getUpdatePasswordTime, LocalDateTime.now())
                .set(DefUser::getPasswordErrorNum, 0L)
                .set(DefUser::getPasswordErrorLastTime, null)
                .set(DefUser::getPasswordExpireTime, null)
                .eq(DefUser::getId, id)
        );
        superManager.delCache(id);
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBaseInfo(DefUserBaseInfoUpdateVO data) {
        DefUser old = getById(data.getId());
        DefUser defUser = BeanUtil.toBean(data, DefUser.class);

        boolean flag = superManager.updateById(defUser);
        if (StrUtil.isAllNotEmpty(data.getIdCard(), old.getIdCard()) && !StrUtil.equals(old.getIdCard(), data.getIdCard())) {
            cacheOps.del(DefUserIdCardCacheKeyBuilder.builder(old.getIdCard()));
        }
        return flag;
    }

    @Override
    public IPage<DefUserResultVO> findNotUserByTenantId(PageParams<DefUserPageQuery> params) {
        IPage<DefUser> page = params.buildPage(DefUser.class);
        return superManager.selectNotUserByTenantId(params.getModel(), page);
    }

    @Override
    public IPage<DefUserResultVO> pageUserByTenant(PageParams<DefUserPageQuery> params) {
        IPage<DefUser> page = params.buildPage(DefUser.class);
        DefUserPageQuery pageQuery = params.getModel();
        if (pageQuery.getTenantId() == null) {
            pageQuery.setTenantId(ContextUtil.getTenantId());
        }
        return superManager.pageUserByTenant(pageQuery, page);
    }

    @Override
    public List<Long> findUserIdList(DefUserPageQuery pageQuery) {
        if (pageQuery == null) {
            return superManager.listObjs(Wraps.<DefUser>lbQ().select(DefUser::getId), Convert::toLong);
        }
        return superManager.listObjs(Wraps.<DefUser>lbQ().select(DefUser::getId)
                        .like(DefUser::getMobile, pageQuery.getMobile())
                        .like(DefUser::getUsername, pageQuery.getUsername())
                        .like(DefUser::getIdCard, pageQuery.getIdCard())
                        .like(DefUser::getEmail, pageQuery.getEmail())
                        .eq(DefUser::getSex, pageQuery.getSex())
                , Convert::toLong);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int resetPassErrorNum(Long id) {
        int count = superManager.resetPassErrorNum(id);
        superManager.delCache(id);
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void incrPasswordErrorNumById(Long id) {
        superManager.incrPasswordErrorNumById(id);
        superManager.delCache(id);
    }

    @Override
    public List<DefUserResultVO> queryUser(DefUserPageQuery params) {
        LbQueryWrap<DefUser> wrap = Wraps.lbQ();
        if (StrUtil.isAllEmpty(params.getEmail(), params.getUsername(), params.getIdCard(), params.getMobile())) {
            throw BizException.wrap("请至少传递一个参数");
        }
        wrap.eq(DefUser::getEmail, params.getEmail())
                .eq(DefUser::getUsername, params.getUsername())
                .eq(DefUser::getIdCard, params.getIdCard())
                .eq(DefUser::getMobile, params.getMobile());
        List<DefUser> list = superManager.list(wrap);
        return BeanPlusUtil.copyToList(list, DefUserResultVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean checkPassword(DefUserCheckPasswordVO checkPasswordVO) {
        ArgumentAssert.notEmpty(checkPasswordVO.getPassword(), "请输入密码");
        DefUser user = superManager.getById(checkPasswordVO.getUserId());
        ArgumentAssert.notNull(user, "用户不存在");
        String oldPassword = SecureUtil.sha256(checkPasswordVO.getPassword() + user.getSalt());
        ArgumentAssert.equals(user.getPassword(), oldPassword, "密码输入错误");
        return true;
    }

    @Override
    public DefUser getUserByToken() {
        return null;
    }


    @Override
    public String genLoginAccount() {
        String userName = generateRandomLoginAccount();
        //存在账号则重新生成
        if (checkUsername(userName, null)) {
            return genLoginAccount();
        }
        return userName;
    }

    private String generateRandomLoginAccount() {
        // 生成前两位或一位字母
        // 随机选择1或2
        int prefixLength = RandomUtil.randomInt(1, 3);
        String prefix = RandomUtil.randomString(RandomUtil.BASE_CHAR, prefixLength);

        // 生成中间6位数字
        String middle = RandomUtil.randomNumbers(6);

        // 生成最后一位字母
        String suffix = RandomUtil.randomString(RandomUtil.BASE_CHAR, 1);

        return prefix + middle + suffix;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(DefUser defUser) {
        return superManager.save(defUser);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateEntityById(DefUser defUser) {
        return superManager.updateById(defUser);
    }

    @Override
    public Boolean updateOne(LbUpdateWrap<DefUser> updateWrap) {
        return superManager.update(updateWrap);
    }

    public static void main(String[] args) {
        String str = "17266206504";
        String sub = StrUtil.sub(str, str.length() - 6, str.length());

        System.out.println(sub);
    }
}
