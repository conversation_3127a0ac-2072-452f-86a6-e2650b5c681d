package top.kx.kxss.system.service.subscription.order.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.subscription.order.SubscriptionOrderTemplate;
import top.kx.kxss.system.manager.subscription.order.SubscriptionOrderTemplateManager;
import top.kx.kxss.system.service.subscription.order.SubscriptionOrderTemplateService;
import top.kx.kxss.system.vo.query.subscription.order.SubscriptionOrderTemplatePageQuery;
import top.kx.kxss.system.vo.result.subscription.order.SubscriptionOrderTemplateResultVO;
import top.kx.kxss.system.vo.save.subscription.order.SubscriptionOrderTemplateSaveVO;
import top.kx.kxss.system.vo.update.subscription.order.SubscriptionOrderTemplateUpdateVO;

/**
 * <p>
 * 业务实现类
 * 订单订阅模板
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-09 18:56:18
 * @create [2025-06-09 18:56:18] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class SubscriptionOrderTemplateServiceImpl extends SuperServiceImpl<SubscriptionOrderTemplateManager, Long, SubscriptionOrderTemplate, SubscriptionOrderTemplateSaveVO,
    SubscriptionOrderTemplateUpdateVO, SubscriptionOrderTemplatePageQuery, SubscriptionOrderTemplateResultVO> implements SubscriptionOrderTemplateService {

    @Override
    public boolean save(SubscriptionOrderTemplate orderTemplate) {
        return superManager.save(orderTemplate);
    }

    @Override
    public boolean remove(LbQueryWrap<SubscriptionOrderTemplate> eq) {
        return superManager.remove(eq);
    }
}


