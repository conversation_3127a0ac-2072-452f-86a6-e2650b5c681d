package top.kx.kxss.system.service.sms.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.exception.BizException;
import top.kx.basic.exception.code.ExceptionCode;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.sms.DefSmsInfo;
import top.kx.kxss.system.manager.sms.DefSmsInfoManager;
import top.kx.kxss.system.service.sms.DefSmsInfoService;
import top.kx.kxss.system.vo.query.sms.DefSmsInfoPageQuery;
import top.kx.kxss.system.vo.result.sms.DefSmsInfoResultVO;
import top.kx.kxss.system.vo.save.sms.DefSmsInfoSaveVO;
import top.kx.kxss.system.vo.update.sms.DefSmsInfoUpdateNumVO;
import top.kx.kxss.system.vo.update.sms.DefSmsInfoUpdateVO;

import java.util.Objects;

/**
 * <p>
 * 业务实现类
 * 商户门店-短信
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-28 17:22:09
 * @create [2024-04-28 17:22:09] [yan] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class DefSmsInfoServiceImpl extends SuperServiceImpl<DefSmsInfoManager, Long, DefSmsInfo, DefSmsInfoSaveVO,
        DefSmsInfoUpdateVO, DefSmsInfoPageQuery, DefSmsInfoResultVO> implements DefSmsInfoService {
    @Autowired
    private DistributedLock distributedLock;

    @GlobalTransactional
    @Override
    public Boolean updateSmsNum(DefSmsInfoUpdateNumVO updateNumVO) {
        boolean lock = false;
        try {
            lock = distributedLock.lock(updateNumVO.getTenantId() + "_" + updateNumVO.getOrgId() + "_SMS", 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            // 先查询是否存在, 如果不存直接新增,否则更新
            DefSmsInfo smsInfo = superManager.getOne(Wraps.<DefSmsInfo>lbQ().eq(DefSmsInfo::getDeleteFlag, 0)
                    .eq(DefSmsInfo::getTenantId, updateNumVO.getTenantId()).eq(DefSmsInfo::getOrgId, updateNumVO.getOrgId()));
            if (updateNumVO.getIsUse()) {
                if (smsInfo == null
                        || smsInfo.getTotal() == 0
                        || smsInfo.getTotal() - smsInfo.getUse() < updateNumVO.getNum()
                        || smsInfo.getTotal() - smsInfo.getUse() <= 0
                ) {
                    log.error("短信条数不足, 无法发送");
                    return false;
                }
            }

            if (Objects.isNull(smsInfo)) {
                smsInfo = DefSmsInfo.builder()
                        .orgId(updateNumVO.getOrgId())
                        .tenantId(updateNumVO.getTenantId())
                        .tenantName(updateNumVO.getTenantName())
                        .orgName(updateNumVO.getOrgName())
                        .isNotice(false)
                        .total(updateNumVO.getIsUse() ? 0 : (updateNumVO.getIsAdd() ? updateNumVO.getNum() : -updateNumVO.getNum()))
                        .use(updateNumVO.getIsUse() ? updateNumVO.getNum() : 0)
                        .build();
                superManager.save(smsInfo);
                return true;
            }

            if (updateNumVO.getIsUse()) {
                smsInfo.setUse(updateNumVO.getNum() + smsInfo.getUse());
                if (!smsInfo.getIsNotice() && (smsInfo.getTotal() - smsInfo.getUse() <= 10)) {
                    smsInfo.setIsNotice(true);
                }
            } else {
                smsInfo.setTotal(updateNumVO.getIsAdd() ? updateNumVO.getNum() + smsInfo.getTotal() : smsInfo.getTotal() - updateNumVO.getNum());
                if (smsInfo.getIsNotice() && (smsInfo.getTotal() - smsInfo.getUse() > 10)) {
                    smsInfo.setIsNotice(false);
                }
            }
            return superManager.updateById(smsInfo);
        } finally {
            if (lock) {
                distributedLock.releaseLock(updateNumVO.getTenantId() + "_" + updateNumVO.getOrgId() + "_SMS");
            }
        }
    }
}


