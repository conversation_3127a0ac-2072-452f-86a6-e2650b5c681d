package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.system.DefTaskDetails;
import top.kx.kxss.system.vo.save.system.DefTaskDetailsSaveVO;
import top.kx.kxss.system.vo.update.system.DefTaskDetailsUpdateVO;
import top.kx.kxss.system.vo.result.system.DefTaskDetailsResultVO;
import top.kx.kxss.system.vo.query.system.DefTaskDetailsPageQuery;


/**
 * <p>
 * 业务接口
 * 任务详情
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-12 17:42:51
 * @create [2024-12-12 17:42:51] [yan] [代码生成器生成]
 */
public interface DefTaskDetailsService extends SuperService<Long, DefTaskDetails, DefTaskDetailsSaveVO,
    DefTaskDetailsUpdateVO, DefTaskDetailsPageQuery, DefTaskDetailsResultVO> {

}


