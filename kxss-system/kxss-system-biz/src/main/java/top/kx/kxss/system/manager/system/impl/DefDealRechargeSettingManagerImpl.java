package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DefDealRechargeSetting;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DefDealRechargeSettingManager;
import top.kx.kxss.system.mapper.system.DefDealRechargeSettingMapper;

/**
 * <p>
 * 通用业务实现类
 * 团购充值设置
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-19 17:21:26
 * @create [2024-10-19 17:21:26] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefDealRechargeSettingManagerImpl extends SuperManagerImpl<DefDealRechargeSettingMapper, DefDealRechargeSetting> implements DefDealRechargeSettingManager {

}


