package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DefSystemUpgrade;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DefSystemUpgradeManager;
import top.kx.kxss.system.mapper.system.DefSystemUpgradeMapper;

/**
 * <p>
 * 通用业务实现类
 * 系统升级公告
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-20 15:22:31
 * @create [2024-02-20 15:22:31] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefSystemUpgradeManagerImpl extends SuperManagerImpl<DefSystemUpgradeMapper, DefSystemUpgrade> implements DefSystemUpgradeManager {

}


