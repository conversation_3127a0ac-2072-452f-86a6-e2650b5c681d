package top.kx.kxss.system.manager.subscription.order;

import top.kx.basic.base.manager.SuperManager;
import top.kx.kxss.system.entity.subscription.order.SubscriptionOrderTemplate;

/**
 * <p>
 * 通用业务接口
 * 订单订阅模板
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-09 18:56:18
 * @create [2025-06-09 18:56:18] [dou] [代码生成器生成]
 */
public interface SubscriptionOrderTemplateManager extends SuperManager<SubscriptionOrderTemplate> {

}


