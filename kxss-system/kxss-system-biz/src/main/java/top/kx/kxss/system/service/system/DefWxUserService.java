package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.system.entity.system.DefWxUser;
import top.kx.kxss.system.vo.save.system.DefWxUserSaveVO;
import top.kx.kxss.system.vo.update.system.DefWxUserUpdateVO;
import top.kx.kxss.system.vo.result.system.DefWxUserResultVO;
import top.kx.kxss.system.vo.query.system.DefWxUserPageQuery;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 微信用户
 * </p>S
 *
 * <AUTHOR>
 * @date 2023-12-11 11:40:01
 * @create [2023-12-11 11:40:01] [yh] [代码生成器生成]
 */
public interface DefWxUserService extends SuperService<Long, DefWxUser, DefWxUserSaveVO,
    DefWxUserUpdateVO, DefWxUserPageQuery, DefWxUserResultVO> {

    List<DefWxUser> getByOpenidAndIsSubscribe(String openid,Boolean isSubscribe);

    DefWxUser getByOpenidAndPhone(String openid,String phone);

    DefWxUser getByPhoneAndIsSubscribe(String phone,Boolean isSubscribe, Long tenantId, Long orgId);

    DefWxUser getByMemberIdAndIsSubscribe(Long memberId, Boolean isSubscribe, Long tenantId, Long orgId);

    List<DefWxUser> getByPhoneListAndIsSubscribe(List<String> phoneList, Boolean isSubscribe, Long tenantId, Long orgId);

    List<DefWxUser> getByOpenidAndIsSubscribe(String openId, boolean isSubscribe, Long tenantId, Long orgId);

    boolean remove(LbUpdateWrap<DefWxUser> eq);

    boolean updateBatchById(List<DefWxUser> defWxUserList);

    boolean update(LbUpdateWrap<DefWxUser> eq);

    List<DefWxUser> getByMemberIdListAndIsSubscribe(List<Long> memberIdList, boolean isSubscribe, Long tenantId, Long orgId);


}


