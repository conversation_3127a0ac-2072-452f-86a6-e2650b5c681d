package top.kx.kxss.system.service.subscription.order.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.subscription.order.SubscriptionOrderTemplateFeature;
import top.kx.kxss.system.manager.subscription.order.SubscriptionOrderTemplateFeatureManager;
import top.kx.kxss.system.service.subscription.order.SubscriptionOrderTemplateFeatureService;
import top.kx.kxss.system.vo.query.subscription.order.SubscriptionOrderTemplateFeaturePageQuery;
import top.kx.kxss.system.vo.result.subscription.order.SubscriptionOrderTemplateFeatureResultVO;
import top.kx.kxss.system.vo.save.subscription.order.SubscriptionOrderTemplateFeatureSaveVO;
import top.kx.kxss.system.vo.update.subscription.order.SubscriptionOrderTemplateFeatureUpdateVO;

/**
 * <p>
 * 业务实现类
 * 订单订阅模版与功能权益
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-09 18:56:23
 * @create [2025-06-09 18:56:23] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class SubscriptionOrderTemplateFeatureServiceImpl extends SuperServiceImpl<SubscriptionOrderTemplateFeatureManager, Long, SubscriptionOrderTemplateFeature, SubscriptionOrderTemplateFeatureSaveVO,
        SubscriptionOrderTemplateFeatureUpdateVO, SubscriptionOrderTemplateFeaturePageQuery, SubscriptionOrderTemplateFeatureResultVO> implements SubscriptionOrderTemplateFeatureService {


    @Override
    public boolean remove(LbQueryWrap<SubscriptionOrderTemplateFeature> eq) {
        return superManager.remove(eq);
    }
}


