package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.system.DefWxTemplate;
import top.kx.kxss.system.vo.save.system.DefWxTemplateSaveVO;
import top.kx.kxss.system.vo.update.system.DefWxTemplateUpdateVO;
import top.kx.kxss.system.vo.result.system.DefWxTemplateResultVO;
import top.kx.kxss.system.vo.query.system.DefWxTemplatePageQuery;


/**
 * <p>
 * 业务接口
 * 微信模板
 * </p>
 *
 * <AUTHOR>
 * @date 2023-12-11 11:42:11
 * @create [2023-12-11 11:42:11] [yh] [代码生成器生成]
 */
public interface DefWxTemplateService extends SuperService<Long, DefWxTemplate, DefWxTemplateSaveVO,
    DefWxTemplateUpdateVO, DefWxTemplatePageQuery, DefWxTemplateResultVO> {

}


