package top.kx.kxss.system.service.system.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.system.DefDealRechargeSetting;
import top.kx.kxss.system.manager.system.DefDealRechargeSettingManager;
import top.kx.kxss.system.service.system.DefDealRechargeSettingService;
import top.kx.kxss.system.vo.query.system.DefDealRechargeSettingPageQuery;
import top.kx.kxss.system.vo.result.system.DefDealRechargeSettingResultVO;
import top.kx.kxss.system.vo.save.system.DefDealRechargeSettingSaveVO;
import top.kx.kxss.system.vo.update.system.DefDealRechargeSettingUpdateVO;

/**
 * <p>
 * 业务实现类
 * 团购充值设置
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-19 17:21:26
 * @create [2024-10-19 17:21:26] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class DefDealRechargeSettingServiceImpl extends SuperServiceImpl<DefDealRechargeSettingManager, Long, DefDealRechargeSetting, DefDealRechargeSettingSaveVO,
    DefDealRechargeSettingUpdateVO, DefDealRechargeSettingPageQuery, DefDealRechargeSettingResultVO> implements DefDealRechargeSettingService {


    @Override
    public Boolean updateState(Long id) {
        ArgumentAssert.notNull(id, "团购充值信息不能为空");
        DefDealRechargeSetting rechargeSetting = superManager.getById(id);
        ArgumentAssert.notNull(rechargeSetting, "团购充值信息不存在");
        rechargeSetting.setState(!rechargeSetting.getState());
        return superManager.updateById(rechargeSetting);
    }

    @Override
    public Boolean updateIsUnitPrice(Long id) {
        ArgumentAssert.notNull(id, "团购充值信息不能为空");
        DefDealRechargeSetting rechargeSetting = superManager.getById(id);
        ArgumentAssert.notNull(rechargeSetting, "团购充值信息不存在");
        rechargeSetting.setIsUnitPrice(!rechargeSetting.getIsUnitPrice());
        return superManager.updateById(rechargeSetting);
    }
}


