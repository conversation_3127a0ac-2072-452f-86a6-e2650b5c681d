package top.kx.kxss.system.service.subscription;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.system.entity.subscription.SubscriptionTemplate;
import top.kx.kxss.system.entity.subscription.SubscriptionTemplateFeature;
import top.kx.kxss.system.vo.query.subscription.SubscriptionTemplatePageQuery;
import top.kx.kxss.system.vo.result.subscription.SubscriptionTemplateResultVO;
import top.kx.kxss.system.vo.save.subscription.SubscriptionTemplateSaveVO;
import top.kx.kxss.system.vo.update.subscription.SubscriptionTemplateUpdateVO;

import java.util.List;
import java.util.Map;


/**
 * <p>
 * 业务接口
 * 订阅模版
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-07 11:17:35
 * @create [2025-05-07 11:17:35] [dou] [代码生成器生成]
 */
public interface SubscriptionTemplateService extends SuperService<Long, SubscriptionTemplate, SubscriptionTemplateSaveVO,
        SubscriptionTemplateUpdateVO, SubscriptionTemplatePageQuery, SubscriptionTemplateResultVO> {

    Boolean updateIsDefault(Long id, Boolean isDefault);

    Boolean updateState(Long id, Boolean state);

    boolean checkCode(String code, Long id);

    boolean checkName(String code, Long id);

    boolean update(LbUpdateWrap<SubscriptionTemplate> eq);

    String getCode(String serviceType);

    SubscriptionTemplate saveTemplate(SubscriptionTemplateSaveVO model);

    SubscriptionTemplate updateTemplate(SubscriptionTemplateUpdateVO model);

    void tmpResultVO(SubscriptionTemplateResultVO record, Map<Long, List<SubscriptionTemplateFeature>> templateFeatureMap);

    String billingTypeDesc(String billingType, Integer days);

    SubscriptionTemplate getOne(LbQueryWrap<SubscriptionTemplate> eq);
}


