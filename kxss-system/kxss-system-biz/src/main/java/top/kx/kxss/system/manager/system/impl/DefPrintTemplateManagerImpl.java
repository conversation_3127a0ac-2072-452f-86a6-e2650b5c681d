package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DefPrintTemplate;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DefPrintTemplateManager;
import top.kx.kxss.system.mapper.system.DefPrintTemplateMapper;

/**
 * <p>
 * 通用业务实现类
 * 打印模板
 * </p>
 *
 * <AUTHOR>
 * @date 2023-12-08 12:25:11
 * @create [2023-12-08 12:25:11] [yh] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefPrintTemplateManagerImpl extends SuperManagerImpl<DefPrintTemplateMapper, DefPrintTemplate> implements DefPrintTemplateManager {

}


