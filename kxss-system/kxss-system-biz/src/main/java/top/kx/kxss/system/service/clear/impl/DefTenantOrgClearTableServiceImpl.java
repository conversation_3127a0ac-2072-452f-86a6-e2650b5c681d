package top.kx.kxss.system.service.clear.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.service.clear.DefTenantOrgClearTableService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.system.manager.clear.DefTenantOrgClearTableManager;
import top.kx.kxss.system.entity.clear.DefTenantOrgClearTable;
import top.kx.kxss.system.vo.save.clear.DefTenantOrgClearTableSaveVO;
import top.kx.kxss.system.vo.update.clear.DefTenantOrgClearTableUpdateVO;
import top.kx.kxss.system.vo.result.clear.DefTenantOrgClearTableResultVO;
import top.kx.kxss.system.vo.query.clear.DefTenantOrgClearTablePageQuery;

/**
 * <p>
 * 业务实现类
 * 清空的表数据
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-20 17:43:47
 * @create [2025-06-20 17:43:47] [yan] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class DefTenantOrgClearTableServiceImpl extends SuperServiceImpl<DefTenantOrgClearTableManager, Long, DefTenantOrgClearTable, DefTenantOrgClearTableSaveVO,
    DefTenantOrgClearTableUpdateVO, DefTenantOrgClearTablePageQuery, DefTenantOrgClearTableResultVO> implements DefTenantOrgClearTableService {


}


