package top.kx.kxss.system.service.deal.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.system.entity.deal.DealOrder;
import top.kx.kxss.system.entity.deal.DealOrderPayment;
import top.kx.kxss.system.manager.deal.DealOrderPaymentManager;
import top.kx.kxss.system.service.deal.DealOrderPaymentService;
import top.kx.kxss.system.vo.query.deal.DealOrderPaymentPageQuery;
import top.kx.kxss.system.vo.result.deal.DealOrderPaymentResultVO;
import top.kx.kxss.system.vo.save.deal.DealOrderPaymentSaveVO;
import top.kx.kxss.system.vo.update.deal.DealOrderPaymentUpdateVO;

/**
 * <p>
 * 业务实现类
 * 团购充值收款记录
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-24 14:08:50
 * @create [2024-10-24 14:08:50] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class DealOrderPaymentServiceImpl extends SuperServiceImpl<DealOrderPaymentManager, Long, DealOrderPayment, DealOrderPaymentSaveVO,
        DealOrderPaymentUpdateVO, DealOrderPaymentPageQuery, DealOrderPaymentResultVO> implements DealOrderPaymentService {

    @Override
    public boolean save(DealOrderPayment build) {
        return superManager.save(build);
    }

    @Override
    public DealOrderPayment createCashPayment(DealOrder dealOrder, String mchOrderNo) {
        DealOrderPayment build = DealOrderPayment.builder()
                .createdOrgId(dealOrder.getCreatedOrgId()).orderId(dealOrder.getId())
                .amount(dealOrder.getUnpaid()).mchOrderNo(mchOrderNo)
                .status(PosCashPaymentStatusEnum.NO_PAY.getCode())
                .sn(ContextUtil.getSn()).employeeId(ContextUtil.getEmployeeId())
                .payType("").orderSource(ContextUtil.getOrderSource()).tenantId(dealOrder.getTenantId())
                .build();
        ArgumentAssert.isFalse(!save(build), "支付明细创建异常");
        return build;
    }

    @Override
    public DealOrderPayment getOne(LbQueryWrap<DealOrderPayment> eq) {
        return superManager.getOne(eq);
    }

    @Override
    public boolean updateById(DealOrderPayment dealOrderPayment) {
        return superManager.updateById(dealOrderPayment);
    }
}


