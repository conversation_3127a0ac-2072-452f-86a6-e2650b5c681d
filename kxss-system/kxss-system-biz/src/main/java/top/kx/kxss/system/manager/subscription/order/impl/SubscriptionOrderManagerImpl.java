package top.kx.kxss.system.manager.subscription.order.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.subscription.order.SubscriptionOrder;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.subscription.order.SubscriptionOrderManager;
import top.kx.kxss.system.mapper.subscription.order.SubscriptionOrderMapper;

/**
 * <p>
 * 通用业务实现类
 * 订单订阅模版
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-09 17:25:13
 * @create [2025-06-09 17:25:13] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SubscriptionOrderManagerImpl extends SuperManagerImpl<SubscriptionOrderMapper, SubscriptionOrder> implements SubscriptionOrderManager {

}


