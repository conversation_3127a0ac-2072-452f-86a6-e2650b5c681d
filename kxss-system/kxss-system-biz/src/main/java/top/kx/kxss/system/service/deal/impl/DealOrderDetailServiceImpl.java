package top.kx.kxss.system.service.deal.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.deal.DealOrderDetail;
import top.kx.kxss.system.manager.deal.DealOrderDetailManager;
import top.kx.kxss.system.service.deal.DealOrderDetailService;
import top.kx.kxss.system.vo.query.deal.DealOrderDetailPageQuery;
import top.kx.kxss.system.vo.result.deal.DealOrderDetailResultVO;
import top.kx.kxss.system.vo.save.deal.DealOrderDetailSaveVO;
import top.kx.kxss.system.vo.update.deal.DealOrderDetailUpdateVO;

/**
 * <p>
 * 业务实现类
 * 订单明细
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-24 14:08:24
 * @create [2024-10-24 14:08:24] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class DealOrderDetailServiceImpl extends SuperServiceImpl<DealOrderDetailManager, Long, DealOrderDetail, DealOrderDetailSaveVO,
    DealOrderDetailUpdateVO, DealOrderDetailPageQuery, DealOrderDetailResultVO> implements DealOrderDetailService {

    @Override
    public boolean save(DealOrderDetail detail) {
        return superManager.save(detail);
    }
}


