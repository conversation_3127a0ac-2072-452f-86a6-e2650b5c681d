package top.kx.kxss.system.manager.subscription.order;

import top.kx.basic.base.manager.SuperManager;
import top.kx.kxss.system.entity.subscription.order.SubscriptionOrderTemplateFeature;

/**
 * <p>
 * 通用业务接口
 * 订单订阅模版与功能权益
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-09 18:56:23
 * @create [2025-06-09 18:56:23] [dou] [代码生成器生成]
 */
public interface SubscriptionOrderTemplateFeatureManager extends SuperManager<SubscriptionOrderTemplateFeature> {

}


