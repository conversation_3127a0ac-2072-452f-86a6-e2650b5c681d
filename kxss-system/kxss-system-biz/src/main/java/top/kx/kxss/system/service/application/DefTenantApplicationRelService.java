package top.kx.kxss.system.service.application;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.model.entity.system.SysUser;
import top.kx.kxss.system.entity.application.DefTenantApplicationRel;
import top.kx.kxss.system.vo.query.application.DefTenantApplicationRelPageQuery;
import top.kx.kxss.system.vo.result.application.DefTenantApplicationRelResultVO;
import top.kx.kxss.system.vo.save.application.DefTenantApplicationRelSaveVO;
import top.kx.kxss.system.vo.update.application.DefTenantApplicationRelUpdateVO;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 业务接口
 * 租户的应用
 * </p>
 *
 * <AUTHOR>
 * @date 2021-09-15
 */
public interface DefTenantApplicationRelService extends SuperService<Long, DefTenantApplicationRel, DefTenantApplicationRelSaveVO, DefTenantApplicationRelUpdateVO, DefTenantApplicationRelPageQuery, DefTenantApplicationRelResultVO> {
    /**
     * 授权
     *
     * @param saveVO  saveVO
     * @param sysUser 系统用户信息
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2021/9/27 5:44 下午
     * @create [2021/9/27 5:44 下午 ] [tangyh] [初始创建]
     */
    Boolean grant(DefTenantApplicationRelSaveVO saveVO, SysUser sysUser);

    /**
     * 取消授权
     *
     * @param ids     ids
     * @param sysUser 系统用户信息
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2021/9/27 5:44 下午
     * @create [2021/9/27 5:44 下午 ] [tangyh] [初始创建]
     */
    Boolean cancel(List<Long> ids, SysUser sysUser);

    /**
     * 续期
     *
     * @param updateVO updateVO
     * @param sysUser  系统用户信息
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2021/9/27 5:44 下午
     * @create [2021/9/27 5:44 下午 ] [tangyh] [初始创建]
     */
    Boolean renewal(DefTenantApplicationRelUpdateVO updateVO, SysUser sysUser);

    /**
     * 查询授权信息
     *
     * @param id id
     * @return top.kx.kxss.tenant.vo.result.tenant.DefTenantApplicationRelResultVO
     * <AUTHOR>
     * @date 2021/9/29 10:48 下午
     * @create [2021/9/29 10:48 下午 ] [tangyh] [初始创建]
     */
    DefTenantApplicationRelResultVO getDetailById(Long id);

    void grantGeneralApplication(@NotNull(message = "ID不能为空") Long id);

    void deleteByTenantId(List<Long> ids);
}
