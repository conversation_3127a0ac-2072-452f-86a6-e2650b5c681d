package top.kx.kxss.system.service.scan;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.system.entity.scan.DefScan;
import top.kx.kxss.system.vo.save.scan.DefScanSaveVO;
import top.kx.kxss.system.vo.update.scan.DefScanUpdateVO;
import top.kx.kxss.system.vo.result.scan.DefScanResultVO;
import top.kx.kxss.system.vo.query.scan.DefScanPageQuery;


/**
 * <p>
 * 业务接口
 * 二维码生成
 * </p>
 *
 * <AUTHOR>
 * @date 2024-08-29 15:20:38
 * @create [2024-08-29 15:20:38] [dou] [代码生成器生成]
 */
public interface DefScanService extends SuperService<Long, DefScan, DefScanSaveVO,
    DefScanUpdateVO, DefScanPageQuery, DefScanResultVO> {

    boolean save(DefScan defScan);

    DefScan getOne(LbQueryWrap<DefScan> queryWrap);

    boolean updateById(DefScan defScan);
}


