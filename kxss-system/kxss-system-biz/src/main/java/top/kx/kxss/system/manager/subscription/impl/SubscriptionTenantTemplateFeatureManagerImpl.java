package top.kx.kxss.system.manager.subscription.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplateFeature;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.subscription.SubscriptionTenantTemplateFeatureManager;
import top.kx.kxss.system.mapper.subscription.SubscriptionTenantTemplateFeatureMapper;

/**
 * <p>
 * 通用业务实现类
 * 租户订阅模版与功能权益
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-21 10:23:54
 * @create [2025-05-21 10:23:54] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SubscriptionTenantTemplateFeatureManagerImpl extends SuperManagerImpl<SubscriptionTenantTemplateFeatureMapper, SubscriptionTenantTemplateFeature> implements SubscriptionTenantTemplateFeatureManager {

}


