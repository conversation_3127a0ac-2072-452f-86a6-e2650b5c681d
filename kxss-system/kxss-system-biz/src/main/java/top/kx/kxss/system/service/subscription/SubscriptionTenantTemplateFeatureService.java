package top.kx.kxss.system.service.subscription;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplateFeature;
import top.kx.kxss.system.vo.save.subscription.SubscriptionTenantTemplateFeatureSaveVO;
import top.kx.kxss.system.vo.update.subscription.SubscriptionTenantTemplateFeatureUpdateVO;
import top.kx.kxss.system.vo.result.subscription.SubscriptionTenantTemplateFeatureResultVO;
import top.kx.kxss.system.vo.query.subscription.SubscriptionTenantTemplateFeaturePageQuery;


/**
 * <p>
 * 业务接口
 * 租户订阅模版与功能权益
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-21 10:23:54
 * @create [2025-05-21 10:23:54] [dou] [代码生成器生成]
 */
public interface SubscriptionTenantTemplateFeatureService extends SuperService<Long, SubscriptionTenantTemplateFeature, SubscriptionTenantTemplateFeatureSaveVO,
    SubscriptionTenantTemplateFeatureUpdateVO, SubscriptionTenantTemplateFeaturePageQuery, SubscriptionTenantTemplateFeatureResultVO> {

    boolean remove(LbQueryWrap<SubscriptionTenantTemplateFeature> eq);

    SubscriptionTenantTemplateFeature getOne(LbQueryWrap<SubscriptionTenantTemplateFeature> eq);
}


