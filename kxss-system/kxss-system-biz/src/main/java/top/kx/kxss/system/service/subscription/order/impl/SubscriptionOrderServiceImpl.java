package top.kx.kxss.system.service.subscription.order.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baidu.fsg.uid.UidGenerator;
import com.baomidou.dynamic.datasource.annotation.DS;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.exception.BizException;
import top.kx.basic.exception.code.ExceptionCode;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.query.DealOrderPaymentQuery;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.pos.DealOrderStatusEnum;
import top.kx.kxss.model.enumeration.pos.RewardOrderStatusEnum;
import top.kx.kxss.model.enumeration.system.subscription.SubscriptionFeatureTypeEnum;
import top.kx.kxss.model.enumeration.system.subscription.SubscriptionStatusEnum;
import top.kx.kxss.system.entity.subscription.SubscriptionTemplate;
import top.kx.kxss.system.entity.subscription.SubscriptionTemplateFeature;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplate;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplateFeature;
import top.kx.kxss.system.entity.subscription.order.SubscriptionOrder;
import top.kx.kxss.system.entity.subscription.order.SubscriptionOrderTemplate;
import top.kx.kxss.system.entity.subscription.order.SubscriptionOrderTemplateFeature;
import top.kx.kxss.system.manager.subscription.order.SubscriptionOrderManager;
import top.kx.kxss.system.service.subscription.SubscriptionTemplateFeatureService;
import top.kx.kxss.system.service.subscription.SubscriptionTemplateService;
import top.kx.kxss.system.service.subscription.SubscriptionTenantTemplateFeatureService;
import top.kx.kxss.system.service.subscription.SubscriptionTenantTemplateService;
import top.kx.kxss.system.service.subscription.order.SubscriptionOrderService;
import top.kx.kxss.system.service.subscription.order.SubscriptionOrderTemplateFeatureService;
import top.kx.kxss.system.service.subscription.order.SubscriptionOrderTemplateService;
import top.kx.kxss.system.vo.query.UpdateCashPaymentQuery;
import top.kx.kxss.system.vo.query.UpdateRefundPaymentQuery;
import top.kx.kxss.system.vo.query.subscription.order.SubscriptionOrderPageQuery;
import top.kx.kxss.system.vo.result.subscription.order.SubscriptionOrderResultVO;
import top.kx.kxss.system.vo.save.subscription.order.SubscriptionOrderSaveVO;
import top.kx.kxss.system.vo.update.subscription.order.SubscriptionOrderUpdateVO;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 订单订阅模版
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-09 17:25:13
 * @create [2025-06-09 17:25:13] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class SubscriptionOrderServiceImpl extends SuperServiceImpl<SubscriptionOrderManager, Long, SubscriptionOrder, SubscriptionOrderSaveVO,
        SubscriptionOrderUpdateVO, SubscriptionOrderPageQuery, SubscriptionOrderResultVO> implements SubscriptionOrderService {


    private final SubscriptionOrderTemplateService orderTemplateService;
    private final SubscriptionOrderTemplateFeatureService orderTemplateFeatureService;
    private final SubscriptionTenantTemplateFeatureService tenantTemplateFeatureService;
    private final SubscriptionTenantTemplateService tenantTemplateService;
    private final SubscriptionTemplateService templateService;
    private final SubscriptionTemplateFeatureService templateFeatureService;
    private final DistributedLock distributedLock;
    private final UidGenerator uidGenerator;


    @Override
    public SubscriptionOrder saveOrder(SubscriptionOrderSaveVO model) {
        SubscriptionOrder subscriptionOrder = BeanUtil.copyProperties(model, SubscriptionOrder.class);
        subscriptionOrder.setId(uidGenerator.getUid());
        saveOrderTemplate(subscriptionOrder, model.getTmpList());
        superManager.save(subscriptionOrder);
        return subscriptionOrder;
    }

    @Override
    public SubscriptionOrder updateOrder(SubscriptionOrderUpdateVO model) {
        if (StrUtil.isBlank(model.getPlanName())) {
            List<SubscriptionTemplate> templateList = templateService.list(Wraps.<SubscriptionTemplate>lbQ()
                    .eq(SubscriptionTemplate::getDeleteFlag, 0)
                    .in(SubscriptionTemplate::getId, model.getTmpList())
            );
            if (CollUtil.isNotEmpty(templateList)) {
                model.setPlanName(templateList.stream().map(SubscriptionTemplate::getName).collect(Collectors.joining(",")));
            }
        }
        SubscriptionOrder subscriptionOrder = super.updateById(model);
        saveOrderTemplate(subscriptionOrder, model.getTmpList());
        return subscriptionOrder;
    }

    private void saveOrderTemplate(SubscriptionOrder subscriptionOrder, List<Long> tmpList) {
        List<SubscriptionTemplate> templateList = templateService.list(Wraps.<SubscriptionTemplate>lbQ()
                .eq(SubscriptionTemplate::getDeleteFlag, 0)
                .in(SubscriptionTemplate::getId, tmpList)
        );
        if (CollUtil.isEmpty(templateList)) {
            return;
        }
        subscriptionOrder.setPlanName(templateList.stream().map(SubscriptionTemplate::getName).collect(Collectors.joining(",")));
        orderTemplateService.remove(Wraps.<SubscriptionOrderTemplate>lbQ()
                .eq(SubscriptionOrderTemplate::getOrderId, subscriptionOrder.getId()));
        orderTemplateFeatureService.remove(Wraps.<SubscriptionOrderTemplateFeature>lbQ()
                .eq(SubscriptionOrderTemplateFeature::getOrderId, subscriptionOrder.getId()));
        List<SubscriptionOrderTemplate> orderTemplateList = templateList.stream().map(template -> {
            SubscriptionOrderTemplate orderTemplate = new SubscriptionOrderTemplate();
            orderTemplate.setTmpServiceType(template.getServiceType());
            orderTemplate.setTmpDays(template.getDays());
            orderTemplate.setOrderId(subscriptionOrder.getId());
            orderTemplate.setTmpId(template.getId());
            orderTemplate.setPrice(template.getPrice());
            orderTemplate.setTmpPrice(template.getPrice());
            orderTemplate.setAdminAssigned(true);
            orderTemplate.setTmpBillingType(template.getBillingType());
            orderTemplate.setTmpDescription(template.getDescription());
            orderTemplate.setTmpIsDefault(template.getIsDefault());
            orderTemplate.setTmpName(template.getName());
            orderTemplate.setTmpType(template.getType());
            return orderTemplate;
        }).collect(Collectors.toList());
        orderTemplateService.saveBatch(orderTemplateList);
        List<SubscriptionTemplateFeature> templateFeatureList = templateFeatureService.list(Wraps.<SubscriptionTemplateFeature>lbQ()
                .eq(SubscriptionTemplateFeature::getDeleteFlag, 0)
                .in(SubscriptionTemplateFeature::getTmpId, tmpList)
        );
        if (CollUtil.isEmpty(templateFeatureList)) {
            return;
        }
        Map<Long, SubscriptionOrderTemplate> orderTemplateMap = orderTemplateList.stream().collect(Collectors.toMap(SubscriptionOrderTemplate::getTmpId,
                k -> k));

        orderTemplateFeatureService.saveBatch(templateFeatureList.stream().map(templateFeature -> {
            SubscriptionOrderTemplateFeature orderTemplateFeature = BeanUtil.copyProperties(templateFeature,
                    SubscriptionOrderTemplateFeature.class);
            orderTemplateFeature.setOrderTmpId(orderTemplateMap.get(templateFeature.getTmpId()).getId());
            orderTemplateFeature.setOrderId(subscriptionOrder.getId());
            orderTemplateFeature.setFeatureIsLimitCount(templateFeature.getIsLimitCount());
            orderTemplateFeature.setFeatureLimitCount(templateFeature.getLimitCount());
            orderTemplateFeature.setId(null);
            orderTemplateFeature.setCreatedTime(null);
            orderTemplateFeature.setUpdatedTime(null);
            return orderTemplateFeature;
        }).collect(Collectors.toList()));

    }

    @Override
    public SubscriptionOrder creatOrder(
            SubscriptionOrder subscriptionOrder, SubscriptionTenantTemplate tenantTemplate) {
        subscriptionOrder.setExpireTime(LocalDateTime.now().plusMinutes(10));
        subscriptionOrder.setOrgId(tenantTemplate.getOrgId());
        subscriptionOrder.setTenantId(tenantTemplate.getTenantId());
        subscriptionOrder.setPlanName("模板续费");
        subscriptionOrder.setStatus(RewardOrderStatusEnum.NO_PAY.getCode());
        boolean save = superManager.save(subscriptionOrder);
        ArgumentAssert.isFalse(!save, "创建订单失败");
        SubscriptionOrderTemplate orderTemplate = BeanUtil.copyProperties(tenantTemplate, SubscriptionOrderTemplate.class);
        orderTemplate.setId(null);
        orderTemplate.setOrderId(subscriptionOrder.getId());
        orderTemplate.setPrice(tenantTemplate.getPrice());
        orderTemplate.setRemark(tenantTemplate.getRemark());
        orderTemplate.setCreatedTime(LocalDateTime.now());
        orderTemplate.setUpdatedTime(LocalDateTime.now());
        orderTemplateService.save(orderTemplate);
        List<SubscriptionTenantTemplateFeature> tenantTemplateFeatureList = tenantTemplateFeatureService.list(Wraps.<SubscriptionTenantTemplateFeature>lbQ()
                .eq(SubscriptionTenantTemplateFeature::getTmpId, tenantTemplate.getId())
                .eq(SubscriptionTenantTemplateFeature::getDeleteFlag, 0));
        if (CollUtil.isNotEmpty(tenantTemplateFeatureList)) {
            List<SubscriptionOrderTemplateFeature> templateFeatureList = BeanUtil.copyToList(tenantTemplateFeatureList, SubscriptionOrderTemplateFeature.class);
            templateFeatureList.forEach(item -> {
                item.setId(null);
                item.setOrderId(subscriptionOrder.getId());
                item.setTmpId(orderTemplate.getTmpId());
                item.setOrderTmpId(orderTemplate.getId());
                item.setCreatedTime(LocalDateTime.now());
                item.setUpdatedTime(LocalDateTime.now());
            });
            orderTemplateFeatureService.saveBatch(templateFeatureList);
        }
        return subscriptionOrder;
    }

    @Override
    public boolean updateById(SubscriptionOrder subscriptionOrder) {
        return superManager.updateById(subscriptionOrder);
    }


    @Override
    public Boolean updatePayment(UpdateCashPaymentQuery query) {
        SubscriptionOrderUpdateVO build = SubscriptionOrderUpdateVO.builder()
                .status(query.getStatus())
                .build();
        build.setId(query.getId());
        super.updateById(build);
        return true;
    }

    @Override
    public Boolean updateRefundPayment(UpdateRefundPaymentQuery query) {
        return false;
    }

    @Override
    public SubscriptionOrder getByOrderId(Long orderId) {
        return superManager.getById(orderId);
    }

    @Override
    @GlobalTransactional
    public Boolean paySuccess(DealOrderPaymentQuery query) {
        log.info("套餐支付成功：{}", JSON.toJSONString(query));
        boolean lock = false;
        try {
            lock = distributedLock.lock(query.getOrderId() + "_SUBSCRIPTION_ORDER", 2);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            Long orderId = query.getOrderId();
            SubscriptionOrder subscriptionOrder = superManager.getById(orderId);
            ArgumentAssert.notNull(subscriptionOrder, "订单不存在！");
            ArgumentAssert.isFalse(subscriptionOrder.getStatus().equals(DealOrderStatusEnum.COMPLETE.getCode()), "订单已完成！");
            ArgumentAssert.isFalse(!subscriptionOrder.getStatus().equals(DealOrderStatusEnum.NO_PAY.getCode())
                    && !subscriptionOrder.getStatus().equals(DealOrderStatusEnum.PART_PAY.getCode()), "当前状态订单无法操作！");
            subscriptionOrder.setStatus(DealOrderStatusEnum.COMPLETE.getCode());
            subscriptionOrder.setCompleteTime(LocalDateTime.now());
            grantTenant(subscriptionOrder);
            return superManager.updateById(subscriptionOrder);
        } finally {
            if (lock) {
                distributedLock.releaseLock(query.getOrderId() + "_SUBSCRIPTION_ORDER");
            }
        }
    }

    private void grantTenant(SubscriptionOrder subscriptionOrder) {
        List<SubscriptionOrderTemplate> orderTemplateList = orderTemplateService.list(Wraps.<SubscriptionOrderTemplate>lbQ()
                .eq(SubscriptionOrderTemplate::getOrderId, subscriptionOrder.getId())
                .eq(SubscriptionOrderTemplate::getDeleteFlag, 0)
        );
        if (CollUtil.isEmpty(orderTemplateList)) {
            return;
        }
        Map<String, List<SubscriptionOrderTemplate>> serviceTypeMap = orderTemplateList.stream().collect(Collectors.groupingBy(SubscriptionOrderTemplate::getTmpServiceType));
        for (String serviceType : serviceTypeMap.keySet()) {
            if (serviceType.equals(SubscriptionFeatureTypeEnum.TEMPLATE.getCode())) {
                grantTenantTemplate(serviceTypeMap.get(serviceType), subscriptionOrder);
                continue;
            }
            if (serviceType.equals(SubscriptionFeatureTypeEnum.VALUE_ADDED.getCode())) {
                grantValueAddedTemplate(serviceTypeMap.get(serviceType), subscriptionOrder);
            }
        }
        List<SubscriptionOrderTemplateFeature> templateFeatureList = orderTemplateFeatureService.list(Wraps.<SubscriptionOrderTemplateFeature>lbQ()
                .eq(SubscriptionOrderTemplateFeature::getOrderId, subscriptionOrder.getId())
                .eq(SubscriptionOrderTemplateFeature::getDeleteFlag, 0)
        );
        tenantTemplateFeatureService.saveBatch(templateFeatureList.stream().map(v -> {
            SubscriptionTenantTemplateFeature feature = BeanUtil.copyProperties(v, SubscriptionTenantTemplateFeature.class);
            feature.setOrderId(subscriptionOrder.getId());
            feature.setTenantId(subscriptionOrder.getTenantId());
            feature.setOrgId(subscriptionOrder.getOrgId());
            feature.setOrgName(subscriptionOrder.getOrgName());
            feature.setCreatedTime(LocalDateTime.now());
            feature.setUpdatedTime(LocalDateTime.now());
            return feature;
        }).collect(Collectors.toList()));
    }

    private void grantValueAddedTemplate(List<SubscriptionOrderTemplate> subscriptionOrderTemplates, SubscriptionOrder subscriptionOrder) {
        Map<Long, List<SubscriptionTenantTemplate>> tenantTemplateMap = tenantTemplateService.list(Wraps.<SubscriptionTenantTemplate>lbQ()
                .eq(SubscriptionTenantTemplate::getTenantId, ContextUtil.getTenantId())
                .eq(SubscriptionTenantTemplate::getOrgId, ContextUtil.getCurrentCompanyId())
                .eq(SubscriptionTenantTemplate::getTmpServiceType, SubscriptionFeatureTypeEnum.VALUE_ADDED.getCode())
                .eq(SubscriptionTenantTemplate::getStatus, SubscriptionStatusEnum.ACTIVE.getCode())
                .eq(SubscriptionTenantTemplate::getDeleteFlag, 0)
        ).stream().collect(Collectors.groupingBy(SubscriptionTenantTemplate::getTmpId));
        List<SubscriptionTenantTemplate> tenantTemplateList = new ArrayList<>();
        for (SubscriptionOrderTemplate subscriptionOrderTemplate : subscriptionOrderTemplates) {
            SubscriptionTenantTemplate tenantTemplate = BeanUtil.copyProperties(subscriptionOrderTemplate, SubscriptionTenantTemplate.class);
            tenantTemplate.setStatus(SubscriptionStatusEnum.NO_ACTIVE.getCode());
            tenantTemplate.setStartTime(LocalDateTime.now().withNano(0));
            tenantTemplate.setOrderId(subscriptionOrder.getId());
            tenantTemplate.setOrderCode(subscriptionOrder.getCode());
            tenantTemplate.setTenantId(subscriptionOrder.getTenantId());
            tenantTemplate.setOrgId(subscriptionOrder.getOrgId());
            tenantTemplate.setPrice(subscriptionOrder.getPrice());
            tenantTemplate.setActualPrice(subscriptionOrder.getActualPrice());
            tenantTemplate.setOrgName(subscriptionOrder.getOrgName());
            if (tenantTemplate.getNextActualPrice() == null) {
                tenantTemplate.setNextActualPrice(tenantTemplate.getPrice());
            }
            if (tenantTemplate.getActualPrice() == null) {
                tenantTemplate.setActualPrice(tenantTemplate.getPrice());
            }
            tenantTemplate.setDiscountPrice(tenantTemplate.getPrice().subtract(tenantTemplate.getActualPrice()));
            if (CollUtil.isEmpty(tenantTemplateMap)
                    || !tenantTemplateMap.containsKey(tenantTemplate.getTmpId())) {
                tenantTemplate.setStatus(SubscriptionStatusEnum.ACTIVE.getCode());
                tenantTemplate.setExpirationTime(tenantTemplateService.getExpirationTime(tenantTemplate.getStartTime(),
                        tenantTemplate.getTmpBillingType(), subscriptionOrderTemplate.getTmpDays()));
            }
            tenantTemplate.setCreatedTime(LocalDateTime.now());
            tenantTemplate.setUpdatedTime(LocalDateTime.now());
            tenantTemplateList.add(tenantTemplate);
        }
        tenantTemplateService.saveBatch(tenantTemplateList);
    }

    private void grantTenantTemplate(List<SubscriptionOrderTemplate> subscriptionOrderTemplates
            , SubscriptionOrder subscriptionOrder) {
        SubscriptionTenantTemplate subscriptionTemplate = tenantTemplateService.getOne(Wraps.<SubscriptionTenantTemplate>lbQ()
                .eq(SubscriptionTenantTemplate::getTenantId, ContextUtil.getTenantId())
                .eq(SubscriptionTenantTemplate::getOrgId, ContextUtil.getCurrentCompanyId())
                .eq(SubscriptionTenantTemplate::getTmpServiceType, SubscriptionFeatureTypeEnum.TEMPLATE.getCode())
                .eq(SubscriptionTenantTemplate::getStatus, SubscriptionStatusEnum.ACTIVE.getCode())
                .eq(SubscriptionTenantTemplate::getDeleteFlag, 0)
                .last("limit 1"));
        subscriptionOrderTemplates.sort(Comparator.comparing(SubscriptionOrderTemplate::getTmpType));
        List<SubscriptionTenantTemplate> tenantTemplateList = new ArrayList<>();
        boolean isUse = false;
        for (SubscriptionOrderTemplate subscriptionOrderTemplate : subscriptionOrderTemplates) {
            SubscriptionTenantTemplate tenantTemplate = BeanUtil.copyProperties(subscriptionOrderTemplate, SubscriptionTenantTemplate.class);
            tenantTemplate.setStatus(SubscriptionStatusEnum.NO_ACTIVE.getCode());
            tenantTemplate.setStartTime(LocalDateTime.now().withNano(0));
            tenantTemplate.setOrderId(subscriptionOrder.getId());
            tenantTemplate.setOrderCode(subscriptionOrder.getCode());
            tenantTemplate.setTenantId(subscriptionOrder.getTenantId());
            tenantTemplate.setOrgId(subscriptionOrder.getOrgId());
            tenantTemplate.setPrice(subscriptionOrder.getPrice());
            tenantTemplate.setActualPrice(subscriptionOrder.getActualPrice());
            tenantTemplate.setOrgName(subscriptionOrder.getOrgName());
            if (tenantTemplate.getNextActualPrice() == null) {
                tenantTemplate.setNextActualPrice(tenantTemplate.getPrice());
            }
            if (tenantTemplate.getActualPrice() == null) {
                tenantTemplate.setActualPrice(tenantTemplate.getPrice());
            }
            tenantTemplate.setDiscountPrice(tenantTemplate.getPrice().subtract(tenantTemplate.getActualPrice()));
            if (!isUse && subscriptionTemplate == null) {
                tenantTemplate.setStatus(SubscriptionStatusEnum.ACTIVE.getCode());
                tenantTemplate.setExpirationTime(tenantTemplateService.getExpirationTime(tenantTemplate.getStartTime(),
                        tenantTemplate.getTmpBillingType(), subscriptionOrderTemplate.getTmpDays()));
            }
            tenantTemplate.setCreatedTime(LocalDateTime.now());
            tenantTemplate.setUpdatedTime(LocalDateTime.now());
            tenantTemplateList.add(tenantTemplate);
        }
        tenantTemplateService.saveBatch(tenantTemplateList);
    }


}


