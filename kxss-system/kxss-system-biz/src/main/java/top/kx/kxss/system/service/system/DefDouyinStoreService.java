package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.system.DefDouyinStore;
import top.kx.kxss.system.vo.save.system.DefDouyinStoreSaveVO;
import top.kx.kxss.system.vo.update.system.DefDouyinStoreUpdateVO;
import top.kx.kxss.system.vo.result.system.DefDouyinStoreResultVO;
import top.kx.kxss.system.vo.query.system.DefDouyinStorePageQuery;


/**
 * <p>
 * 业务接口
 * 抖音授权门店
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-07 13:54:39
 * @create [2024-04-07 13:54:39] [yan] [代码生成器生成]
 */
public interface DefDouyinStoreService extends SuperService<Long, DefDouyinStore, DefDouyinStoreSaveVO,
    DefDouyinStoreUpdateVO, DefDouyinStorePageQuery, DefDouyinStoreResultVO> {

}


