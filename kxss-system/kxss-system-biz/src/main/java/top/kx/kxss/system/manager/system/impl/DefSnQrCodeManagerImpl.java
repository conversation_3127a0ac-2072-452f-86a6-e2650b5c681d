package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DefSnQrCode;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DefSnQrCodeManager;
import top.kx.kxss.system.mapper.system.DefSnQrCodeMapper;

/**
 * <p>
 * 通用业务实现类
 * 二维码生成
 * </p>
 *
 * <AUTHOR>
 * @date 2024-06-04 11:22:59
 * @create [2024-06-04 11:22:59] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefSnQrCodeManagerImpl extends SuperManagerImpl<DefSnQrCodeMapper, DefSnQrCode> implements DefSnQrCodeManager {

}


