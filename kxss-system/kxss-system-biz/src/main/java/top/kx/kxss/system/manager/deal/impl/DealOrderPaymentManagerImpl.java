package top.kx.kxss.system.manager.deal.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.deal.DealOrderPayment;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.deal.DealOrderPaymentManager;
import top.kx.kxss.system.mapper.deal.DealOrderPaymentMapper;

/**
 * <p>
 * 通用业务实现类
 * 团购充值收款记录
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-24 14:08:50
 * @create [2024-10-24 14:08:50] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DealOrderPaymentManagerImpl extends SuperManagerImpl<DealOrderPaymentMapper, DealOrderPayment> implements DealOrderPaymentManager {

}


