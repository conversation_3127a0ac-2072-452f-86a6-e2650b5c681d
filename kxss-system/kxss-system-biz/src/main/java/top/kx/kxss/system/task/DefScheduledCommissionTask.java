package top.kx.kxss.system.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.model.enumeration.system.DefTaskModuleEnum;
import top.kx.kxss.system.service.TaskService;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class DefScheduledCommissionTask {

    private final static String LOCK_KEY = "def_task_commission";

    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private TaskService taskService;

    //@Scheduled(cron = "0 0/1 * * * ?")
    public void start() {
        boolean lock = false;
        try {
            lock = distributedLock.lock(LOCK_KEY, 0);
            if (!lock) {
                return;
            }
            log.info("处理{}定时任务开始", DefTaskModuleEnum.COMMISSION.getDesc());
            taskService.task(DefTaskModuleEnum.COMMISSION.getCode(), 1);
        } finally {
            ContextUtil.remove();
            if (lock) {
                distributedLock.releaseLock(LOCK_KEY);
            }
        }
    }


}
