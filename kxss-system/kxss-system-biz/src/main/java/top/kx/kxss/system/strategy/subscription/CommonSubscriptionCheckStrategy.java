package top.kx.kxss.system.strategy.subscription;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplate;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplateFeature;

import java.util.List;

/**
 * 套餐开台策略
 *
 * <AUTHOR>
 * @date 2025/5/14 15:11
 */
@RequiredArgsConstructor
@DS(DsConstant.DEFAULTS)
@Service
@Slf4j
public class CommonSubscriptionCheckStrategy extends AbstractSubscriptionCheckService {

    @Override
    public boolean checkPermission(List<SubscriptionTenantTemplateFeature> featureList, JSONObject params, String featureCode
            , SubscriptionTenantTemplate tenantTemplate) {
        boolean b = checkFeatureCode(featureList, featureCode);
        if (b) {
            return checkExpire(tenantTemplate);
        }
        return false;
    }
}
