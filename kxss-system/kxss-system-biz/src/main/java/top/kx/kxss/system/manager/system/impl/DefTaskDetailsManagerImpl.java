package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DefTaskDetails;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DefTaskDetailsManager;
import top.kx.kxss.system.mapper.system.DefTaskDetailsMapper;

/**
 * <p>
 * 通用业务实现类
 * 任务详情
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-12 17:42:51
 * @create [2024-12-12 17:42:51] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefTaskDetailsManagerImpl extends SuperManagerImpl<DefTaskDetailsMapper, DefTaskDetails> implements DefTaskDetailsManager {

}


