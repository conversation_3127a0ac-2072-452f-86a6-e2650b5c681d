package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DefWxUser;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DefWxUserManager;
import top.kx.kxss.system.mapper.system.DefWxUserMapper;

/**
 * <p>
 * 通用业务实现类
 * 微信用户
 * </p>
 *
 * <AUTHOR>
 * @date 2023-12-11 11:40:01
 * @create [2023-12-11 11:40:01] [yh] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefWxUserManagerImpl extends SuperManagerImpl<DefWxUserMapper, DefWxUser> implements DefWxUserManager {

}


