package top.kx.kxss.system.service.system.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.system.DefSunmiTransaction;
import top.kx.kxss.system.manager.system.DefSunmiTransactionManager;
import top.kx.kxss.system.service.system.DefSunmiTransactionService;
import top.kx.kxss.system.vo.query.system.DefSunmiTransactionPageQuery;
import top.kx.kxss.system.vo.result.system.DefSunmiTransactionResultVO;
import top.kx.kxss.system.vo.save.system.DefSunmiTransactionSaveVO;
import top.kx.kxss.system.vo.update.system.DefSunmiTransactionUpdateVO;

import java.time.LocalDateTime;

/**
 * <p>
 * 业务实现类
 * 商米流水记录
 * </p>
 *
 * <AUTHOR>
 * @date 2023-10-14 14:50:41
 * @create [2023-10-14 14:50:41] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class DefSunmiTransactionServiceImpl extends SuperServiceImpl<DefSunmiTransactionManager, Long, DefSunmiTransaction, DefSunmiTransactionSaveVO,
    DefSunmiTransactionUpdateVO, DefSunmiTransactionPageQuery, DefSunmiTransactionResultVO> implements DefSunmiTransactionService {


    @Override
    public boolean update(LbUpdateWrap<DefSunmiTransaction> eq) {
       return superManager.update(eq);
    }

    @Override
    public boolean save(DefSunmiTransaction sunmiTransaction) {
        return superManager.save(sunmiTransaction);
    }

    @Override
    public DefSunmiTransaction getOne(LbQueryWrap<DefSunmiTransaction> last) {
        return superManager.getOne(last);
    }

    @Override
    public boolean update(DefSunmiTransaction sunmiTransaction) {
        sunmiTransaction.setUpdatedTime(LocalDateTime.now());
        return superManager.updateById(sunmiTransaction);
    }
}


