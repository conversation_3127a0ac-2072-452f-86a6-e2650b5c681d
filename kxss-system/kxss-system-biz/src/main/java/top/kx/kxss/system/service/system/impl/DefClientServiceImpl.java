package top.kx.kxss.system.service.system.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.RandomUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperCacheServiceImpl;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.system.DefClient;
import top.kx.kxss.system.manager.system.DefClientManager;
import top.kx.kxss.system.service.system.DefClientService;
import top.kx.kxss.system.vo.query.system.DefClientPageQuery;
import top.kx.kxss.system.vo.result.system.DefClientResultVO;
import top.kx.kxss.system.vo.save.system.DefClientSaveVO;
import top.kx.kxss.system.vo.update.system.DefClientUpdateVO;

import java.io.Serializable;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 客户端
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
@DS(DsConstant.DEFAULTS)
public class DefClientServiceImpl extends SuperCacheServiceImpl<DefClientManager, Long, DefClient, DefClientSaveVO, DefClientUpdateVO, DefClientPageQuery, DefClientResultVO>
        implements DefClientService {

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        return superManager.findByIds(ids.stream().map(Convert::toLong).collect(Collectors.toSet()));
    }

    @Override
    protected DefClient saveBefore(DefClientSaveVO defClientSaveVO) {
        DefClient defClient = super.saveBefore(defClientSaveVO);
        defClient.setClientId(RandomUtil.randomString(24));
        defClient.setClientSecret(RandomUtil.randomString(32));
        return defClient;
    }

    @Override
    public DefClient getClient(String clientId, String clientSecret) {
        return superManager.getClient(clientId, clientSecret);
    }

    @Override
    public DefClient getById(LbQueryWrap<DefClient> eq) {
        return superManager.getOne(eq);
    }

    @Override
    public DefClient getOne(LbQueryWrap<DefClient> eq) {
        return superManager.getOne(eq);
    }
}
