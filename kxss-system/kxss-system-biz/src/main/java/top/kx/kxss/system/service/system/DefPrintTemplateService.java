package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.system.DefPrintTemplate;
import top.kx.kxss.system.vo.save.system.DefPrintTemplateSaveVO;
import top.kx.kxss.system.vo.update.system.DefPrintTemplateUpdateVO;
import top.kx.kxss.system.vo.result.system.DefPrintTemplateResultVO;
import top.kx.kxss.system.vo.query.system.DefPrintTemplatePageQuery;


/**
 * <p>
 * 业务接口
 * 打印模板
 * </p>
 *
 * <AUTHOR>
 * @date 2023-12-08 12:25:11
 * @create [2023-12-08 12:25:11] [yh] [代码生成器生成]
 */
public interface DefPrintTemplateService extends SuperService<Long, DefPrintTemplate, DefPrintTemplateSaveVO,
    DefPrintTemplateUpdateVO, DefPrintTemplatePageQuery, DefPrintTemplateResultVO> {

}


