package top.kx.kxss.system.service.subscription.order;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.app.query.DealOrderPaymentQuery;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplate;
import top.kx.kxss.system.entity.subscription.order.SubscriptionOrder;
import top.kx.kxss.system.vo.query.UpdateCashPaymentQuery;
import top.kx.kxss.system.vo.query.UpdateRefundPaymentQuery;
import top.kx.kxss.system.vo.query.subscription.order.SubscriptionOrderPageQuery;
import top.kx.kxss.system.vo.result.subscription.order.SubscriptionOrderResultVO;
import top.kx.kxss.system.vo.save.subscription.order.SubscriptionOrderSaveVO;
import top.kx.kxss.system.vo.update.subscription.order.SubscriptionOrderUpdateVO;


/**
 * <p>
 * 业务接口
 * 订单订阅模版
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-09 17:25:13
 * @create [2025-06-09 17:25:13] [dou] [代码生成器生成]
 */
public interface SubscriptionOrderService extends SuperService<Long, SubscriptionOrder, SubscriptionOrderSaveVO,
        SubscriptionOrderUpdateVO, SubscriptionOrderPageQuery, SubscriptionOrderResultVO> {

    SubscriptionOrder creatOrder(SubscriptionOrder subscriptionOrder, SubscriptionTenantTemplate tenantTemplate);

    boolean updateById(SubscriptionOrder subscriptionOrder);

    Boolean updatePayment(UpdateCashPaymentQuery query);

    Boolean updateRefundPayment(UpdateRefundPaymentQuery query);

    SubscriptionOrder getByOrderId(Long orderId);

    Boolean paySuccess(DealOrderPaymentQuery query);

    SubscriptionOrder saveOrder(SubscriptionOrderSaveVO model);

    SubscriptionOrder updateOrder(SubscriptionOrderUpdateVO model);

}


