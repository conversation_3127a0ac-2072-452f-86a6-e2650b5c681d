package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.system.DefLoginLog;
import top.kx.kxss.system.vo.query.system.DefLoginLogPageQuery;
import top.kx.kxss.system.vo.result.system.DefLoginLogResultVO;
import top.kx.kxss.system.vo.save.system.DefLoginLogSaveVO;
import top.kx.kxss.system.vo.update.system.DefLoginLogUpdateVO;

import java.time.LocalDateTime;

/**
 * <p>
 * 业务接口
 * 登录日志
 * </p>
 *
 * <AUTHOR>
 * @date 2021-11-12
 */
public interface DefLoginLogService extends SuperService<Long, DefLoginLog, DefLoginLogSaveVO, DefLoginLogUpdateVO, DefLoginLogPageQuery, DefLoginLogResultVO> {
    /**
     * 清理日志
     *
     * @param clearBeforeTime 多久之前的
     * @param clearBeforeNum  多少条
     * @return 是否成功
     */
    boolean clearLog(LocalDateTime clearBeforeTime, Integer clearBeforeNum);
}
