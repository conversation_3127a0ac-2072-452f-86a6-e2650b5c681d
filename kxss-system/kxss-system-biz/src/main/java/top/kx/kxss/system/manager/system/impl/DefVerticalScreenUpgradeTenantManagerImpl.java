package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DefVerticalScreenUpgradeTenant;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DefVerticalScreenUpgradeTenantManager;
import top.kx.kxss.system.mapper.system.DefVerticalScreenUpgradeTenantMapper;

/**
 * <p>
 * 通用业务实现类
 * 智慧屏可更新商户
 * </p>
 *
 * <AUTHOR>
 * @date 2025-08-20 16:03:24
 * @create [2025-08-20 16:03:24] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefVerticalScreenUpgradeTenantManagerImpl extends SuperManagerImpl<DefVerticalScreenUpgradeTenantMapper, DefVerticalScreenUpgradeTenant> implements DefVerticalScreenUpgradeTenantManager {

}


