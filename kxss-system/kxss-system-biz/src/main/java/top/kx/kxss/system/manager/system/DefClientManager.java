package top.kx.kxss.system.manager.system;

import top.kx.basic.base.manager.SuperCacheManager;
import top.kx.basic.interfaces.echo.LoadService;
import top.kx.kxss.system.entity.system.DefClient;

/**
 * <p>
 * 通用业务接口
 * 客户端
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-13
 */
public interface DefClientManager extends SuperCacheManager<DefClient>, LoadService {
    /**
     * 根据 客户端id 和 客户端秘钥查询应用
     *
     * @param clientId
     * @param clientSecret
     * @return
     */
    DefClient getClient(String clientId, String clientSecret);
}
