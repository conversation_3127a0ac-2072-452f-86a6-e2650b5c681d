package top.kx.kxss.system.service.template.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.cache.repository.CacheOps;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.RedisConstant;
import top.kx.kxss.system.entity.template.DefQueryTemplate;
import top.kx.kxss.system.service.template.DefQueryWhereService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.system.manager.template.DefQueryWhereManager;
import top.kx.kxss.system.entity.template.DefQueryWhere;
import top.kx.kxss.system.vo.save.template.DefQueryWhereSaveVO;
import top.kx.kxss.system.vo.update.template.DefQueryTemplateUpdateVO;
import top.kx.kxss.system.vo.update.template.DefQueryWhereUpdateVO;
import top.kx.kxss.system.vo.result.template.DefQueryWhereResultVO;
import top.kx.kxss.system.vo.query.template.DefQueryWherePageQuery;

/**
 * <p>
 * 业务实现类
 * 查询条件
 * </p>
 *
 * <AUTHOR>
 * @date 2024-01-06 17:20:29
 * @create [2024-01-06 17:20:29] [yh] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class DefQueryWhereServiceImpl extends SuperServiceImpl<DefQueryWhereManager, Long, DefQueryWhere, DefQueryWhereSaveVO,
    DefQueryWhereUpdateVO, DefQueryWherePageQuery, DefQueryWhereResultVO> implements DefQueryWhereService {
    @Autowired
    protected CacheOps cacheOps;
    protected DefQueryWhere updateBefore(DefQueryWhereUpdateVO updateVO) {
        //更新清除cache
        cacheOps.del(RedisConstant.TEMP_CONDITION +updateVO.getQueryTemplateId().toString());
        return super.updateBefore(updateVO);
    }

}


