package top.kx.kxss.system.manager.clear;

import top.kx.basic.base.manager.SuperManager;
import top.kx.kxss.system.entity.clear.DefTenantOrgClear;

import java.time.LocalDateTime;

/**
 * <p>
 * 通用业务接口
 * 数据清空记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-20 17:43:48
 * @create [2025-06-20 17:43:48] [yan] [代码生成器生成]
 */
public interface DefTenantOrgClearManager extends SuperManager<DefTenantOrgClear> {

    /**
     * 复制表和数据
     * @param oldTable
     * @param newTable
     */
    void copyTableWithData(String oldTable, String newTable, Long tenantId, Long orgId);

    /**
     * 删除表数据
     * @param tableName 表名称
     * @param startTime 开始时间
     * @param endTime 结束时间
     */
    void markDataDeletedByTimeRange(Long tenantId, Long orgId, String tableName, LocalDateTime startTime, LocalDateTime endTime);


}


