package top.kx.kxss.system.service.application.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.application.DefTenantApplicationRecord;
import top.kx.kxss.system.manager.application.DefTenantApplicationRecordManager;
import top.kx.kxss.system.service.application.DefTenantApplicationRecordService;
import top.kx.kxss.system.vo.query.application.DefTenantApplicationRecordPageQuery;
import top.kx.kxss.system.vo.result.application.DefTenantApplicationRecordResultVO;
import top.kx.kxss.system.vo.save.application.DefTenantApplicationRecordSaveVO;
import top.kx.kxss.system.vo.update.application.DefTenantApplicationRecordUpdateVO;

/**
 * <p>
 * 业务实现类
 * 租户应用授权记录
 * </p>
 *
 * <AUTHOR>
 * @date 2021-09-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DS(DsConstant.DEFAULTS)
public class DefTenantApplicationRecordServiceImpl extends SuperServiceImpl<DefTenantApplicationRecordManager, Long, DefTenantApplicationRecord, DefTenantApplicationRecordSaveVO, DefTenantApplicationRecordUpdateVO, DefTenantApplicationRecordPageQuery, DefTenantApplicationRecordResultVO>
        implements DefTenantApplicationRecordService {

    @Override
    public boolean save(DefTenantApplicationRecord record) {
        return superManager.save(record);
    }
}
