package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DefHoliday;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DefHolidayManager;
import top.kx.kxss.system.mapper.system.DefHolidayMapper;

/**
 * <p>
 * 通用业务实现类
 * 节假日日期
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-10 18:24:54
 * @create [2024-04-10 18:24:54] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefHolidayManagerImpl extends SuperManagerImpl<DefHolidayMapper, DefHoliday> implements DefHolidayManager {

}


