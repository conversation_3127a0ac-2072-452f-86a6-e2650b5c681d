package top.kx.kxss.system.manager.tenant;

import top.kx.basic.base.manager.SuperCacheManager;
import top.kx.kxss.system.entity.tenant.DefUserTenantRel;
import top.kx.kxss.system.vo.result.tenant.DefUserTenantRelResultVO;

import java.util.List;

/**
 * <p>
 * 通用业务接口
 * 员工
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-27
 */
public interface DefUserTenantRelManager extends SuperCacheManager<DefUserTenantRel> {
    /**
     * 根据用户id查询员工
     *
     * @param userId 用户id
     * @return
     */
    List<DefUserTenantRelResultVO> listEmployeeByUserId(Long userId);
}
