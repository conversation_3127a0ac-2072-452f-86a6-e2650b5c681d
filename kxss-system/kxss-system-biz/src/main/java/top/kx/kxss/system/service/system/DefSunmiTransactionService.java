package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.system.entity.system.DefSunmiTransaction;
import top.kx.kxss.system.vo.save.system.DefSunmiTransactionSaveVO;
import top.kx.kxss.system.vo.update.system.DefSunmiTransactionUpdateVO;
import top.kx.kxss.system.vo.result.system.DefSunmiTransactionResultVO;
import top.kx.kxss.system.vo.query.system.DefSunmiTransactionPageQuery;


/**
 * <p>
 * 业务接口
 * 商米流水记录
 * </p>
 *
 * <AUTHOR>
 * @date 2023-10-14 14:50:41
 * @create [2023-10-14 14:50:41] [dou] [代码生成器生成]
 */
public interface DefSunmiTransactionService extends SuperService<Long, DefSunmiTransaction, DefSunmiTransactionSaveVO,
    DefSunmiTransactionUpdateVO, DefSunmiTransactionPageQuery, DefSunmiTransactionResultVO> {

    boolean update(LbUpdateWrap<DefSunmiTransaction> eq);

    boolean save(DefSunmiTransaction sunmiTransaction);

    DefSunmiTransaction getOne(LbQueryWrap<DefSunmiTransaction> last);

    boolean update(DefSunmiTransaction sunmiTransaction);
}


