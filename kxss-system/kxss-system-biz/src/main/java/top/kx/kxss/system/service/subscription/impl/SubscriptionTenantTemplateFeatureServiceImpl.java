package top.kx.kxss.system.service.subscription.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplateFeature;
import top.kx.kxss.system.manager.subscription.SubscriptionTenantTemplateFeatureManager;
import top.kx.kxss.system.service.subscription.SubscriptionTenantTemplateFeatureService;
import top.kx.kxss.system.vo.query.subscription.SubscriptionTenantTemplateFeaturePageQuery;
import top.kx.kxss.system.vo.result.subscription.SubscriptionTenantTemplateFeatureResultVO;
import top.kx.kxss.system.vo.save.subscription.SubscriptionTenantTemplateFeatureSaveVO;
import top.kx.kxss.system.vo.update.subscription.SubscriptionTenantTemplateFeatureUpdateVO;

/**
 * <p>
 * 业务实现类
 * 租户订阅模版与功能权益
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-21 10:23:54
 * @create [2025-05-21 10:23:54] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class SubscriptionTenantTemplateFeatureServiceImpl extends SuperServiceImpl<SubscriptionTenantTemplateFeatureManager, Long, SubscriptionTenantTemplateFeature, SubscriptionTenantTemplateFeatureSaveVO,
        SubscriptionTenantTemplateFeatureUpdateVO, SubscriptionTenantTemplateFeaturePageQuery, SubscriptionTenantTemplateFeatureResultVO> implements SubscriptionTenantTemplateFeatureService {


    @Override
    public boolean remove(LbQueryWrap<SubscriptionTenantTemplateFeature> eq) {
        return superManager.remove(eq);
    }

    @Override
    public SubscriptionTenantTemplateFeature getOne(LbQueryWrap<SubscriptionTenantTemplateFeature> eq) {
        return superManager.getOne(eq);
    }
}


