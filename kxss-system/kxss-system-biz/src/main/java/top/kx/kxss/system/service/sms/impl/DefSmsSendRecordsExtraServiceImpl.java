package top.kx.kxss.system.service.sms.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.service.sms.DefSmsSendRecordsExtraService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.system.manager.sms.DefSmsSendRecordsExtraManager;
import top.kx.kxss.system.entity.sms.DefSmsSendRecordsExtra;
import top.kx.kxss.system.vo.save.sms.DefSmsSendRecordsExtraSaveVO;
import top.kx.kxss.system.vo.update.sms.DefSmsSendRecordsExtraUpdateVO;
import top.kx.kxss.system.vo.result.sms.DefSmsSendRecordsExtraResultVO;
import top.kx.kxss.system.vo.query.sms.DefSmsSendRecordsExtraPageQuery;

/**
 * <p>
 * 业务实现类
 * 短信发送记录-拓展表
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-28 17:22:09
 * @create [2024-04-28 17:22:09] [yan] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class DefSmsSendRecordsExtraServiceImpl extends SuperServiceImpl<DefSmsSendRecordsExtraManager, Long, DefSmsSendRecordsExtra, DefSmsSendRecordsExtraSaveVO,
    DefSmsSendRecordsExtraUpdateVO, DefSmsSendRecordsExtraPageQuery, DefSmsSendRecordsExtraResultVO> implements DefSmsSendRecordsExtraService {


}


