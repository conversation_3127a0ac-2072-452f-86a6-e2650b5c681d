package top.kx.kxss.msg.service;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.msg.entity.DefMsgTemplate;
import top.kx.kxss.msg.vo.query.DefMsgTemplatePageQuery;
import top.kx.kxss.msg.vo.result.DefMsgTemplateResultVO;
import top.kx.kxss.msg.vo.save.DefMsgTemplateSaveVO;
import top.kx.kxss.msg.vo.update.DefMsgTemplateUpdateVO;


/**
 * <p>
 * 业务接口
 * 消息模板
 * </p>
 *
 * <AUTHOR>
 * @date 2022-07-04 15:51:37
 * @create [2022-07-04 15:51:37] [zuihou] [代码生成器生成]
 */
public interface DefMsgTemplateService extends SuperService<Long, DefMsgTemplate, DefMsgTemplateSaveVO,
        DefMsgTemplateUpdateVO, DefMsgTemplatePageQuery, DefMsgTemplateResultVO> {
    /**
     * 检测 模板标识 是否存在
     *
     * @param code
     * @param id
     * @return
     */
    Boolean check(String code, Long id);

    /**
     * 根据消息模板编码查询消息模板
     *
     * @param code
     * @return
     */
    DefMsgTemplate getByCode(String code);
}


