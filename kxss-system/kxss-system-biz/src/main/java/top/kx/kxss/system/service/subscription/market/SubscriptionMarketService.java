package top.kx.kxss.system.service.subscription.market;

import top.kx.kxss.system.vo.result.subscription.SubscriptionTemplateResultVO;
import top.kx.kxss.system.vo.result.subscription.market.SubscriptionMarketTreeResultVO;

import java.util.List;

/**
 * 功能市场
 * <AUTHOR>
 */
public interface SubscriptionMarketService {
    List<SubscriptionMarketTreeResultVO> marketList();

    SubscriptionTemplateResultVO getInfo(Long tmplId);

    Boolean isRenewalCap();

}


