package top.kx.kxss.system.service.sms;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.sms.DefSmsInfo;
import top.kx.kxss.system.entity.sms.DefSmsSendRecords;
import top.kx.kxss.system.vo.query.sms.DefSmsInfoPageQuery;
import top.kx.kxss.system.vo.result.sms.DefSmsInfoResultVO;
import top.kx.kxss.system.vo.save.sms.DefSmsInfoSaveVO;
import top.kx.kxss.system.vo.update.sms.DefSmsInfoUpdateNumVO;
import top.kx.kxss.system.vo.update.sms.DefSmsInfoUpdateVO;


/**
 * <p>
 * 业务接口
 * 商户门店-短信
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-28 17:22:09
 * @create [2024-04-28 17:22:09] [yan] [代码生成器生成]
 */
public interface DefSmsInfoService extends SuperService<Long, DefSmsInfo, DefSmsInfoSaveVO,
        DefSmsInfoUpdateVO, DefSmsInfoPageQuery, DefSmsInfoResultVO> {


    Boolean updateSmsNum(DefSmsInfoUpdateNumVO updateNumVO);

}


