package top.kx.kxss.system.service.sms;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.sms.DefSmsSendRecordsExtra;
import top.kx.kxss.system.vo.save.sms.DefSmsSendRecordsExtraSaveVO;
import top.kx.kxss.system.vo.update.sms.DefSmsSendRecordsExtraUpdateVO;
import top.kx.kxss.system.vo.result.sms.DefSmsSendRecordsExtraResultVO;
import top.kx.kxss.system.vo.query.sms.DefSmsSendRecordsExtraPageQuery;


/**
 * <p>
 * 业务接口
 * 短信发送记录-拓展表
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-28 17:22:09
 * @create [2024-04-28 17:22:09] [yan] [代码生成器生成]
 */
public interface DefSmsSendRecordsExtraService extends SuperService<Long, DefSmsSendRecordsExtra, DefSmsSendRecordsExtraSaveVO,
    DefSmsSendRecordsExtraUpdateVO, DefSmsSendRecordsExtraPageQuery, DefSmsSendRecordsExtraResultVO> {

}


