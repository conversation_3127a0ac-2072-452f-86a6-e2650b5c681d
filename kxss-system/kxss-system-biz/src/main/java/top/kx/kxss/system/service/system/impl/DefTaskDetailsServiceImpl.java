package top.kx.kxss.system.service.system.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.service.system.DefTaskDetailsService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.system.manager.system.DefTaskDetailsManager;
import top.kx.kxss.system.entity.system.DefTaskDetails;
import top.kx.kxss.system.vo.save.system.DefTaskDetailsSaveVO;
import top.kx.kxss.system.vo.update.system.DefTaskDetailsUpdateVO;
import top.kx.kxss.system.vo.result.system.DefTaskDetailsResultVO;
import top.kx.kxss.system.vo.query.system.DefTaskDetailsPageQuery;

/**
 * <p>
 * 业务实现类
 * 任务详情
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-12 17:42:51
 * @create [2024-12-12 17:42:51] [yan] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class DefTaskDetailsServiceImpl extends SuperServiceImpl<DefTaskDetailsManager, Long, DefTaskDetails, DefTaskDetailsSaveVO,
    DefTaskDetailsUpdateVO, DefTaskDetailsPageQuery, DefTaskDetailsResultVO> implements DefTaskDetailsService {


}


