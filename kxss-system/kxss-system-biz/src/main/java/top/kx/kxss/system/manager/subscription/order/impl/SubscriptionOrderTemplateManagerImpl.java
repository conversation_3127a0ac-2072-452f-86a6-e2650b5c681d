package top.kx.kxss.system.manager.subscription.order.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.subscription.order.SubscriptionOrderTemplate;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.subscription.order.SubscriptionOrderTemplateManager;
import top.kx.kxss.system.mapper.subscription.order.SubscriptionOrderTemplateMapper;

/**
 * <p>
 * 通用业务实现类
 * 订单订阅模板
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-09 18:56:18
 * @create [2025-06-09 18:56:18] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SubscriptionOrderTemplateManagerImpl extends SuperManagerImpl<SubscriptionOrderTemplateMapper, SubscriptionOrderTemplate> implements SubscriptionOrderTemplateManager {

}


