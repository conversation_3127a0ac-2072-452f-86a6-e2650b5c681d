package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DefTask;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DefTaskManager;
import top.kx.kxss.system.mapper.system.DefTaskMapper;

/**
 * <p>
 * 通用业务实现类
 * 任务表
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-12 17:42:51
 * @create [2024-12-12 17:42:51] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefTaskManagerImpl extends SuperManagerImpl<DefTaskMapper, DefTask> implements DefTaskManager {

}


