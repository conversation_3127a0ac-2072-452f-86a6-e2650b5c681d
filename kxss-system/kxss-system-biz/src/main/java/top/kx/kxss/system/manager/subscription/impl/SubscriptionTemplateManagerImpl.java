package top.kx.kxss.system.manager.subscription.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.subscription.SubscriptionTemplate;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.subscription.SubscriptionTemplateManager;
import top.kx.kxss.system.mapper.subscription.SubscriptionTemplateMapper;

/**
 * <p>
 * 通用业务实现类
 * 订阅模版
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-07 11:17:35
 * @create [2025-05-07 11:17:35] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SubscriptionTemplateManagerImpl extends SuperManagerImpl<SubscriptionTemplateMapper, SubscriptionTemplate> implements SubscriptionTemplateManager {

}


