package top.kx.kxss.system.mapper.subscription.order;

import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.system.entity.subscription.order.SubscriptionOrder;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * Mapper 接口
 * 订单订阅模版
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-09 17:25:13
 * @create [2025-06-09 17:25:13] [dou] [代码生成器生成]
 */
@Repository
public interface SubscriptionOrderMapper extends SuperMapper<SubscriptionOrder> {

}


