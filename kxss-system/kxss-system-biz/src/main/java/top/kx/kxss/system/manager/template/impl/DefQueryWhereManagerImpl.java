package top.kx.kxss.system.manager.template.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.template.DefQueryWhere;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.template.DefQueryWhereManager;
import top.kx.kxss.system.mapper.template.DefQueryWhereMapper;

/**
 * <p>
 * 通用业务实现类
 * 查询条件
 * </p>
 *
 * <AUTHOR>
 * @date 2024-01-06 17:20:29
 * @create [2024-01-06 17:20:29] [yh] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefQueryWhereManagerImpl extends SuperManagerImpl<DefQueryWhereMapper, DefQueryWhere> implements DefQueryWhereManager {

}


