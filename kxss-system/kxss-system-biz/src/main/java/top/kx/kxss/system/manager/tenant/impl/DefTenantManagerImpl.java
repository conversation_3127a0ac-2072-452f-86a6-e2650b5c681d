package top.kx.kxss.system.manager.tenant.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.kx.basic.base.manager.impl.SuperCacheManagerImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.basic.utils.CollHelper;
import top.kx.kxss.common.cache.tenant.tenant.TenantCacheKeyBuilder;
import top.kx.kxss.model.enumeration.system.DefTenantStatusEnum;
import top.kx.kxss.system.entity.tenant.DefTenant;
import top.kx.kxss.system.manager.tenant.DefTenantManager;
import top.kx.kxss.system.mapper.tenant.DefTenantMapper;
import top.kx.kxss.system.vo.result.tenant.DefTenantResultVO;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 应用管理
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2021/9/29 1:26 下午
 * @create [2021/9/29 1:26 下午 ] [tangyh] [初始创建]
 */
@RequiredArgsConstructor
@Service
public class DefTenantManagerImpl extends SuperCacheManagerImpl<DefTenantMapper, DefTenant> implements DefTenantManager {
    @Override
    protected CacheKeyBuilder cacheKeyBuilder() {
        return new TenantCacheKeyBuilder();
    }

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        return CollHelper.uniqueIndex(find(ids), DefTenant::getId, DefTenant::getName);
    }

    public List<DefTenant> find(Set<Serializable> ids) {
        // 强转， 防止数据库隐式转换，  若你的id 是string类型，请勿强转
        return findByIds(ids, null).stream().filter(Objects::nonNull).collect(Collectors.toList());
    }


    @Override
    public List<DefTenant> listNormal() {
        return list(Wraps.<DefTenant>lbQ().eq(DefTenant::getStatus, DefTenantStatusEnum.NORMAL.getCode()));
    }

    @Override
    public List<DefTenantResultVO> listTenantByUserId(Long userId) {
        return baseMapper.listTenantByUserId(userId);
    }
}
