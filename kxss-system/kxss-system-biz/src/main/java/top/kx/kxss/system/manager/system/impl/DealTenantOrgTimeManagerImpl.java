package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DealTenantOrgTime;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DealTenantOrgTimeManager;
import top.kx.kxss.system.mapper.system.DealTenantOrgTimeMapper;

/**
 * <p>
 * 通用业务实现类
 * 租户-门店团购到期时间
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-21 15:48:08
 * @create [2024-10-21 15:48:08] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DealTenantOrgTimeManagerImpl extends SuperManagerImpl<DealTenantOrgTimeMapper, DealTenantOrgTime> implements DealTenantOrgTimeManager {

}


