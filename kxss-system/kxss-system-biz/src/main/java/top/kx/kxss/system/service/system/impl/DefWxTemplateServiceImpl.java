package top.kx.kxss.system.service.system.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.service.system.DefWxTemplateService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.system.manager.system.DefWxTemplateManager;
import top.kx.kxss.system.entity.system.DefWxTemplate;
import top.kx.kxss.system.vo.save.system.DefWxTemplateSaveVO;
import top.kx.kxss.system.vo.update.system.DefWxTemplateUpdateVO;
import top.kx.kxss.system.vo.result.system.DefWxTemplateResultVO;
import top.kx.kxss.system.vo.query.system.DefWxTemplatePageQuery;

/**
 * <p>
 * 业务实现类
 * 微信模板
 * </p>
 *
 * <AUTHOR>
 * @date 2023-12-11 11:42:11
 * @create [2023-12-11 11:42:11] [yh] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class DefWxTemplateServiceImpl extends SuperServiceImpl<DefWxTemplateManager, Long, DefWxTemplate, DefWxTemplateSaveVO,
    DefWxTemplateUpdateVO, DefWxTemplatePageQuery, DefWxTemplateResultVO> implements DefWxTemplateService {


}


