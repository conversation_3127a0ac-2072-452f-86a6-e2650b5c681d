package top.kx.kxss.system.service.clear;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.clear.DefTenantOrgClearTable;
import top.kx.kxss.system.vo.save.clear.DefTenantOrgClearTableSaveVO;
import top.kx.kxss.system.vo.update.clear.DefTenantOrgClearTableUpdateVO;
import top.kx.kxss.system.vo.result.clear.DefTenantOrgClearTableResultVO;
import top.kx.kxss.system.vo.query.clear.DefTenantOrgClearTablePageQuery;


/**
 * <p>
 * 业务接口
 * 清空的表数据
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-20 17:43:47
 * @create [2025-06-20 17:43:47] [yan] [代码生成器生成]
 */
public interface DefTenantOrgClearTableService extends SuperService<Long, DefTenantOrgClearTable, DefTenantOrgClearTableSaveVO,
    DefTenantOrgClearTableUpdateVO, DefTenantOrgClearTablePageQuery, DefTenantOrgClearTableResultVO> {

}


