package top.kx.kxss.system.strategy.subscription;


import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplate;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplateFeature;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 订阅检查策略
 *
 * <AUTHOR>
 */
public abstract class AbstractSubscriptionCheckService implements SubscriptionCheckStrategy {


    protected boolean checkFeatureCode(List<SubscriptionTenantTemplateFeature> featureList, String featureCode) {
        if (featureList == null || featureList.isEmpty()) {
            featureList = new ArrayList<>();
        }
        return featureList.stream().anyMatch(feature -> feature.getFeatureCode().equals(featureCode));
    }

    protected boolean checkExpire(SubscriptionTenantTemplate tenantTemplate) {
        if (tenantTemplate == null || tenantTemplate.getExpirationTime() == null) {
            return false;
        }
        LocalDateTime expirationTime = tenantTemplate.getExpirationTime();
        return LocalDateTime.now().isBefore(expirationTime);
    }

}
