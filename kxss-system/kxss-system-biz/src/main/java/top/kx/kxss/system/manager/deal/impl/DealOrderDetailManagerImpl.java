package top.kx.kxss.system.manager.deal.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.deal.DealOrderDetail;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.deal.DealOrderDetailManager;
import top.kx.kxss.system.mapper.deal.DealOrderDetailMapper;

/**
 * <p>
 * 通用业务实现类
 * 订单明细
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-24 14:08:24
 * @create [2024-10-24 14:08:24] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DealOrderDetailManagerImpl extends SuperManagerImpl<DealOrderDetailMapper, DealOrderDetail> implements DealOrderDetailManager {

}


