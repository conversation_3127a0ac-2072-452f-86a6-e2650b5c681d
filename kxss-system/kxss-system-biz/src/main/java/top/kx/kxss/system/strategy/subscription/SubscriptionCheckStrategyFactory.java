package top.kx.kxss.system.strategy.subscription;

import cn.hutool.extra.spring.SpringUtil;
import com.google.common.base.CaseFormat;
import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.stereotype.Service;
import top.kx.kxss.model.enumeration.system.subscription.FeatureCodeEnum;

/**
 * 订阅检查策略
 *
 * <AUTHOR>
 * @date 2025/5/14 15:11
 */
@Service
public class SubscriptionCheckStrategyFactory {

    public SubscriptionCheckStrategy strategyType(String type) {
        FeatureCodeEnum featureCodeEnum = FeatureCodeEnum.get(type);
        if (featureCodeEnum == null) {
            return SpringUtil.getBean("commonSubscriptionCheckStrategy", SubscriptionCheckStrategy.class);
        }
        String ans2 = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.LOWER_CAMEL,
                featureCodeEnum.getCode());
        //获取系统点单接口
        try {
            return SpringUtil.getBean(ans2 + "SubscriptionCheckStrategy", SubscriptionCheckStrategy.class);
        } catch (NoSuchBeanDefinitionException e) {
            return SpringUtil.getBean("commonSubscriptionCheckStrategy", SubscriptionCheckStrategy.class);
        }
    }
}
