package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.system.entity.deal.DealOrder;
import top.kx.kxss.system.entity.deal.DealOrderDetail;
import top.kx.kxss.system.entity.system.DealTenantOrgTime;
import top.kx.kxss.system.vo.query.system.DealTenantOrgTimePageQuery;
import top.kx.kxss.system.vo.result.system.DealTenantOrgTimeResultVO;
import top.kx.kxss.system.vo.save.system.DealTenantOrgTimeSaveVO;
import top.kx.kxss.system.vo.update.system.DealTenantOrgTimeUpdateVO;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <p>
 * 业务接口
 * 租户-门店团购到期时间
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-21 15:48:08
 * @create [2024-10-21 15:48:08] [dou] [代码生成器生成]
 */
public interface DealTenantOrgTimeService extends SuperService<Long, DealTenantOrgTime, DealTenantOrgTimeSaveVO,
        DealTenantOrgTimeUpdateVO, DealTenantOrgTimePageQuery, DealTenantOrgTimeResultVO> {

    DealTenantOrgTime getOne(LbQueryWrap<DealTenantOrgTime> eq);

    boolean grant(DealOrder dealOrder, List<DealOrderDetail> orderDetailList, LocalDateTime createdTime);

    LocalDateTime getExpireTime(LocalDateTime createdTime);

    boolean isAuthExpire(LocalDateTime createdTime);

    long getExpireDays(LocalDateTime createdTime);
}


