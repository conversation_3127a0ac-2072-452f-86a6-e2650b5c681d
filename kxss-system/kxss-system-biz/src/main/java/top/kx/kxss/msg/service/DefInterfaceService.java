package top.kx.kxss.msg.service;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.msg.entity.DefInterface;
import top.kx.kxss.msg.vo.query.DefInterfacePageQuery;
import top.kx.kxss.msg.vo.result.DefInterfaceResultVO;
import top.kx.kxss.msg.vo.save.DefInterfaceSaveVO;
import top.kx.kxss.msg.vo.update.DefInterfaceUpdateVO;


/**
 * <p>
 * 业务接口
 * 接口
 * </p>
 *
 * <AUTHOR>
 * @date 2022-07-04 16:45:45
 * @create [2022-07-04 16:45:45] [zuihou] [代码生成器生成]
 */
public interface DefInterfaceService extends SuperService<Long, DefInterface, DefInterfaceSaveVO,
        DefInterfaceUpdateVO, DefInterfacePageQuery, DefInterfaceResultVO> {
    /**
     * 检查接口编码是否重复
     *
     * @param code
     * @param id
     * @return
     */
    Boolean check(String code, Long id);
}


