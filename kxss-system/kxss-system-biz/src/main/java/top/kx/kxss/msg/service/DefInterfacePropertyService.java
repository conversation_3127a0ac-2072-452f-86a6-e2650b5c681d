package top.kx.kxss.msg.service;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.msg.entity.DefInterfaceProperty;
import top.kx.kxss.msg.vo.query.DefInterfacePropertyPageQuery;
import top.kx.kxss.msg.vo.result.DefInterfacePropertyResultVO;
import top.kx.kxss.msg.vo.save.DefInterfacePropertyBatchSaveVO;
import top.kx.kxss.msg.vo.save.DefInterfacePropertySaveVO;
import top.kx.kxss.msg.vo.update.DefInterfacePropertyUpdateVO;

import java.util.Map;


/**
 * <p>
 * 业务接口
 * 接口属性
 * </p>
 *
 * <AUTHOR>
 * @date 2022-07-04 15:51:37
 * @create [2022-07-04 15:51:37] [zuihou] [代码生成器生成]
 */
public interface DefInterfacePropertyService extends SuperService<Long, DefInterfaceProperty, DefInterfacePropertySaveVO,
        DefInterfacePropertyUpdateVO, DefInterfacePropertyPageQuery, DefInterfacePropertyResultVO> {
    /**
     * 根据接口ID查询接口属性参数
     *
     * @param id
     * @return
     */
    Map<String, Object> listByInterfaceId(Long id);

    /**
     * 批量保存
     *
     * @param saveVO
     * @return
     */
    Boolean batchSave(DefInterfacePropertyBatchSaveVO saveVO);
}


