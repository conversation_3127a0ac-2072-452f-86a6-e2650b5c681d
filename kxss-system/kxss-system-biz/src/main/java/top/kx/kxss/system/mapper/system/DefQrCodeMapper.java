package top.kx.kxss.system.mapper.system;

import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.system.entity.system.DefQrCode;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * Mapper 接口
 * 二维码生成
 * </p>
 *
 * <AUTHOR>
 * @date 2023-08-22 17:49:38
 * @create [2023-08-22 17:49:38] [lixue<PERSON>] [代码生成器生成]
 */
@Repository
public interface DefQrCodeMapper extends SuperMapper<DefQrCode> {

}


