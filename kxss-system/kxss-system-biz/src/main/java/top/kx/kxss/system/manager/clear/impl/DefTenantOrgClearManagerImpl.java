package top.kx.kxss.system.manager.clear.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.clear.DefTenantOrgClear;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.clear.DefTenantOrgClearManager;
import top.kx.kxss.system.mapper.clear.DefTenantOrgClearMapper;

import java.time.LocalDateTime;

/**
 * <p>
 * 通用业务实现类
 * 数据清空记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-20 17:43:48
 * @create [2025-06-20 17:43:48] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefTenantOrgClearManagerImpl extends SuperManagerImpl<DefTenantOrgClearMapper, DefTenantOrgClear> implements DefTenantOrgClearManager {

    private final DefTenantOrgClearMapper defTenantOrgClearMapper;

    @Override
    public void copyTableWithData(String oldTable, String newTable, Long tenantId, Long orgId) {
        defTenantOrgClearMapper.copyTableWithData(oldTable, newTable, tenantId, orgId);
    }

    @Override
    public void markDataDeletedByTimeRange(Long tenantId, Long orgId, String tableName, LocalDateTime startTime, LocalDateTime endTime) {
        defTenantOrgClearMapper.markDataDeletedByTimeRange(tenantId, orgId, tableName, startTime, endTime);
    }
}


