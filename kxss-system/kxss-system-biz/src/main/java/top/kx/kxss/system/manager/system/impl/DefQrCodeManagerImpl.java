package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DefQrCode;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DefQrCodeManager;
import top.kx.kxss.system.mapper.system.DefQrCodeMapper;

/**
 * <p>
 * 通用业务实现类
 * 二维码生成
 * </p>
 *
 * <AUTHOR>
 * @date 2023-08-22 17:49:38
 * @create [2023-08-22 17:49:38] [lixue<PERSON>] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefQrCodeManagerImpl extends SuperManagerImpl<DefQrCodeMapper, DefQrCode> implements DefQrCodeManager {

}


