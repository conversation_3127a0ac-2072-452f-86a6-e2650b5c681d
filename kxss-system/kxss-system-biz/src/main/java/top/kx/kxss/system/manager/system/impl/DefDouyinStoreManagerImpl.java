package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DefDouyinStore;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DefDouyinStoreManager;
import top.kx.kxss.system.mapper.system.DefDouyinStoreMapper;

/**
 * <p>
 * 通用业务实现类
 * 抖音授权门店
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-07 13:54:39
 * @create [2024-04-07 13:54:39] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefDouyinStoreManagerImpl extends SuperManagerImpl<DefDouyinStoreMapper, DefDouyinStore> implements DefDouyinStoreManager {

}


