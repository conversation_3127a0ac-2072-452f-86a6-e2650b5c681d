package top.kx.kxss.system.aspect;

/**
 * <AUTHOR>
 * @date 2025/5/21 15:56
 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.exception.BizException;
import top.kx.basic.jackson.JsonUtil;
import top.kx.kxss.model.enumeration.system.subscription.FeatureCodeEnum;
import top.kx.kxss.model.enumeration.system.subscription.SubscriptionFeatureTypeEnum;
import top.kx.kxss.system.annotation.SubscriptionFeatureCheck;
import top.kx.kxss.system.service.subscription.impl.SubscriptionFeatureCheckService;

import java.lang.reflect.Method;

/**
 * 功能权限检查拦截器
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class SubscriptionFeatureCheckAspect {

    private final SubscriptionFeatureCheckService featureCheckService;

    @Around("@annotation(top.kx.kxss.system.annotation.SubscriptionFeatureCheck)")
    public Object checkFeaturePermission(ProceedingJoinPoint joinPoint) throws Throwable {
        // 获取当前租户ID
        Long tenantId = ContextUtil.getTenantId();
        if (tenantId == null) {
            throw new BizException("未获取到租户信息");
        }
        // 获取参数
        JSONObject params = new JSONObject();
        if (joinPoint.getArgs() != null && joinPoint.getArgs().length > 0) {
            String data = JSON.toJSONString(joinPoint.getArgs()[0]);
            params = JsonUtil.parse(data, JSONObject.class);
        }
        // 获取注解信息
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        SubscriptionFeatureCheck annotation = method.getAnnotation(SubscriptionFeatureCheck.class);
        if (annotation == null) {
            return joinPoint.proceed();
        }
        FeatureCodeEnum[] featureCode = annotation.featureCode();
        SubscriptionFeatureTypeEnum featureTypeEnum = annotation.type();
        // 检查是否有功能权限
        //套餐功能
        boolean hasPermission = featureCheckService.hasFeaturePermission(tenantId, featureCode, params, featureTypeEnum);
        if (!hasPermission) {
            throw new BizException(annotation.errorMessage().isEmpty() ?
                    "您没有该功能的使用权限，请升级订阅计划" : annotation.errorMessage());
        }

        // 检查功能数量限制（如果需要）
//        if (annotation.checkLimit()) {
//            int limitCount = featureCheckService.getFeatureLimitCount(tenantId, featureCode);
//            if (limitCount == 0) {
//                throw new BizException(annotation.limitErrorMessage().isEmpty() ?
//                        "您没有该功能的使用权限，请升级订阅计划" : annotation.limitErrorMessage());
//            }
//
//            if (limitCount > 0) {
//                // 这里需要根据业务逻辑检查当前使用量是否超过限制
//                // 此处只是示例，实际实现需要根据具体业务场景处理
////                 int currentCount = getCurrentUsageCount(tenantId, featureCode);
////                 if (currentCount >= limitCount) {
////                     throw new BizException(annotation.limitErrorMessage().isEmpty() ?
////                             "您已达到该功能的使用上限，请升级订阅计划" : annotation.limitErrorMessage());
////                 }
//            }
//        }
        return joinPoint.proceed();
    }

}
