package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperCacheService;
import top.kx.kxss.system.entity.system.DefParameter;
import top.kx.kxss.system.vo.query.system.DefParameterPageQuery;
import top.kx.kxss.system.vo.result.system.DefParameterResultVO;
import top.kx.kxss.system.vo.save.system.DefParameterSaveVO;
import top.kx.kxss.system.vo.update.system.DefParameterUpdateVO;

/**
 * <p>
 * 业务接口
 * 参数配置
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-13
 */
public interface DefParameterService extends SuperCacheService<Long, DefParameter, DefParameterSaveVO, DefParameterUpdateVO, DefParameterPageQuery, DefParameterResultVO> {
    /**
     * 检测参数键是否可用
     *
     * @param key 健
     * @param id  参数ID
     * @return 是否存在
     */
    Boolean checkKey(String key, Long id);
}
