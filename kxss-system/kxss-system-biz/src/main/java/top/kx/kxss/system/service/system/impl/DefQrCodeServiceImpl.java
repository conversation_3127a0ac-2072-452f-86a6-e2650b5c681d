package top.kx.kxss.system.service.system.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.system.DefQrCode;
import top.kx.kxss.system.manager.system.DefQrCodeManager;
import top.kx.kxss.system.service.system.DefQrCodeService;
import top.kx.kxss.system.vo.query.system.DefQrCodePageQuery;
import top.kx.kxss.system.vo.result.system.DefQrCodeResultVO;
import top.kx.kxss.system.vo.save.system.DefQrCodeSaveVO;
import top.kx.kxss.system.vo.update.system.DefQrCodeUpdateVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 业务实现类
 * 二维码生成
 * </p>
 *
 * <AUTHOR>
 * @date 2023-08-22 17:49:38
 * @create [2023-08-22 17:49:38] [lixuecheng] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class DefQrCodeServiceImpl extends SuperServiceImpl<DefQrCodeManager, Long, DefQrCode, DefQrCodeSaveVO,
        DefQrCodeUpdateVO, DefQrCodePageQuery, DefQrCodeResultVO> implements DefQrCodeService {
    @Override
    public DefQrCode getOne(LbQueryWrap<DefQrCode> eq) {

        DefQrCode defQrCode = superManager.getOne(eq);
        return defQrCode;
    }

    @Override
    public void saveQrCode(DefQrCode saveQrCode) {
        saveQrCode.setCreatedBy(ContextUtil.getUserId());
        saveQrCode.setUpdatedBy(ContextUtil.getUserId());
        superManager.save(saveQrCode);
    }

    @Override
    public Boolean updateById(DefQrCode defQrCode) {
        defQrCode.setUpdatedTime(LocalDateTime.now());
        return superManager.updateById(defQrCode);
    }

    @Override
    public void saveOrUpdateBatch(List<DefQrCode> saveBatch) {
        superManager.saveOrUpdateBatch(saveBatch);
    }
}


