package top.kx.kxss.system.manager.system.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.manager.impl.SuperCacheManagerImpl;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.basic.utils.CollHelper;
import top.kx.kxss.common.cache.base.common.AreaCacheKeyBuilder;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.system.DefArea;
import top.kx.kxss.system.manager.system.DefAreaManager;
import top.kx.kxss.system.mapper.system.DefAreaMapper;

import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用业务实现类
 * 地区表
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-13
 * @create [2021-10-13] [zuihou] [代码生成器生成]
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefAreaManagerImpl extends SuperCacheManagerImpl<DefAreaMapper, DefArea> implements DefAreaManager {


    @Override
    protected CacheKeyBuilder cacheKeyBuilder() {
        return new AreaCacheKeyBuilder();
    }
    @Transactional(readOnly = true)
    @Override
    @DS(DsConstant.DEFAULTS)
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        List<DefArea> list = findByIds(ids, null).stream().filter(Objects::nonNull).collect(Collectors.toList());
        return CollHelper.uniqueIndex(list, DefArea::getId, DefArea::getName);
    }
}
