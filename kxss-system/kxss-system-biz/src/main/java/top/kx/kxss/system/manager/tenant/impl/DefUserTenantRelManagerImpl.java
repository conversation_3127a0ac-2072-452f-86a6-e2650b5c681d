package top.kx.kxss.system.manager.tenant.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.manager.impl.SuperCacheManagerImpl;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.kxss.common.cache.tenant.tenant.DefUserTenantCacheKeyBuilder;
import top.kx.kxss.system.entity.tenant.DefUserTenantRel;
import top.kx.kxss.system.manager.tenant.DefUserTenantRelManager;
import top.kx.kxss.system.mapper.tenant.DefUserTenantRelMapper;
import top.kx.kxss.system.vo.result.tenant.DefUserTenantRelResultVO;

import java.util.List;

/**
 * <p>
 * 通用业务实现类
 * 员工
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-27
 * @create [2021-10-27] [zuihou] [代码生成器生成]
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefUserTenantRelManagerImpl extends SuperCacheManagerImpl<DefUserTenantRelMapper, DefUserTenantRel> implements DefUserTenantRelManager {
    @Override
    protected CacheKeyBuilder cacheKeyBuilder() {
        return new DefUserTenantCacheKeyBuilder();
    }


    @Override
    public List<DefUserTenantRelResultVO> listEmployeeByUserId(Long userId) {
        return baseMapper.listEmployeeByUserId(userId);
    }
}
