package top.kx.kxss.system.service.scan.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.scan.DefScan;
import top.kx.kxss.system.manager.scan.DefScanManager;
import top.kx.kxss.system.service.scan.DefScanService;
import top.kx.kxss.system.vo.query.scan.DefScanPageQuery;
import top.kx.kxss.system.vo.result.scan.DefScanResultVO;
import top.kx.kxss.system.vo.save.scan.DefScanSaveVO;
import top.kx.kxss.system.vo.update.scan.DefScanUpdateVO;

/**
 * <p>
 * 业务实现类
 * 二维码生成
 * </p>
 *
 * <AUTHOR>
 * @date 2024-08-29 15:20:38
 * @create [2024-08-29 15:20:38] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class DefScanServiceImpl extends SuperServiceImpl<DefScanManager, Long, DefScan, DefScanSaveVO,
        DefScanUpdateVO, DefScanPageQuery, DefScanResultVO> implements DefScanService {


    @Override
    public boolean save(DefScan defScan) {
        return superManager.save(defScan);
    }

    @Override
    public DefScan getOne(LbQueryWrap<DefScan> queryWrap) {
        return superManager.getOne(queryWrap);
    }

    @Override
    public boolean updateById(DefScan defScan) {
        return superManager.updateById(defScan);
    }
}


