package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.system.entity.system.DefNorthStarStore;
import top.kx.kxss.system.vo.save.system.DefNorthStarStoreSaveVO;
import top.kx.kxss.system.vo.update.system.DefNorthStarStoreUpdateVO;
import top.kx.kxss.system.vo.result.system.DefNorthStarStoreResultVO;
import top.kx.kxss.system.vo.query.system.DefNorthStarStorePageQuery;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 北极星授权门店
 * </p>
 *
 * <AUTHOR>
 * @date 2023-11-02 10:00:21
 * @create [2023-11-02 10:00:21] [dou] [代码生成器生成]
 */
public interface DefNorthStarStoreService extends SuperService<Long, DefNorthStarStore, DefNorthStarStoreSaveVO,
    DefNorthStarStoreUpdateVO, DefNorthStarStorePageQuery, DefNorthStarStoreResultVO> {

    DefNorthStarStore getOne(LbQueryWrap<DefNorthStarStore> eq);

    Boolean updateById(DefNorthStarStore northStarStore);

    Boolean updateBatchById(List<DefNorthStarStore> defNorthStarStoreList);

    Boolean saveOrUpdate(DefNorthStarStore defNorthStarStore);

}


