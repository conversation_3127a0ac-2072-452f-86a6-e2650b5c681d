package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DefVerticalScreenUpgrade;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DefVerticalScreenUpgradeManager;
import top.kx.kxss.system.mapper.system.DefVerticalScreenUpgradeMapper;

/**
 * <p>
 * 通用业务实现类
 * 智慧屏升级公告
 * </p>
 *
 * <AUTHOR>
 * @date 2024-11-21 10:38:04
 * @create [2024-11-21 10:38:04] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefVerticalScreenUpgradeManagerImpl extends SuperManagerImpl<DefVerticalScreenUpgradeMapper, DefVerticalScreenUpgrade> implements DefVerticalScreenUpgradeManager {

}


