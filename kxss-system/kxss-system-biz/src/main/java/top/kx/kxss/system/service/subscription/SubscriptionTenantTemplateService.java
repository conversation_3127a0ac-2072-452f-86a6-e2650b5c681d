package top.kx.kxss.system.service.subscription;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplate;
import top.kx.kxss.system.vo.save.subscription.SubscriptionTenantTemplateSaveVO;
import top.kx.kxss.system.vo.update.subscription.SubscriptionTenantTemplateUpdateVO;
import top.kx.kxss.system.vo.result.subscription.SubscriptionTenantTemplateResultVO;
import top.kx.kxss.system.vo.query.subscription.SubscriptionTenantTemplatePageQuery;

import java.time.LocalDateTime;
import java.util.List;


/**
 * <p>
 * 业务接口
 * 租户订阅模板
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-08 15:33:09
 * @create [2025-05-08 15:33:09] [dou] [代码生成器生成]
 */
public interface SubscriptionTenantTemplateService extends SuperService<Long, SubscriptionTenantTemplate, SubscriptionTenantTemplateSaveVO,
    SubscriptionTenantTemplateUpdateVO, SubscriptionTenantTemplatePageQuery, SubscriptionTenantTemplateResultVO> {

    SubscriptionTenantTemplate saveTenantTemplate(SubscriptionTenantTemplateSaveVO model);

    SubscriptionTenantTemplate updateTenantTemplate(SubscriptionTenantTemplateUpdateVO subscriptionTenantTemplateUpdateVO);

    LocalDateTime getExpirationTime(LocalDateTime startTime, String tmpBillingType, Integer tmpDays);

    SubscriptionTenantTemplate getOne(LbQueryWrap<SubscriptionTenantTemplate> last);

    boolean updateById(SubscriptionTenantTemplate tenantTemplate);

    boolean updateBatchById(List<SubscriptionTenantTemplate> updateTenantTemplateList);

    long count(LbQueryWrap<SubscriptionTenantTemplate> eq);

}


