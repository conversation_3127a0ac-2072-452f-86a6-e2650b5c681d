package top.kx.kxss.system.service.system.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.file.service.DefAppendixService;
import top.kx.kxss.model.enumeration.system.TenantClientTypeEnum;
import top.kx.kxss.system.entity.system.DefApplet;
import top.kx.kxss.system.entity.system.DefClient;
import top.kx.kxss.system.entity.tenant.DefTenant;
import top.kx.kxss.system.manager.system.DefAppletManager;
import top.kx.kxss.system.manager.system.DefClientManager;
import top.kx.kxss.system.manager.tenant.DefTenantManager;
import top.kx.kxss.system.service.system.DefAppletService;
import top.kx.kxss.system.vo.query.system.DefAppletPageQuery;
import top.kx.kxss.system.vo.result.system.DefAppletResultVO;
import top.kx.kxss.system.vo.save.system.DefAppletSaveVO;
import top.kx.kxss.system.vo.update.system.DefAppletUpdateVO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 小程序配置
 * </p>
 *
 * <AUTHOR>
 * @date 2023-07-06 15:46:15
 * @create [2023-07-06 15:46:15] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class DefAppletServiceImpl extends SuperServiceImpl<DefAppletManager, Long, DefApplet, DefAppletSaveVO,
        DefAppletUpdateVO, DefAppletPageQuery, DefAppletResultVO> implements DefAppletService {


    private final DefAppendixService appendixService;
    private final DefClientManager defClientManager;
    private final DefTenantManager defTenantManager;

    @Override
    public Boolean check(Long id, String name) {
        LbQueryWrap<DefApplet> wrap = Wraps.<DefApplet>lbQ().eq(DefApplet::getName, name)
                .ne(DefApplet::getId, id);
        return superManager.count(wrap) > 0;
    }

    @Override
    public Boolean checkTenant(Long id, List<String> tenantId) {
        LbQueryWrap<DefApplet> wrap = Wraps.<DefApplet>lbQ().in(DefApplet::getTenantId, tenantId)
                .ne(DefApplet::getId, id);
        return superManager.count(wrap) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DefApplet save(DefAppletSaveVO defAppletSaveVO) {
        ArgumentAssert.notNull(defAppletSaveVO.getAppendixIcon(), "请上传小程序码");
        ArgumentAssert.isFalse(check(null, defAppletSaveVO.getName()), "小程序名称重复");
        ArgumentAssert.isFalse(checkTenant(null, defAppletSaveVO.getTenantId()), "此商户已绑定小程序");
        DefApplet defApplet = super.save(defAppletSaveVO);
        appendixService.save(defApplet.getId(), defAppletSaveVO.getAppendixIcon());
        return defApplet;
    }

    @Override
    public DefApplet updateById(DefAppletUpdateVO updateVO) {
        ArgumentAssert.notNull(updateVO.getAppendixIcon(), "请上传小程序码");
        ArgumentAssert.isFalse(check(updateVO.getId(), updateVO.getName()), "小程序名称重复");
        ArgumentAssert.isFalse(checkTenant(updateVO.getId(), updateVO.getTenantId()), "此商户已绑定小程序");
        DefApplet defApplet = BeanPlusUtil.toBean(updateVO, DefApplet.class);
        superManager.updateById(defApplet);
        updateVO.getAppendixIcon().setId(null);
        appendixService.save(defApplet.getId(), updateVO.getAppendixIcon());
        return super.updateById(updateVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateState(Long id, Boolean state) {
        // 演示环境专用标识，用于WriteInterceptor拦截器判断演示环境需要禁止用户执行sql，若您无需搭建演示环境，可以删除下面一行代码
        DefApplet defApplet = DefApplet.builder().state(state).build();
        defApplet.setId(id);
        defApplet.setUpdatedTime(LocalDateTime.now());
        return superManager.updateById(defApplet);
    }

    @Override
    public DefApplet getOne(LbQueryWrap<DefApplet> eq) {
        return superManager.getOne(eq);
    }

    @Override
    public boolean checkTenant(Long clientId, List<String> tenantId, Long id) {
        DefClient defClient = defClientManager.getById(clientId);
        ArgumentAssert.notNull(defClient, "应用不存在");
        Map<Long, DefTenant> tenantMap = defTenantManager.list(Wraps.<DefTenant>lbQ().in(DefTenant::getId, tenantId)).stream()
                .collect(Collectors.toMap(DefTenant::getId, Function.identity()));
        for (String string : tenantId) {
            DefApplet defApplet = superManager.getOne(Wraps.<DefApplet>lbQ()
                    .ne(DefApplet::getId, id)
                    .inSql(DefApplet::getClientId, "select id from def_client where delete_flag = 0 and " +
                            "type = " + defClient.getType())
                    .apply(" json_contains(tenant_id, '" + string + "')").last("limit 1"));
            if (defApplet != null) {
                DefTenant defTenant = tenantMap.get(Long.parseLong(string));
                ArgumentAssert.isFalse(ObjectUtil.equals(1, 1), "【" + defTenant.getName() + "】已存在【" +
                        TenantClientTypeEnum.get(defClient.getType()).getDesc() + "】应用类型中");
                return false;
            }
            if (ObjectUtil.equals(defClient.getType(), TenantClientTypeEnum.SELF_HELP.getCode())
                    || ObjectUtil.equals(defClient.getType(), TenantClientTypeEnum.PLATFORM_APPLET.getCode())) {
                defApplet = superManager.getOne(Wraps.<DefApplet>lbQ()
                        .ne(DefApplet::getId, id)
                        .inSql(DefApplet::getClientId, "select id from def_client where delete_flag = 0 and " +
                                "type = " + (ObjectUtil.equals(defClient.getType(), TenantClientTypeEnum.SELF_HELP.getCode()) ?
                                TenantClientTypeEnum.PLATFORM_APPLET.getCode() :
                                TenantClientTypeEnum.SELF_HELP.getCode()))
                        .apply(" json_contains(tenant_id, '" + string + "')").last("limit 1"));
                if (defApplet != null) {
                    DefTenant defTenant = tenantMap.get(Long.parseLong(string));
                    ArgumentAssert.isFalse(ObjectUtil.equals(1, 1), "自助小程序和平台小程序商户绑定不能重复，【" + defTenant.getName() + "】已存在【" +
                            (ObjectUtil.equals(defClient.getType(), TenantClientTypeEnum.SELF_HELP.getCode()) ?
                                    TenantClientTypeEnum.PLATFORM_APPLET.getDesc() :
                                    TenantClientTypeEnum.SELF_HELP.getDesc()) + "】应用类型中");
                    return false;
                }
            }
        }

        return true;
    }
}


