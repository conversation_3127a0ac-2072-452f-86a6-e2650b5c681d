package top.kx.kxss.system.service.template;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.template.DefQueryWhere;
import top.kx.kxss.system.vo.save.template.DefQueryWhereSaveVO;
import top.kx.kxss.system.vo.update.template.DefQueryWhereUpdateVO;
import top.kx.kxss.system.vo.result.template.DefQueryWhereResultVO;
import top.kx.kxss.system.vo.query.template.DefQueryWherePageQuery;


/**
 * <p>
 * 业务接口
 * 查询条件
 * </p>
 *
 * <AUTHOR>
 * @date 2024-01-06 17:20:29
 * @create [2024-01-06 17:20:29] [yh] [代码生成器生成]
 */
public interface DefQueryWhereService extends SuperService<Long, DefQueryWhere, DefQueryWhereSaveVO,
    DefQueryWhereUpdateVO, DefQueryWherePageQuery, DefQueryWhereResultVO> {

}


