package top.kx.kxss.system.service.system.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.system.DefVerticalScreenUpgradeTenant;
import top.kx.kxss.system.manager.system.DefVerticalScreenUpgradeTenantManager;
import top.kx.kxss.system.service.system.DefVerticalScreenUpgradeTenantService;
import top.kx.kxss.system.vo.query.system.DefVerticalScreenUpgradeTenantPageQuery;
import top.kx.kxss.system.vo.result.system.DefVerticalScreenUpgradeTenantResultVO;
import top.kx.kxss.system.vo.save.system.DefVerticalScreenUpgradeTenantSaveVO;
import top.kx.kxss.system.vo.update.system.DefVerticalScreenUpgradeTenantUpdateVO;

/**
 * <p>
 * 业务实现类
 * 智慧屏可更新商户
 * </p>
 *
 * <AUTHOR>
 * @date 2025-08-20 16:03:24
 * @create [2025-08-20 16:03:24] [yan] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class DefVerticalScreenUpgradeTenantServiceImpl extends SuperServiceImpl<DefVerticalScreenUpgradeTenantManager, Long, DefVerticalScreenUpgradeTenant, DefVerticalScreenUpgradeTenantSaveVO,
    DefVerticalScreenUpgradeTenantUpdateVO, DefVerticalScreenUpgradeTenantPageQuery, DefVerticalScreenUpgradeTenantResultVO> implements DefVerticalScreenUpgradeTenantService {


    @Override
    public void remove(LbQueryWrap<DefVerticalScreenUpgradeTenant> wrap) {
        superManager.remove(wrap);
    }
}


