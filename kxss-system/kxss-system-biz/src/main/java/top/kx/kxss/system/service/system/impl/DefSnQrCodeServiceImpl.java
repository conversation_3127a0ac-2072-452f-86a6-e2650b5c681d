package top.kx.kxss.system.service.system.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.system.DefSnQrCode;
import top.kx.kxss.system.manager.system.DefSnQrCodeManager;
import top.kx.kxss.system.service.system.DefSnQrCodeService;
import top.kx.kxss.system.vo.query.system.DefSnQrCodePageQuery;
import top.kx.kxss.system.vo.result.system.DefSnQrCodeResultVO;
import top.kx.kxss.system.vo.save.system.DefSnQrCodeSaveVO;
import top.kx.kxss.system.vo.update.system.DefSnQrCodeUpdateVO;

/**
 * <p>
 * 业务实现类
 * 二维码生成
 * </p>
 *
 * <AUTHOR>
 * @date 2024-06-04 11:22:59
 * @create [2024-06-04 11:22:59] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class DefSnQrCodeServiceImpl extends SuperServiceImpl<DefSnQrCodeManager, Long, DefSnQrCode, DefSnQrCodeSaveVO,
        DefSnQrCodeUpdateVO, DefSnQrCodePageQuery, DefSnQrCodeResultVO> implements DefSnQrCodeService {


    @Override
    public boolean save(DefSnQrCode saveQrCode) {
        return superManager.save(saveQrCode);
    }

    @Override
    public DefSnQrCode getOne(LbQueryWrap<DefSnQrCode> wq) {
        return superManager.getOne(wq);
    }

    @Override
    public boolean updateById(DefSnQrCode defSnQrCode) {
        return superManager.updateById(defSnQrCode);
    }
}


