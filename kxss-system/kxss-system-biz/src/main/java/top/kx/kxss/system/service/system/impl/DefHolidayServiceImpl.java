package top.kx.kxss.system.service.system.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.system.HolidayStatusEnum;
import top.kx.kxss.system.entity.system.DefHoliday;
import top.kx.kxss.system.manager.system.DefHolidayManager;
import top.kx.kxss.system.service.system.DefHolidayService;
import top.kx.kxss.system.vo.query.system.DefHolidayPageQuery;
import top.kx.kxss.system.vo.query.system.DefHolidaySyncQuery;
import top.kx.kxss.system.vo.result.system.DefHolidayResultVO;
import top.kx.kxss.system.vo.save.system.DefHolidaySaveVO;
import top.kx.kxss.system.vo.update.system.DefHolidayUpdateVO;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 节假日日期
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-10 18:24:54
 * @create [2024-04-10 18:24:54] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class DefHolidayServiceImpl extends SuperServiceImpl<DefHolidayManager, Long, DefHoliday, DefHolidaySaveVO,
        DefHolidayUpdateVO, DefHolidayPageQuery, DefHolidayResultVO> implements DefHolidayService {


    @Override
    @Transactional
    public Boolean sync(Integer year) {
        String url = "https://api.jiejiariapi.com/v1/holidays/";
        String string = HttpUtil.get(url + year);
        log.info("节假日数据：{}", string);
        ArgumentAssert.isFalse(string.contains("Year not found"), "年份不存在！");
        JSONObject jsonObject = JSON.parseObject(string);
        List<DefHolidaySyncQuery> list = Lists.newArrayList();
        jsonObject.forEach((k, v) -> {
            list.add(JSON.parseObject(v.toString(), DefHolidaySyncQuery.class));
        });
        Map<LocalDate, DefHolidaySyncQuery> holidaySync = list.stream().collect(Collectors.toMap(DefHolidaySyncQuery::getDate, item -> item));
        Map<LocalDate, DefHoliday> holidayMap = list(Wraps.<DefHoliday>lbQ().in(DefHoliday::getDate, holidaySync.keySet()))
                .stream().collect(Collectors.toMap(DefHoliday::getDate, item -> item));
        List<DefHoliday> collect = list.stream()
                .filter(v -> {
                    DefHoliday defHoliday = holidayMap.get(v.getDate());
                    return defHoliday == null;
                })
                .map(v -> {
                    int week = v.getDate()
                            .getDayOfWeek().getValue();
                    return DefHoliday.builder()
                            .name(v.getName())
                            .deleteFlag(0)
                            .date(v.getDate())
                            .isHoliday(true)
                            .status(v.getIsOffDay() ?
                                    (week == 6 || week == 7 ? HolidayStatusEnum.WEEK_REST.getCode()
                                            : HolidayStatusEnum.REST.getCode()) : HolidayStatusEnum.COVER_SHIFT.getCode())
                            .build();
                }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            saveBatch(
                    collect
            );
        }

        collect = list.stream()
                .filter(v -> {
                    DefHoliday defHoliday = holidayMap.get(v.getDate());
                    return defHoliday != null;
                })
                .map(v -> {
                    DefHoliday defHoliday = holidayMap.get(v.getDate());
                    int week = v.getDate()
                            .getDayOfWeek().getValue();
                    DefHoliday build = DefHoliday.builder()
                            .name(v.getName())
                            .deleteFlag(0)
                            .date(v.getDate())
                            .isHoliday(true)
                            .status(v.getIsOffDay() ?
                                    (week == 6 || week == 7 ? HolidayStatusEnum.WEEK_REST.getCode()
                                            : HolidayStatusEnum.REST.getCode()) : HolidayStatusEnum.COVER_SHIFT.getCode())
                            .build();
                    build.setId(defHoliday.getId());
                    return build;
                }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            return superManager.updateBatchById(collect);
        }
        return true;
    }

    @Override
    public Boolean isHoliday(LocalDate localDate) {
        return superManager.count(Wraps.<DefHoliday>lbQ()
                .eq(DefHoliday::getDate, DateUtils.format(localDate, DateUtils.DEFAULT_DATE_FORMAT))
                .eq(DefHoliday::getIsHoliday, true)
                .in(DefHoliday::getStatus, HolidayStatusEnum.REST.getCode(), HolidayStatusEnum.WEEK_REST.getCode()))
                > 0;
    }
}


