package top.kx.kxss.system.service.tenant;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.system.entity.tenant.DefTenantOrg;
import top.kx.kxss.system.vo.query.tenant.DefTenantOrgPageQuery;
import top.kx.kxss.system.vo.result.tenant.DefTenantOrgResultVO;
import top.kx.kxss.system.vo.save.tenant.DefTenantOrgSaveVO;
import top.kx.kxss.system.vo.update.tenant.DefTenantOrgUpdateVO;


/**
 * <p>
 * 业务接口
 * 商户门店信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-07-12 14:20:57
 * @create [2023-07-12 14:20:57] [dou] [代码生成器生成]
 */
public interface DefTenantOrgService extends SuperService<Long, DefTenantOrg, DefTenantOrgSaveVO,
    DefTenantOrgUpdateVO, DefTenantOrgPageQuery, DefTenantOrgResultVO> {

    /**
     * 根据条件查询单个数据
     * @param queryWrap
     * @return
     */
    DefTenantOrg getOne(LbQueryWrap<DefTenantOrg> queryWrap);

    void saveEntity(DefTenantOrgSaveVO saveVO);

    void update(LbUpdateWrap<DefTenantOrg> eq);
}


