package top.kx.kxss.system.service.tenant;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import top.kx.basic.base.service.SuperCacheService;
import top.kx.kxss.system.entity.tenant.DefUserTenantRel;
import top.kx.kxss.system.vo.query.tenant.DefUserTenantRelPageQuery;
import top.kx.kxss.system.vo.result.tenant.DefUserTenantRelResultVO;
import top.kx.kxss.system.vo.save.tenant.DefUserTenantRelSaveVO;
import top.kx.kxss.system.vo.update.tenant.DefUserTenantRelUpdateVO;

import java.util.List;

/**
 * <p>
 * 业务接口
 * 员工
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-27
 */
public interface DefUserTenantRelService extends SuperCacheService<Long, DefUserTenantRel, DefUserTenantRelSaveVO, DefUserTenantRelUpdateVO, DefUserTenantRelPageQuery, DefUserTenantRelResultVO> {
    /**
     * 查询数量
     *
     * @param queryWrapper queryWrapper
     * @return long
     * <AUTHOR>
     * @date 2022/10/28 4:57 PM
     * @create [2022/10/28 4:57 PM ] [tangyh] [初始创建]
     */
    default long count(Wrapper<DefUserTenantRel> queryWrapper) {
        return getSuperManager().count(queryWrapper);
    }

    /**
     * 根据用户id查询员工
     *
     * @param userId 用户id
     * @return
     */
    List<DefUserTenantRelResultVO> listEmployeeByUserId(Long userId);

    /**
     * 查询指定企业指定用户的员工信息
     *
     * @param tenantId 企业id
     * @param userId   用户id
     * @return
     */
    DefUserTenantRel getEmployeeByTenantAndUser(Long tenantId, Long userId);

    /**
     * 设置用户（userId) 的默认企业为 tenantId
     *
     * @param tenantId 企业id
     * @param userId   用户id
     * @return
     */
    boolean updateDefaultTenant(Long tenantId, Long userId);

    boolean updateById(DefUserTenantRel u);

    boolean save(DefUserTenantRel defUserTenantRel);

}
