package top.kx.kxss.system.service.system.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.exception.BizException;
import top.kx.basic.exception.code.ExceptionCode;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.deal.DealOrder;
import top.kx.kxss.system.entity.deal.DealOrderDetail;
import top.kx.kxss.system.entity.system.DealTenantOrgTime;
import top.kx.kxss.system.manager.system.DealTenantOrgTimeManager;
import top.kx.kxss.system.service.system.DealTenantOrgTimeService;
import top.kx.kxss.system.vo.query.system.DealTenantOrgTimePageQuery;
import top.kx.kxss.system.vo.result.system.DealTenantOrgTimeResultVO;
import top.kx.kxss.system.vo.save.system.DealTenantOrgTimeSaveVO;
import top.kx.kxss.system.vo.update.system.DealTenantOrgTimeUpdateVO;

import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 业务实现类
 * 租户-门店团购到期时间
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-21 15:48:08
 * @create [2024-10-21 15:48:08] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class DealTenantOrgTimeServiceImpl extends SuperServiceImpl<DealTenantOrgTimeManager, Long, DealTenantOrgTime, DealTenantOrgTimeSaveVO,
        DealTenantOrgTimeUpdateVO, DealTenantOrgTimePageQuery, DealTenantOrgTimeResultVO> implements DealTenantOrgTimeService {

    @Autowired
    private DistributedLock distributedLock;

    @Override
    public DealTenantOrgTime getOne(LbQueryWrap<DealTenantOrgTime> eq) {
        return superManager.getOne(eq);
    }

    @Override
    protected DealTenantOrgTime updateBefore(DealTenantOrgTimeUpdateVO model) {
        ArgumentAssert.isFalse(checkTenantOrg(model.getTenantId(), model.getOrgId(), model.getId())
                , "门店团购记录已存在！");
        return super.updateBefore(model);
    }

    @Override
    protected DealTenantOrgTime saveBefore(DealTenantOrgTimeSaveVO dealTenantOrgTimeSaveVO) {
        ArgumentAssert.isFalse(checkTenantOrg(dealTenantOrgTimeSaveVO.getTenantId(), dealTenantOrgTimeSaveVO.getOrgId(), null)
                , "门店团购记录已存在！");
        return super.saveBefore(dealTenantOrgTimeSaveVO);
    }

    private Boolean checkTenantOrg(Long tenantId, Long orgId, Long id) {
        return superManager.count(Wraps.<DealTenantOrgTime>lbQ().ne(DealTenantOrgTime::getId, id)
                .eq(DealTenantOrgTime::getTenantId, tenantId)
                .eq(DealTenantOrgTime::getOrgId, orgId)) > 0;
    }

    @Override
    public boolean grant(DealOrder dealOrder, List<DealOrderDetail> orderDetailList, LocalDateTime createdTime) {
        if (CollUtil.isEmpty(orderDetailList)) {
            return false;
        }
        boolean lock = false;
        try {
            lock = distributedLock.lock(dealOrder.getTenantId() + "_" + dealOrder.getCreatedOrgId() + "_GRANT_DEAL_RECHARGE", 3);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            int sumDays = orderDetailList.stream().mapToInt(DealOrderDetail::getDays)
                    .filter(ObjectUtil::isNotNull).sum();
            int giftDays = orderDetailList.stream().mapToInt(DealOrderDetail::getGiftDays)
                    .filter(ObjectUtil::isNotNull).sum();
            DealTenantOrgTime dealTenantOrgTime = getOne(Wraps.<DealTenantOrgTime>lbQ()
                    .eq(DealTenantOrgTime::getTenantId, dealOrder.getTenantId())
                    .eq(DealTenantOrgTime::getOrgId, dealOrder.getCreatedOrgId())
                    .eq(DealTenantOrgTime::getDeleteFlag, 0));
            if (dealTenantOrgTime == null) {
                LocalDateTime localDateTime = LocalDateTime.now().withSecond(0);
                LocalDateTime dateTime = LocalDateTime.of(2024, 9, 15, 0, 0, 0);
                if (createdTime.isBefore(dateTime)) {
                    localDateTime = LocalDateTime.of(2024, 11, 30, 23, 59, 59);
                }
                dealTenantOrgTime = DealTenantOrgTime.builder()
                        .orgId(dealOrder.getCreatedOrgId())
                        .tenantId(dealOrder.getTenantId())
                        .totalRechargeAmount(BigDecimal.ZERO)
                        .expireTime(localDateTime)
                        .totalRechargeDays(0).totalRechargeTimes(0).totalGiftDays(0)
                        .build();
            }
            int totalDays = sumDays + giftDays;
            dealTenantOrgTime.setTotalRechargeAmount(dealTenantOrgTime.getTotalRechargeAmount().add(dealOrder.getPayment()));
            dealTenantOrgTime.setTotalRechargeTimes(dealTenantOrgTime.getTotalRechargeTimes() + 1);
            dealTenantOrgTime.setTotalRechargeDays(dealTenantOrgTime.getTotalRechargeDays() + totalDays);
            dealTenantOrgTime.setTotalGiftDays(dealTenantOrgTime.getTotalGiftDays() + giftDays);
            //未过期
            if (LocalDateTime.now().isBefore(dealTenantOrgTime.getExpireTime())) {
                dealTenantOrgTime.setExpireTime(dealTenantOrgTime.getExpireTime().plusDays(totalDays));
            } else {
                dealTenantOrgTime.setExpireTime(LocalDateTime.now().plusDays(totalDays)
                        .withHour(23).withMinute(59).withSecond(59).withNano(0));
            }
            return superManager.saveOrUpdate(dealTenantOrgTime);
        } finally {
            if (lock) {
                distributedLock.releaseLock(dealOrder.getTenantId() + "_" + dealOrder.getCreatedOrgId() + "_GRANT_DEAL_RECHARGE");
            }
        }
    }

    @Override
    public LocalDateTime getExpireTime(LocalDateTime createdTime) {
        DealTenantOrgTime dealTenantOrgTime = superManager.getOne(Wraps.<DealTenantOrgTime>lbQ()
                .eq(DealTenantOrgTime::getTenantId, ContextUtil.getTenantId())
                .eq(DealTenantOrgTime::getOrgId, ContextUtil.getCurrentCompanyId())
                .eq(DealTenantOrgTime::getDeleteFlag, 0));
        //当记录不为空时直接返回过期时间
        //当记录为空时
        // 门店创建时间在2024-09-15 00:00:00之前，则到期时间在2024-11-30 23:59:59，
        // 否则未配置
        if (dealTenantOrgTime != null) {
            return dealTenantOrgTime.getExpireTime().withNano(0);
        }
        LocalDateTime dateTime = LocalDateTime.of(2024, 9, 15, 0, 0, 0);
        if (createdTime.isBefore(dateTime)) {
            return LocalDateTime.of(2024, 11, 30, 23, 59, 59);
        }
        return null;
    }

    @Override
    public boolean isAuthExpire(LocalDateTime createdTime) {
        LocalDateTime expireTime = getExpireTime(createdTime);
        if (expireTime == null) {
            return true;
        }
        return LocalDateTime.now().withNano(0).isAfter(expireTime);

    }

    @Override
    public long getExpireDays(LocalDateTime createdTime) {
        LocalDateTime expireTime = getExpireTime(createdTime);
        if (expireTime == null) {
            return 0;
        }
        LocalDateTime nowTime = LocalDateTime.now();
        long days = Duration.between(nowTime, expireTime).toDays();
        if (days == 0 &&
                nowTime.toLocalDate().equals(expireTime.toLocalDate())) {
            return LocalDateTime.now().withNano(0).isBefore(expireTime) ? 1 : -1;
        }
        return days;
    }
}


