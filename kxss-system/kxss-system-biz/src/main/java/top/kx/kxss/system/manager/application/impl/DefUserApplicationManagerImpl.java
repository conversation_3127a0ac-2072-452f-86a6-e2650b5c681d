package top.kx.kxss.system.manager.application.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.entity.application.DefUserApplication;
import top.kx.kxss.system.manager.application.DefUserApplicationManager;
import top.kx.kxss.system.mapper.application.DefUserApplicationMapper;

/**
 * <p>
 * 通用业务实现类
 * 用户的默认应用
 * </p>
 *
 * <AUTHOR>
 * @date 2022-03-06
 * @create [2022-03-06] [zuihou] [代码生成器生成]
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefUserApplicationManagerImpl extends SuperManagerImpl<DefUserApplicationMapper, DefUserApplication> implements DefUserApplicationManager {
}
