package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.system.DefArea;
import top.kx.kxss.system.vo.query.system.DefAreaPageQuery;
import top.kx.kxss.system.vo.result.system.DefAreaResultVO;
import top.kx.kxss.system.vo.save.system.DefAreaSaveVO;
import top.kx.kxss.system.vo.update.system.DefAreaUpdateVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 业务接口
 * 地区表
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-13
 */
public interface DefAreaService extends SuperService<Long, DefArea, DefAreaSaveVO, DefAreaUpdateVO, DefAreaPageQuery, DefAreaResultVO> {
    /**
     * 查询地区的树结构
     *
     * @param pageQuery 查询条件
     * @return 树结构
     */
    List<DefArea> findTree(DefAreaPageQuery pageQuery);

    /**
     * 检测地区代码是否存在
     *
     * @param code 地区代码
     * @param id   地区id
     * @return 是否存在
     */
    Boolean check(String code, Long id);

    /**
     * 地区
     *
     * @param parentId 父id
     * @return
     */
    List<DefArea> findLazyList(Long parentId);

    /**
     * 下载地区json文件
     *
     * @param treeGrade 层级
     * @param request   请求
     * @param response  响应
     */
    void downloadJson(Integer treeGrade, HttpServletRequest request, HttpServletResponse response);

    /**
     * 根据id查询待回显参数
     *
     * @param ids 唯一键（可能不是主键ID)
     * @return
     */
    Map<Serializable, Object> findByIds(Set<Serializable> ids);
}
