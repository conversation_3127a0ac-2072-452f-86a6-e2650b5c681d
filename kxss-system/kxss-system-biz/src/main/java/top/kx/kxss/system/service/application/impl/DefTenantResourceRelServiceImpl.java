package top.kx.kxss.system.service.application.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.application.DefTenantResourceRel;
import top.kx.kxss.system.manager.application.DefTenantResourceRelManager;
import top.kx.kxss.system.service.application.DefTenantResourceRelService;
import top.kx.kxss.system.vo.query.application.DefTenantResourceRelPageQuery;
import top.kx.kxss.system.vo.result.application.DefTenantResourceRelResultVO;
import top.kx.kxss.system.vo.save.application.DefTenantResourceRelSaveVO;
import top.kx.kxss.system.vo.update.application.DefTenantResourceRelUpdateVO;

import java.util.List;

/**
 * <p>
 * 业务实现类
 * 用户的默认应用
 * </p>
 *
 * <AUTHOR>
 * @date 2022-03-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
@DS(DsConstant.DEFAULTS)
public class DefTenantResourceRelServiceImpl extends SuperServiceImpl<DefTenantResourceRelManager, Long, DefTenantResourceRel, DefTenantResourceRelSaveVO, DefTenantResourceRelUpdateVO, DefTenantResourceRelPageQuery, DefTenantResourceRelResultVO> implements DefTenantResourceRelService {

    @Override
    public boolean remove(LbQueryWrap<DefTenantResourceRel> eq) {
        return superManager.remove(eq);
    }

    @Override
    public void deleteByTenantId(List<Long> ids) {
        superManager.deleteByTenantId(ids);
    }
}
