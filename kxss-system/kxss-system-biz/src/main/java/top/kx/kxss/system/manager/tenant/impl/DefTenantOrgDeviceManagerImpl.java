package top.kx.kxss.system.manager.tenant.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.tenant.DefTenantOrgDevice;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.tenant.DefTenantOrgDeviceManager;
import top.kx.kxss.system.mapper.tenant.DefTenantOrgDeviceMapper;

/**
 * <p>
 * 通用业务实现类
 * 商户门店设备信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-08-17 10:28:19
 * @create [2023-08-17 10:28:19] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefTenantOrgDeviceManagerImpl extends SuperManagerImpl<DefTenantOrgDeviceMapper, DefTenantOrgDevice> implements DefTenantOrgDeviceManager {

}


