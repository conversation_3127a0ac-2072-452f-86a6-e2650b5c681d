package top.kx.kxss.system.service.tenant;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.system.entity.tenant.DefTenantDatasourceConfigRel;
import top.kx.kxss.system.vo.query.tenant.DefTenantDatasourceConfigRelPageQuery;
import top.kx.kxss.system.vo.result.tenant.DefTenantDatasourceConfigRelResultVO;
import top.kx.kxss.system.vo.save.tenant.DefTenantDatasourceConfigRelSaveVO;
import top.kx.kxss.system.vo.update.tenant.DefTenantDatasourceConfigRelUpdateVO;

/**
 * <p>
 * 业务接口
 * 数据源
 * </p>
 *
 * <AUTHOR>
 * @date 2021-09-13
 */
public interface DefTenantDatasourceConfigRelService extends SuperService<Long, DefTenantDatasourceConfigRel, DefTenantDatasourceConfigRelSaveVO, DefTenantDatasourceConfigRelUpdateVO, DefTenantDatasourceConfigRelPageQuery, DefTenantDatasourceConfigRelResultVO> {

    void remove(LbQueryWrap<DefTenantDatasourceConfigRel> in);
}
