package top.kx.kxss.system.service.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.kxss.system.service.system.TableRelationService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.system.manager.system.TableRelationManager;
import top.kx.kxss.system.entity.system.TableRelation;
import top.kx.kxss.system.vo.save.system.TableRelationSaveVO;
import top.kx.kxss.system.vo.update.system.TableRelationUpdateVO;
import top.kx.kxss.system.vo.result.system.TableRelationResultVO;
import top.kx.kxss.system.vo.query.system.TableRelationPageQuery;

/**
 * <p>
 * 业务实现类
 * 表间关联关系表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-11 11:11:51
 * @create [2023-05-11 11:11:51] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class TableRelationServiceImpl extends SuperServiceImpl<TableRelationManager, Long, TableRelation, TableRelationSaveVO,
    TableRelationUpdateVO, TableRelationPageQuery, TableRelationResultVO> implements TableRelationService {


}


