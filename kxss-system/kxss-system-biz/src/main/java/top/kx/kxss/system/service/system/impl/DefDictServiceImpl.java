package top.kx.kxss.system.service.system.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baidu.fsg.uid.UidGenerator;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.cache.repository.CachePlusOps;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.model.cache.CacheHashKey;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.common.BaseDict;
import top.kx.kxss.common.cache.tenant.base.DictCacheKeyBuilder;
import top.kx.kxss.common.constant.DefValConstants;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.system.DictClassifyEnum;
import top.kx.kxss.system.entity.application.DefResource;
import top.kx.kxss.system.entity.application.DefTenantResourceRel;
import top.kx.kxss.system.entity.system.DefDict;
import top.kx.kxss.system.manager.application.DefTenantResourceRelManager;
import top.kx.kxss.system.manager.system.DefDictManager;
import top.kx.kxss.system.service.application.DefResourceApiService;
import top.kx.kxss.system.service.application.DefResourceService;
import top.kx.kxss.system.service.system.DefDictService;
import top.kx.kxss.system.vo.query.system.DefDictPageQuery;
import top.kx.kxss.system.vo.result.system.DefDictResultVO;
import top.kx.kxss.system.vo.save.application.DefResourceApiSaveVO;
import top.kx.kxss.system.vo.save.application.DefResourceSaveVO;
import top.kx.kxss.system.vo.save.system.DefDictItemSaveVO;
import top.kx.kxss.system.vo.save.system.DefDictSaveVO;
import top.kx.kxss.system.vo.update.system.DefDictItemUpdateVO;
import top.kx.kxss.system.vo.update.system.DefDictUpdateVO;

import java.util.ArrayList;
import java.util.List;

import static java.util.stream.Collectors.toList;

/**
 * <p>
 * 业务实现类
 * 字典
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-04
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
@DS(DsConstant.DEFAULTS)
public class DefDictServiceImpl extends SuperServiceImpl<DefDictManager, Long, DefDict, DefDictSaveVO, DefDictUpdateVO, DefDictPageQuery, DefDictResultVO> implements DefDictService {

    private final CachePlusOps cachePlusOps;
    private final UidGenerator uidGenerator;
    private final DefResourceService defResourceService;
    private final DefResourceApiService defResourceApiService;
    private final DefTenantResourceRelManager defTenantResourceRelManager;

    @Override
    public boolean checkByKey(String key, Long id) {
        ArgumentAssert.notEmpty(key, "请填写字典标识");
        return superManager.count(Wraps.<DefDict>lbQ().eq(DefDict::getKey, key)
                .eq(DefDict::getParentId, DefValConstants.PARENT_ID).ne(DefDict::getId, id)) > 0;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public DefDict save(DefDictSaveVO dictSaveVO) {
        if (dictSaveVO.getIsDefault() == null) {
            dictSaveVO.setIsDefault(false);
        }
        ArgumentAssert.isFalse(checkByKey(dictSaveVO.getKey(), null), "字典标识已存在");
        DefDict dict = BeanPlusUtil.toBean(dictSaveVO, DefDict.class);
        dict.setClassify(DictClassifyEnum.SYSTEM.getCode());
        dict.setParentId(DefValConstants.PARENT_ID);
        superManager.save(dict);
        saveItem(dictSaveVO.getInsertList(), dict);
        return dict;
    }

    private void saveItem(List<DefDictItemSaveVO> insertList, DefDict dict) {
        if (CollUtil.isNotEmpty(insertList)) {
            List<DefDict> itemList = new ArrayList<>();
            insertList.forEach(insert -> {
                DefDict item = new DefDict();
                BeanPlusUtil.copyProperties(insert, item);
                item.setParentId(dict.getId());
                item.setParentKey(dict.getKey());
                item.setClassify(DictClassifyEnum.SYSTEM.getCode());
                itemList.add(item);

                CacheHashKey hashKey = DictCacheKeyBuilder.builder(item.getParentKey(), item.getKey());
                cachePlusOps.hSet(hashKey, item.getName());
            });
            superManager.saveBatch(itemList);
        }
    }

    private void updateItem(List<DefDictItemUpdateVO> updateInsert, DefDict dict, DefDict old) {
        if (CollUtil.isNotEmpty(updateInsert)) {
            List<DefDict> itemList = new ArrayList<>();
            updateInsert.forEach(insert -> {
                DefDict item = new DefDict();
                BeanPlusUtil.copyProperties(insert, item);
                item.setParentId(dict.getId());
                item.setParentKey(dict.getKey());
                item.setClassify(DictClassifyEnum.SYSTEM.getCode());
                itemList.add(item);

                // 淘汰旧缓存
                CacheHashKey oldHashKey = DictCacheKeyBuilder.builder(item.getParentKey(), old.getKey());
                cachePlusOps.hDel(oldHashKey);
                // 设置新缓存
                CacheHashKey hashKey = DictCacheKeyBuilder.builder(item.getParentKey(), item.getKey());
                cachePlusOps.hSet(hashKey, item.getName());
            });
            superManager.updateBatchById(itemList);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteDict(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }
        List<DefDict> list = superManager.listByIds(ids);
        if (CollUtil.isEmpty(list)) {
            return false;
        }
        // 删除 字典条目
        superManager.remove(Wraps.<DefDict>lbQ().in(DefDict::getParentId, ids));

//        删除字典
        boolean flag = removeByIds(ids);
        CacheHashKey[] typeKeys = list.stream().map(type -> DictCacheKeyBuilder.builder(type.getKey())).toArray(CacheHashKey[]::new);
        cachePlusOps.del(typeKeys);
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DefDict updateById(DefDictUpdateVO dictUpdateVO) {
        ArgumentAssert.isFalse(checkByKey(dictUpdateVO.getKey(), dictUpdateVO.getId()), "标识【{}】重复", dictUpdateVO.getKey());
        if (dictUpdateVO.getIsDefault() == null) {
            dictUpdateVO.setIsDefault(false);
        }
        DefDict old = getById(dictUpdateVO.getId());

        DefDict dict = BeanPlusUtil.toBean(dictUpdateVO, DefDict.class);
        dict.setParentId(DefValConstants.PARENT_ID);
        dict.setClassify(DictClassifyEnum.SYSTEM.getCode());
        superManager.updateById(dict);

        saveItem(dictUpdateVO.getInsertList(), dict);
        updateItem(dictUpdateVO.getUpdateList(), dict, old);
        superManager.removeItemByIds(dictUpdateVO.getDeleteList());

        return dict;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public DefDict copy(Long id) {
        DefDict old = getById(id);
        ArgumentAssert.notNull(old, "字典不存在或已被删除，请刷新重试");

        DefDict dict = BeanPlusUtil.toBean(old, DefDict.class);
        dict.setId(null);
        dict.setKey(dict.getKey() + "_copy");
        dict.setParentId(DefValConstants.PARENT_ID);
        dict.setClassify(DictClassifyEnum.SYSTEM.getCode());
        superManager.save(dict);

        LbQueryWrap<DefDict> wrap = Wraps.<DefDict>lbQ().eq(DefDict::getParentId, old.getId());
        List<DefDict> itemList = superManager.list(wrap);
        itemList.forEach(item -> {
            item.setId(null);
            item.setParentId(dict.getId());
            item.setParentKey(dict.getKey());
        });
        superManager.saveBatch(itemList);
        return dict;
    }


    @Override
    public List<DefDict> findItemByDictId(Long id) {
        return list(Wraps.<DefDict>lbQ().eq(DefDict::getParentId, id));
    }

    /**
     * 创建菜单
     *
     * @param key
     * @return
     */
    @Override
    @Transactional
    public Boolean registToMenu(String key) {
        DefDict dict = superManager.getOne(Wraps.<DefDict>lbQ().eq(DefDict::getKey, key));
        String baseCode = "basic:registMenu:" + dict.getKey();

        DefResource existsResource = defResourceService.getSuperManager().getOne(Wraps.<DefResource>lbQ().eq(DefResource::getCode, baseCode));
        ArgumentAssert.isNull(existsResource, "菜单已注册");

        DefResourceSaveVO menu = new DefResourceSaveVO();
//        menu.setId(uidGenerator.getUid());
        menu.setApplicationId(1L);  //商户运营后台
        menu.setCode(baseCode);
        menu.setName(dict.getName());
        menu.setResourceType("20"); //类型;[20-菜单 30-视图 40-功能 50-字段 60-数据]
        menu.setParentId(144313439471271936L);   //挂在 基础数据 菜单下
        menu.setOpenWith("01");
        menu.setDescribe("");
        menu.setPath("/basic/registMenu/" + dict.getId());
//        menu.setPath("/basic/registMenu?code=" + dict.getKey());
        menu.setComponent("/basic/registMenu/index");
        menu.setRedirect("");
        menu.setIcon("ant-design:tags-outlined");
        menu.setIsGeneral(false);
        menu.setState(true);
        menu.setSortValue(30);
        menu.setTreePath("/143911967403278336/");
        menu.setTreeGrade(1);
//        menu.setCreatedBy(ContextUtil.getUserId());
//        menu.setCreatedTime(LocalDateTime.now());
//        menu.setUpdatedBy(menu.getCreatedBy());
//        menu.setUpdatedTime(menu.getCreatedTime());

        DefResource parent = defResourceService.saveWithCache(menu);
        if (parent == null) {
            return false;
        }
        this.createRel(parent);
        this.createSubMenu(parent, "新增", "add", 1);
        this.createSubMenu(parent, "编辑", "edit", 2);
        this.createSubMenu(parent, "复制", "copy", 3);
        this.createSubMenu(parent, "删除", "delete", 4);
        this.createSubMenu(parent, "查看", "view", 5);
        return true;
    }

    /**
     * 创建增删改查子菜单
     *
     * @param parent
     * @param name
     * @param code
     * @param sortValue
     * @return
     */
    private DefResource createSubMenu(DefResource parent, String name, String code, Integer sortValue) {
        DefResourceSaveVO subMenu = new DefResourceSaveVO();
//        subMenu.setId(uidGenerator.getUid());
        subMenu.setApplicationId(1L);  //商户运营后台
        subMenu.setCode(parent.getCode() + ":" + code);
        subMenu.setName(name);
        subMenu.setResourceType("40"); //类型;[20-菜单 30-视图 40-功能 50-字段 60-数据]
        subMenu.setParentId(parent.getId());
        subMenu.setOpenWith("01");
        subMenu.setTreePath("/143911967403278336/" + parent.getId() + "/");
        subMenu.setTreeGrade(2);
        subMenu.setSortValue(sortValue);
        subMenu.setState(true);
        subMenu.setIsGeneral(false);
//        subMenu.setCreatedBy(ContextUtil.getUserId());
//        subMenu.setCreatedTime(LocalDateTime.now());
//        subMenu.setUpdatedBy(subMenu.getCreatedBy());
//        subMenu.setUpdatedTime(subMenu.getCreatedTime());

        DefResource resource = defResourceService.saveWithCache(subMenu);
        switch (code) {
            case "add": {
                DefResourceApiSaveVO add = this.createApi(resource);
                add.setRequestMethod("POST");
                add.setName(parent.getName() + "-新增");
                add.setUri("/system/defDictItem");
                defResourceApiService.save(add);
                break;
            }
            case "edit": {
                DefResourceApiSaveVO edit = this.createApi(resource);
                edit.setRequestMethod("PUT");
                edit.setName(parent.getName() + "-修改");
                edit.setUri("/system/defDictItem");
                defResourceApiService.save(edit);
                break;
            }
            case "copy": {
                DefResourceApiSaveVO copy = this.createApi(resource);
                copy.setRequestMethod("POST");
                copy.setName(parent.getName() + "-复制");
                copy.setUri("/system/defDictItem/copy");
                defResourceApiService.save(copy);
                break;
            }
            case "delete": {
                DefResourceApiSaveVO delete = this.createApi(resource);
                delete.setRequestMethod("DELETE");
                delete.setName(parent.getName() + "-删除");
                delete.setUri("/system/defDictItem");
                defResourceApiService.save(delete);
                break;
            }
            case "view": {
                DefResourceApiSaveVO page = this.createApi(resource);
                page.setRequestMethod("POST");
                page.setName(parent.getName() + "-分页列表查询");
                page.setUri("/system/defDictItem/page");
                defResourceApiService.save(page);

                DefResourceApiSaveVO detail = this.createApi(resource);
                detail.setRequestMethod("POST");
                detail.setName(parent.getName() + "-查询单体详情");
                detail.setUri("/system/defDictItem/detail");
                defResourceApiService.save(detail);
                break;
            }
        }
        this.createRel(resource);
        return resource;
    }

    /**
     * 创建资源api关联
     *
     * @param resource
     * @return
     */
    private DefResourceApiSaveVO createApi(DefResource resource) {
        DefResourceApiSaveVO resourceApi = new DefResourceApiSaveVO();
        resourceApi.setResourceId(resource.getId());
        resourceApi.setController("DefDictItemController");
        resourceApi.setSpringApplicationName("kxss-system-server");
        resourceApi.setIsInput(false);
//        resourceApi.setCreatedBy(ContextUtil.getUserId());
//        resourceApi.setCreatedTime(LocalDateTime.now());
//        resourceApi.setUpdatedBy(resourceApi.getCreatedBy());
//        resourceApi.setUpdatedTime(resourceApi.getCreatedTime());

        return resourceApi;
    }

    private DefTenantResourceRel createRel(DefResource resource) {
        DefTenantResourceRel relSaveVO = new DefTenantResourceRel();
        relSaveVO.setTenantId(1L);
        relSaveVO.setApplicationId(1L);
        relSaveVO.setResourceId(resource.getId());
        defTenantResourceRelManager.save(relSaveVO);

        return relSaveVO;
    }

    @Override
    public List<BaseDict> findItemByDictKeys(List<String> keys) {
        List<DefDict> defDictList = list(Wraps.<DefDict>lbQ().in(DefDict::getKey, keys).eq(DefDict::getParentId, 0));
        if (CollUtil.isEmpty(defDictList)) {
            return null;
        }
        return defDictList.stream().map(defDict -> {
            return BeanUtil.toBean(defDict, BaseDict.class);
        }).collect(toList());
    }

    @Override
    public List<BaseDict> findParentItemByDictKeys(List<String> keys, List<BaseDict> defDicts) {
        List<DefDict> defDictList = list(Wraps.<DefDict>lbQ().in(DefDict::getParentKey, keys).ne(DefDict::getParentId, 0));
        if (CollUtil.isEmpty(defDictList)) {
            return null;
        }
        List<BaseDict> list = new ArrayList<>();
        for (DefDict item : defDictList) {
            List<BaseDict> collect = defDicts.stream().filter(defDict -> ObjectUtil.equal(defDict.getKey(), item.getParentKey())).collect(toList());
            if (CollUtil.isEmpty(collect)) {
                continue;
            }
            BaseDict baseItem = new BaseDict();
            BeanUtil.copyProperties(item, baseItem);
            baseItem.setParentId(collect.get(0).getId());
            list.add(baseItem);
        }
        return list;
    }

    @Override
    public List<String> echoList(String parentKey, List<String> keys) {
        List<DefDict> defDictList = list(Wraps.<DefDict>lbQ().eq(DefDict::getParentKey, parentKey)
                .ne(DefDict::getParentId, 0));
        if (CollUtil.isEmpty(defDictList)) {
            return null;
        }
        List<String> list = new ArrayList<>();
        for (DefDict item : defDictList) {
            if (keys.contains(item.getKey())) {
                list.add(item.getName());
            }
        }
        return list;
    }
}
