package top.kx.kxss.system.mapper.system;

import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.system.entity.system.DealTenantOrgTime;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * Mapper 接口
 * 租户-门店团购到期时间
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-21 15:48:08
 * @create [2024-10-21 15:48:08] [dou] [代码生成器生成]
 */
@Repository
public interface DealTenantOrgTimeMapper extends SuperMapper<DealTenantOrgTime> {

}


