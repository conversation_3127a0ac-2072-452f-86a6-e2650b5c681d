package top.kx.kxss.system.service.tenant.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperCacheServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.exception.BizException;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.file.service.DefAppendixService;
import top.kx.kxss.model.enumeration.system.DefTenantStatusEnum;
import top.kx.kxss.model.enumeration.system.TenantConnectTypeEnum;
import top.kx.kxss.system.entity.tenant.DefTenant;
import top.kx.kxss.system.entity.tenant.DefTenantDatasourceConfigRel;
import top.kx.kxss.system.entity.tenant.DefTenantOrg;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.enumeration.tenant.DefTenantRegisterTypeEnum;
import top.kx.kxss.system.manager.tenant.DefTenantManager;
import top.kx.kxss.system.service.application.DefTenantApplicationRelService;
import top.kx.kxss.system.service.application.DefTenantResourceRelService;
import top.kx.kxss.system.service.tenant.DefTenantDatasourceConfigRelService;
import top.kx.kxss.system.service.tenant.DefTenantOrgService;
import top.kx.kxss.system.service.tenant.DefTenantService;
import top.kx.kxss.system.service.tenant.DefUserService;
import top.kx.kxss.system.strategy.InitSystemContext;
import top.kx.kxss.system.vo.query.tenant.DefTenantPageQuery;
import top.kx.kxss.system.vo.result.tenant.DefTenantResultVO;
import top.kx.kxss.system.vo.save.tenant.DefTenantInitVO;
import top.kx.kxss.system.vo.save.tenant.DefTenantSaveVO;
import top.kx.kxss.system.vo.update.tenant.DefTenantUpdateVO;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 企业
 * </p>
 *
 * <AUTHOR>
 * @date 2021-09-13
 */
@Slf4j
@Service
@DS(DsConstant.DEFAULTS)
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class DefTenantServiceImpl extends SuperCacheServiceImpl<DefTenantManager, Long, DefTenant, DefTenantSaveVO, DefTenantUpdateVO, DefTenantPageQuery, DefTenantResultVO> implements DefTenantService {
    private final InitSystemContext initSystemContext;
    private final DefUserService defUserService;
    private final DefAppendixService appendixService;
    private final DefTenantDatasourceConfigRelService defTenantDatasourceConfigRelManager;
    private final DefTenantApplicationRelService defTenantApplicationRelManager;
    private final DefTenantResourceRelService defTenantResourceRelManager;
    private final DefTenantOrgService tenantOrgService;


    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        return superManager.findByIds(ids.stream().map(Convert::toLong).collect(Collectors.toSet()));
    }

    @Override
    public boolean check(String tenantCode) {
        return superManager.count(Wraps.<DefTenant>lbQ().eq(DefTenant::getCode, tenantCode)) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DefTenant register(DefTenantSaveVO defTenantSaveVO) {
        // 1， 保存租户 (默认库)
        DefTenant tenant = BeanPlusUtil.toBean(defTenantSaveVO, DefTenant.class);
        tenant.setStatus(DefTenantStatusEnum.WAITING.getCode());
        tenant.setRegisterType(DefTenantRegisterTypeEnum.REGISTER);
        tenant.setCode(RandomUtil.randomNumbers(8));
        tenant.setReadonly(false);
        tenant.setReadonly(false);
        DefUser result = defUserService.getByIdCache(ContextUtil.getUserId());
        if (result != null) {
            tenant.setCreatedName(result.getNickName());
        }

        superManager.save(tenant);
        appendixService.save(tenant.getId(), defTenantSaveVO.getLogos());
        return tenant;
    }

    @Override
    protected DefTenant saveBefore(DefTenantSaveVO defTenantSaveVO) {
        // 1， 保存租户 (默认库)
        DefTenant tenant = BeanPlusUtil.toBean(defTenantSaveVO, DefTenant.class);
        tenant.setStatus(DefTenantStatusEnum.WAIT_INIT_SCHEMA.getCode());
        tenant.setRegisterType(DefTenantRegisterTypeEnum.CREATE);
        tenant.setReadonly(false);
        if (StrUtil.isEmpty(tenant.getCreatedName())) {
            DefUser result = defUserService.getByIdCache(ContextUtil.getUserId());
            if (result != null) {
                tenant.setCreatedName(result.getNickName());
            }
        }
        return tenant;
    }

    @Override
    protected void saveAfter(DefTenantSaveVO defTenantSaveVO, DefTenant defTenant) {
        appendixService.save(defTenant.getId(), defTenantSaveVO.getLogos());

    }

    @Override
    protected void updateAfter(DefTenantUpdateVO defTenantUpdateVO, DefTenant defTenant) {
        appendixService.save(defTenant.getId(), defTenantUpdateVO.getLogos());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean initData(DefTenantInitVO tenantConnect) {
        return initSystemContext.initData(tenantConnect) && updateTenantStatus(tenantConnect);
    }

    private Boolean updateTenantStatus(DefTenantInitVO initVO) {
        Boolean flag = superManager.update(Wraps.<DefTenant>lbU().set(DefTenant::getStatus, DefTenantStatusEnum.WAIT_INIT_DATASOURCE.getCode())
                .set(DefTenant::getConnectType, initVO.getConnectType())
                .eq(DefTenant::getId, initVO.getId()));
        superManager.delCache(initVO.getId());
        defTenantApplicationRelManager.grantGeneralApplication(initVO.getId());
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(List<Long> ids) {
        appendixService.removeByBizId(ids);
        defTenantDatasourceConfigRelManager.remove(Wraps.<DefTenantDatasourceConfigRel>lbQ().in(DefTenantDatasourceConfigRel::getTenantId, ids));
        defTenantApplicationRelManager.deleteByTenantId(ids);
        defTenantResourceRelManager.deleteByTenantId(ids);
        return removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteAll(List<Long> ids) {
        appendixService.removeByBizId(ids);
        removeByIds(ids);
        defTenantDatasourceConfigRelManager.remove(Wraps.<DefTenantDatasourceConfigRel>lbQ().in(DefTenantDatasourceConfigRel::getTenantId, ids));
        defTenantApplicationRelManager.deleteByTenantId(ids);
        defTenantResourceRelManager.deleteByTenantId(ids);
        return initSystemContext.delete(ids);
    }

    @Override
    public List<DefTenant> findNormalTenant() {
        return list(Wraps.<DefTenant>lbQ().eq(DefTenant::getStatus, DefTenantStatusEnum.NORMAL));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatus(Long id, String status, String reviewComments) {
        ArgumentAssert.notNull(id, "请传递正确的企业ID");
        ArgumentAssert.notNull(status, "请传递正确的状态值");

        if (DefTenantStatusEnum.AGREED.eq(status)) {
            DefTenantInitVO tenantConnect = new DefTenantInitVO();
            tenantConnect.setConnectType(TenantConnectTypeEnum.SYSTEM);
            tenantConnect.setId(id);
            initSystemContext.initData(tenantConnect);

            defTenantApplicationRelManager.grantGeneralApplication(id);
            status = DefTenantStatusEnum.NORMAL.getCode();
        }

        boolean update = superManager.update(Wraps.<DefTenant>lbU().set(DefTenant::getStatus, status)
                .set(DefTenant::getReviewComments, reviewComments)
                .eq(DefTenant::getId, id));
        superManager.delCache(id);
        return update;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateState(Long id, Boolean state) {
        ArgumentAssert.notNull(id, "请选择正确的企业进行修改");
        ArgumentAssert.notNull(state, "请传递正确的状态值");
        // 演示环境专用标识，用于WriteInterceptor拦截器判断演示环境需要禁止用户执行sql，若您无需搭建演示环境，可以删除下面一行代码
        ContextUtil.setStop();
        boolean update = superManager.update(Wraps.<DefTenant>lbU().set(DefTenant::getState, state).eq(DefTenant::getId, id));
        superManager.delCache(id);
        //更新租户状态
        tenantOrgService.update(Wraps.<DefTenantOrg>lbU().set(DefTenantOrg::getTenantState, state).eq(DefTenantOrg::getTenantId, id));
        return update;
    }

    @Override
    public List<DefTenantResultVO> listTenantByUserId(Long userId) {
        return superManager.listTenantByUserId(userId);
    }

    /**
     * 检查租户状态是否正常
     *
     * @param tenantId
     */
    @Override
    public void checkTenantIdState(DefUser defUser, Long tenantId) {
        DefTenant tenant = getById(tenantId);
        if (!tenant.getState()) {
            throw new BizException("当前商户状态异常，请联系授权方！");
        }
        if (tenant.getExpirationTime() != null && tenant.getExpirationTime().isBefore(LocalDateTime.now())) {
            throw new BizException("当前商户已过期，请联系授权方！");
        }
    }

    @Override
    public DefTenant getOne(LbQueryWrap<DefTenant> eq) {

        return superManager.getOne(eq);
    }

}
