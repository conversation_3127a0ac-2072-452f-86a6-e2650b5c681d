package top.kx.kxss.system.service.system.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.cache.repository.CachePlusOps;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.model.cache.CacheHashKey;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.common.cache.tenant.base.DictCacheKeyBuilder;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.system.DictClassifyEnum;
import top.kx.kxss.system.entity.system.DefDict;
import top.kx.kxss.system.manager.system.DefDictManager;
import top.kx.kxss.system.service.system.DefDictItemService;
import top.kx.kxss.system.vo.query.system.DefDictItemPageQuery;
import top.kx.kxss.system.vo.result.system.DefDictItemResultVO;
import top.kx.kxss.system.vo.save.system.DefDictItemSaveVO;
import top.kx.kxss.system.vo.update.system.DefDictItemUpdateVO;

import java.time.LocalDateTime;
import java.util.Collection;

/**
 * <p>
 * 业务实现类
 * 字典
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-04
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
@DS(DsConstant.DEFAULTS)
public class DefDictItemServiceImpl extends SuperServiceImpl<DefDictManager, Long, DefDict, DefDictItemSaveVO, DefDictItemUpdateVO, DefDictItemPageQuery, DefDictItemResultVO> implements DefDictItemService {

    private final CachePlusOps cachePlusOps;


    @Override
    public boolean checkItemByKey(String key, Long dictId, Long id) {
        ArgumentAssert.notEmpty(key, "请填写字典项标识");
        ArgumentAssert.notNull(dictId, "字典不能为空");
        return superManager.count(Wraps.<DefDict>lbQ().eq(DefDict::getKey, key)
                .eq(DefDict::getParentId, dictId).ne(DefDict::getId, id)) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DefDict save(DefDictItemSaveVO saveVO) {
        if (saveVO.getIsDefault() == null) {
            saveVO.setIsDefault(false);
        }
        DefDict model = BeanUtil.toBean(saveVO, DefDict.class);
        ArgumentAssert.isFalse(checkItemByKey(model.getKey(), model.getParentId(), null), "字典[{}]已经存在，请勿重复创建", model.getKey());
        DefDict parent = getById(model.getParentId());
        ArgumentAssert.notNull(parent, "字典不存在");
        model.setParentKey(parent.getKey());
        model.setClassify(DictClassifyEnum.SYSTEM.getCode());
        superManager.save(model);
        CacheHashKey hashKey = DictCacheKeyBuilder.builder(model.getParentKey(), model.getKey());
        cachePlusOps.hSet(hashKey, model.getName());
        return model;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DefDict updateById(DefDictItemUpdateVO updateVO) {
        if (updateVO.getIsDefault() == null) {
            updateVO.setIsDefault(false);
        }
        DefDict model = BeanUtil.toBean(updateVO, DefDict.class);
        DefDict old = getById(model.getId());
        ArgumentAssert.notNull(old, "您要删除的字典项不存在或已被删除！");
        DefDict parent = getById(model.getParentId());
        ArgumentAssert.notNull(parent, "您要删除的字典不存在或已被删除！");
        model.setParentKey(parent.getKey());
        model.setClassify(DictClassifyEnum.SYSTEM.getCode());
        model.setUpdatedTime(LocalDateTime.now());
        superManager.updateById(model);

        // 淘汰旧缓存
        CacheHashKey oldHashKey = DictCacheKeyBuilder.builder(parent.getKey(), old.getKey());
        cachePlusOps.hDel(oldHashKey);
        // 设置新缓存
        CacheHashKey hashKey = DictCacheKeyBuilder.builder(parent.getKey(), model.getKey());
        cachePlusOps.hSet(hashKey, model.getName());
        return model;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeByIds(Collection<Long> idList) {
        return superManager.removeItemByIds(idList);
    }
}
