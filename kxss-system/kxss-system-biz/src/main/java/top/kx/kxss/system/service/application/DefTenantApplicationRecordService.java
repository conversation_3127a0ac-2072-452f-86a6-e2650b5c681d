package top.kx.kxss.system.service.application;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.application.DefTenantApplicationRecord;
import top.kx.kxss.system.vo.query.application.DefTenantApplicationRecordPageQuery;
import top.kx.kxss.system.vo.result.application.DefTenantApplicationRecordResultVO;
import top.kx.kxss.system.vo.save.application.DefTenantApplicationRecordSaveVO;
import top.kx.kxss.system.vo.update.application.DefTenantApplicationRecordUpdateVO;

/**
 * <p>
 * 业务接口
 * 租户应用授权记录
 * </p>
 *
 * <AUTHOR>
 * @date 2021-09-15
 */
public interface DefTenantApplicationRecordService extends SuperService<Long, DefTenantApplicationRecord, DefTenantApplicationRecordSaveVO, DefTenantApplicationRecordUpdateVO, DefTenantApplicationRecordPageQuery, DefTenantApplicationRecordResultVO> {

    boolean save(DefTenantApplicationRecord record);
}
