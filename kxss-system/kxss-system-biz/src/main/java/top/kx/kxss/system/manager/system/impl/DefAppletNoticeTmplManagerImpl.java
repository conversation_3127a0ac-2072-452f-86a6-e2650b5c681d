package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DefAppletNoticeTmpl;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DefAppletNoticeTmplManager;
import top.kx.kxss.system.mapper.system.DefAppletNoticeTmplMapper;

/**
 * <p>
 * 通用业务实现类
 * 小程序服务通知模版
 * </p>
 *
 * <AUTHOR>
 * @date 2023-10-25 15:39:53
 * @create [2023-10-25 15:39:53] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefAppletNoticeTmplManagerImpl extends SuperManagerImpl<DefAppletNoticeTmplMapper, DefAppletNoticeTmpl> implements DefAppletNoticeTmplManager {

}


