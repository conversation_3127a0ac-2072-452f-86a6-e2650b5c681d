package top.kx.kxss.system.service.tenant.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.tenant.DefTenantOrg;
import top.kx.kxss.system.manager.tenant.DefTenantOrgManager;
import top.kx.kxss.system.service.tenant.DefTenantOrgService;
import top.kx.kxss.system.vo.query.tenant.DefTenantOrgPageQuery;
import top.kx.kxss.system.vo.result.tenant.DefTenantOrgResultVO;
import top.kx.kxss.system.vo.save.tenant.DefTenantOrgSaveVO;
import top.kx.kxss.system.vo.update.tenant.DefTenantOrgUpdateVO;

import java.time.LocalDateTime;

/**
 * <p>
 * 业务实现类
 * 商户门店信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-07-12 14:20:57
 * @create [2023-07-12 14:20:57] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.DEFAULTS)
public class DefTenantOrgServiceImpl extends SuperServiceImpl<DefTenantOrgManager, Long, DefTenantOrg, DefTenantOrgSaveVO,
        DefTenantOrgUpdateVO, DefTenantOrgPageQuery, DefTenantOrgResultVO> implements DefTenantOrgService {


    @Override
    public DefTenantOrg getOne(LbQueryWrap<DefTenantOrg> queryWrap) {
        return superManager.getOne(queryWrap);
    }

    @Override
    public void saveEntity(DefTenantOrgSaveVO saveVO) {
        saveVO.setOrgState(ObjectUtil.isNotNull(saveVO.getOrgState()) ? saveVO.getOrgState() : true);
        saveVO.setTenantState(ObjectUtil.isNotNull(saveVO.getTenantState()) ? saveVO.getTenantState() : true);
        DefTenantOrg tenantOrg = getOne(Wraps.<DefTenantOrg>lbQ().eq(DefTenantOrg::getTenantId, saveVO.getTenantId()).eq(DefTenantOrg::getOrgId, saveVO.getOrgId()));
        if (ObjectUtil.isNotNull(tenantOrg)) {
            tenantOrg.setOrgState(saveVO.getOrgState());
            tenantOrg.setTenantState(saveVO.getTenantState());
            tenantOrg.setOrgName(saveVO.getOrgName());
            tenantOrg.setLongitude(saveVO.getLongitude());
            tenantOrg.setDimension(saveVO.getDimension());
            tenantOrg.setUpdatedTime(LocalDateTime.now());
            superManager.updateById(tenantOrg);
        } else {
            DefTenantOrg tenantOrg1 = BeanUtil.copyProperties(saveVO, DefTenantOrg.class);
            superManager.save(tenantOrg1);
        }
    }

    @Override
    public void update(LbUpdateWrap<DefTenantOrg> eq) {
        superManager.update(eq);
    }
}


