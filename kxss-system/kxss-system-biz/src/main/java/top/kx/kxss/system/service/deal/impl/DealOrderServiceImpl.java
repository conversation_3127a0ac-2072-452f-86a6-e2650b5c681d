package top.kx.kxss.system.service.deal.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.pos.DealOrderStatusEnum;
import top.kx.kxss.system.entity.deal.DealOrder;
import top.kx.kxss.system.manager.deal.DealOrderManager;
import top.kx.kxss.system.service.deal.DealOrderService;
import top.kx.kxss.system.vo.query.deal.DealOrderPageQuery;
import top.kx.kxss.system.vo.result.deal.DealOrderResultVO;
import top.kx.kxss.system.vo.save.deal.DealOrderSaveVO;
import top.kx.kxss.system.vo.update.deal.DealOrderUpdateVO;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Random;

/**
 * <p>
 * 业务实现类
 * 订单记录
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-23 10:19:46
 * @create [2024-10-23 10:19:46] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class DealOrderServiceImpl extends SuperServiceImpl<DealOrderManager, Long, DealOrder, DealOrderSaveVO,
        DealOrderUpdateVO, DealOrderPageQuery, DealOrderResultVO> implements DealOrderService {

    @Override
    public DealOrder defaultDealOrder() {
       return  DealOrder.builder()
                .code(randomOrderCode())
                .createdOrgId(ContextUtil.getCurrentCompanyId())
                .status(DealOrderStatusEnum.NO_PAY.getCode())
                .paid(BigDecimal.ZERO).isFirst(false).payment(BigDecimal.ZERO)
                .refundAmount(BigDecimal.ZERO).discountAmount(BigDecimal.ZERO)
                .tenantId(ContextUtil.getTenantId()).originalPrice(BigDecimal.ZERO)
                .unpaid(BigDecimal.ZERO).name("").build();
    }

    @Override
    public boolean save(DealOrder dealOrder) {
        return superManager.save(dealOrder);
    }

    @Override
    public boolean updateById(DealOrder dealOrder) {
        return superManager.updateById(dealOrder);
    }

    private String getRandom(int len) {
        Random r = new Random();
        StringBuilder rs = new StringBuilder();
        for (int i = 0; i < len; i++) {
            rs.append(r.nextInt(10));
        }
        return rs.toString();
    }

    public String randomOrderCode() {
        SimpleDateFormat dmDate = new SimpleDateFormat("yyyyMMddHHmmss");
        String randata = getRandom(6);
        Date date = new Date();
        String dateran = dmDate.format(date);
        String Xsode = "DR" + dateran + randata;
        if (Xsode.length() < 24) {
            Xsode = Xsode + 0;
        }
        return Xsode;
    }
}


