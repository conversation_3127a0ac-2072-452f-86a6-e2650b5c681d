package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.system.DefSystemUpgrade;
import top.kx.kxss.system.vo.save.system.DefSystemUpgradeSaveVO;
import top.kx.kxss.system.vo.update.system.DefSystemUpgradeUpdateVO;
import top.kx.kxss.system.vo.result.system.DefSystemUpgradeResultVO;
import top.kx.kxss.system.vo.query.system.DefSystemUpgradePageQuery;

import java.util.Map;


/**
 * <p>
 * 业务接口
 * 系统升级公告
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-20 15:22:31
 * @create [2024-02-20 15:22:31] [dou] [代码生成器生成]
 */
public interface DefSystemUpgradeService extends SuperService<Long, DefSystemUpgrade, DefSystemUpgradeSaveVO,
    DefSystemUpgradeUpdateVO, DefSystemUpgradePageQuery, DefSystemUpgradeResultVO> {

    Map<String, Object> checkVersion(String version);

    DefSystemUpgradeResultVO getOneByOrderSource();
}


