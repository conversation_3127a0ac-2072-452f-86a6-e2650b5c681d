package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.common.BaseDict;
import top.kx.kxss.system.entity.system.DefDict;
import top.kx.kxss.system.vo.query.system.DefDictPageQuery;
import top.kx.kxss.system.vo.result.system.DefDictResultVO;
import top.kx.kxss.system.vo.save.system.DefDictSaveVO;
import top.kx.kxss.system.vo.update.system.DefDictUpdateVO;

import java.util.List;

/**
 * <p>
 * 业务接口
 * 字典
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-04
 */
public interface DefDictService extends SuperService<Long, DefDict, DefDictSaveVO, DefDictUpdateVO, DefDictPageQuery, DefDictResultVO> {

    /**
     * 检查字典标识是否可用
     *
     * @param key 标识
     * @param id  当前id
     * @return
     */
    boolean checkByKey(String key, Long id);

    /**
     * 删除字典
     *
     * @param ids
     * @return
     */
    Boolean deleteDict(List<Long> ids);

    /**
     * 复制
     *
     * @param id 字典id
     * @return top.kx.kxss.system.entity.system.DefDict
     * <AUTHOR>
     * @date 2022/4/18 2:39 PM
     * @create [2022/4/18 2:39 PM ] [tangyh] [初始创建]
     */
    @Override
    DefDict copy(Long id);

    /**
     * 根据字典id查询字典项列表
     *
     * @param id 字典ID
     * @return java.util.List<top.kx.kxss.tenant.entity.base.DefDict>
     * <AUTHOR>
     * @date 2021/10/8 11:39 下午
     * @create [2021/10/8 11:39 下午 ] [tangyh] [初始创建]
     */
    List<DefDict> findItemByDictId(Long id);

    /**
     * 将系统字典注册到商户运营后台的基础数据模块
     *
     * @param key 当前字典key
     * @return
     */
    Boolean registToMenu(String key);

    /**
     * 根据字典key查询字典项列表
     */
    List<BaseDict> findItemByDictKeys(List<String> keys);

    /**
     * 根据字典key查询字典项列表
     */
    List<BaseDict> findParentItemByDictKeys(List<String> keys, List<BaseDict> defDicts);

    List<String> echoList(String parentKey, List<String> keys);


}
