package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.system.DefDealRechargeSetting;
import top.kx.kxss.system.vo.save.system.DefDealRechargeSettingSaveVO;
import top.kx.kxss.system.vo.update.system.DefDealRechargeSettingUpdateVO;
import top.kx.kxss.system.vo.result.system.DefDealRechargeSettingResultVO;
import top.kx.kxss.system.vo.query.system.DefDealRechargeSettingPageQuery;


/**
 * <p>
 * 业务接口
 * 团购充值设置
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-19 17:21:26
 * @create [2024-10-19 17:21:26] [dou] [代码生成器生成]
 */
public interface DefDealRechargeSettingService extends SuperService<Long, DefDealRechargeSetting, DefDealRechargeSettingSaveVO,
    DefDealRechargeSettingUpdateVO, DefDealRechargeSettingPageQuery, DefDealRechargeSettingResultVO> {

    Boolean updateState(Long id);

    Boolean updateIsUnitPrice(Long id);
}


