package top.kx.kxss.system.service.sms;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.sms.DefSmsSendRecords;
import top.kx.kxss.system.vo.query.sms.DefSmsSendRecordsPageQuery;
import top.kx.kxss.system.vo.result.sms.DefSmsSendRecordsResultVO;
import top.kx.kxss.system.vo.save.sms.DefSmsSendRecordsSaveVO;
import top.kx.kxss.system.vo.update.sms.DefSmsSendRecordsUpdateVO;


/**
 * <p>
 * 业务接口
 * 短信发送记录
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-28 17:22:09
 * @create [2024-04-28 17:22:09] [yan] [代码生成器生成]
 */
public interface DefSmsSendRecordsService extends SuperService<Long, DefSmsSendRecords, DefSmsSendRecordsSaveVO,
    DefSmsSendRecordsUpdateVO, DefSmsSendRecordsPageQuery, DefSmsSendRecordsResultVO> {

    DefSmsSendRecords saveMarketing(DefSmsSendRecordsSaveVO saveVO);

    DefSmsSendRecords update(DefSmsSendRecordsUpdateVO updateVO);

    DefSmsSendRecords saveRecord(DefSmsSendRecords defSmsSendRecords);

    Boolean sendSuccess(Long recordId);

    Boolean sendFail(Long recordId);
}


