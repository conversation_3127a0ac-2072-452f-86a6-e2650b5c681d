package top.kx.kxss.system.service.subscription.order;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.system.entity.subscription.order.SubscriptionOrderTemplate;
import top.kx.kxss.system.vo.save.subscription.order.SubscriptionOrderTemplateSaveVO;
import top.kx.kxss.system.vo.update.subscription.order.SubscriptionOrderTemplateUpdateVO;
import top.kx.kxss.system.vo.result.subscription.order.SubscriptionOrderTemplateResultVO;
import top.kx.kxss.system.vo.query.subscription.order.SubscriptionOrderTemplatePageQuery;


/**
 * <p>
 * 业务接口
 * 订单订阅模板
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-09 18:56:18
 * @create [2025-06-09 18:56:18] [dou] [代码生成器生成]
 */
public interface SubscriptionOrderTemplateService extends SuperService<Long, SubscriptionOrderTemplate, SubscriptionOrderTemplateSaveVO,
    SubscriptionOrderTemplateUpdateVO, SubscriptionOrderTemplatePageQuery, SubscriptionOrderTemplateResultVO> {

    boolean save(SubscriptionOrderTemplate orderTemplate);

    boolean remove(LbQueryWrap<SubscriptionOrderTemplate> eq);
}


