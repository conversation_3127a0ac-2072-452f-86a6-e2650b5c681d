package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.system.DefAppletToken;
import top.kx.kxss.system.vo.save.system.DefAppletTokenSaveVO;
import top.kx.kxss.system.vo.update.system.DefAppletTokenUpdateVO;
import top.kx.kxss.system.vo.result.system.DefAppletTokenResultVO;
import top.kx.kxss.system.vo.query.system.DefAppletTokenPageQuery;


/**
 * <p>
 * 业务接口
 * 客户小程序令牌信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-27 10:41:58
 * @create [2024-04-27 10:41:58] [dou] [代码生成器生成]
 */
public interface DefAppletTokenService extends SuperService<Long, DefAppletToken, DefAppletTokenSaveVO,
    DefAppletTokenUpdateVO, DefAppletTokenPageQuery, DefAppletTokenResultVO> {

    boolean save(DefAppletToken defAppletToken);

    DefAppletToken getByAuthorizerAppid(String authorizerAppid);

    boolean updateById(DefAppletToken defAppletToken);
}


