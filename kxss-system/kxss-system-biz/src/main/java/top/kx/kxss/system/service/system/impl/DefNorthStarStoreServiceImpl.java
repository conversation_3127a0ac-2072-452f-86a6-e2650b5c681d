package top.kx.kxss.system.service.system.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.system.DefNorthStarStore;
import top.kx.kxss.system.manager.system.DefNorthStarStoreManager;
import top.kx.kxss.system.service.system.DefNorthStarStoreService;
import top.kx.kxss.system.vo.query.system.DefNorthStarStorePageQuery;
import top.kx.kxss.system.vo.result.system.DefNorthStarStoreResultVO;
import top.kx.kxss.system.vo.save.system.DefNorthStarStoreSaveVO;
import top.kx.kxss.system.vo.update.system.DefNorthStarStoreUpdateVO;

import java.util.List;

/**
 * <p>
 * 业务实现类
 * 北极星授权门店
 * </p>
 *
 * <AUTHOR>
 * @date 2023-11-02 10:00:21
 * @create [2023-11-02 10:00:21] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
@DS(DsConstant.DEFAULTS)
public class DefNorthStarStoreServiceImpl extends SuperServiceImpl<DefNorthStarStoreManager, Long, DefNorthStarStore, DefNorthStarStoreSaveVO,
        DefNorthStarStoreUpdateVO, DefNorthStarStorePageQuery, DefNorthStarStoreResultVO> implements DefNorthStarStoreService {


    @Override
    public DefNorthStarStore getOne(LbQueryWrap<DefNorthStarStore> eq) {
        return superManager.getOne(eq);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateById(DefNorthStarStore northStarStore) {
        return superManager.updateById(northStarStore);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBatchById(List<DefNorthStarStore> defNorthStarStoreList) {
        return superManager.updateBatchById(defNorthStarStoreList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdate(DefNorthStarStore defNorthStarStore) {
        return superManager.saveOrUpdate(defNorthStarStore);
    }
}


