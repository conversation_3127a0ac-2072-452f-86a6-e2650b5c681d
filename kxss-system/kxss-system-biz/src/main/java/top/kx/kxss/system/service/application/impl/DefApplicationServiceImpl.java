package top.kx.kxss.system.service.application.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Multimap;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperCacheServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.CollHelper;
import top.kx.basic.utils.TreeUtil;
import top.kx.kxss.common.constant.DefValConstants;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.file.service.DefAppendixService;
import top.kx.kxss.model.enumeration.system.ResourceTypeEnum;
import top.kx.kxss.system.entity.application.DefApplication;
import top.kx.kxss.system.entity.application.DefResource;
import top.kx.kxss.system.entity.application.DefUserApplication;
import top.kx.kxss.system.manager.application.DefApplicationManager;
import top.kx.kxss.system.manager.application.DefResourceManager;
import top.kx.kxss.system.manager.application.DefTenantApplicationRelManager;
import top.kx.kxss.system.manager.application.DefUserApplicationManager;
import top.kx.kxss.system.service.application.DefApplicationService;
import top.kx.kxss.system.vo.query.application.DefApplicationPageQuery;
import top.kx.kxss.system.vo.result.application.ApplicationResourceResultVO;
import top.kx.kxss.system.vo.result.application.DefApplicationResultVO;
import top.kx.kxss.system.vo.result.application.DefResourceResultVO;
import top.kx.kxss.system.vo.save.application.DefApplicationSaveVO;
import top.kx.kxss.system.vo.update.application.DefApplicationUpdateVO;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 业务实现类
 * 应用
 * </p>
 *
 * <AUTHOR>
 * @date 2021-09-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
@DS(DsConstant.DEFAULTS)
public class DefApplicationServiceImpl extends SuperCacheServiceImpl<DefApplicationManager, Long, DefApplication, DefApplicationSaveVO, DefApplicationUpdateVO, DefApplicationPageQuery, DefApplicationResultVO>
        implements DefApplicationService {
    private final DefResourceManager defResourceManager;
    private final EchoService echoService;
    private final DefAppendixService appendixService;
    private final DefUserApplicationManager defUserApplicationManager;
    private final DefTenantApplicationRelManager defTenantApplicationRelManager;

    @Override
    public List<Long> findApplicationByEmployeeId(Long employeeId) {
        return defTenantApplicationRelManager.findApplicationByEmployeeId(employeeId);
    }

    @Override
    public List<DefApplicationResultVO> findMyApplication(Long tenantId, String name) {
        if (tenantId == null) {
            tenantId = ContextUtil.getTenantId();
        }
        List<DefApplicationResultVO> list = superManager.findMyApplication(tenantId, name);
        list.forEach(item -> {
            //0-过期 1-有效
            String state = item.getExpirationTime() == null || item.getExpirationTime().isAfter(LocalDateTime.now()) ? "1" : "0";
            item.setState(state);
        });
        return list;
    }

    @Override
    public List<DefApplicationResultVO> findRecommendApplication(Long tenantId, String name) {
        if (tenantId == null) {
            tenantId = ContextUtil.getTenantId();
        }
        return superManager.findRecommendApplication(tenantId, name);
    }

    @Override
    public Boolean check(Long id, String name) {
        LbQueryWrap<DefApplication> wrap = Wraps.<DefApplication>lbQ().eq(DefApplication::getName, name)
                .ne(DefApplication::getId, id);
        return superManager.count(wrap) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DefApplication save(DefApplicationSaveVO saveVO) {
        ArgumentAssert.isFalse(check(null, saveVO.getName()), "应用名称重复");
        DefApplication defApplication = BeanPlusUtil.toBean(saveVO, DefApplication.class);
        defApplication.setAppKey(RandomUtil.randomString(12));
        defApplication.setAppSecret(RandomUtil.randomString(36));
        defApplication.setIsVisible(true);
        superManager.save(defApplication);
        appendixService.save(defApplication.getId(), saveVO.getAppendixIcon());
        return defApplication;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public DefApplication updateById(DefApplicationUpdateVO updateVO) {
        ArgumentAssert.isFalse(check(updateVO.getId(), updateVO.getName()), "应用名称重复");
        DefApplication defApplication = BeanPlusUtil.toBean(updateVO, DefApplication.class);
        defApplication.setUpdatedTime(LocalDateTime.now());
        superManager.updateById(defApplication);

        appendixService.save(defApplication.getId(), updateVO.getAppendixIcon());
        return defApplication;
    }

    @Override
    public List<ApplicationResourceResultVO> findApplicationResourceList() {
        List<DefApplication> list = list(Wraps.<DefApplication>lbQ().orderByAsc(DefApplication::getSortValue));
        List<Long> applicationIdList = list.stream().map(DefApplication::getId).collect(Collectors.toList());
        List<DefResource> resourceList = defResourceManager.list(Wraps.<DefResource>lbQ().eq(DefResource::getState, true)
                .in(DefResource::getApplicationId, applicationIdList).orderByAsc(DefResource::getSortValue));

        return buildAppResResult(list, BeanPlusUtil.toBeanList(resourceList, DefResourceResultVO.class));
    }

    @Override
    public List<ApplicationResourceResultVO> findAvailableApplicationResourceList() {
        Long tenantId = ContextUtil.getTenantId();
        List<DefApplication> list = superManager.findApplicationListByTenantId(tenantId);
        List<Long> applicationIdList = list.stream().map(DefApplication::getId).collect(Collectors.toList());
        List<DefResource> resourceList;
        if (CollUtil.isNotEmpty(applicationIdList)) {
            List<String> codes = Arrays.asList(ResourceTypeEnum.MENU.getCode(), ResourceTypeEnum.VIEW.getCode(), ResourceTypeEnum.FUNCTION.getCode(), ResourceTypeEnum.FIELD.getCode());
            // 不查询数据权限
            resourceList = defResourceManager.findResourceListByTenantIdAndApplicationIds(tenantId, applicationIdList, codes);
        } else {
            resourceList = Collections.emptyList();
        }
        return buildAppResResult(list, BeanPlusUtil.toBeanList(resourceList, DefResourceResultVO.class));
    }

    private List<ApplicationResourceResultVO> buildAppResResult(List<DefApplication> list, List<DefResourceResultVO> resultList) {
        // 回显 @Echo 标记的字段
        echoService.action(resultList);
        // 按应用ID分组
        Multimap<Long, DefResourceResultVO> resourceMap = CollHelper.iterableToMultiMap(resultList,
                DefResourceResultVO::getApplicationId, resource -> resource);

        return list.stream().map(item -> {
            Collection<DefResourceResultVO> resources = resourceMap.containsKey(item.getId()) ? resourceMap.get(item.getId()) : Collections.emptyList();
            return ApplicationResourceResultVO.builder()
                    .defApplication(item)
                    .resourceList(TreeUtil.buildTree(resources))
                    .build();
        }).collect(Collectors.toList());
    }

    @Override
    public List<ApplicationResourceResultVO> findAvailableApplicationDataScopeList() {
        Long tenantId = ContextUtil.getTenantId();
        // 查租户拥有的应用
        List<DefApplication> list = superManager.findApplicationListByTenantId(tenantId);
        List<Long> applicationIdList = list.stream().map(DefApplication::getId).collect(Collectors.toList());
        List<Long> resourceIdList;
        if (CollUtil.isNotEmpty(applicationIdList)) {
            // 查租户拥有的资源
            List<DefResource> dataScopeList = defResourceManager.findResourceListByTenantIdAndApplicationIds(tenantId, applicationIdList, Collections.singletonList(ResourceTypeEnum.DATA.getCode()));

            // 将id和treePath截取后 合并成list，其中treePath存放的是该节点的所有父节点ID
            Stream<Long> dataScopeIdStream = dataScopeList.parallelStream().map(DefResource::getId);
            Stream<Long> parentIdStream = dataScopeList.parallelStream()
                    // 将父节点路径截取为父ID数组
                    .map(item -> StrUtil.splitToArray(item.getTreePath(), DefValConstants.TREE_PATH_SPLIT))
                    // 数组流 转 字符串流
                    .flatMap(Arrays::stream)
                    // 去除空数据
                    .filter(ObjectUtil::isNotEmpty)
                    // 类型转换
                    .map(Convert::toLong);
            // 合并 数据权限ID 和 父ID
            resourceIdList = Stream.concat(dataScopeIdStream, parentIdStream).distinct().collect(Collectors.toList());
        } else {
            resourceIdList = Collections.emptyList();
        }
        List<DefResource> resourceList = CollUtil.isEmpty(resourceIdList) ? Collections.emptyList() :
                defResourceManager.list(Wraps.<DefResource>lbQ().in(DefResource::getId, resourceIdList).orderByAsc(DefResource::getSortValue));
        // 将应用和应用的资源封装后返回
        return buildAppResResult(list, BeanPlusUtil.toBeanList(resourceList, DefResourceResultVO.class));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateDefApp(Long applicationId, Long userId) {
        defUserApplicationManager.remove(Wraps.<DefUserApplication>lbQ().eq(DefUserApplication::getUserId, userId));
        return defUserApplicationManager.save(DefUserApplication.builder()
                .applicationId(applicationId).userId(userId).build());
    }

    @Override
    public DefApplication getDefApp(Long userId) {
        DefUserApplication one = defUserApplicationManager.getOne(Wraps.<DefUserApplication>lbQ().eq(DefUserApplication::getUserId, userId), false);
        if (one == null) {
            return null;
        }
        return superManager.getByIdCache(one.getApplicationId());
    }

    @Override
    public List<ApplicationResourceResultVO> findCashierApplicationResourceList() {
        Long tenantId = ContextUtil.getTenantId();
        // 查租户拥有的应用
        List<DefApplication> list = superManager.findCashierApplicationListByTenantId(tenantId);
        List<Long> applicationIdList = list.stream().map(DefApplication::getId).collect(Collectors.toList());
        List<Long> resourceIdList;
        if (CollUtil.isNotEmpty(applicationIdList)) {
            // 查租户拥有的资源
            List<DefResource> dataScopeList = defResourceManager.findResourceListByTenantIdAndApplicationIds(tenantId, applicationIdList, Collections.singletonList(ResourceTypeEnum.DATA.getCode()));

            // 将id和treePath截取后 合并成list，其中treePath存放的是该节点的所有父节点ID
            Stream<Long> dataScopeIdStream = dataScopeList.parallelStream().map(DefResource::getId);
            Stream<Long> parentIdStream = dataScopeList.parallelStream()
                    // 将父节点路径截取为父ID数组
                    .map(item -> StrUtil.splitToArray(item.getTreePath(), DefValConstants.TREE_PATH_SPLIT))
                    // 数组流 转 字符串流
                    .flatMap(Arrays::stream)
                    // 去除空数据
                    .filter(ObjectUtil::isNotEmpty)
                    // 类型转换
                    .map(Convert::toLong);
            // 合并 数据权限ID 和 父ID
            resourceIdList = Stream.concat(dataScopeIdStream, parentIdStream).distinct().collect(Collectors.toList());
        } else {
            resourceIdList = Collections.emptyList();
        }
        List<DefResource> resourceList = CollUtil.isEmpty(resourceIdList) ? Collections.emptyList() :
                defResourceManager.list(Wraps.<DefResource>lbQ().in(DefResource::getId, resourceIdList).orderByAsc(DefResource::getSortValue));
        // 将应用和应用的资源封装后返回
        return buildAppResResult(list, BeanPlusUtil.toBeanList(resourceList, DefResourceResultVO.class));
    }
}
