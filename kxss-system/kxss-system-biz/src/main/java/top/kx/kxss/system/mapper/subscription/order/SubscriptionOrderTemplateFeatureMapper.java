package top.kx.kxss.system.mapper.subscription.order;

import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.system.entity.subscription.order.SubscriptionOrderTemplateFeature;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * Mapper 接口
 * 订单订阅模版与功能权益
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-09 18:56:23
 * @create [2025-06-09 18:56:23] [dou] [代码生成器生成]
 */
@Repository
public interface SubscriptionOrderTemplateFeatureMapper extends SuperMapper<SubscriptionOrderTemplateFeature> {

}


