package top.kx.kxss.system.rabbitmq;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.jackson.JsonUtil;
import top.kx.kxss.app.query.TenantSubscriptionQuery;
import top.kx.kxss.common.constant.RabbitMqConstant;
import top.kx.kxss.model.enumeration.system.subscription.SubscriptionFeatureTypeEnum;
import top.kx.kxss.model.enumeration.system.subscription.SubscriptionStatusEnum;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplate;
import top.kx.kxss.system.service.subscription.SubscriptionTenantTemplateService;
import top.kx.kxss.system.service.subscription.impl.SubscriptionFeatureCheckService;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RabbitListener(queues = RabbitMqConstant.TENANT_SUBSCRIPTION)
public class TenantSubscriptionConsume {


    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private SubscriptionTenantTemplateService tenantTemplateService;
    @Autowired
    private SubscriptionFeatureCheckService subscriptionFeatureCheckService;

    @RabbitHandler
    public void process(String msg) {
        log.info("YearEnd年终总结消费者收到消息: {}", msg);
        TenantSubscriptionQuery consumeQuery =
                JsonUtil.parse(msg, TenantSubscriptionQuery.class);
        boolean lock = false;
        try {
            lock = distributedLock.lock(consumeQuery.getUuid() + "_tenant_subscription", 0);
            if (!lock) {
                return;
            }
            //获取请求中门店的订阅信息
            List<SubscriptionTenantTemplate> tenantTemplateList = tenantTemplateService.list(Wraps.<SubscriptionTenantTemplate>lbQ()
                    .in(SubscriptionTenantTemplate::getTenantId, consumeQuery.getTenantIdList())
                    .eq(SubscriptionTenantTemplate::getDeleteFlag, 0)
                    .in(SubscriptionTenantTemplate::getStatus,
                            Arrays.asList(SubscriptionStatusEnum.ACTIVE.getCode()
                                    , SubscriptionStatusEnum.NO_ACTIVE.getCode()))
            );
            if (CollUtil.isEmpty(tenantTemplateList)) {
                return;
            }
            Map<Long, List<SubscriptionTenantTemplate>> tenantTemplateMap = tenantTemplateList.stream().collect(Collectors.groupingBy(SubscriptionTenantTemplate::getTenantId));
            for (Long tenantId : tenantTemplateMap.keySet()) {
                //获取当前门店的订阅信息
                List<SubscriptionTenantTemplate> tenantTemplates = tenantTemplateMap.get(tenantId);
                Map<String, List<SubscriptionTenantTemplate>> serviceTypeMap = tenantTemplates.stream().collect(Collectors.groupingBy(SubscriptionTenantTemplate::getTmpServiceType));
                serviceTypeMap.forEach(this::checkSubscription);
            }

        } catch (Exception e) {
            log.error("操作失败", e);
        } finally {
            ContextUtil.remove();
            if (lock) {
                distributedLock.releaseLock(consumeQuery.getUuid() + "_tenant_subscription");
            }
        }
    }

    private void checkSubscription(String serviceType, List<SubscriptionTenantTemplate> tenantTemplateList) {
        if (StrUtil.isBlank(serviceType) || CollUtil.isEmpty(tenantTemplateList)) {
            return;
        }
        SubscriptionFeatureTypeEnum featureTypeEnum = SubscriptionFeatureTypeEnum.get(serviceType);
        if (featureTypeEnum == null) {
            return;
        }
        switch (featureTypeEnum) {
            case TEMPLATE:
                subscriptionFeatureCheckService.checkTemplate(tenantTemplateList);
                break;
            case VALUE_ADDED:
                subscriptionFeatureCheckService.checkValueAdded(tenantTemplateList);
                break;
        }

    }

}
