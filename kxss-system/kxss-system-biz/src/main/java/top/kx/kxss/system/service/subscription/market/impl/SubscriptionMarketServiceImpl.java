package top.kx.kxss.system.service.subscription.market.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.model.enumeration.system.subscription.SubscriptionFeatureTypeEnum;
import top.kx.kxss.model.enumeration.system.subscription.SubscriptionStatusEnum;
import top.kx.kxss.system.entity.subscription.SubscriptionTemplate;
import top.kx.kxss.system.entity.subscription.SubscriptionTemplateFeature;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplate;
import top.kx.kxss.system.service.subscription.SubscriptionTemplateFeatureService;
import top.kx.kxss.system.service.subscription.SubscriptionTemplateService;
import top.kx.kxss.system.service.subscription.SubscriptionTenantTemplateService;
import top.kx.kxss.system.service.subscription.market.SubscriptionMarketService;
import top.kx.kxss.system.vo.result.subscription.SubscriptionTemplateFeatureResultVO;
import top.kx.kxss.system.vo.result.subscription.SubscriptionTemplateResultVO;
import top.kx.kxss.system.vo.result.subscription.market.SubscriptionMarketTreeResultVO;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SubscriptionMarketServiceImpl implements SubscriptionMarketService {

    private final SubscriptionTemplateService subscriptionTemplateService;
    private final SubscriptionTenantTemplateService subscriptionTenantTemplateService;
    private final SubscriptionTemplateFeatureService subscriptionTemplateFeatureService;
    private final EchoService echoService;

    @Override
    public List<SubscriptionMarketTreeResultVO> marketList() {
        List<SubscriptionTemplate> templateList = subscriptionTemplateService.list(Wraps.<SubscriptionTemplate>lbQ()
                .eq(SubscriptionTemplate::getEnabled, true)
                .eq(SubscriptionTemplate::getDeleteFlag, 0));
        if (CollUtil.isEmpty(templateList)) {
            return Lists.newArrayList();
        }
        List<SubscriptionTemplateResultVO> templateResultVOList = BeanUtil.copyToList(templateList, SubscriptionTemplateResultVO.class);
        Map<String, List<SubscriptionTemplateResultVO>> groupedTemplates = templateResultVOList.stream()
                .collect(Collectors.groupingBy(SubscriptionTemplateResultVO::getServiceType));
        List<SubscriptionMarketTreeResultVO> result = new ArrayList<>();
        groupedTemplates.forEach((category, templates) -> {
            SubscriptionMarketTreeResultVO resultVO = new SubscriptionMarketTreeResultVO();
            resultVO.setServiceType(category);
            resultVO.setTemplateList(templates);
            result.add(resultVO);
        });
        echoService.action(result);
        return result;
    }

    @Override
    public SubscriptionTemplateResultVO getInfo(Long tmplId) {
        SubscriptionTemplate subscriptionTemplate = subscriptionTemplateService.getById(tmplId);
        ArgumentAssert.notNull(subscriptionTemplate, "模板不存在");
        ArgumentAssert.isTrue(subscriptionTemplate.getEnabled(), "模板已禁用");
        SubscriptionTemplateResultVO resultVO = BeanUtil.copyProperties(subscriptionTemplate, SubscriptionTemplateResultVO.class);
        List<SubscriptionTemplateFeature> templateFeatureList = subscriptionTemplateFeatureService.list(Wraps.<SubscriptionTemplateFeature>lbQ()
                .eq(SubscriptionTemplateFeature::getTmpId, tmplId)
                .eq(SubscriptionTemplateFeature::getDeleteFlag, 0)
        );
        List<SubscriptionTemplateFeatureResultVO> featureResultVOList = BeanUtil.copyToList(templateFeatureList, SubscriptionTemplateFeatureResultVO.class);
        resultVO.setTemplateFeatureList(featureResultVOList);
        echoService.action(resultVO);
        resultVO.setBillingTypeDesc(subscriptionTemplateService.billingTypeDesc(subscriptionTemplate.getBillingType(), subscriptionTemplate.getDays()));
        resultVO.setIsRenewalCap(isRenewalCap());
        return resultVO;
    }


    @Override
    public Boolean isRenewalCap() {
        return subscriptionTenantTemplateService.count(Wraps.<SubscriptionTenantTemplate>lbQ()
                .eq(SubscriptionTenantTemplate::getTenantId, ContextUtil.getTenantId())
                .eq(SubscriptionTenantTemplate::getOrgId, ContextUtil.getCurrentCompanyId())
                .eq(SubscriptionTenantTemplate::getTmpServiceType, SubscriptionFeatureTypeEnum.TEMPLATE.getCode())
                .eq(SubscriptionTenantTemplate::getStatus, SubscriptionStatusEnum.NO_ACTIVE.getCode())
                .eq(SubscriptionTenantTemplate::getDeleteFlag, 0)) >= 3;
    }

}
