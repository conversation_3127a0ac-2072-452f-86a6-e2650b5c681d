package top.kx.kxss.system.mapper.subscription;

import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplate;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * Mapper 接口
 * 租户订阅模板
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-08 15:33:09
 * @create [2025-05-08 15:33:09] [dou] [代码生成器生成]
 */
@Repository
public interface SubscriptionTenantTemplateMapper extends SuperMapper<SubscriptionTenantTemplate> {

}


