package top.kx.kxss.system.strategy.subscription;

import com.alibaba.fastjson.JSONObject;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplate;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplateFeature;

import java.util.List;

/**
 * 订阅检查策略
 *
 * <AUTHOR>
 * @date 2025/5/14 15:11
 */
public interface SubscriptionCheckStrategy {

    boolean checkPermission(List<SubscriptionTenantTemplateFeature> featureList,
                            JSONObject params, String featureCode, SubscriptionTenantTemplate tenantTemplate);
}
