package top.kx.kxss.system.service.application.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.kxss.system.entity.application.DefResourceApi;
import top.kx.kxss.system.manager.application.DefResourceApiManager;
import top.kx.kxss.system.service.application.DefResourceApiService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.system.vo.save.application.DefResourceApiSaveVO;
import top.kx.kxss.system.vo.update.DefResourceApiUpdateVO;
import top.kx.kxss.system.vo.result.DefResourceApiResultVO;
import top.kx.kxss.system.vo.query.DefResourceApiPageQuery;

/**
 * <p>
 * 业务实现类
 * 资源API接口
 * </p>
 *
 * <AUTHOR>
 * @date 2023-03-18 10:45:36
 * @create [2023-03-18 10:45:36] [Wang] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class DefResourceApiServiceImpl extends SuperServiceImpl<DefResourceApiManager, Long, DefResourceApi, DefResourceApiSaveVO,
    DefResourceApiUpdateVO, DefResourceApiPageQuery, DefResourceApiResultVO> implements DefResourceApiService {


}


