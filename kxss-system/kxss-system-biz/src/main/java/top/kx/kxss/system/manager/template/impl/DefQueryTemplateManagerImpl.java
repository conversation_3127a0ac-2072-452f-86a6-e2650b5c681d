package top.kx.kxss.system.manager.template.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.template.DefQueryTemplate;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.template.DefQueryTemplateManager;
import top.kx.kxss.system.mapper.template.DefQueryTemplateMapper;

/**
 * <p>
 * 通用业务实现类
 * 查询模板
 * </p>
 *
 * <AUTHOR>
 * @date 2024-01-06 17:11:55
 * @create [2024-01-06 17:11:55] [yh] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefQueryTemplateManagerImpl extends SuperManagerImpl<DefQueryTemplateMapper, DefQueryTemplate> implements DefQueryTemplateManager {

}


