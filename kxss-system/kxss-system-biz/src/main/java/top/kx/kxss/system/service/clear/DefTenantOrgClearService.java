package top.kx.kxss.system.service.clear;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.clear.DefTenantOrgClear;
import top.kx.kxss.system.vo.save.clear.DefTenantOrgClearSaveVO;
import top.kx.kxss.system.vo.update.clear.DefTenantOrgClearUpdateVO;
import top.kx.kxss.system.vo.result.clear.DefTenantOrgClearResultVO;
import top.kx.kxss.system.vo.query.clear.DefTenantOrgClearPageQuery;


/**
 * <p>
 * 业务接口
 * 数据清空记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-20 17:43:48
 * @create [2025-06-20 17:43:48] [yan] [代码生成器生成]
 */
public interface DefTenantOrgClearService extends SuperService<Long, DefTenantOrgClear, DefTenantOrgClearSaveVO,
    DefTenantOrgClearUpdateVO, DefTenantOrgClearPageQuery, DefTenantOrgClearResultVO> {

}


