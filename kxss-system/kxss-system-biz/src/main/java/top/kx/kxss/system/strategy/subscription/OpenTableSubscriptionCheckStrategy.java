package top.kx.kxss.system.strategy.subscription;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.utils.ExpressionEvaluator;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplate;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplateFeature;

import java.util.List;

/**
 * 套餐开台策略
 *
 * <AUTHOR>
 * @date 2025/5/14 15:11
 */
@RequiredArgsConstructor
@DS(DsConstant.DEFAULTS)
@Service
@Slf4j
public class OpenTableSubscriptionCheckStrategy extends AbstractSubscriptionCheckService {

    @Override
    public boolean checkPermission(List<SubscriptionTenantTemplateFeature> featureList, JSONObject params, String featureCode
            , SubscriptionTenantTemplate tenantTemplate) {
        boolean isThail = ExpressionEvaluator.evaluate("ObjectUtil.isNotNull(#{thailList})", params);
        boolean isVoucher = ExpressionEvaluator.evaluate("StrUtil.isNotBlank(#{securitiesNumber})", params);
        if (!isThail && !isVoucher) {
            return checkExpire(tenantTemplate);
        } else {
            return true;
        }
    }
}
