package top.kx.kxss.system.manager.subscription;

import top.kx.basic.base.manager.SuperManager;
import top.kx.kxss.system.entity.subscription.SubscriptionTemplateFeature;

/**
 * <p>
 * 通用业务接口
 * 订阅模版与功能权益
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-07 16:01:29
 * @create [2025-05-07 16:01:29] [dou] [代码生成器生成]
 */
public interface SubscriptionTemplateFeatureManager extends SuperManager<SubscriptionTemplateFeature> {

}


