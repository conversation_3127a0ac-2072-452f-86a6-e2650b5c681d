package top.kx.kxss.system.manager.subscription;

import top.kx.basic.base.manager.SuperManager;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplateFeature;

/**
 * <p>
 * 通用业务接口
 * 租户订阅模版与功能权益
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-21 10:23:54
 * @create [2025-05-21 10:23:54] [dou] [代码生成器生成]
 */
public interface SubscriptionTenantTemplateFeatureManager extends SuperManager<SubscriptionTenantTemplateFeature> {

}


