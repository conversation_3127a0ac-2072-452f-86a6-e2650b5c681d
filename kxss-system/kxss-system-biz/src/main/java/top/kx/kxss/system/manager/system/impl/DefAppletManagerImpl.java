package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DefApplet;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DefAppletManager;
import top.kx.kxss.system.mapper.system.DefAppletMapper;

/**
 * <p>
 * 通用业务实现类
 * 小程序配置
 * </p>
 *
 * <AUTHOR>
 * @date 2023-07-06 15:46:15
 * @create [2023-07-06 15:46:15] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefAppletManagerImpl extends SuperManagerImpl<DefAppletMapper, DefApplet> implements DefAppletManager {

}


