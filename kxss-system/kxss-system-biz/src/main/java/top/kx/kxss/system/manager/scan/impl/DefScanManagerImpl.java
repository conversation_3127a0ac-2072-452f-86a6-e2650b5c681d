package top.kx.kxss.system.manager.scan.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.scan.DefScan;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.scan.DefScanManager;
import top.kx.kxss.system.mapper.scan.DefScanMapper;

/**
 * <p>
 * 通用业务实现类
 * 二维码生成
 * </p>
 *
 * <AUTHOR>
 * @date 2024-08-29 15:20:38
 * @create [2024-08-29 15:20:38] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefScanManagerImpl extends SuperManagerImpl<DefScanMapper, DefScan> implements DefScanManager {

}


