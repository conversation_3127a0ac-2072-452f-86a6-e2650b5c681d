package top.kx.kxss.system.manager.tenant.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.tenant.DefTenantMember;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.tenant.DefTenantMemberManager;
import top.kx.kxss.system.mapper.tenant.DefTenantMemberMapper;

/**
 * <p>
 * 通用业务实现类
 * 租户会员
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-29 15:09:15
 * @create [2024-02-29 15:09:15] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefTenantMemberManagerImpl extends SuperManagerImpl<DefTenantMemberMapper, DefTenantMember> implements DefTenantMemberManager {

}


