package top.kx.kxss.system.service.system.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.service.system.DefDouyinStoreService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.system.manager.system.DefDouyinStoreManager;
import top.kx.kxss.system.entity.system.DefDouyinStore;
import top.kx.kxss.system.vo.save.system.DefDouyinStoreSaveVO;
import top.kx.kxss.system.vo.update.system.DefDouyinStoreUpdateVO;
import top.kx.kxss.system.vo.result.system.DefDouyinStoreResultVO;
import top.kx.kxss.system.vo.query.system.DefDouyinStorePageQuery;

/**
 * <p>
 * 业务实现类
 * 抖音授权门店
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-07 13:54:39
 * @create [2024-04-07 13:54:39] [yan] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class DefDouyinStoreServiceImpl extends SuperServiceImpl<DefDouyinStoreManager, Long, DefDouyinStore, DefDouyinStoreSaveVO,
    DefDouyinStoreUpdateVO, DefDouyinStorePageQuery, DefDouyinStoreResultVO> implements DefDouyinStoreService {


}


