package top.kx.kxss.system.service.system.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.system.DefVerticalScreenUpgrade;
import top.kx.kxss.system.entity.system.DefVerticalScreenUpgradeTenant;
import top.kx.kxss.system.manager.system.DefVerticalScreenUpgradeManager;
import top.kx.kxss.system.service.system.DefVerticalScreenUpgradeService;
import top.kx.kxss.system.service.system.DefVerticalScreenUpgradeTenantService;
import top.kx.kxss.system.vo.query.system.DefVerticalScreenUpgradePageQuery;
import top.kx.kxss.system.vo.result.system.DefVerticalScreenUpgradeResultVO;
import top.kx.kxss.system.vo.save.system.DefVerticalScreenUpgradeSaveVO;
import top.kx.kxss.system.vo.update.system.DefVerticalScreenUpgradeUpdateVO;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 智慧屏升级公告
 * </p>
 *
 * <AUTHOR>
 * @date 2024-11-21 10:38:04
 * @create [2024-11-21 10:38:04] [yan] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class DefVerticalScreenUpgradeServiceImpl extends SuperServiceImpl<DefVerticalScreenUpgradeManager, Long, DefVerticalScreenUpgrade, DefVerticalScreenUpgradeSaveVO,
        DefVerticalScreenUpgradeUpdateVO, DefVerticalScreenUpgradePageQuery, DefVerticalScreenUpgradeResultVO> implements DefVerticalScreenUpgradeService {

    private final DefVerticalScreenUpgradeTenantService defVerticalScreenUpgradeTenantService;

    @Override
    public DefVerticalScreenUpgrade save(DefVerticalScreenUpgradeSaveVO defVerticalScreenUpgradeSaveVO) {
        DefVerticalScreenUpgrade upgrade = super.save(defVerticalScreenUpgradeSaveVO);
        if (CollUtil.isNotEmpty(defVerticalScreenUpgradeSaveVO.getTenantIds())) {
            List<DefVerticalScreenUpgradeTenant> tenantList = defVerticalScreenUpgradeSaveVO.getTenantIds().stream().map(s -> DefVerticalScreenUpgradeTenant.builder().tenantId(s).upgradeId(upgrade.getId()).build()).collect(Collectors.toList());
            defVerticalScreenUpgradeTenantService.saveBatch(tenantList);
        }
        return upgrade;
    }


    @Override
    public DefVerticalScreenUpgrade updateById(DefVerticalScreenUpgradeUpdateVO defVerticalScreenUpgradeUpdateVO) {
        DefVerticalScreenUpgrade screenUpgrade = super.updateById(defVerticalScreenUpgradeUpdateVO);
        defVerticalScreenUpgradeTenantService.remove(Wraps.<DefVerticalScreenUpgradeTenant>lbQ().eq(DefVerticalScreenUpgradeTenant::getUpgradeId, defVerticalScreenUpgradeUpdateVO.getId()));
        if (CollUtil.isNotEmpty(defVerticalScreenUpgradeUpdateVO.getTenantIds())) {
            List<DefVerticalScreenUpgradeTenant> tenantList = defVerticalScreenUpgradeUpdateVO.getTenantIds().stream().map(s -> DefVerticalScreenUpgradeTenant.builder().tenantId(s).upgradeId(defVerticalScreenUpgradeUpdateVO.getId()).build()).collect(Collectors.toList());
            defVerticalScreenUpgradeTenantService.saveBatch(tenantList);
        }
        return screenUpgrade;
    }

    @Override
    public Map<String, Object> checkVersion(String version) {
        Map<String, Object> map = MapUtil.newHashMap();
        if (StringUtils.isBlank(version)) {
            map.put("status", true);
            return map;
        }
        // 校验版本， 淮南康溪盛世 济南J8 用不更新 2025-08-19
        if (Objects.equals(ContextUtil.getTenantId(), 639886162161851951L) || Objects.equals(ContextUtil.getTenantId(), 657311926814056962L)) {
            map.put("status", true);
            return map;
        }
        // 校验当前版本
        String[] serverParts = version.split("\\.");
        if (Integer.parseInt(serverParts[0]) < 2) {
            map.put("status", true);
            return map;
        }
        DefVerticalScreenUpgrade defSystemUpgrade = superManager.getOne(Wraps.<DefVerticalScreenUpgrade>lbQ()
                .orderByDesc(DefVerticalScreenUpgrade::getCreatedTime)
                .last("limit 1"));
        map.put("status", true);
        if (ObjectUtil.isNotNull(defSystemUpgrade)) {
            List<DefVerticalScreenUpgradeTenant> upgradeTenantList = defVerticalScreenUpgradeTenantService.list(Wraps.<DefVerticalScreenUpgradeTenant>lbQ().eq(DefVerticalScreenUpgradeTenant::getUpgradeId, defSystemUpgrade.getId()));
            if (CollUtil.isNotEmpty(upgradeTenantList)) {
                List<Long> tenantIds = upgradeTenantList.stream().map(DefVerticalScreenUpgradeTenant::getTenantId).distinct().collect(Collectors.toList());
                if (tenantIds.contains(ContextUtil.getTenantId())) {
                    if (needsUpdate(defSystemUpgrade.getVersion(), version)) {
                        map.put("vo", defSystemUpgrade);
                        map.put("status", false);
                    }
                }
                return map;
            }
            if (needsUpdate(defSystemUpgrade.getVersion(), version)) {
                map.put("vo", defSystemUpgrade);
                map.put("status", false);
            }
        }
        return map;
    }

    /**
     * 检测是否需要更新
     * @param serverVersion 最新版本
     * @param clientVersion 客户版本
     * @return
     */
    public static boolean needsUpdate(String serverVersion, String clientVersion) {
        String[] serverParts = serverVersion.split("\\.");
        String[] clientParts = clientVersion.split("\\.");
        int length = Math.max(serverParts.length, clientParts.length);

        for (int i = 0; i < length; i++) {
            int min = i < serverParts.length ? Integer.parseInt(serverParts[i]) : 0;
            int client = i < clientParts.length ? Integer.parseInt(clientParts[i]) : 0;
            // 客户端版本低，需要更新
            if (client < min) {
                return true;
            }
            // 客户端版本高，不需要更新
            if (client > min) {
                return false;
            }
        }
        // 版本完全相同，不需要更新
        return false;
    }

    @Override
    public DefVerticalScreenUpgrade getOne() {
        return superManager.getOne(Wraps.<DefVerticalScreenUpgrade>lbQ()
                .orderByDesc(DefVerticalScreenUpgrade::getCreatedTime)
                .last("limit 1"));
    }
}


