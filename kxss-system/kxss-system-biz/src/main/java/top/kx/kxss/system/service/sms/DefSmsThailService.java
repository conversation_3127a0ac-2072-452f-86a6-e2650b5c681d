package top.kx.kxss.system.service.sms;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.sms.DefSmsThail;
import top.kx.kxss.system.vo.save.sms.DefSmsThailSaveVO;
import top.kx.kxss.system.vo.update.sms.DefSmsThailUpdateVO;
import top.kx.kxss.system.vo.result.sms.DefSmsThailResultVO;
import top.kx.kxss.system.vo.query.sms.DefSmsThailPageQuery;


/**
 * <p>
 * 业务接口
 * 短信套餐
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-28 17:22:09
 * @create [2024-04-28 17:22:09] [yan] [代码生成器生成]
 */
public interface DefSmsThailService extends SuperService<Long, DefSmsThail, DefSmsThailSaveVO,
    DefSmsThailUpdateVO, DefSmsThailPageQuery, DefSmsThailResultVO> {

}


