package top.kx.kxss.system.manager.application;

import top.kx.basic.base.manager.SuperCacheManager;
import top.kx.basic.base.manager.SuperManager;
import top.kx.kxss.system.entity.application.DefResourceApi;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 * 通用业务层
 * 资源API
 * </p>
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2021/9/29 1:26 下午
 * @create [2021/9/29 1:26 下午 ] [tangyh] [初始创建]
 */
public interface DefResourceApiManager extends SuperCacheManager<DefResourceApi>, SuperManager<DefResourceApi> {
    /**
     * 根据资源id 删除资源的接口
     *
     * @param resourceIdList 资源id
     * <AUTHOR>
     * @date 2021/9/17 9:42 下午
     * @create [2021/9/17 9:42 下午 ] [tangyh] [初始创建]
     */
    void removeByResourceId(List<Long> resourceIdList);

    /**
     * 根据资源id查询资源接口
     *
     * @param resourceId 资源id
     * @return java.util.List<top.kx.kxss.tenant.vo.result.tenant.DefResourceApiResultVO>
     * <AUTHOR>
     * @date 2021/9/20 6:49 下午
     * @create [2021/9/20 6:49 下午 ] [tangyh] [初始创建]
     */
    List<DefResourceApi> findByResourceId(Long resourceId);

    /**
     * 根据资源id查询资源 api id
     *
     * @param resourceIdList
     * @return
     */
    List<DefResourceApi> findApiByResourceId(List<Long> resourceIdList);
    /**
     * 查询指定租户下指定应用和指定资源类型的 接口
     *
     * @param tenantId          租户ID
     * @param applicationIdList 应用ID
     * @param resourceTypes     资源类型
     * @return java.util.List<top.tangyh.lamp.system.entity.application.DefResourceApi>
     * <AUTHOR>
     * @date 2023/5/19 3:26 PM
     * @create [2023/5/19 3:26 PM ] [tangyh] [初始创建]
     */
    List<DefResourceApi> findResourceApi(Long tenantId, List<Long> applicationIdList,
                                         Collection<String> resourceTypes);
}
