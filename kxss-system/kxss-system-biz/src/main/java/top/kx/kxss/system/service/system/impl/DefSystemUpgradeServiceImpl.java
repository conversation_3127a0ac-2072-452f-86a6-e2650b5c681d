package top.kx.kxss.system.service.system.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.system.DefSystemUpgrade;
import top.kx.kxss.system.manager.system.DefSystemUpgradeManager;
import top.kx.kxss.system.service.system.DefSystemUpgradeService;
import top.kx.kxss.system.vo.query.system.DefSystemUpgradePageQuery;
import top.kx.kxss.system.vo.result.system.DefSystemUpgradeResultVO;
import top.kx.kxss.system.vo.save.system.DefSystemUpgradeSaveVO;
import top.kx.kxss.system.vo.update.system.DefSystemUpgradeUpdateVO;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 业务实现类
 * 系统升级公告
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-20 15:22:31
 * @create [2024-02-20 15:22:31] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class DefSystemUpgradeServiceImpl extends SuperServiceImpl<DefSystemUpgradeManager, Long, DefSystemUpgrade, DefSystemUpgradeSaveVO,
        DefSystemUpgradeUpdateVO, DefSystemUpgradePageQuery, DefSystemUpgradeResultVO> implements DefSystemUpgradeService {

    @Resource
    private EchoService echoService;

    @Override
    protected DefSystemUpgrade saveBefore(DefSystemUpgradeSaveVO defSystemUpgradeSaveVO) {
        if (CollUtil.isNotEmpty(defSystemUpgradeSaveVO.getOrderSourceList())) {
            defSystemUpgradeSaveVO.setOrderSource(JSON.toJSONString(defSystemUpgradeSaveVO.getOrderSourceList()));
        }
        return super.saveBefore(defSystemUpgradeSaveVO);
    }

    @Override
    protected DefSystemUpgrade updateBefore(DefSystemUpgradeUpdateVO defSystemUpgradeUpdateVO) {
        if (CollUtil.isNotEmpty(defSystemUpgradeUpdateVO.getOrderSourceList())) {
            defSystemUpgradeUpdateVO.setOrderSource(JSON.toJSONString(defSystemUpgradeUpdateVO.getOrderSourceList()));
        }
        return super.updateBefore(defSystemUpgradeUpdateVO);
    }

    @Override
    public Map<String, Object> checkVersion(String version) {
        Map<String, Object> map = MapUtil.newHashMap();
        DefSystemUpgrade defSystemUpgrade = superManager.getOne(Wraps.<DefSystemUpgrade>lbQ()
                .apply("JSON_CONTAINS(order_source, JSON_ARRAY({0}))", ContextUtil.getOrderSource())
                .orderByDesc(DefSystemUpgrade::getCreatedTime)
                .last("limit 1"));
        map.put("status", true);
        if (ObjectUtil.isNotNull(defSystemUpgrade)) {
            if (!ObjectUtil.equal(version, defSystemUpgrade.getVersion())) {
                map.put("vo", defSystemUpgrade);
                map.put("status", false);
            }
        }
        return map;
    }

    @Override
    public DefSystemUpgradeResultVO getOneByOrderSource() {
        if (StringUtils.isBlank(ContextUtil.getOrderSource())) {
            return null;
        }
        DefSystemUpgrade defSystemUpgrade = superManager.getOne(Wraps.<DefSystemUpgrade>lbQ()
                .apply("JSON_CONTAINS(order_source, JSON_ARRAY({0}))", ContextUtil.getOrderSource())
                .orderByDesc(DefSystemUpgrade::getCreatedTime)
                .last("limit 1"));
        if (ObjectUtil.isNull(defSystemUpgrade)) {
            return null;
        }
        DefSystemUpgradeResultVO resultVO = BeanPlusUtil.copyProperties(defSystemUpgrade, DefSystemUpgradeResultVO.class);
        if (Objects.nonNull(resultVO.getOrderSource())) {
            resultVO.setOrderSourceList(JSON.parseArray(resultVO.getOrderSource(), String.class));
            resultVO.setOrderSource(Strings.join(JSON.parseArray(resultVO.getOrderSource(), String.class), ','));
        }
        echoService.action(resultVO);
        return resultVO;
    }
}


