package top.kx.kxss.system.service.system.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.system.DefApplet;
import top.kx.kxss.system.entity.system.DefAppletNoticeTmpl;
import top.kx.kxss.system.manager.system.DefAppletNoticeTmplManager;
import top.kx.kxss.system.service.system.DefAppletNoticeTmplService;
import top.kx.kxss.system.service.system.DefAppletService;
import top.kx.kxss.system.vo.query.system.DefAppletNoticeTmplPageQuery;
import top.kx.kxss.system.vo.result.system.DefAppletNoticeTmplResultVO;
import top.kx.kxss.system.vo.save.system.DefAppletNoticeTmplSaveVO;
import top.kx.kxss.system.vo.update.system.DefAppletNoticeTmplUpdateVO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 小程序服务通知模版
 * </p>
 *
 * <AUTHOR>
 * @date 2023-10-25 15:39:53
 * @create [2023-10-25 15:39:53] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class DefAppletNoticeTmplServiceImpl extends SuperServiceImpl<DefAppletNoticeTmplManager, Long, DefAppletNoticeTmpl, DefAppletNoticeTmplSaveVO,
        DefAppletNoticeTmplUpdateVO, DefAppletNoticeTmplPageQuery, DefAppletNoticeTmplResultVO> implements DefAppletNoticeTmplService {


    @Autowired
    private DefAppletService defAppletService;

    @Override
    public DefAppletNoticeTmpl getOne(LbQueryWrap<DefAppletNoticeTmpl> queryWrap) {
        return superManager.getOne(queryWrap);
    }

    @Override
    public List<String> tmpList() {
        String clientId = ContextUtil.getClientId();
        if (StrUtil.isBlank(clientId)) {
            return Lists.newArrayList();
        }

        DefApplet defApplet = defAppletService.getOne(Wraps.<DefApplet>lbQ()
                .inSql(DefApplet::getClientId, "select id from def_client where delete_flag = 0 and " +
                        " state = 1 and client_id = '" + clientId + "'")
                .orderByDesc(DefApplet::getCreatedTime)
                .last(" limit 1"));
        if (ObjectUtil.isNull(defApplet)) {
            return Lists.newArrayList();
        }
        return superManager.listObjs(Wraps.<DefAppletNoticeTmpl>lbQ()
                .select(DefAppletNoticeTmpl::getTmplId)
                .eq(DefAppletNoticeTmpl::getAppletId, defApplet.getId())
                .eq(DefAppletNoticeTmpl::getDeleteFlag, 0)
                .orderByDesc(DefAppletNoticeTmpl::getCreatedTime), Convert::toStr)
                .stream().distinct().collect(Collectors.toList());
    }
}


