package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DefAppletToken;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DefAppletTokenManager;
import top.kx.kxss.system.mapper.system.DefAppletTokenMapper;

/**
 * <p>
 * 通用业务实现类
 * 客户小程序令牌信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-27 10:41:58
 * @create [2024-04-27 10:41:58] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefAppletTokenManagerImpl extends SuperManagerImpl<DefAppletTokenMapper, DefAppletToken> implements DefAppletTokenManager {

}


