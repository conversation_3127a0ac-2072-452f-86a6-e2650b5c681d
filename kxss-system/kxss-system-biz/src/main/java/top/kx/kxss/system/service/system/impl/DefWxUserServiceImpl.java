package top.kx.kxss.system.service.system.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import io.seata.common.util.CollectionUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.system.DefWxUser;
import top.kx.kxss.system.manager.system.DefWxUserManager;
import top.kx.kxss.system.service.system.DefWxUserService;
import top.kx.kxss.system.vo.query.system.DefWxUserPageQuery;
import top.kx.kxss.system.vo.result.system.DefWxUserResultVO;
import top.kx.kxss.system.vo.save.system.DefWxUserSaveVO;
import top.kx.kxss.system.vo.update.system.DefWxUserUpdateVO;

import java.util.List;

/**
 * <p>
 * 业务实现类
 * 微信用户
 * </p>
 *
 * <AUTHOR>
 * @date 2023-12-11 11:40:01
 * @create [2023-12-11 11:40:01] [yh] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class DefWxUserServiceImpl extends SuperServiceImpl<DefWxUserManager, Long, DefWxUser, DefWxUserSaveVO,
        DefWxUserUpdateVO, DefWxUserPageQuery, DefWxUserResultVO> implements DefWxUserService {


    @Override
    public List<DefWxUser> getByOpenidAndIsSubscribe(String openid, Boolean isSubscribe) {
        LbQueryWrap<DefWxUser> queryWrap = Wraps.<DefWxUser>lbQ()
                .eq(DefWxUser::getOpenId, openid)
                .eq(DefWxUser::getIsSubscribe, isSubscribe)
                .eq(DefWxUser::getDeleteFlag, 0);
        return this.list(queryWrap);
    }

    @Override
    public DefWxUser getByOpenidAndPhone(String openid, String phone) {
        LbQueryWrap<DefWxUser> queryWrap = Wraps.<DefWxUser>lbQ()
                .eq(DefWxUser::getOpenId, openid)
                .eq(DefWxUser::getPhone, phone)
                .eq(DefWxUser::getDeleteFlag, 0);
        List<DefWxUser> list = this.list(queryWrap);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public DefWxUser getByPhoneAndIsSubscribe(String phone, Boolean isSubscribe, Long tenantId, Long orgId) {
        LbQueryWrap<DefWxUser> queryWrap = Wraps.<DefWxUser>lbQ()
                .eq(DefWxUser::getPhone, phone)
                .eq(DefWxUser::getIsSubscribe, isSubscribe)
                .eq(DefWxUser::getTenantId, tenantId)
                .eq(DefWxUser::getOrgId, orgId)
                .eq(DefWxUser::getDeleteFlag, 0)
                .orderByDesc(DefWxUser::getCreatedTime);
        List<DefWxUser> list = this.list(queryWrap);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }
    @Override
    public DefWxUser getByMemberIdAndIsSubscribe(Long memberId, Boolean isSubscribe, Long tenantId, Long orgId) {
        LbQueryWrap<DefWxUser> queryWrap = Wraps.<DefWxUser>lbQ()
                .eq(DefWxUser::getMemberId, memberId)
                .eq(DefWxUser::getIsSubscribe, isSubscribe)
                .eq(DefWxUser::getTenantId, tenantId)
                .eq(DefWxUser::getOrgId, orgId)
                .eq(DefWxUser::getDeleteFlag, 0)
                .orderByDesc(DefWxUser::getCreatedTime);
        List<DefWxUser> list = this.list(queryWrap);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public List<DefWxUser> getByPhoneListAndIsSubscribe(List<String> phoneList, Boolean isSubscribe, Long tenantId, Long orgId) {
        LbQueryWrap<DefWxUser> queryWrap = Wraps.<DefWxUser>lbQ()
                .in(DefWxUser::getPhone, phoneList)
                .eq(DefWxUser::getIsSubscribe, isSubscribe)
                .eq(DefWxUser::getTenantId, tenantId)
                .eq(DefWxUser::getOrgId, orgId)
                .eq(DefWxUser::getDeleteFlag, 0);
        return this.list(queryWrap);
    }

    @Override
    public List<DefWxUser> getByMemberIdListAndIsSubscribe(List<Long> memberIdList, boolean isSubscribe, Long tenantId, Long orgId) {
        LbQueryWrap<DefWxUser> queryWrap = Wraps.<DefWxUser>lbQ()
                .in(DefWxUser::getMemberId, memberIdList)
                .eq(DefWxUser::getIsSubscribe, isSubscribe)
                .eq(DefWxUser::getTenantId, tenantId)
                .eq(DefWxUser::getOrgId, orgId)
                .eq(DefWxUser::getDeleteFlag, 0);
        return this.list(queryWrap);
    }

    @Override
    public List<DefWxUser> getByOpenidAndIsSubscribe(String openId, boolean isSubscribe, Long tenantId, Long orgId) {
        LbQueryWrap<DefWxUser> queryWrap = Wraps.<DefWxUser>lbQ()
                .eq(DefWxUser::getOpenId, openId)
//                .eq(DefWxUser::getTenantId, tenantId)
//                .eq(DefWxUser::getOrgId, orgId)
                .eq(DefWxUser::getIsSubscribe, isSubscribe)
                .eq(DefWxUser::getDeleteFlag, 0);
        return this.list(queryWrap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean remove(LbUpdateWrap<DefWxUser> eq) {
        return superManager.remove(eq);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchById(List<DefWxUser> defWxUserList) {
        return superManager.updateBatchById(defWxUserList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(LbUpdateWrap<DefWxUser> eq) {
        return superManager.update(eq);
    }
}


