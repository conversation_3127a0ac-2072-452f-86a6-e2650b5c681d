package top.kx.kxss.system.manager.deal.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.deal.DealOrder;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.deal.DealOrderManager;
import top.kx.kxss.system.mapper.deal.DealOrderMapper;

/**
 * <p>
 * 通用业务实现类
 * 订单记录
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-23 10:19:46
 * @create [2024-10-23 10:19:46] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DealOrderManagerImpl extends SuperManagerImpl<DealOrderMapper, DealOrder> implements DealOrderManager {

}


