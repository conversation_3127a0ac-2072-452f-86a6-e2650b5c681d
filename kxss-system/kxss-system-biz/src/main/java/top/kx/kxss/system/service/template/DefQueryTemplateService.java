package top.kx.kxss.system.service.template;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.interfaces.echo.LoadService;
import top.kx.kxss.system.entity.template.DefQueryTemplate;
import top.kx.kxss.system.vo.save.template.DefQueryTemplateSaveVO;
import top.kx.kxss.system.vo.update.template.DefQueryTemplateUpdateVO;
import top.kx.kxss.system.vo.result.template.DefQueryTemplateResultVO;
import top.kx.kxss.system.vo.query.template.DefQueryTemplatePageQuery;


/**
 * <p>
 * 业务接口
 * 查询模板
 * </p>
 *
 * <AUTHOR>
 * @date 2024-01-06 17:11:55
 * @create [2024-01-06 17:11:55] [yh] [代码生成器生成]
 */
public interface DefQueryTemplateService extends SuperService<Long, DefQueryTemplate, DefQueryTemplateSaveVO,
    DefQueryTemplateUpdateVO, DefQueryTemplatePageQuery, DefQueryTemplateResultVO>, LoadService {

}


