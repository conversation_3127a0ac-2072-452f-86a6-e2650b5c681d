package top.kx.kxss.system.service.template.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.cache.repository.CacheOps;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.utils.CollHelper;
import top.kx.kxss.base.entity.banner.BaseBanner;
import top.kx.kxss.base.vo.save.banner.BaseBannerSaveVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.RedisConstant;
import top.kx.kxss.system.service.template.DefQueryTemplateService;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.system.manager.template.DefQueryTemplateManager;
import top.kx.kxss.system.entity.template.DefQueryTemplate;
import top.kx.kxss.system.vo.save.template.DefQueryTemplateSaveVO;
import top.kx.kxss.system.vo.update.template.DefQueryTemplateUpdateVO;
import top.kx.kxss.system.vo.result.template.DefQueryTemplateResultVO;
import top.kx.kxss.system.vo.query.template.DefQueryTemplatePageQuery;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 查询模板
 * </p>
 *
 * <AUTHOR>
 * @date 2024-01-06 17:11:55
 * @create [2024-01-06 17:11:55] [yh] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
@Transactional(readOnly = true)
public class DefQueryTemplateServiceImpl extends SuperServiceImpl<DefQueryTemplateManager, Long, DefQueryTemplate, DefQueryTemplateSaveVO,
    DefQueryTemplateUpdateVO, DefQueryTemplatePageQuery, DefQueryTemplateResultVO> implements DefQueryTemplateService {
    @Autowired
    protected  CacheOps cacheOps;
    protected DefQueryTemplate updateBefore(DefQueryTemplateUpdateVO  updateVO) {
        //更新清除cache
        cacheOps.del(RedisConstant.TEMP_QUERY+updateVO.getId().toString());
        return super.updateBefore(updateVO);
    }
    @Override
    @DS(DsConstant.DEFAULTS)
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        LbQueryWrap<DefQueryTemplate> defQueryTemplateWrap = Wraps.lbQ();
        defQueryTemplateWrap.in(DefQueryTemplate::getId, ids);

        List<DefQueryTemplate> defQueryTemplateInfos = list(defQueryTemplateWrap);
        if (CollUtil.isNotEmpty(defQueryTemplateInfos)) {
            List<DefQueryTemplate> list = defQueryTemplateInfos.stream().filter(Objects::nonNull).collect(Collectors.toList());
            return CollHelper.uniqueIndex(list, DefQueryTemplate::getId, DefQueryTemplate::getName);
        } else {
            return new HashMap<>();
        }
    }
}


