package top.kx.kxss.system.manager.sms.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.sms.DefSmsRecharge;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.sms.DefSmsRechargeManager;
import top.kx.kxss.system.mapper.sms.DefSmsRechargeMapper;

/**
 * <p>
 * 通用业务实现类
 * 短信充值
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-28 17:22:09
 * @create [2024-04-28 17:22:09] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefSmsRechargeManagerImpl extends SuperManagerImpl<DefSmsRechargeMapper, DefSmsRecharge> implements DefSmsRechargeManager {

}


