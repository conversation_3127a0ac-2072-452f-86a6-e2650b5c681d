package top.kx.kxss.system.service.sms.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.app.cash.member.MemberApi;
import top.kx.kxss.base.vo.query.member.MemberInfoMobileQuery;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.utils.FreeMarkerUtil;
import top.kx.kxss.employee.EmployeeApi;
import top.kx.kxss.model.enumeration.system.SmsSendStateEnum;
import top.kx.kxss.model.enumeration.system.SmsSendTypeEnum;
import top.kx.kxss.sms.SmsInfoApi;
import top.kx.kxss.system.entity.sms.DefSmsSendRecords;
import top.kx.kxss.system.entity.sms.DefSmsSendRecordsExtra;
import top.kx.kxss.system.entity.tenant.DefTenant;
import top.kx.kxss.system.entity.tenant.DefTenantOrg;
import top.kx.kxss.system.manager.sms.DefSmsSendRecordsManager;
import top.kx.kxss.system.service.sms.DefSmsSendRecordsExtraService;
import top.kx.kxss.system.service.sms.DefSmsSendRecordsService;
import top.kx.kxss.system.service.tenant.DefTenantOrgService;
import top.kx.kxss.system.service.tenant.DefTenantService;
import top.kx.kxss.system.vo.query.sms.DefSmsSendRecordsPageQuery;
import top.kx.kxss.system.vo.result.sms.DefSmsInfoResultVO;
import top.kx.kxss.system.vo.result.sms.DefSmsSendRecordsResultVO;
import top.kx.kxss.system.vo.save.sms.DefSmsSendRecordsSaveVO;
import top.kx.kxss.system.vo.update.sms.DefSmsInfoUpdateNumVO;
import top.kx.kxss.system.vo.update.sms.DefSmsSendRecordsUpdateVO;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 短信发送记录
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-28 17:22:09
 * @create [2024-04-28 17:22:09] [yan] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class DefSmsSendRecordsServiceImpl extends SuperServiceImpl<DefSmsSendRecordsManager, Long, DefSmsSendRecords, DefSmsSendRecordsSaveVO,
        DefSmsSendRecordsUpdateVO, DefSmsSendRecordsPageQuery, DefSmsSendRecordsResultVO> implements DefSmsSendRecordsService {


    @Resource
    private DefSmsSendRecordsExtraService smsSendRecordsExtraService;
    @Resource
    private DefTenantService defTenantService;
    @Resource
    private DefTenantOrgService defTenantOrgService;
    @Resource
    private EmployeeApi employeeApi;
    @Resource
    private SmsInfoApi smsInfoApi;
    @Resource
    private MemberApi memberApi;

    @GlobalTransactional
    @Override
    public DefSmsSendRecords saveMarketing(DefSmsSendRecordsSaveVO saveVO) {

        // 如果手机号为空, 获取筛选条件, 筛选数据
        if (Objects.nonNull(saveVO.getIsAllSelect()) && saveVO.getIsAllSelect()) {
            R<List<String>> listR = memberApi.mobileList(BeanPlusUtil.toBean(saveVO, MemberInfoMobileQuery.class));
            ArgumentAssert.isFalse(!listR.getIsSuccess(), "全选获取手机号失败,请单选会员用户");
            ArgumentAssert.notNull(listR.getData(), "手机号码为空,请选择需要发送的手机号");
            List<String> mobileList = listR.getData().stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            ArgumentAssert.notNull(mobileList, "手机号码为空,请选择需要发送的手机号");
            saveVO.setMobileList(mobileList);
        }

        if (StringUtils.isBlank(saveVO.getType())) {
            saveVO.setType(SmsSendTypeEnum.MARKETING.getCode());
        }

        // 处理短信模板参数替换
        if (StringUtils.isNotBlank(saveVO.getTemplate()) && saveVO.getTemplateParams() != null && !saveVO.getTemplateParams().isEmpty()) {
            String replacedContent = replaceSmsTemplate(saveVO.getTemplate(), saveVO.getTemplateParams());
            saveVO.setContent(replacedContent);
            log.info("短信模板替换完成: 原模板={}, 参数={}, 替换后内容={}", saveVO.getTemplate(), saveVO.getTemplateParams(), replacedContent);
        }

        String employeeName = null;
        if (Objects.nonNull(ContextUtil.getEmployeeId())) {
            R<BaseEmployeeResultVO> baseEmployeeResultVOR = employeeApi.get(ContextUtil.getEmployeeId());
            if (baseEmployeeResultVOR.getIsSuccess() && Objects.nonNull(baseEmployeeResultVOR.getData())) {
                employeeName = baseEmployeeResultVOR.getData().getRealName();
            }
        }

        DefTenant defTenant = defTenantService.getById(ContextUtil.getTenantId());
        DefTenantOrg defTenantOrg = defTenantOrgService.getOne(Wraps.<DefTenantOrg>lbQ().eq(DefTenantOrg::getDeleteFlag, 0)
                .eq(DefTenantOrg::getTenantId, ContextUtil.getTenantId())
                .eq(DefTenantOrg::getOrgId, ContextUtil.getCurrentCompanyId()));
        saveVO.setTenantId(ContextUtil.getTenantId());
        saveVO.setTenantName(Objects.nonNull(defTenant) ? defTenant.getName() : null);
        saveVO.setOrgId(ContextUtil.getCurrentCompanyId());
        saveVO.setOrgName(Objects.nonNull(defTenantOrg) ? defTenantOrg.getOrgName() : null);
        saveVO.setEmployeeId(ContextUtil.getEmployeeId());
        saveVO.setEmployeeName(employeeName);

        // 计算消耗次数 (length / 70) + (length % 70 == 0 ? 0 : 1)
        saveVO.setSingleNum((saveVO.getContent().length() / 70) + (saveVO.getContent().length() % 70 == 0 ? 0 : 1));
        saveVO.setNum(saveVO.getMobileList().size() * saveVO.getSingleNum());
        saveVO.setState("0");
        DefSmsSendRecords sendRecords = BeanPlusUtil.toBean(saveVO, DefSmsSendRecords.class);

        // 判断总条数是否足够
        R<DefSmsInfoResultVO> resultVOR = smsInfoApi.getSmsNum();
        ArgumentAssert.isFalse(Objects.isNull(resultVOR) || !resultVOR.getIsSuccess() || Objects.isNull(resultVOR.getData())
                || (resultVOR.getData().getTotal() - resultVOR.getData().getUse() <= saveVO.getNum()), "短信条数不足,请充值");

        superManager.save(sendRecords);

        List<DefSmsSendRecordsExtra> extraList = new ArrayList<>();
        for (String mobile : saveVO.getMobileList()) {
            extraList.add(DefSmsSendRecordsExtra.builder().recordsId(sendRecords.getId()).mobile(mobile).build());
        }
        smsSendRecordsExtraService.saveBatch(extraList);
        return sendRecords;
    }

    @GlobalTransactional
    @Override
    public DefSmsSendRecords update(DefSmsSendRecordsUpdateVO updateVO) {

        DefSmsSendRecords smsSendRecords = getById(updateVO.getId());
        ArgumentAssert.notNull(smsSendRecords, "发送记录不存在");
        DefSmsSendRecords records = updateById(updateVO);

        DefSmsInfoUpdateNumVO updateNumVO = DefSmsInfoUpdateNumVO.builder()
                .tenantId(records.getTenantId())
                .tenantName(records.getTenantName())
                .orgId(records.getOrgId())
                .orgName(records.getOrgName())
                .isUse(true)
                .num(updateVO.getRealNum())
                .build();

        // 判断是否不同
        if (!StringUtils.equals(smsSendRecords.getState(), updateVO.getState())) {
            // 判断是不是由待生效改为 发送成功
            if (SmsSendStateEnum.CONFIRMED.getCode().equals(smsSendRecords.getState()) && SmsSendStateEnum.SEND.getCode().equals(updateVO.getState())) {
                // 总条数增加
                updateNumVO.setIsAdd(true);
            }
            // 发送成功改为发送失败
            if (SmsSendStateEnum.SEND.getCode().equals(smsSendRecords.getState()) && SmsSendStateEnum.NO_SEND.getCode().equals(updateVO.getState())) {
                // 总条数减少
                updateNumVO.setIsAdd(false);
            }
            // 发送成功,改为待确认
            if (SmsSendStateEnum.SEND.getCode().equals(smsSendRecords.getState()) && SmsSendStateEnum.CONFIRMED.getCode().equals(updateVO.getState())) {
                // 总条数减少
                updateNumVO.setIsAdd(false);
            }
            if (SmsSendStateEnum.NO_SEND.getCode().equals(smsSendRecords.getState()) && SmsSendStateEnum.SEND.getCode().equals(updateVO.getState())) {
                // 总条数增加
                updateNumVO.setIsAdd(true);
            }
            if (Objects.nonNull(updateNumVO.getIsAdd())) {
                R<Boolean> booleanR = smsInfoApi.updateSmsNum(updateNumVO);
                ArgumentAssert.isFalse(Objects.isNull(booleanR) || !booleanR.getIsSuccess() || !booleanR.getData(), "短信条数处理失败,请联系管理员");
            }
            return records;
        }

        // 判断是不是生效中
        if (SmsSendStateEnum.SEND.getCode().equals(updateVO.getState())) {
            if (Objects.isNull(smsSendRecords.getRealNum())) {
                smsSendRecords.setRealNum(0);
            }
            // 状态相同,只有 - 如果是生效中的, 比较原本的数量, 如果不相同,就增加或者减少
            if (!Objects.equals(updateVO.getRealNum(), smsSendRecords.getRealNum())) {
                if (updateVO.getRealNum() < smsSendRecords.getRealNum()) {
                    updateNumVO.setIsAdd(true);
                    updateNumVO.setNum(smsSendRecords.getRealNum() - updateVO.getRealNum());
                } else {
                    updateNumVO.setIsAdd(false);
                    updateNumVO.setNum(updateVO.getRealNum() - smsSendRecords.getRealNum());
                }
                R<Boolean> booleanR = smsInfoApi.updateSmsNum(updateNumVO);
                ArgumentAssert.isFalse(Objects.isNull(booleanR) || !booleanR.getIsSuccess() || !booleanR.getData(), "短信条数处理失败,请联系管理员");
                return records;
            }
        }

        return records;
    }

    /**
     * 短信模板参数自动映射
     *
     * @param template 短信模板内容，例如：尊敬的${memberName}，您好！您于${time}在${orgName}门店消费了${amount}元，当前余额${balance}元，如有疑问请立即联系商家处理。
     * @param paramMap 参数Map，例如：{memberName: "张三", time: "2024-01-01 10:30", orgName: "XX门店", amount: "100.00", balance: "500.00"}
     * @return 替换后的短信内容
     */
    public String replaceSmsTemplate(String template, Map<String, Object> paramMap) {
        if (StringUtils.isBlank(template) || paramMap == null || paramMap.isEmpty()) {
            return template;
        }

        try {
            // 使用FreeMarker模板引擎进行参数替换
            return FreeMarkerUtil.generateString(template, paramMap);
        } catch (Exception e) {
            log.error("短信模板参数替换失败: template={}, paramMap={}", template, paramMap, e);
            return template;
        }
    }

    /**
     * 短信模板参数替换工具方法示例
     * <p>
     * 使用示例：
     * 1. 定义短信模板（支持FreeMarker语法）：
     * String template = "尊敬的${memberName}，您好！您于${time}在${orgName}门店消费了${amount}元，当前余额${balance}元，如有疑问请立即联系商家处理。";
     * <p>
     * 2. 准备参数Map：
     * Map<String, Object> params = new HashMap<>();
     * params.put("memberName", "张三");
     * params.put("time", "2024-01-01 10:30");
     * params.put("orgName", "XX门店");
     * params.put("amount", "100.00");
     * params.put("balance", "500.00");
     * <p>
     * 3. 调用替换方法：
     * String result = replaceSmsTemplate(template, params);
     * <p>
     * 4. 得到的结果：
     * "尊敬的张三，您好！您于2024-01-01 10:30在XX门店门店消费了100.00元，当前余额500.00元，如有疑问请立即联系商家处理。"
     */
    public static void smsTemplateUsageExample() {
        // 示例代码在注释中提供，不需要实际执行
        log.info("短信模板参数替换功能使用示例：请查看方法注释");
    }

    @Override
    public DefSmsSendRecords saveRecord(DefSmsSendRecords defSmsSendRecords) {
        String employeeName = "";
        if (Objects.nonNull(defSmsSendRecords.getEmployeeId())) {
            R<BaseEmployeeResultVO> baseEmployeeResultVO = employeeApi.get(ContextUtil.getEmployeeId());
            if (baseEmployeeResultVO.getIsSuccess() && Objects.nonNull(baseEmployeeResultVO.getData())) {
                employeeName = baseEmployeeResultVO.getData().getRealName();
            }
        }
        defSmsSendRecords.setEmployeeName(employeeName);
        DefTenant defTenant = defTenantService.getById(ContextUtil.getTenantId());
        DefTenantOrg defTenantOrg = defTenantOrgService.getOne(Wraps.<DefTenantOrg>lbQ().eq(DefTenantOrg::getDeleteFlag, 0)
                .eq(DefTenantOrg::getTenantId, ContextUtil.getTenantId())
                .eq(DefTenantOrg::getOrgId, ContextUtil.getCurrentCompanyId()));
        defSmsSendRecords.setTenantName(Objects.nonNull(defTenant) ? defTenant.getName() : null);
        defSmsSendRecords.setOrgName(Objects.nonNull(defTenantOrg) ? defTenantOrg.getOrgName() : null);
        // 计算消耗次数 (length / 70) + (length % 70 == 0 ? 0 : 1)
        defSmsSendRecords.setSingleNum(1);
        if (defSmsSendRecords.getContent().length() > 70) {
            defSmsSendRecords.setSingleNum(defSmsSendRecords.getContent().length() % 67 > 0 ? defSmsSendRecords.getContent().length() / 67 + 1
                    : defSmsSendRecords.getContent().length() / 67);
        }
        defSmsSendRecords.setNum(defSmsSendRecords.getNum() * defSmsSendRecords.getSingleNum());
        defSmsSendRecords.setRealNum(defSmsSendRecords.getNum());
        defSmsSendRecords.setState("0");
        defSmsSendRecords.setDescribe("系统短信发送");
        superManager.save(defSmsSendRecords);
        return defSmsSendRecords;
    }

    @Override
    public Boolean sendSuccess(Long recordId) {
        DefSmsSendRecords defSmsSendRecords = superManager.getById(recordId);
        if (defSmsSendRecords == null) {
            return false;
        }
        defSmsSendRecords.setCompleteTime(LocalDateTime.now());
        defSmsSendRecords.setState("1");
        try {
            smsInfoApi.updateSmsNum(DefSmsInfoUpdateNumVO.builder()
                    .tenantId(defSmsSendRecords.getTenantId())
                    .orgId(defSmsSendRecords.getOrgId())
                    .num(defSmsSendRecords.getNum())
                    .isAdd(false)
                    .isUse(true)
                    .build());
        } catch (Exception e) {
            log.error("短信条数处理失败,请联系管理员", e);
        }
        return superManager.updateById(defSmsSendRecords);
    }

    @Override
    public Boolean sendFail(Long recordId) {
        DefSmsSendRecords defSmsSendRecords = superManager.getById(recordId);
        if (defSmsSendRecords == null) {
            return false;
        }
        defSmsSendRecords.setCompleteTime(LocalDateTime.now());
        defSmsSendRecords.setState("2");
        try {
            smsInfoApi.updateSmsNum(DefSmsInfoUpdateNumVO.builder()
                    .tenantId(defSmsSendRecords.getTenantId())
                    .orgId(defSmsSendRecords.getOrgId())
                    .num(defSmsSendRecords.getNum())
                    .isAdd(false)
                    .isUse(true)
                    .build());
        } catch (Exception e) {
            log.error("短信条数处理失败,请联系管理员", e);
        }
        return superManager.updateById(defSmsSendRecords);
    }
}


