package top.kx.kxss.system.service.subscription.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.exception.BizException;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.subscription.SubscriptionFeature;
import top.kx.kxss.system.entity.subscription.SubscriptionTemplate;
import top.kx.kxss.system.entity.subscription.SubscriptionTemplateFeature;
import top.kx.kxss.system.manager.subscription.SubscriptionFeatureManager;
import top.kx.kxss.system.manager.subscription.SubscriptionTemplateFeatureManager;
import top.kx.kxss.system.manager.subscription.SubscriptionTemplateManager;
import top.kx.kxss.system.service.subscription.SubscriptionFeatureService;
import top.kx.kxss.system.vo.query.subscription.SubscriptionFeaturePageQuery;
import top.kx.kxss.system.vo.result.subscription.SubscriptionFeatureResultVO;
import top.kx.kxss.system.vo.save.subscription.SubscriptionFeatureSaveVO;
import top.kx.kxss.system.vo.update.subscription.SubscriptionFeatureUpdateVO;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 功能权益
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-07 10:17:58
 * @create [2025-05-07 10:17:58] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class SubscriptionFeatureServiceImpl extends SuperServiceImpl<SubscriptionFeatureManager, Long, SubscriptionFeature, SubscriptionFeatureSaveVO,
        SubscriptionFeatureUpdateVO, SubscriptionFeaturePageQuery, SubscriptionFeatureResultVO> implements SubscriptionFeatureService {

    private final EchoService echoService;

    private final SubscriptionTemplateFeatureManager templateFeatureManager;
    private final SubscriptionTemplateManager templateManager;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateState(Long id, Boolean state) {
        SubscriptionFeature build = SubscriptionFeature.builder().enabled(state).build();
        build.setId(id);
        return superManager.updateById(build);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateIsLimitCount(Long id, Boolean isLimitCount) {
        SubscriptionFeature build = SubscriptionFeature.builder().isLimitCount(isLimitCount).build();
        build.setId(id);
        return superManager.updateById(build);
    }

    @Override
    public boolean checkCode(String code, Long id) {
        return superManager.count(Wraps.<SubscriptionFeature>lbQ().ne(SubscriptionFeature::getId, id)
                .eq(SubscriptionFeature::getCode, code)) > 0;

    }

    @Override
    public boolean checkName(String name, Long id) {
        return superManager.count(Wraps.<SubscriptionFeature>lbQ().ne(SubscriptionFeature::getId, id)
                .eq(SubscriptionFeature::getName, name)) > 0;

    }

    @Override
    public List<SubscriptionFeatureResultVO> queryTemplateFeature() {
        List<SubscriptionFeature> featureList = superManager.list(Wraps.<SubscriptionFeature>lbQ()
//                .eq(SubscriptionFeature::getType, SubscriptionFeatureTypeEnum.TEMPLATE.getCode())
                .eq(SubscriptionFeature::getDeleteFlag, 0)
                .orderByAsc(SubscriptionFeature::getSort));
        List<SubscriptionFeatureResultVO> resultList = BeanPlusUtil.copyToList(featureList, SubscriptionFeatureResultVO.class);
        echoService.action(resultList);
        return resultList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteByIds(List<Long> ids) {
        List<Long> tmpIds = templateFeatureManager.list(Wraps.<SubscriptionTemplateFeature>lbQ()
                        .eq(SubscriptionTemplateFeature::getDeleteFlag, 0)
                        .in(SubscriptionTemplateFeature::getFeatureId, ids))
                .stream().map(SubscriptionTemplateFeature::getTmpId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(tmpIds)) {
            String string = templateManager.list(Wraps.<SubscriptionTemplate>lbQ()
                            .eq(SubscriptionTemplate::getDeleteFlag, 0)
                            .in(SubscriptionTemplate::getId, tmpIds))
                    .stream().map(SubscriptionTemplate::getName).distinct().collect(Collectors.joining(","));
            if (StrUtil.isNotBlank(string)) {
                throw new BizException("套餐模板[" + string + "]正在使用，无法删除");
            }
        }
        return superManager.removeByIds(ids);
    }
}


