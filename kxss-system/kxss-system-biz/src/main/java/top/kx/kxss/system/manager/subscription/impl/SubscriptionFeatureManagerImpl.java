package top.kx.kxss.system.manager.subscription.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.subscription.SubscriptionFeature;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.subscription.SubscriptionFeatureManager;
import top.kx.kxss.system.mapper.subscription.SubscriptionFeatureMapper;

/**
 * <p>
 * 通用业务实现类
 * 功能权益
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-07 10:17:58
 * @create [2025-05-07 10:17:58] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SubscriptionFeatureManagerImpl extends SuperManagerImpl<SubscriptionFeatureMapper, SubscriptionFeature> implements SubscriptionFeatureManager {

}


