package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DefWxTemplate;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DefWxTemplateManager;
import top.kx.kxss.system.mapper.system.DefWxTemplateMapper;

/**
 * <p>
 * 通用业务实现类
 * 微信模板
 * </p>
 *
 * <AUTHOR>
 * @date 2023-12-11 11:42:11
 * @create [2023-12-11 11:42:11] [yh] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefWxTemplateManagerImpl extends SuperManagerImpl<DefWxTemplateMapper, DefWxTemplate> implements DefWxTemplateManager {

}


