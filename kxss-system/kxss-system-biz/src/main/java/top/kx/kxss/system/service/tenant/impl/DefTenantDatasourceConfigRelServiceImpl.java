package top.kx.kxss.system.service.tenant.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.tenant.DefTenantDatasourceConfigRel;
import top.kx.kxss.system.manager.tenant.DefTenantDatasourceConfigRelManager;
import top.kx.kxss.system.service.tenant.DefTenantDatasourceConfigRelService;
import top.kx.kxss.system.vo.query.tenant.DefTenantDatasourceConfigRelPageQuery;
import top.kx.kxss.system.vo.result.tenant.DefTenantDatasourceConfigRelResultVO;
import top.kx.kxss.system.vo.save.tenant.DefTenantDatasourceConfigRelSaveVO;
import top.kx.kxss.system.vo.update.tenant.DefTenantDatasourceConfigRelUpdateVO;

/**
 * <p>
 * 业务实现类
 * 数据源
 * </p>
 *
 * <AUTHOR>
 * @date 2021-09-13
 */
@Slf4j
@Service
@DS(DsConstant.DEFAULTS)
@RequiredArgsConstructor
public class DefTenantDatasourceConfigRelServiceImpl extends SuperServiceImpl<DefTenantDatasourceConfigRelManager, Long, DefTenantDatasourceConfigRel, DefTenantDatasourceConfigRelSaveVO, DefTenantDatasourceConfigRelUpdateVO, DefTenantDatasourceConfigRelPageQuery, DefTenantDatasourceConfigRelResultVO>
        implements DefTenantDatasourceConfigRelService {

    @Override
    public void remove(LbQueryWrap<DefTenantDatasourceConfigRel> in) {
        superManager.remove(in);
    }
}
