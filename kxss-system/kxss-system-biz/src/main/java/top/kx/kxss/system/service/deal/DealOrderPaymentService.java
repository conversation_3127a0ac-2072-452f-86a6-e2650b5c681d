package top.kx.kxss.system.service.deal;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.system.entity.deal.DealOrder;
import top.kx.kxss.system.entity.deal.DealOrderPayment;
import top.kx.kxss.system.vo.save.deal.DealOrderPaymentSaveVO;
import top.kx.kxss.system.vo.update.deal.DealOrderPaymentUpdateVO;
import top.kx.kxss.system.vo.result.deal.DealOrderPaymentResultVO;
import top.kx.kxss.system.vo.query.deal.DealOrderPaymentPageQuery;


/**
 * <p>
 * 业务接口
 * 团购充值收款记录
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-24 14:08:50
 * @create [2024-10-24 14:08:50] [dou] [代码生成器生成]
 */
public interface DealOrderPaymentService extends SuperService<Long, DealOrderPayment, DealOrderPaymentSaveVO,
    DealOrderPaymentUpdateVO, DealOrderPaymentPageQuery, DealOrderPaymentResultVO> {

    boolean save(DealOrderPayment build);

    DealOrderPayment createCashPayment(DealOrder dealOrder, String mchOrderNo);

    DealOrderPayment getOne(LbQueryWrap<DealOrderPayment> eq);

    boolean updateById(DealOrderPayment dealOrderPayment);

}


