package top.kx.kxss.system.service.tenant;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.tenant.DefDatasourceConfig;
import top.kx.kxss.system.vo.query.tenant.DefDatasourceConfigPageQuery;
import top.kx.kxss.system.vo.result.tenant.DefDatasourceConfigResultVO;
import top.kx.kxss.system.vo.save.tenant.DefDatasourceConfigSaveVO;
import top.kx.kxss.system.vo.update.tenant.DefDatasourceConfigUpdateVO;

/**
 * <p>
 * 业务接口
 * 数据源
 * </p>
 *
 * <AUTHOR>
 * @date 2021-09-13
 */
public interface DefDatasourceConfigService extends SuperService<Long, DefDatasourceConfig, DefDatasourceConfigSaveVO, DefDatasourceConfigUpdateVO, DefDatasourceConfigPageQuery, DefDatasourceConfigResultVO> {
    /**
     * 测试数据源链接
     *
     * @param id 数据源信息
     * @return
     */
    Boolean testConnection(Long id);
}
