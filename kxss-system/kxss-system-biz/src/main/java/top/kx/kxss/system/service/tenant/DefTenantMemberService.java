package top.kx.kxss.system.service.tenant;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.system.entity.tenant.DefTenantMember;
import top.kx.kxss.system.vo.query.tenant.DefTenantMemberPageQuery;
import top.kx.kxss.system.vo.result.tenant.DefTenantMemberResultVO;
import top.kx.kxss.system.vo.save.tenant.DefTenantMemberSaveVO;
import top.kx.kxss.system.vo.update.tenant.DefTenantMemberUpdateVO;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 租户会员
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-29 15:09:15
 * @create [2024-02-29 15:09:15] [dou] [代码生成器生成]
 */
public interface DefTenantMemberService extends SuperService<Long, DefTenantMember, DefTenantMemberSaveVO,
        DefTenantMemberUpdateVO, DefTenantMemberPageQuery, DefTenantMemberResultVO> {

    Boolean update(LbUpdateWrap<DefTenantMember> defTenantMemberLbUpdateWrap);

    boolean saveOrUpdate(MemberInfo memberInfo, Long tenantId, Long orgId);

    DefTenantMember saveOrUpdateInfo(MemberInfo memberInfo, Long tenantId, Long orgId);

    Boolean updateBatchById(List<DefTenantMember> updateList);
}


