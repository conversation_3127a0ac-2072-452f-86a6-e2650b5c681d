package top.kx.kxss.system.service.tenant.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.basic.exception.BizException;
import top.kx.kxss.base.entity.member.MemberInfo;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.tenant.DefTenant;
import top.kx.kxss.system.entity.tenant.DefTenantMember;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.manager.tenant.DefTenantMemberManager;
import top.kx.kxss.system.service.tenant.DefTenantMemberService;
import top.kx.kxss.system.service.tenant.DefTenantService;
import top.kx.kxss.system.service.tenant.DefUserService;
import top.kx.kxss.system.vo.query.tenant.DefTenantMemberPageQuery;
import top.kx.kxss.system.vo.result.tenant.DefTenantMemberResultVO;
import top.kx.kxss.system.vo.save.tenant.DefTenantMemberSaveVO;
import top.kx.kxss.system.vo.save.tenant.DefUserSaveVO;
import top.kx.kxss.system.vo.update.tenant.DefTenantMemberUpdateVO;

import java.util.List;

/**
 * <p>
 * 业务实现类
 * 租户会员
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-29 15:09:15
 * @create [2024-02-29 15:09:15] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class DefTenantMemberServiceImpl extends SuperServiceImpl<DefTenantMemberManager, Long, DefTenantMember, DefTenantMemberSaveVO,
        DefTenantMemberUpdateVO, DefTenantMemberPageQuery, DefTenantMemberResultVO> implements DefTenantMemberService {


    @Autowired
    private DefUserService defUserService;
    @Autowired
    private DefTenantService defTenantService;

    @Override
    public Boolean update(LbUpdateWrap<DefTenantMember> defTenantMemberLbUpdateWrap) {
        return superManager.update(defTenantMemberLbUpdateWrap);
    }

    @Override
    public boolean saveOrUpdate(MemberInfo memberInfo, Long tenantId, Long orgId) {
        DefTenantMember defTenantMember = superManager.getOne(Wraps.<DefTenantMember>lbQ()
                .eq(DefTenantMember::getTenantId, tenantId)
                .eq(DefTenantMember::getDeleteFlag, 0)
                .eq(DefTenantMember::getOrgId, orgId)
                .eq(DefTenantMember::getMemberId, memberInfo.getId()).last("limit 1"));
        DefUser userByMobile = null;
        if (StrUtil.isNotBlank(memberInfo.getMobile())) {
            userByMobile = defUserService.getUserByMobile(memberInfo.getMobile());
        }
        if (ObjectUtil.isNull(userByMobile)) {
            DefUserSaveVO userSaveVO = DefUserSaveVO.builder().username("a" + memberInfo.getMobile())
                    .nickName(memberInfo.getName())
                    .mobile(memberInfo.getMobile())
                    .sex(memberInfo.getSex())
                    .state(true).idCard("")
                    .build();
            userByMobile = defUserService.save(userSaveVO);
        }
        memberInfo.setUserId(userByMobile != null ? userByMobile.getId() : null);
        if (ObjectUtil.isNull(defTenantMember)) {
            DefTenant defTenant = defTenantService.getById(tenantId);
            if (defTenant == null) {
                log.error("tenantId:{} is not exist", tenantId);
                return false;
            }
            return superManager.save(DefTenantMember.builder()
                    .memberId(memberInfo.getId())
                    .tenantId(tenantId).orgId(orgId)
                    .userId(memberInfo.getUserId() == null ? null : memberInfo.getUserId())
                    .name(memberInfo.getName()).createdOrgId(orgId)
                    .deleteFlag(0).mobile(memberInfo.getMobile())
                    .userId(userByMobile != null ? userByMobile.getId() : null)
                    .build());
        }
        defTenantMember.setName(memberInfo.getName());
        defTenantMember.setMobile(memberInfo.getMobile());
        defTenantMember.setMemberId(memberInfo.getId());
        memberInfo.setUserId(userByMobile != null ? userByMobile.getId() : null);
        defTenantMember.setUserId(userByMobile != null ? userByMobile.getId() : null);
        return superManager.updateById(defTenantMember);
    }

    @Override
    public DefTenantMember saveOrUpdateInfo(MemberInfo memberInfo, Long tenantId, Long orgId) {
        if (ObjectUtil.equal(tenantId, orgId)) {
            throw new BizException("登录信息异常，请重新登录！");
        }
        DefTenantMember defTenantMember = superManager.getOne(Wraps.<DefTenantMember>lbQ()
                .eq(DefTenantMember::getTenantId, tenantId)
                .eq(DefTenantMember::getOrgId, orgId)
                .eq(DefTenantMember::getDeleteFlag, 0)
                .eq(DefTenantMember::getMemberId, memberInfo.getId()).last("limit 1"));
        DefUser userByMobile = defUserService.getUserByMobile(memberInfo.getMobile());
        if (ObjectUtil.isNull(defTenantMember)) {
            DefTenant defTenant = defTenantService.getById(tenantId);
            if (defTenant == null) {
                log.error("tenantId:{} is not exist", tenantId);
                return defTenantMember;
            }
            defTenantMember = DefTenantMember.builder()
                    .memberId(memberInfo.getId())
                    .tenantId(tenantId).orgId(orgId)
                    .userId(memberInfo.getUserId() == null ? null : memberInfo.getUserId())
                    .name(memberInfo.getName()).createdOrgId(orgId)
                    .deleteFlag(0).mobile(memberInfo.getMobile())
                    .userId(userByMobile != null ? userByMobile.getId() : null)
                    .build();
            superManager.save(defTenantMember);
            return defTenantMember;
        }
        defTenantMember.setName(memberInfo.getName());
        defTenantMember.setMobile(memberInfo.getMobile());
        defTenantMember.setMemberId(memberInfo.getId());
        memberInfo.setUserId(userByMobile != null ? userByMobile.getId() : null);
        defTenantMember.setUserId(userByMobile != null ? userByMobile.getId() : null);
        superManager.updateById(defTenantMember);
        return defTenantMember;
    }


    @Override
    public Boolean updateBatchById(List<DefTenantMember> updateList) {
        return superManager.updateBatchById(updateList);
    }
}


