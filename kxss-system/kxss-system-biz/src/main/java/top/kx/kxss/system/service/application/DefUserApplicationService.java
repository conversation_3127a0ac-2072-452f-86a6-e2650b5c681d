package top.kx.kxss.system.service.application;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.application.DefUserApplication;
import top.kx.kxss.system.vo.query.application.DefUserApplicationPageQuery;
import top.kx.kxss.system.vo.result.application.DefUserApplicationResultVO;
import top.kx.kxss.system.vo.save.application.DefUserApplicationSaveVO;
import top.kx.kxss.system.vo.update.application.DefUserApplicationUpdateVO;

/**
 * <p>
 * 业务接口
 * 用户的默认应用
 * </p>
 *
 * <AUTHOR>
 * @date 2022-03-06
 */
public interface DefUserApplicationService extends SuperService<Long, DefUserApplication, DefUserApplicationSaveVO, DefUserApplicationUpdateVO, DefUserApplicationPageQuery, DefUserApplicationResultVO> {

    /**
     * 查询用户设置的默认应用
     *
     * @param userId 用户id
     * @return
     */
    Long getMyDefAppByUserId(Long userId);
}
