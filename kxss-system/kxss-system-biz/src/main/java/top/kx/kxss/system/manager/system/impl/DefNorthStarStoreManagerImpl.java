package top.kx.kxss.system.manager.system.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.system.DefNorthStarStore;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.system.DefNorthStarStoreManager;
import top.kx.kxss.system.mapper.system.DefNorthStarStoreMapper;

/**
 * <p>
 * 通用业务实现类
 * 北极星授权门店
 * </p>
 *
 * <AUTHOR>
 * @date 2023-11-02 10:00:21
 * @create [2023-11-02 10:00:21] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefNorthStarStoreManagerImpl extends SuperManagerImpl<DefNorthStarStoreMapper, DefNorthStarStore> implements DefNorthStarStoreManager {

}


