package top.kx.kxss.system.service.application;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.system.entity.application.DefTenantResourceRel;
import top.kx.kxss.system.vo.query.application.DefTenantResourceRelPageQuery;
import top.kx.kxss.system.vo.result.application.DefTenantResourceRelResultVO;
import top.kx.kxss.system.vo.save.application.DefTenantResourceRelSaveVO;
import top.kx.kxss.system.vo.update.application.DefTenantResourceRelUpdateVO;

import java.util.List;

/**
 * <p>
 * 业务接口
 * </p>
 *
 * <AUTHOR>
 * @date 2021-09-15
 */
public interface DefTenantResourceRelService extends SuperService<Long, DefTenantResourceRel, DefTenantResourceRelSaveVO, DefTenantResourceRelUpdateVO, DefTenantResourceRelPageQuery, DefTenantResourceRelResultVO> {
    boolean remove(LbQueryWrap<DefTenantResourceRel> eq);

    void deleteByTenantId(List<Long> ids);
}
