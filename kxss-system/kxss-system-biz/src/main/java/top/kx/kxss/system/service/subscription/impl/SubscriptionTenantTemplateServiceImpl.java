package top.kx.kxss.system.service.subscription.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.cash.MqttApi;
import top.kx.kxss.app.vo.mqtt.MQTTMessage;
import top.kx.kxss.app.vo.mqtt.MQTTParams;
import top.kx.kxss.base.entity.store.BaseStore;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.MqttConstant;
import top.kx.kxss.model.enumeration.app.MQTTTypeEnum;
import top.kx.kxss.model.enumeration.system.subscription.SubscriptionBillingTypeEnum;
import top.kx.kxss.model.enumeration.system.subscription.SubscriptionFeatureTypeEnum;
import top.kx.kxss.model.enumeration.system.subscription.SubscriptionStatusEnum;
import top.kx.kxss.store.api.BaseStoreApi;
import top.kx.kxss.system.entity.subscription.SubscriptionTemplate;
import top.kx.kxss.system.entity.subscription.SubscriptionTemplateFeature;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplate;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplateFeature;
import top.kx.kxss.system.manager.subscription.SubscriptionTenantTemplateManager;
import top.kx.kxss.system.service.subscription.SubscriptionTemplateFeatureService;
import top.kx.kxss.system.service.subscription.SubscriptionTemplateService;
import top.kx.kxss.system.service.subscription.SubscriptionTenantTemplateFeatureService;
import top.kx.kxss.system.service.subscription.SubscriptionTenantTemplateService;
import top.kx.kxss.system.vo.query.subscription.SubscriptionTenantTemplatePageQuery;
import top.kx.kxss.system.vo.result.subscription.SubscriptionTenantTemplateResultVO;
import top.kx.kxss.system.vo.save.subscription.SubscriptionTenantTemplateSaveVO;
import top.kx.kxss.system.vo.update.subscription.SubscriptionTenantTemplateUpdateVO;
import top.kx.kxss.table.TableApi;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 租户订阅模板
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-08 15:33:09
 * @create [2025-05-08 15:33:09] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class SubscriptionTenantTemplateServiceImpl extends SuperServiceImpl<SubscriptionTenantTemplateManager, Long, SubscriptionTenantTemplate, SubscriptionTenantTemplateSaveVO,
        SubscriptionTenantTemplateUpdateVO, SubscriptionTenantTemplatePageQuery, SubscriptionTenantTemplateResultVO> implements SubscriptionTenantTemplateService {


    private final MqttApi mqttApi;
    private final TableApi tableApi;
    private final BaseStoreApi baseStoreApi;

    private final SubscriptionTemplateService subscriptionTemplateService;
    private final SubscriptionTemplateFeatureService subscriptionTemplateFeatureService;
    private final SubscriptionTenantTemplateFeatureService subscriptionTenantTemplateFeatureService;

    @Override
    public SubscriptionTenantTemplate saveTenantTemplate(SubscriptionTenantTemplateSaveVO model) {
        SubscriptionTemplate subscriptionTemplate = subscriptionTemplateService.getById(model.getTmpId());
        ArgumentAssert.notNull(subscriptionTemplate, "模板不存在");
        model.setTmpName(subscriptionTemplate.getName());
        model.setTmpDescription(subscriptionTemplate.getDescription());
        model.setTmpType(subscriptionTemplate.getType());
        model.setTmpPrice(subscriptionTemplate.getPrice());
        model.setTmpServiceType(subscriptionTemplate.getServiceType());
        if (StrUtil.isNotBlank(model.getTmpBillingType()) &&
                model.getTmpBillingType().equals(SubscriptionBillingTypeEnum.DAYS.getCode())) {
            ArgumentAssert.notNull(model.getTmpDays(), "请输入计费天数");
        }
        if (StrUtil.isBlank(model.getTmpBillingType())) {
            model.setTmpBillingType(subscriptionTemplate.getBillingType());
        }
        if (model.getTmpDays() == null) {
            model.setTmpDays(subscriptionTemplate.getDays());
        }
        model.setTmpIsDefault(subscriptionTemplate.getIsDefault());
        if (model.getStatus().equals(SubscriptionStatusEnum.ACTIVE.getCode())) {
            ArgumentAssert.isFalse(checkTenantTemplate(model.getTenantId(), model.getOrgId(), model.getTmpId(), null), "门店已存在生效中许可证，请勿重复操作！");
            ArgumentAssert.notNull(model.getStartTime(), "请选择生效时间");
            if (model.getTmpBillingType().equals(SubscriptionBillingTypeEnum.DAYS.getCode())) {
                ArgumentAssert.notNull(model.getTmpDays(), "过期时间设置有误");
            }
            if (model.getExpirationTime() == null) {
                model.setExpirationTime(getExpirationTime(model.getStartTime(), model.getTmpBillingType(), model.getTmpDays()));
            }
        }
        if (model.getStatus().equals(SubscriptionStatusEnum.EXPIRED.getCode())
                || model.getStatus().equals(SubscriptionStatusEnum.CANCELED.getCode())) {
            ArgumentAssert.notNull(model.getExpirationTime(), "请选择过期/取消时间");
        }
        if (model.getExpirationTime() != null) {
            model.setExpirationTime(model.getExpirationTime().withMinute(59).withSecond(59).withHour(23).withNano(0));
        }
        //订单信息
        if (StrUtil.isNotBlank(model.getOrderCode())) {

        }
        if (ObjectUtil.isNotNull(model.getOrgId())
                && StrUtil.isBlank(model.getOrgName())) {
            ContextUtil.setTenantBasePoolName(model.getTenantId());
            ContextUtil.setTenantId(model.getTenantId());
            BaseStore baseStore = baseStoreApi.getById(model.getOrgId());
            if (baseStore != null) {
                model.setOrgName(baseStore.getName());
            }
            ContextUtil.setDefTenantId();
        }
        SubscriptionTenantTemplate tenantTemplate = super.save(model);
        ArgumentAssert.notNull(tenantTemplate, "保存失败");
        List<SubscriptionTemplateFeature> templateFeatureList = subscriptionTemplateFeatureService.list(Wraps.<SubscriptionTemplateFeature>lbQ()
                .eq(SubscriptionTemplateFeature::getTmpId, subscriptionTemplate.getId())
                .eq(SubscriptionTemplateFeature::getDeleteFlag, 0));
        List<SubscriptionTenantTemplateFeature> featureList = templateFeatureList.stream().map(v -> {
            SubscriptionTenantTemplateFeature feature = BeanUtil.copyProperties(v, SubscriptionTenantTemplateFeature.class);
            feature.setTmpId(tenantTemplate.getId());
            feature.setOrderId(tenantTemplate.getOrderId());
            feature.setTenantId(tenantTemplate.getTenantId());
            feature.setOrgId(tenantTemplate.getOrgId());
            feature.setOrgName(tenantTemplate.getOrgName());
            feature.setFeatureIsLimitCount(v.getIsLimitCount());
            feature.setFeatureLimitCount(v.getLimitCount());
            feature.setId(null);
            return feature;
        }).collect(Collectors.toList());
        subscriptionTenantTemplateFeatureService.saveBatch(featureList);
        sendMsg(tenantTemplate.getTenantId());
        return tenantTemplate;
    }

    private boolean checkTenantTemplate(Long tenantId, Long orgId, Long tmpId, Long id) {
        return superManager.count(Wraps.<SubscriptionTenantTemplate>lbQ()
                .eq(SubscriptionTenantTemplate::getTenantId, tenantId)
                .eq(SubscriptionTenantTemplate::getOrgId, orgId)
                .eq(SubscriptionTenantTemplate::getTmpId, tmpId)
                .eq(SubscriptionTenantTemplate::getTmpServiceType, SubscriptionFeatureTypeEnum.TEMPLATE.getCode())
                .eq(SubscriptionTenantTemplate::getStatus, SubscriptionStatusEnum.ACTIVE.getCode())
                .ne(SubscriptionTenantTemplate::getId, id)) > 0;
    }

    @Override
    public SubscriptionTenantTemplate updateTenantTemplate(SubscriptionTenantTemplateUpdateVO model) {
        SubscriptionTemplate subscriptionTemplate = subscriptionTemplateService.getById(model.getTmpId());
        ArgumentAssert.notNull(subscriptionTemplate, "模板不存在");
        model.setTmpName(subscriptionTemplate.getName());
        model.setTmpDescription(subscriptionTemplate.getDescription());
        model.setTmpType(subscriptionTemplate.getType());
        model.setTmpPrice(subscriptionTemplate.getPrice());
        model.setTmpDays(subscriptionTemplate.getDays());
        model.setTmpBillingType(subscriptionTemplate.getBillingType());
        model.setTmpIsDefault(subscriptionTemplate.getIsDefault());
        model.setTmpServiceType(subscriptionTemplate.getServiceType());
        if (ObjectUtil.isNotNull(model.getOrgId())
                && StrUtil.isBlank(model.getOrgName())) {
            ContextUtil.setTenantBasePoolName(model.getTenantId());
            ContextUtil.setTenantId(model.getTenantId());
            BaseStore baseStore = baseStoreApi.getById(model.getOrgId());
            if (baseStore != null) {
                model.setOrgName(baseStore.getName());
            }
            ContextUtil.setDefTenantId();
        }
        if (model.getStatus().equals(SubscriptionStatusEnum.ACTIVE.getCode())) {
            ArgumentAssert.isFalse(checkTenantTemplate(model.getTenantId(), model.getOrgId(), model.getTmpId(), model.getId()), "门店已存在生效中许可证，请勿重复操作！");
            ArgumentAssert.notNull(model.getStartTime(), "请选择生效时间");
            if (model.getTmpBillingType().equals(SubscriptionBillingTypeEnum.DAYS.getCode())) {
                ArgumentAssert.notNull(model.getTmpDays(), "过期时间设置有误");
            }
            if (model.getExpirationTime() == null) {
                model.setExpirationTime(getExpirationTime(model.getStartTime(), model.getTmpBillingType(), model.getTmpDays()));
            }
        }
        if (model.getStatus().equals(SubscriptionStatusEnum.EXPIRED.getCode())
                || model.getStatus().equals(SubscriptionStatusEnum.CANCELED.getCode())) {
            ArgumentAssert.notNull(model.getExpirationTime(), "请选择过期/取消时间");
        }
        if (model.getExpirationTime() != null) {
            model.setExpirationTime(model.getExpirationTime().withMinute(59).withSecond(59).withHour(23).withNano(0));
        }
        SubscriptionTenantTemplate tenantTemplate = super.updateById(model);
        ArgumentAssert.notNull(tenantTemplate, "保存失败");
        subscriptionTenantTemplateFeatureService.remove(Wraps.<SubscriptionTenantTemplateFeature>lbQ()
                .eq(SubscriptionTenantTemplateFeature::getTmpId, model.getId()));
        List<SubscriptionTemplateFeature> templateFeatureList = subscriptionTemplateFeatureService.list(Wraps.<SubscriptionTemplateFeature>lbQ()
                .eq(SubscriptionTemplateFeature::getTmpId, subscriptionTemplate.getId())
                .eq(SubscriptionTemplateFeature::getDeleteFlag, 0));
        List<SubscriptionTenantTemplateFeature> featureList = templateFeatureList.stream().map(v -> {
            SubscriptionTenantTemplateFeature feature = BeanUtil.copyProperties(v, SubscriptionTenantTemplateFeature.class);
            feature.setTmpId(tenantTemplate.getId());
            feature.setOrderId(tenantTemplate.getOrderId());
            feature.setTenantId(tenantTemplate.getTenantId());
            feature.setOrgId(tenantTemplate.getOrgId());
            feature.setOrgName(tenantTemplate.getOrgName());
            feature.setFeatureIsLimitCount(v.getIsLimitCount());
            feature.setFeatureLimitCount(v.getLimitCount());
            feature.setId(null);
            return feature;
        }).collect(Collectors.toList());
        subscriptionTenantTemplateFeatureService.saveBatch(featureList);
        sendMsg(tenantTemplate.getTenantId());
        return tenantTemplate;
    }

    @Override
    public LocalDateTime getExpirationTime(LocalDateTime startTime, String tmpBillingType, Integer tmpDays) {
        SubscriptionBillingTypeEnum subscriptionBillingTypeEnum = SubscriptionBillingTypeEnum.get(tmpBillingType);
        switch (subscriptionBillingTypeEnum) {
            case YEARLY:
                return startTime.plusYears(1).minusDays(1).withHour(23).withMinute(59).withSecond(59)
                        .withNano(0);
            case MONTHLY:
                return startTime.plusMonths(1).minusDays(1).withHour(23).withMinute(59).withSecond(59)
                        .withNano(0);
            case NO_LIMIT:
                return LocalDateTime.of(9999, 12, 31, 23, 59, 59, 0);
            case QUARTER:
                return startTime.plusMonths(3).minusDays(1).withHour(23).withMinute(59).withSecond(59)
                        .withNano(0);
            case DAYS:
                return startTime.plusDays(tmpDays).withNano(0);
        }
        return startTime;

    }

    @Override
    public SubscriptionTenantTemplate getOne(LbQueryWrap<SubscriptionTenantTemplate> last) {
        return superManager.getOne(last);
    }


    @Override
    public boolean updateById(SubscriptionTenantTemplate tenantTemplate) {
        return superManager.updateById(tenantTemplate);
    }

    @Override
    public boolean updateBatchById(List<SubscriptionTenantTemplate> updateTenantTemplateList) {
        return superManager.updateBatchById(updateTenantTemplateList);
    }

    @Override
    public long count(LbQueryWrap<SubscriptionTenantTemplate> eq) {
        return superManager.count(eq);
    }


    private void sendMsg(Long tenantId) {
        ContextUtil.setTenantBasePoolName(tenantId);
        ContextUtil.setTenantId(tenantId);
        List<Long> longs = tableApi.listIds();
        Map<String, Object> map = new HashMap<>();
        map.put("refreshConfig", true);
        for (Long tableId : longs) {
            //首页刷新
            mqttApi.send(MQTTMessage.builder()
                    .content(JSON.toJSONString(MQTTParams.builder()
                            .data(map)
                            .type(MQTTTypeEnum.TERMINAL_REFRESH_ORDER.getCode())
                            .build())).qos(2)
                    .topic(MqttConstant.TERMINAL_TOPIC.concat(tenantId + "/" + tableId))
                    .build());
        }
    }
}


