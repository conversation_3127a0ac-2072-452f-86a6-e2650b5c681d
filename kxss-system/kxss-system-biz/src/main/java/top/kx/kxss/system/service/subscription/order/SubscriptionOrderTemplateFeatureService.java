package top.kx.kxss.system.service.subscription.order;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.system.entity.subscription.order.SubscriptionOrderTemplateFeature;
import top.kx.kxss.system.vo.save.subscription.order.SubscriptionOrderTemplateFeatureSaveVO;
import top.kx.kxss.system.vo.update.subscription.order.SubscriptionOrderTemplateFeatureUpdateVO;
import top.kx.kxss.system.vo.result.subscription.order.SubscriptionOrderTemplateFeatureResultVO;
import top.kx.kxss.system.vo.query.subscription.order.SubscriptionOrderTemplateFeaturePageQuery;


/**
 * <p>
 * 业务接口
 * 订单订阅模版与功能权益
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-09 18:56:23
 * @create [2025-06-09 18:56:23] [dou] [代码生成器生成]
 */
public interface SubscriptionOrderTemplateFeatureService extends SuperService<Long, SubscriptionOrderTemplateFeature, SubscriptionOrderTemplateFeatureSaveVO,
    SubscriptionOrderTemplateFeatureUpdateVO, SubscriptionOrderTemplateFeaturePageQuery, SubscriptionOrderTemplateFeatureResultVO> {

    boolean remove(LbQueryWrap<SubscriptionOrderTemplateFeature> eq);
}


