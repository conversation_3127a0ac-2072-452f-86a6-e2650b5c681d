package top.kx.kxss.system.service.sms;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.sms.DefSmsRecharge;
import top.kx.kxss.system.vo.save.sms.DefSmsRechargeSaveVO;
import top.kx.kxss.system.vo.update.sms.DefSmsRechargeUpdateVO;
import top.kx.kxss.system.vo.result.sms.DefSmsRechargeResultVO;
import top.kx.kxss.system.vo.query.sms.DefSmsRechargePageQuery;


/**
 * <p>
 * 业务接口
 * 短信充值
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-28 17:22:09
 * @create [2024-04-28 17:22:09] [yan] [代码生成器生成]
 */
public interface DefSmsRechargeService extends SuperService<Long, DefSmsRecharge, DefSmsRechargeSaveVO,
    DefSmsRechargeUpdateVO, DefSmsRechargePageQuery, DefSmsRechargeResultVO> {


    DefSmsRechargeResultVO smsRecharge(Long thailId);

    DefSmsRecharge update(DefSmsRechargeUpdateVO updateVO);

    boolean saveRecharge(DefSmsRechargeSaveVO build);
}


