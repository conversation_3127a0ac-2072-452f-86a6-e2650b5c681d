package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.system.entity.system.DefVerticalScreenUpgrade;
import top.kx.kxss.system.vo.save.system.DefVerticalScreenUpgradeSaveVO;
import top.kx.kxss.system.vo.update.system.DefVerticalScreenUpgradeUpdateVO;
import top.kx.kxss.system.vo.result.system.DefVerticalScreenUpgradeResultVO;
import top.kx.kxss.system.vo.query.system.DefVerticalScreenUpgradePageQuery;

import java.util.Map;


/**
 * <p>
 * 业务接口
 * 智慧屏升级公告
 * </p>
 *
 * <AUTHOR>
 * @date 2024-11-21 10:38:04
 * @create [2024-11-21 10:38:04] [yan] [代码生成器生成]
 */
public interface DefVerticalScreenUpgradeService extends SuperService<Long, DefVerticalScreenUpgrade, DefVerticalScreenUpgradeSaveVO,
    DefVerticalScreenUpgradeUpdateVO, DefVerticalScreenUpgradePageQuery, DefVerticalScreenUpgradeResultVO> {

    Map<String, Object> checkVersion(String version);


    DefVerticalScreenUpgrade getOne();
}


