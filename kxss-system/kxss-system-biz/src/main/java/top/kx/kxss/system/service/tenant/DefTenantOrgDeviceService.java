package top.kx.kxss.system.service.tenant;

import com.baomidou.mybatisplus.core.metadata.IPage;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.system.entity.tenant.DefTenantOrgDevice;
import top.kx.kxss.system.vo.query.tenant.DefTenantOrgDevicePageQuery;
import top.kx.kxss.system.vo.result.tenant.DefTenantOrgDeviceResultVO;
import top.kx.kxss.system.vo.save.tenant.DefTenantOrgDeviceSaveVO;
import top.kx.kxss.system.vo.update.tenant.DefTenantOrgDeviceUpdateVO;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 商户门店设备信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-08-17 10:28:19
 * @create [2023-08-17 10:28:19] [dou] [代码生成器生成]
 */
public interface DefTenantOrgDeviceService extends SuperService<Long, DefTenantOrgDevice, DefTenantOrgDeviceSaveVO,
    DefTenantOrgDeviceUpdateVO, DefTenantOrgDevicePageQuery, DefTenantOrgDeviceResultVO> {

    DefTenantOrgDevice getOne(LbQueryWrap<DefTenantOrgDevice> eq);

    boolean save(DefTenantOrgDevice build);

    String getRandomTerminalNum(Long ignoreId);

    void update(DefTenantOrgDeviceUpdateVO build);

    IPage<DefTenantOrgDeviceResultVO> pageList(PageParams<DefTenantOrgDevicePageQuery> query);

    List<DefTenantOrgDevice> queryList(LbQueryWrap<DefTenantOrgDevice> queryWrap);

}


