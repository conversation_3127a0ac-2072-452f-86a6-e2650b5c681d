package top.kx.kxss.system.service.subscription.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.subscription.SubscriptionTemplateFeature;
import top.kx.kxss.system.manager.subscription.SubscriptionTemplateFeatureManager;
import top.kx.kxss.system.service.subscription.SubscriptionTemplateFeatureService;
import top.kx.kxss.system.vo.query.subscription.SubscriptionTemplateFeaturePageQuery;
import top.kx.kxss.system.vo.result.subscription.SubscriptionTemplateFeatureResultVO;
import top.kx.kxss.system.vo.save.subscription.SubscriptionTemplateFeatureSaveVO;
import top.kx.kxss.system.vo.update.subscription.SubscriptionTemplateFeatureUpdateVO;

/**
 * <p>
 * 业务实现类
 * 订阅模版与功能权益
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-07 16:01:29
 * @create [2025-05-07 16:01:29] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class SubscriptionTemplateFeatureServiceImpl extends SuperServiceImpl<SubscriptionTemplateFeatureManager, Long, SubscriptionTemplateFeature, SubscriptionTemplateFeatureSaveVO,
    SubscriptionTemplateFeatureUpdateVO, SubscriptionTemplateFeaturePageQuery, SubscriptionTemplateFeatureResultVO> implements SubscriptionTemplateFeatureService {

    @Override
    public boolean remove(LbUpdateWrap<SubscriptionTemplateFeature> eq) {
        return superManager.remove(eq);
    }
}


