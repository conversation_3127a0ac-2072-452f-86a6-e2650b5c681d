package top.kx.kxss.system.service.sms.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.employee.EmployeeApi;
import top.kx.kxss.model.enumeration.system.SmsRechargeStateEnum;
import top.kx.kxss.sms.SmsInfoApi;
import top.kx.kxss.system.entity.sms.DefSmsRecharge;
import top.kx.kxss.system.entity.sms.DefSmsThail;
import top.kx.kxss.system.entity.tenant.DefTenant;
import top.kx.kxss.system.entity.tenant.DefTenantOrg;
import top.kx.kxss.system.manager.sms.DefSmsRechargeManager;
import top.kx.kxss.system.service.sms.DefSmsRechargeService;
import top.kx.kxss.system.service.sms.DefSmsThailService;
import top.kx.kxss.system.service.tenant.DefTenantOrgService;
import top.kx.kxss.system.service.tenant.DefTenantService;
import top.kx.kxss.system.vo.query.sms.DefSmsRechargePageQuery;
import top.kx.kxss.system.vo.result.sms.DefSmsRechargeResultVO;
import top.kx.kxss.system.vo.save.sms.DefSmsRechargeSaveVO;
import top.kx.kxss.system.vo.update.sms.DefSmsInfoUpdateNumVO;
import top.kx.kxss.system.vo.update.sms.DefSmsRechargeUpdateVO;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <p>
 * 业务实现类
 * 短信充值
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-28 17:22:09
 * @create [2024-04-28 17:22:09] [yan] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class DefSmsRechargeServiceImpl extends SuperServiceImpl<DefSmsRechargeManager, Long, DefSmsRecharge, DefSmsRechargeSaveVO,
        DefSmsRechargeUpdateVO, DefSmsRechargePageQuery, DefSmsRechargeResultVO> implements DefSmsRechargeService {

    @Resource
    private DefSmsThailService defSmsThailService;
    @Resource
    private SmsInfoApi smsInfoApi;
    @Resource
    private DefTenantService defTenantService;
    @Resource
    private DefTenantOrgService defTenantOrgService;
    @Resource
    private EmployeeApi employeeApi;

    @Override
    public DefSmsRechargeResultVO smsRecharge(Long thailId) {

        // 查询 内部员工名称
        String employeeName = null;
        if (Objects.nonNull(ContextUtil.getEmployeeId())) {
            R<BaseEmployeeResultVO> baseEmployeeResultVOR = employeeApi.get(ContextUtil.getEmployeeId());
            if (baseEmployeeResultVOR.getIsSuccess() && Objects.nonNull(baseEmployeeResultVOR.getData())) {
                employeeName = baseEmployeeResultVOR.getData().getRealName();
            }

        }
        DefSmsThail smsThail = defSmsThailService.getById(thailId);
        DefTenant defTenant = defTenantService.getById(ContextUtil.getTenantId());
        DefTenantOrg defTenantOrg = defTenantOrgService.getOne(Wraps.<DefTenantOrg>lbQ().eq(DefTenantOrg::getDeleteFlag, 0).eq(DefTenantOrg::getTenantId, ContextUtil.getTenantId())
                .eq(DefTenantOrg::getOrgId, ContextUtil.getCurrentCompanyId()));
        ArgumentAssert.notNull(smsThail, "套餐不存在");
        DefSmsRechargeSaveVO saveVO = DefSmsRechargeSaveVO.builder()
                .tenantId(ContextUtil.getTenantId())
                .tenantName(Objects.nonNull(defTenant) ? defTenant.getName() : null)
                .orgId(ContextUtil.getCurrentCompanyId())
                .orgName(Objects.nonNull(defTenantOrg) ? defTenantOrg.getOrgName() : null)
                .employeeId(ContextUtil.getEmployeeId())
                .employeeName(employeeName)
                .thailId(thailId)
                .num(smsThail.getTotal())
                .price(smsThail.getPrice())
                .state(SmsRechargeStateEnum.CONFIRMED.getCode())
                .build();
        DefSmsRecharge save = save(saveVO);
        return BeanPlusUtil.toBean(save, DefSmsRechargeResultVO.class);
    }

    @Override
    public boolean saveRecharge(DefSmsRechargeSaveVO build) {
        long count = superManager.count(Wraps.<DefSmsRecharge>lbQ()
                .isNotNull(DefSmsRecharge::getTempOrderId)
                .eq(DefSmsRecharge::getDeleteFlag, 0)
                .eq(DefSmsRecharge::getTempOrderId, build.getTempOrderId())
        );
        ArgumentAssert.isFalse(count > 0, "该订单已充值");
        // 查询 内部员工名称
        String employeeName = null;
        if (Objects.nonNull(ContextUtil.getEmployeeId())) {
            R<BaseEmployeeResultVO> baseEmployeeResultVOR = employeeApi.get(ContextUtil.getEmployeeId());
            if (baseEmployeeResultVOR.getIsSuccess() && Objects.nonNull(baseEmployeeResultVOR.getData())) {
                employeeName = baseEmployeeResultVOR.getData().getRealName();
            }
        }
        DefSmsThail smsThail = defSmsThailService.getById(build.getThailId());
        DefTenant defTenant = defTenantService.getById(ContextUtil.getTenantId());
        DefTenantOrg defTenantOrg = defTenantOrgService.getOne(Wraps.<DefTenantOrg>lbQ().eq(DefTenantOrg::getDeleteFlag, 0).eq(DefTenantOrg::getTenantId, ContextUtil.getTenantId())
                .eq(DefTenantOrg::getOrgId, ContextUtil.getCurrentCompanyId()));
        ArgumentAssert.notNull(smsThail, "套餐不存在");
        DefSmsRechargeSaveVO saveVO = DefSmsRechargeSaveVO.builder()
                .tenantId(build.getTenantId())
                .tenantName(Objects.nonNull(defTenant) ? defTenant.getName() : null)
                .orgId(build.getOrgId())
                .orgName(Objects.nonNull(defTenantOrg) ? defTenantOrg.getOrgName() : null)
                .employeeId(build.getEmployeeId())
                .employeeName(employeeName)
                .thailId(build.getThailId())
                .num(smsThail.getTotal()).realNum(smsThail.getTotal())
                .price(smsThail.getPrice())
                .state(SmsRechargeStateEnum.RECHARGE.getCode())
                .build();
        DefSmsRecharge recharge = super.save(saveVO);
        DefSmsInfoUpdateNumVO updateNumVO = DefSmsInfoUpdateNumVO.builder()
                .tenantId(recharge.getTenantId())
                .tenantName(recharge.getTenantName())
                .orgId(recharge.getOrgId())
                .orgName(recharge.getOrgName())
                .isUse(false).isAdd(true)
                .num(recharge.getRealNum())
                .build();
        R<Boolean> booleanR = smsInfoApi.updateSmsNum(updateNumVO);
        ArgumentAssert.isFalse(!booleanR.getIsSuccess(), "短信条数修改失败, 请联系管理员");
        return true;
    }

    @GlobalTransactional
    @Override
    public DefSmsRecharge update(DefSmsRechargeUpdateVO updateVO) {

        DefSmsRecharge smsRecharge = getById(updateVO.getId());
        ArgumentAssert.notNull(smsRecharge, "充值记录不存在");

        DefSmsRecharge recharge = updateById(updateVO);

        DefSmsInfoUpdateNumVO updateNumVO = DefSmsInfoUpdateNumVO.builder()
                .tenantId(recharge.getTenantId())
                .tenantName(recharge.getTenantName())
                .orgId(recharge.getOrgId())
                .orgName(recharge.getOrgName())
                .isUse(false)
                .num(updateVO.getRealNum())
                .build();

        // 判断是否不同
        if (!StringUtils.equals(smsRecharge.getState(), updateVO.getState())) {
            // 判断是不是由待生效改为 成功
            if (SmsRechargeStateEnum.CONFIRMED.getCode().equals(smsRecharge.getState()) && SmsRechargeStateEnum.RECHARGE.getCode().equals(updateVO.getState())) {
                // 总条数增加
                updateNumVO.setIsAdd(true);
            }
            // 充值成功改为充值失败
            if (SmsRechargeStateEnum.RECHARGE.getCode().equals(smsRecharge.getState()) && SmsRechargeStateEnum.NO_RECHARGE.getCode().equals(updateVO.getState())) {
                // 总条数减少
                updateNumVO.setIsAdd(false);
            }
            // 充值充公,改为待确认
            if (SmsRechargeStateEnum.RECHARGE.getCode().equals(smsRecharge.getState()) && SmsRechargeStateEnum.CONFIRMED.getCode().equals(updateVO.getState())) {
                // 总条数减少
                updateNumVO.setIsAdd(false);
            }
            R<Boolean> booleanR = smsInfoApi.updateSmsNum(updateNumVO);
            ArgumentAssert.isFalse(!booleanR.getIsSuccess(), "短信条数修改失败, 请联系管理员");
            return recharge;
        }

        // 判断是不是生效中
        if (SmsRechargeStateEnum.RECHARGE.getCode().equals(updateVO.getState())) {
            // 状态相同,只有 - 如果是生效中的, 比较原本的数量, 如果不相同,就增加或者减少
            if (!Objects.equals(updateVO.getRealNum(), smsRecharge.getRealNum())) {
                if (updateVO.getRealNum() > smsRecharge.getRealNum()) {
                    updateNumVO.setIsAdd(true);
                    updateNumVO.setNum(updateVO.getRealNum() - smsRecharge.getRealNum());
                } else {
                    updateNumVO.setIsAdd(false);
                    updateNumVO.setNum(smsRecharge.getRealNum() - updateVO.getRealNum());
                }
            }
            R<Boolean> booleanR = smsInfoApi.updateSmsNum(updateNumVO);
            ArgumentAssert.isFalse(!booleanR.getIsSuccess(), "短信条数修改失败, 请联系管理员");
            return recharge;
        }

        return recharge;
    }
}


