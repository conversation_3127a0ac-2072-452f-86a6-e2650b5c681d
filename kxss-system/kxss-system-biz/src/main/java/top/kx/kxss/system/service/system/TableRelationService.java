package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.system.TableRelation;
import top.kx.kxss.system.vo.save.system.TableRelationSaveVO;
import top.kx.kxss.system.vo.update.system.TableRelationUpdateVO;
import top.kx.kxss.system.vo.result.system.TableRelationResultVO;
import top.kx.kxss.system.vo.query.system.TableRelationPageQuery;


/**
 * <p>
 * 业务接口
 * 表间关联关系表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-05-11 11:11:51
 * @create [2023-05-11 11:11:51] [dou] [代码生成器生成]
 */
public interface TableRelationService extends SuperService<Long, TableRelation, TableRelationSaveVO,
    TableRelationUpdateVO, TableRelationPageQuery, TableRelationResultVO> {

}


