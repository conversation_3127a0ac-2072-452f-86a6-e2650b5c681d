package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.system.DefTask;
import top.kx.kxss.system.vo.save.system.DefTaskSaveVO;
import top.kx.kxss.system.vo.update.system.DefTaskUpdateVO;
import top.kx.kxss.system.vo.result.system.DefTaskResultVO;
import top.kx.kxss.system.vo.query.system.DefTaskPageQuery;


/**
 * <p>
 * 业务接口
 * 任务表
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-12 17:42:51
 * @create [2024-12-12 17:42:51] [yan] [代码生成器生成]
 */
public interface DefTaskService extends SuperService<Long, DefTask, DefTaskSaveVO,
    DefTaskUpdateVO, DefTaskPageQuery, DefTaskResultVO> {

    Boolean updateById(DefTask defTask);

    Boolean save(DefTask build);
}


