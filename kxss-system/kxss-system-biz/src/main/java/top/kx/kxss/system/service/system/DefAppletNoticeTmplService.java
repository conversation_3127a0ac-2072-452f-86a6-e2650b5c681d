package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.system.entity.system.DefAppletNoticeTmpl;
import top.kx.kxss.system.vo.save.system.DefAppletNoticeTmplSaveVO;
import top.kx.kxss.system.vo.update.system.DefAppletNoticeTmplUpdateVO;
import top.kx.kxss.system.vo.result.system.DefAppletNoticeTmplResultVO;
import top.kx.kxss.system.vo.query.system.DefAppletNoticeTmplPageQuery;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 小程序服务通知模版
 * </p>
 *
 * <AUTHOR>
 * @date 2023-10-25 15:39:53
 * @create [2023-10-25 15:39:53] [dou] [代码生成器生成]
 */
public interface DefAppletNoticeTmplService extends SuperService<Long, DefAppletNoticeTmpl, DefAppletNoticeTmplSaveVO,
    DefAppletNoticeTmplUpdateVO, DefAppletNoticeTmplPageQuery, DefAppletNoticeTmplResultVO> {

    DefAppletNoticeTmpl getOne(LbQueryWrap<DefAppletNoticeTmpl> queryWrap);

    List<String> tmpList();

}


