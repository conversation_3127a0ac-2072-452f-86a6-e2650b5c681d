package top.kx.kxss.system.manager.tenant.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.entity.tenant.DefTenantOrg;
import top.kx.kxss.system.manager.tenant.DefTenantOrgManager;
import top.kx.kxss.system.mapper.tenant.DefTenantOrgMapper;

/**
 * <p>
 * 通用业务实现类
 * 商户门店信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-07-12 14:20:57
 * @create [2023-07-12 14:20:57] [dou] [代码生成器生成]
 */
@RequiredArgsConstructor
@Service
public class DefTenantOrgManagerImpl extends SuperManagerImpl<DefTenantOrgMapper, DefTenantOrg> implements DefTenantOrgManager {

}


