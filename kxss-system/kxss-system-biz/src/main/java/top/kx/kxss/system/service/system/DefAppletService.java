package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.system.entity.system.DefApplet;
import top.kx.kxss.system.vo.query.system.DefAppletPageQuery;
import top.kx.kxss.system.vo.result.system.DefAppletResultVO;
import top.kx.kxss.system.vo.save.system.DefAppletSaveVO;
import top.kx.kxss.system.vo.update.system.DefAppletUpdateVO;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 小程序配置
 * </p>
 *
 * <AUTHOR>
 * @date 2023-07-06 15:46:15
 * @create [2023-07-06 15:46:15] [dou] [代码生成器生成]
 */
public interface DefAppletService extends SuperService<Long, DefApplet, DefAppletSaveVO,
    DefAppletUpdateVO, DefAppletPageQuery, DefAppletResultVO> {

    /**
     * @param id   应用id
     * @param name 所属商户
     */
    Boolean check(Long id, String name);

    /**
     *
     * @param id   应用id
     * @param tenantId 所属商户
     */
    Boolean checkTenant(Long id, List<String> tenantId);

    /**
     * 更新状态
     * @param id
     * @param state
     * @return
     */
    Boolean updateState(Long id, Boolean state);

    DefApplet getOne(LbQueryWrap<DefApplet> eq);

    boolean checkTenant(Long clientId, List<String> tenantId, Long id);
}


