package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.system.entity.system.DefVerticalScreenUpgradeTenant;
import top.kx.kxss.system.vo.save.system.DefVerticalScreenUpgradeTenantSaveVO;
import top.kx.kxss.system.vo.update.system.DefVerticalScreenUpgradeTenantUpdateVO;
import top.kx.kxss.system.vo.result.system.DefVerticalScreenUpgradeTenantResultVO;
import top.kx.kxss.system.vo.query.system.DefVerticalScreenUpgradeTenantPageQuery;


/**
 * <p>
 * 业务接口
 * 智慧屏可更新商户
 * </p>
 *
 * <AUTHOR>
 * @date 2025-08-20 16:03:24
 * @create [2025-08-20 16:03:24] [yan] [代码生成器生成]
 */
public interface DefVerticalScreenUpgradeTenantService extends SuperService<Long, DefVerticalScreenUpgradeTenant, DefVerticalScreenUpgradeTenantSaveVO,
    DefVerticalScreenUpgradeTenantUpdateVO, DefVerticalScreenUpgradeTenantPageQuery, DefVerticalScreenUpgradeTenantResultVO> {

    void remove(LbQueryWrap<DefVerticalScreenUpgradeTenant> wrap);
}


