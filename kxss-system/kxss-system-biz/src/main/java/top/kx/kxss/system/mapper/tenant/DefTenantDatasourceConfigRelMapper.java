package top.kx.kxss.system.mapper.tenant;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.springframework.stereotype.Repository;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.system.entity.tenant.DefTenantDatasourceConfigRel;

/**
 * <p>
 * Mapper 接口
 * 租户的数据源
 * </p>
 *
 * <AUTHOR>
 * @date 2021-09-13
 */
@Repository
@InterceptorIgnore(tenantLine = "true", dynamicTableName = "true")
public interface DefTenantDatasourceConfigRelMapper extends SuperMapper<DefTenantDatasourceConfigRel> {

}
