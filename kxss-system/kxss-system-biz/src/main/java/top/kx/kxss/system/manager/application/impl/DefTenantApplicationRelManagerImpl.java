package top.kx.kxss.system.manager.application.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.basic.cache.redis2.CacheResult;
import top.kx.basic.cache.repository.CacheOps;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.model.cache.CacheKey;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.common.cache.tenant.tenant.TenantApplicationCacheKeyBuilder;
import top.kx.kxss.system.entity.application.DefApplication;
import top.kx.kxss.system.entity.application.DefResource;
import top.kx.kxss.system.entity.application.DefTenantApplicationRel;
import top.kx.kxss.system.entity.application.DefTenantResourceRel;
import top.kx.kxss.system.entity.tenant.DefUserTenantRel;
import top.kx.kxss.system.manager.application.DefApplicationManager;
import top.kx.kxss.system.manager.application.DefResourceManager;
import top.kx.kxss.system.manager.application.DefTenantApplicationRelManager;
import top.kx.kxss.system.manager.application.DefTenantResourceRelManager;
import top.kx.kxss.system.manager.tenant.DefUserTenantRelManager;
import top.kx.kxss.system.mapper.application.DefTenantApplicationRelMapper;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 应用管理
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2021/9/29 1:26 下午
 * @create [2021/9/29 1:26 下午 ] [tangyh] [初始创建]
 */
@RequiredArgsConstructor
@Service
public class DefTenantApplicationRelManagerImpl extends SuperManagerImpl<DefTenantApplicationRelMapper, DefTenantApplicationRel> implements DefTenantApplicationRelManager {
    private final DefApplicationManager defApplicationManager;
    private final DefResourceManager defResourceManager;
    private final DefTenantResourceRelManager defTenantResourceRelManager;
    private final DefUserTenantRelManager defUserTenantRelManager;
    private final CacheOps cacheOps;

    @Override
    public void grantGeneralApplication(Long tenantId) {
        List<DefApplication> list = defApplicationManager.findGeneral();
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> applicationIds = new ArrayList<>();
        remove(Wraps.<DefTenantApplicationRel>lbQ()
                .eq(DefTenantApplicationRel::getTenantId, tenantId));
        List<DefTenantApplicationRel> tarList = list.stream().map(application -> {
            DefTenantApplicationRel tar = new DefTenantApplicationRel();
            tar.setTenantId(tenantId);
            tar.setApplicationId(application.getId());
            applicationIds.add(application.getId());
            return tar;
        }).collect(Collectors.toList());
        saveBatch(tarList);

        //List<DefResource> resourceList = defResourceManager.findByApplicationId(applicationIds);
        List<DefTenantResourceRel> resourceList = defTenantResourceRelManager.list(Wraps.<DefTenantResourceRel>lbQ().eq(SuperEntity::getDeleteFlag, 0)
                .eq(DefTenantResourceRel::getTenantId, 1L)
                .in(DefTenantResourceRel::getApplicationId, applicationIds));
        if (CollUtil.isEmpty(resourceList)) {
            return;
        }
        defTenantResourceRelManager.remove(Wraps.<DefTenantResourceRel>lbQ()
                .eq(DefTenantResourceRel::getTenantId, tenantId));
        resourceList.forEach(trr-> {
            trr.setId(null);
            trr.setTenantId(tenantId);
            trr.setCreatedBy(null);
            trr.setCreatedTime(null);
            trr.setUpdatedBy(null);
            trr.setUpdatedTime(null);
        });
//        List<DefTenantResourceRel> trrList = resourceList.stream().map(resource -> {
//            DefTenantResourceRel trr = new DefTenantResourceRel();
//            trr.setTenantId(tenantId);
//            trr.setApplicationId(resource.getApplicationId());
//            trr.setResourceId(resource.getId());
//            return trr;
//        }).collect(Collectors.toList());
        defTenantResourceRelManager.saveBatch(resourceList);
    }


    @Override
    public List<Long> findApplicationByEmployeeId(Long employeeId) {
        DefUserTenantRel employee = defUserTenantRelManager.getById(employeeId);
        ArgumentAssert.notNull(employee, "用户不存在");
        Long tenantId = employee.getTenantId();
        CacheKey key = TenantApplicationCacheKeyBuilder.builder(tenantId);
        CacheResult<List<Long>> applicationIds = cacheOps.get(key, k -> listObjs(
                Wraps.<DefTenantApplicationRel>lbQ().select(DefTenantApplicationRel::getApplicationId)
                        .eq(DefTenantApplicationRel::getTenantId, tenantId)
                        .and(w ->
                                w.gt(DefTenantApplicationRel::getExpirationTime, LocalDateTime.now()).or().isNull(DefTenantApplicationRel::getExpirationTime)
                        )
                , Convert::toLong));
        return applicationIds.asList();
    }

    @Override
    public void deleteByTenantId(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        remove(Wraps.<DefTenantApplicationRel>lbQ().in(DefTenantApplicationRel::getTenantId, ids));

        cacheOps.del(ids.stream().map(TenantApplicationCacheKeyBuilder::builder).collect(Collectors.toList()));
    }
}
