package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.system.entity.system.DefQrCode;
import top.kx.kxss.system.vo.query.system.DefQrCodePageQuery;
import top.kx.kxss.system.vo.result.system.DefQrCodeResultVO;
import top.kx.kxss.system.vo.save.system.DefQrCodeSaveVO;
import top.kx.kxss.system.vo.update.system.DefQrCodeUpdateVO;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 二维码生成
 * </p>
 *
 * <AUTHOR>
 * @date 2023-08-22 17:49:38
 * @create [2023-08-22 17:49:38] [lixuecheng] [代码生成器生成]
 */
public interface DefQrCodeService extends SuperService<Long, DefQrCode, DefQrCodeSaveVO,
    DefQrCodeUpdateVO, DefQrCodePageQuery, DefQrCodeResultVO> {
    DefQrCode getOne(LbQueryWrap<DefQrCode> eq);

    void saveQrCode(DefQrCode saveQrCode);

    Boolean updateById(DefQrCode defQrCode);

    void saveOrUpdateBatch(List<DefQrCode> saveBatch);
}


