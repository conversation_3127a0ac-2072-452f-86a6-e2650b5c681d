package top.kx.kxss.system.service.system;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.system.entity.system.DefSnQrCode;
import top.kx.kxss.system.vo.query.system.DefSnQrCodePageQuery;
import top.kx.kxss.system.vo.result.system.DefSnQrCodeResultVO;
import top.kx.kxss.system.vo.save.system.DefSnQrCodeSaveVO;
import top.kx.kxss.system.vo.update.system.DefSnQrCodeUpdateVO;


/**
 * <p>
 * 业务接口
 * 二维码生成
 * </p>
 *
 * <AUTHOR>
 * @date 2024-06-04 11:22:59
 * @create [2024-06-04 11:22:59] [dou] [代码生成器生成]
 */
public interface DefSnQrCodeService extends SuperService<Long, DefSnQrCode, DefSnQrCodeSaveVO,
    DefSnQrCodeUpdateVO, DefSnQrCodePageQuery, DefSnQrCodeResultVO> {

    boolean save(DefSnQrCode saveQrCode);

    DefSnQrCode getOne(LbQueryWrap<DefSnQrCode> wq);

    boolean updateById(DefSnQrCode defSnQrCode);

}


