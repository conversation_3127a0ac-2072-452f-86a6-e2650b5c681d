package top.kx.kxss.system.service.deal;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.system.entity.deal.DealOrderDetail;
import top.kx.kxss.system.vo.save.deal.DealOrderDetailSaveVO;
import top.kx.kxss.system.vo.update.deal.DealOrderDetailUpdateVO;
import top.kx.kxss.system.vo.result.deal.DealOrderDetailResultVO;
import top.kx.kxss.system.vo.query.deal.DealOrderDetailPageQuery;


/**
 * <p>
 * 业务接口
 * 订单明细
 * </p>
 *
 * <AUTHOR>
 * @date 2024-10-24 14:08:24
 * @create [2024-10-24 14:08:24] [dou] [代码生成器生成]
 */
public interface DealOrderDetailService extends SuperService<Long, DealOrderDetail, DealOrderDetailSaveVO,
    DealOrderDetailUpdateVO, DealOrderDetailPageQuery, DealOrderDetailResultVO> {

    boolean save(DealOrderDetail detail);
}


