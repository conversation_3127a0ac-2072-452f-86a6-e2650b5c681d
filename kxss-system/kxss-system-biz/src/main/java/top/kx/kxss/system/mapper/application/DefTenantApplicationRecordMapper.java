package top.kx.kxss.system.mapper.application;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.springframework.stereotype.Repository;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.system.entity.application.DefTenantApplicationRecord;

/**
 * <p>
 * Mapper 接口
 * 租户应用授权记录
 * </p>
 *
 * <AUTHOR>
 * @date 2021-09-15
 */
@Repository
@InterceptorIgnore(tenantLine = "true", dynamicTableName = "true")
public interface DefTenantApplicationRecordMapper extends SuperMapper<DefTenantApplicationRecord> {

}
