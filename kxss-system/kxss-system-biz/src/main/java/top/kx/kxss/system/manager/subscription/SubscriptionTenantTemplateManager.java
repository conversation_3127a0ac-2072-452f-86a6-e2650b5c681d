package top.kx.kxss.system.manager.subscription;

import top.kx.basic.base.manager.SuperManager;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplate;

/**
 * <p>
 * 通用业务接口
 * 租户订阅模板
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-08 15:33:09
 * @create [2025-05-08 15:33:09] [dou] [代码生成器生成]
 */
public interface SubscriptionTenantTemplateManager extends SuperManager<SubscriptionTenantTemplate> {

}


