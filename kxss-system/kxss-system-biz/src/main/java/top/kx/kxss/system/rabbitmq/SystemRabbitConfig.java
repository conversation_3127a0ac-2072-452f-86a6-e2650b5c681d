package top.kx.kxss.system.rabbitmq;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import top.kx.kxss.common.constant.RabbitMqConstant;

/**
 * <AUTHOR>
 */
@Configuration
public class SystemRabbitConfig {

    @Bean
    public Queue tenantSubscriptionQueue() {
        return new Queue(RabbitMqConstant.TENANT_SUBSCRIPTION);
    }


    @Bean
    TopicExchange exchange() {
        return new TopicExchange(RabbitMqConstant.SYSTEM_EXCHANGE);
    }

    @Bean
    Binding tenantSubscriptionMessage() {
        return BindingBuilder.bind(tenantSubscriptionQueue()).to(exchange()).with(RabbitMqConstant.TENANT_SUBSCRIPTION);
    }
}
