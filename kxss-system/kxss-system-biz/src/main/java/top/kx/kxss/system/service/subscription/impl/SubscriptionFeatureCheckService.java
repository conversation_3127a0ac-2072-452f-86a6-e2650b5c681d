package top.kx.kxss.system.service.subscription.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.kxss.common.utils.ExpressionEvaluator;
import top.kx.kxss.model.enumeration.system.subscription.FeatureCodeEnum;
import top.kx.kxss.model.enumeration.system.subscription.SubscriptionFeatureTypeEnum;
import top.kx.kxss.model.enumeration.system.subscription.SubscriptionStatusEnum;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplate;
import top.kx.kxss.system.entity.subscription.SubscriptionTenantTemplateFeature;
import top.kx.kxss.system.service.subscription.SubscriptionTenantTemplateFeatureService;
import top.kx.kxss.system.service.subscription.SubscriptionTenantTemplateService;
import top.kx.kxss.system.strategy.subscription.SubscriptionCheckStrategy;
import top.kx.kxss.system.strategy.subscription.SubscriptionCheckStrategyFactory;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/5/21 15:47
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SubscriptionFeatureCheckService {

    private final SubscriptionTenantTemplateService tenantTemplateManager;
    private final SubscriptionTenantTemplateFeatureService tenantTemplateFeatureManager;
    private final SubscriptionCheckStrategyFactory subscriptionCheckStrategyFactory;

    /**
     * 检查租户是否有功能权限
     *
     * @param tenantId    租户ID
     * @param featureCode 功能代码
     * @return 是否有权限
     */
    public boolean hasFeaturePermission(Long tenantId, FeatureCodeEnum[] featureCode, JSONObject params,
                                        SubscriptionFeatureTypeEnum featureTypeEnum) {
        // 检查租户是否有有效的订阅
        SubscriptionTenantTemplate template = tenantTemplateManager.getOne(
                Wraps.<SubscriptionTenantTemplate>lbQ()
                        .eq(SubscriptionTenantTemplate::getTenantId, tenantId)
                        .eq(SubscriptionTenantTemplate::getOrgId, ContextUtil.getCurrentCompanyId())
                        .eq(SubscriptionTenantTemplate::getStatus, SubscriptionStatusEnum.ACTIVE.getCode())
                        .eq(SubscriptionTenantTemplate::getTmpServiceType, featureTypeEnum.getCode())
                        .eq(SubscriptionTenantTemplate::getDeleteFlag, 0)
                        .orderByDesc(SubscriptionTenantTemplate::getCreatedTime)
                        .last("LIMIT 1")
        );
        if (template == null) {
            log.info("租户,{}没有有效的订阅", tenantId);
            return false;
        }

        // 查询功能限制
        List<String> featureCodeList = Arrays.stream(featureCode).map(FeatureCodeEnum::getCode).collect(Collectors.toList());
        // 检查功能权限
        List<SubscriptionTenantTemplateFeature> featureList = tenantTemplateFeatureManager.list(
                Wraps.<SubscriptionTenantTemplateFeature>lbQ()
                        .eq(SubscriptionTenantTemplateFeature::getTenantId, tenantId)
                        .eq(SubscriptionTenantTemplateFeature::getOrgId, ContextUtil.getCurrentCompanyId())
                        .eq(ObjectUtil.isNotNull(template.getId()), SubscriptionTenantTemplateFeature::getTmpId, template.getId())
                        .in(SubscriptionTenantTemplateFeature::getFeatureCode, featureCodeList)
                        .eq(SubscriptionTenantTemplateFeature::getDeleteFlag, 0)
        );
        if (CollUtil.isEmpty(featureList)) {
            featureList = Lists.newArrayList();
        }
        List<Boolean> booleanList = new ArrayList<>();
        for (String code : featureCodeList) {
            SubscriptionCheckStrategy strategyType = subscriptionCheckStrategyFactory.strategyType(code);
            booleanList.add(strategyType.checkPermission(featureList, params, code, template));
        }
        return booleanList.stream().anyMatch(booleanItem -> booleanItem);
    }

    /**
     * 检查并获取租户功能限制数量
     *
     * @param tenantId    租户ID
     * @param featureCode 功能代码
     * @return 限制数量，-1表示无限制，0表示无权限
     */
    public int getFeatureLimitCount(Long tenantId, FeatureCodeEnum[] featureCode) {
        // 检查租户是否有有效的订阅
        SubscriptionTenantTemplate template = tenantTemplateManager.getOne(
                Wraps.<SubscriptionTenantTemplate>lbQ()
                        .eq(SubscriptionTenantTemplate::getTenantId, tenantId)
                        .eq(SubscriptionTenantTemplate::getStatus, SubscriptionStatusEnum.ACTIVE.getCode())
                        .eq(SubscriptionTenantTemplate::getDeleteFlag, 0)
                        .orderByDesc(SubscriptionTenantTemplate::getCreatedTime)
                        .last("LIMIT 1")
        );

        if (template == null) {
            log.info("租户,{}没有有效的订阅", tenantId);
            return 0;
        }
        List<String> featureCodeList = Arrays.stream(featureCode).map(FeatureCodeEnum::getCode).collect(Collectors.toList());

        // 查询功能限制
        SubscriptionTenantTemplateFeature feature = tenantTemplateFeatureManager.getOne(
                Wraps.<SubscriptionTenantTemplateFeature>lbQ()
                        .eq(SubscriptionTenantTemplateFeature::getTenantId, tenantId)
                        .eq(SubscriptionTenantTemplateFeature::getTmpId, template.getId())
                        .in(SubscriptionTenantTemplateFeature::getFeatureCode, featureCodeList)
                        .eq(SubscriptionTenantTemplateFeature::getDeleteFlag, 0)
        );

        if (feature == null) {
            log.info("租户{}没有功能{}的权限", tenantId, featureCode);
            return 0;
        }
        if (feature.getFeatureIsLimitCount() != null && feature.getFeatureIsLimitCount()) {
            return feature.getFeatureLimitCount() != null ? feature.getFeatureLimitCount() : 0;
        } else {
            // 无限制
            return -1;
        }
    }

    /**
     * 获取租户所有功能权限
     *
     * @param tenantId 租户ID
     * @return 功能权限列表
     */
    public List<SubscriptionTenantTemplateFeature> getTenantFeatures(Long tenantId) {
        // 检查租户是否有有效的订阅
        SubscriptionTenantTemplate template = tenantTemplateManager.getOne(
                Wraps.<SubscriptionTenantTemplate>lbQ()
                        .eq(SubscriptionTenantTemplate::getTenantId, tenantId)
                        .eq(SubscriptionTenantTemplate::getStatus, SubscriptionStatusEnum.ACTIVE.getCode())
                        .eq(SubscriptionTenantTemplate::getDeleteFlag, 0)
                        .orderByDesc(SubscriptionTenantTemplate::getCreatedTime)
                        .last("LIMIT 1")
        );

        if (template == null) {
            log.info("租户{}没有有效的订阅", tenantId);
            return Lists.newArrayList();
        }

        // 查询所有功能
        return tenantTemplateFeatureManager.list(
                Wraps.<SubscriptionTenantTemplateFeature>lbQ()
                        .eq(SubscriptionTenantTemplateFeature::getTenantId, tenantId)
                        .eq(SubscriptionTenantTemplateFeature::getTmpId, template.getId())
                        .eq(SubscriptionTenantTemplateFeature::getDeleteFlag, 0)
        );
    }

    public static void main(String[] args) {
        JSONObject params = new JSONObject();
        params.put("data", "123");
        String aa = "StrUtil.isEmpty(#{data})";
        System.out.println(ExpressionEvaluator.evaluate(aa, params));
    }

    public void checkTemplate(List<SubscriptionTenantTemplate> tenantTemplateList) {
        if (CollUtil.isEmpty(tenantTemplateList)) {
            return;
        }
        List<SubscriptionTenantTemplate> templateList = tenantTemplateList.stream().filter(v -> v.getStatus().equals(SubscriptionStatusEnum.ACTIVE.getCode()))
                .collect(Collectors.toList());
        LocalDateTime startTime = LocalDateTime.now().withSecond(0).withNano(0);
        if (CollUtil.isNotEmpty(templateList)) {
            SubscriptionTenantTemplate tenantTemplate = templateList.get(0);
            tenantTemplate.setStatus(SubscriptionStatusEnum.EXPIRED.getCode());
            tenantTemplateManager.updateById(tenantTemplate);
            startTime = tenantTemplate.getExpirationTime();
        }
        templateList = tenantTemplateList.stream().filter(v -> v.getStatus().equals(SubscriptionStatusEnum.NO_ACTIVE.getCode()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(templateList)) {
            templateList.sort(Comparator.comparing(SubscriptionTenantTemplate::getCreatedTime));
            SubscriptionTenantTemplate tenantTemplate = templateList.get(0);
            tenantTemplate.setStatus(SubscriptionStatusEnum.ACTIVE.getCode());
            tenantTemplate.setStartTime(startTime.plusSeconds(1));
            tenantTemplate.setExpirationTime(tenantTemplateManager.getExpirationTime(
                    tenantTemplate.getStartTime(), tenantTemplate.getTmpBillingType(), tenantTemplate.getTmpDays()
            ));
            tenantTemplateManager.updateById(tenantTemplate);
        }
    }

    public void checkValueAdded(List<SubscriptionTenantTemplate> tenantTemplateList) {
        if (CollUtil.isEmpty(tenantTemplateList)) {
            return;
        }
        Map<Long, List<SubscriptionTenantTemplate>> tenantTemplateMap = tenantTemplateList.stream()
                .collect(Collectors.groupingBy(SubscriptionTenantTemplate::getTmpId));
        List<SubscriptionTenantTemplate> updateTenantTemplateList = new ArrayList<>();
        tenantTemplateMap.forEach((k, v) -> {
            List<SubscriptionTenantTemplate> templateList = v.stream().filter(item -> item.getStatus().equals(SubscriptionStatusEnum.ACTIVE.getCode()))
                    .collect(Collectors.toList());
            LocalDateTime startTime = LocalDateTime.now().withSecond(0).withNano(0);
            if (CollUtil.isNotEmpty(templateList)) {
                SubscriptionTenantTemplate tenantTemplate = templateList.get(0);
                tenantTemplate.setStatus(SubscriptionStatusEnum.EXPIRED.getCode());
                updateTenantTemplateList.add(tenantTemplate);
                startTime = tenantTemplate.getExpirationTime();
            }
            templateList = v.stream().filter(item -> item.getStatus().equals(SubscriptionStatusEnum.NO_ACTIVE.getCode()))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(templateList)) {
                templateList.sort(Comparator.comparing(SubscriptionTenantTemplate::getCreatedTime));
                SubscriptionTenantTemplate tenantTemplate = templateList.get(0);
                tenantTemplate.setStatus(SubscriptionStatusEnum.ACTIVE.getCode());
                tenantTemplate.setStartTime(startTime.plusSeconds(1));
                tenantTemplate.setExpirationTime(tenantTemplateManager.getExpirationTime(
                        tenantTemplate.getStartTime(), tenantTemplate.getTmpBillingType(), tenantTemplate.getTmpDays()
                ));
                updateTenantTemplateList.add(tenantTemplate);
            }
        });
        tenantTemplateManager.updateBatchById(updateTenantTemplateList);
    }
}
