package top.kx.kxss.system.service.system.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.system.entity.system.DefAppletToken;
import top.kx.kxss.system.manager.system.DefAppletTokenManager;
import top.kx.kxss.system.service.system.DefAppletTokenService;
import top.kx.kxss.system.vo.query.system.DefAppletTokenPageQuery;
import top.kx.kxss.system.vo.result.system.DefAppletTokenResultVO;
import top.kx.kxss.system.vo.save.system.DefAppletTokenSaveVO;
import top.kx.kxss.system.vo.update.system.DefAppletTokenUpdateVO;

/**
 * <p>
 * 业务实现类
 * 客户小程序令牌信息
 * </p>
 *
 * <AUTHOR>
 * @date 2024-04-27 10:41:58
 * @create [2024-04-27 10:41:58] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class DefAppletTokenServiceImpl extends SuperServiceImpl<DefAppletTokenManager, Long, DefAppletToken, DefAppletTokenSaveVO,
        DefAppletTokenUpdateVO, DefAppletTokenPageQuery, DefAppletTokenResultVO> implements DefAppletTokenService {


    @Override
    @Transactional
    public boolean save(DefAppletToken defAppletToken) {
        return superManager.save(defAppletToken);
    }

    @Override
    @Transactional
    public boolean updateById(DefAppletToken defAppletToken) {
        return superManager.updateById(defAppletToken);
    }

    @Override
    public DefAppletToken getByAuthorizerAppid(String authorizerAppid) {
        return superManager.getOne(Wraps.<DefAppletToken>lbQ()
                .eq(DefAppletToken::getAuthorizerAppid, authorizerAppid)
                .eq(DefAppletToken::getDeleteFlag, 0));
    }
}


