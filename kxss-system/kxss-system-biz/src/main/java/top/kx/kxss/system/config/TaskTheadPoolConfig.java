package top.kx.kxss.system.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.SchedulingConfigurer;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.config.ScheduledTaskRegistrar;
import top.kx.kxss.common.properties.TaskProperties;

import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 系统Yml配置参数定义Bean
 *
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties(TaskProperties.class)
public class TaskTheadPoolConfig implements SchedulingConfigurer {

    @Autowired
    private TaskProperties taskProperties;

    @Override
    public void configureTasks(ScheduledTaskRegistrar taskRegistrar) {
        taskRegistrar.setScheduler(Executors.newScheduledThreadPool(taskProperties.getCorePoolSize()));
    }

    @Bean
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //设置核心线程数
        executor.setCorePoolSize(taskProperties.getCorePoolSize());
        //设置最大线程数
        executor.setMaxPoolSize(taskProperties.getMaxPoolSize());
        //缓冲队列200：用来缓冲执行任务的队列
        executor.setQueueCapacity(taskProperties.getQueueCapacity());
        //线程活路时间 60 秒
        executor.setKeepAliveSeconds(taskProperties.getKeepAliveSeconds());
        //线程池名的前缀：设置好了之后可以方便我们定位处理任务所在的线程池
        // 继续沿用 scheduling 默认的线程名前缀
        executor.setThreadNamePrefix("task-scheduling-executor-");
        //设置拒绝策略
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        return executor;
    }
}
