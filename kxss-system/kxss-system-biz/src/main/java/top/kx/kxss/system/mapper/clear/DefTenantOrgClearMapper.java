package top.kx.kxss.system.mapper.clear;

import org.apache.ibatis.annotations.Param;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.system.entity.clear.DefTenantOrgClear;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;

/**
 * <p>
 * Mapper 接口
 * 数据清空记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-20 17:43:48
 * @create [2025-06-20 17:43:48] [yan] [代码生成器生成]
 */
@Repository
public interface DefTenantOrgClearMapper extends SuperMapper<DefTenantOrgClear> {

    void copyTableWithData(@Param("oldTableName") String oldTableName, @Param("newTableName") String newTableName,
                           @Param("tenantId") Long tenantId, @Param("orgId") Long orgId);

    void markDataDeletedByTimeRange(@Param("tenantId") Long tenantId, @Param("orgId") Long orgId,
                                    @Param("tableName") String tableName,
                                    @Param("startTime") LocalDateTime startTime,
                                    @Param("endTime") LocalDateTime endTime);
}


