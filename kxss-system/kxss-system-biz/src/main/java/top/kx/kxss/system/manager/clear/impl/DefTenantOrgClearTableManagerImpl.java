package top.kx.kxss.system.manager.clear.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.system.entity.clear.DefTenantOrgClearTable;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.system.manager.clear.DefTenantOrgClearTableManager;
import top.kx.kxss.system.mapper.clear.DefTenantOrgClearTableMapper;

/**
 * <p>
 * 通用业务实现类
 * 清空的表数据
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-20 17:43:47
 * @create [2025-06-20 17:43:47] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DefTenantOrgClearTableManagerImpl extends SuperManagerImpl<DefTenantOrgClearTableMapper, DefTenantOrgClearTable> implements DefTenantOrgClearTableManager {

}


