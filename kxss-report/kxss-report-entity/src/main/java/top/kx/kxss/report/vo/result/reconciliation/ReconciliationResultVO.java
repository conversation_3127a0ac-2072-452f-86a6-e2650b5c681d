package top.kx.kxss.report.vo.result.reconciliation;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 商户对账单
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-01 15:55:00
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "ReconciliationResultVO", description = "商户对账单")
public class ReconciliationResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键ID")
    private Long id;

    /**
    * 对账单日期（yyyyMMdd格式）
    */
    @ApiModelProperty(value = "对账单日期（yyyyMMdd格式）")
    private String reconciliationDate;
    /**
    * 机构号
    */
    @ApiModelProperty(value = "机构号")
    private String instNo;
    /**
    * 商户号
    */
    @ApiModelProperty(value = "商户号")
    private String mchNo;
    /**
    * 交易金额（分）
    */
    @ApiModelProperty(value = "交易金额（分）")
    private Long transactionAmount;
    /**
    * 手续费金额（分）
    */
    @ApiModelProperty(value = "手续费金额（分）")
    private Long feeAmount;
    /**
    * 退款金额（分）
    */
    @ApiModelProperty(value = "退款金额（分）")
    private Long refundAmount;
    /**
    * 结余金额（分）
    */
    @ApiModelProperty(value = "结余金额（分）")
    private Long balanceAmount;
    /**
    * 商家优惠金额（分）
    */
    @ApiModelProperty(value = "商家优惠金额（分）")
    private Long merchantDiscountAmount;
    /**
    * 商家实收金额（分）
    */
    @ApiModelProperty(value = "商家实收金额（分）")
    private Long merchantActualAmount;
    /**
    * 用户实付金额（分）
    */
    @ApiModelProperty(value = "用户实付金额（分）")
    private Long userPaidAmount;
    /**
    * 平台优惠金额（分）
    */
    @ApiModelProperty(value = "平台优惠金额（分）")
    private Long platformDiscountAmount;



}
