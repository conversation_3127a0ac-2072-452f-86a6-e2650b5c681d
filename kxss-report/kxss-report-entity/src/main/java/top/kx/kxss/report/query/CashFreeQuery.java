package top.kx.kxss.report.query;


import cn.hutool.core.map.MapUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 商品销售统计
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-12 17:22:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "CashFreeQuery", description = "支付明细数据")
public class CashFreeQuery extends DataOverviewQuery implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "单号")
    private String keyword;

    @ApiModelProperty(value = "类型")
    private String type;

    /**
     * 自定义备注
     */
    @ApiModelProperty(value = "单品自定义备注")
    private String remark;

    /**
     * 快捷备注
     */
    @ApiModelProperty(value = "单品快捷备注")
    private String singleRemark;

    /**
     * 是否审核
     */
    @ApiModelProperty(value = "是否审核")
    private Integer reviewStatus;

}
