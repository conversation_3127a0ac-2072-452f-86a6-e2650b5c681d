package top.kx.kxss.report.vo;


import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 商品销售统计
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-12 17:22:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ColumnWidth(20)
@ExcelIgnoreUnannotated
@ApiModel(value = "PosCashDetailsResultVO", description = "订单详情")
public class PosCashDetailsResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "订单id")
    private Long id;

    @ApiModelProperty(value = "流水号")
    @ExcelProperty(value = "流水号", index = 0)
    private String code;

    @ApiModelProperty(value = "业务类型")
    @ExcelProperty(value = "业务类型", index = 1)
    private String type;

    /**
     * 单据状态 0待结算  1挂单 2完成
     */
    @ApiModelProperty(value = "单据状态   0正单  1挂单 2退单")
    //@Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.BILL_STATE)
    @ExcelProperty(value = "单据状态", index = 2)
    private String billState;
    /**
     * 单据类型 0正单 1退单 2取消
     */
    @ApiModelProperty(value = "单据类型   0正单  1退单  2 取消")
    //@Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.BILL_TYPE)
    @ExcelProperty(value = "单据类型", index = 3)
    private String billType;


    @ApiModelProperty(value = "台桌名称")
    @ExcelProperty(value = "台桌名称", index = 4)
    private String tableName;

    @ApiModelProperty(value = "台桌类型")
    @ExcelProperty(value = "台桌类型", index = 5)
    private String tableType;

    @ApiModelProperty(value = "台桌区域")
    @ExcelProperty(value = "台桌区域", index = 6)
    private String tableArea;

    @ApiModelProperty(value = "创建员工")
    @ExcelProperty(value = "创建员工", index = 7)
    private String createdEmp;

    /**
     * 完成员工
     */
    @ApiModelProperty(value = "完成员工")
    @ExcelProperty(value = "完成员工", index = 8)
    private String completeEmp;

    @ApiModelProperty(value = "销售员工")
    @ExcelProperty(value = "销售员工", index = 9)
    private String employeeEmp;

    @ApiModelProperty(value = "提成员工")
    @ExcelProperty(value = "提成员工", index = 10)
    private String commenter;


    @ApiModelProperty(value = "订单来源")
    @ExcelProperty(value = "订单来源", index = 11)
    private String orderSource;

    @ApiModelProperty(value = "会员名称")
    @ExcelProperty(value = "会员名称", index = 12)
    private String memberName;

    @ApiModelProperty(value = "开台时间")
    @ExcelProperty(value = "开台时间", index = 13)
    private String createTime;

    @ApiModelProperty(value = "结账时间")
    @ExcelProperty(value = "结账时间", index = 14)
    private String completeTime;


    @ApiModelProperty(value = "台桌金额")
    @ExcelProperty(value = "台桌金额", index = 15)
    private BigDecimal tableAmount;


    @ApiModelProperty(value = "商品金额")
    @ExcelProperty(value = "商品金额", index = 16)
    private BigDecimal productAmount;


    @ApiModelProperty(value = "服务金额")
    @ExcelProperty(value = "服务金额", index = 17)
    private BigDecimal serviceAmount;


    @ApiModelProperty(value = "套餐金额")
    @ExcelProperty(value = "套餐金额", index = 18)
    private BigDecimal thailAmount;

    @ApiModelProperty(value = "充电金额")
    @ExcelProperty(value = "充电金额", index = 18)
    private BigDecimal powerAmount;


    @ApiModelProperty(value = "订单原价")
    @ExcelProperty(value = "订单原价", index = 19)
    private BigDecimal amount;


    @ApiModelProperty(value = "订单优惠金额")
    @ExcelProperty(value = "订单优惠金额", index = 20)
    private BigDecimal discountAmount;


    @ApiModelProperty(value = "订单部分退款金额")
    @ExcelProperty(value = "订单部分退款金额", index = 21)
    private BigDecimal refundAmount;


    @ApiModelProperty(value = "订单实收金额")
    @ExcelProperty(value = "订单实收金额", index = 22)
    private BigDecimal paid;


    @ApiModelProperty(value = "整单备注")
    @ExcelProperty(value = "整单备注", index = 23)
    private String orderRemarks;

    /**
     * 挂单时间
     */
    @ApiModelProperty(value = "挂单时间")
    @ExcelProperty(value = "挂单时间", index = 24)
    private String registrationTime;

    @ApiModelProperty(value = "挂单备注")
    @ExcelProperty(value = "挂单备注", index = 25)
    private String registrationRemarks;

    @ApiModelProperty(value = "支付详情")
    @ExcelProperty(value = "支付详情", index = 26)
    private String paymentDetails;

    @ApiModelProperty(value = "门店")
    @ExcelProperty(value = "门店", index = 27)
    private String org;


}
