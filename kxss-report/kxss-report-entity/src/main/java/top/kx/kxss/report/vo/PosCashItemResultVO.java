package top.kx.kxss.report.vo;


import cn.hutool.core.map.MapUtil;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 商品销售统计
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-12 17:22:26
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ColumnWidth(20)
@ExcelIgnoreUnannotated
@ApiModel(value = "PosCashItemResultVO", description = "订单详情")
public class PosCashItemResultVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "订单id")
    private Long cashId;

    @ApiModelProperty(value = "原价")
    private BigDecimal amount;

    @ApiModelProperty(value = "实际收入")
    private BigDecimal payment;

}
