package top.kx.kxss.report.controller.cash;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.report.vo.result.cash.OrderFreeResultVO;
import top.kx.kxss.pos.vo.order.OrderResultVO;
import top.kx.kxss.report.query.CashFreeQuery;
import top.kx.kxss.report.service.PosCashService;
import top.kx.kxss.report.vo.PosCashDetailsResultVO;
import top.kx.kxss.report.vo.PosCashNoPayDetailsResultVO;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.query.statistics.PosCashDetailsQuery;
import top.kx.kxss.wxapp.vo.query.statistics.ThailOverviewQuery;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 运营目标统计API
 *
 * <AUTHOR>
 */
@Api(value = "/report/cash", tags = "订单相关报表")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/cash")
public class PosCashController {

    private final PosCashService posCashService;


    @ApiOperation(value = "订单明细表", notes = "订单明细表")
    @PostMapping("/details")
    public R<Map<String, Object>> posCashDetails(@RequestBody PageParams<PosCashDetailsQuery> params) {
        return R.success(posCashService.posCashDetails(params));
    }

    @ApiOperation(value = "订单明细表-合计", notes = "订单明细表-合计")
    @PostMapping("/details/sum")
    public R<Map<String, Object>> posCashDetailsSum(@RequestBody PosCashDetailsQuery params) {
        return R.success(posCashService.posCashDetailsSum(params));
    }


    @ApiOperation(value = "订单明细表-导出", notes = "订单明细表-导出")
    @RequestMapping(value = "/details/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void posCashDetailsExport(@RequestBody PosCashDetailsQuery query, HttpServletResponse response) {
        List<PosCashDetailsResultVO> list = posCashService.posCashDetailsList(query);
//        PosCashDetailsResultVO sum = posCashService.posCashDetailsSum(query);
//        sum.setCode("合计");
//        list.add(sum);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=POS CASH.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, PosCashDetailsResultVO.class)
                    .sheet("sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }



    @ApiOperation(value = "待结账列表", notes = "待结账列表")
    @PostMapping("/noPay/list")
    public R<Map<String, Object>> noPayList(@RequestBody PageParams<DataOverviewQuery> params) {
        return R.success(posCashService.noPayPage(params));
    }

    @ApiOperation(value = "待结账-合计", notes = "待结账-合计")
    @PostMapping("/noPay/sum")
    public R<PosCashNoPayDetailsResultVO> noPaySum(@RequestBody DataOverviewQuery params) {
        return R.success(posCashService.noPaySum(params));
    }


    @ApiOperation(value = "待结账-导出", notes = "待结账-导出")
    @RequestMapping(value = "/noPay/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void noPayExport(@RequestBody DataOverviewQuery query, HttpServletResponse response) {
        List<PosCashNoPayDetailsResultVO> list = posCashService.noPayList(query);
        PosCashNoPayDetailsResultVO sum = posCashService.noPaySum(query);
        sum.setCode("合计");
        list.add(sum);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=NO PAY.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, PosCashNoPayDetailsResultVO.class)
                    .sheet("sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "套餐订单列表", notes = "套餐订单列表")
    @PostMapping("/thailPage")
    @WebLog("套餐订单列表")
    public R<IPage<OrderResultVO>> thailPage(@RequestBody @Validated PageParams<ThailOverviewQuery> query) {
        return R.success(posCashService.thailPage(query));
    }

    /**
     * 免单 - 分页
     */
    @ApiOperation(value = "免单明细报表 - 分页", notes = "免单明细报表 - 分页")
    @PostMapping("/free")
    public R<Map<String, Object>> freePage(@RequestBody @Validated PageParams<CashFreeQuery> query) {
        return R.success(posCashService.freePage(query));
    }

    @ApiOperation(value = "免单明细报表 - 求和", notes = "免单明细报表 - 分页")
    @PostMapping("/free/sum")
    public R<OrderFreeResultVO> freeSum(@RequestBody @Validated CashFreeQuery query) {
        return R.success(posCashService.freeSum(query));
    }

    @ApiOperation(value = "免单明细报表 - 导出", notes = "免单明细报表 - 导出")
    @RequestMapping(value = "/free/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void freeExport(@RequestBody CashFreeQuery query, HttpServletResponse response) {
        List<OrderFreeResultVO> list = posCashService.freeList(query);
        OrderFreeResultVO sum = posCashService.freeSum(query);
        sum.setCode("合计");
        list.add(sum);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=CASH FREE.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, OrderFreeResultVO.class)
                    .sheet("sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }

}
