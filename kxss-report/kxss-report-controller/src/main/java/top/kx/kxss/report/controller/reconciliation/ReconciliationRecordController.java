package top.kx.kxss.report.controller.reconciliation;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.report.service.reconciliation.ReconciliationRecordService;
import top.kx.kxss.report.entity.reconciliation.ReconciliationRecord;
import top.kx.kxss.report.vo.save.reconciliation.ReconciliationRecordSaveVO;
import top.kx.kxss.report.vo.update.reconciliation.ReconciliationRecordUpdateVO;
import top.kx.kxss.report.vo.result.reconciliation.ReconciliationRecordResultVO;
import top.kx.kxss.report.vo.query.reconciliation.ReconciliationRecordPageQuery;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 前端控制器
 * 对账单记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-30 20:18:34
 * @create [2025-06-30 20:18:34] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/reconciliationRecord")
@Api(value = "ReconciliationRecord", tags = "对账单记录")
public class ReconciliationRecordController extends SuperController<ReconciliationRecordService, Long, ReconciliationRecord, ReconciliationRecordSaveVO,
    ReconciliationRecordUpdateVO, ReconciliationRecordPageQuery, ReconciliationRecordResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

}


