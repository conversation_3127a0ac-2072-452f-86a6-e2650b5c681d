package top.kx.kxss.report.controller.attribute;

import com.alibaba.excel.EasyExcel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.report.query.ProductAttributeQuery;
import top.kx.kxss.report.service.ProductAttributeService;
import top.kx.kxss.report.vo.ProductAttributeResultVO;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 商品属性统计API
 *
 * <AUTHOR>
 */
@Api(value = "/report/product", tags = "商品属性统计API")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/product")
public class ProductAttributeController {

    private final ProductAttributeService productAttributeService;


    @ApiOperation(value = "商品属性-分页", notes = "商品属性-分页")
    @PostMapping("/attribute/page")
    @WebLog("商品属性")
    public R<Map<String, Object>> page(@RequestBody @Validated PageParams<ProductAttributeQuery> query) {
        return R.success(productAttributeService.page(query));
    }

    @ApiOperation(value = "商品属性-求和", notes = "商品属性-求和")
    @PostMapping("/attribute/sum")
    @WebLog("商品属性")
    public R<ProductAttributeResultVO> sum(@RequestBody @Validated ProductAttributeQuery query) {
        return R.success(productAttributeService.sum(query));
    }

    @ApiOperation(value = "商品属性列表-导出", notes = "商品属性列表-导出")
    @RequestMapping(value = "/attribute/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void export(@RequestBody @Validated ProductAttributeQuery query, HttpServletResponse response) {
        List<ProductAttributeResultVO> list = productAttributeService.list(query);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=PRODUCT.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, ProductAttributeResultVO.class)
                    .sheet("sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }


}
