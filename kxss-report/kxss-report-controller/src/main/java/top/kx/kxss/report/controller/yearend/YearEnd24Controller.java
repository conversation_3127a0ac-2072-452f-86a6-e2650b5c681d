package top.kx.kxss.report.controller.yearend;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.kxss.report.service.yearend.YearEnd24Service;
import top.kx.kxss.report.vo.yearend.YearEnd24DataResultVO;

/**
 * 24年年终总结相关API
 *
 * <AUTHOR>
 */
@Api(value = "/report/year/end/24", tags = "24年年终总结相关API")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/year/end/24")
public class YearEnd24Controller {

    private final YearEnd24Service yearEnd24Service;


    @ApiOperation(value = "年终总结信息概览", notes = "年终总结信息概览")
    @PostMapping("/overview")
    @WebLog("年终总结信息概览")
    public R<YearEnd24DataResultVO> overview() {
        return R.success(yearEnd24Service.overview());
    }

}
