package top.kx.kxss.report.controller.goals;

import cn.hutool.core.date.DateUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.report.query.GoalsQuery;
import top.kx.kxss.report.service.GoalsService;
import top.kx.kxss.report.service.ProfitService;
import top.kx.kxss.report.vo.GoalsAchievementResultVO;
import top.kx.kxss.report.vo.GoalsPaymentResultVO;
import top.kx.kxss.report.vo.ReferenceProfitResultVO;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

/**
 * 运营目标统计API
 *
 * <AUTHOR>
 */
@Api(value = "/report/goals", tags = "运营目标统计API")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/goals")
public class GoalsController {

    private final GoalsService goalsService;


    @ApiOperation(value = "上月收入汇总", notes = "上月收入汇总")
    @PostMapping("payment")
    @WebLog("收入汇总")
    public R<GoalsPaymentResultVO> payment() {
        DataOverviewQuery query = new DataOverviewQuery();
        query.setStartDate(DateUtil.format(DateUtil.beginOfMonth(DateUtil.offsetMonth(DateUtil.date(), -1)), DateUtils.DEFAULT_DATE_FORMAT));
        query.setEndDate(DateUtil.format(DateUtil.endOfMonth(DateUtil.offsetMonth(DateUtil.date(), -1)), DateUtils.DEFAULT_DATE_FORMAT));
        return R.success(goalsService.payment(query));
    }

    @ApiOperation(value = "目标达成率", notes = "目标达成率")
    @PostMapping("achievementRate")
    @WebLog("目标达成率")
    public R<GoalsAchievementResultVO> achievementRate(@RequestBody @Validated GoalsQuery goalsQuery) {
        return R.success(goalsService.achievementRate(goalsQuery));
    }


}
