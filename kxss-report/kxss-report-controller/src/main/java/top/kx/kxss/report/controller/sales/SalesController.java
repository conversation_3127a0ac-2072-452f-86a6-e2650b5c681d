package top.kx.kxss.report.controller.sales;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.kxss.report.query.ProductSalesQuery;
import top.kx.kxss.report.service.SalesService;
import top.kx.kxss.report.vo.SalesResultVO;

/**
 * 商品销售统计API
 *
 * <AUTHOR>
 */
@Api(value = "/report/sales", tags = "销售统计API")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/sales")
public class SalesController {

    private final SalesService salesService;


    @ApiOperation(value = "销售统计列表", notes = "销售统计列表")
    @PostMapping("/{equityType}")
    @WebLog("销售统计列表")
    public R<SalesResultVO> info(@PathVariable String equityType, @RequestBody @Validated ProductSalesQuery query) {
        return R.success(salesService.info(equityType, query));
    }


}
