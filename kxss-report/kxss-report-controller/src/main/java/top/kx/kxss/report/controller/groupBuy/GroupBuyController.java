package top.kx.kxss.report.controller.groupBuy;

import com.alibaba.excel.EasyExcel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.report.query.GroupBuyQuery;
import top.kx.kxss.report.service.GroupBuyService;
import top.kx.kxss.report.vo.GroupBuyResultVO;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 运营目标统计API
 *
 * <AUTHOR>
 */
@Api(value = "/report/groupBuy", tags = "团购统计API")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/groupBuy")
public class GroupBuyController {

    private final GroupBuyService groupBuyService;


    @ApiOperation(value = "团购列表", notes = "团购列表")
    @PostMapping("/page")
    @WebLog("团购列表")
    public R<Map<String, Object>> page(@RequestBody @Validated PageParams<GroupBuyQuery> query) {
        return R.success(groupBuyService.page(query));
    }

    @ApiOperation(value = "团购列表-统计", notes = "团购列表-统计")
    @PostMapping("/sum")
    public R<GroupBuyResultVO> sum(@RequestBody @Validated GroupBuyQuery query) {
        return R.success(groupBuyService.sum(query));
    }

    @ApiOperation(value = "团购列表-导出", notes = "团购列表-导出")
    @RequestMapping(value = "/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void export(@RequestBody @Validated GroupBuyQuery query, HttpServletResponse response) {
        List<GroupBuyResultVO> list = groupBuyService.list(query);
        GroupBuyResultVO sum = groupBuyService.sum(query);
        sum.setSecuritiesNumber("合计");
        list.add(sum);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=Group Buy.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, GroupBuyResultVO.class)
                    .sheet("sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }


}
