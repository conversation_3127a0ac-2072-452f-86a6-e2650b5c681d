package top.kx.kxss.report.controller.score;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.report.query.ProductSalesQuery;
import top.kx.kxss.report.query.ScoreExchangeQuery;
import top.kx.kxss.report.service.SalesService;
import top.kx.kxss.report.service.ScoreExchangeService;
import top.kx.kxss.report.vo.ReferenceProfitResultVO;
import top.kx.kxss.report.vo.SalesResultVO;
import top.kx.kxss.report.vo.ScoreExchangeResultVO;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.query.statistics.TimeRangeQuery;
import top.kx.kxss.wxapp.vo.result.statistics.StatisTimeRangeResultVO;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 商品销售统计API
 *
 * <AUTHOR>
 */
@Api(value = "/report/scoreExchange", tags = "积分兑换统计API")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/score")
public class ScoreExchangeController {

    private final ScoreExchangeService scoreExchangeService;


    @ApiOperation(value = "积分兑换记录-分页", notes = "积分兑换记录-分页")
    @PostMapping("exchange/page")
    @WebLog("积分兑换记录")
    public R<Map<String, Object>> exchange(@RequestBody @Validated PageParams<ScoreExchangeQuery> query) {
        return R.success(scoreExchangeService.page(query));
    }

    @ApiOperation(value = "积分兑换记录-统计", notes = "积分兑换记录-统计")
    @PostMapping("exchange/sum")
    public R<ScoreExchangeResultVO> sum(@RequestBody @Validated ScoreExchangeQuery query) {
        return R.success(scoreExchangeService.sum(query));
    }

    @ApiOperation(value = "积分兑换记录-导出", notes = "积分兑换记录-导出")
    @RequestMapping(value = "exchange/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void export(@RequestBody @Validated ScoreExchangeQuery query, HttpServletResponse response) {
        List<ScoreExchangeResultVO> list = scoreExchangeService.list(query);
        ScoreExchangeResultVO sum = scoreExchangeService.sum(query);
        sum.setCategoryName("合计");
        list.add(sum);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=Score Exchange.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, ScoreExchangeResultVO.class)
                    .sheet("sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }


}
