package top.kx.kxss.report.controller.analysis;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.report.query.AnalysisMonthQuery;
import top.kx.kxss.report.query.ProductAttributeQuery;
import top.kx.kxss.report.service.AnalysisService;
import top.kx.kxss.report.service.ProductAttributeService;

import java.util.Map;

/**
 * 分析报表
 *
 * <AUTHOR>
 */
@Api(value = "/report/analysis", tags = "分析报表api")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/analysis")
public class AnalysisController {

    private final AnalysisService analysisService;


    @ApiOperation(value = "月营业分析-分页", notes = "月营业分析-分页")
    @PostMapping("/month/page")
    @WebLog("门店营业分析")
    public R<Map<String, Object>> monthPage(@RequestBody @Validated PageParams<AnalysisMonthQuery> query) {
        return R.success(analysisService.monthPage(query));
    }



}
