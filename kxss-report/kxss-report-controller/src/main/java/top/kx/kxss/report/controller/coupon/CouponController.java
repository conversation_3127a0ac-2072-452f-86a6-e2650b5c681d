package top.kx.kxss.report.controller.coupon;

import com.alibaba.excel.EasyExcel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.report.query.CouponInfoQuery;
import top.kx.kxss.report.query.CouponIssueQuery;
import top.kx.kxss.report.service.CouponService;
import top.kx.kxss.report.vo.MemberCouponResultVO;
import top.kx.kxss.report.vo.StatisCouponResultVO;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 会员券统计API
 *
 * <AUTHOR>
 */
@Api(value = "/report/coupon", tags = "会员优惠券API")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/coupon")
public class CouponController {

    private final CouponService couponService;


    @ApiOperation(value = "优惠券统计表", notes = "优惠券统计表")
    @PostMapping("statis/page")
    @WebLog("团购列表")
    public R<Map<String, Object>> statisPage(@RequestBody @Validated PageParams<CouponInfoQuery> query) {
        return R.success(couponService.statisPage(query));
    }

    @ApiOperation(value = "优惠券统计表-统计", notes = "优惠券统计表-统计")
    @PostMapping("statis/sum")
    public R<StatisCouponResultVO> statisSum(@RequestBody @Validated CouponInfoQuery query) {
        return R.success(couponService.statisSum(query));
    }

    @ApiOperation(value = "优惠券统计表-导出", notes = "优惠券统计表-导出")
    @RequestMapping(value = "statis/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void statisExport(@RequestBody @Validated CouponInfoQuery query, HttpServletResponse response) {
        List<StatisCouponResultVO> list = couponService.statisList(query);
        StatisCouponResultVO sum = couponService.statisSum(query);
        sum.setTypeDesc("合计");
        list.add(sum);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=STATIS_COUPON.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, StatisCouponResultVO.class)
                    .sheet("sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }


    @ApiOperation(value = "优惠券统计表", notes = "优惠券统计表")
    @PostMapping("memberCoupon/page")
    @WebLog("团购列表")
    public R<Map<String, Object>> memberCouponPage(@RequestBody @Validated PageParams<CouponIssueQuery> query) {
        return R.success(couponService.memberCouponPage(query));
    }

    @ApiOperation(value = "优惠券统计表-统计", notes = "优惠券统计表-统计")
    @PostMapping("memberCoupon/sum")
    public R<MemberCouponResultVO> memberCouponSum(@RequestBody @Validated CouponIssueQuery query) {
        return R.success(couponService.memberCouponSum(query));
    }

    @ApiOperation(value = "优惠券统计表-导出", notes = "优惠券统计表-导出")
    @RequestMapping(value = "memberCoupon/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void memberCouponExport(@RequestBody @Validated CouponIssueQuery query, HttpServletResponse response) {
        List<MemberCouponResultVO> list = couponService.memberCouponList(query);
        MemberCouponResultVO sum = couponService.memberCouponSum(query);
        sum.setTypeDesc("合计");
        list.add(sum);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=MEMBER_COUPON.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, MemberCouponResultVO.class)
                    .sheet("sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }


}
