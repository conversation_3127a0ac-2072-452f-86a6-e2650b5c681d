package top.kx.kxss.report.controller.reconciliation;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.kxss.base.vo.result.payment.BankCardInfoResultVO;
import top.kx.kxss.pay.vo.query.DayReconciliationQuery;
import top.kx.kxss.pay.vo.result.ReconciliationResultVO;
import top.kx.kxss.report.query.reconciliation.ReconciliationDownloadQuery;
import top.kx.kxss.report.query.reconciliation.StatisticReconciliationQuery;
import top.kx.kxss.report.service.reconciliation.ReconciliationDownloadService;
import top.kx.kxss.report.service.reconciliation.ReconciliationService;
import top.kx.kxss.report.vo.result.reconciliation.StatisticReconciliationResultVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 商品属性统计API
 *
 * <AUTHOR>
 */
@Api(value = "/report/reconciliation", tags = "聚合对账")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/reconciliation")
public class ReconciliationController {

    private final ReconciliationDownloadService downloadService;
    private final ReconciliationService reconciliationService;

    @ApiOperation(value = "解析数据", notes = "解析数据")
    @PostMapping("/download")
    public R<String> service(@RequestBody @Validated ReconciliationDownloadQuery query) {
        return R.success(downloadService.downloadAndSaveReconciliation(query));
    }

    @ApiOperation(value = "支付配置", notes = "支付配置")
    @PostMapping("/payChannel")
    public R<List<BankCardInfoResultVO>> payChannel() {
        return R.success(reconciliationService.payChannel());
    }


    @ApiOperation(value = "统计", notes = "统计")
    @PostMapping("/statistic")
    public R<StatisticReconciliationResultVO> statistic(@RequestBody @Validated StatisticReconciliationQuery query) {
        return R.success(reconciliationService.statistic(query));
    }


    @ApiOperation(value = "日对账单下载", notes = "日对账单下载")
    @PostMapping(value = "/dayReconciliationDownload", produces = "application/octet-stream")
    public void dayReconciliationDownload(@RequestBody @Validated StatisticReconciliationQuery query, HttpServletResponse response) {
        downloadService.dayReconciliationDownload(query, response);
    }

    @ApiOperation(value = "对账日报表", notes = "根据类型获取日期列表：type=1获取当月所有日期，type=2获取当年所有月份")
    @PostMapping("/dateList")
    public R<List<ReconciliationResultVO>> dateList(@RequestBody DayReconciliationQuery query) {
        return R.success(reconciliationService.dateList(query));
    }

}
