package top.kx.kxss.report.controller.profit;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.kxss.report.service.ProfitService;
import top.kx.kxss.report.vo.ReferenceProfitResultVO;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

/**
 * 商品销售统计API
 *
 * <AUTHOR>
 */
@Api(value = "/report/profit", tags = "利润统计API")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/profit")
public class ProfitController {

    private final ProfitService profitService;


    @ApiOperation(value = "参考利润", notes = "参考利润")
    @PostMapping("reference")
    @WebLog("参考利润")
    public R<ReferenceProfitResultVO> reference(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(profitService.reference(query));
    }


}
