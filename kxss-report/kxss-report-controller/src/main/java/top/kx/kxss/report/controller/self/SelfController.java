package top.kx.kxss.report.controller.self;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.kxss.report.service.SelfService;
import top.kx.kxss.report.vo.result.self.SelfServiceResultVO;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

/**
 * 商品属性统计API
 *
 * <AUTHOR>
 */
@Api(value = "/report/self", tags = "个人统计API")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/self")
public class SelfController {

    private final SelfService selfService;

    @ApiOperation(value = "服务", notes = "服务")
    @PostMapping("/service")
    @WebLog("服务")
    public R<SelfServiceResultVO> service(@RequestBody @Validated DataOverviewQuery query) {
        return R.success(selfService.service(query));
    }



}
