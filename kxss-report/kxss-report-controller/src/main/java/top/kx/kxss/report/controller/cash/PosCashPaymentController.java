package top.kx.kxss.report.controller.cash;

import com.alibaba.excel.EasyExcel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.report.query.PaymentTransactionQuery;
import top.kx.kxss.report.service.PosCashPaymentService;
import top.kx.kxss.report.vo.PaymentTransactionResultVO;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 支付方式统计API
 *
 * <AUTHOR>
 */
@Api(value = "/report/payment", tags = "订单相关报表")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/payment")
public class PosCashPaymentController {

    private final PosCashPaymentService posCashPaymentService;


    @ApiOperation(value = "支付流水", notes = "支付流水")
    @PostMapping("/transaction")
    public R<Map<String, Object>> transaction(@RequestBody PageParams<PaymentTransactionQuery> params) {
        return R.success(posCashPaymentService.transaction(params));
    }

    @ApiOperation(value = "支付流水-合计", notes = "支付流水-合计")
    @PostMapping("/transaction/sum")
    public R<PaymentTransactionResultVO> transactionSum(@RequestBody PaymentTransactionQuery params) {
        return R.success(posCashPaymentService.transactionSum(params));
    }


    @ApiOperation(value = "支付流水-导出", notes = "支付流水-导出")
    @RequestMapping(value = "/transaction/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void transactionExport(@RequestBody PaymentTransactionQuery query, HttpServletResponse response) {
        List<PaymentTransactionResultVO> list = posCashPaymentService.transactionList(query);
        PaymentTransactionResultVO sum = posCashPaymentService.transactionSum(query);
        sum.setCode("合计");
        list.add(sum);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=TRANSACTION.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, PaymentTransactionResultVO.class)
                    .sheet("sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }



}
