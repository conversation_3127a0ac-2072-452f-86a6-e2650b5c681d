package top.kx.kxss.report.controller.card;

import com.alibaba.excel.EasyExcel;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.report.query.CardConsumeQuery;
import top.kx.kxss.report.service.CardService;
import top.kx.kxss.report.vo.MemberCardChangeResultVO;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 会员卡统计API
 *
 * <AUTHOR>
 */
@Api(value = "/report/card", tags = "会员卡API")
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("/card")
public class CardController {

    private final CardService cardService;


    @ApiOperation(value = "会员卡使用记录", notes = "会员卡使用记录")
    @PostMapping("memberCardChange/page")
    public R<Map<String, Object>> memberCardChangePage(@RequestBody @Validated PageParams<CardConsumeQuery> query) {
        return R.success(cardService.memberCardChangePage(query));
    }

    @ApiOperation(value = "会员卡使用记录-统计", notes = "会员卡使用记录-统计")
    @PostMapping("memberCardChange/sum")
    public R<MemberCardChangeResultVO> memberCardChangeSum(@RequestBody @Validated CardConsumeQuery query) {
        return R.success(cardService.memberCardChangeSum(query));
    }

    @ApiOperation(value = "会员卡使用记录-导出", notes = "会员卡使用记录-导出")
    @RequestMapping(value = "memberCardChange/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void memberCardChangeExport(@RequestBody @Validated CardConsumeQuery query, HttpServletResponse response) {
        List<MemberCardChangeResultVO> list = cardService.memberCardChangeList(query);
        MemberCardChangeResultVO sum = cardService.memberCardChangeSum(query);
        sum.setTypeDesc("合计");
        list.add(sum);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=MEMBER_CARD.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, MemberCardChangeResultVO.class)
                    .sheet("sheet1")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }


}
