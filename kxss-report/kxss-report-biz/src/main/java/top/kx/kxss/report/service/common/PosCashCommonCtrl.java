package top.kx.kxss.report.service.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillTypeEnum;
import top.kx.kxss.model.enumeration.base.PosCashTypeEnum;
import top.kx.kxss.pos.entity.cash.PosCashPaymentTransaction;
import top.kx.kxss.report.query.PaymentTransactionQuery;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.query.statistics.PosCashDetailsQuery;
import top.kx.kxss.wxapp.vo.query.statistics.ThailOverviewQuery;

import java.util.Arrays;
import java.util.Objects;

/**
 * 商品销售API
 *
 * <AUTHOR>
 */
public class PosCashCommonCtrl {


    /**
     * 基础查询条件
     * @param query
     * @return
     */
    public QueryWrapper baseWrapper(DataOverviewQuery query) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(),
                PosCashBillStateEnum.PART_REFUND.getCode()));
        queryWrapper.notIn("p.bill_type", Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                , PosCashBillTypeEnum.CHARGEBACK.getCode()));
        queryWrapper.eq("p.delete_flag", 0);
        queryWrapper.isNotNull("p.complete_time");
        queryWrapper.eq("p.org_id", ContextUtil.getCurrentCompanyId());
        queryWrapper.between("p.complete_time", query.getStartDate(), query.getEndDate());
        return queryWrapper;
    }

    public QueryWrap<PosCashPaymentTransaction> paymentTransactionWrapper(PaymentTransactionQuery query) {
        QueryWrap<PosCashPaymentTransaction> queryWrapper = new QueryWrap<>();
        queryWrapper.eq("pcpt.delete_flag", 0).eq("pc.delete_flag", 0);
        queryWrapper.eq("pcpt.created_org_id", ContextUtil.getCurrentCompanyId());
        queryWrapper.like(StringUtils.isNotBlank(query.getCode()),"pcpt.code_", query.getCode());
        queryWrapper.eq(StringUtils.isNotBlank(query.getType()),"pcpt.type_", query.getType());
        queryWrapper.eq(StringUtils.isNotBlank(query.getStatus()),"pcpt.status", query.getStatus());
        queryWrapper.eq(ObjectUtil.isNotNull(query.getPayTypeId()),"pcpt.pay_type_id", query.getPayTypeId());
        queryWrapper.in(CollUtil.isNotEmpty(query.getPayTypeIds()),"pcpt.pay_type_id", query.getPayTypeIds());
        queryWrapper.in(CollUtil.isNotEmpty(query.getOrgIdList()),"pcpt.created_org_id", query.getOrgIdList());
        if (StringUtils.isNotBlank(query.getStartDate())) {
            queryWrapper.ge("pcpt.pay_time", query.getStartDate());
        }
        if (StringUtils.isNotBlank(query.getEndDate())) {
            queryWrapper.le("pcpt.pay_time", query.getEndDate());
        }
        if (StringUtils.isNotBlank(query.getCompleteStartDate())) {
            queryWrapper.ge("pc.complete_time", query.getCompleteStartDate());
        }
        if (StringUtils.isNotBlank(query.getCompleteEndDate())) {
            queryWrapper.le("pc.complete_time", query.getCompleteEndDate());
        }
        queryWrapper.in(CollUtil.isNotEmpty(query.getOrderSourceList()),"pcpt.order_source", query.getOrderSourceList());
        queryWrapper.like(StringUtils.isNotBlank(query.getOrderId()),"pcpt.order_id", query.getOrderId());
        queryWrapper.like(StringUtils.isNotBlank(query.getSecuritiesNumber()),"pcpt.securities_number", query.getSecuritiesNumber());
        queryWrapper.in(CollUtil.isNotEmpty(query.getEmployeeIdList()),"pcpt.employee_id", query.getEmployeeIdList());
        queryWrapper.in(CollUtil.isNotEmpty(query.getBillStateList()),"pc.bill_state", query.getBillStateList());
        queryWrapper.in(CollUtil.isNotEmpty(query.getTableIdList()),"pc.table_id", query.getTableIdList());
        queryWrapper.orderByDesc("pcpt.pay_time").orderByDesc("pcpt.id");
        return queryWrapper;
    }


    public QueryWrap<PosCash> initWarp(PosCashDetailsQuery model) {
        QueryWrap<PosCash> wrap = new QueryWrap<>();
        wrap
                .in(StringUtils.isNotBlank(model.getType()), "p.type_", model.getType())
                .in(CollUtil.isNotEmpty(model.getTypeList()), "p.type_", model.getTypeList())
                .eq(StringUtils.isNotBlank(model.getOrderSource()), "p.order_source", model.getOrderSource())
                .like(StringUtils.isNotBlank(model.getTableName()), "p.table_name", model.getTableName())
                .like(StringUtils.isNotBlank(model.getKeyword()), "p.code", model.getKeyword())
                .like(StringUtils.isNotBlank(model.getCode()), "p.code", model.getCode())
                .eq(ObjectUtil.isNotNull(model.getEmployeeId()), "p.employee_id", model.getEmployeeId())
                .in(CollUtil.isNotEmpty(model.getBillTypeList()), "p.bill_type", model.getBillTypeList())
                .in(CollUtil.isNotEmpty(model.getBillStateList()), "p.bill_state", model.getBillStateList())
                .eq("p.delete_flag", 0)
                .in(CollUtil.isNotEmpty(model.getOrgIdList()), "p.org_id", model.getOrgIdList())
                .between(StringUtils.isNotBlank(model.getStartDate())
                        && StringUtils.isNotBlank(model.getEndDate()), "p.created_time", model.getStartDate(), model.getEndDate())
                .between(StringUtils.isNotBlank(model.getCompleteTime_st())
                        && StringUtils.isNotBlank(model.getCompleteTime_ed()), "p.complete_time", model.getCompleteTime_st(), model.getCompleteTime_ed())
                .eq(ObjectUtil.isNotNull(model.getCommenter()), "c.employee_id", model.getCommenter())
                .eq(ObjectUtil.isNotNull(model.getCommenter()), "c.delete_flag", 0)
                .eq(ObjectUtil.isNotNull(model.getCommenter()), "c.type_", "4002")
                .eq(StringUtils.isNotBlank(model.getTableType()) || StringUtils.isNotBlank(model.getTableArea()),
                        "t.delete_flag", 0)
                .eq(StringUtils.isNotBlank(model.getTableType()), "t.table_type", model.getTableType())
                .eq(StringUtils.isNotBlank(model.getTableArea()), "t.table_area", model.getTableArea())
                .eq(StringUtils.isNotBlank(model.getMemberName()), "m.delete_flag", 0)
                .apply(StringUtils.isNotBlank(model.getMemberName()), "instr(m.name, '" + model.getMemberName() + "')");
        wrap.groupBy("p.id");
        wrap.orderByDesc("p.complete_time");
        return wrap;
    }

    public QueryWrap<PosCash> noPay(DataOverviewQuery model) {
        QueryWrap<PosCash> wrap = new QueryWrap<>();
        wrap.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.NO_SETTLED.getCode()
                        , PosCashBillStateEnum.NO_PAY.getCode(), PosCashBillStateEnum.PART_PAY.getCode()))
                .in(CollUtil.isNotEmpty(model.getOrgIdList()), "p.org_id", model.getOrgIdList())
                .between(StringUtils.isNotBlank(model.getStartDate())
                        && StringUtils.isNotBlank(model.getEndDate()), "p.created_time", model.getStartDate(), model.getEndDate());
        wrap.orderByDesc("p.created_time");
        return wrap;
    }



    public QueryWrap<PosCash> thailWarp(ThailOverviewQuery model) {
        QueryWrap<PosCash> wrapper = new QueryWrap<>();
        wrapper.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(),
                        PosCashBillStateEnum.PART_REFUND.getCode()))
                .notIn("p.bill_type", Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode(),
                        PosCashBillTypeEnum.CHARGEBACK.getCode()))
                .eq("p.delete_flag", 0)
                .eq("t.delete_flag", 0)
                .eq(Objects.nonNull(model.getThailId()),"t.thail_id", model.getThailId())
                .eq(StringUtils.isNotBlank(model.getThailName()),"t.thail_name", model.getThailName())
                .eq(Objects.nonNull(model.getAmount()),"t.amount", model.getAmount())
                .eq(StringUtils.isNotBlank(model.getGroupBuyType()),"t.group_buy_type", model.getGroupBuyType())
                .ne("p.type_", PosCashTypeEnum.RECHARGE.getCode())
                .between("p.complete_time", model.getStartDate(), model.getEndDate())
                .eq("p.org_id", ContextUtil.getCurrentCompanyId());
        if (Objects.nonNull(model.getIsCheckSecurities())) {
            wrapper.eq("t.is_check_securities", model.getIsCheckSecurities());
        }
        wrapper.groupBy("p.id");
        return wrapper;
    }


}

