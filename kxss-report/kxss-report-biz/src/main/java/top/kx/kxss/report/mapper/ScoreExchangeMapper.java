package top.kx.kxss.report.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.report.query.ScoreExchangeQuery;
import top.kx.kxss.report.vo.ScoreExchangeResultVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * 积分兑换
 * </p>
 *
 * <AUTHOR>
 */
@Repository
public interface ScoreExchangeMapper {

    IPage<ScoreExchangeResultVO> page(@Param("page") IPage<ScoreExchangeResultVO> page, @Param("model") ScoreExchangeQuery model);


    ScoreExchangeResultVO sum(@Param("model") ScoreExchangeQuery model);


    List<ScoreExchangeResultVO> list(@Param("model") ScoreExchangeQuery model);

}


