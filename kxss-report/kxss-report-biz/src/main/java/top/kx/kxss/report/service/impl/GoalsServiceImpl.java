package top.kx.kxss.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.vo.result.cash.AmountResultVO;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO;
import top.kx.kxss.base.vo.query.goals.BaseBusinessGoalsPageQuery;
import top.kx.kxss.base.vo.result.goals.BaseBusinessGoalsResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.goals.BusinessGoalsApi;
import top.kx.kxss.model.enumeration.base.*;
import top.kx.kxss.report.query.GoalsQuery;
import top.kx.kxss.report.service.GoalsService;
import top.kx.kxss.report.service.PosCashPaymentService;
import top.kx.kxss.report.service.PosCashService;
import top.kx.kxss.report.service.common.GoalsCommonCtrl;
import top.kx.kxss.report.vo.GoalsAchievementResultVO;
import top.kx.kxss.report.vo.GoalsPaymentResultVO;
import top.kx.kxss.wxapp.api.custom.CustomApi;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Calendar;
import java.util.List;
import java.util.Objects;

/**
 * 利润销售API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class GoalsServiceImpl extends GoalsCommonCtrl implements GoalsService {

    @Autowired
    private CustomApi customApi;
    @Autowired
    private PosCashService posCashService;
    @Autowired
    private PosCashPaymentService posCashPaymentService;
    @Autowired
    private BusinessGoalsApi businessGoalsApi;


    @Override
    public GoalsPaymentResultVO payment(DataOverviewQuery query) {
        GoalsPaymentResultVO resultVO = GoalsPaymentResultVO.builder()
                .build();
        R<DataOverviewQuery> storeTime = customApi.getStoreTime(query);
        DataOverviewQuery storeTimeData = storeTime.getData();
        query.setStartDate(storeTimeData.getStartDate());
        query.setEndDate(storeTimeData.getEndDate());
        AmountResultVO amountResultVO = posCashService.selectOneAmount(query);
        // 营业额(原价不含充值)
        BigDecimal amount = new BigDecimal("0.00");
        // 收款金额(含充值)
        BigDecimal payment = new BigDecimal("0.00");
        // 优惠
        BigDecimal discountAmount = BigDecimal.ZERO;
        if (Objects.nonNull(amountResultVO)) {
            amount = amountResultVO.getAmount().subtract(amountResultVO.getRechargeAmount());
            // payment = amountResultVO.getPayment();
            //营业收入
            QueryWrapper<PosCash> wrapper = payTypeWrapper(query);
            wrapper.notIn("bpt.biz_type", PaymentBizTypeEnum.ACCOUNT.getCode(), PaymentBizTypeEnum.STORED.getCode());
            List<AmountResultVO> amountResultVOList = posCashService.selectByPayType(wrapper);
            if (CollUtil.isNotEmpty(amountResultVOList)) {
                payment = amountResultVOList.stream().map(AmountResultVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            }
            discountAmount = amountResultVO.getDiscountAmount();
        }
        PosCashPaymentResultVO paymentResultVO = posCashPaymentService.selectOneRechargeAmount(query);
        if (Objects.nonNull(paymentResultVO)) {
            discountAmount = discountAmount.add(paymentResultVO.getGiftPayment());
        }
        // 营业额
        resultVO.setAmount(amount);
        // 收款
        resultVO.setPayment(payment);
        // 按营业收入(不含优惠), 优惠金额 = 优惠金额 + 赠金消费
        resultVO.setRevenue(amount.subtract(discountAmount));
        return resultVO;
    }

    @Override
    public GoalsAchievementResultVO achievementRate(GoalsQuery query) {
        BaseBusinessGoalsPageQuery queryGoals = BaseBusinessGoalsPageQuery.builder().build();
        switch (query.getCompareType()) {
            case "1":
                queryGoals.setClassify(BaseBusinessClassifyEnum.AMOUNT.getCode());
                break;
            case "2":
                queryGoals.setClassify(BaseBusinessClassifyEnum.PAYMENT.getCode());
                break;
            case "3":
                queryGoals.setClassify(BaseBusinessClassifyEnum.REVENUE.getCode());
                break;
            default:
                ArgumentAssert.isTrue(false, "请选择正确的对比类型");
                break;
        }
        R<BaseBusinessGoalsResultVO> goalsResultVOR = businessGoalsApi.selectOne(queryGoals);
        // R<BaseBusinessGoalsResultVO> goalsResultVOR = null;
        //BaseBusinessGoals goals = baseBusinessGoalsMapper.selectOne(Wraps.<BaseBusinessGoals>lbQ().eq(SuperEntity::getDeleteFlag, 0).orderByDesc(SuperEntity::getCreatedTime).last("limit 1"));
        //BaseBusinessGoalsResultVO goalsResultVO = BeanPlusUtil.toBean(goals, BaseBusinessGoalsResultVO.class);
        ArgumentAssert.isTrue(goalsResultVOR.getIsSuccess(), "营运目标查询失败,请稍后再试");
        ArgumentAssert.notNull(goalsResultVOR.getData(), "营运目标未设置,请设置后查看");
        BaseBusinessGoalsResultVO goalsResultVO = goalsResultVOR.getData();
        BaseBusinessGoalsEnum baseBusinessGoalsEnum = BaseBusinessGoalsEnum.get(goalsResultVO.getType());
        // 查询当天的,
        DataOverviewQuery dayQuery = new DataOverviewQuery();
        dayQuery.setStartDate(DateUtil.format(DateUtil.beginOfDay(DateUtil.date()), DateUtils.DEFAULT_DATE_FORMAT));
        dayQuery.setEndDate(DateUtil.format(DateUtil.endOfDay(DateUtil.date()), DateUtils.DEFAULT_DATE_FORMAT));
        // 本周的
        DataOverviewQuery weekQuery = new DataOverviewQuery();
        weekQuery.setStartDate(DateUtil.format(DateUtil.beginOfWeek(DateUtil.date()), DateUtils.DEFAULT_DATE_FORMAT));
        weekQuery.setEndDate(DateUtil.format(DateUtil.date(), DateUtils.DEFAULT_DATE_FORMAT));
        // 本月的
        DataOverviewQuery monthQuery = new DataOverviewQuery();
        monthQuery.setStartDate(DateUtil.format(DateUtil.beginOfMonth(DateUtil.date()), DateUtils.DEFAULT_DATE_FORMAT));
        monthQuery.setEndDate(DateUtil.format(DateUtil.date(), DateUtils.DEFAULT_DATE_FORMAT));

        GoalsPaymentResultVO dayPayment = payment(dayQuery);
        GoalsPaymentResultVO weekPayment = payment(weekQuery);
        GoalsPaymentResultVO monthPayment = payment(monthQuery);
        GoalsAchievementResultVO resultVO = new GoalsAchievementResultVO();

        // 本月天数
        resultVO.setAchievementDays(Calendar.getInstance().getActualMaximum(Calendar.DAY_OF_MONTH));
        // 已经过去的天数
        resultVO.setAchievementDaysPassed(DateUtil.dayOfMonth(DateUtil.date()));
        // 月时间比
        resultVO.setAchievementTimeRatio(BigDecimal.valueOf(resultVO.getAchievementDaysPassed())
                .divide(BigDecimal.valueOf(resultVO.getAchievementDays()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP)));
        // 本周开始日期
        resultVO.setWeekStartDate(DateUtil.format(DateUtil.beginOfWeek(DateUtil.date()), "MM月dd日"));
        resultVO.setWeekEndDate(DateUtil.format(DateUtil.endOfWeek(DateUtil.date()), "MM月dd日"));

        // 本日的日期
        resultVO.setDayDate(DateUtil.format(DateUtil.date(), "MM月dd日"));

        // 1-营业额,2-收款金额,3-营业收入
        switch (query.getCompareType()) {
            case "1":
                setResultVO(goalsResultVO, baseBusinessGoalsEnum, resultVO, monthPayment.getAmount(), weekPayment.getAmount(), dayPayment.getAmount());
                break;
            case "2":
                setResultVO(goalsResultVO, baseBusinessGoalsEnum, resultVO, monthPayment.getPayment(), weekPayment.getPayment(), dayPayment.getPayment());
                break;
            case "3":
                setResultVO(goalsResultVO, baseBusinessGoalsEnum, resultVO, monthPayment.getRevenue(), weekPayment.getRevenue(), dayPayment.getRevenue());
                break;
            default:
                ArgumentAssert.isTrue(false, "请选择正确的对比类型");
                break;
        }
        return resultVO;
    }

    /**
     * 设置返回值
     *
     * @param resultVO              返回值
     * @param goalsResultVO         营业目标
     * @param baseBusinessGoalsEnum 营业目标枚举
     * @param month                 月营业
     * @param week                  周营业
     * @param day                   日营业
     */
    private static void setResultVO(BaseBusinessGoalsResultVO goalsResultVO, BaseBusinessGoalsEnum baseBusinessGoalsEnum, GoalsAchievementResultVO resultVO,
                                    BigDecimal month, BigDecimal week, BigDecimal day) {
        // 月
        resultVO.setCumulativeRevenue(month);
        resultVO.setCumulativeTarget(goalsResultVO.getMonthAmount());
        resultVO.setCumulativeTargetDiff(goalsResultVO.getMonthAmount().subtract(resultVO.getCumulativeRevenue()));
        resultVO.setCumulativeTargetShould(goalsResultVO.getMonthAmount().divide(BigDecimal.valueOf(resultVO.getAchievementDays()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(resultVO.getAchievementDaysPassed())).setScale(2, RoundingMode.HALF_UP));
        if (resultVO.getCumulativeTarget().compareTo(BigDecimal.ZERO) != 0) {
            resultVO.setAchievementRate(resultVO.getCumulativeRevenue().divide(resultVO.getCumulativeTarget(), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
        } else {
            resultVO.setAchievementRate(new BigDecimal("100.00"));
        }
        resultVO.setAchievementRateDiff(resultVO.getAchievementRate().subtract(resultVO.getAchievementTimeRatio()).setScale(2, RoundingMode.HALF_UP));
        // 周
        resultVO.setCumulativeRevenueWeek(week);
        switch (baseBusinessGoalsEnum) {
            case WEEK:
                resultVO.setCumulativeTargetWeek((Objects.nonNull(goalsResultVO.getMonday()) ? goalsResultVO.getMonday() : BigDecimal.ZERO)
                        .add(Objects.nonNull(goalsResultVO.getTuesday()) ? goalsResultVO.getTuesday() : BigDecimal.ZERO)
                        .add(Objects.nonNull(goalsResultVO.getWednesday()) ? goalsResultVO.getWednesday() : BigDecimal.ZERO)
                        .add(Objects.nonNull(goalsResultVO.getThursday()) ? goalsResultVO.getThursday() : BigDecimal.ZERO)
                        .add(Objects.nonNull(goalsResultVO.getFriday()) ? goalsResultVO.getFriday() : BigDecimal.ZERO)
                        .add(Objects.nonNull(goalsResultVO.getSaturday()) ? goalsResultVO.getSaturday() : BigDecimal.ZERO)
                        .add(Objects.nonNull(goalsResultVO.getSunday()) ? goalsResultVO.getSunday() : BigDecimal.ZERO));
                switch (DateUtil.dayOfWeek(DateUtil.date())) {
                    case 1:
                        resultVO.setCumulativeTargetDay(goalsResultVO.getMonday());
                        break;
                    case 2:
                        resultVO.setCumulativeTargetDay(goalsResultVO.getTuesday());
                        break;
                    case 3:
                        resultVO.setCumulativeTargetDay(goalsResultVO.getWednesday());
                        break;
                    case 4:
                        resultVO.setCumulativeTargetDay(goalsResultVO.getThursday());
                        break;
                    case 5:
                        resultVO.setCumulativeTargetDay(goalsResultVO.getFriday());
                        break;
                    case 6:
                        resultVO.setCumulativeTargetDay(goalsResultVO.getSaturday());
                        break;
                    case 7:
                        resultVO.setCumulativeTargetDay(goalsResultVO.getSunday());
                        break;
                    default:
                        break;
                }
                break;
            case WORK:
                resultVO.setCumulativeTargetWeek((Objects.nonNull(goalsResultVO.getWorkday()) ? goalsResultVO.getWorkday() : BigDecimal.ZERO)
                        .multiply(BigDecimal.valueOf(5)).add((Objects.nonNull(goalsResultVO.getWeekend()) ? goalsResultVO.getWeekend() : BigDecimal.ZERO).multiply(BigDecimal.valueOf(2))));
                switch (DateUtil.dayOfWeek(DateUtil.date())) {
                    case 1:
                    case 2:
                    case 3:
                    case 4:
                    case 5:
                        resultVO.setCumulativeTargetDay(goalsResultVO.getWorkday());
                        break;
                    case 6:
                    case 7:
                        resultVO.setCumulativeTargetDay(goalsResultVO.getWeekend());
                        break;
                    default:
                        break;
                }
                break;
            default:
                break;
        }
        // 周差值
        resultVO.setCumulativeTargetWeekDiff(resultVO.getCumulativeTargetWeek().subtract(resultVO.getCumulativeRevenueWeek()));
        // 周达成率
        if (resultVO.getCumulativeTargetWeek().compareTo(BigDecimal.ZERO) != 0) {
            resultVO.setWeekAchievementRate(resultVO.getCumulativeRevenueWeek().divide(resultVO.getCumulativeTargetWeek(), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
        } else {
            resultVO.setWeekAchievementRate(new BigDecimal("100.00"));
        }

        // 日
        resultVO.setCumulativeRevenueDay(day);
        // 日差值
        resultVO.setCumulativeTargetDayDiff(resultVO.getCumulativeTargetDay().subtract(resultVO.getCumulativeRevenueDay()));
        // 日达成率
        if(resultVO.getCumulativeTargetDay().compareTo(BigDecimal.ZERO) != 0) {
            resultVO.setDayAchievementRate(resultVO.getCumulativeRevenueDay().divide(resultVO.getCumulativeTargetDay(), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP));
        } else {
            resultVO.setDayAchievementRate(new BigDecimal("100.00"));
        }

    }
}

