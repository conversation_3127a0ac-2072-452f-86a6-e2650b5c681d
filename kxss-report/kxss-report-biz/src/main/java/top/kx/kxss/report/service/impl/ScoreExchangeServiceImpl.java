package top.kx.kxss.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.vo.query.warehouse.BaseWarehousePageQuery;
import top.kx.kxss.base.vo.result.warehouse.BaseWarehouseResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.report.mapper.ScoreExchangeMapper;
import top.kx.kxss.report.query.ScoreExchangeQuery;
import top.kx.kxss.report.service.ScoreExchangeService;
import top.kx.kxss.report.vo.ScoreExchangeResultVO;
import top.kx.kxss.warehouse.BaseWarehouseApi;
import top.kx.kxss.wxapp.api.custom.CustomApi;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.ColumnVO;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 利润销售API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class ScoreExchangeServiceImpl implements ScoreExchangeService {

    @Autowired
    private CustomApi customApi;
    @Autowired
    private ScoreExchangeMapper scoreExchangeMapper;
    @Autowired
    private BaseWarehouseApi baseWarehouseApi;


    @Override
    public Map<String, Object> page(PageParams<ScoreExchangeQuery> params) {
        ScoreExchangeQuery query = params.getModel();
        setDate(query);
        params.setSort("");
        params.setOrder("");
        IPage<ScoreExchangeResultVO> page = scoreExchangeMapper.page(params.buildPage(ScoreExchangeResultVO.class), query);
        IPage<Map> pageList = BeanPlusUtil.toBeanPage(page, Map.class);
        List<Map> resultVOList = Lists.newArrayList();
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("createdTime").label("出库时间").width(250).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("categoryName").label("分类").width(250).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("名称").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("measuringUnit").label("单位").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("数量").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("totalCostPrice").label("利润").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("warehouseName").label("仓库").width(180).emptyString("-").fixed(false).build()
        );
        if (CollUtil.isEmpty(page.getRecords())) {
            pageList.setRecords(resultVOList);
            Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
            objectMap.put("columnList", columnVOList);
            return objectMap;
        }
        setWarehouse(page.getRecords());
        pageList.setRecords(BeanPlusUtil.toBeanList(page.getRecords(), Map.class));
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public ScoreExchangeResultVO sum(ScoreExchangeQuery params) {
        setDate(params);
        return scoreExchangeMapper.sum(params);
    }

    @Override
    public List<ScoreExchangeResultVO> list(ScoreExchangeQuery params) {
        setDate(params);
        List<ScoreExchangeResultVO> list = scoreExchangeMapper.list(params);
        if (CollUtil.isEmpty(list)) {
            return list;
        }
        setWarehouse(list);
        return list;
    }

    private void setWarehouse(List<ScoreExchangeResultVO> list) {
        R<List<BaseWarehouseResultVO>> listR = baseWarehouseApi.query(new BaseWarehousePageQuery());
        List<BaseWarehouseResultVO> warehouseList = listR.getData();
        Map<Long, BaseWarehouseResultVO> warehouseMap = warehouseList.stream().collect(Collectors.toMap(BaseWarehouseResultVO::getId, Function.identity()));
        list.forEach(s-> {
            if (Objects.nonNull(s.getWarehouseId()) && warehouseMap.containsKey(s.getWarehouseId())) {
                s.setWarehouseName(warehouseMap.get(s.getWarehouseId()).getName());
            }
        });
    }


    private void setDate(ScoreExchangeQuery params) {
        R<DataOverviewQuery> storeTime = customApi.getStoreTime(params);
        DataOverviewQuery storeTimeData = storeTime.getData();
        params.setStartDate(storeTimeData.getStartDate());
        params.setEndDate(storeTimeData.getEndDate());
    }
}

