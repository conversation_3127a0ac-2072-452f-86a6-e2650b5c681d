package top.kx.kxss.report.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.vo.result.cash.AmountResultVO;
import top.kx.kxss.report.vo.result.cash.OrderFreeResultVO;
import top.kx.kxss.pos.vo.order.OrderResultVO;
import top.kx.kxss.report.query.CashFreeQuery;
import top.kx.kxss.report.vo.PosCashDetailsResultVO;
import top.kx.kxss.report.vo.PosCashNoPayDetailsResultVO;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.query.statistics.PosCashDetailsQuery;
import top.kx.kxss.wxapp.vo.query.statistics.ThailOverviewQuery;

import java.util.List;
import java.util.Map;

/**
 * API
 *
 * <AUTHOR>
 */
public interface PosCashService {


    AmountResultVO selectOneAmount(DataOverviewQuery query);

    /**
     * pos_cash 查询
     * @param wrapper
     * @return
     */
    List<AmountResultVO> selectByPayType(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    Map<String, Object> posCashDetails(PageParams<PosCashDetailsQuery> params);

    List<PosCashDetailsResultVO> posCashDetailsList(PosCashDetailsQuery params);


    Map<String, Object> posCashDetailsSum(PosCashDetailsQuery params);

    Map<String, Object> noPayPage(PageParams<DataOverviewQuery> params);

    PosCashNoPayDetailsResultVO noPaySum(DataOverviewQuery params);

    List<PosCashNoPayDetailsResultVO> noPayList(DataOverviewQuery params);


    IPage<OrderResultVO> thailPage(PageParams<ThailOverviewQuery> query);


    Map<String, Object> freePage(PageParams<CashFreeQuery> query);

    OrderFreeResultVO freeSum(CashFreeQuery query);

    List<OrderFreeResultVO> freeList(CashFreeQuery query);
}
