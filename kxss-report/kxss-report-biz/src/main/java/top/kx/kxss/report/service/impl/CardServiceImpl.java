package top.kx.kxss.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.member.card.MemberCardChange;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.report.mapper.CardMapper;
import top.kx.kxss.report.query.CardConsumeQuery;
import top.kx.kxss.report.service.CardService;
import top.kx.kxss.report.service.common.CardCommonCtrl;
import top.kx.kxss.report.vo.MemberCardChangeResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.ColumnVO;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 套餐销售API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class CardServiceImpl extends CardCommonCtrl implements CardService {

    private final CardMapper  cardMapper;
    private final EchoService echoService;

    @Override
    public Map<String, Object> memberCardChangePage(PageParams<CardConsumeQuery> params) {
        CardConsumeQuery query = params.getModel();
        params.setSort("");
        params.setOrder("");
        QueryWrapper<MemberCardChange> wrapper = memberCardChangeWrapper(query);
        IPage<MemberCardChangeResultVO> page = cardMapper.memberCardChangePage(params.buildPage(MemberCardChangeResultVO.class), wrapper);
        echoService.action(page.getRecords());
        initMemberCardResult(page.getRecords());
        IPage<Map> pageList = BeanPlusUtil.toBeanPage(page, Map.class);
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("memberCardId").label("会员卡编号").width(250).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("typeDesc").label("卡类型").width(250).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("卡名称").width(250).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("consumeTime").label("使用时间").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeTime").label("结账时间").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("code").label("关联订单号").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orderSourceDesc").label("订单来源").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("memberName").label("会员名称").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("mobile").label("会员手机号").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("discountType").label("优惠类型").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("耗卡金额").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("balance").label("当前余额").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("lastBalance").label("上次结余").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("project").label("划卡项目").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("explainDesc").label("划卡说明").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("employeeName").label("操作员工").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店").width(250).emptyString("-").fixed(false).build()
        );
        if (CollUtil.isEmpty(page.getRecords())) {
            pageList.setRecords(Lists.newArrayList());
            Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
            objectMap.put("columnList", columnVOList);
            return objectMap;
        }
        pageList.setRecords(BeanPlusUtil.toBeanList(page.getRecords(), Map.class));
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public MemberCardChangeResultVO memberCardChangeSum(CardConsumeQuery params) {
        return cardMapper.memberCardChangeSum(memberCardChangeWrapper(params));
    }

    @Override
    public List<MemberCardChangeResultVO> memberCardChangeList(CardConsumeQuery params) {
        List<MemberCardChangeResultVO> couponResultVOList = cardMapper.memberCardChangeList(memberCardChangeWrapper(params));
        echoService.action(couponResultVOList);
        initMemberCardResult(couponResultVOList);
        return couponResultVOList;
    }

    private static void initMemberCardResult(List<MemberCardChangeResultVO> couponResultVOList) {
        couponResultVOList.forEach(item -> {
            Map<String, Object> echoMap = item.getEchoMap();
            if (Objects.nonNull(echoMap.get("type"))) {
                item.setTypeDesc(echoMap.get("type").toString());
            }
            if (Objects.nonNull(echoMap.get("orderSource"))) {
                item.setOrderSourceDesc(echoMap.get("orderSource").toString());
            }
            if (Objects.nonNull(echoMap.get("orgId"))) {
                item.setOrg(echoMap.get("orgId").toString());
            }
            if (Objects.nonNull(echoMap.get("employeeId"))) {
                item.setEmployeeName(echoMap.get("employeeId").toString());
            }
        });
    }
}

