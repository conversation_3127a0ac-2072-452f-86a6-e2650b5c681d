package top.kx.kxss.report.service.reconciliation;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.base.vo.result.payment.BankCardInfoResultVO;
import top.kx.kxss.base.vo.result.payment.BaseBankCardInfoResultVO;
import top.kx.kxss.pay.vo.query.DayReconciliationQuery;
import top.kx.kxss.pay.vo.result.IsvReconciliationInfoResultVO;
import top.kx.kxss.report.entity.reconciliation.Reconciliation;
import top.kx.kxss.report.query.reconciliation.StatisticReconciliationQuery;
import top.kx.kxss.report.vo.query.reconciliation.ReconciliationPageQuery;
import top.kx.kxss.report.vo.result.reconciliation.ReconciliationResultVO;
import top.kx.kxss.report.vo.result.reconciliation.StatisticReconciliationResultVO;
import top.kx.kxss.report.vo.save.reconciliation.ReconciliationSaveVO;
import top.kx.kxss.report.vo.update.reconciliation.ReconciliationUpdateVO;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 商户对账单
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-01 15:55:00
 * @create [2025-07-01 15:55:00] [dou] [代码生成器生成]
 */
public interface ReconciliationService extends SuperService<Long, Reconciliation, ReconciliationSaveVO,
    ReconciliationUpdateVO, ReconciliationPageQuery, ReconciliationResultVO> {

    boolean remove(LbQueryWrap<Reconciliation> eq);

    List<BaseBankCardInfoResultVO> bankCardList();

    List<BankCardInfoResultVO> payChannel();

    StatisticReconciliationResultVO statistic(StatisticReconciliationQuery query);

    IsvReconciliationInfoResultVO reconciliationInfo(Long bankCardId);

    List<top.kx.kxss.pay.vo.result.ReconciliationResultVO> dateList(DayReconciliationQuery query);


}


