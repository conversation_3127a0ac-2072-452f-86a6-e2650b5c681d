package top.kx.kxss.report.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.kxss.base.entity.member.card.MemberCardChange;
import top.kx.kxss.report.vo.MemberCardChangeResultVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 *
 * </p>
 *
 * <AUTHOR>
 */
@Repository
public interface CardMapper {

    IPage<MemberCardChangeResultVO> memberCardChangePage(IPage<MemberCardChangeResultVO> page, @Param(Constants.WRAPPER) QueryWrapper<MemberCardChange> wrap);

    MemberCardChangeResultVO memberCardChangeSum(@Param(Constants.WRAPPER) QueryWrapper<MemberCardChange> wrap);

    List<MemberCardChangeResultVO> memberCardChangeList(@Param(Constants.WRAPPER) QueryWrapper<MemberCardChange> wrap);

}


