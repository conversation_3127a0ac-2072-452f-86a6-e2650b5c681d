package top.kx.kxss.report.rabbitmq;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.jackson.JsonUtil;
import top.kx.kxss.app.query.YearEndConsumeQuery;
import top.kx.kxss.common.constant.RabbitMqConstant;
import top.kx.kxss.model.enumeration.system.DefTaskStatusEnum;
import top.kx.kxss.report.service.yearend.YearEndService;
import top.kx.kxss.system.api.DefTaskApi;
import top.kx.kxss.system.entity.system.DefTask;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
@RabbitListener(queues = RabbitMqConstant.YEAR_END)
public class YearEndConsume {

    @Autowired
    private YearEndService yearEndService;
    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private DefTaskApi defTaskApi;

    @RabbitHandler
    public void process(String msg) {
        log.info("YearEnd年终总结消费者收到消息: {}", msg);
        YearEndConsumeQuery consumeQuery =
                JsonUtil.parse(msg, YearEndConsumeQuery.class);
        boolean lock = false;
        DefTask defTask = null;
        try {
            ContextUtil.setDefTenantId();
            defTask = defTaskApi.getById(consumeQuery.getTaskId());
            if (defTask == null) {
                return;
            }
            if (defTask.getStatus().equals(DefTaskStatusEnum.SUCCESS.getCode())
                    || defTask.getStatus().equals(DefTaskStatusEnum.ERROR.getCode())) {
                return;
            }

            lock = distributedLock.lock(consumeQuery.getTaskId() + "_year_end_task", 0);
            if (!lock) {
                return;
            }
            ContextUtil.setTenantBasePoolName(consumeQuery.getTenantId());
            ContextUtil.setCurrentCompanyId(consumeQuery.getOrgId());
            ContextUtil.setTenantId(consumeQuery.getTenantId());
            yearEndService.calcData();
            defTask.setCompletionTime(LocalDateTime.now());
            defTask.setStatus(DefTaskStatusEnum.SUCCESS.getCode());
        } catch (Exception e) {
            defTask.setStatus(DefTaskStatusEnum.ERROR.getCode());
            defTask.setCompletionTime(LocalDateTime.now());
            if (StrUtil.isNotBlank(e.getMessage())) {
                defTask.setDesc(e.getMessage().trim().length() > 200 ? e.getMessage().trim().substring(0, 200) : e.getMessage().trim());
            }
            log.error("操作失败", e);
        } finally {
            ContextUtil.remove();
            if (defTask != null) {
                defTaskApi.updateById(defTask);
            }
            if (lock) {
                distributedLock.releaseLock(consumeQuery.getTaskId() + "_year_end_task");
            }
        }
    }

}
