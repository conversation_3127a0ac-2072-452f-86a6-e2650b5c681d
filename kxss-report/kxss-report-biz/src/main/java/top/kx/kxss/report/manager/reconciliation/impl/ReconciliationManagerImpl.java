package top.kx.kxss.report.manager.reconciliation.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.report.entity.reconciliation.Reconciliation;
import top.kx.kxss.report.manager.reconciliation.ReconciliationManager;
import top.kx.kxss.report.mapper.reconciliation.ReconciliationMapper;
import top.kx.kxss.report.query.reconciliation.StatisticReconciliationQuery;
import top.kx.kxss.report.vo.result.reconciliation.StatisticReconciliationResultVO;

/**
 * <p>
 * 通用业务实现类
 * 商户对账单
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-01 15:55:00
 * @create [2025-07-01 15:55:00] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ReconciliationManagerImpl extends SuperManagerImpl<ReconciliationMapper, Reconciliation> implements ReconciliationManager {
}


