package top.kx.kxss.report.service.common;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.base.entity.coupon.BaseCouponInfo;
import top.kx.kxss.base.entity.member.coupon.MemberCoupon;
import top.kx.kxss.report.query.CouponInfoQuery;
import top.kx.kxss.report.query.CouponIssueQuery;

import java.util.Objects;

/**
 * 商品销售API
 *
 * <AUTHOR>
 */
public class CouponCommonCtrl extends PosCashCommonCtrl {


    /**
     * 基础查询条件
     *
     * @param query
     * @return
     */
    public QueryWrapper<BaseCouponInfo> statisCouponWrapper(CouponInfoQuery query) {
        QueryWrapper<BaseCouponInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bci.delete_flag", 0);
        queryWrapper.eq("mc.delete_flag", 0);
        queryWrapper.eq("bci.created_org_id", ContextUtil.getCurrentCompanyId());
        queryWrapper.eq(StringUtils.isNotBlank(query.getType()), "bci.type_", query.getType());
        queryWrapper.like(StringUtils.isNotBlank(query.getKeyword()), "bci.name", query.getKeyword());
        if (Objects.nonNull(query.getIssueStartTime()) && Objects.nonNull(query.getIssueEndTime())) {
            queryWrapper.between("mc.created_time", query.getIssueStartTime(), query.getIssueEndTime());
        } else if (Objects.nonNull(query.getIssueStartTime())) {
            queryWrapper.ge("mc.created_time", query.getIssueStartTime());
        } else if (Objects.nonNull(query.getIssueEndTime())) {
            queryWrapper.le("mc.created_time", query.getIssueEndTime());
        }
        return queryWrapper;
    }

    /**
     * memberCoupon
     */
    public QueryWrapper<MemberCoupon> memberCouponWrapper(CouponIssueQuery query) {
        QueryWrapper<MemberCoupon> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mc.delete_flag", 0);
        queryWrapper.eq("pc.org_id", ContextUtil.getCurrentCompanyId());
        queryWrapper.eq(StringUtils.isNotBlank(query.getType()), "mc.type_", query.getType());
        if (Objects.nonNull(query.getIssueStartTime()) && Objects.nonNull(query.getIssueEndTime())) {
            queryWrapper.between("mc.created_time", query.getIssueStartTime(), query.getIssueEndTime());
        } else if (Objects.nonNull(query.getIssueStartTime())) {
            queryWrapper.ge("mc.created_time", query.getIssueStartTime());
        } else if (Objects.nonNull(query.getIssueEndTime())) {
            queryWrapper.le("mc.created_time", query.getIssueEndTime());
        }
        if (Objects.nonNull(query.getCompleteStartTime()) && Objects.nonNull(query.getCompleteEndTime())) {
            queryWrapper.between("pc.complete_time", query.getCompleteStartTime(), query.getCompleteEndTime());
        } else if (Objects.nonNull(query.getCompleteStartTime())) {
            queryWrapper.ge("pc.complete_time", query.getCompleteStartTime());
        } else if (Objects.nonNull(query.getCompleteEndTime())) {
            queryWrapper.le("pc.complete_time", query.getCompleteEndTime());
        }
        // 券码
        queryWrapper.like(StringUtils.isNotBlank(query.getMemberCode()), "mc.code", query.getMemberCode());
        // 订单号
        queryWrapper.like(StringUtils.isNotBlank(query.getCode()), "pc.code", query.getCode());
        // 关键词
        if (StringUtils.isNotBlank(query.getKeyword())) {
            queryWrapper.and(i -> i.like("mc.code", query.getKeyword())
                    .or().like("pc.code", query.getKeyword())
                    .or().like("mc.name", query.getKeyword())
            );
        }
        // 手机号
        if (StringUtils.isNotBlank(query.getMobile())) {
            //queryWrapper.exists("select 1 from member_info where mobile = {0}", query.getMobile());
            queryWrapper.exists("select 1 from member_info where mobile like '%" + query.getMobile() + "%'");
        }
        return queryWrapper;
    }


}

