package top.kx.kxss.report.service;

import top.kx.basic.base.request.PageParams;
import top.kx.kxss.report.query.CouponInfoQuery;
import top.kx.kxss.report.query.CouponIssueQuery;
import top.kx.kxss.report.vo.MemberCouponResultVO;
import top.kx.kxss.report.vo.StatisCouponResultVO;

import java.util.List;
import java.util.Map;

/**
 * API
 *
 * <AUTHOR>
 */
public interface CouponService {

    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    Map<String, Object> statisPage(PageParams<CouponInfoQuery> params);

    /**
     * 统计查询
     *
     * @param params
     * @return
     */
    StatisCouponResultVO statisSum(CouponInfoQuery params);

    /**
     * 列表查询
     *
     * @param params
     * @return
     */
    List<StatisCouponResultVO> statisList(CouponInfoQuery params);


    Map<String, Object> memberCouponPage(PageParams<CouponIssueQuery> params);

    MemberCouponResultVO memberCouponSum(CouponIssueQuery params);

    List<MemberCouponResultVO> memberCouponList(CouponIssueQuery params);


}
