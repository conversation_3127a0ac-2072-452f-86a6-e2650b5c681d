package top.kx.kxss.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.employee.EmployeeApi;
import top.kx.kxss.report.mapper.GroupBuyMapper;
import top.kx.kxss.report.query.GroupBuyQuery;
import top.kx.kxss.report.service.GroupBuyService;
import top.kx.kxss.report.service.common.GroupBuyCommonCtrl;
import top.kx.kxss.report.vo.GroupBuyResultVO;
import top.kx.kxss.wxapp.api.custom.CustomApi;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.ColumnVO;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 套餐销售API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class GroupBuyServiceImpl extends GroupBuyCommonCtrl implements GroupBuyService {

    private final GroupBuyMapper groupBuyMapper;
    private final CustomApi customApi;
    private final EmployeeApi employeeApi;

    @Override
    public Map<String, Object> page(PageParams<GroupBuyQuery> params) {
        GroupBuyQuery query = params.getModel();
        setDate(query);
        params.setSort("");
        params.setOrder("");
        query.setOrgId(ContextUtil.getCurrentCompanyId());
        IPage<GroupBuyResultVO> page = groupBuyMapper.page(params.buildPage(GroupBuyResultVO.class), query);
        IPage<Map> pageList = BeanPlusUtil.toBeanPage(page, Map.class);
        List<Map> resultVOList = Lists.newArrayList();
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("securitiesNumber").label("团购券号").width(250).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("prepareTime").label("验券时间").width(250).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("groupBuy").label("团购名称").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("套餐核销金额").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("feeAmount").label("预估手续费").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payment").label("预估到账收入").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createdEmp").label("操作员工").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("code").label("关联订单").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeTime").label("订单完成时间").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type").label("类型").width(180).emptyString("-").fixed(false).build()
        );
        if (CollUtil.isEmpty(page.getRecords())) {
            pageList.setRecords(resultVOList);
            Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
            objectMap.put("columnList", columnVOList);
            return objectMap;
        }
        setCreatedEmp(page.getRecords());
        pageList.setRecords(BeanPlusUtil.toBeanList(page.getRecords(), Map.class));
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public GroupBuyResultVO sum(GroupBuyQuery params) {
        setDate(params);
        params.setOrgId(ContextUtil.getCurrentCompanyId());
        return groupBuyMapper.sum(params);
    }

    @Override
    public List<GroupBuyResultVO> list(GroupBuyQuery params) {
        setDate(params);
        params.setOrgId(ContextUtil.getCurrentCompanyId());
        List<GroupBuyResultVO> list = groupBuyMapper.list(params);
        if (CollUtil.isEmpty(list)) {
            return list;
        }
        setCreatedEmp(list);
        return list;
    }


    private void setCreatedEmp(List<GroupBuyResultVO> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> userIds = list.stream().map(GroupBuyResultVO::getCreatedBy).distinct().collect(Collectors.toList());
        if (CollUtil.isEmpty(userIds)) {
            return;
        }
        R<List<BaseEmployeeResultVO>> listR = employeeApi.findListByUserIds(userIds);
        List<BaseEmployeeResultVO> employeeResultVOList = listR.getData();
        Map<Long, BaseEmployeeResultVO> employeeMap = employeeResultVOList.stream().collect(Collectors.toMap(BaseEmployeeResultVO::getUserId, Function.identity()));
        list.forEach(s-> {
            if (Objects.nonNull(s.getCreatedBy()) && employeeMap.containsKey(s.getCreatedBy())) {
                s.setCreatedEmp(employeeMap.get(s.getCreatedBy()).getRealName());
            }
        });
    }


    private void setDate(GroupBuyQuery params) {
        if (StringUtils.isNotBlank(params.getStartConsumeTime()) && StringUtils.isNotBlank(params.getEndConsumeTime())) {
            DataOverviewQuery query = new DataOverviewQuery();
            query.setStartDate(params.getStartConsumeTime());
            query.setEndDate(params.getEndConsumeTime());
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(query);
            DataOverviewQuery storeTimeData = storeTime.getData();
            params.setStartConsumeTime(storeTimeData.getStartDate());
            params.setEndConsumeTime(storeTimeData.getEndDate());
        }
        if (StringUtils.isNotBlank(params.getStartCompleteTime()) && StringUtils.isNotBlank(params.getEndCompleteTime())) {
            DataOverviewQuery query = new DataOverviewQuery();
            query.setStartDate(params.getStartCompleteTime());
            query.setEndDate(params.getEndCompleteTime());
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(query);
            DataOverviewQuery storeTimeData = storeTime.getData();
            params.setStartCompleteTime(storeTimeData.getStartDate());
            params.setEndCompleteTime(storeTimeData.getEndDate());
        }
    }
}

