package top.kx.kxss.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.report.SalesTypeEnum;
import top.kx.kxss.report.mapper.ThailSalesMapper;
import top.kx.kxss.report.query.ProductSalesQuery;
import top.kx.kxss.report.service.ThailSalesService;
import top.kx.kxss.report.service.common.CommonCtrl;
import top.kx.kxss.report.vo.SalesDetailResultVO;

import java.util.List;

/**
 * 套餐销售API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class ThailSalesServiceImpl extends CommonCtrl implements ThailSalesService {

    private final ThailSalesMapper thailSalesMapper;

    @Override
    public List<SalesDetailResultVO> list(ProductSalesQuery query) {
        QueryWrapper queryWrapper = queryWrapper(query);
        queryWrapper.like(StrUtil.isNotBlank(query.getName()), "pro.thail_name", query.getName());
        queryWrapper.in(CollUtil.isNotEmpty(query.getCategoryIds()), "b.category_id", query.getCategoryIds());
        queryWrapper.like(StrUtil.isNotBlank(query.getCategoryName()), "c.name", query.getCategoryName());
        groupBuy(queryWrapper, query.getSalesType());
        return thailSalesMapper.queryList(queryWrapper);
    }

    private void groupBuy(QueryWrapper queryWrapper, String salesType) {
        salesType = StrUtil.isNotBlank(salesType)
                ? salesType : SalesTypeEnum.ITEM_THAIL_DETAIL.getCode();
        switch (SalesTypeEnum.get(salesType)) {
            case ITEM:
            case THAIL_DETAIL:
            case ITEM_THAIL_DETAIL:
                queryWrapper.eq("pro.thail_id", "-1");
                queryWrapper.groupBy("pro.thail_id");
                break;
            case ITEM_THAIL:
            case THAIL:
                queryWrapper.groupBy("pro.thail_id");
                break;
        }

    }
}

