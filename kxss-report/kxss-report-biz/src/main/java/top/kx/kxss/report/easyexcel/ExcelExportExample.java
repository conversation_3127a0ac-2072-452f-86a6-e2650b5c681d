package top.kx.kxss.report.easyexcel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * EasyExcel 颜色拦截器使用示例
 *
 * <AUTHOR>
 * @date 2024/12/28
 */
@Slf4j
public class ExcelExportExample {

    /**
     * 示例数据对象
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class OrderExportData {
        @ExcelProperty(value = "订单号", index = 0)
        private String orderNo;

        @ExcelProperty(value = "订单状态", index = 1)
        private String status;

        @ExcelProperty(value = "订单金额", index = 2)
        private BigDecimal amount;

        @ExcelProperty(value = "完成率", index = 3)
        private Double completionRate;

        @ExcelProperty(value = "优先级", index = 4)
        private String priority;

        @ExcelProperty(value = "是否VIP", index = 5)
        private String isVip;

        @ExcelProperty(value = "备注", index = 6)
        private String remark;
    }

    /**
     * 基础使用示例 - 根据单元格值设置颜色
     */
    public static void basicCellColorExample(HttpServletResponse response) throws IOException {
        // 创建颜色处理器
        CellColorHandler colorHandler = new CellColorHandlerBuilder()
            // 订单状态列（第1列）颜色设置
            .cellStringEquals(1, "成功", CellColorHandlerBuilder.Colors.GREEN)
            .cellStringEquals(1, "失败", CellColorHandlerBuilder.Colors.RED)
            .cellStringEquals(1, "处理中", CellColorHandlerBuilder.Colors.YELLOW)

            // 金额列（第2列）颜色设置
            .cellNumberGreaterThan(2, 1000.0, CellColorHandlerBuilder.Colors.GREEN)
            .cellNumberLessThan(2, 100.0, CellColorHandlerBuilder.Colors.RED)

            // 完成率列（第3列）颜色设置
            .cellNumberGreaterEquals(3, 80.0, CellColorHandlerBuilder.Colors.GREEN)
            .cellNumberLessThan(3, 60.0, CellColorHandlerBuilder.Colors.RED)

            .build();

        // 准备数据
        List<OrderExportData> data = createSampleData();

        // 导出Excel
        exportExcel(response, "基础单元格颜色示例.xlsx", data, colorHandler);
    }

    /**
     * 整行颜色示例 - 根据某列值设置整行颜色
     */
    public static void rowColorExample(HttpServletResponse response) throws IOException {
        // 创建颜色处理器
        CellColorHandler colorHandler = new CellColorHandlerBuilder()
            // 根据状态列（第1列）设置整行颜色
            .rowStringEquals(1, "成功", CellColorHandlerBuilder.Colors.LIGHT_GREEN)
            .rowStringEquals(1, "失败", CellColorHandlerBuilder.Colors.PINK)
            .rowStringEquals(1, "异常", CellColorHandlerBuilder.Colors.LIGHT_YELLOW)

            // 根据金额列（第2列）设置整行颜色
            .rowNumberGreaterThan(2, 5000.0, CellColorHandlerBuilder.Colors.LIGHT_BLUE)
            .rowNumberLessThan(2, 0.0, CellColorHandlerBuilder.Colors.GREY_25_PERCENT)

            .build();

        // 准备数据
        List<OrderExportData> data = createSampleData();

        // 导出Excel
        exportExcel(response, "整行颜色示例.xlsx", data, colorHandler);
    }

    /**
     * 混合使用示例 - 单元格颜色和整行颜色混合使用
     */
    public static void mixedColorExample(HttpServletResponse response) throws IOException {
        // 创建颜色处理器
        CellColorHandler colorHandler = new CellColorHandlerBuilder()
            // 单元格颜色规则
            .cellStringEquals(1, "失败", CellColorHandlerBuilder.Colors.RED)
            .cellIsNull(6, CellColorHandlerBuilder.Colors.GREY_40_PERCENT)

            // 整行颜色规则（优先级高于单元格颜色）
            .rowStringEquals(5, "是", CellColorHandlerBuilder.Colors.LIGHT_GREEN)  // VIP客户整行浅绿色
            .rowStringEquals(4, "高", CellColorHandlerBuilder.Colors.LIGHT_YELLOW) // 高优先级整行浅黄色

            .build();

        // 准备数据
        List<OrderExportData> data = createSampleData();

        // 导出Excel
        exportExcel(response, "混合颜色示例.xlsx", data, colorHandler);
    }

    /**
     * 业务场景快捷方法示例
     */
    public static void businessScenarioExample(HttpServletResponse response) throws IOException {
        // 创建颜色处理器
        CellColorHandler colorHandler = new CellColorHandlerBuilder()
            // 使用预定义的业务场景方法
            .statusColumn(1)        // 状态列
            .amountColumn(2)        // 金额列
            .percentageColumn(3)    // 百分比列
            .priorityColumn(4)      // 优先级列
            .booleanColumn(5)       // 是否列

            // 根据状态设置整行颜色
            .statusRowColor(1)

            .build();

        // 准备数据
        List<OrderExportData> data = createSampleData();

        // 导出Excel
        exportExcel(response, "业务场景示例.xlsx", data, colorHandler);
    }

    /**
     * 自定义样式配置示例
     */
    public static void customStyleExample(HttpServletResponse response) throws IOException {
        // 创建自定义样式配置
        CellColorHandler.StyleConfig styleConfig = new CellColorHandler.StyleConfig();
        styleConfig.setFontName("微软雅黑");
        styleConfig.setHeaderFontSize((short) 16);
        styleConfig.setDataFontSize((short) 11);
        styleConfig.setHeaderRowHeight((short) (30 * 20));
        styleConfig.setDataRowHeight((short) (25 * 20));
        styleConfig.setColumnWidth(20 * 256);

        // 创建颜色处理器
        CellColorHandler colorHandler = new CellColorHandlerBuilder(styleConfig)
            .statusColumn(1)
            .amountColumn(2)
            .build();

        // 准备数据
        List<OrderExportData> data = createSampleData();

        // 导出Excel
        exportExcel(response, "自定义样式示例.xlsx", data, colorHandler);
    }

    /**
     * 复杂条件示例 - 使用自定义条件
     */
    public static void complexConditionExample(HttpServletResponse response) throws IOException {
        // 创建颜色处理器
        CellColorHandler colorHandler = new CellColorHandlerBuilder()
            // 自定义条件：订单号以"VIP"开头的设置为金色
            .cellCustom(0, value -> value != null && value.toString().startsWith("VIP"),
                       CellColorHandlerBuilder.Colors.YELLOW, "VIP订单")

            // 自定义条件：备注包含"紧急"的设置整行为红色
            .rowCustom(6, value -> value != null && value.toString().contains("紧急"),
                      CellColorHandlerBuilder.Colors.PINK, "紧急订单")

            // 自定义条件：金额在1000-5000之间的设置为橙色
            .cellCustom(2, value -> {
                if (value == null) return false;
                try {
                    double amount = Double.parseDouble(value.toString());
                    return amount >= 1000 && amount <= 5000;
                } catch (NumberFormatException e) {
                    return false;
                }
            }, CellColorHandlerBuilder.Colors.ORANGE, "中等金额")

            .build();

        // 准备数据
        List<OrderExportData> data = createSampleData();

        // 导出Excel
        exportExcel(response, "复杂条件示例.xlsx", data, colorHandler);
    }

    /**
     * 创建示例数据
     */
    private static List<OrderExportData> createSampleData() {
        List<OrderExportData> data = new ArrayList<>();

        data.add(new OrderExportData("ORDER001", "成功", new BigDecimal("1500.00"), 95.5, "高", "是", "正常订单"));
        data.add(new OrderExportData("ORDER002", "失败", new BigDecimal("800.00"), 45.2, "中", "否", "支付失败"));
        data.add(new OrderExportData("ORDER003", "处理中", new BigDecimal("2300.00"), 78.8, "低", "是", "处理中"));
        data.add(new OrderExportData("VIP001", "成功", new BigDecimal("5600.00"), 100.0, "高", "是", "VIP客户"));
        data.add(new OrderExportData("ORDER004", "异常", new BigDecimal("-200.00"), 0.0, "中", "否", "退款订单"));
        data.add(new OrderExportData("ORDER005", "成功", new BigDecimal("12000.00"), 88.9, "高", "是", "大额订单"));
        data.add(new OrderExportData("ORDER006", "待审核", new BigDecimal("0.00"), 50.0, "低", "否", null));
        data.add(new OrderExportData("ORDER007", "成功", new BigDecimal("3400.00"), 92.1, "中", "是", "紧急处理"));
        data.add(new OrderExportData("ORDER008", "已取消", new BigDecimal("0.00"), 0.0, "低", "否", "客户取消"));
        data.add(new OrderExportData("VIP002", "成功", new BigDecimal("8900.00"), 96.7, "高", "是", "VIP大客户"));

        return data;
    }

    /**
     * 导出Excel通用方法
     */
    private static void exportExcel(HttpServletResponse response, String fileName,
                                   List<OrderExportData> data, CellColorHandler colorHandler) throws IOException {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);

            EasyExcel.write(response.getOutputStream(), OrderExportData.class)
                    .sheet("订单数据")
                    .registerWriteHandler(colorHandler)
                    .doWrite(data);

        } catch (Exception e) {
            log.error("导出Excel失败", e);
            response.reset();
            response.setContentType("application/json");
            response.setCharacterEncoding("utf-8");
            response.getWriter().write("{\"error\":\"导出失败：" + e.getMessage() + "\"}");
        }
    }

    /**
     * 在Controller中的使用示例
     */
    /*
    @RestController
    @RequestMapping("/api/export")
    public class ExportController {

        @GetMapping("/orders/basic")
        public void exportBasicCellColor(HttpServletResponse response) throws IOException {
            ExcelExportExample.basicCellColorExample(response);
        }

        @GetMapping("/orders/row")
        public void exportRowColor(HttpServletResponse response) throws IOException {
            ExcelExportExample.rowColorExample(response);
        }

        @GetMapping("/orders/mixed")
        public void exportMixedColor(HttpServletResponse response) throws IOException {
            ExcelExportExample.mixedColorExample(response);
        }

        @GetMapping("/orders/business")
        public void exportBusinessScenario(HttpServletResponse response) throws IOException {
            ExcelExportExample.businessScenarioExample(response);
        }

        @GetMapping("/orders/custom")
        public void exportCustomStyle(HttpServletResponse response) throws IOException {
            ExcelExportExample.customStyleExample(response);
        }

        @GetMapping("/orders/complex")
        public void exportComplexCondition(HttpServletResponse response) throws IOException {
            ExcelExportExample.complexConditionExample(response);
        }
    }
    */
}
