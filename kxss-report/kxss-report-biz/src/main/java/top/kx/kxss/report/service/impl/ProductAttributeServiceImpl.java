package top.kx.kxss.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.report.mapper.AttributeMapper;
import top.kx.kxss.report.query.ProductAttributeQuery;
import top.kx.kxss.report.service.ProductAttributeService;
import top.kx.kxss.report.service.common.AttributeCommonCtrl;
import top.kx.kxss.report.service.common.PosCashCommonCtrl;
import top.kx.kxss.report.vo.GroupBuyResultVO;
import top.kx.kxss.report.vo.ProductAttributeResultVO;
import top.kx.kxss.wxapp.api.custom.CustomApi;
import top.kx.kxss.wxapp.vo.query.common.OrgIdListQuery;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.query.statistics.InventoryFlowQuery;
import top.kx.kxss.wxapp.vo.result.statistics.ColumnVO;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 利润销售API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class ProductAttributeServiceImpl extends AttributeCommonCtrl implements ProductAttributeService {

    private final CustomApi customApi;
    private final AttributeMapper attributeMapper;
    private final EchoService echoService;

    @Override
    public Map<String, Object> page(PageParams<ProductAttributeQuery> params) {
        ProductAttributeQuery model = params.getModel();
        setDate(model);
        params.setSort(null);
        params.setOrder(null);
        IPage<ProductAttributeResultVO> page = attributeMapper.productPage(params.buildPage(ProductAttributeResultVO.class), productAttributeWrapper(model));
        echoService.action(page);
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("name").label("商品名称")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("attributeName").label("属性分类")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("attributeSettingName").label("属性名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createdTime").label("出库时间")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeTime").label("结账时间")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("code").label("单号")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type").label("出库类型")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("categoryName").label("分类")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("measuringUnitName").label("单位")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("数量")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("price").label("销售单价")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("costPrice").label("合计金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("totalCostPrice").label("成本金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("profitPrice").label("利润")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createdEmp").label("操作员工")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("warehouseName").label("仓库")
                        .width(180).emptyString("-").fixed(false).build()
        );
        for (ProductAttributeResultVO record : page.getRecords()) {
            record.setCreatedEmp(Objects.nonNull(record.getEchoMap().get("createdBy")) ? record.getEchoMap().get("createdBy").toString() : "-");
            record.setCategoryName(Objects.nonNull(record.getEchoMap().get("categoryId")) ? record.getEchoMap().get("categoryId").toString() : "-");
            record.setMeasuringUnitName(Objects.nonNull(record.getEchoMap().get("measuringUnit")) ? record.getEchoMap().get("measuringUnit").toString() : "-");
            record.setWarehouseName(Objects.nonNull(record.getEchoMap().get("warehouseId")) ? record.getEchoMap().get("warehouseId").toString() : "-");
        }
        Map<String, Object> objectMap = BeanUtil.beanToMap(page);
        objectMap.put("columnList", columnVOList);
        // 添加一个报表, 商品属性报表
        // 商品名称, 属性分类, 属性名称, + 商品出库统计的报表字段
        return objectMap;
    }

    @Override
    public ProductAttributeResultVO sum(ProductAttributeQuery params) {
        setDate(params);
        return attributeMapper.productSum(productAttributeWrapper(params));
    }

    private void setDate(ProductAttributeQuery params) {
        initOrgIdList(params);
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(params);
            DataOverviewQuery storeTimeData = storeTime.getData();
            params.setStartDate(storeTimeData.getStartDate());
            params.setEndDate(storeTimeData.getEndDate());
        }
    }

    @Override
    public List<ProductAttributeResultVO> list(ProductAttributeQuery params) {
        setDate(params);
        List<ProductAttributeResultVO> list = attributeMapper.productList(productAttributeWrapper(params));
        echoService.action(list);
        for (ProductAttributeResultVO record : list) {
            record.setCreatedEmp(Objects.nonNull(record.getEchoMap().get("createdBy")) ? record.getEchoMap().get("createdBy").toString() : "-");
            record.setCategoryName(Objects.nonNull(record.getEchoMap().get("categoryId")) ? record.getEchoMap().get("categoryId").toString() : "-");
            record.setMeasuringUnitName(Objects.nonNull(record.getEchoMap().get("measuringUnit")) ? record.getEchoMap().get("measuringUnit").toString() : "-");
            record.setWarehouseName(Objects.nonNull(record.getEchoMap().get("warehouseId")) ? record.getEchoMap().get("warehouseId").toString() : "-");
        }
        return list;
    }


    public void initOrgIdList(OrgIdListQuery params) {
        if (Objects.isNull(params)) {
            params = new OrgIdListQuery();
        }
        if (CollUtil.isEmpty(params.getOrgIdList())) {
            params.setOrgIdList(Collections.singletonList(ContextUtil.getCurrentCompanyId()));
        }
    }
}

