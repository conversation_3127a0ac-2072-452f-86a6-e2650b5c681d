package top.kx.kxss.report.easyexcel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.util.StyleUtil;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Predicate;

/**
 * EasyExcel 自定义单元格颜色处理器
 * 支持：
 * 1. 根据单元格值设置单元格颜色
 * 2. 根据某行某个单元格值设置整行颜色
 *
 * <AUTHOR>
 * @date 2024/12/28
 */
@Slf4j
public class CellColorHandler implements CellWriteHandler {

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, org.apache.poi.ss.usermodel.Row row, Head head, Integer integer, Integer integer1, Boolean aBoolean) {

    }

    /**
     * 单元格颜色规则列表
     */
    private final List<CellColorRule> cellColorRules = new ArrayList<>();

    /**
     * 行颜色规则列表
     */
    private final List<RowColorRule> rowColorRules = new ArrayList<>();

    /**
     * 行数据缓存（用于整行颜色判断）
     */
    private final Map<Integer, Map<Integer, Object>> rowDataCache = new ConcurrentHashMap<>();

    /**
     * 样式配置
     */
    private StyleConfig styleConfig;

    public CellColorHandler() {
        this.styleConfig = new StyleConfig();
    }

    public CellColorHandler(StyleConfig styleConfig) {
        this.styleConfig = styleConfig != null ? styleConfig : new StyleConfig();
    }

    /**
     * 添加单元格颜色规则
     *
     * @param columnIndex 列索引
     * @param condition   条件判断函数
     * @param color       颜色索引
     * @param description 规则描述
     * @return 当前处理器实例
     */
    public CellColorHandler addCellColorRule(int columnIndex, Predicate<Object> condition, Short color, String description) {
        cellColorRules.add(new CellColorRule(columnIndex, condition, color, description));
        return this;
    }

    /**
     * 添加整行颜色规则
     *
     * @param triggerColumnIndex 触发列索引（根据此列的值判断）
     * @param condition          条件判断函数
     * @param color              颜色索引
     * @param description        规则描述
     * @return 当前处理器实例
     */
    public CellColorHandler addRowColorRule(int triggerColumnIndex, Predicate<Object> condition, Short color, String description) {
        rowColorRules.add(new RowColorRule(triggerColumnIndex, condition, color, description));
        return this;
    }

    /**
     * 添加字符串相等的单元格颜色规则
     */
    public CellColorHandler addStringEqualsCellRule(int columnIndex, String targetValue, Short color, String description) {
        return addCellColorRule(columnIndex, value ->
                value != null && targetValue.equals(String.valueOf(value)), color, description);
    }

    /**
     * 添加字符串包含的单元格颜色规则
     */
    public CellColorHandler addStringContainsCellRule(int columnIndex, String targetValue, Short color, String description) {
        return addCellColorRule(columnIndex, value ->
                value != null && String.valueOf(value).contains(targetValue), color, description);
    }

    /**
     * 添加数值比较的单元格颜色规则
     */
    public CellColorHandler addNumberCompareCellRule(int columnIndex, Double threshold, CompareType compareType, Short color, String description) {
        return addCellColorRule(columnIndex, value -> {
            if (value == null) return false;
            try {
                double numValue = Double.parseDouble(String.valueOf(value));
                switch (compareType) {
                    case GREATER_THAN:
                        return numValue > threshold;
                    case LESS_THAN:
                        return numValue < threshold;
                    case EQUAL:
                        return Math.abs(numValue - threshold) < 0.001;
                    case GREATER_EQUAL:
                        return numValue >= threshold;
                    case LESS_EQUAL:
                        return numValue <= threshold;
                    default:
                        return false;
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }, color, description);
    }

    /**
     * 添加空值/非空值的单元格颜色规则
     */
    public CellColorHandler addNullCheckCellRule(int columnIndex, boolean isNull, Short color, String description) {
        return addCellColorRule(columnIndex, value -> {
            if (isNull) {
                return value == null || String.valueOf(value).trim().isEmpty();
            } else {
                return value != null && !String.valueOf(value).trim().isEmpty();
            }
        }, color, description);
    }

    /**
     * 添加字符串相等的整行颜色规则
     */
    public CellColorHandler addStringEqualsRowRule(int triggerColumnIndex, String targetValue, Short color, String description) {
        return addRowColorRule(triggerColumnIndex, value ->
                value != null && targetValue.equals(String.valueOf(value)), color, description);
    }

    /**
     * 添加字符串包含的整行颜色规则
     */
    public CellColorHandler addStringContainsRowRule(int triggerColumnIndex, String targetValue, Short color, String description) {
        return addRowColorRule(triggerColumnIndex, value ->
                value != null && String.valueOf(value).contains(targetValue), color, description);
    }

    /**
     * 添加数值比较的整行颜色规则
     */
    public CellColorHandler addNumberCompareRowRule(int triggerColumnIndex, Double threshold, CompareType compareType, Short color, String description) {
        return addRowColorRule(triggerColumnIndex, value -> {
            if (value == null) return false;
            try {
                double numValue = Double.parseDouble(String.valueOf(value));
                switch (compareType) {
                    case GREATER_THAN:
                        return numValue > threshold;
                    case LESS_THAN:
                        return numValue < threshold;
                    case EQUAL:
                        return Math.abs(numValue - threshold) < 0.001;
                    case GREATER_EQUAL:
                        return numValue >= threshold;
                    case LESS_EQUAL:
                        return numValue <= threshold;
                    default:
                        return false;
                }
            } catch (NumberFormatException e) {
                return false;
            }
        }, color, description);
    }

    /**
     * 添加空值/非空值的整行颜色规则
     */
    public CellColorHandler addNullCheckRowRule(int triggerColumnIndex, boolean isNull, Short color, String description) {
        return addRowColorRule(triggerColumnIndex, value -> {
            if (isNull) {
                return value == null || String.valueOf(value).trim().isEmpty();
            } else {
                return value != null && !String.valueOf(value).trim().isEmpty();
            }
        }, color, description);
    }


    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 无需处理
    }

    @Override
    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, WriteCellData cellData, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        // 缓存行数据，用于整行颜色判断
        if (!isHead && cellData != null) {
            int rowIndex = cell.getRowIndex();
            int columnIndex = cell.getColumnIndex();
            Object cellValue = getCellValue(cellData);
            rowDataCache.computeIfAbsent(rowIndex, k -> new ConcurrentHashMap<>())
                    .put(columnIndex, cellValue == null ? "" : cellValue);
        }
    }


    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        if (isHead) {
            // 表头样式处理
            applyHeaderStyle(writeSheetHolder, cell);
            return;
        }

        // 数据行样式处理
        applyDataCellStyle(writeSheetHolder, cell, cellDataList);
    }

    /**
     * 应用表头样式
     */
    private void applyHeaderStyle(WriteSheetHolder writeSheetHolder, Cell cell) {
        Workbook workbook = cell.getSheet().getWorkbook();
        WriteCellStyle writeCellStyle = createBaseStyle();
        WriteFont writeFont = createBaseFont();

        // 表头特殊样式
        writeFont.setBold(styleConfig.getHeaderBold());
        writeFont.setFontHeightInPoints(styleConfig.getHeaderFontSize());
        writeCellStyle.setFillForegroundColor(styleConfig.getHeaderBackgroundColor());
        writeCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);

        // 设置行高和列宽
        setRowHeight(writeSheetHolder.getSheet(), cell.getRowIndex(), styleConfig.getHeaderRowHeight());
        setColumnWidth(writeSheetHolder.getSheet(), cell.getColumnIndex(), styleConfig.getColumnWidth());

        writeCellStyle.setWriteFont(writeFont);
        CellStyle cellStyle = StyleUtil.buildCellStyle(workbook, null, writeCellStyle);
        cell.setCellStyle(cellStyle);
    }

    /**
     * 应用数据单元格样式
     */
    private void applyDataCellStyle(WriteSheetHolder writeSheetHolder, Cell cell, List<WriteCellData<?>> cellDataList) {
        Workbook workbook = cell.getSheet().getWorkbook();
        WriteCellStyle writeCellStyle = createBaseStyle();
        WriteFont writeFont = createBaseFont();

        // 数据行基础样式
        writeFont.setFontHeightInPoints(styleConfig.getDataFontSize());
        setRowHeight(writeSheetHolder.getSheet(), cell.getRowIndex(), styleConfig.getDataRowHeight());
        setColumnWidth(writeSheetHolder.getSheet(), cell.getColumnIndex(), styleConfig.getColumnWidth());

        // 应用颜色规则
        Short finalColor = determineCellColor(cell, cellDataList);
        if (finalColor != null) {
            writeCellStyle.setFillForegroundColor(finalColor);
            writeCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        }

        writeCellStyle.setWriteFont(writeFont);
        CellStyle cellStyle = StyleUtil.buildCellStyle(workbook, null, writeCellStyle);
        cell.setCellStyle(cellStyle);
    }

    /**
     * 确定单元格颜色
     */
    private Short determineCellColor(Cell cell, List<WriteCellData<?>> cellDataList) {
        int rowIndex = cell.getRowIndex();
        int columnIndex = cell.getColumnIndex();
        Object cellValue = getCellValue(cellDataList);

        // 1. 优先检查整行颜色规则
        Short rowColor = checkRowColorRules(rowIndex);
        // 2. 检查单元格颜色规则
        Short cellColor = checkCellColorRules(columnIndex, cellValue);
        if (cellColor != null) {
            return cellColor;
        }
        if (rowColor == null) {
            return IndexedColors.WHITE.getIndex();
        }
        return rowColor;
    }

    /**
     * 检查整行颜色规则
     */
    private Short checkRowColorRules(int rowIndex) {
        Map<Integer, Object> rowData = rowDataCache.get(rowIndex);
        if (rowData == null) {
            return null;
        }

        for (RowColorRule rule : rowColorRules) {
            try {
                Object triggerValue = rowData.get(rule.getTriggerColumnIndex());
                if (rule.getCondition().test(triggerValue)) {
                    log.debug("行颜色规则匹配: {} - 行: {}, 触发列: {}, 值: {}",
                            rule.getDescription(), rowIndex, rule.getTriggerColumnIndex(), triggerValue);
                    return rule.getColor();
                }
            } catch (Exception e) {
                log.warn("行颜色规则执行失败: {} - 行: {}, 错误: {}",
                        rule.getDescription(), rowIndex, e.getMessage());
            }
        }

        return null;
    }

    /**
     * 检查单元格颜色规则
     */
    private Short checkCellColorRules(int columnIndex, Object cellValue) {
        for (CellColorRule rule : cellColorRules) {
            try {
                if (rule.getColumnIndex() == columnIndex && rule.getCondition().test(cellValue)) {
                    log.debug("单元格颜色规则匹配: {} - 列: {}, 值: {}",
                            rule.getDescription(), columnIndex, cellValue);
                    return rule.getColor();
                }
            } catch (Exception e) {
                log.warn("单元格颜色规则执行失败: {} - 列: {}, 值: {}, 错误: {}",
                        rule.getDescription(), columnIndex, cellValue, e.getMessage());
            }
        }

        return null;
    }

    /**
     * 获取单元格值
     */
    private Object getCellValue(List<WriteCellData<?>> cellDataList) {
        if (cellDataList == null || cellDataList.isEmpty()) {
            return null;
        }

        WriteCellData cellData = cellDataList.get(0);
        return getCellValue(cellData);
    }

    /**
     * 获取单元格值
     */
    private Object getCellValue(WriteCellData cellData) {
        if (cellData == null) {
            return null;
        }

        switch (cellData.getType()) {
            case STRING:
                return cellData.getStringValue();
            case NUMBER:
                return cellData.getNumberValue();
            case BOOLEAN:
                return cellData.getBooleanValue();
//            case DATE:
//                return cellData.getDateValue();
            default:
                return null;
        }
    }

    /**
     * 创建基础样式
     */
    private WriteCellStyle createBaseStyle() {
        WriteCellStyle style = new WriteCellStyle();
        style.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setHorizontalAlignment(HorizontalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        return style;
    }

    /**
     * 创建基础字体
     */
    private WriteFont createBaseFont() {
        WriteFont font = new WriteFont();
        font.setFontName(styleConfig.getFontName());
        font.setBold(false);
        return font;
    }

    /**
     * 设置行高
     */
    private void setRowHeight(Sheet sheet, int rowIndex, Short height) {
        if (height != null) {
            Row row = sheet.getRow(rowIndex);
            if (row != null) {
                row.setHeight(height);
            }
        }
    }

    /**
     * 设置列宽
     */
    private void setColumnWidth(Sheet sheet, int columnIndex, Integer width) {
        if (width != null) {
            sheet.setColumnWidth(columnIndex, width);
        }
    }

    /**
     * 单元格颜色规则
     */
    public static class CellColorRule {
        private final int columnIndex;
        private final Predicate<Object> condition;
        private final Short color;
        private final String description;

        public CellColorRule(int columnIndex, Predicate<Object> condition, Short color, String description) {
            this.columnIndex = columnIndex;
            this.condition = condition;
            this.color = color;
            this.description = description;
        }

        public int getColumnIndex() {
            return columnIndex;
        }

        public Predicate<Object> getCondition() {
            return condition;
        }

        public Short getColor() {
            return color;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 整行颜色规则
     */
    public static class RowColorRule {
        private final int triggerColumnIndex;
        private final Predicate<Object> condition;
        private final Short color;
        private final String description;

        public RowColorRule(int triggerColumnIndex, Predicate<Object> condition, Short color, String description) {
            this.triggerColumnIndex = triggerColumnIndex;
            this.condition = condition;
            this.color = color;
            this.description = description;
        }

        public int getTriggerColumnIndex() {
            return triggerColumnIndex;
        }

        public Predicate<Object> getCondition() {
            return condition;
        }

        public Short getColor() {
            return color;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 数值比较类型
     */
    public enum CompareType {
        GREATER_THAN,    // 大于
        LESS_THAN,       // 小于
        EQUAL,           // 等于
        GREATER_EQUAL,   // 大于等于
        LESS_EQUAL       // 小于等于
    }

    /**
     * 样式配置
     */
    public static class StyleConfig {
        private String fontName = "微软雅黑";
        private Short headerFontSize = 14;
        private Short dataFontSize = 12;
        private Boolean headerBold = true;
        private Short headerRowHeight = (short) (25 * 20);
        private Short dataRowHeight = (short) (20 * 20);
        private Integer columnWidth = 15 * 256;
        private Short headerBackgroundColor = IndexedColors.GREY_25_PERCENT.getIndex();

        // Getters and Setters
        public String getFontName() {
            return fontName;
        }

        public void setFontName(String fontName) {
            this.fontName = fontName;
        }

        public Short getHeaderFontSize() {
            return headerFontSize;
        }

        public void setHeaderFontSize(Short headerFontSize) {
            this.headerFontSize = headerFontSize;
        }

        public Short getDataFontSize() {
            return dataFontSize;
        }

        public void setDataFontSize(Short dataFontSize) {
            this.dataFontSize = dataFontSize;
        }

        public Boolean getHeaderBold() {
            return headerBold;
        }

        public void setHeaderBold(Boolean headerBold) {
            this.headerBold = headerBold;
        }

        public Short getHeaderRowHeight() {
            return headerRowHeight;
        }

        public void setHeaderRowHeight(Short headerRowHeight) {
            this.headerRowHeight = headerRowHeight;
        }

        public Short getDataRowHeight() {
            return dataRowHeight;
        }

        public void setDataRowHeight(Short dataRowHeight) {
            this.dataRowHeight = dataRowHeight;
        }

        public Integer getColumnWidth() {
            return columnWidth;
        }

        public void setColumnWidth(Integer columnWidth) {
            this.columnWidth = columnWidth;
        }

        public Short getHeaderBackgroundColor() {
            return headerBackgroundColor;
        }

        public void setHeaderBackgroundColor(Short headerBackgroundColor) {
            this.headerBackgroundColor = headerBackgroundColor;
        }
    }
}
