package top.kx.kxss.report.service.common;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

/**
 * 商品销售API
 *
 * <AUTHOR>
 */
public class PosCashPaymentCommonCtrl extends PosCashCommonCtrl{

    /**
     * 会员消费相关 wrapper
     * @param query
     * @return
     */
    public QueryWrapper basePaymentWrapper(DataOverviewQuery query) {
        QueryWrapper queryWrapper = baseWrapper(query);
        queryWrapper.eq("bpt.delete_flag", 0);
        queryWrapper.eq("bpt.state", true);
        queryWrapper.eq("pcp.delete_flag", 0);
        queryWrapper.eq("bpt.created_org_id", ContextUtil.getCurrentCompanyId());
        queryWrapper.eq("pcp.status", PosCashPaymentStatusEnum.PAY_SUCCESS.getCode());
        return queryWrapper;
    }

    /**
     * 会员消费相关 wrapper
     * @param query
     * @return
     */
    public QueryWrapper rechargePaymentWrapper(DataOverviewQuery query) {
        QueryWrapper queryWrapper = basePaymentWrapper(query);
        queryWrapper.eq("bpt.biz_type", "1");
        return queryWrapper;
    }

    /**
     * 储值卡相关 wrapper
     * @param query
     * @return
     */
    public QueryWrapper rechargeCardPaymentWrapper(DataOverviewQuery query) {
        QueryWrapper queryWrapper = basePaymentWrapper(query);
        queryWrapper.eq("bpt.biz_type", "8");
        return queryWrapper;
    }


}

