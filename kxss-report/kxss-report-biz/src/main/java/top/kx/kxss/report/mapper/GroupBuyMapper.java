package top.kx.kxss.report.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.kxss.report.query.GroupBuyQuery;
import top.kx.kxss.report.vo.GroupBuyResultVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 *
 * </p>
 *
 * <AUTHOR>
 */
@Repository
public interface GroupBuyMapper {

    IPage<GroupBuyResultVO> page(@Param("page") IPage<GroupBuyResultVO> page, @Param("model") GroupBuyQuery model);


    GroupBuyResultVO sum(@Param("model") GroupBuyQuery model);


    List<GroupBuyResultVO> list(@Param("model") GroupBuyQuery model);

}


