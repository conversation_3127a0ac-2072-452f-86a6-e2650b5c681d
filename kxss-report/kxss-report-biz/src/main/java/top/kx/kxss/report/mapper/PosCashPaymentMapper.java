package top.kx.kxss.report.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO;
import top.kx.kxss.pos.entity.cash.PosCashPaymentTransaction;
import top.kx.kxss.report.query.CashPaymentQuery;
import top.kx.kxss.report.vo.CashPaymentResultVO;
import top.kx.kxss.report.vo.PaymentTransactionResultVO;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 *
 * </p>
 *
 * <AUTHOR>
 */
@Repository
public interface PosCashPaymentMapper {


    /**
     * pos_cash 查询
     *
     * @param wrapper
     * @return
     */
    PosCashPaymentResultVO selectOneAmount(@Param(Constants.WRAPPER) QueryWrapper wrapper);


    List<PosCashPaymentResultVO> selectListAmountByPayTypeId(@Param(Constants.WRAPPER) QueryWrapper wrapper);


    List<PosCashPaymentResultVO> queryListByCashIds(List<Long> cashIds);


    IPage<PaymentTransactionResultVO> transactionPageResultVO(IPage<PosCashPaymentTransaction> page, @Param(Constants.WRAPPER) QueryWrap<PosCashPaymentTransaction> wrap);


    PaymentTransactionResultVO transactionSum(@Param(Constants.WRAPPER) QueryWrap<PosCashPaymentTransaction> wrap);

    List<PaymentTransactionResultVO> transactionList(@Param(Constants.WRAPPER) QueryWrap<PosCashPaymentTransaction> wrap);


    PosCashPaymentResultVO selectOnePayAmount(@Param("model") DataOverviewQuery model, @Param("orgId") Long orgId);

    List<PaymentTransactionResultVO> selectPayAmount(@Param("model") DataOverviewQuery model, @Param("orgId") Long orgId);

    List<CashPaymentResultVO> cashPaymentList(@Param("model") CashPaymentQuery model);

    List<CashPaymentResultVO> cashList(@Param("model") CashPaymentQuery build);
}


