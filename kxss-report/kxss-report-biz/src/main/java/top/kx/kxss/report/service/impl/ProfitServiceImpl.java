package top.kx.kxss.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.kxss.app.vo.result.cash.AmountResultVO;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO;
import top.kx.kxss.base.vo.query.payment.BasePaymentTypePageQuery;
import top.kx.kxss.base.vo.result.payment.BasePaymentTypeResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.base.PaymentBizTypeEnum;
import top.kx.kxss.payment.BasePaymentTypeApi;
import top.kx.kxss.report.mapper.ProfitMapper;
import top.kx.kxss.report.service.PosCashPaymentService;
import top.kx.kxss.report.service.ProfitService;
import top.kx.kxss.report.service.common.CommonCtrl;
import top.kx.kxss.report.service.common.ProfitCommonCtrl;
import top.kx.kxss.report.vo.FeeAmountResultVO;
import top.kx.kxss.report.vo.ReferenceProfitDetailResultVO;
import top.kx.kxss.report.vo.ReferenceProfitResultVO;
import top.kx.kxss.wxapp.api.commission.CommissionApi;
import top.kx.kxss.wxapp.api.custom.CustomApi;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.StatisCommissionResultVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 利润销售API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class ProfitServiceImpl extends ProfitCommonCtrl implements ProfitService {

    private final CustomApi customApi;
    private final ProfitMapper profitMapper;
    private final CommissionApi commissionApi;
    private final PosCashPaymentService posCashPaymentService;
    private final BasePaymentTypeApi basePaymentTypeApi;


    @Override
    public ReferenceProfitResultVO reference(DataOverviewQuery query) {
        R<DataOverviewQuery> storeTime = customApi.getStoreTime(query);
        DataOverviewQuery storeTimeData = storeTime.getData();
        query.setStartDate(storeTimeData.getStartDate());
        query.setEndDate(storeTimeData.getEndDate());
        ReferenceProfitResultVO resultVO = ReferenceProfitResultVO.builder()
                .referenceProfit(BigDecimal.ZERO).build();

        BigDecimal referenceProfit = BigDecimal.ZERO;
        AmountResultVO amountResultVO = profitMapper.selectOneCashAmount(baseWrapper(query));
        List<ReferenceProfitDetailResultVO> detailResultVOList = new ArrayList<>();
        ReferenceProfitDetailResultVO payment = ReferenceProfitDetailResultVO.builder()
                .name("收款(含充值)")
                .value("0.00")
                .build();
        BigDecimal discountAmount = BigDecimal.ZERO;
        BigDecimal rechargeAmount = BigDecimal.ZERO;
        BigDecimal rechargeCardAmount = BigDecimal.ZERO;
        PosCashPaymentResultVO cashPaymentResultVO = posCashPaymentService.selectOneRechargeAmount(query);
        if (Objects.nonNull(amountResultVO)) {
            referenceProfit = amountResultVO.getAmount().subtract(amountResultVO.getRechargeAmount());
            // 收款(含充值) = 实际收款 - 赠金支付 - 本金支付 - 储值卡支付
            discountAmount = amountResultVO.getDiscountAmount();
            if (Objects.nonNull(cashPaymentResultVO)) {
                discountAmount = discountAmount.add(cashPaymentResultVO.getGiftPayment());
                rechargeAmount = cashPaymentResultVO.getRechargePayment();
            }
            PosCashPaymentResultVO cardPaymentResultVO = posCashPaymentService.selectOneRechargeCardAmount(query);
            if (Objects.nonNull(cardPaymentResultVO)) {
                rechargeCardAmount = cardPaymentResultVO.getPayment();
            }
            referenceProfit = referenceProfit.subtract(discountAmount).subtract(rechargeAmount).subtract(rechargeCardAmount);
            referenceProfit = referenceProfit.add(amountResultVO.getRechargeAmount());
            payment.setValue(referenceProfit.setScale(2, RoundingMode.HALF_UP).toPlainString());
        }
        detailResultVOList.add(payment);

        // 商品销售成本
        AmountResultVO productAmountResultVO = profitMapper.selectProductAmount(cashProductWrapper(query));
        // 销售成本
        ReferenceProfitDetailResultVO saleCost = ReferenceProfitDetailResultVO.builder()
                .name("商品销售成本")
                .value("0.00")
                .build();
        if (Objects.nonNull(productAmountResultVO)) {
            referenceProfit = referenceProfit.subtract(productAmountResultVO.getCostPrice());
            saleCost.setValue(productAmountResultVO.getCostPrice().setScale(2, RoundingMode.HALF_UP).toPlainString());
        }
        detailResultVOList.add(saleCost);

        // 提成
        R<StatisCommissionResultVO> commissionResultVOR = commissionApi.commissionSum(query);
        // 充值提成
        ReferenceProfitDetailResultVO recharge = ReferenceProfitDetailResultVO.builder()
                .name("充值提成")
                .value("0.00")
                .build();
        // 服务业绩
        ReferenceProfitDetailResultVO service = ReferenceProfitDetailResultVO.builder()
                .name("服务提成")
                .value("0.00")
                .build();
        // 商品提成
        ReferenceProfitDetailResultVO product = ReferenceProfitDetailResultVO.builder()
                .name("商品提成")
                .value("0.00")
                .build();
        if (commissionResultVOR.getIsSuccess() && Objects.nonNull(commissionResultVOR.getData())) {
            StatisCommissionResultVO commissionResultVO = commissionResultVOR.getData();
            if (Objects.nonNull(commissionResultVO) && Objects.nonNull(commissionResultVO.getRecharge())) {
                referenceProfit = referenceProfit.subtract(commissionResultVO.getRecharge());
                recharge.setValue(commissionResultVO.getRecharge().setScale(2, RoundingMode.HALF_UP).toPlainString());
            }
            if (Objects.nonNull(commissionResultVO) && Objects.nonNull(commissionResultVO.getService())) {
                referenceProfit = referenceProfit.subtract(commissionResultVO.getService());
                service.setValue(commissionResultVO.getService().setScale(2, RoundingMode.HALF_UP).toPlainString());
            }
            if (Objects.nonNull(commissionResultVO) && Objects.nonNull(commissionResultVO.getProduct())) {
                referenceProfit = referenceProfit.subtract(commissionResultVO.getProduct());
                product.setValue(commissionResultVO.getProduct().setScale(2, RoundingMode.HALF_UP).toPlainString());
            }
        }
        detailResultVOList.add(recharge);
        detailResultVOList.add(service);
        detailResultVOList.add(product);
        R<List<BasePaymentTypeResultVO>> paymentTypelistR = basePaymentTypeApi.query(new BasePaymentTypePageQuery());
        List<PosCashPaymentResultVO> cashPaymentResultVOList = posCashPaymentService.selectListAmountByPayTypeId(query);
        if (!paymentTypelistR.getIsSuccess() || CollUtil.isEmpty(paymentTypelistR.getData()) || CollUtil.isEmpty(cashPaymentResultVOList)) {
            resultVO.setReferenceProfit(referenceProfit);
            resultVO.setDetailList(detailResultVOList);
            return resultVO;
        }
        List<BasePaymentTypeResultVO> paymentTypeList = paymentTypelistR.getData();
        // 转 map
        Map<Long, BasePaymentTypeResultVO> paymentTypeMap = paymentTypeList.stream().collect(Collectors.toMap(BasePaymentTypeResultVO::getId, Function.identity()));
        // 订单类型
        BigDecimal meituan = BigDecimal.ZERO;
        BigDecimal douyin = BigDecimal.ZERO;
        BigDecimal polymerization = BigDecimal.ZERO;
        BigDecimal other = BigDecimal.ZERO;

        for (PosCashPaymentResultVO cashPayment : cashPaymentResultVOList) {
            PaymentBizTypeEnum bizTypeEnum = PaymentBizTypeEnum.get(cashPayment.getBizType());
            BasePaymentTypeResultVO basePaymentTypeResultVO = paymentTypeMap.get(cashPayment.getPayTypeId());
            // 美团/大众, 抖音, 聚合, 其他支付
            switch (bizTypeEnum) {
                case MEITUAN:
                    if (Objects.nonNull(basePaymentTypeResultVO) && Objects.nonNull(basePaymentTypeResultVO.getFeeRate())) {
                        meituan = meituan.add(cashPayment.getPayment().multiply(basePaymentTypeResultVO.getFeeRate()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
                    }
                    break;
                case DOUYIN:
                    if (Objects.nonNull(basePaymentTypeResultVO) && Objects.nonNull(basePaymentTypeResultVO.getFeeRate())) {
                        douyin = douyin.add(cashPayment.getPayment().multiply(basePaymentTypeResultVO.getFeeRate()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
                    }
                    break;
                case POLYMERIZATION:
                    if (Objects.nonNull(basePaymentTypeResultVO) && Objects.nonNull(basePaymentTypeResultVO.getFeeRate())) {
                        polymerization = polymerization.add(cashPayment.getPayment().multiply(basePaymentTypeResultVO.getFeeRate()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
                    }
                    break;
                case DIRECT:
                case ACCOUNT:
                case WECHAT:
                case CASH:
                case OTHER_PAY:
                case STORED:
                    if (Objects.nonNull(basePaymentTypeResultVO) && Objects.nonNull(basePaymentTypeResultVO.getFeeRate())) {
                        other = other.add(cashPayment.getPayment().multiply(basePaymentTypeResultVO.getFeeRate()).divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP));
                    }
                    break;
                default:
                    break;
            }
        }
        // 美团/大众手续费
        ReferenceProfitDetailResultVO meituanFee = ReferenceProfitDetailResultVO.builder()
                .name("美团/大众手续费")
                .value(meituan.toPlainString())
                .build();

         // 抖音手续费
        ReferenceProfitDetailResultVO douyinFee = ReferenceProfitDetailResultVO.builder()
                .name("抖音手续费")
                .value(douyin.toPlainString())
                .build();

        // 聚合 手续费
        ReferenceProfitDetailResultVO polymerizationFee = ReferenceProfitDetailResultVO.builder()
                .name("聚合支付手续费")
                .value(polymerization.toPlainString())
                .build();

        // 其他手续费
        ReferenceProfitDetailResultVO otherFee = ReferenceProfitDetailResultVO.builder()
                .name("其他手续费")
                .value(other.toPlainString())
                .build();

        referenceProfit = referenceProfit.subtract(meituan).subtract(douyin).subtract(polymerization).subtract(other);
        detailResultVOList.add(meituanFee);
        detailResultVOList.add(douyinFee);
        detailResultVOList.add(polymerizationFee);
        detailResultVOList.add(otherFee);
        resultVO.setReferenceProfit(referenceProfit);
        resultVO.setDetailList(detailResultVOList);
        return resultVO;
    }
}

