package top.kx.kxss.report.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.report.query.ScoreExchangeQuery;
import top.kx.kxss.report.vo.ScoreExchangeResultVO;

import java.util.List;
import java.util.Map;

/**
 * 商品销售API
 *
 * <AUTHOR>
 */
public interface ScoreExchangeService {

    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    Map<String, Object> page(PageParams<ScoreExchangeQuery> params);

    /**
     * 统计查询
     *
     * @param params
     * @return
     */
    ScoreExchangeResultVO sum(ScoreExchangeQuery params);

    /**
     * 列表查询
     *
     * @param params
     * @return
     */
    List<ScoreExchangeResultVO> list(ScoreExchangeQuery params);
}
