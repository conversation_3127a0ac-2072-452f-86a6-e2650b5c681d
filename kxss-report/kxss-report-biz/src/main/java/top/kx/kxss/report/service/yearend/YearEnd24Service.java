package top.kx.kxss.report.service.yearend;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.report.entity.yearend.YearEnd24;
import top.kx.kxss.report.vo.query.yearend.YearEnd24PageQuery;
import top.kx.kxss.report.vo.result.yearend.YearEnd24ResultVO;
import top.kx.kxss.report.vo.save.yearend.YearEnd24SaveVO;
import top.kx.kxss.report.vo.update.yearend.YearEnd24UpdateVO;
import top.kx.kxss.report.vo.yearend.YearEnd24DataResultVO;

/**
 * 24年年终总结统计
 *
 * <AUTHOR>
 */
public interface YearEnd24Service  extends SuperService<Long, YearEnd24, YearEnd24SaveVO,
        YearEnd24UpdateVO, YearEnd24PageQuery, YearEnd24ResultVO> {

    YearEnd24DataResultVO overview();

    boolean save(YearEnd24 build);

    boolean update(LbUpdateWrap<YearEnd24> eq);
}
