package top.kx.kxss.report.service;

import top.kx.basic.base.request.PageParams;
import top.kx.kxss.report.query.AnalysisMonthQuery;
import top.kx.kxss.report.query.ProductAttributeQuery;
import top.kx.kxss.report.vo.ProductAttributeResultVO;

import java.util.List;
import java.util.Map;

/**
 * API
 *
 * <AUTHOR>
 */
public interface AnalysisService {

    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    Map<String, Object> monthPage(PageParams<AnalysisMonthQuery> params);


}
