package top.kx.kxss.report.service.common;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.poi.ss.formula.functions.T;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.model.enumeration.base.PaymentBizTypeEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillTypeEnum;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

import java.util.Arrays;

/**
 * 商品销售API
 *
 * <AUTHOR>
 */
public class GroupBuyCommonCtrl extends PosCashCommonCtrl{


    /**
     * 基础查询条件
     * @param query
     * @return
     */
    public QueryWrapper<T> groupBuyWrapper(DataOverviewQuery query) {
        QueryWrapper<T> queryWrapper = baseWrapper(query);
        queryWrapper.eq("bpt.delete_flag", 0);
        queryWrapper.in("bpt.biz_type", PaymentBizTypeEnum.MEITUAN.getCode(), PaymentBizTypeEnum.DOUYIN.getCode());
        queryWrapper.eq("bpt.state", true);
        queryWrapper.eq("pct.is_check_securities", true);
        queryWrapper.eq("pcp.delete_flag", 0);
        queryWrapper.eq("bpt.created_org_id", ContextUtil.getCurrentCompanyId());
        queryWrapper.eq("pcp.status", PosCashPaymentStatusEnum.PAY_SUCCESS.getCode());
        return queryWrapper;
    }


}

