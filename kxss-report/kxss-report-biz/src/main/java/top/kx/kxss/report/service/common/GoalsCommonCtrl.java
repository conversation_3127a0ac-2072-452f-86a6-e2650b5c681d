package top.kx.kxss.report.service.common;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

/**
 * 商品销售API
 *
 * <AUTHOR>
 */
public class GoalsCommonCtrl extends PosCashCommonCtrl{


    /**
     * 基础查询条件
     * @param query
     * @return
     */
    public QueryWrapper<PosCash> payTypeWrapper(DataOverviewQuery query) {
        QueryWrapper<PosCash> queryWrapper = baseWrapper(query);
        queryWrapper.eq("t.status", PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                .eq("t.delete_flag", 0)
                .eq("p.org_id", ContextUtil.getCurrentCompanyId());
        return queryWrapper;
    }


}

