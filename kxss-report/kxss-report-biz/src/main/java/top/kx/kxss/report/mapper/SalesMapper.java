package top.kx.kxss.report.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.kxss.report.vo.SalesDetailResultVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * 商品销售统计
 * </p>
 *
 * <AUTHOR>
 */
@Repository
public interface SalesMapper {

    List<SalesDetailResultVO> productList(@Param(Constants.WRAPPER) QueryWrapper queryWrapper);

    List<SalesDetailResultVO> tableList(@Param(Constants.WRAPPER) QueryWrapper queryWrapper);

    List<SalesDetailResultVO> serviceList(@Param(Constants.WRAPPER) QueryWrapper queryWrapper);

}


