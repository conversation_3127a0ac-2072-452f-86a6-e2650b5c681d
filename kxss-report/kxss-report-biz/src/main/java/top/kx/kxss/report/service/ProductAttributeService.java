package top.kx.kxss.report.service;

import top.kx.basic.base.request.PageParams;
import top.kx.kxss.report.query.GroupBuyQuery;
import top.kx.kxss.report.query.ProductAttributeQuery;
import top.kx.kxss.report.vo.ProductAttributeResultVO;

import java.util.List;
import java.util.Map;

/**
 * API
 *
 * <AUTHOR>
 */
public interface ProductAttributeService {

    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    Map<String, Object> page(PageParams<ProductAttributeQuery> params);


    /**
     * 列表查询
     * @param params
     * @return
     */
    ProductAttributeResultVO sum(ProductAttributeQuery params);

    /**
     * 列表查询
     * @param params
     * @return
     */
    List<ProductAttributeResultVO> list(ProductAttributeQuery params);

}
