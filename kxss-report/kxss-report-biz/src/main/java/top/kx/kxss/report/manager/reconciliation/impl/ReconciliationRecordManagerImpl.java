package top.kx.kxss.report.manager.reconciliation.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.report.entity.reconciliation.ReconciliationRecord;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.report.manager.reconciliation.ReconciliationRecordManager;
import top.kx.kxss.report.mapper.reconciliation.ReconciliationRecordMapper;

/**
 * <p>
 * 通用业务实现类
 * 对账单记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-30 20:18:34
 * @create [2025-06-30 20:18:34] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ReconciliationRecordManagerImpl extends SuperManagerImpl<ReconciliationRecordMapper, ReconciliationRecord> implements ReconciliationRecordManager {

}


