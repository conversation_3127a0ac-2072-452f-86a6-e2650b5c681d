package top.kx.kxss.report.mapper.reconciliation;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.report.entity.reconciliation.ReconciliationRecord;
import top.kx.kxss.report.vo.result.reconciliation.StatisticReconciliationDownloadResultVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * 对账单记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-30 20:18:34
 * @create [2025-06-30 20:18:34] [dou] [代码生成器生成]
 */
@Repository
public interface ReconciliationRecordMapper extends SuperMapper<ReconciliationRecord> {

    List<StatisticReconciliationDownloadResultVO> getDownloadDataList(@Param("instNo") String instNo,
                                                                      @Param("mchNo") String mchNo,
                                                                      @Param("startTime") String startTime,
                                                                      @Param("endTime") String endTime);
}


