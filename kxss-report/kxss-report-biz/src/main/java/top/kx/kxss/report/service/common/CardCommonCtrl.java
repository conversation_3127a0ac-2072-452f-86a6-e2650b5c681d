package top.kx.kxss.report.service.common;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.base.entity.member.card.MemberCardChange;
import top.kx.kxss.report.query.CardConsumeQuery;

import java.util.Objects;

/**
 * 商品销售API
 *
 * <AUTHOR>
 */
public class CardCommonCtrl extends PosCashCommonCtrl {


    /**
     * memberCardChangeWrapper
     */
    public QueryWrapper<MemberCardChange> memberCardChangeWrapper(CardConsumeQuery query) {
        QueryWrapper<MemberCardChange> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("mcc.delete_flag", 0)
                .eq("pc.delete_flag", 0)
                .eq("mcc.org_id", ContextUtil.getCurrentCompanyId())
                .eq(Objects.nonNull(query.getMemberCardId()), "mcc.member_card_id", query.getMemberCardId())
                .eq(StringUtils.isNotBlank(query.getType()), "mcc.type_", query.getType())
                .eq(Objects.nonNull(query.getCardId()), "mc.card_id", query.getCardId())
                .like(StringUtils.isNotBlank(query.getCode()), "pc.code", query.getCode())
                .like(StringUtils.isNotBlank(query.getKeyword()), "mcc.name", query.getKeyword());
        if (Objects.nonNull(query.getConsumeStartTime()) && Objects.nonNull(query.getConsumeEndTime())) {
            queryWrapper.between("mcc.created_time", query.getConsumeStartTime(), query.getConsumeEndTime());
        } else if (Objects.nonNull(query.getConsumeStartTime())) {
            queryWrapper.ge("mcc.created_time", query.getConsumeStartTime());
        } else if (Objects.nonNull(query.getConsumeEndTime())) {
            queryWrapper.le("mcc.created_time", query.getConsumeEndTime());
        }
        if (Objects.nonNull(query.getCompleteStartTime()) && Objects.nonNull(query.getCompleteEndTime())) {
            queryWrapper.between("pc.complete_time", query.getCompleteStartTime(), query.getCompleteEndTime());
        } else if (Objects.nonNull(query.getCompleteStartTime())) {
            queryWrapper.ge("pc.complete_time", query.getCompleteStartTime());
        } else if (Objects.nonNull(query.getCompleteEndTime())) {
            queryWrapper.le("pc.complete_time", query.getCompleteEndTime());
        }
        // 手机号
        if (StringUtils.isNotBlank(query.getMobile())) {
            //queryWrapper.exists("select 1 from member_info where mobile = {0}", query.getMobile());
            queryWrapper.exists("select 1 from member_info where delete_flag = 0 and instr(mobile, {0})", query.getMobile());
            //queryWrapper.exists("select 1 from member_info where mobile like '%" + query.getMobile() + "%'");
        }
        return queryWrapper;
    }


}

