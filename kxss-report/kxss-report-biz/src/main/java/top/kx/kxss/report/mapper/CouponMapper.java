package top.kx.kxss.report.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.kxss.base.entity.coupon.BaseCouponInfo;
import top.kx.kxss.base.entity.member.coupon.MemberCoupon;
import top.kx.kxss.report.vo.MemberCouponResultVO;
import top.kx.kxss.report.vo.StatisCouponResultVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 *
 * </p>
 *
 * <AUTHOR>
 */
@Repository
public interface CouponMapper {

    IPage<StatisCouponResultVO> statisPage(IPage<StatisCouponResultVO> page, @Param(Constants.WRAPPER) QueryWrapper<BaseCouponInfo> wrap);

    StatisCouponResultVO statisSum(@Param(Constants.WRAPPER) QueryWrapper<BaseCouponInfo> wrap);

    List<StatisCouponResultVO> statisList(@Param(Constants.WRAPPER) QueryWrapper<BaseCouponInfo> wrap);

    List<StatisCouponResultVO> statisPosCashCouponList(@Param(Constants.WRAPPER) QueryWrapper<BaseCouponInfo> wrap);

    IPage<MemberCouponResultVO> memberCouponPage(IPage<MemberCouponResultVO> page, @Param(Constants.WRAPPER) QueryWrapper<MemberCoupon> wrap);

    MemberCouponResultVO memberCouponSum(@Param(Constants.WRAPPER) QueryWrapper<MemberCoupon> wrap);

    List<MemberCouponResultVO> memberCouponList(@Param(Constants.WRAPPER) QueryWrapper<MemberCoupon> wrap);

}


