package top.kx.kxss.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.report.mapper.AttributeMapper;
import top.kx.kxss.report.query.AnalysisMonthQuery;
import top.kx.kxss.report.query.ProductAttributeQuery;
import top.kx.kxss.report.service.AnalysisService;
import top.kx.kxss.report.service.ProductAttributeService;
import top.kx.kxss.report.service.common.AnalysisCommonCtrl;
import top.kx.kxss.report.service.common.AttributeCommonCtrl;
import top.kx.kxss.report.vo.ProductAttributeResultVO;
import top.kx.kxss.wxapp.api.custom.CustomApi;
import top.kx.kxss.wxapp.vo.query.common.OrgIdListQuery;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.ColumnVO;

import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分析报表
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class AnalysisServiceImpl extends AnalysisCommonCtrl implements AnalysisService {

    private final CustomApi customApi;
    private final AttributeMapper attributeMapper;
    private final EchoService echoService;


    @Override
    public Map<String, Object> monthPage(PageParams<AnalysisMonthQuery> params) {
        AnalysisMonthQuery model = params.getModel();
//        List<String> dateList = generateDateList(model.getYearMonth());
//        Map<String, String> dayOfWeekMap = convertDateListToDayOfWeekMap(dateList);
        // 总营业额	总单数	平均每单价  不包含



        return Collections.emptyMap();
    }

}

