package top.kx.kxss.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.base.GroupBuyTypeEnum;
import top.kx.kxss.report.mapper.PosCashPaymentMapper;
import top.kx.kxss.report.query.CashPaymentQuery;
import top.kx.kxss.report.query.PaymentTransactionQuery;
import top.kx.kxss.report.service.PosCashPaymentService;
import top.kx.kxss.report.service.common.PosCashPaymentCommonCtrl;
import top.kx.kxss.report.vo.CashPaymentResultVO;
import top.kx.kxss.report.vo.PaymentTransactionResultVO;
import top.kx.kxss.wxapp.api.custom.CustomApi;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.ColumnVO;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 利润销售API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class PosCashPaymentServiceImpl extends PosCashPaymentCommonCtrl implements PosCashPaymentService {

    private final PosCashPaymentMapper posCashPaymentMapper;
    private final CustomApi customApi;
    @Autowired
    private EchoService echoService;


    @Override
    public PosCashPaymentResultVO selectOneRechargeAmount(DataOverviewQuery query) {
        return posCashPaymentMapper.selectOneAmount(rechargePaymentWrapper(query));
    }

    @Override
    public PosCashPaymentResultVO selectOnePayAmount(DataOverviewQuery query, Long orgId) {
        return posCashPaymentMapper.selectOnePayAmount(query, orgId);
    }
    @Override
    public List<PaymentTransactionResultVO> selectPayAmount(DataOverviewQuery query, Long orgId) {
        return posCashPaymentMapper.selectPayAmount(query, orgId);
    }

    @Override
    public PosCashPaymentResultVO selectOneRechargeCardAmount(DataOverviewQuery query) {
        return posCashPaymentMapper.selectOneAmount(rechargeCardPaymentWrapper(query));
    }

    @Override
    public List<PosCashPaymentResultVO> selectListAmountByPayTypeId(DataOverviewQuery query) {
        return posCashPaymentMapper.selectListAmountByPayTypeId(basePaymentWrapper(query));
    }

    @Override
    public Map<String, Object> transaction(PageParams<PaymentTransactionQuery> params) {
        // 设置表头
        List<ColumnVO> columnVOList = com.google.common.collect.Lists.newArrayList(
                ColumnVO.builder().name("completeDate").label("订单完成日期")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("payDate").label("支付日期")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("orderCreatedTime").label("订单创建时间")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeTime").label("订单完成时间")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payTime").label("支付时间")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("typeDesc").label("业务类型")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orderSourceDesc").label("支付渠道")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payTypeName").label("支付方式")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payTypeDetail").label("支付方式明细")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("statusDesc").label("交易类型")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("交易金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("feeAmount").label("手续费")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orderId").label("第三方流水号")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("securitiesTypeDesc").label("券类型")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("securitiesNumber").label("券号")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("employee").label("员工")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("code").label("业务订单号")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("billStateDesc").label("订单状态")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableName").label("台桌")
                        .width(180).emptyString("-").fixed(false).build()
        );
        PaymentTransactionQuery model = params.getModel();
        transactionInitDate(model);
        params.setOrder(null);
        params.setSort(null);
        IPage<PaymentTransactionResultVO> pageResultVO = posCashPaymentMapper.transactionPageResultVO(params.buildPage(PaymentTransactionResultVO.class), paymentTransactionWrapper(model));
        if (CollUtil.isEmpty(pageResultVO.getRecords())) {
            Map<String, Object> objectMap = BeanUtil.beanToMap(pageResultVO);
            objectMap.put("columnList", columnVOList);
            return objectMap;
        }

        echoService.action(pageResultVO.getRecords());
        for (PaymentTransactionResultVO record : pageResultVO.getRecords()) {
            // 判断是否为空
            if (StringUtils.isNotBlank(record.getSecuritiesType())) {
                GroupBuyTypeEnum groupBuyTypeEnum = GroupBuyTypeEnum.get(record.getSecuritiesType());
                if (Objects.nonNull(groupBuyTypeEnum)) {
                    record.setSecuritiesTypeDesc(groupBuyTypeEnum.getDesc());
                }
            }
            Map<String, Object> echoMap = record.getEchoMap();
            if (Objects.nonNull(echoMap.get("orgId"))) {
                record.setOrg(echoMap.get("orgId").toString());
            }
            if (Objects.nonNull(echoMap.get("type"))) {
                record.setTypeDesc(echoMap.get("type").toString());
            }
            if (Objects.nonNull(echoMap.get("orderSource"))) {
                record.setOrderSourceDesc(echoMap.get("orderSource").toString());
            }
            if (Objects.nonNull(echoMap.get("status"))) {
                record.setStatusDesc(echoMap.get("status").toString());
            }
            if (Objects.nonNull(echoMap.get("employeeId"))) {
                record.setEmployee(echoMap.get("employeeId").toString());
            }
            if (Objects.nonNull(echoMap.get("billState"))) {
                record.setBillStateDesc(echoMap.get("billState").toString());
            }
        }
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageResultVO);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public PaymentTransactionResultVO transactionSum(PaymentTransactionQuery params) {
        transactionInitDate(params);
        return posCashPaymentMapper.transactionSum(paymentTransactionWrapper(params));
    }

    @Override
    public List<PaymentTransactionResultVO> transactionList(PaymentTransactionQuery params) {
        transactionInitDate(params);
        List<PaymentTransactionResultVO> transactionResultVOList = posCashPaymentMapper.transactionList(paymentTransactionWrapper(params));
        echoService.action(transactionResultVOList);
        for (PaymentTransactionResultVO record : transactionResultVOList) {
            // 判断是否为空
            if (StringUtils.isNotBlank(record.getSecuritiesType())) {
                GroupBuyTypeEnum groupBuyTypeEnum = GroupBuyTypeEnum.get(record.getSecuritiesType());
                if (Objects.nonNull(groupBuyTypeEnum)) {
                    record.setSecuritiesTypeDesc(groupBuyTypeEnum.getDesc());
                }
            }
            Map<String, Object> echoMap = record.getEchoMap();
            if (Objects.nonNull(echoMap.get("orgId"))) {
                record.setOrg(echoMap.get("orgId").toString());
            }
            if (Objects.nonNull(echoMap.get("type"))) {
                record.setTypeDesc(echoMap.get("type").toString());
            }
            if (Objects.nonNull(echoMap.get("orderSource"))) {
                record.setOrderSourceDesc(echoMap.get("orderSource").toString());
            }
            if (Objects.nonNull(echoMap.get("status"))) {
                record.setStatusDesc(echoMap.get("status").toString());
            }
            if (Objects.nonNull(echoMap.get("employeeId"))) {
                record.setEmployee(echoMap.get("employeeId").toString());
            }
            if (Objects.nonNull(echoMap.get("billState"))) {
                record.setBillStateDesc(echoMap.get("billState").toString());
            }
        }
        return transactionResultVOList;
    }

    @Override
    public List<CashPaymentResultVO> cashPaymentList(CashPaymentQuery query) {
        return posCashPaymentMapper.cashPaymentList(query);
    }

    @Override
    public List<CashPaymentResultVO> cashList(CashPaymentQuery build) {
        return posCashPaymentMapper.cashList(build);
    }

    private void transactionInitDate(PaymentTransactionQuery params) {
        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(params);
            DataOverviewQuery storeTimeData = storeTime.getData();
            params.setStartDate(storeTimeData.getStartDate());
            params.setEndDate(storeTimeData.getEndDate());
        }
    }
}

