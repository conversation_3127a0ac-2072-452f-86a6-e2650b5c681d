package top.kx.kxss.report.service.yearend.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.report.entity.yearend.YearEnd24;
import top.kx.kxss.report.manager.yearend.YearEnd24Manager;
import top.kx.kxss.report.service.yearend.YearEnd24Service;
import top.kx.kxss.report.vo.query.yearend.YearEnd24PageQuery;
import top.kx.kxss.report.vo.result.yearend.YearEnd24ResultVO;
import top.kx.kxss.report.vo.save.yearend.YearEnd24SaveVO;
import top.kx.kxss.report.vo.update.yearend.YearEnd24UpdateVO;
import top.kx.kxss.report.vo.yearend.YearEnd24DataResultVO;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.time.LocalDate;

/**
 * 24年年终总结统计
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.DEFAULTS)
public class YearEnd24ServiceImpl extends SuperServiceImpl<YearEnd24Manager, Long, YearEnd24, YearEnd24SaveVO,
        YearEnd24UpdateVO, YearEnd24PageQuery, YearEnd24ResultVO> implements YearEnd24Service {


    @Override
    public YearEnd24DataResultVO overview() {
        YearEnd24DataResultVO dataResultVO = new YearEnd24DataResultVO();
        YearEnd24 yearEnd24 = superManager.getOne(Wraps.<YearEnd24>lbQ()
                .eq(YearEnd24::getDeleteFlag, 0)
                .eq(YearEnd24::getOrgId, ContextUtil.getCurrentCompanyId())
                .eq(YearEnd24::getTenantId, ContextUtil.getTenantId()));
        if (yearEnd24 != null) {
            BeanUtil.copyProperties(yearEnd24, dataResultVO);
            dataResultVO.setStartDate(DateUtils.format(yearEnd24.getStartDate(), DateUtils.DEFAULT_DATE_FORMAT_EN));
            dataResultVO.setTotalDays(formatValue(yearEnd24.getTotalDays()));
            dataResultVO.setTotalDuration(durationDesc(yearEnd24.getTotalDuration()));
            dataResultVO.setOrderNum(formatValue(yearEnd24.getOrderNum()));
            dataResultVO.setLightTimes(formatValue(yearEnd24.getLightTimes()));
            dataResultVO.setProductSales(formatValue(yearEnd24.getProductSales()));
            dataResultVO.setRechargeTimes(formatValue(yearEnd24.getRechargeTimes()));
            dataResultVO.setTotalGiftAmount(formatMoney(yearEnd24.getTotalGiftAmount()));
            dataResultVO.setTotalDiscountAmount(formatMoney(yearEnd24.getTotalDiscountAmount()));
            dataResultVO.setMemberNum(formatValue(yearEnd24.getMemberNum()));
            dataResultVO.setCurrMemberNum(formatValue(yearEnd24.getCurrMemberNum()));
            dataResultVO.setBusyMonth(yearEnd24.getBusyMonth().split("-")[1]);
            if (dataResultVO.getBusyMonth().startsWith("0")) {
                dataResultVO.setBusyMonth(dataResultVO.getBusyMonth().replace("0", ""));
            }
            dataResultVO.setBusyMouthAmount(formatMoney(yearEnd24.getBusyMouthAmount()));
            dataResultVO.setMostPopularTableDuration(durationDesc(yearEnd24.getMostPopularTableDuration()));
            dataResultVO.setLastDuration(durationDesc(yearEnd24.getLastDuration()));
            dataResultVO.setHighestPaidDate(DateUtils.format(yearEnd24.getHighestPaidDate(), "M月dd日"));
            dataResultVO.setHighestProfitProductAmount(formatMoney(yearEnd24.getHighestProfitProductAmount()));
            dataResultVO.setTotalEmployeeNum(formatValue(yearEnd24.getTotalEmployeeNum()));
            dataResultVO.setDepartureEmployeeNum(formatValue(yearEnd24.getDepartureEmployeeNum()));
            dataResultVO.setCurrEmployeeNum(formatValue(yearEnd24.getCurrEmployeeNum()));
            dataResultVO.setAvgOpenDuration(durationDesc(yearEnd24.getAvgOpenDuration()));
            dataResultVO.setMonthMostAmount(formatMoney(yearEnd24.getMonthMostAmount()));
            dataResultVO.setDayMostAmount(formatMoney(yearEnd24.getDayMostAmount()));
            dataResultVO.setFullPlatformDuration(durationDesc(yearEnd24.getFullPlatformDuration()));
            dataResultVO.setTotalAmount(formatMoney(yearEnd24.getTotalAmount()));
            dataResultVO.setMonthMost(yearEnd24.getMonthMost().split("-")[1]);
            if (dataResultVO.getMonthMost().startsWith("0")) {
                dataResultVO.setMonthMost(dataResultVO.getMonthMost().replace("0", ""));
            }
            dataResultVO.setDayMost(DateUtils.format(LocalDate.parse(yearEnd24.getDayMost()), "M月dd日"));

        }
        return dataResultVO;
    }

    @Override
    public boolean save(YearEnd24 build) {
        return superManager.save(build);
    }


    @Override
    public boolean update(LbUpdateWrap<YearEnd24> eq) {
        return superManager.update(eq);
    }


    private String durationDesc(Integer duration) {
        if (duration == null) {
            return null;
        }
        if (duration == 0) {
            return duration + "分钟";
        }
        int hours = (int) Math.floor((double) duration / 60);
        int minute = duration % 60;
        return (hours == 0 ? "" : formatValue(hours) + "小时")
                + (minute == 0 ? "" : minute + "分钟");
    }

    public static String formatMoney(BigDecimal amount) {
        DecimalFormat decimalFormat = new DecimalFormat("#,##0.##");
        return decimalFormat.format(amount);
    }

    public static String formatValue(Integer amount) {
        DecimalFormat decimalFormat = new DecimalFormat("#,###");
        return decimalFormat.format(amount);
    }
}

