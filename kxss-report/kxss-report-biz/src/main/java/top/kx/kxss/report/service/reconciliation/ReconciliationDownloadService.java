package top.kx.kxss.report.service.reconciliation;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.report.easyexcel.EasyExcelUtils;
import top.kx.kxss.pay.vo.result.IsvReconciliationInfoResultVO;
import top.kx.kxss.report.config.ReconciliationProperties;
import top.kx.kxss.report.entity.reconciliation.Reconciliation;
import top.kx.kxss.report.entity.reconciliation.ReconciliationRecord;
import top.kx.kxss.report.query.reconciliation.ReconciliationDownloadQuery;
import top.kx.kxss.report.query.reconciliation.StatisticReconciliationQuery;
import top.kx.kxss.report.vo.result.reconciliation.StatisticReconciliationDownloadResultVO;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/6/30 18:10
 */
@Service
@Slf4j
@EnableConfigurationProperties(ReconciliationProperties.class)
public class ReconciliationDownloadService {
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private ReconciliationRecordService reconciliationRecordService;
    @Autowired
    private ReconciliationService reconciliationService;
    @Autowired
    private ReconciliationProperties reconciliationProperties;

    public String downloadAndSaveReconciliation(ReconciliationDownloadQuery query) {
        List<ReconciliationProperties.Config> configs = reconciliationProperties.getConfigs();
        ArgumentAssert.notEmpty(configs, "对账单配置为空");
        ReconciliationProperties.Config config = configs.get(0);
        try {
            // 构建下载URL
            String downloadUrl = buildDownloadUrl(query, config);
            log.info("对账单下载URL：{}", downloadUrl);

            // 下载对账单内容
            String reconciliationContent = restTemplate.getForObject(downloadUrl, String.class);

            if (!StringUtils.hasText(reconciliationContent)) {
                log.warn("对账单内容为空，日期：{}，机构号：{}", query.getReconciliationDate(), config.getInstNo());
                return "对账单内容为空";
            }
            // 解析对账单内容
            List<ReconciliationRecord> records = parseReconciliationContent(reconciliationContent, query.getReconciliationDate(), config.getInstNo());
            if (records.isEmpty()) {
                log.warn("解析对账单记录为空，日期：{}，机构号：{}", query.getReconciliationDate(), config.getInstNo());
                return "解析对账单记录为空";
            }
            reconciliationRecordService.remove(Wraps.<ReconciliationRecord>lbQ()
                    .eq(ReconciliationRecord::getReconciliationDate, query.getReconciliationDate())
                    .eq(ReconciliationRecord::getDeleteFlag, 0));
            // 批量保存记录
            boolean saveResult = reconciliationRecordService.saveBatch(records);
            if (saveResult) {
                log.info("对账单下载并保存成功，日期：{}，机构号：{}，记录数：{}",
                        query.getReconciliationDate(), config.getInstNo(), records.size());

                // 将records转换为Map<String, ReconciliationRecord>格式
                Map<String, ReconciliationRecord> recordMap = groupAndMergeRecords(records);
                List<Reconciliation> reconciliationList = recordMap.entrySet().stream().map(entry -> {
                            String groupKey = entry.getKey();
                            ReconciliationRecord reconciliationRecord = entry.getValue();
                            String[] keyParts = parseGroupKey(groupKey);
                            Reconciliation reconciliation = Reconciliation.builder().build();
                            reconciliation.setReconciliationDate(keyParts[0]);
                            reconciliation.setInstNo(keyParts[1]);
                            reconciliation.setMchNo(keyParts[2]);
                            reconciliation.setTransactionAmount(reconciliationRecord.getTransactionAmount());
                            reconciliation.setFeeAmount(reconciliationRecord.getFeeAmount());
                            reconciliation.setRefundAmount(reconciliationRecord.getRefundAmount());
                            reconciliation.setBalanceAmount(reconciliationRecord.getBalanceAmount());
                            reconciliation.setMerchantDiscountAmount(reconciliationRecord.getMerchantDiscountAmount());
                            reconciliation.setUserPaidAmount(reconciliationRecord.getUserPaidAmount());
                            reconciliation.setMerchantActualAmount(reconciliationRecord.getMerchantActualAmount());
                            reconciliation.setTransactionOrderNum(reconciliationRecord.getCount());
                            reconciliation.setPlatformDiscountAmount(reconciliationRecord.getPlatformDiscountAmount());
                            return reconciliation;
                        })
                        .collect(Collectors.toList());

                // 先清理同日期和机构号的旧Reconciliation数据，避免重复
                reconciliationService.remove(Wraps.<Reconciliation>lbQ()
                        .eq(Reconciliation::getReconciliationDate, query.getReconciliationDate())
                );

                reconciliationService.saveBatch(reconciliationList);
                return String.format("对账单下载并保存成功，共%d条记录", records.size());
            } else {
                log.error("对账单保存失败，日期：{}，机构号：{}", query.getReconciliationDate(), config.getInstNo());
                return "对账单保存失败";
            }
        } catch (Exception e) {
            log.error("下载对账单异常，日期：{}，机构号：{}，异常信息：{}",
                    query.getReconciliationDate(), config.getInstNo(), e.getMessage(), e);
            return "下载对账单异常：" + e.getMessage();
        }
    }


    public void dayReconciliationDownload(StatisticReconciliationQuery query, HttpServletResponse response) {
        IsvReconciliationInfoResultVO resultVO = reconciliationService.reconciliationInfo(query.getBankCardId());
        ArgumentAssert.notNull(resultVO, "对账单信息为空");
        LocalDateTime startTime = DateUtils.getStartTime(query.getStartTime());
        LocalDateTime endTime = DateUtils.getEndTime(query.getEndTime());
        //两个时间相差天数
        int days = (int) (endTime.toLocalDate().toEpochDay() - startTime.toLocalDate().toEpochDay());
        ArgumentAssert.isFalse(days > 31, "最多只能下载1个月的数据");
        query.setStartTime(DateUtils.format(startTime, DateUtils.DEFAULT_DATE_TIME_FORMAT));
        query.setEndTime(DateUtils.format(endTime, DateUtils.DEFAULT_DATE_TIME_FORMAT));

        List<StatisticReconciliationDownloadResultVO> dataList =
                reconciliationRecordService.getDownloadDataList(resultVO, query.getStartTime(), query.getEndTime());
        int[] total = {};
        EasyExcelUtils.singleExport(resultVO.getMchNo() + "对账单" + query.getStartTime().concat(".xlsx"),
                dataList, StatisticReconciliationDownloadResultVO.class,
                total, response);
    }

    public List<ReconciliationRecord> parseReconciliationContent(String reconciliationContent, String reconciliationDate, String instNo) {
        List<ReconciliationRecord> records = new ArrayList<>();

        if (!StringUtils.hasText(reconciliationContent)) {
            return records;
        }

        // 按行分割对账单内容
        String[] lines = reconciliationContent.split("\r\n");

        for (String line : lines) {
            if (!StringUtils.hasText(line.trim())) {
                continue;
            }

            try {
                ReconciliationRecord record = parseReconciliationLine(line.trim(), reconciliationDate, instNo);
                if (record != null) {
                    records.add(record);
                }
            } catch (Exception e) {
                log.warn("解析对账单行数据异常，行内容：{}，异常信息：{}", line, e.getMessage());
            }
        }

        return records;
    }

    /**
     * 解析对账单行数据
     */
    private ReconciliationRecord parseReconciliationLine(String line, String reconciliationDate, String instNo) {
        if (!StringUtils.hasText(line)) {
            return null;
        }

        // 使用自定义方法分割字段，正确处理包含JSON的字段
        String[] fields = parseCSVLine(line);
        // 根据文档，对账单格式为：
        // 商户号，终端号，交易时间，退款完成时间，交易金额（分），手续费金额（分），退款金额（分），结余金额（分），
        // 支付方式，支付类型，交易状态，交易单号，退款原单号，终端流水号，渠道订单号，交易日期，用户标识，
        // 银行卡类型，附加数据，商家优惠金额（分），商家实收金额（分），用户实付金额（分），平台优惠金额（分），
        // 订单备注，自定义设备编号，支付银行标识，门店名称，通道类型

        if (fields.length < 27) {
            log.warn("对账单行数据字段不足，期望27个字段，实际{}个字段，行内容：{}", fields.length, line);
            return null;
        }

        try {
            ReconciliationRecord record = new ReconciliationRecord();
            record.setReconciliationDate(reconciliationDate);
            record.setInstNo(instNo);
            record.setMchNo(getFieldValue(fields, 0));
            record.setTerminalNo(getFieldValue(fields, 1));

            // 解析交易时间
            String transactionTimeStr = getFieldValue(fields, 2);
            if (StringUtils.hasText(transactionTimeStr)) {
                try {
                    record.setTransactionTime(LocalDateTime.parse(transactionTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                } catch (Exception e) {
                    log.warn("解析交易时间失败：{}", transactionTimeStr);
                }
            }

            // 解析退款完成时间
            String refundCompleteTimeStr = getFieldValue(fields, 3);
            if (StringUtils.hasText(refundCompleteTimeStr)) {
                try {
                    record.setRefundCompleteTime(LocalDateTime.parse(refundCompleteTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                } catch (Exception e) {
                    log.warn("解析退款完成时间失败：{}", refundCompleteTimeStr);
                }
            }

            //商户号，终端号，交易时间，退款完成时间，交易金额（分），手续费金额（分），退款金额（分），结余金额（分），
            // 支付方式，支付类型，交易状态，
            // 交易单号，退款原单号，终端流水号，渠道订单号，交易日期，用户标识，银行卡类型，附加数据，商家优惠金额（分），商家实收金额（分），
            // 用户实付金额（分），平台优惠金额（分）,订单备注，自定义设备编号，支付银行标识，门店名称，通道类型
            record.setTransactionAmount(parseLongField(fields, 4));
            record.setFeeAmount(parseLongField(fields, 5));
            record.setRefundAmount(parseLongField(fields, 6));
            record.setBalanceAmount(parseLongField(fields, 7));
            record.setPayMethod(parseIntegerField(fields, 8));
            record.setPaymentType(parseIntegerField(fields, 9));
            record.setTransactionState(parseIntegerField(fields, 10));
            record.setTransactionOrderNo(getFieldValue(fields, 11));
            record.setRefundOriginalOrderNo(getFieldValue(fields, 12));
            record.setTerminalSerialNo(getFieldValue(fields, 13));
            record.setChannelOrderNo(getFieldValue(fields, 14));
            record.setTransactionDate(getFieldValue(fields, 15));
            record.setUserIdentifier(getFieldValue(fields, 16));
            record.setBankCardType(parseIntegerField(fields, 17));
            String fieldValue = getFieldValue(fields, 18);
            if (StrUtil.isNotBlank(fieldValue)) {
                fieldValue = fieldValue.replaceAll("\"\"", "\"");
            }
            record.setAdditionalData(fieldValue);
            record.setMerchantDiscountAmount(parseLongField(fields, 19));
            record.setMerchantActualAmount(parseLongField(fields, 20));
            record.setUserPaidAmount(parseLongField(fields, 21));
            record.setPlatformDiscountAmount(parseLongField(fields, 22));
            record.setOrderRemark(getFieldValue(fields, 23));
            record.setCustomDeviceNo(getFieldValue(fields, 24));
            record.setPaymentBankCode(getFieldValue(fields, 25));
            record.setStoreName(getFieldValue(fields, 26));
            record.setCount(1);
            if (fields.length > 27) {
                record.setChannelType(parseIntegerField(fields, 27));
            }

            // 保存原始数据
            record.setRawData(line);
            // 已下载
            record.setDownloadStatus(1);
            log.info("解析对账单行数据，行内容：{}", JSON.toJSONString(record));
            return record;
        } catch (Exception e) {
            log.error("解析对账单行数据异常，行内容：{}，异常信息：{}", line, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 自定义CSV行解析方法，能够正确处理包含JSON字符串的字段
     * 支持双引号包围的字段和JSON字符串中的逗号
     */
    private String[] parseCSVLine(String line) {
        if (line == null || line.trim().isEmpty()) {
            return new String[0];
        }

        List<String> fields = new ArrayList<>();
        StringBuilder currentField = new StringBuilder();
        boolean inQuotes = false;
        boolean inBraces = false;
        int braceCount = 0;

        for (int i = 0; i < line.length(); i++) {
            char c = line.charAt(i);

            switch (c) {
                case '"':
                    // 处理双引号
                    if (!inBraces) {
                        inQuotes = !inQuotes;
                    }
                    currentField.append(c);
                    break;
                case '{':
                    // 进入JSON对象
                    if (!inQuotes) {
                        inBraces = true;
                        braceCount = 1;
                    } else if (inBraces) {
                        braceCount++;
                    }
                    currentField.append(c);
                    break;
                case '}':
                    // 退出JSON对象
                    if (inBraces && !inQuotes) {
                        braceCount--;
                        if (braceCount == 0) {
                            inBraces = false;
                        }
                    }
                    currentField.append(c);
                    break;
                case ',':
                    // 逗号处理
                    if (!inQuotes && !inBraces) {
                        // 不在引号或大括号中，这是字段分隔符
                        fields.add(currentField.toString().trim());
                        currentField.setLength(0);
                    } else {
                        // 在引号或大括号中，这是字段内容的一部分
                        currentField.append(c);
                    }
                    break;
                default:
                    currentField.append(c);
                    break;
            }
        }

        // 添加最后一个字段
        fields.add(currentField.toString().trim());

        // 清理字段值，移除可能的外层双引号
        for (int i = 0; i < fields.size(); i++) {
            String field = fields.get(i);
            if (field.startsWith("\"") && field.endsWith("\"") && field.length() > 1) {
                fields.set(i, field.substring(1, field.length() - 1));
            }
        }

        return fields.toArray(new String[0]);
    }

    /**
     * 获取字段值
     */
    private String getFieldValue(String[] fields, int index) {
        if (index >= 0 && index < fields.length) {
            String value = fields[index].trim();
            return StringUtils.hasText(value) ? value : null;
        }
        return null;
    }

    /**
     * 解析Long类型字段
     */
    private Long parseLongField(String[] fields, int index) {
        String value = getFieldValue(fields, index);
        if (StringUtils.hasText(value)) {
            try {
                return Long.parseLong(value);
            } catch (NumberFormatException e) {
                log.warn("解析Long字段失败，索引：{}，值：{}", index, value);
            }
        }
        return null;
    }

    /**
     * 解析Integer类型字段
     */
    private Integer parseIntegerField(String[] fields, int index) {
        String value = getFieldValue(fields, index);
        if (StringUtils.hasText(value)) {
            try {
                return Integer.parseInt(value);
            } catch (NumberFormatException e) {
                log.warn("解析Integer字段失败，索引：{}，值：{}", index, value);
            }
        }
        return null;
    }

    public String generateDownloadSignature(String reconciliationDate, String instNo, String instKey) {
        // 根据文档：MD5加密，utf-8格式转小写,加密后得到key_sign的值
        // 签名参数：day=日期值&inst_no=机构号值&key=机构密钥值
        String signData = String.format("day=%s&inst_no=%s&key=%s", reconciliationDate, instNo, instKey);

        try {
            String md5Hex = DigestUtils.md5DigestAsHex(signData.getBytes(StandardCharsets.UTF_8));
            return md5Hex.toLowerCase();
        } catch (Exception e) {
            log.error("生成对账单下载签名异常，签名数据：{}，异常信息：{}", signData, e.getMessage(), e);
            throw new RuntimeException("生成签名失败", e);
        }
    }

    public String buildDownloadUrl(ReconciliationDownloadQuery query, ReconciliationProperties.Config config) {
        try {
            log.info("开始下载对账单，日期：{}，机构号：{}", query.getReconciliationDate(), config.getInstNo());

            // 生成签名
            String keySign = generateDownloadSignature(query.getReconciliationDate(), config.getInstNo(), config.getInstKey());
            // 对机构号进行URL编码
            String encodedInstNo = URLEncoder.encode(config.getInstNo(), StandardCharsets.UTF_8.name());
            String fileName = String.format("%s_%s.txt", encodedInstNo, query.getReconciliationDate());

            // 构建URL
            // URL格式：https://example.com/order/day/inst_no/key_sign/inst_no_day.txt
            String url = String.format("%s/order/%s/%s/%s/%s",
                    // 移除末尾的斜杠
                    reconciliationProperties.getDownloadUrl()
                            .replaceAll("/$", ""),
                    query.getReconciliationDate(),
                    encodedInstNo,
                    keySign,
                    fileName
            );

            return url;
        } catch (UnsupportedEncodingException e) {
            log.error("构建对账单下载URL异常，异常信息：{}", e.getMessage(), e);
            throw new RuntimeException("构建下载URL失败", e);
        }
    }

    /**
     * 将ReconciliationRecord列表根据指定字段进行分组并合并交易金额和退款数据
     *
     * @param records 原始记录列表
     * @return 分组合并后的Map，key为分组键，value为合并后的ReconciliationRecord
     */
    public Map<String, ReconciliationRecord> groupAndMergeRecords(List<ReconciliationRecord> records) {
        if (records == null || records.isEmpty()) {
            return new HashMap<>();
        }
        return records.stream().collect(
                Collectors.groupingBy(
                        // 分组键：对账日期_机构号_商户号
                        record -> buildGroupKey(record.getReconciliationDate(), record.getInstNo(), record.getMchNo()),
                        Collectors.reducing(
                                // 初始值
                                new ReconciliationRecord(),
                                // 映射函数，直接返回原对象
                                Function.identity(),
                                // 合并函数
                                (prev, curr) -> ReconciliationRecord.merge(curr, prev)
                        )
                )
        );
    }

    /**
     * 根据不同分组策略进行分组合并
     *
     * @return 分组合并后的Map
     */
    private String buildGroupKey(String reconciliationDate, String instNo, String mchNo) {
        return String.format("%s_%s_%s",
                Optional.ofNullable(reconciliationDate).orElse(""),
                Optional.ofNullable(instNo).orElse(""),
                Optional.ofNullable(mchNo).orElse(""));
    }

    /**
     * 解析分组键，获取对账日期、机构号、商户号
     *
     * @param groupKey 分组键
     * @return 包含三个元素的数组：[对账日期, 机构号, 商户号]
     */
    private String[] parseGroupKey(String groupKey) {
        if (StringUtils.hasText(groupKey)) {
            return groupKey.split("_");
        }
        return new String[]{"", "", ""};
    }

    public String downloadAndSaveReconciliationTask() {
        LocalDate localDate = LocalDate.now().minusDays(1);
        String time = DateUtils.format(localDate, "yyyyMMdd");
        ReconciliationDownloadQuery query = new ReconciliationDownloadQuery();
        query.setReconciliationDate(time);
        return downloadAndSaveReconciliation(query);
    }

}
