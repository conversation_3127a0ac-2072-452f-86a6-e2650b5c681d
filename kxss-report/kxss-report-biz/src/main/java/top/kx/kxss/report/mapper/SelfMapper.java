package top.kx.kxss.report.mapper;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.kxss.report.vo.result.self.SelfServiceDetailResultVO;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 *
 * </p>
 *
 * <AUTHOR>
 */
@Repository
public interface SelfMapper {

    List<SelfServiceDetailResultVO> getSelfServiceDetail(@Param("model") DataOverviewQuery model);

}


