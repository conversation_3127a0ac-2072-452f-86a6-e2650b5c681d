package top.kx.kxss.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.vo.query.reward.RewardEmployeeIdQuery;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.employee.EmployeeApi;
import top.kx.kxss.file.api.FileApi;
import top.kx.kxss.model.enumeration.base.ServiceStaffTimeEnum;
import top.kx.kxss.parameter.ParameterApi;
import top.kx.kxss.report.mapper.SelfMapper;
import top.kx.kxss.report.service.SelfService;
import top.kx.kxss.report.vo.result.self.SelfServiceDetailResultVO;
import top.kx.kxss.report.vo.result.self.SelfServiceResultVO;
import top.kx.kxss.reward.RewardOrderApi;
import top.kx.kxss.wxapp.api.custom.CustomApi;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 个人API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class SelfServiceImpl implements SelfService {

    private final CustomApi customApi;
    private final SelfMapper selfMapper;
    private final EmployeeApi employeeApi;
    private final EchoService echoService;
    private final FileApi fileApi;
    private final ParameterApi parameterApi;
    private final RewardOrderApi rewardOrderApi;

    @Override
    public SelfServiceResultVO service(DataOverviewQuery query) {
        R<DataOverviewQuery> storeTime = customApi.getStoreTime(query);
        if (!storeTime.getIsSuccess()) {
            return null;
        }
        DataOverviewQuery storeTimeData = storeTime.getData();
        query.setStartDate(storeTimeData.getStartDate());
        query.setEndDate(storeTimeData.getEndDate());
        query.setOrgIdList(Collections.singletonList(ContextUtil.getCurrentCompanyId()));

        List<SelfServiceDetailResultVO> selfServiceDetail = selfMapper.getSelfServiceDetail(query);
        R<ServiceStaffTimeEnum> serviceStaffTimeEnumR = parameterApi.getServiceStaffTime();
        ServiceStaffTimeEnum serviceStaffTimeEnum = serviceStaffTimeEnumR.getData();
        selfServiceDetail.forEach(s -> {
            s.setCycleDuration(serviceDurationDesc(s.getCycleDurationMinute(), serviceStaffTimeEnum));
            s.setServiceDuration(serviceDurationDesc(s.getServiceDurationMinute(), serviceStaffTimeEnum));
        });
        Map<Long, List<SelfServiceDetailResultVO>> detailEmployeeMap = selfServiceDetail.stream()
                .collect(Collectors.groupingBy(SelfServiceDetailResultVO::getEmployeeId));
        List<SelfServiceResultVO> employeeList = new ArrayList<>();
        for (Long employeeId : detailEmployeeMap.keySet()) {
            List<SelfServiceDetailResultVO> detailList = detailEmployeeMap.get(employeeId);
            SelfServiceResultVO selfServiceResultVO = BeanPlusUtil.toBean(detailList.get(0), SelfServiceResultVO.class);
            selfServiceResultVO.setOrgId(ContextUtil.getCurrentCompanyId());
            selfServiceResultVO.setTotalCycleDurationMinute(detailList.stream().mapToInt(SelfServiceDetailResultVO::getCycleDurationMinute).sum());
            selfServiceResultVO.setTotalServiceDurationMinute(detailList.stream().mapToInt(SelfServiceDetailResultVO::getServiceDurationMinute).sum());
            selfServiceResultVO.setTotalClockNum(detailList.stream().mapToInt(SelfServiceDetailResultVO::getClockNum).sum());
            employeeList.add(selfServiceResultVO);
        }
        // 上钟的员工
        // 根据计费时长排行
        employeeList = employeeList.stream()
                .sorted(Comparator.comparing(SelfServiceResultVO::getTotalCycleDurationMinute, Comparator.reverseOrder())
                        .thenComparing(SelfServiceResultVO::getEmployeeId))
                .collect(Collectors.toList());

        R<List<BaseEmployeeResultVO>> allEmployee = employeeApi.findAllEmployee();
        List<BaseEmployeeResultVO> employeeResultVOList = new ArrayList<>();
        if (allEmployee.getIsSuccess()) {
            employeeResultVOList = allEmployee.getData();
        }
        BaseEmployeeResultVO baseEmployeeResultVO = employeeResultVOList.stream().filter(s -> Objects.equals(s.getId(), ContextUtil.getEmployeeId())).findFirst().orElse(null);
        if (Objects.isNull(baseEmployeeResultVO)) {
            return null;
        }
        Map<Long, List<BaseEmployeeResultVO>> groupEmployeeMap = employeeResultVOList.stream().filter(s -> Objects.nonNull(s.getGroupId())).collect(Collectors.groupingBy(BaseEmployeeResultVO::getGroupId));
        R<BaseEmployeeResultVO> employeeResultVOR = employeeApi.get(baseEmployeeResultVO.getId());
        SelfServiceResultVO resultVO = SelfServiceResultVO.builder()
                .echoMap(MapUtil.newHashMap()).sex(baseEmployeeResultVO.getSex())
                .number(baseEmployeeResultVO.getNumber())
                .realName(baseEmployeeResultVO.getRealName())
                .employeeId(ContextUtil.getEmployeeId())
                .avatar(baseEmployeeResultVO.getAvatar())
                .orgId(ContextUtil.getCurrentCompanyId())
                .positionId(baseEmployeeResultVO.getPositionId())
                .groupId(baseEmployeeResultVO.getGroupId())
                .totalClockNum(0)
                .totalCycleDuration("0")
                .totalServiceDuration("0")
                .groupEmployeeNum(groupEmployeeMap.containsKey(baseEmployeeResultVO.getGroupId()) ? groupEmployeeMap.get(baseEmployeeResultVO.getGroupId()).size() : 0)
                .orgRank(0)
                .groupRank(0)
                .detailList(Collections.emptyList())
                .build();
        if (employeeResultVOR.getIsSuccess()
                && ObjectUtil.isNotNull(employeeResultVOR.getData())) {
            resultVO.setSex(employeeResultVOR.getData().getSex());
        }
        R<Map<Long, String>> photoR = fileApi.findUrlById(Collections.singletonList(baseEmployeeResultVO.getPhotoId()));
        if (photoR.getIsSuccess()) {
            resultVO.setAvatar(photoR.getData().get(baseEmployeeResultVO.getPhotoId()));
        }

        RewardEmployeeIdQuery build = new RewardEmployeeIdQuery();
        build.setEmployeeId(ContextUtil.getEmployeeId());
        build.setOrgId(ContextUtil.getCurrentCompanyId());
        build.setStartDate(query.getStartDate());
        build.setEndDate(query.getEndDate());
        R<Long> countByEmployeeId = rewardOrderApi.rewardCountByEmployeeId(build);
        Long data = countByEmployeeId.getData();
        resultVO.setRewardCount(data != null ? countByEmployeeId.getData() : 0L);
        SelfServiceResultVO selfServiceResultVO = employeeList.stream().filter(s -> Objects.equals(s.getEmployeeId(), ContextUtil.getEmployeeId())).findFirst().orElse(null);
        if (Objects.isNull(selfServiceResultVO)) {
            echoService.action(resultVO);
            return resultVO;
        }
        resultVO.setTotalServiceDurationMinute(selfServiceResultVO.getTotalServiceDurationMinute());
        resultVO.setTotalCycleDurationMinute(selfServiceResultVO.getTotalCycleDurationMinute());
        resultVO.setTotalClockNum(selfServiceResultVO.getTotalClockNum());
        List<SelfServiceResultVO> finalEmployeeList = employeeList;
        resultVO.setOrgRank(IntStream.range(0, employeeList.size())
                .filter(i -> Objects.equals(finalEmployeeList.get(i).getTotalCycleDurationMinute(), resultVO.getTotalCycleDurationMinute()))
                .findFirst()
                .orElse(-1) + 1);


        // 分钟转小时
        // 分钟 转 xx.xx小时
        resultVO.setTotalCycleDuration(serviceDurationDesc(selfServiceResultVO.getTotalCycleDurationMinute(), serviceStaffTimeEnum));
        resultVO.setTotalServiceDuration(serviceDurationDesc(selfServiceResultVO.getTotalServiceDurationMinute(), serviceStaffTimeEnum));

        if (groupEmployeeMap.containsKey(selfServiceResultVO.getGroupId())) {
            List<BaseEmployeeResultVO> groupEmployeeResultVOS = groupEmployeeMap.get(resultVO.getGroupId());
            List<SelfServiceResultVO> groupServiceList = employeeList.stream().filter(s -> Objects.equals(s.getGroupId(), resultVO.getGroupId())).collect(Collectors.toList());
            resultVO.setGroupEmployeeNum(groupEmployeeResultVOS.size());
            resultVO.setGroupRank(IntStream.range(0, groupServiceList.size())
                    .filter(i -> Objects.equals(groupServiceList.get(i).getTotalCycleDurationMinute(), resultVO.getTotalCycleDurationMinute()))
                    .findFirst()
                    .orElse(-1) + 1);
            if (resultVO.getGroupRank() <= 1) {
                resultVO.setDistanceDuration("-");
            } else {
                int totalCycleDurationDistance = groupServiceList.get(resultVO.getGroupRank() - 2).getTotalCycleDurationMinute() - resultVO.getTotalCycleDurationMinute();
                resultVO.setDistanceDuration(serviceDurationDesc(totalCycleDurationDistance, serviceStaffTimeEnum));
            }
        }

        // 服务明细排行
        List<SelfServiceDetailResultVO> detailResultVOList = detailEmployeeMap.get(resultVO.getEmployeeId());
        List<BaseEmployeeResultVO> baseEmployeeResultVOList = groupEmployeeMap.get(resultVO.getGroupId());
        Map<Long, List<SelfServiceDetailResultVO>> serviceDetailMap = new HashMap<>();
        if (CollUtil.isNotEmpty(baseEmployeeResultVOList)) {
            List<Long> groupEmployeeIds = baseEmployeeResultVOList.stream().map(BaseEmployeeResultVO::getId).collect(Collectors.toList());
            List<SelfServiceDetailResultVO> groupDetailList = selfServiceDetail.stream().filter(s -> groupEmployeeIds.contains(s.getEmployeeId())).collect(Collectors.toList());
            serviceDetailMap = groupDetailList.stream().collect(Collectors.groupingBy(SelfServiceDetailResultVO::getServiceId));
        }

        Map<Long, List<SelfServiceDetailResultVO>> finalServiceDetailMap = serviceDetailMap;
        List<SelfServiceDetailResultVO> serviceDetailResultVOList = detailResultVOList.stream().map(v -> {
            SelfServiceDetailResultVO detailResultVO = BeanPlusUtil.toBean(v, SelfServiceDetailResultVO.class);
            detailResultVO.setServiceDuration(serviceDurationDesc(v.getServiceDurationMinute(), serviceStaffTimeEnum));
            if (CollUtil.isNotEmpty(baseEmployeeResultVOList)) {
                detailResultVO.setGroupNum(baseEmployeeResultVOList.size());
            } else {
                detailResultVO.setGroupNum(0);
            }
            List<SelfServiceDetailResultVO> detailResultVOS = finalServiceDetailMap.get(v.getServiceId());
            List<SelfServiceDetailResultVO> sortServiceDetailList = new ArrayList<>();
            if (CollUtil.isNotEmpty(detailResultVOS)) {
                sortServiceDetailList = detailResultVOS.stream()
                        .sorted(Comparator.comparing(SelfServiceDetailResultVO::getCycleDurationMinute, Comparator.reverseOrder()))
                        .collect(Collectors.toList());
            }

            List<SelfServiceDetailResultVO> finalSortServiceDetailList = sortServiceDetailList;
            detailResultVO.setGroupRank(IntStream.range(0, sortServiceDetailList.size())
                    .filter(i -> Objects.equals(finalSortServiceDetailList.get(i).getCycleDurationMinute(), v.getCycleDurationMinute()))
                    .findFirst()
                    .orElse(-1) + 1);
            if (detailResultVO.getGroupRank() <= 1) {
                detailResultVO.setDistanceDuration("-");
            } else {
                int cycleDurationDuration = sortServiceDetailList.get(detailResultVO.getGroupRank() - 2).getCycleDurationMinute() - v.getCycleDurationMinute();
                detailResultVO.setDistanceDuration(serviceDurationDesc(cycleDurationDuration, serviceStaffTimeEnum));
            }
            return detailResultVO;
        }).collect(Collectors.toList());
        resultVO.setDetailList(serviceDetailResultVOList);
        echoService.action(resultVO);
        echoService.action(resultVO.getDetailList());
        return resultVO;
    }

    private String serviceDurationDesc(Integer duration, ServiceStaffTimeEnum defaultServiceStaffTime) {
        if (Objects.isNull(duration)) {
            return null;
        }
        if (Objects.isNull(defaultServiceStaffTime)) {
            return duration + "分钟";
        }
        switch (defaultServiceStaffTime) {
            case HOUR:
                return String.format("%.2f h", (duration / 60.0));
            case HOUR_MINUTE:
                return duration > 60 ? (duration % 60 == 0 ? StrUtil.format("{}小时", duration / 60) : StrUtil.format("{}小时{}分钟", duration / 60, duration % 60)) : StrUtil.format("{}分钟", duration);
            default:
                return duration + "分钟";
        }
    }

}

