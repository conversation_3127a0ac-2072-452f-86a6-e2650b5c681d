package top.kx.kxss.report.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.report.service.reconciliation.ReconciliationDownloadService;

import java.time.LocalDateTime;

/**
 * 订单过期定时任务
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ReconciliationRecordTask {

    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private ReconciliationDownloadService reconciliationDownloadService;

    // 每天凌晨7点执行
    @Scheduled(cron = "0 30 9 * * *")
    public void start() {
        String lockKey = "reconciliation_record_task";
        boolean lock = false;
        try {
            lock = distributedLock.lock(lockKey, 0);
            if (!lock) {
                return;
            }
            String s = reconciliationDownloadService.downloadAndSaveReconciliationTask();
            log.info("拉取时间：{},扫码对帐单拉取成功:{}", DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_TIME_FORMAT), s);
        } finally {
            ContextUtil.remove();
            distributedLock.releaseLock(lockKey);
        }
    }


}
