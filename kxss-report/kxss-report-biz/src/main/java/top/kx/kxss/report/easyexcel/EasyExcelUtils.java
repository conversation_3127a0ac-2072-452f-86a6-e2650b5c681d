package top.kx.kxss.report.easyexcel;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * EasyExcel 工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class EasyExcelUtils {

    /**
     * 单表导出（使用新的颜色处理器）
     *
     * @param fileName     文件名
     * @param data         数据列表
     * @param clazz        数据类型
     * @param colorHandler 颜色处理器
     * @param response     HTTP响应
     */
    public static <T> void exportWithColorHandler(String fileName, List<T> data, Class<T> clazz,
                                                  CellColorHandler colorHandler, HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            fileName = StrUtil.isNotBlank(fileName) ? fileName : DateUtil.now().concat(".xlsx");
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);

            EasyExcel.write(response.getOutputStream(), clazz)
                    .sheet("数据列表")
                    .registerWriteHandler(colorHandler)
                    .doWrite(data);

        } catch (Exception e) {
            handleExportError(response, e);
        }
    }

    /**
     * 单表导出（使用构建器模式配置颜色）
     *
     * @param fileName           文件名
     * @param data               数据列表
     * @param clazz              数据类型
     * @param colorBuilderConfig 颜色构建器配置函数
     * @param response           HTTP响应
     */
    public static <T> void exportWithColorBuilder(String fileName, List<T> data, Class<T> clazz,
                                                  ColorBuilderConfig colorBuilderConfig, HttpServletResponse response) {
        try {
            CellColorHandlerBuilder builder = new CellColorHandlerBuilder();
            if (colorBuilderConfig != null) {
                builder = colorBuilderConfig.configure(builder);
            }
            CellColorHandler colorHandler = builder.build();

            exportWithColorHandler(fileName, data, clazz, colorHandler, response);

        } catch (Exception e) {
            handleExportError(response, e);
        }
    }

    /**
     * 快速导出 - 状态订单场景
     *
     * @param fileName          文件名
     * @param data              数据列表
     * @param clazz             数据类型
     * @param statusColumnIndex 状态列索引
     * @param amountColumnIndex 金额列索引（可选，-1表示不设置）
     * @param response          HTTP响应
     */
    public static <T> void exportOrderStatus(String fileName, List<T> data, Class<T> clazz,
                                             int statusColumnIndex, int amountColumnIndex, HttpServletResponse response) {
        exportWithColorBuilder(fileName, data, clazz, builder -> {
            builder.statusColumn(statusColumnIndex)
                    .statusRowColor(statusColumnIndex);

            if (amountColumnIndex >= 0) {
                builder.amountColumn(amountColumnIndex);
            }

            return builder;
        }, response);
    }

    /**
     * 快速导出 - 财务报表场景
     *
     * @param fileName              文件名
     * @param data                  数据列表
     * @param clazz                 数据类型
     * @param amountColumnIndex     金额列索引
     * @param percentageColumnIndex 百分比列索引（可选，-1表示不设置）
     * @param response              HTTP响应
     */
    public static <T> void exportFinancialReport(String fileName, List<T> data, Class<T> clazz,
                                                 int amountColumnIndex, int percentageColumnIndex, HttpServletResponse response) {
        exportWithColorBuilder(fileName, data, clazz, builder -> {
            builder.amountColumn(amountColumnIndex)
                    .amountRowColor(amountColumnIndex);

            if (percentageColumnIndex >= 0) {
                builder.percentageColumn(percentageColumnIndex);
            }

            return builder;
        }, response);
    }

    /**
     * 快速导出 - 任务优先级场景
     *
     * @param fileName            文件名
     * @param data                数据列表
     * @param clazz               数据类型
     * @param priorityColumnIndex 优先级列索引
     * @param statusColumnIndex   状态列索引（可选，-1表示不设置）
     * @param response            HTTP响应
     */
    public static <T> void exportTaskPriority(String fileName, List<T> data, Class<T> clazz,
                                              int priorityColumnIndex, int statusColumnIndex, HttpServletResponse response) {
        exportWithColorBuilder(fileName, data, clazz, builder -> {
            builder.priorityColumn(priorityColumnIndex);

            if (statusColumnIndex >= 0) {
                builder.statusColumn(statusColumnIndex);
            }

            return builder;
        }, response);
    }

    /**
     * 原有的单表导出方法（保持兼容性）
     */
    public static <T> void singleExport(String fileName, List<T> data, Class<T> clazz,
                                        int[] mergeColumIndex, HttpServletResponse response) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            fileName = StrUtil.isNotBlank(fileName) ? fileName : DateUtil.now().concat(".xlsx");
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename=" + fileName);
            CellColorHandler.StyleConfig styleConfig = new CellColorHandler.StyleConfig();
            styleConfig.setFontName("微软雅黑");
            styleConfig.setHeaderFontSize((short) 12);
            styleConfig.setDataFontSize((short) 9);
            styleConfig.setHeaderRowHeight(null);
            styleConfig.setDataRowHeight(null);
            styleConfig.setColumnWidth(null);
            // 创建颜色处理器
            CellColorHandler colorHandler = new CellColorHandlerBuilder(styleConfig)
                    // 整行颜色规则（优先级高于单元格颜色）
                    .rowStringEquals(0, "不一致", CellColorHandlerBuilder.Colors.LIGHT_GREEN)
                    .build();
            // 导出Excel
            EasyExcel.write(response.getOutputStream(), clazz)
                    .sheet("数据列表")
                    .registerWriteHandler(colorHandler)
                    .doWrite(data);

        } catch (Exception e) {
            handleExportError(response, e);
        }
    }

    /**
     * 处理导出错误
     */
    private static void handleExportError(HttpServletResponse response, Exception e) {
        log.error("导出Excel失败", e);
        response.reset();
        response.setContentType("application/json");
        response.setCharacterEncoding("utf-8");
        try {
            Map<String, String> errorMap = new HashMap<>();
            errorMap.put("status", "failure");
            errorMap.put("message", "下载文件失败: " + e.getMessage());
            response.getWriter().write(errorMap.toString());
        } catch (Exception ex) {
            log.error("写入错误响应失败", ex);
        }
    }

    /**
     * 颜色构建器配置接口
     */
    @FunctionalInterface
    public interface ColorBuilderConfig {
        CellColorHandlerBuilder configure(CellColorHandlerBuilder builder);
    }

    /**
     * 创建常用的颜色处理器
     */
    public static class CommonColorHandlers {

        /**
         * 创建订单状态颜色处理器
         */
        public static CellColorHandler createOrderStatusHandler(int statusColumnIndex) {
            return new CellColorHandlerBuilder()
                    .statusColumn(statusColumnIndex)
                    .statusRowColor(statusColumnIndex)
                    .build();
        }

        /**
         * 创建财务报表颜色处理器
         */
        public static CellColorHandler createFinancialHandler(int amountColumnIndex, int percentageColumnIndex) {
            CellColorHandlerBuilder builder = new CellColorHandlerBuilder()
                    .amountColumn(amountColumnIndex)
                    .amountRowColor(amountColumnIndex);

            if (percentageColumnIndex >= 0) {
                builder.percentageColumn(percentageColumnIndex);
            }

            return builder.build();
        }

        /**
         * 创建任务管理颜色处理器
         */
        public static CellColorHandler createTaskHandler(int priorityColumnIndex, int statusColumnIndex) {
            CellColorHandlerBuilder builder = new CellColorHandlerBuilder()
                    .priorityColumn(priorityColumnIndex);

            if (statusColumnIndex >= 0) {
                builder.statusColumn(statusColumnIndex);
            }

            return builder.build();
        }

        /**
         * 创建审核流程颜色处理器
         */
        public static CellColorHandler createAuditHandler(int statusColumnIndex, int resultColumnIndex) {
            return new CellColorHandlerBuilder()
                    .cellStringEquals(statusColumnIndex, "待审核", CellColorHandlerBuilder.Colors.YELLOW)
                    .cellStringEquals(statusColumnIndex, "审核中", CellColorHandlerBuilder.Colors.ORANGE)
                    .cellStringEquals(statusColumnIndex, "已审核", CellColorHandlerBuilder.Colors.GREEN)
                    .cellStringEquals(statusColumnIndex, "已驳回", CellColorHandlerBuilder.Colors.RED)
                    .booleanColumn(resultColumnIndex)
                    .build();
        }

        /**
         * 创建库存管理颜色处理器
         */
        public static CellColorHandler createInventoryHandler(int stockColumnIndex, int statusColumnIndex) {
            return new CellColorHandlerBuilder()
                    .cellNumberLessThan(stockColumnIndex, 10.0, CellColorHandlerBuilder.Colors.RED)
                    .cellNumberLessThan(stockColumnIndex, 50.0, CellColorHandlerBuilder.Colors.YELLOW)
                    .cellNumberGreaterEquals(stockColumnIndex, 100.0, CellColorHandlerBuilder.Colors.GREEN)
                    .cellStringEquals(statusColumnIndex, "缺货", CellColorHandlerBuilder.Colors.RED)
                    .cellStringEquals(statusColumnIndex, "正常", CellColorHandlerBuilder.Colors.GREEN)
                    .build();
        }
    }
}
