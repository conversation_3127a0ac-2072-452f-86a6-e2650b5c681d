package top.kx.kxss.report.service.reconciliation.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO;
import top.kx.kxss.base.vo.query.payment.BaseBankCardInfoPageQuery;
import top.kx.kxss.base.vo.result.payment.BankCardInfoResultVO;
import top.kx.kxss.base.vo.result.payment.BaseBankCardInfoResultVO;
import top.kx.kxss.common.api.HelperApi;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.ParameterKey;
import top.kx.kxss.model.enumeration.base.PayChannelEnum;
import top.kx.kxss.pay.MchAppApi;
import top.kx.kxss.pay.vo.query.DayReconciliationQuery;
import top.kx.kxss.pay.vo.result.IsvReconciliationInfoResultVO;
import top.kx.kxss.payment.BaseBankCardInfoApi;
import top.kx.kxss.report.entity.reconciliation.Reconciliation;
import top.kx.kxss.report.manager.reconciliation.ReconciliationManager;
import top.kx.kxss.report.mapper.reconciliation.ReconciliationMapper;
import top.kx.kxss.report.query.reconciliation.StatisticReconciliationQuery;
import top.kx.kxss.report.service.PosCashPaymentService;
import top.kx.kxss.report.service.reconciliation.ReconciliationService;
import top.kx.kxss.report.vo.query.reconciliation.ReconciliationPageQuery;
import top.kx.kxss.report.vo.result.reconciliation.ReconciliationResultVO;
import top.kx.kxss.report.vo.result.reconciliation.StatisticReconciliationResultVO;
import top.kx.kxss.report.vo.save.reconciliation.ReconciliationSaveVO;
import top.kx.kxss.report.vo.update.reconciliation.ReconciliationUpdateVO;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 商户对账单
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-01 15:55:00
 * @create [2025-07-01 15:55:00] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class ReconciliationServiceImpl extends SuperServiceImpl<ReconciliationManager, Long, Reconciliation, ReconciliationSaveVO,
        ReconciliationUpdateVO, ReconciliationPageQuery, ReconciliationResultVO> implements ReconciliationService {

    @Autowired
    private MchAppApi mchAppApi;
    @Autowired
    private BaseBankCardInfoApi baseBankCardInfoApi;
    @Autowired
    private ReconciliationMapper reconciliationMapper;
    @Autowired
    private PosCashPaymentService posCashPaymentService;
    @Autowired
    private EchoService echoService;
    @Autowired
    private HelperApi helperApi;

    @Override
    public boolean remove(LbQueryWrap<Reconciliation> eq) {
        return superManager.remove(eq);
    }


    @Override
    public List<BaseBankCardInfoResultVO> bankCardList() {
        R<List<BaseBankCardInfoResultVO>> query = baseBankCardInfoApi.query(BaseBankCardInfoPageQuery.builder()
                .createdOrgId(ContextUtil.getCurrentCompanyId())
                .status(true)
                .build());

        ArgumentAssert.isFalse(!query.getIsSuccess(), query.getErrorMsg());
        List<BaseBankCardInfoResultVO> data = query.getData();
        if (CollUtil.isEmpty(data)) {
            return Lists.newArrayList();
        }
        return data;
//        IsvReconciliationInfoResultVO resultVO = mchAppApi.queryReconciliationParam("", "");
//        return null;
    }

    @Override
    public List<BankCardInfoResultVO> payChannel() {
        R<List<BaseBankCardInfoResultVO>> query = baseBankCardInfoApi.query(BaseBankCardInfoPageQuery.builder()
                .createdOrgId(ContextUtil.getCurrentCompanyId())
                .status(true)
                .build());
        ArgumentAssert.isFalse(!query.getIsSuccess(), query.getErrorMsg());
        List<BaseBankCardInfoResultVO> data = query.getData();
        if (CollUtil.isEmpty(data)) {
            return Lists.newArrayList();
        }
        List<BankCardInfoResultVO> bankCardInfoResultVOList = BeanUtil.copyToList(data, BankCardInfoResultVO.class);
        echoService.action(bankCardInfoResultVOList);
        return bankCardInfoResultVOList;
    }

    @Override
    public StatisticReconciliationResultVO statistic(StatisticReconciliationQuery query) {
        Long tenantId = ContextUtil.getTenantId();
        ArgumentAssert.notNull(tenantId, "租户不存在");
        Long companyId = ContextUtil.getCurrentCompanyId();
        ArgumentAssert.notNull(companyId, "门店不存在");
        IsvReconciliationInfoResultVO reconciliationInfo = reconciliationInfo(query.getBankCardId());
        query.setMchNo(reconciliationInfo.getMchNo());
        query.setInstNo(reconciliationInfo.getInstNo());
        DataOverviewQuery build = DataOverviewQuery.builder().build();
        build.setStartDate(DateUtil.format(DateUtil.parse(query.getStartTime()), DatePattern.NORM_DATETIME_PATTERN));
        DateTime endTime = DateUtil.parse(query.getEndTime());
        endTime.setField(DateField.MINUTE, 59);
        endTime.setField(DateField.SECOND, 59);
        build.setEndDate(DateUtil.format(endTime, DatePattern.NORM_DATETIME_PATTERN));
        query.setStartTime(build.getStartDate());
        query.setEndTime(build.getEndDate());
        //商户对账单
        StatisticReconciliationResultVO resultVO = reconciliationMapper.statistic(query);
        if (resultVO == null) {
            resultVO = StatisticReconciliationResultVO.builder()
                    .feeAmount(BigDecimal.ZERO).systemAmount(BigDecimal.ZERO)
                    .recordedAmount(BigDecimal.ZERO).transactionAmount(BigDecimal.ZERO)
                    .reconciliationResults(true).transactionOrderNum(0)
                    .build();
        }
        reconciliationResultVO(resultVO);
        resultVO.setSystemAmount(BigDecimal.ZERO);
        //系统对账单
        PosCashPaymentResultVO paymentResultVO = posCashPaymentService.selectOnePayAmount(build, ContextUtil.getCurrentCompanyId());
        if (paymentResultVO != null && paymentResultVO.getAmount() != null) {
            resultVO.setSystemAmount(paymentResultVO.getAmount());
        }
        resultVO.setReconciliationResults(resultVO.getSystemAmount().compareTo(resultVO.getTransactionAmount()) == 0);
        return resultVO;
    }

    private void reconciliationResultVO(StatisticReconciliationResultVO resultVO) {
        if (resultVO == null) {
            return;
        }
        if (resultVO.getFeeAmount() != null) {
            resultVO.setFeeAmount(resultVO.getFeeAmount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        }
        if (resultVO.getRecordedAmount() != null) {
            resultVO.setRecordedAmount(resultVO.getRecordedAmount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        }
        if (resultVO.getSystemAmount() != null) {
            resultVO.setSystemAmount(resultVO.getSystemAmount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        }
        if (resultVO.getTransactionAmount() != null) {
            resultVO.setTransactionAmount(resultVO.getTransactionAmount().divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        }
    }

    @Override
    public IsvReconciliationInfoResultVO reconciliationInfo(Long bankCardId) {
        R<BaseBankCardInfoResultVO> detail = baseBankCardInfoApi.detail(bankCardId);
        ArgumentAssert.notNull(detail, "远程服务异常");
        ArgumentAssert.isFalse(!detail.getIsSuccess(), detail.getErrorMsg());
        BaseBankCardInfoResultVO bankCardInfoResultVO = detail.getData();
        echoService.action(bankCardInfoResultVO);
        PayChannelEnum payChannelEnum = PayChannelEnum.get(bankCardInfoResultVO.getPayChannel());
        ArgumentAssert.notNull(payChannelEnum, "支付渠道不存在");
        R<IsvReconciliationInfoResultVO> reconciliationInfoResultVO = mchAppApi.queryReconciliationParam(bankCardInfoResultVO.getPlatformAppId(),
                (payChannelEnum.getIndex() + "pay").toLowerCase());
        ArgumentAssert.notNull(reconciliationInfoResultVO, "远程服务异常");
        ArgumentAssert.isFalse(!reconciliationInfoResultVO.getIsSuccess(), reconciliationInfoResultVO.getErrorMsg());
        return reconciliationInfoResultVO.getData();
    }

    @Override
    public List<top.kx.kxss.pay.vo.result.ReconciliationResultVO> dateList(DayReconciliationQuery query) {
        //个性参数 分账时间
        Map<String, String> data = helperApi.findParams(Collections.singletonList(ParameterKey.DISTRIBUTION_TIME)).getData();
        ArgumentAssert.notNull(data, "请配置结算时间");
        String distributionTime = data.get(ParameterKey.DISTRIBUTION_TIME);
        LocalTime localTime = LocalTime.of(Integer.parseInt(distributionTime), 0, 0);
        query.setSettlementTime(localTime.toString());
        query.setStartTime(DateUtils.format(DateUtils.getStartTime(LocalDate.now()
                .minusDays(180).toString()), DateUtils.DEFAULT_DATE_TIME_FORMAT));
        query.setEndTime(DateUtils.format(DateUtils.getEndTime(LocalDate.now().toString()),
                DateUtils.DEFAULT_DATE_TIME_FORMAT));
        R<List<BaseBankCardInfoResultVO>> listR = baseBankCardInfoApi.query(BaseBankCardInfoPageQuery.builder()
                .createdOrgId(ContextUtil.getCurrentCompanyId())
                .build());
        if (!listR.getIsSuccess() || CollUtil.isEmpty(listR.getData())) {
            return Lists.newArrayList();
        }
        query.setMchNoList(listR.getData().stream().map(BaseBankCardInfoResultVO::getMerchantCode)
                .collect(Collectors.toList()));
        if (Objects.equals(query.getType(), "1")) {
            //根据年月获取当月的所有日期
            return reconciliationMapper.dayList(query);
        } else if (Objects.equals(query.getType(), "2")) {
            //根据年份获取当年的所有月份
            return reconciliationMapper.monthList(query);
        }
        return Lists.newArrayList();
    }
}


