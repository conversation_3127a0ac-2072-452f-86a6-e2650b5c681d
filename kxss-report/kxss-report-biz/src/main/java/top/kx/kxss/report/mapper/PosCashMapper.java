package top.kx.kxss.report.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.PosCashCommenter;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.entity.cash.service.PosCashService;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.entity.thail.PosCashThail;
import top.kx.kxss.app.vo.result.cash.AmountResultVO;
import top.kx.kxss.app.vo.result.cash.PosCashResultVO;
import top.kx.kxss.app.vo.result.thail.PosCashThailAmountResultVO;
import top.kx.kxss.report.vo.PosCashItemResultVO;
import top.kx.kxss.report.vo.result.cash.OrderFreeResultVO;
import top.kx.kxss.pos.vo.order.OrderResultVO;
import top.kx.kxss.report.vo.PosCashNoPayDetailsResultVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 *
 * </p>
 *
 * <AUTHOR>
 */
@Repository
public interface    PosCashMapper {


    /**
     * pos_cash 查询
     * @param queryWrapper
     * @return
     */
    AmountResultVO selectOneAmount(@Param(Constants.WRAPPER) QueryWrapper queryWrapper);

    List<AmountResultVO> selectByPayType(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    IPage<PosCashResultVO> selectPageResultVO(IPage<PosCash> page, @Param(Constants.WRAPPER) QueryWrap<PosCash> wrap);

    List<PosCashResultVO> findAllResultVO(@Param(Constants.WRAPPER) QueryWrap<PosCash> wrap);

    PosCashNoPayDetailsResultVO findSumResultVO(@Param(Constants.WRAPPER) QueryWrap<PosCash> wrap);

    List<PosCashCommenter> cashCommenterListByCashIds(List<Long> cashIds);

    List<PosCashTable> cashTableListByCashIds(List<Long> cashIds);

    List<PosCashService> cashServiceListByCashIds(List<Long> cashIds);

    List<PosCashProduct> cashProductListByCashIds(List<Long> cashIds);

    List<PosCashThail> cashThailListByCashIds(List<Long> cashIds);

    IPage<OrderResultVO> thailPage(IPage<PosCash> page, @Param(Constants.WRAPPER) QueryWrap<PosCash> wrap);

    IPage<OrderFreeResultVO> freePage(IPage<Object> page, @Param(Constants.WRAPPER) QueryWrap<PosCash> posCashQueryWrap);

    OrderFreeResultVO freeSum(@Param(Constants.WRAPPER) QueryWrap<PosCash> posCashQueryWrap);

    List<OrderFreeResultVO> freeList(@Param(Constants.WRAPPER) QueryWrap<PosCash> posCashQueryWrap);

    List<PosCashPayment> queryList(@Param(Constants.WRAPPER) QueryWrap<PosCashPayment> wrap);

    List<PosCashThailAmountResultVO> thailAmountList(@Param(Constants.WRAPPER) QueryWrap<PosCashThail> wrap);

    List<PosCashItemResultVO> powerAmountList(@Param(Constants.WRAPPER) QueryWrap<PosCash> wrap);
    List<PosCashItemResultVO> tableAmountList(@Param(Constants.WRAPPER) QueryWrap<PosCash> wrap);
    List<PosCashItemResultVO> serviceAmountList(@Param(Constants.WRAPPER) QueryWrap<PosCash> wrap);
    List<PosCashItemResultVO> productAmountList(@Param(Constants.WRAPPER) QueryWrap<PosCash> wrap);

}


