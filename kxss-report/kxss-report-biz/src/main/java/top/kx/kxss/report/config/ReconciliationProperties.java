package top.kx.kxss.report.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.List;

/**
 * 获取微信小程序配置
 *
 * <AUTHOR>
 */
@Getter
@Setter
@ConfigurationProperties(prefix = ReconciliationProperties.PREFIX)
@RefreshScope
public class ReconciliationProperties {
    public static final String PREFIX = "saobei.reconciliation";

    private String downloadUrl;

    private List<Config> configs;

    @Data
    public static class Config {

        @ApiModelProperty(value = "机构号", required = true)
        private String instNo;

        @ApiModelProperty(value = "机构密钥", required = true)
        private String instKey;

    }
}
