package top.kx.kxss.report.service;

import top.kx.basic.base.request.PageParams;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO;
import top.kx.kxss.report.query.CashPaymentQuery;
import top.kx.kxss.report.query.PaymentTransactionQuery;
import top.kx.kxss.report.vo.CashPaymentResultVO;
import top.kx.kxss.report.vo.PaymentTransactionResultVO;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

import java.util.List;
import java.util.Map;

/**
 * API
 *
 * <AUTHOR>
 */
public interface PosCashPaymentService {

    PosCashPaymentResultVO selectOneRechargeAmount(DataOverviewQuery query);

    /**
     * 聚合支付金额
     *
     * @param query
     * @return
     */
    PosCashPaymentResultVO selectOnePayAmount(DataOverviewQuery query, Long orgId);

    List<PaymentTransactionResultVO> selectPayAmount(DataOverviewQuery query, Long orgId);

    PosCashPaymentResultVO selectOneRechargeCardAmount(DataOverviewQuery query);

    List<PosCashPaymentResultVO> selectListAmountByPayTypeId(DataOverviewQuery query);

    Map<String, Object> transaction(PageParams<PaymentTransactionQuery> params);

    PaymentTransactionResultVO transactionSum(PaymentTransactionQuery params);

    List<PaymentTransactionResultVO> transactionList(PaymentTransactionQuery params);

    List<CashPaymentResultVO> cashPaymentList(CashPaymentQuery query);

    List<CashPaymentResultVO> cashList(CashPaymentQuery build);
}
