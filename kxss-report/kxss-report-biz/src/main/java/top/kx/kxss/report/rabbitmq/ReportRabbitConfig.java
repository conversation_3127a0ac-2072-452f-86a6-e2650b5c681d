package top.kx.kxss.report.rabbitmq;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import top.kx.kxss.common.constant.RabbitMqConstant;

/**
 * <AUTHOR>
 */
@Configuration
public class ReportRabbitConfig {

    @Bean
    public Queue yearEndQueue() {
        return new Queue(RabbitMqConstant.YEAR_END);
    }


    @Bean
    TopicExchange exchange() {
        return new TopicExchange(RabbitMqConstant.REPORT_EXCHANGE);
    }

    @Bean
    Binding yearEndMessage() {
        return BindingBuilder.bind(yearEndQueue()).to(exchange()).with(RabbitMqConstant.YEAR_END);
    }
}
