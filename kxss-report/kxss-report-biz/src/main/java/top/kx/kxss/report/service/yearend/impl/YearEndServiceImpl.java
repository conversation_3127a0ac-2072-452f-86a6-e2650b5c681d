package top.kx.kxss.report.service.yearend.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.vo.result.cash.AmountResultVO;
import top.kx.kxss.base.entity.store.BaseStore;
import top.kx.kxss.base.vo.NameValueVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.app.CashTableStatusEnum;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.model.enumeration.base.*;
import top.kx.kxss.report.entity.yearend.YearEnd24;
import top.kx.kxss.report.mapper.yearend.YearEnd24Mapper;
import top.kx.kxss.report.service.yearend.YearEnd24Service;
import top.kx.kxss.report.service.yearend.YearEndService;
import top.kx.kxss.report.vo.yearend.CashAmountResultVO;
import top.kx.kxss.report.vo.yearend.DetailAmountResultVO;
import top.kx.kxss.store.api.BaseStoreApi;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 24年年终总结统计
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class YearEndServiceImpl implements YearEndService {

    private final YearEnd24Mapper yearEnd24Mapper;
    private final YearEnd24Service yearEnd24Service;
    private final BaseStoreApi storeApi;


    @Override
    public void calcData() {
        YearEnd24 build = YearEnd24.builder()
                .tenantId(ContextUtil.getTenantId())
                .orgId(ContextUtil.getCurrentCompanyId())
                .productSales(0).totalDuration(0)
                .build();
        yearEnd24Service.update(Wraps.<YearEnd24>lbU()
                .set(YearEnd24::getDeleteFlag, 1)
                .set(YearEnd24::getUpdatedTime, LocalDateTime.now())
                .eq(YearEnd24::getTenantId, ContextUtil.getTenantId())
                .eq(YearEnd24::getOrgId, ContextUtil.getCurrentCompanyId())
                .eq(YearEnd24::getDeleteFlag, 0)
        );
        LocalDate endTime = LocalDate.of(2024, 12, 31);
        //根据订单日期查询订单
        QueryWrapper<PosCash> wrapper = cashWrapper();
        List<CashAmountResultVO> billState = yearEnd24Mapper.cashListAmount2(wrapper);
        if (CollUtil.isEmpty(billState)) {
            return;
        }
        //总营业额
        build.setTotalAmount(billState.stream().map(CashAmountResultVO::getPayment).filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add));

        wrapper = cashWrapper();
        wrapper.ne("p.type_", PosCashTypeEnum.RECHARGE.getCode())
                .eq("t.delete_flag", 0)
                .ne("t.discount_type", 0);
        List<AmountResultVO> amountResultVOList = yearEnd24Mapper.selectByDiscountType(wrapper);
        if (CollUtil.isNotEmpty(amountResultVOList)) {
            //总优惠金额
            build.setTotalDiscountAmount(amountResultVOList.stream().map(AmountResultVO::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        wrapper = cashWrapper();
        wrapper.ne("p.type_", PosCashTypeEnum.RECHARGE.getCode());
        billState = yearEnd24Mapper.cashListAmount("p.bill_date", wrapper);
        CashAmountResultVO cashAmountResultVO = billState.stream()
                .min(Comparator.comparing(CashAmountResultVO::getField)).get();
        LocalDate parse = LocalDate.parse(cashAmountResultVO.getField());
        //开始是爱
        build.setStartDate(parse);
        long daysBetween = ChronoUnit.DAYS.between(parse, endTime);
        //开店至2024-12-31天数
        build.setTotalDays(Integer.parseInt(String.valueOf(daysBetween)));
        //日最高营业额

        CashAmountResultVO maxCashAmount = Collections.max(billState, Comparator.comparing(CashAmountResultVO::getPayment));
        if (maxCashAmount != null) {
            build.setDayMostAmount(maxCashAmount.getPayment());
            build.setDayMost(maxCashAmount.getField());
        }
        //日均营业额
        if (build.getTotalDays() <= 0) {
            build.setTotalDays(1);
        }
        build.setAvgDayAmount(build.getTotalAmount().divide(BigDecimal.valueOf(build.getTotalDays()),
                2, RoundingMode.HALF_UP));
        //最忙碌月份
        Map<String, Long> monthMap = billState.stream().collect(Collectors.groupingBy(
                CashAmountResultVO::getTime,
                Collectors.summingLong(v -> v.getPaid().multiply(new BigDecimal(100)).longValue())));
        // 获取最大值对应的 key
        Optional<String> maxKey = monthMap.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey);
        if (maxKey.isPresent()) {
            build.setBusyMonth(maxKey.get());
            //最忙碌月份营业总金额
            build.setBusyMouthAmount(new BigDecimal(monthMap.get(maxKey.get()))
                    .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        }
        //月最高营业额
        monthMap = billState.stream().collect(Collectors.groupingBy(
                CashAmountResultVO::getTime,
                Collectors.summingLong(v -> v.getPayment().multiply(new BigDecimal(100)).longValue())));
        // 月最高营业额
        maxKey = monthMap.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey);
        if (maxKey.isPresent()) {
            //月最高营业额
            build.setMonthMost(maxKey.get());
            build.setMonthMostAmount(new BigDecimal(monthMap.get(maxKey.get()))
                    .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP));
        }
        //最高收款的日期
        wrapper = cashWrapper();
        billState = yearEnd24Mapper.cashListAmount("p.bill_date", wrapper);
        maxCashAmount = Collections.max(billState, Comparator.comparing(CashAmountResultVO::getPaid));
        if (maxCashAmount != null) {
            parse = LocalDate.parse(maxCashAmount.getField());
            build.setHighestPaidDate(parse);
        }
        wrapper = cashWrapper();
//        wrapper.isNull("t.cash_thail_id");
        wrapper.eq("t.delete_flag", 0);
        List<DetailAmountResultVO> serviceList = yearEnd24Mapper.serviceList(wrapper);
        DetailAmountResultVO maxDetailAmount;
        build.setMostServiceDurationTable("-");
        if (CollUtil.isNotEmpty(serviceList)) {
            //上钟时长最多的助教
            maxDetailAmount = Collections.max(serviceList, Comparator.comparingInt(DetailAmountResultVO::getBizValue));
            if (maxDetailAmount != null) {
                build.setMostServiceDurationTable(maxDetailAmount.getBizName().contains("-") ? maxDetailAmount.getBizName().split("-")[1]
                        : maxDetailAmount.getBizName());
            }
        }
        List<DetailAmountResultVO> productList = yearEnd24Mapper.productList(wrapper);
        if (CollUtil.isNotEmpty(productList)) {
            //商品总销量
            build.setProductSales(productList.stream().mapToInt(DetailAmountResultVO::getBizValue).sum());
            //销量前三的商品
            String top3Names = productList.stream()
                    .sorted(Comparator.comparingInt(DetailAmountResultVO::getBizValue).reversed())
                    .limit(3)
                    // 只取前3个
                    .map(DetailAmountResultVO::getBizName)
                    .collect(Collectors.joining(","));
            if (StrUtil.isNotBlank(top3Names)) {
                build.setTop3ProductName(top3Names);
            }
            //利润最高的商品
            maxDetailAmount = Collections.max(productList, Comparator.comparing(DetailAmountResultVO::getProfitAmount));
            if (maxDetailAmount != null) {
                build.setHighestProfitProductName(maxDetailAmount.getBizName());
                //利润最高的商品金额
                build.setHighestProfitProductAmount(maxDetailAmount.getProfitAmount());
            }
        }
        wrapper = cashWrapper();
        wrapper.eq("t.delete_flag", 0);
        wrapper.eq("t.status", CashTableStatusEnum.STOP.getCode());
        wrapper.isNull("t.cash_thail_id");
        wrapper.eq("p.type_", PosCashTypeEnum.START_TABLE.getCode());
        List<DetailAmountResultVO> tableList = yearEnd24Mapper.tableList(wrapper);
        if (CollUtil.isNotEmpty(tableList)) {
            //开台总时长
            build.setTotalDuration(tableList.stream().mapToInt(DetailAmountResultVO::getBizValue).sum());
            //最受欢迎的台桌 根据开台时长
            maxDetailAmount = Collections.max(tableList, Comparator.comparingInt(DetailAmountResultVO::getBizValue));
            if (maxDetailAmount != null) {
                build.setMostPopularTable(maxDetailAmount.getBizName());
                build.setMostPopularTableDuration(maxDetailAmount.getBizValue());
            }

        }
        wrapper = cashWrapper();
        wrapper.isNotNull("p.table_id");
        wrapper.eq("p.type_", PosCashTypeEnum.START_TABLE.getCode());
        billState = yearEnd24Mapper.cashListAmount("p.table_id", wrapper);
        if (CollUtil.isNotEmpty(billState)) {
            //营业额最高的台桌
            maxCashAmount = Collections.max(billState, Comparator.comparing(CashAmountResultVO::getPayment));
            if (maxCashAmount != null) {
                build.setMostAmountTable(maxCashAmount.getTableName());
            }
        }
        //开台时长最长的台桌
        wrapper.eq("t.delete_flag", 0);
        wrapper.isNull("t.cash_thail_id");
        wrapper.eq("t.status", CashTableStatusEnum.STOP.getCode());
        DetailAmountResultVO detailAmountResultVO = yearEnd24Mapper.topOneByTable(wrapper);
        if (detailAmountResultVO != null) {
            build.setLastDurationTable(detailAmountResultVO.getBizName());
            //开台最长的台桌时长
            build.setLastDuration(detailAmountResultVO.getBizValue());
        }
        wrapper = bizLogWrapper();
        //开关灯次数
        Long count = yearEnd24Mapper.bizLogCount(wrapper);
        build.setLightTimes(Integer.parseInt(String.valueOf(count == null ? 0 : count)));
        //充值次数
        wrapper = cashWrapper();
        Map<String, CashAmountResultVO> cashTypeVoMap = yearEnd24Mapper.cashListAmount("type_", wrapper)
                .stream().collect(Collectors.toMap(CashAmountResultVO::getField, k -> k));
        if (CollUtil.isNotEmpty(cashTypeVoMap)) {
            //订单总数-接待的客户
            build.setOrderNum(cashTypeVoMap.values().stream().mapToInt(CashAmountResultVO::getNum).filter(Objects::nonNull).sum());

            build.setRechargeTimes(cashTypeVoMap.containsKey(PosCashTypeEnum.RECHARGE.getCode()) ? cashTypeVoMap.get(PosCashTypeEnum.RECHARGE.getCode())
                    .getNum() : 0);
            //总赠送金额
            build.setTotalGiftAmount(cashTypeVoMap.containsKey(PosCashTypeEnum.RECHARGE.getCode()) ? cashTypeVoMap.get(PosCashTypeEnum.RECHARGE.getCode())
                    .getGiftAmount() : BigDecimal.ZERO);
            build.setOpenTimes(cashTypeVoMap.containsKey(PosCashTypeEnum.START_TABLE.getCode()) ? cashTypeVoMap.get(PosCashTypeEnum.START_TABLE.getCode())
                    .getNum() : 0);
        }
        wrapper = cashWrapper();
        wrapper.eq("t.delete_flag", 0);
        wrapper.eq("t.status", CashTableStatusEnum.STOP.getCode());
        //套餐
        List<DetailAmountResultVO> thailList = yearEnd24Mapper.thailList(wrapper);
        if (CollUtil.isNotEmpty(thailList)) {
            int sum = thailList.stream().mapToInt(DetailAmountResultVO::getBizValue).sum();
            build.setTotalDuration(build.getTotalDuration() + sum);
        }
        //会员数
        List<NameValueVO> memberList = yearEnd24Mapper.memberList();
        if (CollUtil.isNotEmpty(memberList)) {
            build.setMemberNum(memberList.stream().mapToInt(v -> Integer.parseInt(v.getValue())).sum());
            Map<String, NameValueVO> collect = memberList.stream().collect(Collectors.toMap(NameValueVO::getName, k -> k));
            build.setCurrMemberNum(CollUtil.isNotEmpty(collect)
                    && collect.containsKey("0") ? Integer.parseInt(collect.get("0").getValue()) : 0);
        }
        List<NameValueVO> employeeList = yearEnd24Mapper.employeeList();
        if (CollUtil.isNotEmpty(employeeList)) {
            //总员工人数
            build.setTotalEmployeeNum(employeeList.stream().mapToInt(v -> Integer.parseInt(v.getValue())).sum());
            //员工离职人数
            build.setDepartureEmployeeNum(employeeList.stream()
                    .filter(v -> !v.getName().contains("10_0")).mapToInt(v -> Integer.parseInt(v.getValue())).sum());
            //当前员工人数
            build.setCurrEmployeeNum(employeeList.stream()
                    .filter(v -> v.getName().contains("10_0")).mapToInt(v -> Integer.parseInt(v.getValue())).sum());
        }
        //满台率 台桌开台总时长÷总台桌数量÷天数,其中总台桌数量不包含虚拟台桌
        Long tableCount = yearEnd24Mapper.tableCount();
        if (tableCount == null) {
            tableCount = 0L;
        }
        build.setFullPlatformRate(BigDecimal.ZERO);
        build.setFullPlatformDuration(0);
        if (tableCount != 0) {
            BigDecimal bigDecimal = new BigDecimal(build.getTotalDuration())
                    .divide(BigDecimal.valueOf(tableCount), 10, RoundingMode.HALF_UP)
                    .divide(BigDecimal.valueOf(build.getTotalDays()), 10, RoundingMode.HALF_UP)
                    .setScale(2, RoundingMode.HALF_UP);
            build.setFullPlatformRate(bigDecimal);
            //满台时长 台桌开台总时长/总台桌数量/天数,其中总台桌数量不包含虚拟台桌
            build.setFullPlatformDuration((int) (build.getTotalDuration() / tableCount / build.getTotalDays()));
        }
        //平均开台时长  = 台桌开台总时长 ÷ 开台订单数
        if (build.getOpenTimes() != null && build.getOpenTimes() > 0) {
            int duration = build.getTotalDuration() / build.getOpenTimes();
            build.setAvgOpenDuration(duration);
        }
        //身份称号
        build.setIdentityTitle(identityTitle(build.getTotalDays()));
        build.setIdentityDesc(identityDesc(build.getAvgDayAmount()));
        build.setLevel(level(build.getAvgDayAmount()));
        //会员消费占比
        //会员消费金额
        build.setMemberConsumeAmount(BigDecimal.ZERO);
        build.setMemberConsumeProp(BigDecimal.ZERO);
        wrapper.eq("t.delete_flag", 0);
        wrapper.inSql("t.pay_type_id", "select id from base_payment_type where biz_type = " + PaymentBizTypeEnum.ACCOUNT.getCode()
                + " and delete_flag = 0 ");
        wrapper.eq("t.status", PosCashPaymentStatusEnum.PAY_SUCCESS.getCode());
        wrapper = cashWrapper();
        wrapper.ne("p.type_", PosCashTypeEnum.RECHARGE.getCode());
        billState = yearEnd24Mapper.cashListAmount("IF(p.member_id is null ,0,1)", wrapper);
        if (CollUtil.isNotEmpty(billState)) {
            //总收款
            BigDecimal payment = billState.stream().map(CashAmountResultVO::getPayment).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal memberAmount = billState.stream().filter(v -> !"0".equals(v.getField()))
                    .map(CashAmountResultVO::getPayment).reduce(BigDecimal.ZERO, BigDecimal::add);
            build.setMemberConsumeAmount(memberAmount.setScale(2, RoundingMode.HALF_UP));
            if (payment.compareTo(BigDecimal.ZERO) > 0) {
                build.setMemberConsumeProp(memberAmount.divide(payment, 10, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100))
                        .setScale(2, RoundingMode.HALF_UP));
            }
        }
        BaseStore baseStore = storeApi.getById(build.getOrgId());
        if (baseStore != null) {
            build.setStoreName(baseStore.getShortName());
        }
        log.info("{},年终总结，{}", build.getTenantId(), build);
        yearEnd24Service.save(build);
    }


    private QueryWrapper<PosCash> bizLogWrapper() {
        LocalDate startTime = LocalDate.of(2023, 5, 1);
        LocalDate endTime = LocalDate.of(2024, 12, 31);
        QueryWrapper<PosCash> wrapper = new QueryWrapper<>();
        wrapper.eq("delete_flag", 0);
        wrapper.eq("created_org_id", ContextUtil.getCurrentCompanyId());
        wrapper.between("created_time", DateUtils.getStartTime(startTime.toString()),
                DateUtils.getEndTime(endTime.toString()));
        wrapper.in("biz_module", Arrays.asList(BizLogModuleEnum.CLOSE_LIGHT.getCode(),
                BizLogModuleEnum.TEMP_OPEN_LIGHT.getCode(), BizLogModuleEnum.OPEN_LIGHT.getCode(),
                BizLogModuleEnum.CASH_TEMP_CLOSE_LIGHT.getCode(), BizLogModuleEnum.CASH_TEMP_OPEN_LIGHT.getCode()));
        return wrapper;
    }

    private QueryWrapper<PosCash> cashWrapper() {
        LocalDate startTime = LocalDate.of(2023, 5, 1);
        LocalDate endTime = LocalDate.of(2024, 12, 31);
        QueryWrapper<PosCash> wrapper = new QueryWrapper<>();
        wrapper.eq("p.delete_flag", 0);
        wrapper.eq("p.org_id", ContextUtil.getCurrentCompanyId());
        wrapper.between("p.complete_time", DateUtils.getStartTime(startTime.toString()),
                DateUtils.getEndTime(endTime.toString()));
        wrapper.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(),
                PosCashBillStateEnum.PART_REFUND.getCode()));
        wrapper.notIn("p.bill_type", Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode(),
                PosCashBillTypeEnum.CHARGEBACK.getCode()));
        return wrapper;
    }

    private String identityDesc(BigDecimal avgDayAmount) {
        if (avgDayAmount == null) {
            return "-";
        }
        //日均1.5以上，日均6-1.5，日6以下
        if (avgDayAmount.compareTo(new BigDecimal("15000")) >= 0) {
            return "探索球房的趣味之旅";
        } else if (avgDayAmount.compareTo(new BigDecimal("6000")) >= 0) {
            return "闯荡球房的热血冒险";
        } else {
            return "点亮球房的璀璨星河";
        }

    }

    private Integer level(BigDecimal avgDayAmount) {
        if (avgDayAmount == null) {
            return 1;
        }
        //日均1.5以上，日均6-1.5，日6以下
        if (avgDayAmount.compareTo(new BigDecimal("15000")) >= 0) {
            return 3;
        } else if (avgDayAmount.compareTo(new BigDecimal("6000")) >= 0) {
            return 2;
        } else {
            return 1;
        }
    }

    private String identityTitle(Integer totalDays) {
        if (totalDays == null) {
            return "LV0";
        }
        //lv1三个月内
        if (totalDays <= 90) {
            return "LV1";
            //lv2:3-9个月
        } else if (totalDays <= 270) {
            return "LV2";
        } else {
            return "LV3";
        }
    }


}

