package top.kx.kxss.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.coupon.BaseCouponInfo;
import top.kx.kxss.base.entity.member.coupon.MemberCoupon;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.pos.DiscountTypeEnum;
import top.kx.kxss.report.mapper.CouponMapper;
import top.kx.kxss.report.query.CouponInfoQuery;
import top.kx.kxss.report.query.CouponIssueQuery;
import top.kx.kxss.report.service.CouponService;
import top.kx.kxss.report.service.common.CouponCommonCtrl;
import top.kx.kxss.report.vo.MemberCouponResultVO;
import top.kx.kxss.report.vo.StatisCouponResultVO;
import top.kx.kxss.wxapp.vo.result.statistics.ColumnVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 套餐销售API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class CouponServiceImpl extends CouponCommonCtrl implements CouponService {

    private final CouponMapper couponMapper;
    private final EchoService echoService;

    @Override
    public Map<String, Object> statisPage(PageParams<CouponInfoQuery> params) {
        CouponInfoQuery query = params.getModel();
        params.setSort("");
        params.setOrder("");
        QueryWrapper<BaseCouponInfo> wrapper = statisCouponWrapper(query);
        IPage<StatisCouponResultVO> page = couponMapper.statisPage(params.buildPage(StatisCouponResultVO.class), wrapper);
        if (CollUtil.isNotEmpty(page.getRecords())) {
            wrapper.eq("pcdd.delete_flag", 0).eq("pc.delete_flag", 0).eq("pcdd.discount_type", DiscountTypeEnum.COUPON_DISCOUNT.getCode());
            List<Long> couponIds = page.getRecords().stream().map(StatisCouponResultVO::getId).collect(Collectors.toList());
            wrapper.in("bci.id", couponIds);
            List<StatisCouponResultVO> posCashCouponList = couponMapper.statisPosCashCouponList(wrapper);
            Map<Long, StatisCouponResultVO> posCashCouponMap = posCashCouponList.stream().collect(Collectors.toMap(StatisCouponResultVO::getId, s -> s));
            if (CollUtil.isNotEmpty(posCashCouponMap)) {
                page.getRecords().forEach(s -> {
                    StatisCouponResultVO statisCouponResultVO = posCashCouponMap.get(s.getId());
                    if (statisCouponResultVO != null) {
                        s.setDeductAmount(statisCouponResultVO.getDeductAmount());
                        s.setPullTurnover(statisCouponResultVO.getPullTurnover());
                        s.setPullIncome(statisCouponResultVO.getPullIncome());
                        s.setPullOrderNum(statisCouponResultVO.getPullOrderNum());
                    }
                });
            }
        }
        echoService.action(page.getRecords());
        page.getRecords().forEach(item -> {
            if (Objects.nonNull(item.getSendNum()) && Objects.nonNull(item.getUsedNum())) {
                item.setUsedRate(BigDecimal.valueOf(item.getUsedNum()).divide(BigDecimal.valueOf(item.getUsedNum()), 2, RoundingMode.HALF_UP).toPlainString() + "%");
            }
            Map<String, Object> echoMap = item.getEchoMap();
            if (CollUtil.isNotEmpty(echoMap)) {
                if (echoMap.containsKey("type")) {
                    item.setTypeDesc(echoMap.get("type").toString());
                }
            }
        });
        IPage<Map> pageList = BeanPlusUtil.toBeanPage(page, Map.class);
        List<Map> resultVOList = Lists.newArrayList();
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("typeDesc").label("券类型").width(250).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("券名称").width(250).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("sendNum").label("发券数量").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("sendMemberCount").label("发券会员数").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("usedMemberCount").label("用券会员数").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("usedNum").label("用券数量").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("usedRate").label("用券率").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("deductAmount").label("抵扣金额").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("pullTurnover").label("拉动营业额").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("pullIncome").label("拉动营业收入").width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("pullOrderNum").label("拉动单数").width(180).emptyString("-").fixed(false).build()
        );
        if (CollUtil.isEmpty(page.getRecords())) {
            pageList.setRecords(resultVOList);
            Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
            objectMap.put("columnList", columnVOList);
            return objectMap;
        }
        pageList.setRecords(BeanPlusUtil.toBeanList(page.getRecords(), Map.class));
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public StatisCouponResultVO statisSum(CouponInfoQuery params) {
        StatisCouponResultVO resultVO = StatisCouponResultVO.builder()
                .sendNum(0)
                .sendMemberCount(0)
                .usedMemberCount(0)
                .usedNum(0)
                .deductAmount(BigDecimal.ZERO)
                .pullTurnover(BigDecimal.ZERO)
                .pullIncome(BigDecimal.ZERO)
                .pullOrderNum(0)
                .build();
        List<StatisCouponResultVO> statisCouponResultVOS = statisList(params);
        if (CollUtil.isNotEmpty(statisCouponResultVOS)) {
            resultVO.setSendNum(statisCouponResultVOS.stream().mapToInt(StatisCouponResultVO::getSendNum).sum());
            resultVO.setSendMemberCount(statisCouponResultVOS.stream().mapToInt(StatisCouponResultVO::getSendMemberCount).sum());
            resultVO.setUsedMemberCount(statisCouponResultVOS.stream().mapToInt(StatisCouponResultVO::getUsedMemberCount).sum());
            resultVO.setUsedNum(statisCouponResultVOS.stream().mapToInt(StatisCouponResultVO::getUsedNum).sum());
            resultVO.setDeductAmount(statisCouponResultVOS.stream().map(StatisCouponResultVO::getDeductAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            resultVO.setPullTurnover(statisCouponResultVOS.stream().map(StatisCouponResultVO::getPullTurnover).reduce(BigDecimal.ZERO, BigDecimal::add));
            resultVO.setPullIncome(statisCouponResultVOS.stream().map(StatisCouponResultVO::getPullIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
            resultVO.setPullOrderNum(statisCouponResultVOS.stream().mapToInt(StatisCouponResultVO::getPullOrderNum).sum());
        }
        return resultVO;
    }

    @Override
    public List<StatisCouponResultVO> statisList(CouponInfoQuery params) {
        QueryWrapper<BaseCouponInfo> wrapper = statisCouponWrapper(params);
        List<StatisCouponResultVO> list = couponMapper.statisList(wrapper);
        if (CollUtil.isNotEmpty(list)) {
            wrapper.eq("pcdd.delete_flag", 0).eq("pc.delete_flag", 0).eq("pcdd.discount_type", DiscountTypeEnum.COUPON_DISCOUNT.getCode());
            List<StatisCouponResultVO> posCashCouponList = couponMapper.statisPosCashCouponList(wrapper);
            Map<Long, StatisCouponResultVO> posCashCouponMap = posCashCouponList.stream().collect(Collectors.toMap(StatisCouponResultVO::getId, s -> s));
            if (CollUtil.isNotEmpty(posCashCouponMap)) {
                list.forEach(s -> {
                    StatisCouponResultVO statisCouponResultVO = posCashCouponMap.get(s.getId());
                    if (statisCouponResultVO != null) {
                        s.setDeductAmount(statisCouponResultVO.getDeductAmount());
                        s.setPullTurnover(statisCouponResultVO.getPullTurnover());
                        s.setPullIncome(statisCouponResultVO.getPullIncome());
                        s.setPullOrderNum(statisCouponResultVO.getPullOrderNum());
                    }
                });
            }
        }
        echoService.action(list);
        list.forEach(item -> {
            Map<String, Object> echoMap = item.getEchoMap();
            if (CollUtil.isNotEmpty(echoMap)) {
                if (echoMap.containsKey("type")) {
                    item.setTypeDesc(echoMap.get("type").toString());
                }
            }
        });
        return list;
    }

    @Override
    public Map<String, Object> memberCouponPage(PageParams<CouponIssueQuery> params) {
        CouponIssueQuery query = params.getModel();
        params.setSort("");
        params.setOrder("");
        QueryWrapper<MemberCoupon> wrapper = memberCouponWrapper(query);
        IPage<MemberCouponResultVO> page = couponMapper.memberCouponPage(params.buildPage(StatisCouponResultVO.class), wrapper);
        echoService.action(page.getRecords());
        page.getRecords().forEach(item -> {
            Map<String, Object> echoMap = item.getEchoMap();
            if (Objects.nonNull(echoMap.get("type"))) {
                item.setTypeDesc(echoMap.get("type").toString());
            }
            if (Objects.nonNull(echoMap.get("orderSource"))) {
                item.setOrderSourceDesc(echoMap.get("orderSource").toString());
            }
            if (Objects.nonNull(echoMap.get("orgId"))) {
                item.setOrg(echoMap.get("orgId").toString());
            }
        });
        IPage<Map> pageList = BeanPlusUtil.toBeanPage(page, Map.class);
        List<Map> resultVOList = Lists.newArrayList();
        // 设置表头
        List<ColumnVO> columnVOList = Lists.newArrayList(
                ColumnVO.builder().name("typeDesc").label("券类型").width(250).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("券名称").width(250).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("consumeTime").label("核销时间").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeTime").label("结账时间").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("code").label("关联订单号").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orderSourceDesc").label("订单来源").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("memberName").label("会员名称").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("mobile").label("会员手机号").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("couponCode").label("券码").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("deductAmount").label("核销抵用金额").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("pullTurnover").label("拉动营业额").width(250).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("pullIncome").label("拉动营业收入").width(250).emptyString("-").fixed(false).build()
        );
        if (CollUtil.isEmpty(page.getRecords())) {
            pageList.setRecords(resultVOList);
            Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
            objectMap.put("columnList", columnVOList);
            return objectMap;
        }
        pageList.setRecords(BeanPlusUtil.toBeanList(page.getRecords(), Map.class));
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public MemberCouponResultVO memberCouponSum(CouponIssueQuery params) {
        return couponMapper.memberCouponSum(memberCouponWrapper(params));
    }

    @Override
    public List<MemberCouponResultVO> memberCouponList(CouponIssueQuery params) {
        List<MemberCouponResultVO> couponResultVOList = couponMapper.memberCouponList(memberCouponWrapper(params));
        echoService.action(couponResultVOList);
        couponResultVOList.forEach(item -> {
            Map<String, Object> echoMap = item.getEchoMap();
            if (echoMap.containsKey("type")) {
                item.setTypeDesc(echoMap.get("type").toString());
            }
            if (echoMap.containsKey("orderSource")) {
                item.setOrderSourceDesc(echoMap.get("orderSource").toString());
            }
            if (echoMap.containsKey("orgId")) {
                item.setOrg(echoMap.get("orgId").toString());
            }
        });
        return couponResultVOList;
    }
}

