package top.kx.kxss.report.service.reconciliation;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.pay.vo.result.IsvReconciliationInfoResultVO;
import top.kx.kxss.report.entity.reconciliation.ReconciliationRecord;
import top.kx.kxss.report.vo.query.reconciliation.ReconciliationRecordPageQuery;
import top.kx.kxss.report.vo.result.reconciliation.ReconciliationRecordResultVO;
import top.kx.kxss.report.vo.result.reconciliation.StatisticReconciliationDownloadResultVO;
import top.kx.kxss.report.vo.save.reconciliation.ReconciliationRecordSaveVO;
import top.kx.kxss.report.vo.update.reconciliation.ReconciliationRecordUpdateVO;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 对账单记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-30 20:18:34
 * @create [2025-06-30 20:18:34] [dou] [代码生成器生成]
 */
public interface ReconciliationRecordService extends SuperService<Long, ReconciliationRecord, ReconciliationRecordSaveVO,
        ReconciliationRecordUpdateVO, ReconciliationRecordPageQuery, ReconciliationRecordResultVO> {

    boolean remove(LbQueryWrap<ReconciliationRecord> eq);

    List<StatisticReconciliationDownloadResultVO> getDownloadDataList(IsvReconciliationInfoResultVO resultVO,
                                                                      String startTime, String endTime);
}


