package top.kx.kxss.report.service.common;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.commons.lang3.StringUtils;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.report.query.ProductAttributeQuery;

/**
 * 商品销售API
 *
 * <AUTHOR>
 */
public class AttributeCommonCtrl extends PosCashCommonCtrl{


    /**
     * 基础查询条件
     * @param query
     * @return
     */
    public QueryWrapper<Object> productAttributeWrapper(ProductAttributeQuery query) {
        QueryWrapper<Object> queryWrapper = baseWrapper(query);
        queryWrapper.eq("pro.delete_flag", 0);
        queryWrapper.eq("pro.created_org_id", ContextUtil.getCurrentCompanyId());
        queryWrapper.like(StringUtils.isNotBlank(query.getKeywords()), "d.`name`", query.getKeywords());
        return queryWrapper;
    }


}

