package top.kx.kxss.report.mapper.reconciliation;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.pay.vo.query.DayReconciliationQuery;
import top.kx.kxss.pay.vo.result.ReconciliationResultVO;
import top.kx.kxss.report.entity.reconciliation.Reconciliation;
import top.kx.kxss.report.query.reconciliation.StatisticReconciliationQuery;
import top.kx.kxss.report.vo.result.reconciliation.StatisticReconciliationResultVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * 商户对账单
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-01 15:55:00
 * @create [2025-07-01 15:55:00] [dou] [代码生成器生成]
 */
@Repository
public interface ReconciliationMapper extends SuperMapper<Reconciliation> {

    StatisticReconciliationResultVO statistic(
            @Param("model") StatisticReconciliationQuery model);

    List<ReconciliationResultVO> dayList(@Param("model") DayReconciliationQuery model);

    List<ReconciliationResultVO> monthList(@Param("model")  DayReconciliationQuery query);

}


