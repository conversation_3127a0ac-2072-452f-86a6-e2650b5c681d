package top.kx.kxss.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.base.BaseEquityTypeEnum;
import top.kx.kxss.report.mapper.SalesMapper;
import top.kx.kxss.report.query.ProductSalesQuery;
import top.kx.kxss.report.service.SalesService;
import top.kx.kxss.report.service.ThailSalesService;
import top.kx.kxss.report.service.common.CommonCtrl;
import top.kx.kxss.report.vo.SalesDetailResultVO;
import top.kx.kxss.report.vo.SalesResultVO;
import top.kx.kxss.wxapp.api.custom.CustomApi;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

import java.util.List;

/**
 * 商品销售API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class SalesServiceImpl extends CommonCtrl implements SalesService {

    private final SalesMapper salesMapper;
    private final ThailSalesService thailSalesService;
    private final CustomApi customApi;


    @Override
    public SalesResultVO info(String equityType, ProductSalesQuery query) {
        BaseEquityTypeEnum equityTypeEnum = BaseEquityTypeEnum.get(equityType.toUpperCase());
        ArgumentAssert.notNull(equityTypeEnum, "无效的权益类型");
        R<DataOverviewQuery> storeTime = customApi.getStoreTime(query);
        DataOverviewQuery storeTimeData = storeTime.getData();
        query.setStartDate(storeTimeData.getStartDate());
        query.setEndDate(storeTimeData.getEndDate());
        QueryWrapper queryWrapper = queryWrapper(query);
        List<SalesDetailResultVO> voList = null;
        switch (equityTypeEnum) {
            case SERVICE:
                queryWrapper.in(CollUtil.isNotEmpty(query.getCategoryIds()), "pro.service_id", query.getCategoryIds());
                queryWrapper.like(StrUtil.isNotBlank(query.getCategoryName()), "b.name", query.getCategoryName());
                queryWrapper.like(StrUtil.isNotBlank(query.getName()), "c.real_name", query.getName());
                itemGroupBuy("concat(pro.service_id,'_','pro.employee_id')", queryWrapper, query.getSalesType());
                voList = salesMapper.serviceList(queryWrapper);
                break;
            case TABLE:
                queryWrapper.in(CollUtil.isNotEmpty(query.getCategoryIds()), "b.table_type", query.getCategoryIds());
                queryWrapper.like(StrUtil.isNotBlank(query.getCategoryName()), "c.name", query.getCategoryName());
                queryWrapper.like(StrUtil.isNotBlank(query.getName()), "pro.table_name", query.getName());
                itemGroupBuy("pro.table_id", queryWrapper, query.getSalesType());
                voList = salesMapper.tableList(queryWrapper);
                break;
            case PRODUCT:
                queryWrapper.in(CollUtil.isNotEmpty(query.getCategoryIds()), "b.category_id", query.getCategoryIds());
                queryWrapper.like(StrUtil.isNotBlank(query.getCategoryName()), "c.name", query.getCategoryName());
                queryWrapper.like(StrUtil.isNotBlank(query.getName()), "pro.product_name", query.getName());
                itemGroupBuy("pro.product_id", queryWrapper, query.getSalesType());
                voList = salesMapper.productList(queryWrapper);
                break;
        }

        if (CollUtil.isEmpty(voList)) {
            voList = Lists.newArrayList();
        }
        //套餐信息
        List<SalesDetailResultVO> voList1 = thailSalesService.list(query);
        if (CollUtil.isNotEmpty(voList1)) {
            voList.addAll(voList1);
        }
        return dataResult(voList);
    }

}

