package top.kx.kxss.report.service;

import top.kx.basic.base.request.PageParams;
import top.kx.kxss.report.query.CardConsumeQuery;
import top.kx.kxss.report.vo.MemberCardChangeResultVO;

import java.util.List;
import java.util.Map;

/**
 * API
 *
 * <AUTHOR>
 */
public interface CardService {

    Map<String, Object> memberCardChangePage(PageParams<CardConsumeQuery> params);

    MemberCardChangeResultVO memberCardChangeSum(CardConsumeQuery params);

    List<MemberCardChangeResultVO> memberCardChangeList(CardConsumeQuery params);


}
