package top.kx.kxss.report.easyexcel;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.util.StyleUtil;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Predicate;

/**
 * 灵活的颜色处理器 - 支持多种颜色判断条件
 */
@Slf4j
public class FlexibleColorHandler implements CellWriteHandler {

    // 颜色规则存储
    private final Map<String, ColorRule> colorRules = new ConcurrentHashMap<>();

    // 默认样式配置
    private final StyleConfig defaultStyle;

    public FlexibleColorHandler() {
        this.defaultStyle = new StyleConfig();
    }

    public FlexibleColorHandler(StyleConfig defaultStyle) {
        this.defaultStyle = defaultStyle;
    }

    /**
     * 添加颜色规则
     */
    public FlexibleColorHandler addRule(String ruleName, ColorRule rule) {
        colorRules.put(ruleName, rule);
        return this;
    }

    /**
     * 添加基于行数据的颜色规则
     */
    public FlexibleColorHandler addRowRule(String ruleName, Predicate<Object> condition, Short color) {
        ColorRule rule = new ColorRule(ColorRule.Type.ROW, condition, color);
        return addRule(ruleName, rule);
    }

    /**
     * 添加基于单元格值的颜色规则
     */
    public FlexibleColorHandler addCellRule(String ruleName, int columnIndex, Predicate<Object> condition, Short color) {
        ColorRule rule = new ColorRule(ColorRule.Type.CELL, condition, color);
        rule.setColumnIndex(columnIndex);
        return addRule(ruleName, rule);
    }

    /**
     * 添加基于行索引的颜色规则
     */
    public FlexibleColorHandler addRowIndexRule(String ruleName, Predicate<Object> condition, Short color) {
        ColorRule rule = new ColorRule(ColorRule.Type.ROW_INDEX, condition, color);
        return addRule(ruleName, rule);
    }

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer columnIndex, Integer relativeRowIndex, Boolean isHead) {
    }

    @Override
    public void afterCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
    }

    @Override
    public void afterCellDataConverted(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, WriteCellData cellData, Cell cell, Head head, Integer integer, Boolean aBoolean) {

    }

    @Override
    public void afterCellDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, List<WriteCellData<?>> cellDataList, Cell cell, Head head, Integer relativeRowIndex, Boolean isHead) {
        Workbook workbook = cell.getSheet().getWorkbook();

        // 创建基础样式
        WriteCellStyle writeCellStyle = createBaseStyle();
        WriteFont writeFont = createBaseFont();

        // 应用默认样式配置
        applyDefaultStyle(writeSheetHolder, cell, writeCellStyle, writeFont, isHead);

        // 应用颜色规则
        Short dynamicColor = applyColorRules(cell, cellDataList, relativeRowIndex, isHead);
        if (dynamicColor != null) {
            writeCellStyle.setFillForegroundColor(dynamicColor);
            writeCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
        }

        writeCellStyle.setWriteFont(writeFont);
        CellStyle cellStyle = StyleUtil.buildCellStyle(workbook,null,  writeCellStyle);
        cell.setCellStyle(cellStyle);
    }

    /**
     * 创建基础样式
     */
    private WriteCellStyle createBaseStyle() {
        WriteCellStyle style = new WriteCellStyle();
        style.setFillForegroundColor(IndexedColors.WHITE.getIndex());
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setHorizontalAlignment(HorizontalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        return style;
    }

    /**
     * 创建基础字体
     */
    private WriteFont createBaseFont() {
        WriteFont font = new WriteFont();
        font.setFontName("宋体");
        font.setBold(false);
        font.setFontHeightInPoints((short) 12);
        return font;
    }

    /**
     * 应用默认样式配置
     */
    private void applyDefaultStyle(WriteSheetHolder writeSheetHolder, Cell cell, WriteCellStyle writeCellStyle, WriteFont writeFont, Boolean isHead) {
        if (defaultStyle == null) return;

        Sheet sheet = writeSheetHolder.getSheet();

        if (isHead) {
            // 表头样式
            if (defaultStyle.getHeaderFontSize() != null) {
                writeFont.setFontHeightInPoints(defaultStyle.getHeaderFontSize());
            }
            if (defaultStyle.getHeaderBold() != null) {
                writeFont.setBold(defaultStyle.getHeaderBold());
            }
            if (defaultStyle.getHeaderRowHeight() != null) {
                sheet.getRow(cell.getRowIndex()).setHeight(defaultStyle.getHeaderRowHeight());
            }
            if (defaultStyle.getHeaderBackgroundColor() != null) {
                writeCellStyle.setFillForegroundColor(defaultStyle.getHeaderBackgroundColor());
                writeCellStyle.setFillPatternType(FillPatternType.SOLID_FOREGROUND);
            }
        } else {
            // 数据行样式
            if (defaultStyle.getDataFontSize() != null) {
                writeFont.setFontHeightInPoints(defaultStyle.getDataFontSize());
            }
            if (defaultStyle.getDataRowHeight() != null) {
                sheet.getRow(cell.getRowIndex()).setHeight(defaultStyle.getDataRowHeight());
            }
        }

        // 列宽设置
        if (defaultStyle.getColumnWidth() != null) {
            sheet.setColumnWidth(cell.getColumnIndex(), defaultStyle.getColumnWidth());
        }
    }

    /**
     * 应用颜色规则
     */
    private Short applyColorRules(Cell cell, List<WriteCellData<?>> cellDataList, Integer relativeRowIndex, Boolean isHead) {
        if (isHead) return null;

        for (ColorRule rule : colorRules.values()) {
            try {
                Short color = rule.getColor(cell, cellDataList, relativeRowIndex);
                if (color != null) {
                    return color; // 返回第一个匹配的颜色
                }
            } catch (Exception e) {
                log.warn("应用颜色规则失败: {}", e.getMessage());
            }
        }

        return null;
    }

    /**
     * 颜色规则类
     */
    public static class ColorRule {
        public enum Type {
            ROW,        // 基于行数据
            CELL,       // 基于单元格值
            ROW_INDEX   // 基于行索引
        }

        private final Type type;
        private final Predicate<Object> condition;
        private final Short color;
        private Integer columnIndex; // 用于CELL类型

        public ColorRule(Type type, Predicate<Object> condition, Short color) {
            this.type = type;
            this.condition = condition;
            this.color = color;
        }

        public void setColumnIndex(Integer columnIndex) {
            this.columnIndex = columnIndex;
        }

        public Short getColor(Cell cell, List<WriteCellData<?>> cellDataList, Integer relativeRowIndex) {
            try {
                switch (type) {
                    case ROW_INDEX:
                        return condition.test(cell.getRowIndex()) ? color : null;
                    case CELL:
                        if (columnIndex != null && !columnIndex.equals(cell.getColumnIndex())) {
                            return null;
                        }
                        Object cellValue = getCellValue(cell);
                        return condition.test(cellValue) ? color : null;
                    case ROW:
                        // 这里需要获取行数据，由于EasyExcel限制，可能需要其他方式
                        return null;
                    default:
                        return null;
                }
            } catch (Exception e) {
                log.warn("颜色规则执行失败: {}", e.getMessage());
                return null;
            }
        }

        private Object getCellValue(Cell cell) {
            try {
                switch (cell.getCellTypeEnum()) {
                    case STRING:
                        return cell.getStringCellValue();
                    case NUMERIC:
                        return cell.getNumericCellValue();
                    case BOOLEAN:
                        return cell.getBooleanCellValue();
                    default:
                        return null;
                }
            } catch (Exception e) {
                return null;
            }
        }
    }

    /**
     * 样式配置类
     */
    public static class StyleConfig {
        private Short headerFontSize = 14;
        private Short dataFontSize = 12;
        private Boolean headerBold = true;
        private Short headerRowHeight = (short) (25 * 20);
        private Short dataRowHeight = (short) (20 * 20);
        private Integer columnWidth = 15 * 256;
        private Short headerBackgroundColor = IndexedColors.GREY_25_PERCENT.getIndex();

        // Getters and Setters
        public Short getHeaderFontSize() {
            return headerFontSize;
        }

        public void setHeaderFontSize(Short headerFontSize) {
            this.headerFontSize = headerFontSize;
        }

        public Short getDataFontSize() {
            return dataFontSize;
        }

        public void setDataFontSize(Short dataFontSize) {
            this.dataFontSize = dataFontSize;
        }

        public Boolean getHeaderBold() {
            return headerBold;
        }

        public void setHeaderBold(Boolean headerBold) {
            this.headerBold = headerBold;
        }

        public Short getHeaderRowHeight() {
            return headerRowHeight;
        }

        public void setHeaderRowHeight(Short headerRowHeight) {
            this.headerRowHeight = headerRowHeight;
        }

        public Short getDataRowHeight() {
            return dataRowHeight;
        }

        public void setDataRowHeight(Short dataRowHeight) {
            this.dataRowHeight = dataRowHeight;
        }

        public Integer getColumnWidth() {
            return columnWidth;
        }

        public void setColumnWidth(Integer columnWidth) {
            this.columnWidth = columnWidth;
        }

        public Short getHeaderBackgroundColor() {
            return headerBackgroundColor;
        }

        public void setHeaderBackgroundColor(Short headerBackgroundColor) {
            this.headerBackgroundColor = headerBackgroundColor;
        }
    }
}
