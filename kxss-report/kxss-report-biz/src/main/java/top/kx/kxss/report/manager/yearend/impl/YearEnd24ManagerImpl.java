package top.kx.kxss.report.manager.yearend.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.report.entity.yearend.YearEnd24;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.report.manager.yearend.YearEnd24Manager;
import top.kx.kxss.report.mapper.yearend.YearEnd24Mapper;

/**
 * <p>
 * 通用业务实现类
 * 年终总结24年
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-26 18:46:28
 * @create [2024-12-26 18:46:28] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class YearEnd24ManagerImpl extends SuperManagerImpl<YearEnd24Mapper, YearEnd24> implements YearEnd24Manager {

}


