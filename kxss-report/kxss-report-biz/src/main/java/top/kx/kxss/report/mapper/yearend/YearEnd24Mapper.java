package top.kx.kxss.report.mapper.yearend;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.vo.result.cash.AmountResultVO;
import top.kx.kxss.base.vo.NameValueVO;
import top.kx.kxss.report.entity.yearend.YearEnd24;
import top.kx.kxss.report.vo.yearend.CashAmountResultVO;
import top.kx.kxss.report.vo.yearend.DetailAmountResultVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * 年终总结24年
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-26 18:46:28
 * @create [2024-12-26 18:46:28] [dou] [代码生成器生成]
 */
@Repository
public interface YearEnd24Mapper extends SuperMapper<YearEnd24> {


    List<CashAmountResultVO> cashListAmount(@Param("field") String field, @Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<CashAmountResultVO> cashListAmount2( @Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<DetailAmountResultVO> tableList(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<DetailAmountResultVO> thailList(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<DetailAmountResultVO> serviceList(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<DetailAmountResultVO> productList(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    Long bizLogCount(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<NameValueVO> memberList();

    DetailAmountResultVO topOneByTable(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<NameValueVO> employeeList();

    Long tableCount();

    List<CashAmountResultVO> memberConsumeAmount(@Param(Constants.WRAPPER) QueryWrapper<PosCash> wrapper);

    List<AmountResultVO> selectByDiscountType(@Param(Constants.WRAPPER) QueryWrapper<PosCash> wrapper);
}


