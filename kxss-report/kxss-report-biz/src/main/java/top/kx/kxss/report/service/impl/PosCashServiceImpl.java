package top.kx.kxss.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.PosCashCommenter;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.entity.thail.PosCashThail;
import top.kx.kxss.app.vo.result.cash.AmountResultVO;
import top.kx.kxss.app.vo.result.cash.PosCashCommenterResultVO;
import top.kx.kxss.app.vo.result.cash.PosCashResultVO;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO;
import top.kx.kxss.app.vo.result.thail.PosCashThailAmountResultVO;
import top.kx.kxss.base.entity.payment.BasePaymentType;
import top.kx.kxss.base.vo.query.payment.BasePaymentTypePageQuery;
import top.kx.kxss.base.vo.query.table.BaseTableInfoPageQuery;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.payment.BasePaymentTypeResultVO;
import top.kx.kxss.base.vo.result.table.BaseTableInfoResultVO;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.employee.EmployeeApi;
import top.kx.kxss.memberInfo.MemberInfoApi;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.payment.BasePaymentTypeApi;
import top.kx.kxss.pos.vo.ItemRemarksExtraResultVO;
import top.kx.kxss.pos.vo.OrderRemarksResultVO;
import top.kx.kxss.report.vo.PosCashItemResultVO;
import top.kx.kxss.report.vo.result.cash.OrderFreeResultVO;
import top.kx.kxss.pos.vo.order.OrderResultVO;
import top.kx.kxss.report.mapper.PosCashMapper;
import top.kx.kxss.report.mapper.PosCashPaymentMapper;
import top.kx.kxss.report.query.CashFreeQuery;
import top.kx.kxss.report.service.PosCashService;
import top.kx.kxss.report.service.common.PosCashCommonCtrl;
import top.kx.kxss.report.vo.PosCashDetailsResultVO;
import top.kx.kxss.report.vo.PosCashNoPayDetailsResultVO;
import top.kx.kxss.table.TableApi;
import top.kx.kxss.wxapp.api.custom.CustomApi;
import top.kx.kxss.wxapp.vo.query.common.OrgIdListQuery;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.query.statistics.PosCashDetailsQuery;
import top.kx.kxss.wxapp.vo.query.statistics.ThailOverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.ColumnVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 利润销售API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class PosCashServiceImpl extends PosCashCommonCtrl implements PosCashService {

    private final PosCashMapper posCashMapper;
    private final PosCashPaymentMapper posCashPaymentMapper;
    private final EchoService echoService;
    private final CustomApi customApi;
    private final EmployeeApi employeeApi;
    private final TableApi tableApi;
    private final MemberInfoApi memberInfoApi;
    @Autowired
    private BasePaymentTypeApi basePaymentTypeApi;


    @Override
    public AmountResultVO selectOneAmount(DataOverviewQuery query) {
        return posCashMapper.selectOneAmount(baseWrapper(query));
    }

    @Override
    public List<AmountResultVO> selectByPayType(Wrapper<PosCash> wrapper) {
        return posCashMapper.selectByPayType(wrapper);
    }

    @Override
    public Map<String, Object> posCashDetails(PageParams<PosCashDetailsQuery> params) {
        initOrgIdList(params.getModel());

        // 设置表头
        List<ColumnVO> columnVOList = com.google.common.collect.Lists.newArrayList(
                ColumnVO.builder().name("code").label("流水号")
                        .width(250).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("billState").label("订单状态")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("billType").label("订单类型")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type").label("业务类型")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableName").label("台桌名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableType").label("台桌类型")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableArea").label("台桌区域")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createdEmp").label("创建员工")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeEmp").label("完成员工")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("employeeEmp").label("销售员工")// employeeId
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("commenter").label("提成员工")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orderSource").label("订单来源")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("memberName").label("会员名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createTime").label("开台时间")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeTime").label("结账时间")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tablePrice").label("台费原价")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableAmount").label("台桌金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("productPrice").label("商品原价")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("productAmount").label("商品金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("servicePrice").label("服务原价")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceAmount").label("服务金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("powerPrice").label("充电原价")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("powerAmount").label("充电金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("thailPrice").label("店内套餐原价")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("thailAmount").label("店内套餐金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("groupBuyPrice").label("团购套餐原价")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("groupBuyAmount").label("团购套餐金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("订单原价")// 原本是订单金额,原价 54.72, amount 字段
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("discountAmount").label("订单优惠金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("refundAmount").label("订单部分退款金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("paid").label("订单实收金额")
                        .width(180).emptyString("-").fixed(false).build(),// amount - discountAmount - refundAmount
                // 挂单时间
                ColumnVO.builder().name("registrationTime").label("挂单时间")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orderRemarks").label("整单备注")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("registrationRemarks").label("挂单备注")
                        .width(180).emptyString("-").fixed(false).build(),
//                ColumnVO.builder().name("paymentDetails").label("支付详情")
//                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(180).emptyString("-").fixed(false).build()
        );
        R<List<BasePaymentTypeResultVO>> basePaymentTypeResultVOList = basePaymentTypeApi.query(BasePaymentTypePageQuery.builder()
                .state(true)
                .orgIdList(params.getModel().getOrgIdList())
                .build());
        for (BasePaymentTypeResultVO typeResultVO : basePaymentTypeResultVOList.getData()) {
            columnVOList.add(ColumnVO.builder().name("payment_" + typeResultVO.getId()).label(typeResultVO.getName())
                    .width(180).emptyString("-").fixed(false).build());
        }
        PosCashDetailsQuery model = params.getModel();
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(model);
            DataOverviewQuery storeTimeData = storeTime.getData();
            model.setStartDate(storeTimeData.getStartDate());
            model.setEndDate(storeTimeData.getEndDate());
        }

        if (StringUtils.isNotBlank(model.getCompleteTime_st()) && StringUtils.isBlank(model.getCompleteTime_ed())) {
            model.setCompleteTime_ed(DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
        }
        IPage<PosCashResultVO> pageResultVO = posCashMapper.selectPageResultVO(params.buildPage(PosCash.class), initWarp(model));
        if (CollUtil.isEmpty(pageResultVO.getRecords())) {
            Map<String, Object> objectMap = BeanUtil.beanToMap(pageResultVO);
            objectMap.put("columnList", columnVOList);
            return objectMap;
        }
        initRemarks(pageResultVO.getRecords());
        IPage<PosCashDetailsResultVO> pageList = BeanPlusUtil.toBeanPage(pageResultVO, PosCashDetailsResultVO.class);
        List<Long> cashIds = pageResultVO.getRecords().stream().map(PosCashResultVO::getId)
                .collect(Collectors.toList());
        List<PosCashCommenter> posCashCommenterList = new ArrayList<>();
        Map<Long, PosCashThailAmountResultVO> thailAmountResultVOMap = new HashMap<>();
        Map<Long, PosCashItemResultVO> tableResultVOMap = new HashMap<>();
        Map<Long, PosCashItemResultVO> serviceResultVOMap = new HashMap<>();
        Map<Long, PosCashItemResultVO> productResultVOMap = new HashMap<>();
        Map<Long, PosCashItemResultVO> powerResultVOMap = new HashMap<>();
        if (CollUtil.isNotEmpty(cashIds)) {
            posCashCommenterList = posCashMapper.cashCommenterListByCashIds(cashIds);
            QueryWrap<PosCashThail> wrap = new QueryWrap<>();
            wrap.in("pc.id", cashIds).eq("pc.delete_flag", 0).isNull("t.cash_thail_id")
                    .eq("t.delete_flag", 0).groupBy("pc.id");
            List<PosCashThailAmountResultVO> thailAmountResultVOList = posCashMapper.thailAmountList(wrap);
            if (CollUtil.isNotEmpty(thailAmountResultVOList)) {
                thailAmountResultVOMap = thailAmountResultVOList.stream().collect(Collectors.toMap(PosCashThailAmountResultVO::getCashId, Function.identity()));
            }
            QueryWrap<PosCash> itemWrap = new QueryWrap<>();
            itemWrap.in("pc.id", cashIds).eq("pc.delete_flag", 0).isNull("t.cash_thail_id")
                    .eq("t.delete_flag", 0).groupBy("pc.id");
            List<PosCashItemResultVO> tableItemList = posCashMapper.tableAmountList(itemWrap);
            if (CollUtil.isNotEmpty(tableItemList)) {
                tableResultVOMap = tableItemList.stream().collect(Collectors.toMap(PosCashItemResultVO::getCashId, Function.identity()));
            }
            List<PosCashItemResultVO> serviceItemList = posCashMapper.serviceAmountList(itemWrap);
            if (CollUtil.isNotEmpty(serviceItemList)) {
                serviceResultVOMap = serviceItemList.stream().collect(Collectors.toMap(PosCashItemResultVO::getCashId, Function.identity()));
            }
            List<PosCashItemResultVO> productItemList = posCashMapper.productAmountList(itemWrap);
            if (CollUtil.isNotEmpty(productItemList)) {
                productResultVOMap = productItemList.stream().collect(Collectors.toMap(PosCashItemResultVO::getCashId, Function.identity()));
            }
            List<PosCashItemResultVO> powerItemList = posCashMapper.powerAmountList(itemWrap);
            if (CollUtil.isNotEmpty(powerItemList)) {
                powerResultVOMap = powerItemList.stream().collect(Collectors.toMap(PosCashItemResultVO::getCashId, Function.identity()));
            }
        }


        List<PosCashCommenterResultVO> cashCommenterListResultVO = BeanPlusUtil.toBeanList(posCashCommenterList, PosCashCommenterResultVO.class);
        Map<Long, String> baseEmployeeMap = MapUtil.newHashMap();
        if (CollUtil.isNotEmpty(cashCommenterListResultVO)) {
            List<Long> employeeIds = cashCommenterListResultVO.stream().map(PosCashCommenterResultVO::getEmployeeId).collect(Collectors.toList());
            R<List<BaseEmployeeResultVO>> employeeListR = employeeApi.findListByIds(employeeIds);
            Map<Long, String> collect = employeeListR.getData()
                    .stream().collect(Collectors.toMap(BaseEmployeeResultVO::getId, BaseEmployeeResultVO::getRealName));
            if (CollUtil.isNotEmpty(collect)) {
                baseEmployeeMap = collect;
            }
        }
        Map<Long, List<PosCashCommenterResultVO>> cashCommenterListResultVOMap = cashCommenterListResultVO.stream().collect(Collectors.groupingBy(PosCashCommenterResultVO::getCashId));
        // 所有支付方式
        List<PosCashPaymentResultVO> cashPaymentList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(cashIds)) {
            cashPaymentList = posCashPaymentMapper.queryListByCashIds(cashIds);
        }
        echoService.action(cashPaymentList);
        // 所有台桌id
        List<Long> tableIdList = pageResultVO.getRecords().stream().map(PosCashResultVO::getTableId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        R<List<BaseTableInfoResultVO>> tableAllR = tableApi.getTableAll(BaseTableInfoPageQuery.builder().build());
        List<BaseTableInfoResultVO> tableInfoResultVOS = tableAllR.getData();
        tableInfoResultVOS = tableInfoResultVOS.stream().filter(v -> tableIdList.contains(v.getId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(tableInfoResultVOS)) {
            tableInfoResultVOS = Lists.newArrayList();
        }
        Map<Long, BaseTableInfoResultVO> baseTableInfoResultVOMap = tableInfoResultVOS.stream().collect(Collectors.toMap(BaseTableInfoResultVO::getId, Function.identity()));
        // 会员信息
        Map<Long, MemberInfoResultVO> memberInfoMap = new HashMap<>();
        List<Long> memberIdList = pageResultVO.getRecords().stream().map(PosCashResultVO::getMemberId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(memberIdList)) {
            List<MemberInfoResultVO> memberInfoList = memberInfoApi.queryByIds(memberIdList).getData();
            memberInfoMap = memberInfoList.stream().collect(Collectors.toMap(MemberInfoResultVO::getId, Function.identity()));
        }
        List<Map<String, Object>> resultMapList = new ArrayList<>();
        for (PosCashResultVO posCashResultVO : pageResultVO.getRecords()) {
            if (posCashResultVO.getRefundAmount() == null) {
                posCashResultVO.setRefundAmount(BigDecimal.ZERO);
            }
            if (posCashResultVO.getPaid() == null) {
                posCashResultVO.setPaid(BigDecimal.ZERO);
            }

            BaseTableInfoResultVO baseTableInfoResultVO = baseTableInfoResultVOMap.get(posCashResultVO.getTableId());

            PosCashDetailsResultVO tableInfoMap = new PosCashDetailsResultVO();
            tableInfoMap.setId(posCashResultVO.getId());
            tableInfoMap.setCode(posCashResultVO.getCode());
            tableInfoMap.setBillState(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("billState")) ? posCashResultVO.getEchoMap().get("billState").toString() : "");
            tableInfoMap.setBillType(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("billType")) ? posCashResultVO.getEchoMap().get("billType").toString() : "");
            tableInfoMap.setType(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("type")) ? posCashResultVO.getEchoMap().get("type").toString() : "");
            tableInfoMap.setTableName(posCashResultVO.getTableName());
            tableInfoMap.setTableType(Objects.nonNull(baseTableInfoResultVO) && Objects.nonNull(baseTableInfoResultVO.getTableType()) && Objects.nonNull(baseTableInfoResultVO.getEchoMap()) ? baseTableInfoResultVO.getEchoMap().get("tableType").toString() : "");
            tableInfoMap.setTableArea(Objects.nonNull(baseTableInfoResultVO) && Objects.nonNull(baseTableInfoResultVO.getTableArea()) && Objects.nonNull(baseTableInfoResultVO.getEchoMap()) ? baseTableInfoResultVO.getEchoMap().get("tableArea").toString() : "");
            tableInfoMap.setCreatedEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("createdEmp")) ? posCashResultVO.getEchoMap().get("createdEmp").toString() : "");
            tableInfoMap.setCompleteEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("completeEmp")) ? posCashResultVO.getEchoMap().get("completeEmp").toString() : "");
            tableInfoMap.setEmployeeEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("employeeId")) ? posCashResultVO.getEchoMap().get("employeeId").toString() : "");
            if (CollUtil.isNotEmpty(cashCommenterListResultVOMap.get(posCashResultVO.getId()))) {
                Map<Long, String> finalBaseEmployeeMap = baseEmployeeMap;
                tableInfoMap.setCommenter(cashCommenterListResultVOMap.get(posCashResultVO.getId())
                        .stream().filter(c -> CollUtil.isNotEmpty(finalBaseEmployeeMap) && ObjectUtil.isNotNull(finalBaseEmployeeMap.containsKey(c.getEmployeeId())))
                        .map(s -> finalBaseEmployeeMap.get(s.getEmployeeId())).collect(Collectors.joining(",")));
            } else {
                tableInfoMap.setCommenter("-");
            }
            tableInfoMap.setOrderSource(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("orderSource")) ? posCashResultVO.getEchoMap().get("orderSource").toString() : "");
            MemberInfoResultVO memberInfo = memberInfoMap.get(posCashResultVO.getMemberId());
            String memberName = "-";
            if (Objects.nonNull(memberInfo)) {
                memberName = memberInfo.getName() + "(" + StrUtil.subSuf(memberInfo.getMobile(), memberInfo.getMobile().length() - 4) + ")";
            }
            tableInfoMap.setMemberName(memberName);
            tableInfoMap.setCreateTime(DateUtils.format(posCashResultVO.getCreatedTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
            tableInfoMap.setCompleteTime(DateUtils.format(posCashResultVO.getCompleteTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
            tableInfoMap.setRegistrationTime(Objects.nonNull(posCashResultVO.getRegistrationTime()) ? DateUtils.format(posCashResultVO.getRegistrationTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT) : "");
            tableInfoMap.setTableAmount(posCashResultVO.getTableAmount());
            tableInfoMap.setProductAmount(posCashResultVO.getProductAmount());
            tableInfoMap.setServiceAmount(posCashResultVO.getServiceAmount());
            tableInfoMap.setThailAmount(posCashResultVO.getThailAmount());
            tableInfoMap.setPowerAmount(posCashResultVO.getPowerAmount());
            tableInfoMap.setAmount(posCashResultVO.getAmount());
            tableInfoMap.setDiscountAmount(posCashResultVO.getDiscountAmount());
            tableInfoMap.setRefundAmount(posCashResultVO.getRefundAmount());
            tableInfoMap.setPaid(posCashResultVO.getAmount().subtract(posCashResultVO.getDiscountAmount()).subtract(posCashResultVO.getRefundAmount()));
            tableInfoMap.setOrderRemarks(posCashResultVO.getOrderRemarks());
            tableInfoMap.setRegistrationRemarks(posCashResultVO.getRegistrationRemarks());
//            tableInfoMap.setPaymentDetails(paymentDetails.toString());
            tableInfoMap.setOrg(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("orgId")) ? posCashResultVO.getEchoMap().get("orgId").toString() : "");
            Map resultVO = BeanPlusUtil.toBean(tableInfoMap, Map.class);
            for (BasePaymentTypeResultVO basePaymentTypeResultVO : basePaymentTypeResultVOList.getData()) {
                List<PosCashPaymentResultVO> posCashPayments = cashPaymentList.stream().filter(c ->
                                Objects.equals(basePaymentTypeResultVO.getId(), c.getPayTypeId())
                                        && Objects.equals(posCashResultVO.getId(), c.getCashId()) && Objects.nonNull(c.getAmount()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(posCashPayments)) {
                    resultVO.put("payment_" + basePaymentTypeResultVO.getId(),
                            posCashPayments.stream().filter(c -> Objects.nonNull(c.getAmount())).map(PosCashPaymentResultVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                                    .subtract(posCashPayments.stream().filter(c -> Objects.nonNull(c.getRefundAmount())).map(PosCashPaymentResultVO::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add))
                                    .subtract(posCashPayments.stream().filter(c -> Objects.nonNull(c.getChangeAmount())).map(PosCashPaymentResultVO::getChangeAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
                } else {
                    resultVO.put("payment_" + basePaymentTypeResultVO.getId(), BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                }
            }
            PosCashThailAmountResultVO posCashThailAmountResultVO = thailAmountResultVOMap.get(posCashResultVO.getId());
            if (ObjectUtil.isNotNull(posCashThailAmountResultVO)) {
                resultVO.put("groupBuyPrice", posCashThailAmountResultVO.getGroupBuyPrice());
                resultVO.put("thailPrice", posCashThailAmountResultVO.getThailPrice());
                resultVO.put("groupBuyAmount", posCashThailAmountResultVO.getGroupBuyAmount());
                resultVO.put("thailAmount", posCashThailAmountResultVO.getThailAmount());
            } else {
                resultVO.put("groupBuyPrice", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                resultVO.put("thailPrice", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                resultVO.put("groupBuyAmount", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                resultVO.put("thailAmount", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
            }
            PosCashItemResultVO tableItemResultVO = tableResultVOMap.get(posCashResultVO.getId());
            if (ObjectUtil.isNotNull(tableItemResultVO)) {
                resultVO.put("tablePrice", tableItemResultVO.getAmount());
            } else {
                resultVO.put("tablePrice", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
            }
            PosCashItemResultVO serviceItemResultVO = serviceResultVOMap.get(posCashResultVO.getId());
            if (ObjectUtil.isNotNull(serviceItemResultVO)) {
                resultVO.put("servicePrice", serviceItemResultVO.getAmount());
            } else {
                resultVO.put("servicePrice", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
            }
            PosCashItemResultVO productItemResultVO = productResultVOMap.get(posCashResultVO.getId());
            if (ObjectUtil.isNotNull(productItemResultVO)) {
                resultVO.put("productPrice", productItemResultVO.getAmount());
            } else {
                resultVO.put("productPrice", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
            }
            PosCashItemResultVO powerItemResultVO = powerResultVOMap.get(posCashResultVO.getId());
            if (ObjectUtil.isNotNull(powerItemResultVO)) {
                resultVO.put("powerPrice", powerItemResultVO.getAmount());
            } else {
                resultVO.put("powerPrice", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
            }

            resultMapList.add(resultVO);
        }
        //pageList.setRecords(resultMapList);
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        objectMap.put("records", resultMapList);
        return objectMap;
    }

    @Override
    public List<PosCashDetailsResultVO> posCashDetailsList(PosCashDetailsQuery model) {
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(model);
            DataOverviewQuery storeTimeData = storeTime.getData();
            model.setStartDate(storeTimeData.getStartDate());
            model.setEndDate(storeTimeData.getEndDate());
        }
        if (StringUtils.isNotBlank(model.getCompleteTime_st()) && StringUtils.isBlank(model.getCompleteTime_ed())) {
            model.setCompleteTime_ed(DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
        }
        List<PosCashResultVO> posCashResultVOList = posCashMapper.findAllResultVO(initWarp(model));
        if (CollUtil.isEmpty(posCashResultVOList)) {
            return Collections.emptyList();
        }
        initRemarks(posCashResultVOList);
        List<Long> cashIds = posCashResultVOList.stream().map(PosCashResultVO::getId)
                .collect(Collectors.toList());
        List<PosCashCommenter> posCashCommenterList = new ArrayList<>();
        if (CollUtil.isNotEmpty(cashIds)) {
            posCashCommenterList = posCashMapper.cashCommenterListByCashIds(cashIds);
        }
        List<PosCashCommenterResultVO> cashCommenterListResultVO = BeanPlusUtil.toBeanList(posCashCommenterList, PosCashCommenterResultVO.class);
        Map<Long, String> baseEmployeeMap = MapUtil.newHashMap();
        if (CollUtil.isNotEmpty(cashCommenterListResultVO)) {
            R<List<BaseEmployeeResultVO>> employeeListR = employeeApi.findListByIds(cashCommenterListResultVO.stream().map(PosCashCommenterResultVO::getEmployeeId).collect(Collectors.toList()));
            Map<Long, String> collect = employeeListR.getData()
                    .stream().collect(Collectors.toMap(BaseEmployeeResultVO::getId, BaseEmployeeResultVO::getRealName));
            if (CollUtil.isNotEmpty(collect)) {
                baseEmployeeMap = collect;
            }
        }
        Map<Long, List<PosCashCommenterResultVO>> cashCommenterListResultVOMap = cashCommenterListResultVO.stream().collect(Collectors.groupingBy(PosCashCommenterResultVO::getCashId));
        // 所有支付方式
        List<PosCashPaymentResultVO> cashPaymentList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(cashIds)) {
            cashPaymentList = posCashPaymentMapper.queryListByCashIds(cashIds);
        }
        echoService.action(cashPaymentList);
        Map<Long, List<PosCashPaymentResultVO>> cashPaymentListMap = cashPaymentList.stream().collect(Collectors.groupingBy(PosCashPaymentResultVO::getCashId));
        // 所有台桌id
        List<Long> tableIdList = posCashResultVOList.stream().map(PosCashResultVO::getTableId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        R<List<BaseTableInfoResultVO>> tableAllR = tableApi.getTableAll(BaseTableInfoPageQuery.builder().build());
        List<BaseTableInfoResultVO> tableInfoResultVOS = tableAllR.getData();
        tableInfoResultVOS = tableInfoResultVOS.stream().filter(v -> tableIdList.contains(v.getId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(tableInfoResultVOS)) {
            tableInfoResultVOS = Lists.newArrayList();
        }
        Map<Long, BaseTableInfoResultVO> baseTableInfoResultVOMap = tableInfoResultVOS.stream().collect(Collectors.toMap(BaseTableInfoResultVO::getId, Function.identity()));
        // 会员信息
        Map<Long, MemberInfoResultVO> memberInfoMap = new HashMap<>();
        List<Long> memberIdList = posCashResultVOList.stream().map(PosCashResultVO::getMemberId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(memberIdList)) {
            List<MemberInfoResultVO> memberInfoList = memberInfoApi.queryByIds(memberIdList).getData();
            memberInfoMap = memberInfoList.stream().collect(Collectors.toMap(MemberInfoResultVO::getId, Function.identity()));
        }
        List<PosCashDetailsResultVO> resultMapList = new ArrayList<>();
        for (PosCashResultVO posCashResultVO : posCashResultVOList) {
            if (posCashResultVO.getRefundAmount() == null) {
                posCashResultVO.setRefundAmount(BigDecimal.ZERO);
            }
            if (posCashResultVO.getPaid() == null) {
                posCashResultVO.setPaid(BigDecimal.ZERO);
            }

            BaseTableInfoResultVO baseTableInfoResultVO = baseTableInfoResultVOMap.get(posCashResultVO.getTableId());

            List<PosCashPaymentResultVO> cashPaymentResultVOList = cashPaymentListMap.get(posCashResultVO.getId());
            StringBuilder paymentDetails = new StringBuilder();
            if (CollUtil.isNotEmpty(cashPaymentResultVOList)) {
                for (PosCashPaymentResultVO paymentResultVO : cashPaymentResultVOList) {
                    paymentDetails.append(paymentResultVO.getEchoMap().get("payTypeId").toString()).append(":").append(paymentResultVO.getAmount().subtract(Objects.nonNull(paymentResultVO.getRefundAmount()) ? paymentResultVO.getRefundAmount() : BigDecimal.ZERO).subtract(Objects.nonNull(paymentResultVO.getChangeAmount()) ? paymentResultVO.getChangeAmount() : BigDecimal.ZERO)).append("元;");
                }
            }

            PosCashDetailsResultVO tableInfoMap = new PosCashDetailsResultVO();
            tableInfoMap.setCode(posCashResultVO.getCode());
            tableInfoMap.setBillState(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("billState")) ? posCashResultVO.getEchoMap().get("billState").toString() : "");
            tableInfoMap.setBillType(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("billType")) ? posCashResultVO.getEchoMap().get("billType").toString() : "");
            tableInfoMap.setType(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("type")) ? posCashResultVO.getEchoMap().get("type").toString() : "");
            tableInfoMap.setTableName(posCashResultVO.getTableName());
            tableInfoMap.setTableType(Objects.nonNull(baseTableInfoResultVO) && Objects.nonNull(baseTableInfoResultVO.getTableType()) && Objects.nonNull(baseTableInfoResultVO.getEchoMap()) ? baseTableInfoResultVO.getEchoMap().get("tableType").toString() : "");
            tableInfoMap.setTableArea(Objects.nonNull(baseTableInfoResultVO) && Objects.nonNull(baseTableInfoResultVO.getTableArea()) && Objects.nonNull(baseTableInfoResultVO.getEchoMap()) ? baseTableInfoResultVO.getEchoMap().get("tableArea").toString() : "");
            tableInfoMap.setCreatedEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("createdEmp")) ? posCashResultVO.getEchoMap().get("createdEmp").toString() : "");
            tableInfoMap.setCompleteEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("completeEmp")) ? posCashResultVO.getEchoMap().get("completeEmp").toString() : "");
            tableInfoMap.setEmployeeEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("employeeId")) ? posCashResultVO.getEchoMap().get("employeeId").toString() : "");
            if (CollUtil.isNotEmpty(cashCommenterListResultVOMap.get(posCashResultVO.getId()))) {
                Map<Long, String> finalBaseEmployeeMap = baseEmployeeMap;
                tableInfoMap.setCommenter(cashCommenterListResultVOMap.get(posCashResultVO.getId())
                        .stream().filter(c -> CollUtil.isNotEmpty(finalBaseEmployeeMap) && ObjectUtil.isNotNull(finalBaseEmployeeMap.containsKey(c.getEmployeeId())))
                        .map(s -> finalBaseEmployeeMap.get(s.getEmployeeId())).collect(Collectors.joining(",")));
            } else {
                tableInfoMap.setCommenter("-");
            }
            tableInfoMap.setOrderSource(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("orderSource")) ? posCashResultVO.getEchoMap().get("orderSource").toString() : "");
            MemberInfoResultVO memberInfo = memberInfoMap.get(posCashResultVO.getMemberId());
            String memberName = "-";
            if (Objects.nonNull(memberInfo)) {
                memberName = memberInfo.getName() + "(" + StrUtil.subSuf(memberInfo.getMobile(), memberInfo.getMobile().length() - 4) + ")";
            }
            tableInfoMap.setMemberName(memberName);
            tableInfoMap.setCreateTime(DateUtils.format(posCashResultVO.getCreatedTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
            tableInfoMap.setCompleteTime(DateUtils.format(posCashResultVO.getCompleteTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
            tableInfoMap.setRegistrationTime(Objects.nonNull(posCashResultVO.getRegistrationTime()) ? DateUtils.format(posCashResultVO.getRegistrationTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT) : "");
            tableInfoMap.setTableAmount(posCashResultVO.getTableAmount());
            tableInfoMap.setProductAmount(posCashResultVO.getProductAmount());
            tableInfoMap.setServiceAmount(posCashResultVO.getServiceAmount());
            tableInfoMap.setThailAmount(posCashResultVO.getThailAmount());
            tableInfoMap.setAmount(posCashResultVO.getAmount());
            tableInfoMap.setDiscountAmount(posCashResultVO.getDiscountAmount());
            tableInfoMap.setRefundAmount(posCashResultVO.getRefundAmount());
            tableInfoMap.setPaid(posCashResultVO.getAmount().subtract(posCashResultVO.getDiscountAmount()).subtract(posCashResultVO.getRefundAmount()));
            tableInfoMap.setOrderRemarks(posCashResultVO.getOrderRemarks());
            tableInfoMap.setRegistrationRemarks(posCashResultVO.getRegistrationRemarks());
            tableInfoMap.setPaymentDetails(paymentDetails.toString());
            tableInfoMap.setOrg(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("orgId")) ? posCashResultVO.getEchoMap().get("orgId").toString() : "");
            resultMapList.add(tableInfoMap);
        }
        return resultMapList;
    }

    private void initRemarks(List<PosCashResultVO> posCashResultVOList) {
        for (PosCashResultVO posCash : posCashResultVOList) {
            //相关备注
            if (StrUtil.isNotBlank(posCash.getRemarks())) {
                OrderRemarksResultVO orderRemarksResultVO = JSON.parseObject(posCash.getRemarks(), OrderRemarksResultVO.class);
                String orderTags = orderRemarksResultVO.getOrderTags();
                posCash.setOrderRemarks("");
                if (StringUtils.isNotBlank(orderTags)) {
                    posCash.setOrderRemarks(orderTags);
                }
                String registrationTags = orderRemarksResultVO.getRegistrationTags();
                posCash.setRegistrationRemarks("");
                posCash.setRegistrationCustomRemarks(orderRemarksResultVO.getRegistrationRemarks());
                if (StringUtils.isNotBlank(registrationTags)) {
                    posCash.setRegistrationRemarks(registrationTags);
                }
                posCash.setRemarks("");
                if (StringUtils.isNotBlank(orderRemarksResultVO.getRemarks())) {
                    posCash.setRemarks(orderRemarksResultVO.getRemarks());
                }
            }
        }

        echoService.action(posCashResultVOList);
        for (PosCashResultVO posCash : posCashResultVOList) {
            String remarks = "";
            String registrationTags = "";
            if (CollUtil.isNotEmpty(posCash.getEchoMap()) &&
                    ObjectUtil.isNotNull(posCash.getEchoMap().get("orderRemarks"))) {
                remarks = remarks.concat(posCash.getEchoMap().get("orderRemarks").toString());
            }
            if (CollUtil.isNotEmpty(posCash.getEchoMap()) &&
                    ObjectUtil.isNotNull(posCash.getEchoMap().get("registrationRemarks"))) {
                registrationTags = registrationTags.concat(posCash.getEchoMap().get("registrationRemarks").toString());
            }
            if (StrUtil.isNotBlank(posCash.getRemarks())) {
                if (StrUtil.isNotBlank(remarks)) {
                    remarks = remarks.concat(" ");
                }
                remarks = remarks.concat(posCash.getRemarks());
            }
            if (StrUtil.isNotBlank(posCash.getRegistrationCustomRemarks())) {
                if (StrUtil.isNotBlank(registrationTags)) {
                    registrationTags = registrationTags.concat(" ");
                }
                registrationTags = registrationTags.concat(posCash.getRegistrationCustomRemarks());
            }
            posCash.setOrderRemarks(remarks);
            posCash.setRemarks(remarks);
            posCash.setRegistrationRemarks(registrationTags);
        }
    }

    @Override
    public Map<String, Object> posCashDetailsSum(PosCashDetailsQuery params) {
        initOrgIdList(params);

        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(params);
            DataOverviewQuery storeTimeData = storeTime.getData();
            params.setStartDate(storeTimeData.getStartDate());
            params.setEndDate(storeTimeData.getEndDate());
        }
        if (StringUtils.isNotBlank(params.getCompleteTime_st()) && StringUtils.isBlank(params.getCompleteTime_ed())) {
            params.setCompleteTime_ed(DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
        }
        List<PosCashResultVO> posCashResultVOS = posCashMapper.findAllResultVO(initWarp(params));
        echoService.action(posCashResultVOS);
        posCashResultVOS = posCashResultVOS.stream().filter(s -> !StringUtils.equals(s.getBillState(), PosCashBillStateEnum.REFUNDED.getCode())).collect(Collectors.toList());

        List<Long> cashIds = posCashResultVOS.stream().map(PosCashResultVO::getId).distinct()
                .collect(Collectors.toList());
        // 所有支付方式
        List<PosCashPaymentResultVO> cashPaymentList = Lists.newArrayList();
        PosCashThailAmountResultVO thailAmountResultVO = new PosCashThailAmountResultVO();
        PosCashItemResultVO tableItemResultVO = new PosCashItemResultVO();
        PosCashItemResultVO productItemResultVO = new PosCashItemResultVO();
        PosCashItemResultVO serviceItemResultVO = new PosCashItemResultVO();
        PosCashItemResultVO powerItemResultVO = new PosCashItemResultVO();
        if (CollUtil.isNotEmpty(cashIds)) {
            cashPaymentList = posCashPaymentMapper.queryListByCashIds(cashIds);
            QueryWrap<PosCashThail> wrap = new QueryWrap<>();
            wrap.in("pc.id", cashIds).eq("pc.delete_flag", 0).isNull("t.cash_thail_id")
                    .eq("t.delete_flag", 0);
            List<PosCashThailAmountResultVO> thailAmountResultVOList = posCashMapper.thailAmountList(wrap);
            if (CollUtil.isNotEmpty(thailAmountResultVOList)) {
                thailAmountResultVO = thailAmountResultVOList.get(0);
            }
            QueryWrap<PosCash> itemWrap = new QueryWrap<>();
            itemWrap.in("pc.id", cashIds).eq("pc.delete_flag", 0).isNull("t.cash_thail_id")
                    .eq("t.delete_flag", 0);
            List<PosCashItemResultVO> tableItemList = posCashMapper.tableAmountList(itemWrap);
            if (CollUtil.isNotEmpty(tableItemList)) {
                tableItemResultVO = tableItemList.get(0);
            }
            List<PosCashItemResultVO> productItemList = posCashMapper.productAmountList(itemWrap);
            if (CollUtil.isNotEmpty(productItemList)) {
                productItemResultVO = productItemList.get(0);
            }
            List<PosCashItemResultVO> serviceItemList = posCashMapper.serviceAmountList(itemWrap);
            if (CollUtil.isNotEmpty(serviceItemList)) {
                serviceItemResultVO = serviceItemList.get(0);
            }
            List<PosCashItemResultVO> powerItemList = posCashMapper.powerAmountList(itemWrap);
            if (CollUtil.isNotEmpty(powerItemList)) {
                powerItemResultVO = powerItemList.get(0);
            }
        }
        echoService.action(cashPaymentList);

        PosCashDetailsResultVO resultMap = new PosCashDetailsResultVO();
        resultMap.setTableAmount(posCashResultVOS.stream().map(PosCashResultVO::getTableAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultMap.setProductAmount(posCashResultVOS.stream().map(PosCashResultVO::getProductAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultMap.setServiceAmount(posCashResultVOS.stream().map(PosCashResultVO::getServiceAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultMap.setThailAmount(posCashResultVOS.stream().map(PosCashResultVO::getThailAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultMap.setAmount(posCashResultVOS.stream().map(PosCashResultVO::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultMap.setDiscountAmount(posCashResultVOS.stream().map(PosCashResultVO::getDiscountAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultMap.setRefundAmount(posCashResultVOS.stream().map(PosCashResultVO::getRefundAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultMap.setPaid(resultMap.getAmount().subtract(resultMap.getRefundAmount()).subtract(resultMap.getDiscountAmount()));
        Map resultVO = BeanPlusUtil.toBean(resultMap, Map.class);
        Map<Long, BigDecimal> paymentMap = cashPaymentList.stream()
                .collect(Collectors.groupingBy(
                        PosCashPaymentResultVO::getPayTypeId, // 按 payTypeId 分组
                        Collectors.collectingAndThen(Collectors.toList(), list ->
                                list.stream()
                                        .map(p -> p.getAmount() == null ? BigDecimal.ZERO : p.getAmount())
                                        .reduce(BigDecimal.ZERO, BigDecimal::add)
                                        .subtract(list.stream()
                                                .map(p -> p.getRefundAmount() == null ? BigDecimal.ZERO : p.getRefundAmount())
                                                .reduce(BigDecimal.ZERO, BigDecimal::add))
                                        .subtract(list.stream()
                                                .map(p -> p.getChangeAmount() == null ? BigDecimal.ZERO : p.getChangeAmount())
                                                .reduce(BigDecimal.ZERO, BigDecimal::add))
                        ))
                );
        for (Long payTypeId : paymentMap.keySet()) {
            resultVO.put("payment_" + payTypeId, paymentMap.get(payTypeId));
        }
        if (Objects.nonNull(thailAmountResultVO)) {
            resultVO.put("groupBuyPrice", thailAmountResultVO.getThailPrice());
            resultVO.put("groupBuyAmount", thailAmountResultVO.getThailAmount());
            resultVO.put("thailPrice", thailAmountResultVO.getThailPrice());
            resultVO.put("thailAmount", thailAmountResultVO.getThailAmount());
        } else {
            resultVO.put("groupBuyPrice", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
            resultVO.put("groupBuyAmount", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
            resultVO.put("thailPrice", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
            resultVO.put("thailAmount", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        }
        if (Objects.nonNull(tableItemResultVO)) {
            resultVO.put("tablePrice", tableItemResultVO.getAmount());
        } else {
            resultVO.put("tablePrice", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        }
        if (Objects.nonNull(productItemResultVO)) {
            resultVO.put("productPrice", productItemResultVO.getAmount());
        } else {
            resultVO.put("productPrice", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        }
        if (Objects.nonNull(serviceItemResultVO)) {
            resultVO.put("servicePrice", serviceItemResultVO.getAmount());
        } else {
            resultVO.put("servicePrice", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        }
        if (Objects.nonNull(powerItemResultVO)) {
            resultVO.put("powerPrice", powerItemResultVO.getAmount());
        } else {
            resultVO.put("powerPrice", BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        }

        return resultVO;
    }

    @Override
    public Map<String, Object> noPayPage(PageParams<DataOverviewQuery> params) {
        // 需要展示商品 详情, 服务时长, 台桌时长,
        initOrgIdList(params.getModel());

        // 设置表头
        List<ColumnVO> columnVOList = com.google.common.collect.Lists.newArrayList(
                ColumnVO.builder().name("code").label("流水号")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("type").label("业务类型")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("tableName").label("台桌名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableType").label("台桌类型")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableArea").label("台桌区域")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createdEmp").label("操作员工")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("employeeEmp").label("销售员工")// employeeId
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orderSource").label("订单来源")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("memberName").label("会员名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createTime").label("开台时间")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableAmount").label("台桌金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("productAmount").label("商品金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceAmount").label("服务金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("thailAmount").label("套餐金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("订单原价")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("discountAmount").label("订单优惠金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("refundAmount").label("订单部分退款金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("paid").label("订单实收金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orderRemarks").label("整单备注")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("registrationRemarks").label("挂单备注")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableDetails").label("台桌详情")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("productDetails").label("商品详情")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceDetails").label("服务详情")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("thailDetails").label("套餐详情")  // 只展示套餐名称
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(180).emptyString("-").fixed(false).build()
        );
        DataOverviewQuery model = params.getModel();
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(model);
            DataOverviewQuery storeTimeData = storeTime.getData();
            model.setStartDate(storeTimeData.getStartDate());
            model.setEndDate(storeTimeData.getEndDate());
        }
        IPage<PosCashResultVO> pageResultVO = posCashMapper.selectPageResultVO(params.buildPage(PosCash.class), noPay(model));
        if (CollUtil.isEmpty(pageResultVO.getRecords())) {
            Map<String, Object> objectMap = BeanUtil.beanToMap(pageResultVO);
            objectMap.put("columnList", columnVOList);
            return objectMap;
        }
        initRemarks(pageResultVO.getRecords());
        IPage<PosCashNoPayDetailsResultVO> pageList = BeanPlusUtil.toBeanPage(pageResultVO, PosCashNoPayDetailsResultVO.class);
        List<Long> cashIds = pageResultVO.getRecords().stream().map(PosCashResultVO::getId)
                .collect(Collectors.toList());

        // 台桌详情信息
        List<PosCashTable> cashTableList = posCashMapper.cashTableListByCashIds(cashIds);
        Map<Long, List<PosCashTable>> cashTableMap = cashTableList.stream().collect(Collectors.groupingBy(PosCashTable::getCashId));

        // 商品详情信息
        List<PosCashProduct> cashProductList = posCashMapper.cashProductListByCashIds(cashIds);
        Map<Long, List<PosCashProduct>> cashProductMap = cashProductList.stream().collect(Collectors.groupingBy(PosCashProduct::getCashId));

        // 服务详情信息
        List<top.kx.kxss.app.entity.cash.service.PosCashService> cashServiceList = posCashMapper.cashServiceListByCashIds(cashIds);
        Map<Long, List<top.kx.kxss.app.entity.cash.service.PosCashService>> cashServiceMap = cashServiceList.stream().collect(Collectors.groupingBy(top.kx.kxss.app.entity.cash.service.PosCashService::getCashId));

        // 套餐详情信息
        List<PosCashThail> cashThailList = posCashMapper.cashThailListByCashIds(cashIds);
        Map<Long, List<PosCashThail>> cashThailMap = cashThailList.stream().collect(Collectors.groupingBy(PosCashThail::getCashId));

        // 所有台桌id
        List<Long> tableIdList = pageResultVO.getRecords().stream().map(PosCashResultVO::getTableId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        R<List<BaseTableInfoResultVO>> tableAllR = tableApi.getTableAll(BaseTableInfoPageQuery.builder().build());
        List<BaseTableInfoResultVO> tableInfoResultVOS = tableAllR.getData();
        tableInfoResultVOS = tableInfoResultVOS.stream().filter(v -> tableIdList.contains(v.getId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(tableInfoResultVOS)) {
            tableInfoResultVOS = Lists.newArrayList();
        }
        Map<Long, BaseTableInfoResultVO> baseTableInfoResultVOMap = tableInfoResultVOS.stream().collect(Collectors.toMap(BaseTableInfoResultVO::getId, Function.identity()));
        // 会员信息
        Map<Long, MemberInfoResultVO> memberInfoMap = new HashMap<>();
        List<Long> memberIdList = pageResultVO.getRecords().stream().map(PosCashResultVO::getMemberId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(memberIdList)) {
            List<MemberInfoResultVO> memberInfoList = memberInfoApi.queryByIds(memberIdList).getData();
            memberInfoMap = memberInfoList.stream().collect(Collectors.toMap(MemberInfoResultVO::getId, Function.identity()));
        }
        List<PosCashNoPayDetailsResultVO> resultMapList = new ArrayList<>();
        for (PosCashResultVO posCashResultVO : pageResultVO.getRecords()) {
            if (posCashResultVO.getRefundAmount() == null) {
                posCashResultVO.setRefundAmount(BigDecimal.ZERO);
            }
            if (posCashResultVO.getPaid() == null) {
                posCashResultVO.setPaid(BigDecimal.ZERO);
            }

            BaseTableInfoResultVO baseTableInfoResultVO = baseTableInfoResultVOMap.get(posCashResultVO.getTableId());

            PosCashNoPayDetailsResultVO detailsResultVO = new PosCashNoPayDetailsResultVO();
            detailsResultVO.setCode(posCashResultVO.getCode());
            detailsResultVO.setType(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("type")) ? posCashResultVO.getEchoMap().get("type").toString() : "");
            detailsResultVO.setTableName(posCashResultVO.getTableName());
            detailsResultVO.setTableType(Objects.nonNull(baseTableInfoResultVO) && Objects.nonNull(baseTableInfoResultVO.getTableType()) && Objects.nonNull(baseTableInfoResultVO.getEchoMap()) ? baseTableInfoResultVO.getEchoMap().get("tableType").toString() : "");
            detailsResultVO.setTableArea(Objects.nonNull(baseTableInfoResultVO) && Objects.nonNull(baseTableInfoResultVO.getTableArea()) && Objects.nonNull(baseTableInfoResultVO.getEchoMap()) ? baseTableInfoResultVO.getEchoMap().get("tableArea").toString() : "");
            detailsResultVO.setCreatedEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("createdEmp")) ? posCashResultVO.getEchoMap().get("createdEmp").toString() : "");
            detailsResultVO.setEmployeeEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("employeeId")) ? posCashResultVO.getEchoMap().get("employeeId").toString() : "");
            detailsResultVO.setOrderSource(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("orderSource")) ? posCashResultVO.getEchoMap().get("orderSource").toString() : "");
            MemberInfoResultVO memberInfo = memberInfoMap.get(posCashResultVO.getMemberId());
            String memberName = "-";
            if (Objects.nonNull(memberInfo)) {
                memberName = memberInfo.getName() + "(" + StrUtil.subSuf(memberInfo.getMobile(), memberInfo.getMobile().length() - 4) + ")";
            }
            detailsResultVO.setMemberName(memberName);
            detailsResultVO.setCreateTime(posCashResultVO.getCreatedTime());
            detailsResultVO.setTableAmount(posCashResultVO.getTableAmount());
            detailsResultVO.setProductAmount(posCashResultVO.getProductAmount());
            detailsResultVO.setServiceAmount(posCashResultVO.getServiceAmount());
            detailsResultVO.setThailAmount(posCashResultVO.getThailAmount());
            detailsResultVO.setAmount(posCashResultVO.getAmount());
            detailsResultVO.setDiscountAmount(posCashResultVO.getDiscountAmount());
            detailsResultVO.setRefundAmount(posCashResultVO.getRefundAmount());
            detailsResultVO.setPaid(posCashResultVO.getAmount().subtract(posCashResultVO.getDiscountAmount()).subtract(posCashResultVO.getRefundAmount()));
            detailsResultVO.setOrderRemarks(posCashResultVO.getOrderRemarks());
            detailsResultVO.setRegistrationRemarks(posCashResultVO.getRegistrationRemarks());
            detailsResultVO.setOrg(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("orgId")) ? posCashResultVO.getEchoMap().get("orgId").toString() : "");
            List<PosCashTable> posCashTableList = cashTableMap.get(posCashResultVO.getId());
            if (CollUtil.isNotEmpty(posCashTableList)) {
                detailsResultVO.setTableDetails(posCashTableList.stream().map(s -> s.getTableName() + "-" + s.getDuration() + "分钟").collect(Collectors.joining(";")));
            }
            List<PosCashProduct> posCashProductList = cashProductMap.get(posCashResultVO.getId());
            if (CollUtil.isNotEmpty(posCashProductList)) {
                detailsResultVO.setProductDetails(posCashProductList.stream().map(s -> s.getProductName() + "*" + ((Objects.nonNull(s.getNum()) ? s.getNum() : 0) - (Objects.nonNull(s.getRefundNum()) ? s.getRefundNum() : 0))).collect(Collectors.joining(";")));
            }
            List<top.kx.kxss.app.entity.cash.service.PosCashService> posCashServiceList = cashServiceMap.get(posCashResultVO.getId());
            if (CollUtil.isNotEmpty(posCashServiceList)) {
                detailsResultVO.setServiceDetails(posCashServiceList.stream().map(s -> s.getEmployeeName() + "-" + s.getDuration() + "分钟").collect(Collectors.joining(";")));
            }
            List<PosCashThail> posCashThailList = cashThailMap.get(posCashResultVO.getId());
            if (CollUtil.isNotEmpty(posCashThailList)) {
                detailsResultVO.setThailDetails(posCashThailList.stream().map(PosCashThail::getThailName).collect(Collectors.joining(";")));
            }
            resultMapList.add(detailsResultVO);
        }
        pageList.setRecords(resultMapList);
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }


    @Override
    public PosCashNoPayDetailsResultVO noPaySum(DataOverviewQuery model) {
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(model);
            DataOverviewQuery storeTimeData = storeTime.getData();
            model.setStartDate(storeTimeData.getStartDate());
            model.setEndDate(storeTimeData.getEndDate());
        }
        return posCashMapper.findSumResultVO(noPay(model));
    }

    @Override
    public List<PosCashNoPayDetailsResultVO> noPayList(DataOverviewQuery model) {
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(model);
            DataOverviewQuery storeTimeData = storeTime.getData();
            model.setStartDate(storeTimeData.getStartDate());
            model.setEndDate(storeTimeData.getEndDate());
        }
        List<PosCashResultVO> posCashResultVOList = posCashMapper.findAllResultVO(noPay(model));
        if (CollUtil.isEmpty(posCashResultVOList)) {
            return Collections.emptyList();
        }
        initRemarks(posCashResultVOList);
        List<Long> cashIds = posCashResultVOList.stream().map(PosCashResultVO::getId)
                .collect(Collectors.toList());

        // 台桌详情信息
        List<PosCashTable> cashTableList = posCashMapper.cashTableListByCashIds(cashIds);
        Map<Long, List<PosCashTable>> cashTableMap = cashTableList.stream().collect(Collectors.groupingBy(PosCashTable::getCashId));

        // 商品详情信息
        List<PosCashProduct> cashProductList = posCashMapper.cashProductListByCashIds(cashIds);
        Map<Long, List<PosCashProduct>> cashProductMap = cashProductList.stream().collect(Collectors.groupingBy(PosCashProduct::getCashId));

        // 服务详情信息
        List<top.kx.kxss.app.entity.cash.service.PosCashService> cashServiceList = posCashMapper.cashServiceListByCashIds(cashIds);
        Map<Long, List<top.kx.kxss.app.entity.cash.service.PosCashService>> cashServiceMap = cashServiceList.stream().collect(Collectors.groupingBy(top.kx.kxss.app.entity.cash.service.PosCashService::getCashId));

        // 套餐详情信息
        List<PosCashThail> cashThailList = posCashMapper.cashThailListByCashIds(cashIds);
        Map<Long, List<PosCashThail>> cashThailMap = cashThailList.stream().collect(Collectors.groupingBy(PosCashThail::getCashId));

        // 所有台桌id
        List<Long> tableIdList = posCashResultVOList.stream().map(PosCashResultVO::getTableId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        R<List<BaseTableInfoResultVO>> tableAllR = tableApi.getTableAll(BaseTableInfoPageQuery.builder().build());
        List<BaseTableInfoResultVO> tableInfoResultVOS = tableAllR.getData();
        tableInfoResultVOS = tableInfoResultVOS.stream().filter(v -> tableIdList.contains(v.getId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(tableInfoResultVOS)) {
            tableInfoResultVOS = Lists.newArrayList();
        }
        Map<Long, BaseTableInfoResultVO> baseTableInfoResultVOMap = tableInfoResultVOS.stream().collect(Collectors.toMap(BaseTableInfoResultVO::getId, Function.identity()));
        // 会员信息
        Map<Long, MemberInfoResultVO> memberInfoMap = new HashMap<>();
        List<Long> memberIdList = posCashResultVOList.stream().map(PosCashResultVO::getMemberId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(memberIdList)) {
            List<MemberInfoResultVO> memberInfoList = memberInfoApi.queryByIds(memberIdList).getData();
            memberInfoMap = memberInfoList.stream().collect(Collectors.toMap(MemberInfoResultVO::getId, Function.identity()));
        }
        List<PosCashNoPayDetailsResultVO> resultMapList = new ArrayList<>();
        for (PosCashResultVO posCashResultVO : posCashResultVOList) {
            if (posCashResultVO.getRefundAmount() == null) {
                posCashResultVO.setRefundAmount(BigDecimal.ZERO);
            }
            if (posCashResultVO.getPaid() == null) {
                posCashResultVO.setPaid(BigDecimal.ZERO);
            }

            BaseTableInfoResultVO baseTableInfoResultVO = baseTableInfoResultVOMap.get(posCashResultVO.getTableId());

            PosCashNoPayDetailsResultVO detailsResultVO = new PosCashNoPayDetailsResultVO();
            detailsResultVO.setCode(posCashResultVO.getCode());
            detailsResultVO.setType(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("type")) ? posCashResultVO.getEchoMap().get("type").toString() : "");
            detailsResultVO.setTableName(posCashResultVO.getTableName());
            detailsResultVO.setTableType(Objects.nonNull(baseTableInfoResultVO) && Objects.nonNull(baseTableInfoResultVO.getTableType()) && Objects.nonNull(baseTableInfoResultVO.getEchoMap()) ? baseTableInfoResultVO.getEchoMap().get("tableType").toString() : "");
            detailsResultVO.setTableArea(Objects.nonNull(baseTableInfoResultVO) && Objects.nonNull(baseTableInfoResultVO.getTableArea()) && Objects.nonNull(baseTableInfoResultVO.getEchoMap()) ? baseTableInfoResultVO.getEchoMap().get("tableArea").toString() : "");
            detailsResultVO.setCreatedEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("createdEmp")) ? posCashResultVO.getEchoMap().get("createdEmp").toString() : "");
            detailsResultVO.setEmployeeEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("employeeId")) ? posCashResultVO.getEchoMap().get("employeeId").toString() : "");
            detailsResultVO.setOrderSource(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("orderSource")) ? posCashResultVO.getEchoMap().get("orderSource").toString() : "");
            MemberInfoResultVO memberInfo = memberInfoMap.get(posCashResultVO.getMemberId());
            String memberName = "-";
            if (Objects.nonNull(memberInfo)) {
                memberName = memberInfo.getName() + "(" + StrUtil.subSuf(memberInfo.getMobile(), memberInfo.getMobile().length() - 4) + ")";
            }
            detailsResultVO.setMemberName(memberName);
            detailsResultVO.setCreateTime(posCashResultVO.getCreatedTime());
            detailsResultVO.setTableAmount(posCashResultVO.getTableAmount());
            detailsResultVO.setProductAmount(posCashResultVO.getProductAmount());
            detailsResultVO.setServiceAmount(posCashResultVO.getServiceAmount());
            detailsResultVO.setThailAmount(posCashResultVO.getThailAmount());
            detailsResultVO.setAmount(posCashResultVO.getAmount());
            detailsResultVO.setDiscountAmount(posCashResultVO.getDiscountAmount());
            detailsResultVO.setRefundAmount(posCashResultVO.getRefundAmount());
            detailsResultVO.setPaid(posCashResultVO.getAmount().subtract(posCashResultVO.getDiscountAmount()).subtract(posCashResultVO.getRefundAmount()));
            detailsResultVO.setOrderRemarks(posCashResultVO.getOrderRemarks());
            detailsResultVO.setRegistrationRemarks(posCashResultVO.getRegistrationRemarks());
            detailsResultVO.setOrg(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("orgId")) ? posCashResultVO.getEchoMap().get("orgId").toString() : "");
            List<PosCashTable> posCashTableList = cashTableMap.get(posCashResultVO.getId());
            if (CollUtil.isNotEmpty(posCashTableList)) {
                detailsResultVO.setTableDetails(posCashTableList.stream().map(s -> s.getTableName() + "-" + s.getDuration() + "分钟").collect(Collectors.joining(";")));
            }
            List<PosCashProduct> posCashProductList = cashProductMap.get(posCashResultVO.getId());
            if (CollUtil.isNotEmpty(posCashProductList)) {
                detailsResultVO.setProductDetails(posCashProductList.stream().map(s -> s.getProductName() + "*" + ((Objects.nonNull(s.getNum()) ? s.getNum() : 0) - (Objects.nonNull(s.getRefundNum()) ? s.getRefundNum() : 0))).collect(Collectors.joining(";")));
            }
            List<top.kx.kxss.app.entity.cash.service.PosCashService> posCashServiceList = cashServiceMap.get(posCashResultVO.getId());
            if (CollUtil.isNotEmpty(posCashServiceList)) {
                detailsResultVO.setServiceDetails(posCashServiceList.stream().map(s -> s.getEmployeeName() + "-" + s.getDuration() + "分钟").collect(Collectors.joining(";")));
            }
            List<PosCashThail> posCashThailList = cashThailMap.get(posCashResultVO.getId());
            if (CollUtil.isNotEmpty(posCashThailList)) {
                detailsResultVO.setThailDetails(posCashThailList.stream().map(PosCashThail::getThailName).collect(Collectors.joining(";")));
            }
            resultMapList.add(detailsResultVO);
        }
        return resultMapList;
    }

    @Override
    public IPage<OrderResultVO> thailPage(PageParams<ThailOverviewQuery> query) {
        ThailOverviewQuery model = query.getModel();
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(model);
            DataOverviewQuery storeTimeData = storeTime.getData();
            model.setStartDate(storeTimeData.getStartDate());
            model.setEndDate(storeTimeData.getEndDate());
        }
        IPage<OrderResultVO> posCashIPage = posCashMapper.thailPage(query.buildPage(PosCash.class), thailWarp(model));
        echoService.action(posCashIPage);
        return posCashIPage;
    }

    @Override
    public Map<String, Object> freePage(PageParams<CashFreeQuery> params) {
        params.setSort("");
        params.setOrder("");
        initOrgIdList(params.getModel());
        List<ColumnVO> columnVOList = com.google.common.collect.Lists.newArrayList(
                ColumnVO.builder().name("code").label("订单号")
                        .width(250).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("typeDesc").label("类型")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("name").label("名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("num").label("数量")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("measuringUnit").label("单位")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("营业额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("discountAmount").label("优惠金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("payment").label("营业收入")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeEmpName").label("操作员工")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeTime").label("完成时间")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("singleRemark").label("单品备注") // 单品备注
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("reviewStatusDesc").label("财务审核")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("reviewEmpName").label("通过人")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("reviewTime").label("通过时间")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("reviewRemark").label("拒绝原因")
                        .width(180).emptyString("-").fixed(false).build()
        );
        CashFreeQuery model = params.getModel();
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(model);
            DataOverviewQuery storeTimeData = storeTime.getData();
            model.setStartDate(storeTimeData.getStartDate());
            model.setEndDate(storeTimeData.getEndDate());
        }
        IPage<OrderFreeResultVO> pageResultVO = posCashMapper.freePage(params.buildPage(OrderFreeResultVO.class), cashFreeWrapper(model));
        if (CollUtil.isEmpty(pageResultVO.getRecords())) {
            Map<String, Object> objectMap = BeanUtil.beanToMap(pageResultVO);
            objectMap.put("columnList", columnVOList);
            return objectMap;
        }
        List<ItemRemarksExtraResultVO> itemRemarksExtraResultVOS = new ArrayList<>();
        pageResultVO.getRecords().forEach(item -> {
            if (StringUtils.isNotBlank(item.getItemRemark())) {
                ItemRemarksExtraResultVO itemRemarksExtraResultVO = JSON.parseObject(item.getItemRemark(), ItemRemarksExtraResultVO.class);
                if (Objects.nonNull(itemRemarksExtraResultVO)) {
                    itemRemarksExtraResultVO.setId(item.getItemId());
                    itemRemarksExtraResultVOS.add(itemRemarksExtraResultVO);
                }
            }
        });
        echoService.action(itemRemarksExtraResultVOS);
        Map<Long, String> itemTagsMap = itemRemarksExtraResultVOS.stream().collect(Collectors.toMap(ItemRemarksExtraResultVO::getId, v -> {
            String itemTags = v.getEchoMap().get("itemTags") != null ? v.getEchoMap().get("itemTags").toString() : "";
            String remarks = v.getRemarks() != null ? v.getRemarks() : "";
            if (StrUtil.isNotBlank(itemTags) && StrUtil.isNotBlank(remarks)) {
                return itemTags + "," + remarks;
            } else if (StrUtil.isNotBlank(itemTags)) {
                return itemTags;
            } else if (StrUtil.isNotBlank(remarks)) {
                return remarks;
            } else {
                return "-";
            }
        }));
        echoService.action(pageResultVO.getRecords());
        pageResultVO.getRecords().forEach(item -> {
            item.setSingleRemark(itemTagsMap.get(item.getItemId()));
            if (CollUtil.isNotEmpty(item.getEchoMap())) {
                if (item.getEchoMap().containsKey("completeEmp") && Objects.nonNull(item.getEchoMap().get("completeEmp"))) {
                    item.setCompleteEmpName(item.getEchoMap().get("completeEmp").toString());
                } else {
                    item.setCompleteEmpName("-");
                }
                if (item.getEchoMap().containsKey("reviewEmp") && Objects.nonNull(item.getEchoMap().get("reviewEmp"))) {
                    item.setReviewEmpName(item.getEchoMap().get("reviewEmp").toString());
                } else {
                    item.setReviewEmpName("-");
                }
            }
        });
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageResultVO);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public OrderFreeResultVO freeSum(CashFreeQuery query) {
        initOrgIdList(query);
        if (StringUtils.isNotBlank(query.getStartDate()) && StringUtils.isNotBlank(query.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(query);
            DataOverviewQuery storeTimeData = storeTime.getData();
            query.setStartDate(storeTimeData.getStartDate());
            query.setEndDate(storeTimeData.getEndDate());
        }
        return posCashMapper.freeSum(cashFreeWrapper(query));
    }

    @Override
    public List<OrderFreeResultVO> freeList(CashFreeQuery query) {
        initOrgIdList(query);
        if (StringUtils.isNotBlank(query.getStartDate()) && StringUtils.isNotBlank(query.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(query);
            DataOverviewQuery storeTimeData = storeTime.getData();
            query.setStartDate(storeTimeData.getStartDate());
            query.setEndDate(storeTimeData.getEndDate());
        }
        List<OrderFreeResultVO> orderFreeResultVOS = posCashMapper.freeList(cashFreeWrapper(query));
        if (CollUtil.isEmpty(orderFreeResultVOS)) {
            return Collections.emptyList();
        }
        List<ItemRemarksExtraResultVO> itemRemarksExtraResultVOS = new ArrayList<>();
        orderFreeResultVOS.forEach(item -> {
            if (StringUtils.isNotBlank(item.getItemRemark())) {
                ItemRemarksExtraResultVO itemRemarksExtraResultVO = JSON.parseObject(item.getItemRemark(), ItemRemarksExtraResultVO.class);
                if (Objects.nonNull(itemRemarksExtraResultVO)) {
                    itemRemarksExtraResultVO.setId(item.getItemId());
                    itemRemarksExtraResultVOS.add(itemRemarksExtraResultVO);
                }
            }
        });
        echoService.action(itemRemarksExtraResultVOS);
        Map<Long, String> itemTagsMap = itemRemarksExtraResultVOS.stream().collect(Collectors.toMap(ItemRemarksExtraResultVO::getId, v -> {
            String itemTags = v.getEchoMap().get("itemTags") != null ? v.getEchoMap().get("itemTags").toString() : "";
            String remarks = v.getRemarks() != null ? v.getRemarks() : "";
            if (StrUtil.isNotBlank(itemTags) && StrUtil.isNotBlank(remarks)) {
                return itemTags + "," + remarks;
            } else if (StrUtil.isNotBlank(itemTags)) {
                return itemTags;
            } else if (StrUtil.isNotBlank(remarks)) {
                return remarks;
            } else {
                return "-";
            }
        }));
        echoService.action(orderFreeResultVOS);
        orderFreeResultVOS.forEach(item -> {
            item.setSingleRemark(itemTagsMap.get(item.getItemId()));
            if (CollUtil.isNotEmpty(item.getEchoMap())) {
                if (item.getEchoMap().containsKey("completeEmp") && Objects.nonNull(item.getEchoMap().get("completeEmp"))) {
                    item.setCompleteEmpName(item.getEchoMap().get("completeEmp").toString());
                } else {
                    item.setCompleteEmpName("-");
                }
                if (item.getEchoMap().containsKey("reviewEmp") && Objects.nonNull(item.getEchoMap().get("reviewEmp"))) {
                    item.setReviewEmpName(item.getEchoMap().get("reviewEmp").toString());
                } else {
                    item.setReviewEmpName("-");
                }
            }
        });
        return orderFreeResultVOS;
    }

    public void initOrgIdList(OrgIdListQuery params) {
        if (Objects.isNull(params)) {
            params = new OrgIdListQuery();
        }
        if (CollUtil.isEmpty(params.getOrgIdList())) {
            params.setOrgIdList(Collections.singletonList(ContextUtil.getCurrentCompanyId()));
        }
    }

}

