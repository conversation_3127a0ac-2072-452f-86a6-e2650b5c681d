package top.kx.kxss.report.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.PosCashCommenter;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.app.entity.cash.table.PosCashTable;
import top.kx.kxss.app.entity.thail.PosCashThail;
import top.kx.kxss.app.vo.result.cash.AmountResultVO;
import top.kx.kxss.app.vo.result.cash.PosCashCommenterResultVO;
import top.kx.kxss.app.vo.result.cash.PosCashResultVO;
import top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO;
import top.kx.kxss.base.vo.query.table.BaseTableInfoPageQuery;
import top.kx.kxss.base.vo.result.member.MemberInfoResultVO;
import top.kx.kxss.base.vo.result.table.BaseTableInfoResultVO;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.employee.EmployeeApi;
import top.kx.kxss.memberInfo.MemberInfoApi;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.pos.vo.OrderRemarksResultVO;
import top.kx.kxss.pos.vo.order.OrderResultVO;
import top.kx.kxss.report.mapper.PosCashMapper;
import top.kx.kxss.report.mapper.PosCashPaymentMapper;
import top.kx.kxss.report.service.PosCashService;
import top.kx.kxss.report.service.common.PosCashCommonCtrl;
import top.kx.kxss.report.vo.PosCashDetailsResultVO;
import top.kx.kxss.report.vo.PosCashNoPayDetailsResultVO;
import top.kx.kxss.table.TableApi;
import top.kx.kxss.wxapp.api.custom.CustomApi;
import top.kx.kxss.wxapp.vo.query.common.OrgIdListQuery;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;
import top.kx.kxss.wxapp.vo.query.statistics.PosCashDetailsQuery;
import top.kx.kxss.wxapp.vo.query.statistics.ThailOverviewQuery;
import top.kx.kxss.wxapp.vo.result.statistics.ColumnVO;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 利润销售API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class PosCashServiceImpl extends PosCashCommonCtrl implements PosCashService {

    private final PosCashMapper posCashMapper;
    private final PosCashPaymentMapper posCashPaymentMapper;
    private final EchoService echoService;
    private final CustomApi customApi;
    private final EmployeeApi employeeApi;
    private final TableApi tableApi;
    private final MemberInfoApi memberInfoApi;


    @Override
    public AmountResultVO selectOneAmount(DataOverviewQuery query) {
        return posCashMapper.selectOneAmount(baseWrapper(query));
    }

    @Override
    public List<AmountResultVO> selectByPayType(Wrapper<PosCash> wrapper) {
        return posCashMapper.selectByPayType(wrapper);
    }

    @Override
    public Map<String, Object> posCashDetails(PageParams<PosCashDetailsQuery> params) {
        initOrgIdList(params.getModel());

        // 设置表头
        List<ColumnVO> columnVOList = com.google.common.collect.Lists.newArrayList(
                ColumnVO.builder().name("code").label("流水号")
                        .width(250).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("billState").label("订单状态")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("billType").label("订单类型")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("type").label("业务类型")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableName").label("台桌名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableType").label("台桌类型")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableArea").label("台桌区域")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createdEmp").label("创建员工")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeEmp").label("完成员工")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("employeeEmp").label("销售员工")// employeeId
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("commenter").label("提成员工")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orderSource").label("订单来源")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("memberName").label("会员名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createTime").label("开台时间")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("completeTime").label("结账时间")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableAmount").label("台桌金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("productAmount").label("商品金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceAmount").label("服务金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("thailAmount").label("套餐金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("订单原价")// 原本是订单金额,原价 54.72, amount 字段
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("discountAmount").label("订单优惠金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("refundAmount").label("订单部分退款金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("paid").label("订单实收金额")
                        .width(180).emptyString("-").fixed(false).build(),// amount - discountAmount - refundAmount
                ColumnVO.builder().name("orderRemarks").label("整单备注")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("registrationRemarks").label("挂单备注")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("paymentDetails").label("支付详情")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(180).emptyString("-").fixed(false).build()
        );
        PosCashDetailsQuery model = params.getModel();
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(model);
            DataOverviewQuery storeTimeData = storeTime.getData();
            model.setStartDate(storeTimeData.getStartDate());
            model.setEndDate(storeTimeData.getEndDate());
        }

        if (StringUtils.isNotBlank(model.getCompleteTime_st()) && StringUtils.isBlank(model.getCompleteTime_ed())) {
            model.setCompleteTime_ed(DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
        }
        IPage<PosCashResultVO> pageResultVO = posCashMapper.selectPageResultVO(params.buildPage(PosCash.class), initWarp(model));
        if (CollUtil.isEmpty(pageResultVO.getRecords())) {
            Map<String, Object> objectMap = BeanUtil.beanToMap(pageResultVO);
            objectMap.put("columnList", columnVOList);
            return objectMap;
        }
        initRemarks(pageResultVO.getRecords());
        IPage<PosCashDetailsResultVO> pageList = BeanPlusUtil.toBeanPage(pageResultVO, PosCashDetailsResultVO.class);
        List<Long> cashIds = pageResultVO.getRecords().stream().map(PosCashResultVO::getId)
                .collect(Collectors.toList());
        List<PosCashCommenter> posCashCommenterList = new ArrayList<>();
        if (CollUtil.isNotEmpty(cashIds)) {
            posCashCommenterList = posCashMapper.cashCommenterListByCashIds(cashIds);
        }

        List<PosCashCommenterResultVO> cashCommenterListResultVO = BeanPlusUtil.toBeanList(posCashCommenterList, PosCashCommenterResultVO.class);
        Map<Long, String> baseEmployeeMap = MapUtil.newHashMap();
        if (CollUtil.isNotEmpty(cashCommenterListResultVO)) {
            List<Long> employeeIds = cashCommenterListResultVO.stream().map(PosCashCommenterResultVO::getEmployeeId).collect(Collectors.toList());
            R<List<BaseEmployeeResultVO>> employeeListR = employeeApi.findListByIds(employeeIds);
            Map<Long, String> collect = employeeListR.getData()
                    .stream().collect(Collectors.toMap(BaseEmployeeResultVO::getId, BaseEmployeeResultVO::getRealName));
            if (CollUtil.isNotEmpty(collect)) {
                baseEmployeeMap = collect;
            }
        }
        Map<Long, List<PosCashCommenterResultVO>> cashCommenterListResultVOMap = cashCommenterListResultVO.stream().collect(Collectors.groupingBy(PosCashCommenterResultVO::getCashId));
        // 所有支付方式
        List<PosCashPaymentResultVO> cashPaymentList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(cashIds)) {
            cashPaymentList = posCashPaymentMapper.queryListByCashIds(cashIds);
        }
        echoService.action(cashPaymentList);
        Map<Long, List<PosCashPaymentResultVO>> cashPaymentListMap = cashPaymentList.stream().collect(Collectors.groupingBy(PosCashPaymentResultVO::getCashId));
        // 所有台桌id
        List<Long> tableIdList = pageResultVO.getRecords().stream().map(PosCashResultVO::getTableId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        R<List<BaseTableInfoResultVO>> tableAllR = tableApi.getTableAll(BaseTableInfoPageQuery.builder().build());
        List<BaseTableInfoResultVO> tableInfoResultVOS = tableAllR.getData();
        tableInfoResultVOS = tableInfoResultVOS.stream().filter(v -> tableIdList.contains(v.getId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(tableInfoResultVOS)) {
            tableInfoResultVOS = Lists.newArrayList();
        }
        Map<Long, BaseTableInfoResultVO> baseTableInfoResultVOMap = tableInfoResultVOS.stream().collect(Collectors.toMap(BaseTableInfoResultVO::getId, Function.identity()));
        // 会员信息
        Map<Long, MemberInfoResultVO> memberInfoMap = new HashMap<>();
        List<Long> memberIdList = pageResultVO.getRecords().stream().map(PosCashResultVO::getMemberId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(memberIdList)) {
            List<MemberInfoResultVO> memberInfoList = memberInfoApi.queryByIds(memberIdList).getData();
            memberInfoMap = memberInfoList.stream().collect(Collectors.toMap(MemberInfoResultVO::getId, Function.identity()));
        }
        List<PosCashDetailsResultVO> resultMapList = new ArrayList<>();
        for (PosCashResultVO posCashResultVO : pageResultVO.getRecords()) {
            if (posCashResultVO.getRefundAmount() == null) {
                posCashResultVO.setRefundAmount(BigDecimal.ZERO);
            }
            if (posCashResultVO.getPaid() == null) {
                posCashResultVO.setPaid(BigDecimal.ZERO);
            }

            BaseTableInfoResultVO baseTableInfoResultVO = baseTableInfoResultVOMap.get(posCashResultVO.getTableId());

            List<PosCashPaymentResultVO> cashPaymentResultVOList = cashPaymentListMap.get(posCashResultVO.getId());
            StringBuilder paymentDetails = new StringBuilder();
            if (CollUtil.isNotEmpty(cashPaymentResultVOList)) {
                for (PosCashPaymentResultVO paymentResultVO : cashPaymentResultVOList) {
                    paymentDetails.append(paymentResultVO.getEchoMap().get("payTypeId").toString()).append(":").append(paymentResultVO.getAmount().subtract(Objects.nonNull(paymentResultVO.getRefundAmount()) ? paymentResultVO.getRefundAmount() : BigDecimal.ZERO).subtract(Objects.nonNull(paymentResultVO.getChangeAmount()) ? paymentResultVO.getChangeAmount() : BigDecimal.ZERO)).append("元;");
                }
            }

            PosCashDetailsResultVO tableInfoMap = new PosCashDetailsResultVO();
            tableInfoMap.setId(posCashResultVO.getId());
            tableInfoMap.setCode(posCashResultVO.getCode());
            tableInfoMap.setBillState(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("billState")) ? posCashResultVO.getEchoMap().get("billState").toString() : "");
            tableInfoMap.setBillType(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("billType")) ? posCashResultVO.getEchoMap().get("billType").toString() : "");
            tableInfoMap.setType(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("type")) ? posCashResultVO.getEchoMap().get("type").toString() : "");
            tableInfoMap.setTableName(posCashResultVO.getTableName());
            tableInfoMap.setTableType(Objects.nonNull(baseTableInfoResultVO) && Objects.nonNull(baseTableInfoResultVO.getTableType()) && Objects.nonNull(baseTableInfoResultVO.getEchoMap()) ? baseTableInfoResultVO.getEchoMap().get("tableType").toString() : "");
            tableInfoMap.setTableArea(Objects.nonNull(baseTableInfoResultVO) && Objects.nonNull(baseTableInfoResultVO.getTableArea()) && Objects.nonNull(baseTableInfoResultVO.getEchoMap()) ? baseTableInfoResultVO.getEchoMap().get("tableArea").toString() : "");
            tableInfoMap.setCreatedEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("createdEmp")) ? posCashResultVO.getEchoMap().get("createdEmp").toString() : "");
            tableInfoMap.setCompleteEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("completeEmp")) ? posCashResultVO.getEchoMap().get("completeEmp").toString() : "");
            tableInfoMap.setEmployeeEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("employeeId")) ? posCashResultVO.getEchoMap().get("employeeId").toString() : "");
            if (CollUtil.isNotEmpty(cashCommenterListResultVOMap.get(posCashResultVO.getId()))) {
                Map<Long, String> finalBaseEmployeeMap = baseEmployeeMap;
                tableInfoMap.setCommenter(cashCommenterListResultVOMap.get(posCashResultVO.getId())
                        .stream().filter(c -> CollUtil.isNotEmpty(finalBaseEmployeeMap) && ObjectUtil.isNotNull(finalBaseEmployeeMap.containsKey(c.getEmployeeId())))
                        .map(s -> finalBaseEmployeeMap.get(s.getEmployeeId())).collect(Collectors.joining(",")));
            } else {
                tableInfoMap.setCommenter("-");
            }
            tableInfoMap.setOrderSource(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("orderSource")) ? posCashResultVO.getEchoMap().get("orderSource").toString() : "");
            MemberInfoResultVO memberInfo = memberInfoMap.get(posCashResultVO.getMemberId());
            String memberName = "-";
            if (Objects.nonNull(memberInfo)) {
                memberName = memberInfo.getName() + "(" + StrUtil.subSuf(memberInfo.getMobile(), memberInfo.getMobile().length() - 4) + ")";
            }
            tableInfoMap.setMemberName(memberName);
            tableInfoMap.setCreateTime(DateUtils.format(posCashResultVO.getCreatedTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
            tableInfoMap.setCompleteTime(DateUtils.format(posCashResultVO.getCompleteTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
            tableInfoMap.setTableAmount(posCashResultVO.getTableAmount());
            tableInfoMap.setProductAmount(posCashResultVO.getProductAmount());
            tableInfoMap.setServiceAmount(posCashResultVO.getServiceAmount());
            tableInfoMap.setThailAmount(posCashResultVO.getThailAmount());
            tableInfoMap.setAmount(posCashResultVO.getAmount());
            tableInfoMap.setDiscountAmount(posCashResultVO.getDiscountAmount());
            tableInfoMap.setRefundAmount(posCashResultVO.getRefundAmount());
            tableInfoMap.setPaid(posCashResultVO.getAmount().subtract(posCashResultVO.getDiscountAmount()).subtract(posCashResultVO.getRefundAmount()));
            tableInfoMap.setOrderRemarks(posCashResultVO.getOrderRemarks());
            tableInfoMap.setRegistrationRemarks(posCashResultVO.getRegistrationRemarks());
            tableInfoMap.setPaymentDetails(paymentDetails.toString());
            tableInfoMap.setOrg(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("orgId")) ? posCashResultVO.getEchoMap().get("orgId").toString() : "");
            resultMapList.add(tableInfoMap);
        }
        pageList.setRecords(resultMapList);
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }

    @Override
    public List<PosCashDetailsResultVO> posCashDetailsList(PosCashDetailsQuery model) {
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(model);
            DataOverviewQuery storeTimeData = storeTime.getData();
            model.setStartDate(storeTimeData.getStartDate());
            model.setEndDate(storeTimeData.getEndDate());
        }
        if (StringUtils.isNotBlank(model.getCompleteTime_st()) && StringUtils.isBlank(model.getCompleteTime_ed())) {
            model.setCompleteTime_ed(DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
        }
        List<PosCashResultVO> posCashResultVOList = posCashMapper.findAllResultVO(initWarp(model));
        if (CollUtil.isEmpty(posCashResultVOList)) {
            return Collections.emptyList();
        }
        initRemarks(posCashResultVOList);
        List<Long> cashIds = posCashResultVOList.stream().map(PosCashResultVO::getId)
                .collect(Collectors.toList());
        List<PosCashCommenter> posCashCommenterList = new ArrayList<>();
        if (CollUtil.isNotEmpty(cashIds)) {
            posCashCommenterList = posCashMapper.cashCommenterListByCashIds(cashIds);
        }
        List<PosCashCommenterResultVO> cashCommenterListResultVO = BeanPlusUtil.toBeanList(posCashCommenterList, PosCashCommenterResultVO.class);
        Map<Long, String> baseEmployeeMap = MapUtil.newHashMap();
        if (CollUtil.isNotEmpty(cashCommenterListResultVO)) {
            R<List<BaseEmployeeResultVO>> employeeListR = employeeApi.findListByIds(cashCommenterListResultVO.stream().map(PosCashCommenterResultVO::getEmployeeId).collect(Collectors.toList()));
            Map<Long, String> collect = employeeListR.getData()
                    .stream().collect(Collectors.toMap(BaseEmployeeResultVO::getId, BaseEmployeeResultVO::getRealName));
            if (CollUtil.isNotEmpty(collect)) {
                baseEmployeeMap = collect;
            }
        }
        Map<Long, List<PosCashCommenterResultVO>> cashCommenterListResultVOMap = cashCommenterListResultVO.stream().collect(Collectors.groupingBy(PosCashCommenterResultVO::getCashId));
        // 所有支付方式
        List<PosCashPaymentResultVO> cashPaymentList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(cashIds)) {
            cashPaymentList = posCashPaymentMapper.queryListByCashIds(cashIds);
        }
        echoService.action(cashPaymentList);
        Map<Long, List<PosCashPaymentResultVO>> cashPaymentListMap = cashPaymentList.stream().collect(Collectors.groupingBy(PosCashPaymentResultVO::getCashId));
        // 所有台桌id
        List<Long> tableIdList = posCashResultVOList.stream().map(PosCashResultVO::getTableId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        R<List<BaseTableInfoResultVO>> tableAllR = tableApi.getTableAll(BaseTableInfoPageQuery.builder().build());
        List<BaseTableInfoResultVO> tableInfoResultVOS = tableAllR.getData();
        tableInfoResultVOS = tableInfoResultVOS.stream().filter(v -> tableIdList.contains(v.getId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(tableInfoResultVOS)) {
            tableInfoResultVOS = Lists.newArrayList();
        }
        Map<Long, BaseTableInfoResultVO> baseTableInfoResultVOMap = tableInfoResultVOS.stream().collect(Collectors.toMap(BaseTableInfoResultVO::getId, Function.identity()));
        // 会员信息
        Map<Long, MemberInfoResultVO> memberInfoMap = new HashMap<>();
        List<Long> memberIdList = posCashResultVOList.stream().map(PosCashResultVO::getMemberId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(memberIdList)) {
            List<MemberInfoResultVO> memberInfoList = memberInfoApi.queryByIds(memberIdList).getData();
            memberInfoMap = memberInfoList.stream().collect(Collectors.toMap(MemberInfoResultVO::getId, Function.identity()));
        }
        List<PosCashDetailsResultVO> resultMapList = new ArrayList<>();
        for (PosCashResultVO posCashResultVO : posCashResultVOList) {
            if (posCashResultVO.getRefundAmount() == null) {
                posCashResultVO.setRefundAmount(BigDecimal.ZERO);
            }
            if (posCashResultVO.getPaid() == null) {
                posCashResultVO.setPaid(BigDecimal.ZERO);
            }

            BaseTableInfoResultVO baseTableInfoResultVO = baseTableInfoResultVOMap.get(posCashResultVO.getTableId());

            List<PosCashPaymentResultVO> cashPaymentResultVOList = cashPaymentListMap.get(posCashResultVO.getId());
            StringBuilder paymentDetails = new StringBuilder();
            if (CollUtil.isNotEmpty(cashPaymentResultVOList)) {
                for (PosCashPaymentResultVO paymentResultVO : cashPaymentResultVOList) {
                    paymentDetails.append(paymentResultVO.getEchoMap().get("payTypeId").toString()).append(":").append(paymentResultVO.getAmount().subtract(Objects.nonNull(paymentResultVO.getRefundAmount()) ? paymentResultVO.getRefundAmount() : BigDecimal.ZERO).subtract(Objects.nonNull(paymentResultVO.getChangeAmount()) ? paymentResultVO.getChangeAmount() : BigDecimal.ZERO)).append("元;");
                }
            }

            PosCashDetailsResultVO tableInfoMap = new PosCashDetailsResultVO();
            tableInfoMap.setCode(posCashResultVO.getCode());
            tableInfoMap.setBillState(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("billState")) ? posCashResultVO.getEchoMap().get("billState").toString() : "");
            tableInfoMap.setBillType(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("billType")) ? posCashResultVO.getEchoMap().get("billType").toString() : "");
            tableInfoMap.setType(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("type")) ? posCashResultVO.getEchoMap().get("type").toString() : "");
            tableInfoMap.setTableName(posCashResultVO.getTableName());
            tableInfoMap.setTableType(Objects.nonNull(baseTableInfoResultVO) && Objects.nonNull(baseTableInfoResultVO.getTableType()) && Objects.nonNull(baseTableInfoResultVO.getEchoMap()) ? baseTableInfoResultVO.getEchoMap().get("tableType").toString() : "");
            tableInfoMap.setTableArea(Objects.nonNull(baseTableInfoResultVO) && Objects.nonNull(baseTableInfoResultVO.getTableArea()) && Objects.nonNull(baseTableInfoResultVO.getEchoMap()) ? baseTableInfoResultVO.getEchoMap().get("tableArea").toString() : "");
            tableInfoMap.setCreatedEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("createdEmp")) ? posCashResultVO.getEchoMap().get("createdEmp").toString() : "");
            tableInfoMap.setCompleteEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("completeEmp")) ? posCashResultVO.getEchoMap().get("completeEmp").toString() : "");
            tableInfoMap.setEmployeeEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("employeeId")) ? posCashResultVO.getEchoMap().get("employeeId").toString() : "");
            if (CollUtil.isNotEmpty(cashCommenterListResultVOMap.get(posCashResultVO.getId()))) {
                Map<Long, String> finalBaseEmployeeMap = baseEmployeeMap;
                tableInfoMap.setCommenter(cashCommenterListResultVOMap.get(posCashResultVO.getId())
                        .stream().filter(c -> CollUtil.isNotEmpty(finalBaseEmployeeMap) && ObjectUtil.isNotNull(finalBaseEmployeeMap.containsKey(c.getEmployeeId())))
                        .map(s -> finalBaseEmployeeMap.get(s.getEmployeeId())).collect(Collectors.joining(",")));
            } else {
                tableInfoMap.setCommenter("-");
            }
            tableInfoMap.setOrderSource(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("orderSource")) ? posCashResultVO.getEchoMap().get("orderSource").toString() : "");
            MemberInfoResultVO memberInfo = memberInfoMap.get(posCashResultVO.getMemberId());
            String memberName = "-";
            if (Objects.nonNull(memberInfo)) {
                memberName = memberInfo.getName() + "(" + StrUtil.subSuf(memberInfo.getMobile(), memberInfo.getMobile().length() - 4) + ")";
            }
            tableInfoMap.setMemberName(memberName);
            tableInfoMap.setCreateTime(DateUtils.format(posCashResultVO.getCreatedTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
            tableInfoMap.setCreateTime(DateUtils.format(posCashResultVO.getCompleteTime(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
            tableInfoMap.setTableAmount(posCashResultVO.getTableAmount());
            tableInfoMap.setProductAmount(posCashResultVO.getProductAmount());
            tableInfoMap.setServiceAmount(posCashResultVO.getServiceAmount());
            tableInfoMap.setThailAmount(posCashResultVO.getThailAmount());
            tableInfoMap.setAmount(posCashResultVO.getAmount());
            tableInfoMap.setDiscountAmount(posCashResultVO.getDiscountAmount());
            tableInfoMap.setRefundAmount(posCashResultVO.getRefundAmount());
            tableInfoMap.setPaid(posCashResultVO.getAmount().subtract(posCashResultVO.getDiscountAmount()).subtract(posCashResultVO.getRefundAmount()));
            tableInfoMap.setOrderRemarks(posCashResultVO.getOrderRemarks());
            tableInfoMap.setRegistrationRemarks(posCashResultVO.getRegistrationRemarks());
            tableInfoMap.setPaymentDetails(paymentDetails.toString());
            tableInfoMap.setOrg(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("orgId")) ? posCashResultVO.getEchoMap().get("orgId").toString() : "");
            resultMapList.add(tableInfoMap);
        }
        return resultMapList;
    }

    private void initRemarks(List<PosCashResultVO> posCashResultVOList) {
        for (PosCashResultVO posCash : posCashResultVOList) {
            //相关备注
            if (StrUtil.isNotBlank(posCash.getRemarks())) {
                OrderRemarksResultVO orderRemarksResultVO = JSON.parseObject(posCash.getRemarks(), OrderRemarksResultVO.class);
                String orderTags = orderRemarksResultVO.getOrderTags();
                posCash.setOrderRemarks("");
                if (StringUtils.isNotBlank(orderTags)) {
                    posCash.setOrderRemarks(orderTags);
                }
                String registrationTags = orderRemarksResultVO.getRegistrationTags();
                posCash.setRegistrationRemarks("");
                posCash.setRegistrationCustomRemarks(orderRemarksResultVO.getRegistrationRemarks());
                if (StringUtils.isNotBlank(registrationTags)) {
                    posCash.setRegistrationRemarks(registrationTags);
                }
                posCash.setRemarks("");
                if (StringUtils.isNotBlank(orderRemarksResultVO.getRemarks())) {
                    posCash.setRemarks(orderRemarksResultVO.getRemarks());
                }
            }
        }

        echoService.action(posCashResultVOList);
        for (PosCashResultVO posCash : posCashResultVOList) {
            String remarks = "";
            String registrationTags = "";
            if (CollUtil.isNotEmpty(posCash.getEchoMap()) &&
                    ObjectUtil.isNotNull(posCash.getEchoMap().get("orderRemarks"))) {
                remarks = remarks.concat(posCash.getEchoMap().get("orderRemarks").toString());
            }
            if (CollUtil.isNotEmpty(posCash.getEchoMap()) &&
                    ObjectUtil.isNotNull(posCash.getEchoMap().get("registrationRemarks"))) {
                registrationTags = registrationTags.concat(posCash.getEchoMap().get("registrationRemarks").toString());
            }
            if (StrUtil.isNotBlank(posCash.getRemarks())) {
                if (StrUtil.isNotBlank(remarks)) {
                    remarks = remarks.concat(" ");
                }
                remarks = remarks.concat(posCash.getRemarks());
            }
            if (StrUtil.isNotBlank(posCash.getRegistrationCustomRemarks())) {
                if (StrUtil.isNotBlank(registrationTags)) {
                    registrationTags = registrationTags.concat(" ");
                }
                registrationTags = registrationTags.concat(posCash.getRegistrationCustomRemarks());
            }
            posCash.setOrderRemarks(remarks);
            posCash.setRemarks(remarks);
            posCash.setRegistrationRemarks(registrationTags);
        }
    }

    @Override
    public PosCashDetailsResultVO posCashDetailsSum(PosCashDetailsQuery params) {
        initOrgIdList(params);

        if (StringUtils.isNotBlank(params.getStartDate()) && StringUtils.isNotBlank(params.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(params);
            DataOverviewQuery storeTimeData = storeTime.getData();
            params.setStartDate(storeTimeData.getStartDate());
            params.setEndDate(storeTimeData.getEndDate());
        }
        if (StringUtils.isNotBlank(params.getCompleteTime_st()) && StringUtils.isBlank(params.getCompleteTime_ed())) {
            params.setCompleteTime_ed(DateUtils.format(LocalDateTime.now(), DateUtils.DEFAULT_DATE_TIME_FORMAT));
        }
        List<PosCashResultVO> posCashResultVOS = posCashMapper.findAllResultVO(initWarp(params));
        echoService.action(posCashResultVOS);
        posCashResultVOS = posCashResultVOS.stream().filter(s -> !StringUtils.equals(s.getBillState(), PosCashBillStateEnum.REFUNDED.getCode())).collect(Collectors.toList());

        List<Long> cashIds = posCashResultVOS.stream().map(PosCashResultVO::getId).distinct()
                .collect(Collectors.toList());
        // 所有支付方式
        List<PosCashPaymentResultVO> cashPaymentList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(cashIds)) {
            cashPaymentList = posCashPaymentMapper.queryListByCashIds(cashIds);
        }
        echoService.action(cashPaymentList);
        StringBuilder paymentDetails = new StringBuilder();
        Map<Long, List<PosCashPaymentResultVO>> cashPaymentListMap = cashPaymentList.stream().collect(Collectors.groupingBy(PosCashPaymentResultVO::getPayTypeId));
        cashPaymentListMap.forEach((k, v) -> {
            paymentDetails.append(v.get(0).getEchoMap().get("payTypeId").toString()).append(":").append(v.stream().map(PosCashPaymentResultVO::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add)
                    .subtract(v.stream().map(PosCashPaymentResultVO::getRefundAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add))
                    .subtract(v.stream().map(PosCashPaymentResultVO::getChangeAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add))).append("元;");
        });

        PosCashDetailsResultVO resultMap = new PosCashDetailsResultVO();
        resultMap.setTableAmount(posCashResultVOS.stream().map(PosCashResultVO::getTableAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultMap.setProductAmount(posCashResultVOS.stream().map(PosCashResultVO::getProductAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultMap.setServiceAmount(posCashResultVOS.stream().map(PosCashResultVO::getServiceAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultMap.setThailAmount(posCashResultVOS.stream().map(PosCashResultVO::getThailAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultMap.setAmount(posCashResultVOS.stream().map(PosCashResultVO::getAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultMap.setDiscountAmount(posCashResultVOS.stream().map(PosCashResultVO::getDiscountAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultMap.setRefundAmount(posCashResultVOS.stream().map(PosCashResultVO::getRefundAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        resultMap.setPaid(resultMap.getAmount().subtract(resultMap.getRefundAmount()).subtract(resultMap.getDiscountAmount()));
        resultMap.setPaymentDetails(paymentDetails.toString());
        return resultMap;
    }

    @Override
    public Map<String, Object> noPayPage(PageParams<DataOverviewQuery> params) {
        // 需要展示商品 详情, 服务时长, 台桌时长,
        initOrgIdList(params.getModel());

        // 设置表头
        List<ColumnVO> columnVOList = com.google.common.collect.Lists.newArrayList(
                ColumnVO.builder().name("code").label("流水号")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("type").label("业务类型")
                        .width(180).emptyString("-").fixed(true).build(),
                ColumnVO.builder().name("tableName").label("台桌名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableType").label("台桌类型")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableArea").label("台桌区域")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createdEmp").label("操作员工")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("employeeEmp").label("销售员工")// employeeId
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orderSource").label("订单来源")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("memberName").label("会员名称")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("createTime").label("开台时间")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableAmount").label("台桌金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("productAmount").label("商品金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceAmount").label("服务金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("thailAmount").label("套餐金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("amount").label("订单原价")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("discountAmount").label("订单优惠金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("refundAmount").label("订单部分退款金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("paid").label("订单实收金额")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("orderRemarks").label("整单备注")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("registrationRemarks").label("挂单备注")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("tableDetails").label("台桌详情")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("productDetails").label("商品详情")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("serviceDetails").label("服务详情")
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("thailDetails").label("套餐详情")  // 只展示套餐名称
                        .width(180).emptyString("-").fixed(false).build(),
                ColumnVO.builder().name("org").label("门店")
                        .width(180).emptyString("-").fixed(false).build()
        );
        DataOverviewQuery model = params.getModel();
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(model);
            DataOverviewQuery storeTimeData = storeTime.getData();
            model.setStartDate(storeTimeData.getStartDate());
            model.setEndDate(storeTimeData.getEndDate());
        }
        IPage<PosCashResultVO> pageResultVO = posCashMapper.selectPageResultVO(params.buildPage(PosCash.class), noPay(model));
        if (CollUtil.isEmpty(pageResultVO.getRecords())) {
            Map<String, Object> objectMap = BeanUtil.beanToMap(pageResultVO);
            objectMap.put("columnList", columnVOList);
            return objectMap;
        }
        initRemarks(pageResultVO.getRecords());
        IPage<PosCashNoPayDetailsResultVO> pageList = BeanPlusUtil.toBeanPage(pageResultVO, PosCashNoPayDetailsResultVO.class);
        List<Long> cashIds = pageResultVO.getRecords().stream().map(PosCashResultVO::getId)
                .collect(Collectors.toList());

        // 台桌详情信息
        List<PosCashTable> cashTableList = posCashMapper.cashTableListByCashIds(cashIds);
        Map<Long, List<PosCashTable>> cashTableMap = cashTableList.stream().collect(Collectors.groupingBy(PosCashTable::getCashId));

        // 商品详情信息
        List<PosCashProduct> cashProductList = posCashMapper.cashProductListByCashIds(cashIds);
        Map<Long, List<PosCashProduct>> cashProductMap = cashProductList.stream().collect(Collectors.groupingBy(PosCashProduct::getCashId));

        // 服务详情信息
        List<top.kx.kxss.app.entity.cash.service.PosCashService> cashServiceList = posCashMapper.cashServiceListByCashIds(cashIds);
        Map<Long, List<top.kx.kxss.app.entity.cash.service.PosCashService>> cashServiceMap = cashServiceList.stream().collect(Collectors.groupingBy(top.kx.kxss.app.entity.cash.service.PosCashService::getCashId));

        // 套餐详情信息
        List<PosCashThail> cashThailList = posCashMapper.cashThailListByCashIds(cashIds);
        Map<Long, List<PosCashThail>> cashThailMap = cashThailList.stream().collect(Collectors.groupingBy(PosCashThail::getCashId));

        // 所有台桌id
        List<Long> tableIdList = pageResultVO.getRecords().stream().map(PosCashResultVO::getTableId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        R<List<BaseTableInfoResultVO>> tableAllR = tableApi.getTableAll(BaseTableInfoPageQuery.builder().build());
        List<BaseTableInfoResultVO> tableInfoResultVOS = tableAllR.getData();
        tableInfoResultVOS = tableInfoResultVOS.stream().filter(v -> tableIdList.contains(v.getId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(tableInfoResultVOS)) {
            tableInfoResultVOS = Lists.newArrayList();
        }
        Map<Long, BaseTableInfoResultVO> baseTableInfoResultVOMap = tableInfoResultVOS.stream().collect(Collectors.toMap(BaseTableInfoResultVO::getId, Function.identity()));
        // 会员信息
        Map<Long, MemberInfoResultVO> memberInfoMap = new HashMap<>();
        List<Long> memberIdList = pageResultVO.getRecords().stream().map(PosCashResultVO::getMemberId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(memberIdList)) {
            List<MemberInfoResultVO> memberInfoList = memberInfoApi.queryByIds(memberIdList).getData();
            memberInfoMap = memberInfoList.stream().collect(Collectors.toMap(MemberInfoResultVO::getId, Function.identity()));
        }
        List<PosCashNoPayDetailsResultVO> resultMapList = new ArrayList<>();
        for (PosCashResultVO posCashResultVO : pageResultVO.getRecords()) {
            if (posCashResultVO.getRefundAmount() == null) {
                posCashResultVO.setRefundAmount(BigDecimal.ZERO);
            }
            if (posCashResultVO.getPaid() == null) {
                posCashResultVO.setPaid(BigDecimal.ZERO);
            }

            BaseTableInfoResultVO baseTableInfoResultVO = baseTableInfoResultVOMap.get(posCashResultVO.getTableId());

            PosCashNoPayDetailsResultVO detailsResultVO = new PosCashNoPayDetailsResultVO();
            detailsResultVO.setCode(posCashResultVO.getCode());
            detailsResultVO.setType(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("type")) ? posCashResultVO.getEchoMap().get("type").toString() : "");
            detailsResultVO.setTableName(posCashResultVO.getTableName());
            detailsResultVO.setTableType(Objects.nonNull(baseTableInfoResultVO) && Objects.nonNull(baseTableInfoResultVO.getTableType()) && Objects.nonNull(baseTableInfoResultVO.getEchoMap()) ? baseTableInfoResultVO.getEchoMap().get("tableType").toString() : "");
            detailsResultVO.setTableArea(Objects.nonNull(baseTableInfoResultVO) && Objects.nonNull(baseTableInfoResultVO.getTableArea()) && Objects.nonNull(baseTableInfoResultVO.getEchoMap()) ? baseTableInfoResultVO.getEchoMap().get("tableArea").toString() : "");
            detailsResultVO.setCreatedEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("createdEmp")) ? posCashResultVO.getEchoMap().get("createdEmp").toString() : "");
            detailsResultVO.setEmployeeEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("employeeId")) ? posCashResultVO.getEchoMap().get("employeeId").toString() : "");
            detailsResultVO.setOrderSource(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("orderSource")) ? posCashResultVO.getEchoMap().get("orderSource").toString() : "");
            MemberInfoResultVO memberInfo = memberInfoMap.get(posCashResultVO.getMemberId());
            String memberName = "-";
            if (Objects.nonNull(memberInfo)) {
                memberName = memberInfo.getName() + "(" + StrUtil.subSuf(memberInfo.getMobile(), memberInfo.getMobile().length() - 4) + ")";
            }
            detailsResultVO.setMemberName(memberName);
            detailsResultVO.setCreateTime(posCashResultVO.getCreatedTime());
            detailsResultVO.setTableAmount(posCashResultVO.getTableAmount());
            detailsResultVO.setProductAmount(posCashResultVO.getProductAmount());
            detailsResultVO.setServiceAmount(posCashResultVO.getServiceAmount());
            detailsResultVO.setThailAmount(posCashResultVO.getThailAmount());
            detailsResultVO.setAmount(posCashResultVO.getAmount());
            detailsResultVO.setDiscountAmount(posCashResultVO.getDiscountAmount());
            detailsResultVO.setRefundAmount(posCashResultVO.getRefundAmount());
            detailsResultVO.setPaid(posCashResultVO.getAmount().subtract(posCashResultVO.getDiscountAmount()).subtract(posCashResultVO.getRefundAmount()));
            detailsResultVO.setOrderRemarks(posCashResultVO.getOrderRemarks());
            detailsResultVO.setRegistrationRemarks(posCashResultVO.getRegistrationRemarks());
            detailsResultVO.setOrg(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("orgId")) ? posCashResultVO.getEchoMap().get("orgId").toString() : "");
            List<PosCashTable> posCashTableList = cashTableMap.get(posCashResultVO.getId());
            if (CollUtil.isNotEmpty(posCashTableList)) {
                detailsResultVO.setTableDetails(posCashTableList.stream().map(s -> s.getTableName() + "-" + s.getDuration() + "分钟").collect(Collectors.joining(";")));
            }
            List<PosCashProduct> posCashProductList = cashProductMap.get(posCashResultVO.getId());
            if (CollUtil.isNotEmpty(posCashProductList)) {
                detailsResultVO.setProductDetails(posCashProductList.stream().map(s -> s.getProductName() + "*" + ((Objects.nonNull(s.getNum()) ? s.getNum() : 0) - (Objects.nonNull(s.getRefundNum()) ? s.getRefundNum() : 0))).collect(Collectors.joining(";")));
            }
            List<top.kx.kxss.app.entity.cash.service.PosCashService> posCashServiceList = cashServiceMap.get(posCashResultVO.getId());
            if (CollUtil.isNotEmpty(posCashServiceList)) {
                detailsResultVO.setServiceDetails(posCashServiceList.stream().map(s -> s.getEmployeeName() + "-" + s.getDuration() + "分钟").collect(Collectors.joining(";")));
            }
            List<PosCashThail> posCashThailList = cashThailMap.get(posCashResultVO.getId());
            if (CollUtil.isNotEmpty(posCashThailList)) {
                detailsResultVO.setThailDetails(posCashThailList.stream().map(PosCashThail::getThailName).collect(Collectors.joining(";")));
            }
            resultMapList.add(detailsResultVO);
        }
        pageList.setRecords(resultMapList);
        Map<String, Object> objectMap = BeanUtil.beanToMap(pageList);
        objectMap.put("columnList", columnVOList);
        return objectMap;
    }


    @Override
    public PosCashNoPayDetailsResultVO noPaySum(DataOverviewQuery model) {
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(model);
            DataOverviewQuery storeTimeData = storeTime.getData();
            model.setStartDate(storeTimeData.getStartDate());
            model.setEndDate(storeTimeData.getEndDate());
        }
        return posCashMapper.findSumResultVO(noPay(model));
    }

    @Override
    public List<PosCashNoPayDetailsResultVO> noPayList(DataOverviewQuery model) {
        initOrgIdList(model);
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(model);
            DataOverviewQuery storeTimeData = storeTime.getData();
            model.setStartDate(storeTimeData.getStartDate());
            model.setEndDate(storeTimeData.getEndDate());
        }
        List<PosCashResultVO> posCashResultVOList = posCashMapper.findAllResultVO(noPay(model));
        if (CollUtil.isEmpty(posCashResultVOList)) {
            return Collections.emptyList();
        }
        initRemarks(posCashResultVOList);
        List<Long> cashIds = posCashResultVOList.stream().map(PosCashResultVO::getId)
                .collect(Collectors.toList());

        // 台桌详情信息
        List<PosCashTable> cashTableList = posCashMapper.cashTableListByCashIds(cashIds);
        Map<Long, List<PosCashTable>> cashTableMap = cashTableList.stream().collect(Collectors.groupingBy(PosCashTable::getCashId));

        // 商品详情信息
        List<PosCashProduct> cashProductList = posCashMapper.cashProductListByCashIds(cashIds);
        Map<Long, List<PosCashProduct>> cashProductMap = cashProductList.stream().collect(Collectors.groupingBy(PosCashProduct::getCashId));

        // 服务详情信息
        List<top.kx.kxss.app.entity.cash.service.PosCashService> cashServiceList = posCashMapper.cashServiceListByCashIds(cashIds);
        Map<Long, List<top.kx.kxss.app.entity.cash.service.PosCashService>> cashServiceMap = cashServiceList.stream().collect(Collectors.groupingBy(top.kx.kxss.app.entity.cash.service.PosCashService::getCashId));

        // 套餐详情信息
        List<PosCashThail> cashThailList = posCashMapper.cashThailListByCashIds(cashIds);
        Map<Long, List<PosCashThail>> cashThailMap = cashThailList.stream().collect(Collectors.groupingBy(PosCashThail::getCashId));

        // 所有台桌id
        List<Long> tableIdList = posCashResultVOList.stream().map(PosCashResultVO::getTableId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        R<List<BaseTableInfoResultVO>> tableAllR = tableApi.getTableAll(BaseTableInfoPageQuery.builder().build());
        List<BaseTableInfoResultVO> tableInfoResultVOS = tableAllR.getData();
        tableInfoResultVOS = tableInfoResultVOS.stream().filter(v -> tableIdList.contains(v.getId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(tableInfoResultVOS)) {
            tableInfoResultVOS = Lists.newArrayList();
        }
        Map<Long, BaseTableInfoResultVO> baseTableInfoResultVOMap = tableInfoResultVOS.stream().collect(Collectors.toMap(BaseTableInfoResultVO::getId, Function.identity()));
        // 会员信息
        Map<Long, MemberInfoResultVO> memberInfoMap = new HashMap<>();
        List<Long> memberIdList = posCashResultVOList.stream().map(PosCashResultVO::getMemberId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(memberIdList)) {
            List<MemberInfoResultVO> memberInfoList = memberInfoApi.queryByIds(memberIdList).getData();
            memberInfoMap = memberInfoList.stream().collect(Collectors.toMap(MemberInfoResultVO::getId, Function.identity()));
        }
        List<PosCashNoPayDetailsResultVO> resultMapList = new ArrayList<>();
        for (PosCashResultVO posCashResultVO : posCashResultVOList) {
            if (posCashResultVO.getRefundAmount() == null) {
                posCashResultVO.setRefundAmount(BigDecimal.ZERO);
            }
            if (posCashResultVO.getPaid() == null) {
                posCashResultVO.setPaid(BigDecimal.ZERO);
            }

            BaseTableInfoResultVO baseTableInfoResultVO = baseTableInfoResultVOMap.get(posCashResultVO.getTableId());

            PosCashNoPayDetailsResultVO detailsResultVO = new PosCashNoPayDetailsResultVO();
            detailsResultVO.setCode(posCashResultVO.getCode());
            detailsResultVO.setType(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("type")) ? posCashResultVO.getEchoMap().get("type").toString() : "");
            detailsResultVO.setTableName(posCashResultVO.getTableName());
            detailsResultVO.setTableType(Objects.nonNull(baseTableInfoResultVO) && Objects.nonNull(baseTableInfoResultVO.getTableType()) && Objects.nonNull(baseTableInfoResultVO.getEchoMap()) ? baseTableInfoResultVO.getEchoMap().get("tableType").toString() : "");
            detailsResultVO.setTableArea(Objects.nonNull(baseTableInfoResultVO) && Objects.nonNull(baseTableInfoResultVO.getTableArea()) && Objects.nonNull(baseTableInfoResultVO.getEchoMap()) ? baseTableInfoResultVO.getEchoMap().get("tableArea").toString() : "");
            detailsResultVO.setCreatedEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("createdEmp")) ? posCashResultVO.getEchoMap().get("createdEmp").toString() : "");
            detailsResultVO.setEmployeeEmp(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("employeeId")) ? posCashResultVO.getEchoMap().get("employeeId").toString() : "");
            detailsResultVO.setOrderSource(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("orderSource")) ? posCashResultVO.getEchoMap().get("orderSource").toString() : "");
            MemberInfoResultVO memberInfo = memberInfoMap.get(posCashResultVO.getMemberId());
            String memberName = "-";
            if (Objects.nonNull(memberInfo)) {
                memberName = memberInfo.getName() + "(" + StrUtil.subSuf(memberInfo.getMobile(), memberInfo.getMobile().length() - 4) + ")";
            }
            detailsResultVO.setMemberName(memberName);
            detailsResultVO.setCreateTime(posCashResultVO.getCreatedTime());
            detailsResultVO.setTableAmount(posCashResultVO.getTableAmount());
            detailsResultVO.setProductAmount(posCashResultVO.getProductAmount());
            detailsResultVO.setServiceAmount(posCashResultVO.getServiceAmount());
            detailsResultVO.setThailAmount(posCashResultVO.getThailAmount());
            detailsResultVO.setAmount(posCashResultVO.getAmount());
            detailsResultVO.setDiscountAmount(posCashResultVO.getDiscountAmount());
            detailsResultVO.setRefundAmount(posCashResultVO.getRefundAmount());
            detailsResultVO.setPaid(posCashResultVO.getAmount().subtract(posCashResultVO.getDiscountAmount()).subtract(posCashResultVO.getRefundAmount()));
            detailsResultVO.setOrderRemarks(posCashResultVO.getOrderRemarks());
            detailsResultVO.setRegistrationRemarks(posCashResultVO.getRegistrationRemarks());
            detailsResultVO.setOrg(CollUtil.isNotEmpty(posCashResultVO.getEchoMap()) && Objects.nonNull(posCashResultVO.getEchoMap().get("orgId")) ? posCashResultVO.getEchoMap().get("orgId").toString() : "");
            List<PosCashTable> posCashTableList = cashTableMap.get(posCashResultVO.getId());
            if (CollUtil.isNotEmpty(posCashTableList)) {
                detailsResultVO.setTableDetails(posCashTableList.stream().map(s -> s.getTableName() + "-" + s.getDuration() + "分钟").collect(Collectors.joining(";")));
            }
            List<PosCashProduct> posCashProductList = cashProductMap.get(posCashResultVO.getId());
            if (CollUtil.isNotEmpty(posCashProductList)) {
                detailsResultVO.setProductDetails(posCashProductList.stream().map(s -> s.getProductName() + "*" + ((Objects.nonNull(s.getNum()) ? s.getNum() : 0) - (Objects.nonNull(s.getRefundNum()) ? s.getRefundNum() : 0))).collect(Collectors.joining(";")));
            }
            List<top.kx.kxss.app.entity.cash.service.PosCashService> posCashServiceList = cashServiceMap.get(posCashResultVO.getId());
            if (CollUtil.isNotEmpty(posCashServiceList)) {
                detailsResultVO.setServiceDetails(posCashServiceList.stream().map(s -> s.getEmployeeName() + "-" + s.getDuration() + "分钟").collect(Collectors.joining(";")));
            }
            List<PosCashThail> posCashThailList = cashThailMap.get(posCashResultVO.getId());
            if (CollUtil.isNotEmpty(posCashThailList)) {
                detailsResultVO.setThailDetails(posCashThailList.stream().map(PosCashThail::getThailName).collect(Collectors.joining(";")));
            }
            resultMapList.add(detailsResultVO);
        }
        return resultMapList;
    }

    @Override
    public IPage<OrderResultVO> thailPage(PageParams<ThailOverviewQuery> query) {
        ThailOverviewQuery model = query.getModel();
        if (StringUtils.isNotBlank(model.getStartDate()) && StringUtils.isNotBlank(model.getEndDate())) {
            R<DataOverviewQuery> storeTime = customApi.getStoreTime(model);
            DataOverviewQuery storeTimeData = storeTime.getData();
            model.setStartDate(storeTimeData.getStartDate());
            model.setEndDate(storeTimeData.getEndDate());
        }
        IPage<OrderResultVO> posCashIPage = posCashMapper.thailPage(query.buildPage(PosCash.class), thailWarp(model));
        echoService.action(posCashIPage);
        return posCashIPage;
    }

    public void initOrgIdList(OrgIdListQuery params) {
        if (Objects.isNull(params)) {
            params = new OrgIdListQuery();
        }
        if (CollUtil.isEmpty(params.getOrgIdList())) {
            params.setOrgIdList(Collections.singletonList(ContextUtil.getCurrentCompanyId()));
        }
    }

}

