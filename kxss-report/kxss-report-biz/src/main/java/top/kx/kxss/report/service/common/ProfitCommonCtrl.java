package top.kx.kxss.report.service.common;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillTypeEnum;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

import java.util.Arrays;

/**
 * 商品销售API
 *
 * <AUTHOR>
 */
public class ProfitCommonCtrl {


    /**
     * 基础查询条件
     * @param query
     * @return
     */
    public QueryWrapper baseWrapper(DataOverviewQuery query) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(),
                PosCashBillStateEnum.PART_REFUND.getCode()));
        queryWrapper.notIn("p.bill_type", Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                , PosCashBillTypeEnum.CHARGEBACK.getCode()));
        queryWrapper.eq("p.delete_flag", 0);
        queryWrapper.isNotNull("p.complete_time");
        queryWrapper.eq("p.org_id", ContextUtil.getCurrentCompanyId());
        queryWrapper.between("p.complete_time", query.getStartDate(), query.getEndDate());
        return queryWrapper;
    }

    /**
     * 查询商品订单信息
     * @param query
     * @return
     */
    public QueryWrapper cashProductWrapper(DataOverviewQuery query) {
        QueryWrapper queryWrapper = baseWrapper(query);
        queryWrapper.eq("t.delete_flag", 0);
        queryWrapper.ne("p.type_", "3");
        return queryWrapper;
    }

    /**
     * payment 查询
     * @param query
     * @return
     */
    public QueryWrapper cashPaymentBaseWrapper(DataOverviewQuery query) {
        QueryWrapper queryWrapper = baseWrapper(query);
        queryWrapper.eq("t.delete_flag", 0);
        return queryWrapper;
    }


}

