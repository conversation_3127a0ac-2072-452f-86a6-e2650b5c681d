package top.kx.kxss.report.service.common;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillTypeEnum;
import top.kx.kxss.model.enumeration.report.SalesTypeEnum;
import top.kx.kxss.report.vo.SalesDetailResultVO;
import top.kx.kxss.report.vo.SalesResultVO;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

/**
 * 商品销售API
 *
 * <AUTHOR>
 */
public class CommonCtrl {


    public QueryWrapper queryWrapper(DataOverviewQuery query) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("pro.delete_flag", 0);
        queryWrapper.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(),
                PosCashBillStateEnum.PART_REFUND.getCode()));
        queryWrapper.notIn("p.bill_type", Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode()
                , PosCashBillTypeEnum.CHARGEBACK.getCode()));
        queryWrapper.eq("p.delete_flag", 0);
        queryWrapper.isNotNull("p.complete_time");
        queryWrapper.eq("p.org_id", ContextUtil.getCurrentCompanyId());
        queryWrapper.between("p.complete_time", query.getStartDate(), query.getEndDate());
        return queryWrapper;
    }

    public SalesResultVO dataResult(List<SalesDetailResultVO> voList) {
        SalesResultVO build = SalesResultVO.builder()
                .salesList(Lists.newArrayList()).totalDiscountAmount(BigDecimal.ZERO)
                .totalPayment(BigDecimal.ZERO).totalAmount(BigDecimal.ZERO)
                .totalSalesNum(0)
                .build();
        if (CollUtil.isEmpty(voList)) {
            return build;
        }
        BigDecimal totalAmount = voList.stream().map(SalesDetailResultVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        BigDecimal totalPayment = voList.stream().map(SalesDetailResultVO::getPayment).reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        BigDecimal totalDiscountAmount = voList.stream().map(SalesDetailResultVO::getDiscountAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                .setScale(2, RoundingMode.HALF_UP);
        int totalSalesNum = voList.stream().mapToInt(SalesDetailResultVO::getSalesNum).sum();
        build.setTotalAmount(totalAmount);
        build.setTotalPayment(totalPayment);
        build.setTotalDiscountAmount(totalDiscountAmount);
        build.setTotalSalesNum(totalSalesNum);
        voList.sort(Comparator.comparing(SalesDetailResultVO::getPayment).reversed());
        for (SalesDetailResultVO sales : voList) {
            BigDecimal totalSalesNumProp = voList.stream()
                    .map(SalesDetailResultVO::getSalesNumProp).filter(ObjectUtil::isNotNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalAmountProp = voList.stream()
                    .map(SalesDetailResultVO::getAmountProp).filter(ObjectUtil::isNotNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalPaymentProp = voList.stream()
                    .map(SalesDetailResultVO::getPaymentProp).filter(ObjectUtil::isNotNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal totalDiscountAmountProp = voList.stream()
                    .map(SalesDetailResultVO::getDiscountAmountProp).filter(ObjectUtil::isNotNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal bigDecimal = new BigDecimal(100);
            //最后一个用减法
            if (sales.equals(voList.get(voList.size() - 1))) {
                sales.setAmount(sales.getAmount().setScale(2, RoundingMode.HALF_UP));
                sales.setPayment(sales.getPayment().setScale(2, RoundingMode.HALF_UP));
                sales.setDiscountAmount(sales.getDiscountAmount().setScale(2, RoundingMode.HALF_UP));
                sales.setAmountProp(totalAmountProp.compareTo(BigDecimal.ZERO) <= 0 ? (sales.getAmount().compareTo(BigDecimal.ZERO) > 0 ? bigDecimal : BigDecimal.ZERO) : bigDecimal.subtract(totalAmountProp).setScale(2, RoundingMode.HALF_UP));
                sales.setPaymentProp(totalPaymentProp.compareTo(BigDecimal.ZERO) <= 0 ? (sales.getPayment().compareTo(BigDecimal.ZERO) > 0 ? bigDecimal : BigDecimal.ZERO) : bigDecimal.subtract(totalPaymentProp).setScale(2, RoundingMode.HALF_UP));
                sales.setDiscountAmountProp(totalDiscountAmountProp.compareTo(BigDecimal.ZERO) <= 0 ? (sales.getDiscountAmount().compareTo(BigDecimal.ZERO) > 0 ? bigDecimal : BigDecimal.ZERO) : bigDecimal.subtract(totalDiscountAmountProp).setScale(2, RoundingMode.HALF_UP));
                sales.setSalesNumProp(totalSalesNumProp.compareTo(BigDecimal.ZERO) <= 0 ? (sales.getSalesNum() > 0 ? bigDecimal : BigDecimal.ZERO) : bigDecimal.subtract(totalSalesNumProp).setScale(2, RoundingMode.HALF_UP));
                break;
            }
            sales.setAmount(sales.getAmount().setScale(2, RoundingMode.HALF_UP));
            sales.setPayment(sales.getPayment().setScale(2, RoundingMode.HALF_UP));
            sales.setDiscountAmount(sales.getDiscountAmount().setScale(2, RoundingMode.HALF_UP));

            sales.setAmountProp(calcProp(totalAmountProp, totalAmount.compareTo(BigDecimal.ZERO) <= 0 ? (voList.size() == 1 ? bigDecimal : BigDecimal.ZERO) : sales.getAmount().divide(totalAmount, 10, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)))
                    .setScale(2, RoundingMode.HALF_UP));
            sales.setPaymentProp(calcProp(totalPaymentProp, totalPayment.compareTo(BigDecimal.ZERO) <= 0 ? (voList.size() == 1 ? bigDecimal : BigDecimal.ZERO) : sales.getPayment().divide(totalPayment, 10, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)))
                    .setScale(2, RoundingMode.HALF_UP));
            sales.setDiscountAmountProp(calcProp(totalDiscountAmountProp, totalDiscountAmount.compareTo(BigDecimal.ZERO) <= 0 ? (voList.size() == 1 ? bigDecimal : BigDecimal.ZERO) : sales.getDiscountAmount().divide(totalDiscountAmount, 10, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)))
                    .setScale(2, RoundingMode.HALF_UP));
            sales.setSalesNumProp(calcProp(totalSalesNumProp, totalSalesNum <= 0 ? (voList.size() == 1 ? bigDecimal : BigDecimal.ZERO) : new BigDecimal(sales.getSalesNum()).divide(new BigDecimal(totalSalesNum), 10, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)))
                    .setScale(2, RoundingMode.HALF_UP));
        }
        build.setSalesList(voList);
        return build;
    }

    private BigDecimal calcProp(BigDecimal totalProp, BigDecimal bigDecimal) {
        if (totalProp.add(bigDecimal).compareTo(BigDecimal.valueOf(100)) >= 0) {
            return BigDecimal.valueOf(100).subtract(totalProp).setScale(2, RoundingMode.HALF_UP);
        }
        return bigDecimal;
    }

    public void itemGroupBuy(String field, QueryWrapper queryWrapper, String salesType) {
        salesType = StrUtil.isNotBlank(salesType)
                ? salesType : SalesTypeEnum.ITEM_THAIL_DETAIL.getCode();
        ArgumentAssert.notBlank(salesType, "销售方式不存在！");
        switch (SalesTypeEnum.get(salesType)) {
            case ITEM:
            case ITEM_THAIL:
                queryWrapper.isNull("pro.cash_thail_id");
                queryWrapper.groupBy(field);
                break;
            case THAIL:
                queryWrapper.eq("pro.cash_thail_id", "-1");
                queryWrapper.groupBy(field);
                break;
            case THAIL_DETAIL:
                queryWrapper.isNotNull("pro.cash_thail_id");
                queryWrapper.groupBy(field);
                break;
            case ITEM_THAIL_DETAIL:
                queryWrapper.groupBy("concat(" + field.concat(",'_',").concat("IF(pro.cash_thail_id is null,0,1)").concat(")"));
                break;
        }
    }
}

