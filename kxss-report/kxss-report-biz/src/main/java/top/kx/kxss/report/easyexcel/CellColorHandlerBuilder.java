package top.kx.kxss.report.easyexcel;

import org.apache.poi.ss.usermodel.IndexedColors;

import java.util.function.Predicate;

/**
 * CellColorHandler 构建器
 * 提供更便捷的API来配置颜色规则
 *
 * <AUTHOR>
 * @date 2024/12/28
 */
public class CellColorHandlerBuilder {

    private final CellColorHandler handler;

    public CellColorHandlerBuilder() {
        this.handler = new CellColorHandler();
    }

    public CellColorHandlerBuilder(CellColorHandler.StyleConfig styleConfig) {
        this.handler = new CellColorHandler(styleConfig);
    }

    /**
     * 常用颜色枚举
     */
    public enum Colors {
        RED(IndexedColors.RED.getIndex()),
        GREEN(IndexedColors.GREEN.getIndex()),
        BLUE(IndexedColors.BLUE.getIndex()),
        YELLOW(IndexedColors.YELLOW.getIndex()),
        ORANGE(IndexedColors.ORANGE.getIndex()),
        PINK(IndexedColors.PINK.getIndex()),
        LIGHT_GREEN(IndexedColors.LIGHT_GREEN.getIndex()),
        LIGHT_BLUE(IndexedColors.LIGHT_BLUE.getIndex()),
        LIGHT_YELLOW(IndexedColors.LIGHT_YELLOW.getIndex()),
        GREY_25_PERCENT(IndexedColors.GREY_25_PERCENT.getIndex()),
        GREY_40_PERCENT(IndexedColors.GREY_40_PERCENT.getIndex()),
        GREY_50_PERCENT(IndexedColors.GREY_50_PERCENT.getIndex()),
        TURQUOISE(IndexedColors.TURQUOISE.getIndex()),
        VIOLET(IndexedColors.VIOLET.getIndex()),
        LAVENDER(IndexedColors.LAVENDER.getIndex()),
        CORAL(IndexedColors.CORAL.getIndex()),
        ROSE(IndexedColors.ROSE.getIndex()),
        TAN(IndexedColors.TAN.getIndex()),
        PLUM(IndexedColors.PLUM.getIndex()),
        ROYAL_BLUE(IndexedColors.ROYAL_BLUE.getIndex());

        private final Short index;

        Colors(Short index) {
            this.index = index;
        }

        public Short getIndex() {
            return index;
        }
    }

    // ========== 单元格颜色规则 ==========

    /**
     * 添加字符串相等的单元格颜色规则
     */
    public CellColorHandlerBuilder cellStringEquals(int columnIndex, String value, Colors color) {
        handler.addStringEqualsCellRule(columnIndex, value, color.getIndex(),
            String.format("列%d字符串等于'%s'", columnIndex, value));
        return this;
    }

    /**
     * 添加字符串包含的单元格颜色规则
     */
    public CellColorHandlerBuilder cellStringContains(int columnIndex, String value, Colors color) {
        handler.addStringContainsCellRule(columnIndex, value, color.getIndex(),
            String.format("列%d字符串包含'%s'", columnIndex, value));
        return this;
    }

    /**
     * 添加数值大于的单元格颜色规则
     */
    public CellColorHandlerBuilder cellNumberGreaterThan(int columnIndex, Double threshold, Colors color) {
        handler.addNumberCompareCellRule(columnIndex, threshold, CellColorHandler.CompareType.GREATER_THAN,
            color.getIndex(), String.format("列%d数值大于%.2f", columnIndex, threshold));
        return this;
    }

    /**
     * 添加数值小于的单元格颜色规则
     */
    public CellColorHandlerBuilder cellNumberLessThan(int columnIndex, Double threshold, Colors color) {
        handler.addNumberCompareCellRule(columnIndex, threshold, CellColorHandler.CompareType.LESS_THAN,
            color.getIndex(), String.format("列%d数值小于%.2f", columnIndex, threshold));
        return this;
    }

    /**
     * 添加数值等于的单元格颜色规则
     */
    public CellColorHandlerBuilder cellNumberEquals(int columnIndex, Double value, Colors color) {
        handler.addNumberCompareCellRule(columnIndex, value, CellColorHandler.CompareType.EQUAL,
            color.getIndex(), String.format("列%d数值等于%.2f", columnIndex, value));
        return this;
    }

    /**
     * 添加数值大于等于的单元格颜色规则
     */
    public CellColorHandlerBuilder cellNumberGreaterEquals(int columnIndex, Double threshold, Colors color) {
        handler.addNumberCompareCellRule(columnIndex, threshold, CellColorHandler.CompareType.GREATER_EQUAL,
            color.getIndex(), String.format("列%d数值大于等于%.2f", columnIndex, threshold));
        return this;
    }

    /**
     * 添加数值小于等于的单元格颜色规则
     */
    public CellColorHandlerBuilder cellNumberLessEquals(int columnIndex, Double threshold, Colors color) {
        handler.addNumberCompareCellRule(columnIndex, threshold, CellColorHandler.CompareType.LESS_EQUAL,
            color.getIndex(), String.format("列%d数值小于等于%.2f", columnIndex, threshold));
        return this;
    }

    /**
     * 添加空值的单元格颜色规则
     */
    public CellColorHandlerBuilder cellIsNull(int columnIndex, Colors color) {
        handler.addNullCheckCellRule(columnIndex, true, color.getIndex(),
            String.format("列%d为空值", columnIndex));
        return this;
    }

    /**
     * 添加非空值的单元格颜色规则
     */
    public CellColorHandlerBuilder cellIsNotNull(int columnIndex, Colors color) {
        handler.addNullCheckCellRule(columnIndex, false, color.getIndex(),
            String.format("列%d为非空值", columnIndex));
        return this;
    }

    /**
     * 添加自定义条件的单元格颜色规则
     */
    public CellColorHandlerBuilder cellCustom(int columnIndex, Predicate<Object> condition, Colors color, String description) {
        handler.addCellColorRule(columnIndex, condition, color.getIndex(), description);
        return this;
    }

    // ========== 整行颜色规则 ==========

    /**
     * 添加字符串相等的整行颜色规则
     */
    public CellColorHandlerBuilder rowStringEquals(int triggerColumnIndex, String value, Colors color) {
        handler.addStringEqualsRowRule(triggerColumnIndex, value, color.getIndex(),
            String.format("根据列%d字符串等于'%s'设置整行颜色", triggerColumnIndex, value));
        return this;
    }

    /**
     * 添加字符串包含的整行颜色规则
     */
    public CellColorHandlerBuilder rowStringContains(int triggerColumnIndex, String value, Colors color) {
        handler.addStringContainsRowRule(triggerColumnIndex, value, color.getIndex(),
            String.format("根据列%d字符串包含'%s'设置整行颜色", triggerColumnIndex, value));
        return this;
    }

    /**
     * 添加数值大于的整行颜色规则
     */
    public CellColorHandlerBuilder rowNumberGreaterThan(int triggerColumnIndex, Double threshold, Colors color) {
        handler.addNumberCompareRowRule(triggerColumnIndex, threshold, CellColorHandler.CompareType.GREATER_THAN,
            color.getIndex(), String.format("根据列%d数值大于%.2f设置整行颜色", triggerColumnIndex, threshold));
        return this;
    }

    /**
     * 添加数值小于的整行颜色规则
     */
    public CellColorHandlerBuilder rowNumberLessThan(int triggerColumnIndex, Double threshold, Colors color) {
        handler.addNumberCompareRowRule(triggerColumnIndex, threshold, CellColorHandler.CompareType.LESS_THAN,
            color.getIndex(), String.format("根据列%d数值小于%.2f设置整行颜色", triggerColumnIndex, threshold));
        return this;
    }

    /**
     * 添加数值等于的整行颜色规则
     */
    public CellColorHandlerBuilder rowNumberEquals(int triggerColumnIndex, Double value, Colors color) {
        handler.addNumberCompareRowRule(triggerColumnIndex, value, CellColorHandler.CompareType.EQUAL,
            color.getIndex(), String.format("根据列%d数值等于%.2f设置整行颜色", triggerColumnIndex, value));
        return this;
    }

    /**
     * 添加数值大于等于的整行颜色规则
     */
    public CellColorHandlerBuilder rowNumberGreaterEquals(int triggerColumnIndex, Double threshold, Colors color) {
        handler.addNumberCompareRowRule(triggerColumnIndex, threshold, CellColorHandler.CompareType.GREATER_EQUAL,
            color.getIndex(), String.format("根据列%d数值大于等于%.2f设置整行颜色", triggerColumnIndex, threshold));
        return this;
    }

    /**
     * 添加数值小于等于的整行颜色规则
     */
    public CellColorHandlerBuilder rowNumberLessEquals(int triggerColumnIndex, Double threshold, Colors color) {
        handler.addNumberCompareRowRule(triggerColumnIndex, threshold, CellColorHandler.CompareType.LESS_EQUAL,
            color.getIndex(), String.format("根据列%d数值小于等于%.2f设置整行颜色", triggerColumnIndex, threshold));
        return this;
    }

    /**
     * 添加空值的整行颜色规则
     */
    public CellColorHandlerBuilder rowIsNull(int triggerColumnIndex, Colors color) {
        handler.addNullCheckRowRule(triggerColumnIndex, true, color.getIndex(),
            String.format("根据列%d为空值设置整行颜色", triggerColumnIndex));
        return this;
    }

    /**
     * 添加非空值的整行颜色规则
     */
    public CellColorHandlerBuilder rowIsNotNull(int triggerColumnIndex, Colors color) {
        handler.addNullCheckRowRule(triggerColumnIndex, false, color.getIndex(),
            String.format("根据列%d为非空值设置整行颜色", triggerColumnIndex));
        return this;
    }

    /**
     * 添加自定义条件的整行颜色规则
     */
    public CellColorHandlerBuilder rowCustom(int triggerColumnIndex, Predicate<Object> condition, Colors color, String description) {
        handler.addRowColorRule(triggerColumnIndex, condition, color.getIndex(), description);
        return this;
    }

    // ========== 常用业务场景快捷方法 ==========

    /**
     * 状态列颜色规则（常用于订单状态、审核状态等）
     */
    public CellColorHandlerBuilder statusColumn(int columnIndex) {
        return cellStringEquals(columnIndex, "成功", Colors.GREEN)
              .cellStringEquals(columnIndex, "失败", Colors.RED)
              .cellStringEquals(columnIndex, "处理中", Colors.YELLOW)
              .cellStringEquals(columnIndex, "待审核", Colors.ORANGE)
              .cellStringEquals(columnIndex, "已取消", Colors.GREY_40_PERCENT);
    }

    /**
     * 金额列颜色规则（正数绿色，负数红色，零灰色）
     */
    public CellColorHandlerBuilder amountColumn(int columnIndex) {
        return cellNumberGreaterThan(columnIndex, 0.0, Colors.GREEN)
              .cellNumberLessThan(columnIndex, 0.0, Colors.RED)
              .cellNumberEquals(columnIndex, 0.0, Colors.GREY_25_PERCENT);
    }

    /**
     * 百分比列颜色规则（>=80%绿色，60-80%黄色，<60%红色）
     */
    public CellColorHandlerBuilder percentageColumn(int columnIndex) {
        return cellNumberGreaterEquals(columnIndex, 80.0, Colors.GREEN)
              .cellNumberGreaterEquals(columnIndex, 60.0, Colors.YELLOW)
              .cellNumberLessThan(columnIndex, 60.0, Colors.RED);
    }

    /**
     * 优先级列颜色规则
     */
    public CellColorHandlerBuilder priorityColumn(int columnIndex) {
        return cellStringEquals(columnIndex, "高", Colors.RED)
              .cellStringEquals(columnIndex, "中", Colors.YELLOW)
              .cellStringEquals(columnIndex, "低", Colors.GREEN);
    }

    /**
     * 是否列颜色规则
     */
    public CellColorHandlerBuilder booleanColumn(int columnIndex) {
        return cellStringEquals(columnIndex, "是", Colors.GREEN)
              .cellStringEquals(columnIndex, "否", Colors.RED)
              .cellStringEquals(columnIndex, "true", Colors.GREEN)
              .cellStringEquals(columnIndex, "false", Colors.RED);
    }

    /**
     * 根据状态列设置整行颜色
     */
    public CellColorHandlerBuilder statusRowColor(int statusColumnIndex) {
        return rowStringEquals(statusColumnIndex, "成功", Colors.LIGHT_GREEN)
              .rowStringEquals(statusColumnIndex, "失败", Colors.PINK)
              .rowStringEquals(statusColumnIndex, "异常", Colors.LIGHT_YELLOW)
              .rowStringEquals(statusColumnIndex, "待处理", Colors.LAVENDER);
    }

    /**
     * 根据金额列设置整行颜色
     */
    public CellColorHandlerBuilder amountRowColor(int amountColumnIndex) {
        return rowNumberGreaterThan(amountColumnIndex, 10000.0, Colors.LIGHT_GREEN)
              .rowNumberLessThan(amountColumnIndex, 0.0, Colors.PINK);
    }

    /**
     * 构建处理器
     */
    public CellColorHandler build() {
        return handler;
    }

    /**
     * 创建默认样式配置
     */
    public static CellColorHandler.StyleConfig createDefaultStyleConfig() {
        return new CellColorHandler.StyleConfig();
    }

    /**
     * 创建自定义样式配置
     */
    public static CellColorHandler.StyleConfig createStyleConfig(String fontName, Short headerFontSize, Short dataFontSize) {
        CellColorHandler.StyleConfig config = new CellColorHandler.StyleConfig();
        config.setFontName(fontName);
        config.setHeaderFontSize(headerFontSize);
        config.setDataFontSize(dataFontSize);
        return config;
    }
}
