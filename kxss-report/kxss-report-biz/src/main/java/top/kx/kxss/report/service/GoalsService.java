package top.kx.kxss.report.service;

import top.kx.kxss.report.query.GoalsQuery;
import top.kx.kxss.report.vo.GoalsAchievementResultVO;
import top.kx.kxss.report.vo.GoalsPaymentResultVO;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

/**
 * API
 *
 * <AUTHOR>
 */
public interface GoalsService {

    GoalsPaymentResultVO payment(DataOverviewQuery query);

    GoalsAchievementResultVO achievementRate(GoalsQuery query);

}
