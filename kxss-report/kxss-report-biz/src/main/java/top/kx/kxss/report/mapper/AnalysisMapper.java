package top.kx.kxss.report.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.kxss.report.vo.ProductAttributeResultVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 *
 * </p>
 *
 * <AUTHOR>
 */
@Repository
public interface AnalysisMapper {

//    IPage<ProductAttributeResultVO> productPage(@Param("page") IPage<ProductAttributeResultVO> page, @Param(Constants.WRAPPER) QueryWrapper queryWrapper);
//
//    ProductAttributeResultVO productSum(@Param(Constants.WRAPPER) QueryWrapper queryWrapper);
//
//    List<ProductAttributeResultVO> productList(@Param(Constants.WRAPPER) QueryWrapper queryWrapper);

}


