package top.kx.kxss.report.service.reconciliation.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillTypeEnum;
import top.kx.kxss.pay.vo.result.IsvReconciliationInfoResultVO;
import top.kx.kxss.report.entity.reconciliation.ReconciliationRecord;
import top.kx.kxss.report.manager.reconciliation.ReconciliationRecordManager;
import top.kx.kxss.report.mapper.reconciliation.ReconciliationRecordMapper;
import top.kx.kxss.report.query.CashPaymentQuery;
import top.kx.kxss.report.service.PosCashPaymentService;
import top.kx.kxss.report.service.reconciliation.ReconciliationRecordService;
import top.kx.kxss.report.vo.CashPaymentResultVO;
import top.kx.kxss.report.vo.PaymentTransactionResultVO;
import top.kx.kxss.report.vo.query.reconciliation.ReconciliationRecordPageQuery;
import top.kx.kxss.report.vo.result.reconciliation.ReconciliationRecordResultVO;
import top.kx.kxss.report.vo.result.reconciliation.StatisticReconciliationDownloadResultVO;
import top.kx.kxss.report.vo.save.reconciliation.ReconciliationRecordSaveVO;
import top.kx.kxss.report.vo.update.reconciliation.ReconciliationRecordUpdateVO;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 对账单记录
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-30 20:18:34
 * @create [2025-06-30 20:18:34] [dou] [代码生成器生成]
 */
@DS(DsConstant.DEFAULTS)
@Slf4j
@RequiredArgsConstructor
@Service
public class ReconciliationRecordServiceImpl extends SuperServiceImpl<ReconciliationRecordManager, Long, ReconciliationRecord, ReconciliationRecordSaveVO,
        ReconciliationRecordUpdateVO, ReconciliationRecordPageQuery, ReconciliationRecordResultVO> implements ReconciliationRecordService {


    @Autowired
    private ReconciliationRecordMapper reconciliationRecordMapper;
    @Autowired
    private PosCashPaymentService posCashPaymentService;

    @Override
    public boolean remove(LbQueryWrap<ReconciliationRecord> eq) {
        return superManager.remove(eq);
    }

    @Override
    public List<StatisticReconciliationDownloadResultVO> getDownloadDataList(IsvReconciliationInfoResultVO resultVO, String startTime
            , String endTime) {
        List<StatisticReconciliationDownloadResultVO> downloadDataList = Optional.ofNullable(reconciliationRecordMapper.getDownloadDataList(resultVO.getInstNo(),
                resultVO.getMchNo(), startTime,
                endTime)).orElse(Lists.newArrayList());
        List<PaymentTransactionResultVO> resultVOList = Optional.ofNullable(posCashPaymentService.selectPayAmount(DataOverviewQuery.builder()
                .startDate(startTime)
                .endDate(endTime)
                .build(), ContextUtil.getCurrentCompanyId())).orElse(Lists.newArrayList());
        Map<String, PaymentTransactionResultVO> transactionMap = Optional.of(resultVOList.stream().collect(Collectors.toMap(PaymentTransactionResultVO::getOrderId,
                        v -> v, (k1, k2) -> k2)))
                .orElse(MapUtil.newHashMap());
        downloadDataList.forEach(v -> {
            PaymentTransactionResultVO paymentTransactionResultVO = transactionMap.get(v.getTerminalSerialNo());
            v.setResult("不一致");
            if (paymentTransactionResultVO != null) {
                v.setCashCode(paymentTransactionResultVO.getCode());
                v.setSystemCompleteTime(paymentTransactionResultVO.getCompleteTime());
                v.setResult("一致");
            }
        });
        List<String> terminalSerialNo = downloadDataList.stream().map(StatisticReconciliationDownloadResultVO::getTerminalSerialNo)
                .collect(Collectors.toList());
        List<StatisticReconciliationDownloadResultVO> collect = resultVOList.stream().filter(v -> !terminalSerialNo.contains(v.getOrderId()))
                .map(v ->
                        StatisticReconciliationDownloadResultVO.builder()
                                .cashCode(v.getCode())
                                .result("不一致")
                                .terminalSerialNo(v.getOrderId())
                                .systemCompleteTime(v.getCompleteTime())
                                .build()).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            downloadDataList.addAll(collect);
        }
        List<StatisticReconciliationDownloadResultVO> collect1 = downloadDataList.stream().filter(v -> StrUtil.isNotBlank(v.getResult())
                && "不一致".equals(v.getResult())).collect(Collectors.toList());
        List<StatisticReconciliationDownloadResultVO> reconciliationDownloadResultVOList = Lists.newArrayList();
        List<StatisticReconciliationDownloadResultVO> voList = collect1.stream()
                .filter(v -> StrUtil.isNotBlank(v.getTerminalNo())
                        && StrUtil.isBlank(v.getCashCode())
                        && StrUtil.isNotBlank(v.getTransactionState()) && "支付成功".equals(v.getTransactionState()))
                .collect(Collectors.toList());
        List<String> rerminalNoList = voList.stream()
                .map(StatisticReconciliationDownloadResultVO::getTerminalNo)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(rerminalNoList)) {
            Map<String, CashPaymentResultVO> cashPaymentResultVOMap = posCashPaymentService
                    .cashPaymentList(CashPaymentQuery.builder().orderIds(rerminalNoList)
                            .orgId(ContextUtil.getCurrentCompanyId())
                            .build())
                    .stream().collect(Collectors.toMap(CashPaymentResultVO::getOrderId,
                            v -> v, (k1, k2) -> k2));
            List<String> noCompletes = Arrays.asList(PosCashBillStateEnum.NO_SETTLED.getCode(), PosCashBillStateEnum.NO_PAY.getCode(),
                    PosCashBillStateEnum.PART_PAY.getCode());
            for (StatisticReconciliationDownloadResultVO downloadResultVO : voList) {
                CashPaymentResultVO paymentResultVO = cashPaymentResultVOMap.get(downloadResultVO.getTerminalNo());
                if (paymentResultVO == null) {
                    downloadResultVO.setResultReason("系统未查询到支付记录");
                    continue;
                }
                if (noCompletes.contains(paymentResultVO.getBillState())) {
                    if (paymentResultVO.getCompleteTime() != null
                            && paymentResultVO.getBillType().equals(PosCashBillTypeEnum.COUNTER_CHECKOUT.getCode())) {
                        downloadResultVO.setResultReason("订单存在反结账，当前订单未完成");
                        continue;
                    }
                    downloadResultVO.setResultReason("当前订单未完成");
                    continue;
                }
                if (paymentResultVO.getBillState().equals(PosCashBillStateEnum.REFUNDED.getCode())) {
                    downloadResultVO.setResultReason("系统对应订单已退款");
                }
            }
            reconciliationDownloadResultVOList.addAll(voList);
        }
        List<StatisticReconciliationDownloadResultVO> voList1 = collect1.stream()
                .filter(v -> StrUtil.isBlank(v.getTerminalNo())
                        && StrUtil.isNotBlank(v.getCashCode()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(voList1)) {
            for (StatisticReconciliationDownloadResultVO downloadResultVO : voList1) {
                downloadResultVO.setResultReason("订单在查询时间内完成，支付时间未在当前时间段内");
            }
            reconciliationDownloadResultVOList.addAll(voList1);
        }
        return reconciliationDownloadResultVOList;

    }
}


