package top.kx.kxss.report.service;

import top.kx.basic.base.request.PageParams;
import top.kx.kxss.report.query.GroupBuyQuery;
import top.kx.kxss.report.vo.GroupBuyResultVO;

import java.util.List;
import java.util.Map;

/**
 * API
 *
 * <AUTHOR>
 */
public interface GroupBuyService {

    /**
     * 分页查询
     *
     * @param params
     * @return
     */
    Map<String, Object> page(PageParams<GroupBuyQuery> params);

    /**
     * 统计查询
     *
     * @param params
     * @return
     */
    GroupBuyResultVO sum(GroupBuyQuery params);

    /**
     * 列表查询
     *
     * @param params
     * @return
     */
    List<GroupBuyResultVO> list(GroupBuyQuery params);

}
