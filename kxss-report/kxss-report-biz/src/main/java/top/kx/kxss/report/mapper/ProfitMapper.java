package top.kx.kxss.report.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.kxss.app.vo.result.cash.AmountResultVO;
import top.kx.kxss.report.vo.FeeAmountResultVO;
import top.kx.kxss.report.vo.ReferenceProfitResultVO;

import java.math.BigDecimal;

/**
 * <p>
 * Mapper 接口
 * 利润销售统计
 * </p>
 *
 * <AUTHOR>
 */
@Repository
public interface ProfitMapper {

    /**
     * pos_cash 查询
     * @param queryWrapper
     * @return
     */
    AmountResultVO selectOneCashAmount(@Param(Constants.WRAPPER) QueryWrapper queryWrapper);

    /**
     * 商品销售查询 查询
     * @param queryWrapper
     * @return
     */
    AmountResultVO selectProductAmount(@Param(Constants.WRAPPER) QueryWrapper queryWrapper);

    /**
     * 手续费
     */
    FeeAmountResultVO selectFeeAmount(@Param(Constants.WRAPPER) QueryWrapper queryWrapper);

}


