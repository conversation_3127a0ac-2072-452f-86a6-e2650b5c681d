<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.report.mapper.CardMapper">

    <select id="memberCardChangePage" resultType="top.kx.kxss.report.vo.MemberCardChangeResultVO">
        SELECT mc.id         AS memberCardId,
               mcc.type_          AS type,
               mcc.name           AS name,
               mcc.created_time   AS consumeTime,
               pc.complete_time   AS completeTime,
               pc.code            AS code,
               pc.order_source    AS orderSource,
               mcc.member_id      AS memberId,
               mi.name            AS memberName,
               mi.mobile          AS mobile,
               mcc.discount_type  AS discountType,
               mcc.amount         AS amount,
               mcc.balance        AS balance,
               mcc.last_balance   AS lastBalance,
               mcc.project        AS project,
               mcc.explain_       AS explainDesc,
               mcc.employee_id    AS employeeId,
               mcc.created_org_id AS orgId
        FROM member_card_change mcc
                 LEFT JOIN member_info mi ON mcc.member_id = mi.id
                 LEFT JOIN member_card mc on mcc.member_card_id = mc.id
                 LEFT JOIN pos_cash pc on mcc.source_id = pc.id
              ${ew.customSqlSegment}
        GROUP BY mcc.id
    </select>

    <select id="memberCardChangeSum" resultType="top.kx.kxss.report.vo.MemberCardChangeResultVO">
        SELECT sum(mcc.amount)         AS amount,
               sum(mcc.balance)        AS balance
        FROM member_card_change mcc
                 LEFT JOIN member_info mi ON mcc.member_id = mi.id
                 LEFT JOIN member_card mc on mcc.member_card_id = mc.id
                 LEFT JOIN pos_cash pc on mcc.source_id = pc.id
            ${ew.customSqlSegment}
    </select>

    <select id="memberCardChangeList" resultType="top.kx.kxss.report.vo.MemberCardChangeResultVO">
        SELECT mc.id         AS memberCardId,
               mcc.type_          AS type,
               mcc.name           AS name,
               mcc.created_time   AS consumeTime,
               pc.complete_time   AS completeTime,
               pc.code            AS code,
               pc.order_source    AS orderSource,
               mcc.member_id      AS memberId,
               mi.name            AS memberName,
               mi.mobile          AS mobile,
               mcc.discount_type  AS discountType,
               mcc.amount         AS amount,
               mcc.balance        AS balance,
               mcc.last_balance   AS lastBalance,
               mcc.project        AS project,
               mcc.explain_       AS explainDesc,
               mcc.employee_id    AS employeeId,
               mcc.created_org_id AS orgId
        FROM member_card_change mcc
                 LEFT JOIN member_info mi ON mcc.member_id = mi.id
                 LEFT JOIN member_card mc on mcc.member_card_id = mc.id
                 LEFT JOIN pos_cash pc on mcc.source_id = pc.id
            ${ew.customSqlSegment}
        GROUP BY mcc.id
    </select>


</mapper>
