<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.report.mapper.SelfMapper">


    <select id="getSelfServiceDetail" resultType="top.kx.kxss.report.vo.result.self.SelfServiceDetailResultVO">
        select t.employee_id                         as employeeId,
               t.service_id                          as serviceId,
               be.group_id                           as groupId,
               be.position_id                        as positionId,
               count(distinct t.id)                  as clockNum,
               ifnull(sum(ifnull(t.duration, 0)), 0) as serviceDurationMinute,
               IFNULL(
                       sum(CASE
                               WHEN t.cycle IS NULL
                                   OR t.cycle = '' THEN
                                   0
                               WHEN instr(t.cycle, '元/') <![CDATA[ <= ]]> 0 THEN 0
                               WHEN instr(t.cycle, '元/小时') > 0 THEN
                                   (IFNULL(t.cycle_num, 0) * 60)
                               WHEN instr(t.cycle, '元/小时') <![CDATA[ <= ]]> 0 AND instr(t.cycle, '小时') > 0 THEN
                                   (
                                       IFNULL(t.cycle_num, 0) * 60 *
                                       SUBSTRING_INDEX(SUBSTRING_INDEX(t.cycle, '/', -1), '小时', 1))
                               WHEN instr(t.cycle, '元/分钟') > 0 THEN
                                   IFNULL(t.cycle_num, 0)
                               WHEN instr(t.cycle, '元/分钟') <![CDATA[ <= ]]> 0 AND instr(t.cycle, '分钟') > 0 THEN
                                   (
                                       IFNULL(t.cycle_num, 0) *
                                       SUBSTRING_INDEX(SUBSTRING_INDEX(t.cycle, '/', -1), '分钟', 1))
                               ELSE 0
                           END),
                       0
               )                                     as cycleDurationMinute
        FROM pos_cash_service t
                 LEFT JOIN pos_cash p ON p.id = t.cash_id
                 LEFT JOIN base_employee be on t.employee_id = be.id
        where p.delete_flag = 0
          and t.delete_flag = 0
          AND p.bill_type IN ('0', '3', '4')
          AND p.bill_state IN ('2', '5')
          and t.status = '1'
        <if test="model.startDate != null and model.startDate != ''">
            and p.complete_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.complete_time &lt;= #{model.endDate}
        </if>
        <if test="model.orgIdList != null and model.orgIdList.size() > 0">
            and p.org_id IN
            <foreach item="orgId" collection="model.orgIdList" open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        group by t.employee_id, t.service_id
    </select>

</mapper>
