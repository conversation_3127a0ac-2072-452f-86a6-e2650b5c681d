<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.report.mapper.yearend.YearEnd24Mapper">
<!--
    代码生成器 by 2024-12-26 18:46:28
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.report.entity.yearend.YearEnd24">
        <id column="id" property="id" />
        <result column="start_date" property="startDate" />
        <result column="total_days" property="totalDays" />
        <result column="total_duration" property="totalDuration" />
        <result column="order_num" property="orderNum" />
        <result column="light_times" property="lightTimes" />
        <result column="product_sales" property="productSales" />
        <result column="recharge_times" property="rechargeTimes" />
        <result column="total_gift_amount" property="totalGiftAmount" />
        <result column="total_discount_amount" property="totalDiscountAmount" />
        <result column="member_num" property="memberNum" />
        <result column="curr_member_num" property="currMemberNum" />
        <result column="busy_month" property="busyMonth" />
        <result column="busy_mouth_amount" property="busyMouthAmount" />
        <result column="most_popular_table" property="mostPopularTable" />
        <result column="most_popular_table_duration" property="mostPopularTableDuration" />
        <result column="most_service_duration_table" property="mostServiceDurationTable" />
        <result column="most_amount_table" property="mostAmountTable" />
        <result column="last_duration_table" property="lastDurationTable" />
        <result column="last_duration" property="lastDuration" />
        <result column="highest_paid_date" property="highestPaidDate" />
        <result column="top3_product_name" property="top3ProductName" />
        <result column="highest_profit_product_name" property="highestProfitProductName" />
        <result column="highest_profit_product_amount" property="highestProfitProductAmount" />
        <result column="total_employee_num" property="totalEmployeeNum" />
        <result column="departure_employee_num" property="departureEmployeeNum" />
        <result column="curr_employee_num" property="currEmployeeNum" />
        <result column="open_times" property="openTimes" />
        <result column="first_complete_time" property="firstCompleteTime" />
        <result column="latest_complete_time" property="latestCompleteTime" />
        <result column="identity_title" property="identityTitle" />
        <result column="full_platform_rate" property="fullPlatformRate" />
        <result column="avg_open_duration" property="avgOpenDuration" />
        <result column="month_most_amount" property="monthMostAmount" />
        <result column="day_most_amount" property="dayMostAmount" />
        <result column="tenant_id" property="tenantId" />
        <result column="org_id" property="orgId" />
        <result column="created_by" property="createdBy" />
        <result column="created_time" property="createdTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="delete_flag" property="deleteFlag" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, start_date, total_days, total_duration, order_num, light_times,
        product_sales, recharge_times, total_gift_amount, total_discount_amount, member_num, curr_member_num,
        busy_month, busy_mouth_amount, most_popular_table, most_popular_table_duration, most_service_duration_table, most_amount_table,
        last_duration_table, last_duration, highest_paid_date, top3_product_name, highest_profit_product_name, highest_profit_product_amount,
        total_employee_num, departure_employee_num, curr_employee_num, open_times, first_complete_time, latest_complete_time,
        identity_title, full_platform_rate, avg_open_duration, month_most_amount, day_most_amount, tenant_id,
        org_id, created_by, created_time, updated_by, updated_time, delete_flag

    </sql>

    <select id="cashListAmount" resultType="top.kx.kxss.report.vo.yearend.CashAmountResultVO">
        select ${field}                               as field,
               DATE_FORMAT(MIN(p.bill_date), '%Y-%m') as `time`,
               sum(p.amount)-sum(p.discount_amount)   as amount,
               sum(p.payment) - sum(p.refund_amount)  as payment,
               sum(p.discount_amount)                 as discountAmount,
               sum(p.refund_amount)                   as refundAmount,
               sum(IFNULL(p.gift_amount, 0))          as giftAmount,
               count(p.id)                            as `num`,
               sum(IFNULL(p.paid, 0))                 as paid,
               sum(IFNULL(p.unpaid, 0))               as unpaid,
               MAX(p.table_name)               as tableName
        from pos_cash p
            ${ew.customSqlSegment}
        GROUP BY ${field}

    </select>
    <select id="tableList" resultType="top.kx.kxss.report.vo.yearend.DetailAmountResultVO">
        select t.table_id                                                            as bizId,
               MAX(IFNULL(b.name, t.table_name))                                                     as bizName,
               sum(IFNULL(t.duration, 0))                                            as bizValue,
               sum(IFNULL(t.refund_amount, 0))                                       as refundAmount,
               sum(IFNULL(t.amount, 0)) - sum(IFNULL(t.refund_amount, 0))            as payment,
               0                                                                     as profitAmount,
               sum(IFNULL(t.orgin_price, 0))                                         as amount,
               sum(IFNULL(t.discount_amount, 0)) + sum(IFNULL(t.assessed_amount, 0)) as discountAmount
        from pos_cash_table t
                 inner join pos_cash p on p.id = t.cash_id
                 left join base_table_info b
                           on b.id = t.table_id and b.is_virtual = 0
            ${ew.customSqlSegment}
        GROUP BY t.table_id
    </select>
    <select id="thailList" resultType="top.kx.kxss.report.vo.yearend.DetailAmountResultVO">
        select p.table_id                                                            as bizId,
               MIN(t.thail_name)                                                     as bizName,
               sum(IFNULL(t.duration, 0))                                            as bizValue,
               sum(IFNULL(t.refund_amount, 0))                                       as refundAmount,
               sum(IFNULL(t.amount, 0)) - sum(IFNULL(t.refund_amount, 0))            as payment,
               sum(IFNULL(t.orgin_price, 0))                                         as amount,
               sum(IFNULL(t.profit_price, 0))                                         as profitAmount,
               sum(IFNULL(t.discount_amount, 0)) + sum(IFNULL(t.assessed_amount, 0)) as discountAmount
        from pos_cash_thail t
                 inner join pos_cash p on p.id = t.cash_id
            ${ew.customSqlSegment}
        GROUP BY p.table_id
    </select>
    <select id="serviceList" resultType="top.kx.kxss.report.vo.yearend.DetailAmountResultVO">
        select t.employee_id                                                         as bizId,
               MIN(t.employee_name)                                                  as bizName,
               sum(IFNULL(t.duration, 0))                                            as bizValue,
               sum(IFNULL(t.refund_amount, 0))                                       as refundAmount,
               sum(IFNULL(t.amount, 0)) - sum(IFNULL(t.refund_amount, 0))            as payment,
               sum(IFNULL(t.orgin_price, 0))                                         as amount,
               sum(IFNULL(t.profit_price, 0))                                         as profitAmount,
               sum(IFNULL(t.discount_amount, 0)) + sum(IFNULL(t.assessed_amount, 0)) as discountAmount
        from pos_cash_service t
                 inner join pos_cash p on p.id = t.cash_id
            ${ew.customSqlSegment}
        GROUP BY t.employee_id
    </select>
    <select id="productList" resultType="top.kx.kxss.report.vo.yearend.DetailAmountResultVO">
        select t.product_id                                                          as bizId,
               MIN(t.product_name)                                                   as bizName,
               sum(IFNULL(t.num, 0))                                                 as bizValue,
               sum(IFNULL(t.refund_amount, 0))                                       as refundAmount,
               sum(IFNULL(t.amount, 0)) - sum(IFNULL(t.refund_amount, 0))            as payment,
               sum(IFNULL(t.orgin_price, 0))                                         as amount,
               sum(IFNULL(t.profit_price, 0))                                         as profitAmount,
               sum(IFNULL(t.discount_amount, 0)) + sum(IFNULL(t.assessed_amount, 0)) as discountAmount
        from pos_cash_product t
                 inner join pos_cash p on p.id = t.cash_id
            ${ew.customSqlSegment}
        GROUP BY t.product_id
    </select>
    <select id="bizLogCount" resultType="java.lang.Long">
        select count(1)
        from base_biz_log
                 ${ew.customSqlSegment}
    </select>
    <select id="memberList" resultType="top.kx.kxss.base.vo.NameValueVO">
        select delete_flag as name,
               count(1)    as `value`
        from member_info
        group by delete_flag
    </select>
    <select id="topOneByTable" resultType="top.kx.kxss.report.vo.yearend.DetailAmountResultVO">
        SELECT bizId,
               MAX(table_name)                             AS bizName,
               SUM(duration)                               AS bizValue,
               SUM(refund_amount)                          AS refundAmount,
               SUM(amount) - SUM(refund_amount)            AS payment,
               SUM(orgin_price)                            AS amount,
               SUM(discount_amount) + SUM(assessed_amount) AS discountAmount
        FROM (SELECT CONCAT(t.cash_id, '_', t.table_id) AS bizId,
                     t.table_name,
                     IFNULL(t.duration, 0)              AS duration,
                     IFNULL(t.refund_amount, 0)         AS refund_amount,
                     IFNULL(t.amount, 0)                AS amount,
                     IFNULL(t.orgin_price, 0)           AS orgin_price,
                     IFNULL(t.discount_amount, 0)       AS discount_amount,
                     IFNULL(t.assessed_amount, 0)       AS assessed_amount
              FROM pos_cash_table t
                       INNER JOIN pos_cash p
                                  ON p.id = t.cash_id
                  ${ew.customSqlSegment}
              ) AS subquery
        GROUP BY bizId
        ORDER BY bizValue DESC LIMIT 1
    </select>
    <select id="employeeList" resultType="top.kx.kxss.base.vo.NameValueVO">
        select concat(IFNULL(position_status, '10'), '_', delete_flag)
                        as name,
               count(1) as `value`
        from base_employee
        group by concat(IFNULL(position_status, '10'), '_', delete_flag)
    </select>
    <select id="tableCount" resultType="java.lang.Long">
        select count(1)
        from base_table_info
        where delete_flag = 0
          and is_virtual = 0
    </select>
    <select id="memberConsumeAmount" resultType="top.kx.kxss.report.vo.yearend.CashAmountResultVO">
        select sum(t.amount) - sum(t.refund_amount) as payment,
               IFNULL(t.member_id, 0) as field
        from pos_cash_payment t
                 inner join pos_cash p on p.id = t.cash_id
            ${ew.customSqlSegment}
        GROUP BY IFNULL(t.member_id, 0)
    </select>
    <select id="cashListAmount2" resultType="top.kx.kxss.report.vo.yearend.CashAmountResultVO">
        SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0)                                           amount,
               IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0) payment,
               IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0) +
               IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0)                                       discountAmount,
               IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0)                                           giftAmount,
               IFNULL(SUM(IFNULL(p.paid, 0)), 0)                                                  paid,
               IFNULL(SUM(IFNULL(p.unpaid, 0)), 0)                                                unpaid,
               IFNULL(COUNT(p.id), 0)                                                             num,
               '套餐'      AS                                                                     field,
               p.bill_type as                                                                     billType,
               'THAIL'     AS                                                                     type
        FROM pos_cash_thail t
                 JOIN pos_cash p ON p.id = t.cash_id
            and t.delete_flag = 0
            ${ew.customSqlSegment}
        GROUP BY
            field,
            bill_type
        UNION ALL
        (
        SELECT
            IFNULL( SUM( IFNULL( t.orgin_price, 0 )), 0 ) amount, IFNULL( SUM( IFNULL( t.amount, 0 )), 0 ) - IFNULL( SUM( IFNULL( t.assessed_amount, 0 )), 0 ) payment, IFNULL( SUM( IFNULL( t.discount_amount, 0 )), 0 ) + IFNULL( SUM( IFNULL( t.assessed_amount, 0 )), 0 ) discountAmount, IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0) giftAmount, IFNULL(SUM(IFNULL(p.paid, 0)), 0) paid, IFNULL(SUM(IFNULL(p.unpaid, 0)), 0) unpaid, IFNULL(count(p.id), 0) num, '台费' AS field, p.bill_type as billType, 'TABLE' AS `type`
        FROM
            pos_cash_table t
            inner JOIN pos_cash p
        ON p.id = t.cash_id
            inner join base_table_info `table` on `table`.id = t.table_id
            and t.cash_thail_id is null and p.table_id is not null
            and p.type_ = '0' and t.delete_flag = 0 and `table`.delete_flag = 0
            and t.status = '1'
            ${ew.customSqlSegment}
        GROUP BY
            field,
            bill_type)
        UNION ALL
        (
        SELECT
            IFNULL( SUM( IFNULL( t.orgin_price, 0 )), 0 ) amount, IFNULL( SUM( IFNULL( t.amount, 0 )), 0 ) -IFNULL( SUM( IFNULL( t.assessed_amount, 0 )), 0 ) payment, IFNULL( SUM( IFNULL( t.discount_amount, 0 )), 0 )+ IFNULL( SUM( IFNULL( t.assessed_amount, 0 )), 0 ) discountAmount, IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0) giftAmount, IFNULL(SUM(IFNULL(p.paid, 0)), 0) paid, IFNULL(SUM(IFNULL(p.unpaid, 0)), 0) unpaid, IFNULL(count(p.id), 0) num, '商品' AS field, p.bill_type as billType, 'PRODUCT' AS `type`
        FROM
            pos_cash_product t
            JOIN pos_cash p
        ON p.id = t.cash_id and t.delete_flag = 0
            and t.cash_thail_id is null
            ${ew.customSqlSegment}
        GROUP BY
            field,
            bill_type
            )
        UNION ALL
        (
        SELECT
            IFNULL( SUM( IFNULL( t.orgin_price, 0 )), 0 ) amount, IFNULL( SUM( IFNULL( t.amount, 0 )), 0 ) -IFNULL( SUM( IFNULL( t.assessed_amount, 0 )), 0 ) payment, IFNULL( SUM( IFNULL( t.discount_amount, 0 )), 0 )+ IFNULL( SUM( IFNULL( t.assessed_amount, 0 )), 0 ) discountAmount, IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0) giftAmount, IFNULL(SUM(IFNULL(p.paid, 0)), 0) paid, IFNULL(SUM(IFNULL(p.unpaid, 0)), 0) unpaid, IFNULL(count(p.id), 0) num, '服务' AS field, p.bill_type as billType, 'SERVICE' AS `type`
        FROM
            pos_cash_service t
            JOIN pos_cash p
        ON p.id = t.cash_id and t.delete_flag = 0
            and t.cash_thail_id is null and t.status = '1'
            ${ew.customSqlSegment}
        GROUP BY
            field,
            bill_type
            )
        UNION ALL
        (
        SELECT
            IFNULL( SUM( IFNULL( t.orgin_price, 0 )), 0 ) amount, IFNULL( SUM( IFNULL( t.amount, 0 )), 0 ) -IFNULL( SUM( IFNULL( t.assessed_amount, 0 )), 0 ) payment, IFNULL( SUM( IFNULL( t.discount_amount, 0 )), 0 )+ IFNULL( SUM( IFNULL( t.assessed_amount, 0 )), 0 ) discountAmount, IFNULL(SUM(IFNULL(p.gift_amount, 0)), 0) giftAmount, IFNULL(SUM(IFNULL(p.paid, 0)), 0) paid, IFNULL(SUM(IFNULL(p.unpaid, 0)), 0) unpaid, IFNULL(count(p.id), 0) num, '购卡' AS field, p.bill_type as billType, 'BUY_CARD' AS `type`
        FROM
            pos_cash_card t
            JOIN pos_cash p
        ON p.id = t.cash_id and t.delete_flag = 0
            and t.cash_thail_id is null
            ${ew.customSqlSegment}
        GROUP BY
            field,
            bill_type
            )
    </select>

    <select id="selectByDiscountType" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT (0 - IFNULL(SUM(IFNULL(t.price_change, 0)), 0)) amount,
               t.discount_type      AS                         field,
               min(t.discount_type) as                         name,
               IFNULL(count(t.id), 0)                          num
        FROM pos_cash_discount_detail t
                 JOIN pos_cash p ON p.id = t.pos_cash_id
            ${ew.customSqlSegment}
        GROUP BY
            t.discount_type

    </select>

</mapper>
