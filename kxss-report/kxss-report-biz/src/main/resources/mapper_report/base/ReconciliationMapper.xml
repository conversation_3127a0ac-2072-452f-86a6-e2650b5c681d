<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.report.mapper.reconciliation.ReconciliationMapper">
    <!--
        代码生成器 by 2025-07-01 15:55:00
        自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
    -->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.report.entity.reconciliation.Reconciliation">
        <id column="id" property="id"/>
        <result column="reconciliation_date" property="reconciliationDate"/>
        <result column="inst_no" property="instNo"/>
        <result column="mch_no" property="mchNo"/>
        <result column="transaction_amount" property="transactionAmount"/>
        <result column="fee_amount" property="feeAmount"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="balance_amount" property="balanceAmount"/>
        <result column="merchant_discount_amount" property="merchantDiscountAmount"/>
        <result column="merchant_actual_amount" property="merchantActualAmount"/>
        <result column="user_paid_amount" property="userPaidAmount"/>
        <result column="platform_discount_amount" property="platformDiscountAmount"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
        <result column="delete_flag" property="deleteFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , reconciliation_date, inst_no, mch_no, transaction_amount, fee_amount,
        refund_amount, balance_amount, merchant_discount_amount, merchant_actual_amount, user_paid_amount, platform_discount_amount,
        created_time, updated_time, created_by, updated_by, delete_flag
    </sql>
    <select id="statistic"
            resultType="top.kx.kxss.report.vo.result.reconciliation.StatisticReconciliationResultVO">
        select
        SUM( CASE WHEN t.transaction_state = 5
        THEN 0 - t.refund_amount
        ELSE t.transaction_amount END ) AS
        transactionAmount,
        SUM( t.balance_amount ) AS recordedAmount,
        SUM( t.fee_amount ) AS feeAmount,
        count( t.id ) AS transactionOrderNum
        from t_reconciliation_record t
        where t.delete_flag = 0
        <if test="model.mchNo != null and model.mchNo !=''">
            and t.mch_no = #{model.mchNo}
        </if>
        <if test="model.instNo != null and model.instNo !=''">
            and t.inst_no = #{model.instNo}
        </if>
        <if test="model.startTime != null and model.startTime !=''">
            and t.transaction_time >= #{model.startTime}
        </if>
        <if test="model.endTime != null and model.endTime !=''">
            and t.transaction_time <![CDATA[ <= ]]>  #{model.endTime}
        </if>
        group by concat(t.mch_no, '_',t.inst_no)
    </select>
    <select id="dayList" resultType="top.kx.kxss.pay.vo.result.ReconciliationResultVO">
        SELECT
        DATE(IF(TIME(transaction_time) &lt; #{model.settlementTime}, DATE_SUB(transaction_time, INTERVAL 1
        DAY),transaction_time)) AS successDate,
        ROUND(SUM(balance_amount) / 100, 2) AS amount,
        ROUND((SUM(balance_amount) - SUM(refund_amount)) / 100 , 2) AS totalAmount,
        ROUND(SUM(fee_amount) / 100, 2) AS mchFeeAmount,
        ROUND(SUM(refund_amount) / 100 ,2) AS refundAmount,
        STR_TO_DATE(
        CONCAT(
        DATE(
        IF(TIME(transaction_time) <![CDATA[ < ]]> #{model.settlementTime},
        DATE_SUB(transaction_time, INTERVAL 1 DAY),
        transaction_time)
        ),
        concat(' ',#{model.settlementTime})
        ),
        '%Y-%m-%d %H:%i:%s'
        ) AS settlementStartDate,
        DATE_SUB(STR_TO_DATE(
        CONCAT(
        DATE(
        IF(TIME(transaction_time) <![CDATA[ < ]]> #{model.settlementTime},
        DATE_SUB(transaction_time, INTERVAL 1 DAY),
        transaction_time)
        ) + INTERVAL 1 DAY,
        concat(' ',#{model.settlementTime})
        ),
        '%Y-%m-%d %H:%i:%s'
        ), INTERVAL 1 SECOND) AS settlementEndDate
        FROM t_reconciliation_record
        WHERE delete_flag = 0
        <if test="model.mchNoList != null and model.mchNoList.size() > 0">
            and mch_no in
            <foreach collection="model.mchNoList" item="mchNo" separator="," open="(" close=")">
                #{mchNo}
            </foreach>
        </if>
        <if test="model.startTime != null and model.startTime!=''">
            and transaction_time >= #{model.startTime}
        </if>
        <if test="model.endTime != null and model.endTime!=''">
            and transaction_time <![CDATA[ <= ]]> #{model.endTime}
        </if>
        GROUP BY successDate
        ORDER BY successDate desc
    </select>
    <select id="monthList" resultType="top.kx.kxss.pay.vo.result.ReconciliationResultVO">
        SELECT DATE_FORMAT(
        IF(TIME(transaction_time) <![CDATA[ < ]]> #{model.settlementTime},
        DATE_SUB(transaction_time, INTERVAL 1 DAY),
        transaction_time
        ),
        '%Y-%m'
        ) AS successDate,
        ROUND(SUM(balance_amount) / 100 ,2) AS amount,
        ROUND((SUM(balance_amount) - SUM(refund_amount)) / 100 , 2) AS totalAmount,
        ROUND(SUM(fee_amount) / 100 ,2) AS mchFeeAmount,
        ROUND(SUM(refund_amount) / 100 ,2) AS refundAmount,
        STR_TO_DATE(
        CONCAT(DATE_FORMAT(
        IF(TIME(transaction_time) <![CDATA[ < ]]> #{model.settlementTime},
        DATE_SUB(transaction_time, INTERVAL 1 DAY),
        transaction_time
        ),
        '%Y-%m-01'),  concat(' ',#{model.settlementTime})
        ),
        '%Y-%m-%d %H:%i:%s'
        )
        AS settlementStartDate,
        DATE_SUB(
        STR_TO_DATE(
        CONCAT(DATE_FORMAT(
        DATE_ADD(
        IF(TIME(transaction_time) <![CDATA[ < ]]> #{model.settlementTime},
        DATE_SUB(transaction_time, INTERVAL 1 DAY),
        transaction_time
        ),
        INTERVAL 1 MONTH
        ),
        '%Y-%m-01'), concat(' ',#{model.settlementTime})),
        '%Y-%m-%d %H:%i:%s'
        ),
        INTERVAL 1 SECOND
        ) AS settlementEndDate
        FROM t_reconciliation_record
        WHERE delete_flag = 0
        <if test="model.mchNoList != null and model.mchNoList.size() > 0">
            and mch_no in
            <foreach collection="model.mchNoList" item="mchNo" separator="," open="(" close=")">
                #{mchNo}
            </foreach>
        </if>
        <if test="model.startTime != null and model.startTime!=''">
            and transaction_time >= #{model.startTime}
        </if>
        <if test="model.endTime != null and model.endTime!=''">
            and transaction_time <![CDATA[ <= ]]> #{model.endTime}
        </if>
        GROUP BY successDate
        ORDER BY successDate DESC
    </select>


</mapper>
