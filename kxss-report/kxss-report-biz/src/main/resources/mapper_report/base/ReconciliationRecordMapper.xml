<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.report.mapper.reconciliation.ReconciliationRecordMapper">
    <!--
        代码生成器 by 2025-06-30 20:18:34
        自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
    -->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.report.entity.reconciliation.ReconciliationRecord">
        <id column="id" property="id"/>
        <result column="reconciliation_date" property="reconciliationDate"/>
        <result column="inst_no" property="instNo"/>
        <result column="mch_no" property="mchNo"/>
        <result column="terminal_no" property="terminalNo"/>
        <result column="transaction_time" property="transactionTime"/>
        <result column="refund_complete_time" property="refundCompleteTime"/>
        <result column="transaction_amount" property="transactionAmount"/>
        <result column="fee_amount" property="feeAmount"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="balance_amount" property="balanceAmount"/>
        <result column="pay_method" property="payMethod"/>
        <result column="payment_type" property="paymentType"/>
        <result column="transaction_state" property="transactionState"/>
        <result column="transaction_order_no" property="transactionOrderNo"/>
        <result column="refund_original_order_no" property="refundOriginalOrderNo"/>
        <result column="terminal_serial_no" property="terminalSerialNo"/>
        <result column="channel_order_no" property="channelOrderNo"/>
        <result column="transaction_date" property="transactionDate"/>
        <result column="user_identifier" property="userIdentifier"/>
        <result column="bank_card_type" property="bankCardType"/>
        <result column="additional_data" property="additionalData"/>
        <result column="merchant_discount_amount" property="merchantDiscountAmount"/>
        <result column="merchant_actual_amount" property="merchantActualAmount"/>
        <result column="user_paid_amount" property="userPaidAmount"/>
        <result column="platform_discount_amount" property="platformDiscountAmount"/>
        <result column="order_remark" property="orderRemark"/>
        <result column="custom_device_no" property="customDeviceNo"/>
        <result column="payment_bank_code" property="paymentBankCode"/>
        <result column="store_name" property="storeName"/>
        <result column="channel_type" property="channelType"/>
        <result column="download_status" property="downloadStatus"/>
        <result column="raw_data" property="rawData"/>
        <result column="created_time" property="createdTime"/>
        <result column="updated_time" property="updatedTime"/>
        <result column="created_by" property="createdBy"/>
        <result column="updated_by" property="updatedBy"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , reconciliation_date, inst_no, mch_no, terminal_no, transaction_time,
        refund_complete_time, transaction_amount, fee_amount, refund_amount, balance_amount, pay_method,
        payment_type, transaction_state, transaction_order_no, refund_original_order_no, terminal_serial_no, channel_order_no,
        transaction_date, user_identifier, bank_card_type, additional_data, merchant_discount_amount, merchant_actual_amount,
        user_paid_amount, platform_discount_amount, order_remark, custom_device_no, payment_bank_code, store_name,
        channel_type, download_status, raw_data, created_time, updated_time, created_by,
        updated_by
    </sql>
    <select id="getDownloadDataList"
            resultType="top.kx.kxss.report.vo.result.reconciliation.StatisticReconciliationDownloadResultVO">
        select
        r.reconciliation_date as reconciliationDate,
        r.mch_no as mchNo,
        r.inst_no as instNo,
        r.terminal_no as terminalNo,
        r.transaction_time as transactionTime,
        r.refund_complete_time as refundCompleteTime,
        ROUND(r.transaction_amount / 100,2) as transactionAmount,
        ROUND(r.fee_amount / 100,2) as feeAmount,
        ROUND(r.refund_amount/ 100,2) as refundAmount,
        ROUND(r.balance_amount/ 100,2) as balanceAmount,
        (case r.pay_method
        when 1 then '微信'
        when 2 then '支付宝'
        when 3 then '银行卡'
        when 4 then '现金'
        when 5 then '无卡支付'
        when 6 then 'qq钱包'
        when 7 then '百度钱包'
        when 8 then '京东钱包'
        when 9 then '京东钱包'
        when 10 then '翼支付'
        when 11 then '云闪付'
        when 12 then '龙支付'
        when 16 then '数字人民币'
        when 17 then '招行支付'
        else
        '其他' end) as payMethod,
        (case r.payment_type
        when 1 then '付款码支付'
        when 2 then '扫码支付'
        when 3 then '公众号支付'
        when 4 then 'wap支付'
        when 5 then 'app支付'
        when 6 then '小程序支付'
        when 7 then '刷脸支付'
        else
        '其他' end) as paymentType,
        (case r.transaction_state
        when 1 then '支付成功'
        when 4 then '已撤销'
        when 5 then '退款成功'
        end) as transactionState,
        r.transaction_order_no as transactionOrderNo,
        r.refund_original_order_no as refundOriginalOrderNo,
        r.terminal_serial_no as terminalSerialNo,
        r.channel_order_no as channelOrderNo,
        r.transaction_date as transactionDate,
        r.user_identifier as userIdentifier,
        (case r.bank_card_type
        when 0 then '储蓄卡'
        when 1 then '信用卡'
        else
        '其他' end) as bankCardType,
        ROUND(r.merchant_discount_amount/ 100,2) as merchantDiscountAmount,
        ROUND(r.merchant_actual_amount/ 100,2) as merchantActualAmount,
        ROUND(r.user_paid_amount/ 100,2) as userPaidAmount,
        ROUND(r.platform_discount_amount/ 100,2) as platformDiscountAmount,
        r.order_remark as orderRemark,
        r.custom_device_no as customDeviceNo,
        r.payment_bank_code as paymentBankCode,
        r.store_name as storeName,
        '不一致' as result
        from t_reconciliation_record r
        where r.delete_flag = 0
        <if test="mchNo != null and mchNo !=''">
            and r.mch_no = #{mchNo}
        </if>
        <if test="instNo != null and instNo !=''">
            and r.inst_no = #{instNo}
        </if>
        <if test="startTime != null and startTime !=''">
            and r.transaction_time >= #{startTime}
        </if>
        <if test="endTime != null and endTime !=''">
            and r.transaction_time <![CDATA[ <= ]]>  #{endTime}
        </if>
    </select>

</mapper>
