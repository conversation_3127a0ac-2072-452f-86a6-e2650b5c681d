<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.report.mapper.AttributeMapper">

    <select id="productPage" resultType="top.kx.kxss.report.vo.ProductAttributeResultVO">
        select IF(d.delete_flag = 1, concat(d.`NAME`, '(已删除)'), d.`NAME`)           AS name,
               GROUP_CONCAT(pcpa.attribute_name)                                       AS attributeName,
               GROUP_CONCAT(pcpa.attribute_setting_name)                               AS attributeSettingName,
               pro.created_time                                                        AS createdTime,
               p.complete_time                                                         AS completeTime,
               (CASE o.type_
                    WHEN 0 THEN '采购入库'
                    WHEN 1 THEN '盘盈入库'
                    WHEN 2 THEN '盘亏出库'
                    WHEN 3 THEN '销售出库'
                    WHEN 4 THEN '其他出库'
                    WHEN 5 THEN '退款入库'
                    WHEN 6 THEN '商品退货'
                    WHEN 7 THEN '采购退货'
                    WHEN 8 THEN '积分兑换'
                    WHEN 9 THEN '调库入库'
                    WHEN 10 THEN '调库出库'
                    WHEN 11 THEN '其他入库'
                    WHEN 12 THEN '采购入库红冲单'
                    WHEN 13 THEN '采购退货红冲单'
                    ELSE '销售出库' END)                                               AS type,
               d.category_id                                                           AS categoryId,
               d.measuring_unit                                                        AS measuringUnit,
               pro.num                                                                 AS num,
               pro.price                                                               AS price,
               ROUND((pro.orgin_price - pro.discount_amount - pro.assessed_amount), 2) AS totalPrice,
               pro.cost_price                                                          AS costPrice,
               (pro.num * IFNULL(pro.cost_price, 0))                                   AS totalCostPrice,
               IFNULL(pro.profit_price, 0)                                             AS profitPrice,
               o.employee_id                                                           AS createdBy,
               p.code                                                                  AS code,
               o.org_id                                                                AS orgId,
               pro.warehouse_id                                                        AS warehouseId
        from pos_cash_product pro
                 INNER JOIN pos_cash p ON p.id = pro.cash_id
                 INNER JOIN pos_cash_product_attribute pcpa
                            ON pro.id = pcpa.cash_product_id and pro.product_id = pcpa.product_id and pcpa.delete_flag = 0
                 LEFT JOIN base_outin o ON o.`code` = p.`code` and o.delete_flag = 0 AND o.state = 1
                 LEFT JOIN base_product d ON d.id = pro.product_id
            ${ew.customSqlSegment}
        GROUP BY pro.id
        ORDER BY pro.id DESC
    </select>

    <select id="productSum"  resultType="top.kx.kxss.report.vo.ProductAttributeResultVO">
        SELECT sum(t.num)            as num,
               sum(t.price)          as price,
               sum(t.totalPrice)     as totalPrice,
               sum(t.costPrice)      as costPrice,
               sum(t.totalCostPrice) as totalCostPrice,
               sum(t.profitPrice)    as profitPrice
        FROM (select max(pro.num)                                                                 AS num,
                     max(pro.price)                                                               AS price,
                     max(ROUND((pro.orgin_price - pro.discount_amount - pro.assessed_amount), 2)) AS totalPrice,
                     max(pro.cost_price)                                                          AS costPrice,
                     max(pro.num * IFNULL(pro.cost_price, 0))                                     AS totalCostPrice,
                     max(IFNULL(pro.profit_price, 0))                                             AS profitPrice
              from pos_cash_product pro
                       INNER JOIN pos_cash p ON p.id = pro.cash_id
                       INNER JOIN pos_cash_product_attribute pcpa
                                  ON pro.id = pcpa.cash_product_id and pro.product_id = pcpa.product_id and pcpa.delete_flag = 0
                       LEFT JOIN base_outin o ON o.`code` = p.`code` and o.delete_flag = 0 AND o.state = 1
                       LEFT JOIN base_product d ON d.id = pro.product_id
                  ${ew.customSqlSegment}
              GROUP BY pro.id) t
    </select>

    <select id="productList"  resultType="top.kx.kxss.report.vo.ProductAttributeResultVO">
        select IF(d.delete_flag = 1, concat(d.`NAME`, '(已删除)'), d.`NAME`)           AS name,
               GROUP_CONCAT(pcpa.attribute_name)                                       AS attributeName,
               GROUP_CONCAT(pcpa.attribute_setting_name)                               AS attributeSettingName,
               pro.created_time                                                        AS createdTime,
               p.complete_time                                                         AS completeTime,
               (CASE o.type_
                    WHEN 0 THEN '采购入库'
                    WHEN 1 THEN '盘盈入库'
                    WHEN 2 THEN '盘亏出库'
                    WHEN 3 THEN '销售出库'
                    WHEN 4 THEN '其他出库'
                    WHEN 5 THEN '退款入库'
                    WHEN 6 THEN '商品退货'
                    WHEN 7 THEN '采购退货'
                    WHEN 8 THEN '积分兑换'
                    WHEN 9 THEN '调库入库'
                    WHEN 10 THEN '调库出库'
                    WHEN 11 THEN '其他入库'
                    WHEN 12 THEN '采购入库红冲单'
                    WHEN 13 THEN '采购退货红冲单'
                    ELSE '销售出库' END)                                               AS type,
               d.category_id                                                           AS categoryId,
               d.measuring_unit                                                        AS measuringUnit,
               pro.num                                                                 AS num,
               pro.price                                                               AS price,
               ROUND((pro.orgin_price - pro.discount_amount - pro.assessed_amount), 2) AS totalPrice,
               pro.cost_price                                                          AS costPrice,
               (pro.num * IFNULL(pro.cost_price, 0))                                   AS totalCostPrice,
               IFNULL(pro.profit_price, 0)                                             AS profitPrice,
               o.employee_id                                                           AS createdBy,
               p.code                                                                  AS code,
               o.org_id                                                                AS orgId,
               pro.warehouse_id                                                        AS warehouseId
        from pos_cash_product pro
                 INNER JOIN pos_cash p ON p.id = pro.cash_id
                 INNER JOIN pos_cash_product_attribute pcpa
                            ON pro.id = pcpa.cash_product_id and pro.product_id = pcpa.product_id and pcpa.delete_flag = 0
                 LEFT JOIN base_outin o ON o.`code` = p.`code` and o.delete_flag = 0 AND o.state = 1
                 LEFT JOIN base_product d ON d.id = pro.product_id
            ${ew.customSqlSegment}
        GROUP BY pro.id
        ORDER BY pro.id DESC
    </select>

</mapper>
