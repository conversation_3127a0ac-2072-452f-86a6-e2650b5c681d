<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.report.mapper.PosCashMapper">

    <select id="selectOneAmount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT IFNULL(COUNT(id), 0)                                                            num,
               IFNULL(SUM(IFNULL(amount, 0)), 0)                                               amount,
               (IFNULL(SUM(IFNULL(payment, 0)), 0) - IFNULL(SUM(IFNULL(refund_amount, 0)), 0)) payment,
               IFNULL(SUM(IFNULL(discount_amount, 0)), 0)                                      discountAmount,
               IFNULL(SUM(IFNULL(refund_amount, 0)), 0)                                        refundAmount,
               IFNULL(SUM(IFNULL(gift_amount, 0)), 0)                                          giftAmount,
               IFNULL(SUM(IFNULL(paid, 0)), 0)                                                 paid,
               IFNULL(SUM(IFNULL(unpaid, 0)), 0)                                               unpaid,
               IFNULL(SUM(CASE
                              WHEN p.type_ = '3' THEN
                                  IFNULL(p.payment, 0)
                              ELSE 0
                   END), 0)                                                                        rechargeAmount
        FROM pos_cash p
            ${ew.customSqlSegment}
    </select>

    <select id="selectByPayType" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT (IFNULL(SUM(IFNULL(t.amount, 0)), 0)
            - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)
            - IFNULL(SUM(IFNULL(t.change_amount, 0)), 0))                                   amount,
               (IFNULL(SUM(IFNULL(t.amount, 0)), 0)
                   - SUM(if(t.is_prepaid is not null and t.is_prepaid = 1, IFNULL(t.refund_amount, 0),
                            0))
                   - IFNULL(SUM(IFNULL(t.change_amount, 0)), 0))                            payment,
               SUM(ROUND(IF(bpt.fee_rate is null, 0,
                            (IFNULL(t.amount, 0) - ifnull(t.refund_amount, 0) - IFNULL(t.change_amount, 0)) *
                            bpt.fee_rate /
                            100), 2))                                                       feePayment,
               t.pay_type_id   AS                                                           field,
               max(t.pay_name) as                                                           name,
               IFNULL(SUM(IF(IFNULL(t.refund_amount, 0) > 0 and IFNULL(t.refund_amount, 0) > IFNULL(t.gift_amount, 0),
                             0, IFNULL(t.gift_amount, 0) - IFNULL(t.refund_amount, 0))), 0) giftAmount,
               IFNULL(SUM(IF(IFNULL(t.refund_amount, 0) > 0 and IFNULL(t.refund_amount, 0) > IFNULL(t.gift_amount, 0),
                             IFNULL(t.recharge_amount, 0) - (IFNULL(t.refund_amount, 0) - IFNULL(t.gift_amount, 0)),
                             IFNULL(t.recharge_amount, 0))), 0)
                                                                                            rechargeAmount,
               IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)                                   refundAmount,
               IFNULL(count(t.pay_type_id), 0)                                              num,
               IFNULL(SUM(IFNULL(t.change_amount, 0)), 0)                                   changeAmount
        FROM pos_cash_payment t
                 JOIN pos_cash p ON p.id = t.cash_id
                 LEFT JOIN base_payment_type bpt on t.pay_type_id = bpt.id
            ${ew.customSqlSegment}
        GROUP BY
            t.pay_type_id

    </select>

    <select id="selectPageResultVO" resultType="top.kx.kxss.app.vo.result.cash.PosCashResultVO">
        SELECT DISTINCT p.id                           as id,
                        p.type_                        as type,
                        t.`name`                       as tableName,
                        p.`code`                       as `code`,
                        p.`bill_date`                  as billDate,
                        p.`bill_state`                 as billState,
                        p.`bill_type`                  as billType,
                        p.`org_id`                     as orgId,
                        p.`employee_id`                as employeeId,
                        p.`created_emp`                as createdEmp,
                        p.`member_id`                  as memberId,
                        p.`table_id`                   as tableId,
                        p.`order_source`               as orderSource,
                        IFNULL(p.`amount`, 0)          as amount,
                        IFNULL(p.`discount_amount`, 0) as discountAmount,
                        IFNULL(p.`payment`, 0)         as payment,
                        IFNULL(p.`paid`, 0)            as paid,
                        IFNULL(p.`unpaid`, 0)          as unpaid,
                        IFNULL(p.`round_amount`, 0)    as roundAmount,
                        IFNULL(p.`refund_amount`, 0)   as refundAmount,
                        IFNULL(p.`product_amount`, 0)  as productAmount,
                        IFNULL(p.`service_amount`, 0)  as serviceAmount,
                        IFNULL(p.`thail_amount`, 0)    as thailAmount,
                        IFNULL(p.`table_amount`, 0)    as tableAmount,
                        IFNULL(p.`buy_card_amount`, 0) as buyCardAmount,
                        p.`remarks`                    as remarks,
                        p.`created_org_id`             as createdOrgId,
                        p.`source_id`                  as sourceId,
                        p.`chargeback_id`              as chargebackId,
                        t.`table_type`                 as tableType,
                        t.`table_area`                 as tableArea,
                        p.`created_time`               as createdTime,
                        p.`complete_time`              as completeTime
        FROM pos_cash p
                 LEFT JOIN member_info m ON p.member_id = m.id
                 LEFT JOIN pos_cash_commenter c ON c.cash_id = p.id
                 LEFT JOIN base_table_info t ON t.id = p.table_id
            ${ew.customSqlSegment}
    </select>

    <select id="findAllResultVO" resultType="top.kx.kxss.app.vo.result.cash.PosCashResultVO">
        SELECT DISTINCT p.id                           as id,
                        p.type_                        as type,
                        t.`name`                       as tableName,
                        p.`code`                       as `code`,
                        p.`bill_date`                  as billDate,
                        p.`bill_state`                 as billState,
                        p.`bill_type`                  as billType,
                        p.`org_id`                     as orgId,
                        p.`employee_id`                as employeeId,
                        p.`created_emp`                as createdEmp,
                        p.`member_id`                  as memberId,
                        p.`table_id`                   as tableId,
                        p.`order_source`               as orderSource,
                        IFNULL(p.`amount`, 0)          as amount,
                        IFNULL(p.`discount_amount`, 0) as discountAmount,
                        IFNULL(p.`payment`, 0)         as payment,
                        IFNULL(p.`paid`, 0)            as paid,
                        IFNULL(p.`unpaid`, 0)          as unpaid,
                        IFNULL(p.`round_amount`, 0)    as roundAmount,
                        IFNULL(p.`refund_amount`, 0)   as refundAmount,
                        IFNULL(p.`product_amount`, 0)  as productAmount,
                        IFNULL(p.`service_amount`, 0)  as serviceAmount,
                        IFNULL(p.`thail_amount`, 0)    as thailAmount,
                        IFNULL(p.`table_amount`, 0)    as tableAmount,
                        IFNULL(p.`buy_card_amount`, 0) as buyCardAmount,
                        p.`remarks`                    as remarks,
                        p.`created_org_id`             as createdOrgId,
                        p.`source_id`                  as sourceId,
                        p.`chargeback_id`              as chargebackId,
                        t.`table_type`                 as tableType,
                        t.`table_area`                 as tableArea,
                        p.`created_time`               as createdTime,
                        p.`complete_time`              as completeTime
        FROM pos_cash p
                 LEFT JOIN member_info m ON p.member_id = m.id
                 LEFT JOIN pos_cash_commenter c ON c.cash_id = p.id
                 LEFT JOIN base_table_info t ON t.id = p.table_id
            ${ew.customSqlSegment}
    </select>

    <select id="findSumResultVO" resultType="top.kx.kxss.report.vo.PosCashNoPayDetailsResultVO">
        select sum(p.table_amount) as productAmount,
               sum(p.service_amount) as serviceAmount,
               sum(p.thail_amount) as thailAmount,
               sum(p.amount)   as amount,
               sum(p.discount_amount) as discountAmount,
               sum(p.refund_amount) as refundAmount,
               sum(p.paid)          as paid
        from pos_cash p
                 ${ew.customSqlSegment}
    </select>

    <select id="cashCommenterListByCashIds" resultType="top.kx.kxss.app.entity.cash.PosCashCommenter">
        select id,
               type_,
               source_id,
               cash_id,
               employee_id,
               created_time,
               created_by,
               updated_time,
               updated_by,
               created_org_id,
               delete_flag
        from pos_cash_commenter
        where delete_flag = 0 and type_ = '4002' and cash_id in
        <foreach collection="cashIds" item="cashId" open="(" separator="," close=")">
            #{cashId}
        </foreach>
    </select>

    <select id="cashTableListByCashIds" resultType="top.kx.kxss.app.entity.cash.table.PosCashTable">
        select id,
               cash_id,
               table_id,
               charging_setting_id,
               duration,
               start_time,
               end_time,
               orgin_price,
               old_orgin_price,
               price,
               old_price,
               amount,
               remind_time,
               type,
               discount,
               remarks,
               status,
               created_time,
               created_by,
               updated_time,
               updated_by,
               created_org_id,
               delete_flag,
               discount_amount,
               discount_type,
               cycle,
               coupon_id,
               table_name,
               discount_remarks,
               deduct_duration,
               is_merge,
               is_turn,
               cash_thail_id,
               bcharging_setting_grade_id,
               charging_setting_grade_id,
               assessed_amount,
               paid,
               over_type,
               calc_type,
               calc_duration,
               calc_price,
               calc_amount,
               is_discount,
               is_account,
               free_amount,
               free_duration,
               merge_cash_id,
               is_split,
               split_cash_id,
               billing_mode,
               card_deduct_amount,
               member_card_id,
               stored_card_id,
               is_pack_field,
               discount_template_id,
               discount_desc,
               reform_price_type,
               reform_price,
               service_activity_id,
               service_activity_name,
               service_activity_discount_value,
               sn,
               is_modify_duration,
               refund_amount,
               billing_level,
               thail_assessed_proportion,
               thail_assessed_amount,
               thail_detail_id
        from pos_cash_table
        where delete_flag = 0  and cash_id in
        <foreach collection="cashIds" item="cashId" open="(" separator="," close=")">
            #{cashId}
        </foreach>
    </select>


    <select id="cashServiceListByCashIds" resultType="top.kx.kxss.app.entity.cash.service.PosCashService">
        select id,
               cash_id,
               service_id,
               status,
               employee_id,
               start_time,
               end_time,
               duration,
               orgin_price,
               price,
               amount,
               type,
               discount,
               remarks,
               remind_time,
               created_time,
               created_by,
               updated_time,
               updated_by,
               created_org_id,
               delete_flag,
               discount_amount,
               discount_type,
               coupon_id,
               cycle,
               employee_name,
               discount_remarks,
               timing_duration,
               deduct_duration,
               num,
               is_merge,
               is_turn,
               last_service_time,
               cash_thail_id,
               assessed_amount,
               paid,
               clock_type,
               is_dscount,
               is_discount,
               merge_cash_id,
               is_split,
               split_cash_id,
               cycle_num,
               is_account,
               profit_price,
               cost_price,
               card_deduct_amount,
               member_card_id,
               stored_card_id,
               discount_template_id,
               discount_desc,
               reform_price_type,
               reform_price,
               old_price,
               old_orgin_price,
               service_activity_id,
               service_activity_name,
               sn,
               refund_amount,
               thail_assessed_proportion,
               thail_assessed_amount,
               thail_detail_id
        from pos_cash_service
        where delete_flag = 0  and cash_id in
        <foreach collection="cashIds" item="cashId" open="(" separator="," close=")">
            #{cashId}
        </foreach>
    </select>

    <select id="cashProductListByCashIds" resultType="top.kx.kxss.app.entity.cash.product.PosCashProduct">
        select id,
        cash_id,
        hh,
        product_id,
        product_name,
        num,
        price,
        orgin_price,
        amount,
        discount_amount,
        remarks,
        is_gift,
        type,
        discount,
        created_time,
        created_by,
        updated_time,
        updated_by,
        created_org_id,
        delete_flag,
        discount_type,
        coupon_id,
        discount_remarks,
        deduct_num,
        is_turn,
        is_merge,
        cash_thail_id,
        is_discount,
        assessed_amount,
        paid,
        merge_cash_id,
        is_split,
        split_cash_id,
        is_account,
        return_num,
        profit_price,
        cost_price,
        card_deduct_amount,
        member_card_id,
        stored_card_id,
        discount_template_id,
        discount_desc,
        reform_price_type,
        reform_price,
        old_price,
        old_orgin_price,
        sn,
        warehouse_id,
        refund_amount,
        attribute_setting,
        attribute_setting_desc,
        attribute_price,
        attribute_amount,
        thail_assessed_proportion,
        thail_assessed_amount,
        thail_detail_id,
        refund_num
        from pos_cash_product
        where delete_flag = 0 and cash_id in
        <foreach collection="cashIds" item="cashId" open="(" separator="," close=")">
            #{cashId}
        </foreach>
    </select>


    <select id="cashThailListByCashIds" resultType="top.kx.kxss.app.entity.thail.PosCashThail">
        select id, cash_id, thail_id, duration, start_time, end_time, orgin_price, price, amount, remind_time, discount,
        remarks, status, created_time, created_by, updated_time, updated_by, created_org_id, delete_flag,
        discount_amount, discount_type, thail_name, discount_remarks, is_merge, is_turn, timing_duration,
        is_check_securities, securities_number, assessed_amount, paid, cash_thail_id, merge_cash_id, is_split,
        split_cash_id, profit_price, cost_price, stored_card_id, discount_desc, reform_price_type, reform_price,
        old_price, old_orgin_price, sn, is_account, is_discount, refund_amount, assessed_type, thail_assessed_type,
        paid_in, group_buy_amount, platform_amount, merchant_amount, group_buy_type, tags
        from pos_cash_thail
        where delete_flag = 0 and cash_id in
        <foreach collection="cashIds" item="cashId" open="(" separator="," close=")">
            #{cashId}
        </foreach>
    </select>

    <select id="thailPage" resultType="top.kx.kxss.pos.vo.order.OrderResultVO">
        select p.id as id,
               p.type_ as type,
               p.code as code,
               p.created_time as createdTime,
               p.complete_time as completeTime,
               p.table_id as tableId,
               p.table_name as tableName,
               p.order_source as orderSource,
               p.bill_state as billState,
               p.bill_type as billType,
               p.member_id as memberId,
               p.amount as amount,
               p.payment as payment,
               p.paid as paid,
               p.unpaid as unpaid,
               p.refund_amount as refundAmount,
               p.discount_amount as discountAmount,
               p.created_emp as createdEmp
        FROM pos_cash_thail t
                 inner join pos_cash p on p.id = t.cash_id
            ${ew.customSqlSegment}
    </select>

</mapper>
