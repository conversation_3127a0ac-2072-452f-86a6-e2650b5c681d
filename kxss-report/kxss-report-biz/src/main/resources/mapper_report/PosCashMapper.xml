<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.report.mapper.PosCashMapper">

    <select id="selectOneAmount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT IFNULL(COUNT(id), 0)                                                            num,
               IFNULL(SUM(IFNULL(amount, 0)), 0)                                               amount,
               (IFNULL(SUM(IFNULL(payment, 0)), 0) - IFNULL(SUM(IFNULL(refund_amount, 0)), 0)) payment,
               IFNULL(SUM(IFNULL(discount_amount, 0)), 0)                                      discountAmount,
               IFNULL(SUM(IFNULL(refund_amount, 0)), 0)                                        refundAmount,
               IFNULL(SUM(IFNULL(gift_amount, 0)), 0)                                          giftAmount,
               IFNULL(SUM(IFNULL(paid, 0)), 0)                                                 paid,
               IFNULL(SUM(IFNULL(unpaid, 0)), 0)                                               unpaid,
               IFNULL(SUM(CASE
                              WHEN p.type_ = '3' THEN
                                  IFNULL(p.payment, 0)
                              ELSE 0
                   END), 0)                                                                        rechargeAmount
        FROM pos_cash p
            ${ew.customSqlSegment}
    </select>

    <select id="selectByPayType" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT (IFNULL(SUM(IFNULL(t.amount, 0)), 0)
            - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)
            - IFNULL(SUM(IFNULL(t.change_amount, 0)), 0))                                   amount,
               (IFNULL(SUM(IFNULL(t.amount, 0)), 0)
                   - SUM(if(t.is_prepaid is not null and t.is_prepaid = 1, IFNULL(t.refund_amount, 0),
                            0))
                   - IFNULL(SUM(IFNULL(t.change_amount, 0)), 0))                            payment,
               SUM(ROUND(IF(bpt.fee_rate is null, 0,
                            (IFNULL(t.amount, 0) - ifnull(t.refund_amount, 0) - IFNULL(t.change_amount, 0)) *
                            bpt.fee_rate /
                            100), 2))                                                       feePayment,
               t.pay_type_id   AS                                                           field,
               max(t.pay_name) as                                                           name,
               IFNULL(SUM(IF(IFNULL(t.refund_amount, 0) > 0 and IFNULL(t.refund_amount, 0) > IFNULL(t.gift_amount, 0),
                             0, IFNULL(t.gift_amount, 0) - IFNULL(t.refund_amount, 0))), 0) giftAmount,
               IFNULL(SUM(IF(IFNULL(t.refund_amount, 0) > 0 and IFNULL(t.refund_amount, 0) > IFNULL(t.gift_amount, 0),
                             IFNULL(t.recharge_amount, 0) - (IFNULL(t.refund_amount, 0) - IFNULL(t.gift_amount, 0)),
                             IFNULL(t.recharge_amount, 0))), 0)
                                                                                            rechargeAmount,
               IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)                                   refundAmount,
               IFNULL(count(t.pay_type_id), 0)                                              num,
               IFNULL(SUM(IFNULL(t.change_amount, 0)), 0)                                   changeAmount
        FROM pos_cash_payment t
                 JOIN pos_cash p ON p.id = t.cash_id
                 LEFT JOIN base_payment_type bpt on t.pay_type_id = bpt.id
            ${ew.customSqlSegment}
        GROUP BY
            t.pay_type_id

    </select>

    <select id="selectPageResultVO" resultType="top.kx.kxss.app.vo.result.cash.PosCashResultVO">
        SELECT DISTINCT p.id                           as id,
                        p.type_                        as type,
                        t.`name`                       as tableName,
                        p.`code`                       as `code`,
                        p.`bill_date`                  as billDate,
                        p.`bill_state`                 as billState,
                        p.`bill_type`                  as billType,
                        p.`org_id`                     as orgId,
                        p.`employee_id`                as employeeId,
                        p.`created_emp`                as createdEmp,
                        p.`member_id`                  as memberId,
                        p.`table_id`                   as tableId,
                        p.`order_source`               as orderSource,
                        IFNULL(p.`amount`, 0)          as amount,
                        IFNULL(p.`discount_amount`, 0) as discountAmount,
                        IFNULL(p.`payment`, 0)         as payment,
                        IFNULL(p.`paid`, 0)            as paid,
                        IFNULL(p.`unpaid`, 0)          as unpaid,
                        IFNULL(p.`round_amount`, 0)    as roundAmount,
                        IFNULL(p.`refund_amount`, 0)   as refundAmount,
                        IFNULL(p.`product_amount`, 0)  as productAmount,
                        IFNULL(p.`service_amount`, 0)  as serviceAmount,
                        IFNULL(p.`thail_amount`, 0)    as thailAmount,
                        IFNULL(p.`power_amount`, 0)    as powerAmount,
                        IFNULL(p.`table_amount`, 0)    as tableAmount,
                        IFNULL(p.`buy_card_amount`, 0) as buyCardAmount,
                        p.`remarks`                    as remarks,
                        p.`created_org_id`             as createdOrgId,
                        p.`source_id`                  as sourceId,
                        p.`chargeback_id`              as chargebackId,
                        t.`table_type`                 as tableType,
                        t.`table_area`                 as tableArea,
                        p.`created_time`               as createdTime,
                        p.`complete_time`              as completeTime,
                        p.`registration_time`              as registrationTime
        FROM pos_cash p
                 LEFT JOIN member_info m ON p.member_id = m.id
                 LEFT JOIN pos_cash_commenter c ON c.cash_id = p.id
                 LEFT JOIN base_table_info t ON t.id = p.table_id
            ${ew.customSqlSegment}
    </select>

    <select id="findAllResultVO" resultType="top.kx.kxss.app.vo.result.cash.PosCashResultVO">
        SELECT DISTINCT p.id                           as id,
                        p.type_                        as type,
                        t.`name`                       as tableName,
                        p.`code`                       as `code`,
                        p.`bill_date`                  as billDate,
                        p.`bill_state`                 as billState,
                        p.`bill_type`                  as billType,
                        p.`org_id`                     as orgId,
                        p.`employee_id`                as employeeId,
                        p.`created_emp`                as createdEmp,
                        p.`member_id`                  as memberId,
                        p.`table_id`                   as tableId,
                        p.`order_source`               as orderSource,
                        IFNULL(p.`amount`, 0)          as amount,
                        IFNULL(p.`discount_amount`, 0) as discountAmount,
                        IFNULL(p.`payment`, 0)         as payment,
                        IFNULL(p.`paid`, 0)            as paid,
                        IFNULL(p.`unpaid`, 0)          as unpaid,
                        IFNULL(p.`round_amount`, 0)    as roundAmount,
                        IFNULL(p.`refund_amount`, 0)   as refundAmount,
                        IFNULL(p.`product_amount`, 0)  as productAmount,
                        IFNULL(p.`service_amount`, 0)  as serviceAmount,
                        IFNULL(p.`thail_amount`, 0)    as thailAmount,
                        IFNULL(p.`table_amount`, 0)    as tableAmount,
                        IFNULL(p.`buy_card_amount`, 0) as buyCardAmount,
                        p.`remarks`                    as remarks,
                        p.`created_org_id`             as createdOrgId,
                        p.`source_id`                  as sourceId,
                        p.`chargeback_id`              as chargebackId,
                        t.`table_type`                 as tableType,
                        t.`table_area`                 as tableArea,
                        p.`created_time`               as createdTime,
                        p.`complete_time`              as completeTime,
                        p.`registration_time`              as registrationTime
        FROM pos_cash p
                 LEFT JOIN member_info m ON p.member_id = m.id
                 LEFT JOIN pos_cash_commenter c ON c.cash_id = p.id
                 LEFT JOIN base_table_info t ON t.id = p.table_id
            ${ew.customSqlSegment}
    </select>

    <select id="findSumResultVO" resultType="top.kx.kxss.report.vo.PosCashNoPayDetailsResultVO">
        select sum(p.table_amount) as productAmount,
               sum(p.service_amount) as serviceAmount,
               sum(p.thail_amount) as thailAmount,
               sum(p.amount)   as amount,
               sum(p.discount_amount) as discountAmount,
               sum(p.refund_amount) as refundAmount,
               sum(p.paid)          as paid
        from pos_cash p
                 ${ew.customSqlSegment}
    </select>

    <select id="cashCommenterListByCashIds" resultType="top.kx.kxss.app.entity.cash.PosCashCommenter">
        select id,
               type_,
               source_id,
               cash_id,
               employee_id,
               created_time,
               created_by,
               updated_time,
               updated_by,
               created_org_id,
               delete_flag
        from pos_cash_commenter
        where delete_flag = 0 and type_ = '4002' and cash_id in
        <foreach collection="cashIds" item="cashId" open="(" separator="," close=")">
            #{cashId}
        </foreach>
    </select>

    <select id="cashTableListByCashIds" resultType="top.kx.kxss.app.entity.cash.table.PosCashTable">
        select id,
               cash_id,
               table_id,
               charging_setting_id,
               duration,
               start_time,
               end_time,
               orgin_price,
               old_orgin_price,
               price,
               old_price,
               amount,
               remind_time,
               type,
               discount,
               remarks,
               status,
               created_time,
               created_by,
               updated_time,
               updated_by,
               created_org_id,
               delete_flag,
               discount_amount,
               discount_type,
               cycle,
               coupon_id,
               table_name,
               discount_remarks,
               deduct_duration,
               is_merge,
               is_turn,
               cash_thail_id,
               bcharging_setting_grade_id,
               charging_setting_grade_id,
               assessed_amount,
               paid,
               over_type,
               calc_type,
               calc_duration,
               calc_price,
               calc_amount,
               is_discount,
               is_account,
               free_amount,
               free_duration,
               merge_cash_id,
               is_split,
               split_cash_id,
               billing_mode,
               card_deduct_amount,
               member_card_id,
               stored_card_id,
               is_pack_field,
               discount_template_id,
               discount_desc,
               reform_price_type,
               reform_price,
               service_activity_id,
               service_activity_name,
               service_activity_discount_value,
               sn,
               is_modify_duration,
               refund_amount,
               billing_level,
               thail_assessed_proportion,
               thail_assessed_amount,
               thail_detail_id
        from pos_cash_table
        where delete_flag = 0  and cash_id in
        <foreach collection="cashIds" item="cashId" open="(" separator="," close=")">
            #{cashId}
        </foreach>
    </select>


    <select id="cashServiceListByCashIds" resultType="top.kx.kxss.app.entity.cash.service.PosCashService">
        select id,
               cash_id,
               service_id,
               status,
               employee_id,
               start_time,
               end_time,
               duration,
               orgin_price,
               price,
               amount,
               type,
               discount,
               remarks,
               remind_time,
               created_time,
               created_by,
               updated_time,
               updated_by,
               created_org_id,
               delete_flag,
               discount_amount,
               discount_type,
               coupon_id,
               cycle,
               employee_name,
               discount_remarks,
               timing_duration,
               deduct_duration,
               num,
               is_merge,
               is_turn,
               last_service_time,
               cash_thail_id,
               assessed_amount,
               paid,
               clock_type,
               is_dscount,
               is_discount,
               merge_cash_id,
               is_split,
               split_cash_id,
               cycle_num,
               is_account,
               profit_price,
               cost_price,
               card_deduct_amount,
               member_card_id,
               stored_card_id,
               discount_template_id,
               discount_desc,
               reform_price_type,
               reform_price,
               old_price,
               old_orgin_price,
               service_activity_id,
               service_activity_name,
               sn,
               refund_amount,
               thail_assessed_proportion,
               thail_assessed_amount,
               thail_detail_id
        from pos_cash_service
        where delete_flag = 0  and cash_id in
        <foreach collection="cashIds" item="cashId" open="(" separator="," close=")">
            #{cashId}
        </foreach>
    </select>

    <select id="cashProductListByCashIds" resultType="top.kx.kxss.app.entity.cash.product.PosCashProduct">
        select id,
        cash_id,
        hh,
        product_id,
        product_name,
        num,
        price,
        orgin_price,
        amount,
        discount_amount,
        remarks,
        is_gift,
        type,
        discount,
        created_time,
        created_by,
        updated_time,
        updated_by,
        created_org_id,
        delete_flag,
        discount_type,
        coupon_id,
        discount_remarks,
        deduct_num,
        is_turn,
        is_merge,
        cash_thail_id,
        is_discount,
        assessed_amount,
        paid,
        merge_cash_id,
        is_split,
        split_cash_id,
        is_account,
        return_num,
        profit_price,
        cost_price,
        card_deduct_amount,
        member_card_id,
        stored_card_id,
        discount_template_id,
        discount_desc,
        reform_price_type,
        reform_price,
        old_price,
        old_orgin_price,
        sn,
        warehouse_id,
        refund_amount,
        attribute_setting,
        attribute_setting_desc,
        attribute_price,
        attribute_amount,
        thail_assessed_proportion,
        thail_assessed_amount,
        thail_detail_id,
        refund_num
        from pos_cash_product
        where delete_flag = 0 and cash_id in
        <foreach collection="cashIds" item="cashId" open="(" separator="," close=")">
            #{cashId}
        </foreach>
    </select>


    <select id="cashThailListByCashIds" resultType="top.kx.kxss.app.entity.thail.PosCashThail">
        select id, cash_id, thail_id, duration, start_time, end_time, orgin_price, price, amount, remind_time, discount,
        remarks, status, created_time, created_by, updated_time, updated_by, created_org_id, delete_flag,
        discount_amount, discount_type, thail_name, discount_remarks, is_merge, is_turn, timing_duration,
        is_check_securities, securities_number, assessed_amount, paid, cash_thail_id, merge_cash_id, is_split,
        split_cash_id, profit_price, cost_price, stored_card_id, discount_desc, reform_price_type, reform_price,
        old_price, old_orgin_price, sn, is_account, is_discount, refund_amount, assessed_type, thail_assessed_type,
        paid_in, group_buy_amount, platform_amount, merchant_amount, group_buy_type, tags
        from pos_cash_thail
        where delete_flag = 0 and cash_id in
        <foreach collection="cashIds" item="cashId" open="(" separator="," close=")">
            #{cashId}
        </foreach>
    </select>

    <select id="thailPage" resultType="top.kx.kxss.pos.vo.order.OrderResultVO">
        select p.id as id,
               p.type_ as type,
               p.code as code,
               p.created_time as createdTime,
               p.complete_time as completeTime,
               p.table_id as tableId,
               p.table_name as tableName,
               p.order_source as orderSource,
               p.bill_state as billState,
               p.bill_type as billType,
               p.member_id as memberId,
               p.amount as amount,
               p.payment as payment,
               p.paid as paid,
               p.unpaid as unpaid,
               p.refund_amount as refundAmount,
               p.discount_amount as discountAmount,
               p.created_emp as createdEmp
        FROM pos_cash_thail t
                 inner join pos_cash p on p.id = t.cash_id
            ${ew.customSqlSegment}
    </select>
    <select id="freePage" resultType="top.kx.kxss.report.vo.result.cash.OrderFreeResultVO">
        SELECT *
        FROM (
                 SELECT
                     p.id AS cashId,
                     p.code AS code,
                     t.id AS itemId,
                     'TABLE' AS type,
                     '台费' AS typeDesc,
                     t.table_name AS name,
                     t.duration AS num,
                     '分钟' AS measuringUnit,
                     t.amount AS amount,
                     IFNULL(t.discount_amount, 0) + ROUND(IFNULL(t.assessed_amount, 0)) AS discountAmount,
                     0.00 AS payment,
                     t.created_time AS createdTime,
                     p.complete_time AS completeTime,
                     p.complete_emp AS completeEmp,
                     t.review_status AS reviewStatus,
                     case
                         when t.review_status = 0 then '未审核'
                         when t.review_status = 1 then '已通过'
                         when t.review_status = 2 then '已拒绝'
                         else '-' end                                                   AS reviewStatusDesc,
                     t.review_remark                                                      as reviewRemark,
                     t.review_emp                                                       as reviewEmp,
                     t.review_time                                                      as reviewTime,
                     p.remarks AS cashRemark,
                     t.remarks AS itemRemark
                 FROM pos_cash_table t
                          JOIN pos_cash p ON p.id = t.cash_id
                     ${ew.customSqlSegment}

                 UNION ALL

                 SELECT
                    p.id                         as cashId,
                    p.code                       as code,
                    t.id                         as itemId,
                     'SERVICE'                   as type,
                    '服务'                       as typeDesc,
                    t.employee_name              as name,
                    IFNULL(
                    CASE
                    WHEN t.cycle IS NULL
                    OR t.cycle = '' THEN
                    0
                    WHEN instr(t.cycle, '元/') <![CDATA[ <= ]]> 0 THEN 0
                    WHEN instr(t.cycle, '元/小时') > 0 THEN
                    (IFNULL(t.cycle_num, 0) * 60)
                    WHEN instr(t.cycle, '元/小时') <![CDATA[ <= ]]> 0 AND instr(t.cycle, '小时') > 0 THEN
                    (
                    IFNULL(t.cycle_num, 0) * 60 * SUBSTRING_INDEX(SUBSTRING_INDEX(t.cycle, '/', -1), '小时', 1))
                    WHEN instr(t.cycle, '元/分钟') > 0 THEN
                    IFNULL(t.cycle_num, 0)
                    WHEN instr(t.cycle, '元/分钟') <![CDATA[ <= ]]> 0 AND instr(t.cycle, '分钟') > 0 THEN
                    (
                    IFNULL(t.cycle_num, 0) * SUBSTRING_INDEX(SUBSTRING_INDEX(t.cycle, '/', -1), '分钟', 1))
                    ELSE 0
                    END,
                    0
                    )                            as num,
                    '分钟'                       as measuringUnit,
                     t.amount AS amount,
                     IFNULL(t.discount_amount, 0)  + ROUND(IFNULL(t.assessed_amount, 0)) AS discountAmount,
                     0.00 AS payment,
                    t.created_time               as createdTime,
                     p.complete_time AS completeTime,
                     p.complete_emp AS completeEmp,
                     t.review_status AS reviewStatus,
                     case
                         when t.review_status = 0 then '未审核'
                         when t.review_status = 1 then '已通过'
                         when t.review_status = 2 then '已拒绝'
                         else '-' end                                                   AS reviewStatusDesc,
                     t.review_remark                                                      as reviewRemark,
                     t.review_emp                                                       as reviewEmp,
                     t.review_time                                                      as reviewTime,
                     p.remarks AS cashRemark,
                     t.remarks AS itemRemark
                     FROM pos_cash_service t
                        JOIN pos_cash p ON p.id = t.cash_id
                     ${ew.customSqlSegment}

                 UNION ALL

                 SELECT
                    p.id                            as cashId,
                    p.code                          as code,
                    t.id                            as itemId,
                     'PRODUCT'                     as type,
                    '商品'                          as typeDesc,
                    t.product_name                  as name,
                    t.num - IFNULL(t.refund_num, 0) as num,
                    bd.name                         as measuringUnit,
                     t.amount AS amount,
                     IFNULL(t.discount_amount, 0) + ROUND(IFNULL(t.assessed_amount, 0)) AS discountAmount,
                     0.00 AS payment,
                    t.created_time                  as createdTime,
                     p.complete_time AS completeTime,
                     p.complete_emp AS completeEmp,
                     t.review_status AS reviewStatus,
                     case
                         when t.review_status = 0 then '未审核'
                         when t.review_status = 1 then '已通过'
                         when t.review_status = 2 then '已拒绝'
                         else '-' end                                                   AS reviewStatusDesc,
                     t.review_remark                                                      as reviewRemark,
                     t.review_emp                                                       as reviewEmp,
                     t.review_time                                                      as reviewTime,
                     p.remarks AS cashRemark,
                     t.remarks AS itemRemark
                    FROM pos_cash_product t
                        JOIN pos_cash p ON p.id = t.cash_id
                     LEFT JOIN base_product d ON t.product_id = d.id
                     LEFT JOIN base_dict bd ON bd.key_ = d.measuring_unit
                     AND bd.parent_key = 'PRODUCT_UNIT'
                     AND bd.created_org_id = d.created_org_id
                     ${ew.customSqlSegment}

                 UNION ALL

                 SELECT
                    p.id                         as cashId,
                    p.code                       as code,
                    t.id                         as itemId,
                     'THAIL'                    as type,
                    '套餐'                       as typeDesc,
                    t.thail_name                 as name,
                    1                            as num,
                    '个'                         as measuringUnit,
                     t.amount AS amount,
                     IFNULL(t.discount_amount, 0) + ROUND(IFNULL(t.assessed_amount, 0)) AS discountAmount,
                     0.00 AS payment,
                    t.created_time               as createdTime,
                     p.complete_time AS completeTime,
                     p.complete_emp AS completeEmp,
                     t.review_status AS reviewStatus,
                     case
                        when t.review_status = 0 then '未审核'
                         when t.review_status = 1 then '已通过'
                         when t.review_status = 2 then '已拒绝'
                         else '-' end                                                   AS reviewStatusDesc,
                     t.review_remark                                                      as reviewRemark,
                     t.review_emp                                                       as reviewEmp,
                     t.review_time                                                      as reviewTime,
                     p.remarks AS cashRemark,
                     t.remarks AS itemRemark
                 FROM pos_cash_thail t
                     JOIN pos_cash p ON p.id = t.cash_id
                     ${ew.customSqlSegment}
                UNION ALL
                 SELECT
                     p.id AS cashId,
                     p.code AS code,
                     t.id AS itemId,
                     'POWER' AS type,
                     '充电' AS typeDesc,
                     t.name AS name,
                     t.duration AS num,
                     '分钟' AS measuringUnit,
                     t.amount AS amount,
                     IFNULL(t.discount_amount, 0) + ROUND(IFNULL(t.assessed_amount, 0)) AS discountAmount,
                     0.00 AS payment,
                     t.created_time AS createdTime,
                     p.complete_time AS completeTime,
                     p.complete_emp AS completeEmp,
                     t.review_status AS reviewStatus,
                     case
                         when t.review_status = 0 then '未审核'
                         when t.review_status = 1 then '已通过'
                         when t.review_status = 2 then '已拒绝'
                         else '-' end                                                   AS reviewStatusDesc,
                     t.review_remark                                                      as reviewRemark,
                     t.review_emp                                                       as reviewEmp,
                     t.review_time                                                      as reviewTime,
                     p.remarks AS cashRemark,
                     t.remarks AS itemRemark
                 FROM pos_cash_power t
                     JOIN pos_cash p ON p.id = t.cash_id
                     ${ew.customSqlSegment}
             ) AS union_result
        ORDER BY createdTime DESC, itemId asc
    </select>
    <select id="freeSum" resultType="top.kx.kxss.report.vo.result.cash.OrderFreeResultVO">
        select sum(union_result.amount) AS amount,
               sum(union_result.payment) AS payment,
                sum(union_result.discountAmount) AS discountAmount

        from (
                          SELECT
                              sum(ifnull(t.amount, 0)) AS amount,
                              sum(IFNULL(t.discount_amount, 0) + ROUND(IFNULL(t.assessed_amount, 2))) AS discountAmount,
                              0.00 AS payment
                          FROM pos_cash_table t
                                   JOIN pos_cash p ON p.id = t.cash_id
                              ${ew.customSqlSegment}

                          UNION ALL

                          SELECT
                              sum(ifnull(t.amount, 0)) AS amount,
                              sum(IFNULL(t.discount_amount, 0) + ROUND(IFNULL(t.assessed_amount, 0), 2)) AS discountAmount,
                              0.00 AS payment
                          FROM pos_cash_service t
                              JOIN pos_cash p ON p.id = t.cash_id
                              ${ew.customSqlSegment}

                          UNION ALL

                          SELECT
                              sum(ifnull(t.amount, 0)) AS amount,
                              sum(IFNULL(t.discount_amount, 0) + ROUND(IFNULL(t.assessed_amount, 0), 2)) AS discountAmount,
                              0.00 AS payment
                          FROM pos_cash_product t
                              JOIN pos_cash p ON p.id = t.cash_id
                              ${ew.customSqlSegment}

                          UNION ALL

                          SELECT
                              sum(ifnull(t.amount, 0)) AS amount,
                              sum(IFNULL(t.discount_amount, 0) + ROUND(IFNULL(t.assessed_amount, 0), 2)) AS discountAmount,
                              0.00 AS payment
                          FROM pos_cash_thail t
                              JOIN pos_cash p ON p.id = t.cash_id
                              ${ew.customSqlSegment}
                          UNION ALL

                          SELECT
                              sum(ifnull(t.amount, 0)) AS amount,
                              sum(IFNULL(t.discount_amount, 0) + ROUND(IFNULL(t.assessed_amount, 0), 2)) AS discountAmount,
                              0.00 AS payment
                          FROM pos_cash_power t
                              JOIN pos_cash p ON p.id = t.cash_id
                              ${ew.customSqlSegment}
                      ) as union_result
    </select>

    <select id="freeList" resultType="top.kx.kxss.report.vo.result.cash.OrderFreeResultVO">
        SELECT union_result.cashId         as cashId,
               union_result.code           AS code,
               union_result.itemId         AS itemId,
               union_result.type           AS type,
               union_result.typeDesc       AS typeDesc,
               union_result.name           AS name,
               union_result.num            AS num,
               union_result.measuringUnit  AS measuringUnit,
               union_result.amount         AS amount,
               union_result.discountAmount AS discountAmount,
               union_result.payment        AS payment,
               DATE_FORMAT(union_result.createdTime, '%Y-%m-%d %H:%i:%s') AS createdTime,
               DATE_FORMAT(union_result.completeTime, '%Y-%m-%d %H:%i:%s')   AS completeTime,
               union_result.completeEmp    AS completeEmp,
               union_result.reviewStatusDesc     AS reviewStatusDesc,
                union_result.reviewRemark     AS reviewRemark,
               union_result.reviewEmp      AS reviewEmp,
               DATE_FORMAT(union_result.reviewTime, '%Y-%m-%d %H:%i:%s')     AS reviewTime,
               union_result.cashRemark     AS cashRemark,
               union_result.itemRemark     AS itemRemark
        FROM (
                 SELECT
                     p.id AS cashId,
                     p.code AS code,
                     t.id AS itemId,
                     'TABLE' AS type,
                     '台费' AS typeDesc,
                     t.table_name AS name,
                     t.duration AS num,
                     '分钟' AS measuringUnit,
                     t.amount AS amount,
                     IFNULL(t.discount_amount, 0) + ROUND(IFNULL(t.assessed_amount, 0)) AS discountAmount,
                     0.00 AS payment,
                     t.created_time AS createdTime,
                     p.complete_time AS completeTime,
                     p.complete_emp AS completeEmp,
                     t.review_status AS reviewStatus,
                     case
                        when t.review_status = 0 then '未审核'
                        when t.review_status = 1 then '已通过'
                        when t.review_status = 2 then '已拒绝'
                         else '-' end                                                   AS reviewStatusDesc,
                     t.review_remark                                                      as reviewRemark,
                     t.review_emp                                                       as reviewEmp,
                     t.review_time                                                      as reviewTime,
                     p.remarks AS cashRemark,
                     t.remarks AS itemRemark
                 FROM pos_cash_table t
                          JOIN pos_cash p ON p.id = t.cash_id
                     ${ew.customSqlSegment}

                 UNION ALL

                 SELECT
                     p.id                         as cashId,
                     p.code                       as code,
                     t.id                         as itemId,
                     'SERVICE' AS type,
                     '服务'                       as typeDesc,
                     t.employee_name              as name,
                     IFNULL(
                     CASE
                     WHEN t.cycle IS NULL
                     OR t.cycle = '' THEN
                     0
                     WHEN instr(t.cycle, '元/') <![CDATA[ <= ]]> 0 THEN 0
                     WHEN instr(t.cycle, '元/小时') > 0 THEN
                     (IFNULL(t.cycle_num, 0) * 60)
                     WHEN instr(t.cycle, '元/小时') <![CDATA[ <= ]]> 0 AND instr(t.cycle, '小时') > 0 THEN
                     (
                     IFNULL(t.cycle_num, 0) * 60 * SUBSTRING_INDEX(SUBSTRING_INDEX(t.cycle, '/', -1), '小时', 1))
                     WHEN instr(t.cycle, '元/分钟') > 0 THEN
                     IFNULL(t.cycle_num, 0)
                     WHEN instr(t.cycle, '元/分钟') <![CDATA[ <= ]]> 0 AND instr(t.cycle, '分钟') > 0 THEN
                     (
                     IFNULL(t.cycle_num, 0) * SUBSTRING_INDEX(SUBSTRING_INDEX(t.cycle, '/', -1), '分钟', 1))
                     ELSE 0
                     END,
                     0
                     )                            as num,
                     '分钟'                       as measuringUnit,
                     t.amount AS amount,
                     IFNULL(t.discount_amount, 0) + ROUND(IFNULL(t.assessed_amount, 0)) AS discountAmount,
                     0.00 AS payment,
                     t.created_time               as createdTime,
                     p.complete_time AS completeTime,
                     p.complete_emp AS completeEmp,
                     t.review_status AS reviewStatus,
                     case
                         when t.review_status = 0 then '待审核'
                         when t.review_status = 1 then '已通过'
                          when t.review_status = 2 then '已拒绝'
                         else '-' end                                                   AS reviewStatusDesc,
                     t.review_remark                                                      as reviewRemark,
                     t.review_emp                                                       as reviewEmp,
                     t.review_time                                                      as reviewTime,
                     p.remarks AS cashRemark,
                     t.remarks AS itemRemark
                 FROM pos_cash_service t
                     JOIN pos_cash p ON p.id = t.cash_id
                     ${ew.customSqlSegment}

                 UNION ALL

                 SELECT
                     p.id                            as cashId,
                     p.code                          as code,
                     t.id                            as itemId,
                     'PRODUCT' AS type,
                     '商品'                          as typeDesc,
                     t.product_name                  as name,
                     t.num - IFNULL(t.refund_num, 0) as num,
                     bd.name                         as measuringUnit,
                     t.amount AS amount,
                     IFNULL(t.discount_amount, 0) + ROUND(IFNULL(t.assessed_amount, 0)) AS discountAmount,
                     0.00 AS payment,
                     t.created_time                  as createdTime,
                     p.complete_time AS completeTime,
                     p.complete_emp AS completeEmp,
                     t.review_status AS reviewStatus,
                     case
                         when t.review_status = 0 then '待审核'
                         when t.review_status = 1 then '已通过'
                         when t.review_status = 2 then '已拒绝'
                         else '-' end                                                   AS reviewStatusDesc,
                     t.review_remark                                                      as reviewRemark,
                     t.review_emp                                                       as reviewEmp,
                     t.review_time                                                      as reviewTime,
                     p.remarks AS cashRemark,
                     t.remarks AS itemRemark
                 FROM pos_cash_product t
                     JOIN pos_cash p ON p.id = t.cash_id
                     LEFT JOIN base_product d ON t.product_id = d.id
                     LEFT JOIN base_dict bd ON bd.key_ = d.measuring_unit
                     AND bd.parent_key = 'PRODUCT_UNIT'
                     AND bd.created_org_id = d.created_org_id
                     ${ew.customSqlSegment}

                 UNION ALL

                 SELECT
                     p.id                         as cashId,
                     p.code                       as code,
                     t.id                         as itemId,
                     'THAIL' AS type,
                     '套餐'                       as typeDesc,
                     t.thail_name                 as name,
                     1                            as num,
                     '个'                         as measuringUnit,
                     t.amount AS amount,
                     IFNULL(t.discount_amount, 0) + ROUND(IFNULL(t.assessed_amount, 0)) AS discountAmount,
                     0.00 AS payment,
                     t.created_time               as createdTime,
                     p.complete_time AS completeTime,
                     p.complete_emp AS completeEmp,
                     t.review_status AS reviewStatus,
                     case
                         when t.review_status = 0 then '待审核'
                         when t.review_status = 1 then '已通过'
                          when t.review_status = 2 then '已拒绝'
                         else '-' end                                                   AS reviewStatusDesc,
                     t.review_remark                                                      as reviewRemark,
                     t.review_emp                                                       as reviewEmp,
                     t.review_time                                                      as reviewTime,
                     p.remarks AS cashRemark,
                     t.remarks AS itemRemark
                 FROM pos_cash_thail t
                     JOIN pos_cash p ON p.id = t.cash_id
                     ${ew.customSqlSegment}
                 UNION ALL
                 SELECT
                     p.id AS cashId,
                     p.code AS code,
                     t.id AS itemId,
                     'POWER' AS type,
                     '充电' AS typeDesc,
                     t.name AS name,
                     t.duration AS num,
                     '分钟' AS measuringUnit,
                     t.amount AS amount,
                     IFNULL(t.discount_amount, 0) + ROUND(IFNULL(t.assessed_amount, 0)) AS discountAmount,
                     0.00 AS payment,
                     t.created_time AS createdTime,
                     p.complete_time AS completeTime,
                     p.complete_emp AS completeEmp,
                     t.review_status AS reviewStatus,
                     case
                        when t.review_status = 0 then '待审核'
                        when t.review_status = 1 then '已通过'
                         when t.review_status = 2 then '已拒绝'
                         else '-' end                                                   AS reviewStatusDesc,
                     t.review_remark                                                      as reviewRemark,
                     t.review_emp                                                       as reviewEmp,
                     t.review_time                                                      as reviewTime,
                     p.remarks AS cashRemark,
                     t.remarks AS itemRemark
                 FROM pos_cash_power t
                     JOIN pos_cash p ON p.id = t.cash_id
                    ${ew.customSqlSegment}
             ) AS union_result
        ORDER BY createdTime DESC, itemId asc
    </select>
    <select id="queryList" resultType="top.kx.kxss.app.entity.cash.payment.PosCashPayment">
        select id,
               cash_id,
               pay_type_id,
               status,
               amount,
               round_amount,
               pay_time,
               recharge_amount,
               gift_amount,
               remarks,
               created_time,
               created_by,
               updated_time,
               updated_by,
               created_org_id,
               delete_flag,
               order_source,
               change_amount,
               pay_name,
               platform_id,
               order_id,
               securities_number,
               refund_amount,
               member_id,
               employee_id,
               auto_round_amount,
               is_auto_round,
               account_deduct_type,
               residue_gift_amount,
               residue_recharge_amount,
               member_card_id,
               mch_refund_no,
               account_pay_type_id,
               mch_order_no,
               pay_type,
               mch_fee_rate,
               mch_fee_amount,
               sn,
               is_prepaid,
               refund_gift_amount,
               voucher_coupon_id,
               voucher_coupon_count,
               pay_discount_amount,
               group_buy_amount,
               platform_amount,
               merchant_amount,
               channel_user,
               is_new_cum,
               is_change,
               consume_limit_id,
               consume_limit_desc
        from pos_cash_payment
                 ${ew.customSqlSegment}
    </select>
    <select id="thailAmountList" resultType="top.kx.kxss.app.vo.result.thail.PosCashThailAmountResultVO">
        SELECT pc.id as                                                                          cashId,
               ROUND(SUM(if(t.is_check_securities, IFNULL(t.amount, 0), 0)) -
                     SUM(if(t.is_check_securities, IFNULL(t.refund_amount, 0), 0)) -
                     SUM(if(t.is_check_securities, IFNULL(t.assessed_amount, 0), 0)),
                     2)                                                                          groupBuyAmount,
                ROUND(SUM(if(t.is_check_securities, IFNULL(t.price, 0), 0)), 2)                   groupBuyPrice,
               ROUND(SUM(if(t.is_check_securities = false, IFNULL(t.amount, 0), 0)) -
                     SUM(if(t.is_check_securities = false, IFNULL(t.refund_amount, 0), 0)) -
                     SUM(if(t.is_check_securities = false, IFNULL(t.assessed_amount, 0), 0)), 2) thailAmount,
               ROUND(SUM(if(t.is_check_securities = false, IFNULL(t.price, 0), 0)), 2)           thailPrice,
               COUNT(distinct CASE WHEN t.is_check_securities THEN pc.id END)                    groupBuyNum,
               COUNT(distinct CASE WHEN NOT t.is_check_securities THEN pc.id END)                thailNum
        FROM pos_cash_thail t
                 inner JOIN pos_cash pc ON pc.id = t.cash_id
            ${ew.customSqlSegment}
    </select>
    <select id="powerAmountList" resultType="top.kx.kxss.report.vo.PosCashItemResultVO">
        SELECT pc.id as cashId,
               ROUND(SUM(IFNULL(t.amount, 0) - IFNULL(t.refund_amount, 0)),
                     2) amount,
               ROUND(SUM(IFNULL(t.amount, 0) - IFNULL(t.refund_amount, 0) - IFNULL(t.assessed_amount, 0)),
                     2) payment
        FROM pos_cash_power t
                 inner JOIN pos_cash pc ON pc.id = t.cash_id
            ${ew.customSqlSegment}
    </select>
    <select id="tableAmountList" resultType="top.kx.kxss.report.vo.PosCashItemResultVO">
        SELECT pc.id as cashId,
               ROUND(SUM(IFNULL(t.amount, 0) - IFNULL(t.refund_amount, 0)),
                     2) amount,
               ROUND(SUM(IFNULL(t.amount, 0) - IFNULL(t.refund_amount, 0) - IFNULL(t.assessed_amount, 0)),
                     2) payment
        FROM pos_cash_table t
                 inner JOIN pos_cash pc ON pc.id = t.cash_id
            ${ew.customSqlSegment}
    </select>
    <select id="serviceAmountList" resultType="top.kx.kxss.report.vo.PosCashItemResultVO">
        SELECT pc.id as cashId,
               ROUND(SUM(IFNULL(t.amount, 0) - IFNULL(t.refund_amount, 0)),
                     2) amount,
               ROUND(SUM(IFNULL(t.amount, 0) - IFNULL(t.refund_amount, 0) - IFNULL(t.assessed_amount, 0)),
                     2) payment
        FROM pos_cash_service t
                 inner JOIN pos_cash pc ON pc.id = t.cash_id
            ${ew.customSqlSegment}
    </select>
    <select id="productAmountList" resultType="top.kx.kxss.report.vo.PosCashItemResultVO">
        SELECT pc.id as cashId,
               ROUND(SUM(IFNULL(t.amount, 0) - IFNULL(t.refund_amount, 0)),
                     2) amount,
               ROUND(SUM(IFNULL(t.amount, 0) - IFNULL(t.refund_amount, 0) - IFNULL(t.assessed_amount, 0)),
                     2) payment
        FROM pos_cash_product t
                 inner JOIN pos_cash pc ON pc.id = t.cash_id
            ${ew.customSqlSegment}
    </select>

</mapper>
