<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.report.mapper.ScoreExchangeMapper">

    <select id="page" resultType="top.kx.kxss.report.vo.ScoreExchangeResultVO">
        select s.created_time                                       AS createdTime,
               cate.`name`                                          AS categoryName,
               d.`NAME`                                             AS name,
               dict.`NAME`                                          AS measuringUnit,
               s.quantity                                           AS num,
               0 - ROUND(IFNULL(bps.cost_price, 0) * s.quantity, 2) AS totalCostPrice,
               s.warehouse_id                                       AS warehouseId
        from score_exchange s
                 LEFT JOIN base_product d ON d.id = s.biz_id
                 LEFT JOIN base_product_category cate ON cate.id = d.category_id AND cate.created_org_id = s.created_org_id
                 LEFT JOIN base_dict dict ON dict.key_ = d.measuring_unit AND dict.parent_key = 'PRODUCT_UNIT' and
                                             dict.created_org_id = s.created_org_id
                 LEFT JOIN base_product_stock bps on s.biz_id = bps.product_id and bps.created_org_id = s.created_org_id and
                                                     s.warehouse_id = bps.warehouse_id
        where s.delete_flag = 0
          and s.biz_type = '1'
        <if test="model.startDate != null and model.startDate != ''">
            and s.created_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and s.created_time &lt;= #{model.endDate}
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and instr(d.name, #{model.keyword})
        </if>
        <if test="model.categoryId != null">
            and d.category_id = #{model.categoryId}
        </if>
        <if test="model.warehouseId != null">
            and s.warehouse_id = #{model.warehouseId}
        </if>
        order by s.created_time desc
    </select>

    <select id="sum" resultType="top.kx.kxss.report.vo.ScoreExchangeResultVO">
        select sum(s.quantity)                                           AS num,
               sum(0 - ROUND(IFNULL(bps.cost_price, 0) * s.quantity, 2)) AS totalCostPrice
        from score_exchange s
                LEFT JOIN base_product d ON d.id = s.biz_id
                 LEFT JOIN base_product_stock bps on s.biz_id = bps.product_id and bps.created_org_id = s.created_org_id and
                                                     s.warehouse_id = bps.warehouse_id
        where s.delete_flag = 0
          and s.biz_type = '1'
        <if test="model.startDate != null and model.startDate != ''">
            and s.created_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and s.created_time &lt;= #{model.endDate}
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and instr(d.name, #{model.keyword})
        </if>
        <if test="model.categoryId != null">
            and d.category_id = #{model.categoryId}
        </if>
        <if test="model.warehouseId != null">
            and s.warehouse_id = #{model.warehouseId}
        </if>
    </select>

    <select id="list" resultType="top.kx.kxss.report.vo.ScoreExchangeResultVO">
        select s.created_time                                       AS createdTime,
               cate.`name`                                          AS categoryName,
               d.`NAME`                                             AS name,
               dict.`NAME`                                          AS measuringUnit,
               s.quantity                                           AS num,
               0 - ROUND(IFNULL(bps.cost_price, 0) * s.quantity, 2) AS totalCostPrice,
               s.warehouse_id                                       AS warehouseId
        from score_exchange s
                 LEFT JOIN base_product d ON d.id = s.biz_id
                 LEFT JOIN base_product_category cate ON cate.id = d.category_id AND cate.created_org_id = s.created_org_id
                 LEFT JOIN base_dict dict ON dict.key_ = d.measuring_unit AND dict.parent_key = 'PRODUCT_UNIT' and
                                             dict.created_org_id = s.created_org_id
                 LEFT JOIN base_product_stock bps on s.biz_id = bps.product_id and bps.created_org_id = s.created_org_id and
                                                     s.warehouse_id = bps.warehouse_id
        where s.delete_flag = 0
          and s.biz_type = '1'
        <if test="model.startDate != null and model.startDate != ''">
            and s.created_time >= #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and s.created_time &lt;= #{model.endDate}
        </if>
        <if test="model.keyword != null and model.keyword != ''">
            and instr(d.name, #{model.keyword})
        </if>
        <if test="model.warehouseId != null">
            and s.warehouse_id = #{model.warehouseId}
        </if>
        <if test="model.categoryId != null">
            and d.category_id = #{model.categoryId}
        </if>
        order by s.created_time desc
    </select>


</mapper>
