<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.report.mapper.CouponMapper">

    <select id="statisPage" resultType="top.kx.kxss.report.vo.StatisCouponResultVO">
        SELECT bci.id                                                          AS id,
                bci.name                                                        AS name,
               bci.type_                                                       AS type,
               bci.sent_num                                                    AS sendNum,
               COUNT(DISTINCT mc.member_id)                                    AS sendMemberCount,
               COUNT(DISTINCT CASE WHEN mc.status = '2' THEN mc.member_id END) AS usedMemberCount,
               SUM(IF(mc.status = '2', mc.received_num, 0))                    AS usedNum
        FROM base_coupon_info bci
                 LEFT JOIN member_coupon mc ON bci.id = mc.coupon_id
            ${ew.customSqlSegment}
        GROUP BY bci.id
    </select>

    <select id="statisSum" resultType="top.kx.kxss.report.vo.StatisCouponResultVO">

    </select>

    <select id="statisList" resultType="top.kx.kxss.report.vo.StatisCouponResultVO">
        SELECT bci.id                                                          AS id,
               bci.name                                                        AS name,
               bci.type_                                                       AS type,
               bci.sent_num                                                    AS sendNum,
               COUNT(DISTINCT mc.member_id)                                    AS sendMemberCount,
               COUNT(DISTINCT CASE WHEN mc.status = '2' THEN mc.member_id END) AS usedMemberCount,
               SUM(IF(mc.status = '2', mc.received_num, 0))                    AS usedNum
        FROM base_coupon_info bci
                 LEFT JOIN member_coupon mc ON bci.id = mc.coupon_id
            ${ew.customSqlSegment}
        GROUP BY bci.id
    </select>

    <select id="statisPosCashCouponList" resultType="top.kx.kxss.report.vo.StatisCouponResultVO">
        SELECT bci.id                     AS id,
               COUNT(distinct pc.id)      AS pullOrderNum,
               0 - sum(pcdd.price_change) AS deductAmount,
               sum(pc.amount)             AS pullTurnover,
               sum(pc.income)             AS pullIncome
        FROM base_coupon_info bci
                 LEFT JOIN member_coupon mc ON bci.id = mc.coupon_id
                 LEFT JOIN pos_cash_discount_detail pcdd on pcdd.ext_id = mc.id
                 LEFT JOIN pos_cash pc on pcdd.pos_cash_id = pc.id
            ${ew.customSqlSegment}
        GROUP BY bci.id
    </select>

    <select id="memberCouponPage" resultType="top.kx.kxss.report.vo.MemberCouponResultVO">
        SELECT mc.id                      AS id,
               mc.type_                   AS type,
               mc.name                    AS name,
               mc.usage_time              AS consumeTime,
               pc.complete_time           AS completeTime,
               pc.code                    AS code,
               pc.order_source            AS orderSource,
               mc.member_id               AS memberId,
               mi.name                    AS memberName,
               mi.mobile                  AS mobile,
               mc.code                    AS couponCode,
               pc.org_id                  AS orgId,
               0 - SUM(pcdd.price_change) AS deductAmount,
               SUM(pc.amount)             AS pullTurnover,
               SUM(pc.income)             AS pullIncome
        FROM member_coupon mc
                 LEFT JOIN member_info mi ON mc.member_id = mi.id
                 LEFT JOIN pos_cash_discount_detail pcdd
                           on pcdd.ext_id = mc.id and pcdd.delete_flag = 0 and pcdd.discount_type = '4'
                 LEFT JOIN pos_cash pc on pcdd.pos_cash_id = pc.id
            ${ew.customSqlSegment}
        GROUP BY mc.id
    </select>

    <select id="memberCouponSum" resultType="top.kx.kxss.report.vo.MemberCouponResultVO">
        SELECT 0 - SUM(pcdd.price_change) AS deductAmount,
               SUM(pc.amount)             AS pullTurnover,
               SUM(pc.income)             AS pullIncome
        FROM member_coupon mc
                 LEFT JOIN member_info mi ON mc.member_id = mi.id
                 LEFT JOIN pos_cash_discount_detail pcdd
                           on pcdd.ext_id = mc.id and pcdd.delete_flag = 0 and pcdd.discount_type = '4'
                 LEFT JOIN pos_cash pc on pcdd.pos_cash_id = pc.id
            ${ew.customSqlSegment}
    </select>

    <select id="memberCouponList" resultType="top.kx.kxss.report.vo.MemberCouponResultVO">
        SELECT mc.id                      AS id,
               mc.type_                   AS type,
               mc.name                    AS name,
               mc.usage_time              AS consumeTime,
               pc.complete_time           AS completeTime,
               pc.code                    AS code,
               pc.order_source            AS orderSource,
               mc.member_id               AS memberId,
               mi.name                    AS memberName,
               mi.mobile                  AS mobile,
               mc.code                    AS couponCode,
               pc.org_id                  AS orgId,
               0 - SUM(pcdd.price_change) AS deductAmount,
               SUM(pc.amount)             AS pullTurnover,
               SUM(pc.income)             AS pullIncome
        FROM member_coupon mc
                 LEFT JOIN member_info mi ON mc.member_id = mi.id
                 LEFT JOIN pos_cash_discount_detail pcdd
                           on pcdd.ext_id = mc.id and pcdd.delete_flag = 0 and pcdd.discount_type = '4'
                 LEFT JOIN pos_cash pc on pcdd.pos_cash_id = pc.id
            ${ew.customSqlSegment}
        GROUP BY mc.id
    </select>


</mapper>
