<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.report.mapper.SalesMapper">


    <select id="productList" resultType="top.kx.kxss.report.vo.SalesDetailResultVO">
        SELECT pro.product_name                               AS NAME,
               c.`name`                                       AS categoryName,
               sum(ifNULL(pro.num, 0))                        as salesNum,
               sum(
                       IFNULL(pro.orgin_price, 0))            AS amount,
               sum(
                       IFNULL(pro.discount_amount, 0)) + sum(
                       IFNULL(pro.assessed_amount, 0))        AS discountAmount,
               IF
               (
                       pro.cash_thail_id IS NOT NULL and pro.thail_assessed_amount is not null,
                       sum(
                               IFNULL(pro.thail_assessed_amount, 0)),
                       sum(
                               IFNULL(pro.amount, 0)) - sum(
                               IFNULL(pro.assessed_amount, 0)) - sum(
                               IFNULL(pro.refund_amount, 0))) AS payment,
               IF(pro.cash_thail_id is null, 0, 1)            as isThail
        FROM pos_cash_product pro
                 INNER JOIN pos_cash p ON p.id = pro.cash_id
                 LEFT JOIN base_product b ON b.id = pro.product_id
                 LEFT JOIN base_product_category c ON c.id = b.category_id
            ${ew.customSqlSegment}
    </select>
    <select id="tableList" resultType="top.kx.kxss.report.vo.SalesDetailResultVO">
        SELECT pro.table_name                               AS NAME,
               c.`name`                                       AS categoryName,
               count(pro.table_id)                        as salesNum,
               sum(
                       IFNULL(pro.orgin_price, 0))            AS amount,
               sum(
                       IFNULL(pro.discount_amount, 0)) + sum(
                       IFNULL(pro.assessed_amount, 0))        AS discountAmount,
               IF
               (
                       pro.cash_thail_id IS NOT NULL and pro.thail_assessed_amount is not null,
                       sum(
                               IFNULL(pro.thail_assessed_amount, 0)),
                       sum(
                               IFNULL(pro.amount, 0)) - sum(
                               IFNULL(pro.assessed_amount, 0)) - sum(
                               IFNULL(pro.refund_amount, 0))) AS payment,
               IF(pro.cash_thail_id is null, 0, 1)            as isThail
        FROM pos_cash_table pro
                 INNER JOIN pos_cash p ON p.id = pro.cash_id
                 LEFT JOIN base_table_info b ON b.id = pro.table_id
                 LEFT JOIN base_dict c
                           ON c.key_ = b.table_type AND c.parent_key = 'BASE_TABLE_TYPE'
            ${ew.customSqlSegment}
    </select>
    <select id="serviceList" resultType="top.kx.kxss.report.vo.SalesDetailResultVO">
        SELECT concat(ifNull(c.number, ''), '-', c.real_name, '-', b.name) AS NAME,
               b.`name`                                                    AS categoryName,
               count(pro.id)                                         as salesNum,
               sum(
                       IFNULL(pro.orgin_price, 0))                         AS amount,
               sum(
                       IFNULL(pro.discount_amount, 0)) + sum(
                       IFNULL(pro.assessed_amount, 0))                     AS discountAmount,
               IF
               (
                       pro.cash_thail_id IS NOT NULL and pro.thail_assessed_amount is not null ,
                       sum(
                               IFNULL(pro.thail_assessed_amount, 0)),
                       sum(
                               IFNULL(pro.amount, 0)) - sum(
                               IFNULL(pro.assessed_amount, 0)) - sum(
                               IFNULL(pro.refund_amount, 0)))              AS payment,
               IF(pro.cash_thail_id is null, 0, 1)                         as isThail
        FROM pos_cash_service pro
                 INNER JOIN pos_cash p ON p.id = pro.cash_id
                 LEFT JOIN base_service b ON b.id = pro.service_id
                 LEFT JOIN base_employee c ON c.id = pro.employee_id
            ${ew.customSqlSegment}
    </select>
</mapper>
