<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.report.mapper.ProfitMapper">


    <select id="selectOneCashAmount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT
            IFNULL(COUNT(id), 0)                                                            num,
            COUNT(DISTINCT CASE WHEN type_ = '3' THEN id END)                              rechargeNum,
            COUNT(DISTINCT CASE WHEN type_ = '0' THEN id END)                      AS       tableNum,
            IFNULL(SUM(IFNULL(amount, 0)), 0)                                               amount,
            (IFNULL(SUM(IFNULL(payment, 0)), 0) - IFNULL(SUM(IFNULL(refund_amount, 0)), 0)) payment,
            IFNULL(SUM(IFNULL(discount_amount, 0)), 0)                                      discountAmount,
            IFNULL(SUM(IFNULL(refund_amount, 0)), 0)                                        refundAmount,
            IFNULL(SUM(IFNULL(gift_amount, 0)), 0)                                          giftAmount,
            IFNULL(SUM(IFNULL(paid, 0)), 0)                                                 paid,
            IFNULL(SUM(IFNULL(unpaid, 0)), 0)                                               unpaid,
            bill_type as                                                                    billType,
            COUNT(DISTINCT CASE WHEN is_first_recharge THEN member_id END)         AS   firstRechargeNum,
            COUNT(DISTINCT CASE WHEN is_first_recharge = false THEN member_id END) AS continueRechargeNum,
            IFNULL(SUM(CASE
                           WHEN p.type_ = '3' THEN
                               IFNULL(p.payment, 0)
                           ELSE 0
                END), 0)  AS                                                                    rechargeAmount
        FROM pos_cash p
            ${ew.customSqlSegment}
    </select>

    <select id="selectProductAmount" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT IFNULL(SUM(IFNULL(t.orgin_price, 0)), 0)                                                     amount,
               ROUND(IFNULL(SUM(IFNULL(t.amount, 0)), 0) - IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0), 2) payment,
               round(IFNULL(SUM(IFNULL(t.discount_amount, 0)), 0) + IFNULL(SUM(IFNULL(t.assessed_amount, 0)), 0),
                     2)                                                                                     discountAmount,
               IFNULL(SUM(IFNULL(t.profit_price, 0)), 0)                                                    profitPrice,
               ifnull(SUM(IFNULL(t.num, 0) * IFNULL(t.cost_price, 0)), 0)                                   costPrice,
               IFNULL(SUM(IFNULL(p.paid, 0)), 0)                                                            paid,
               IFNULL(SUM(IFNULL(p.unpaid, 0)), 0)                                                          unpaid,
               IFNULL(sum(ifnull(t.num, 0)), 0)                                                             productNum,
               IFNULL(COUNT(distinct p.id), 0)                                                              num,
               IFNULL(COUNT(distinct t.cash_id), 0)                                                         productOrderNum,
               IFNULL(COUNT(distinct CASE WHEN p.type_ = '1' THEN p.id END), 0)                             shoppingOrderNum,
               ROUND(IFNULL(sum(CASE WHEN p.type_ = '0' THEN (IFNULL(t.amount, 0) - IFNULL(t.assessed_amount, 0)) END),
                            0), 2)                                                                          tableProductAmount
        FROM pos_cash p
                 INNER JOIN pos_cash_product t ON p.id = t.cash_id
            ${ew.customSqlSegment}
    </select>

    <select id="selectFeeAmount" resultType="top.kx.kxss.report.vo.FeeAmountResultVO">
        select sum(case when b.biz_type = '2' then t.mch_fee_amount end)                     polymerization,
               sum(case when b.biz_type = '5' or b.biz_type = '6' then t.mch_fee_amount end) groupBuy
        from pos_cash_payment t
                 left join pos_cash p on t.cash_id = p.id
                 left join base_payment_type b on t.pay_type_id = b.id
            ${ew.customSqlSegment}
    </select>

</mapper>
