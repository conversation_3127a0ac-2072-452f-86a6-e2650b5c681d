<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.report.mapper.PosCashPaymentMapper">

    <select id="selectOneAmount" resultType="top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO">
        select COUNT(DISTINCT p.id)                                                       as num,
               IFNULL(ROUND(sum(ifnull(pcp.amount, 0) - ifnull(pcp.refund_amount, 0) - ifnull(pcp.change_amount, 0)),
                            2),
                      0)                                                                  as payment,
               IFNULL(ROUND(sum(ifnull(pcp.amount, 0)), 2), 0)                            as amount,
               IFNULL(ROUND(SUM(CASE
                                    WHEN p.type_ = '3' THEN (IFNULL(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
                                                             IFNULL(pcp.change_amount, 0)) END),
                            2),
                      0)                                                                  as rechargeAmount,
               IFNULL(ROUND(SUM(CASE
                                    WHEN p.type_ != '3' THEN (IFNULL(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
                                                              IFNULL(pcp.change_amount, 0)) END),
                            2),
                      0)                                                                  as turnoverAmount,
               IFNULL(SUM(IF(
                       IFNULL(pcp.refund_amount, 0) > 0 and IFNULL(pcp.refund_amount, 0) > IFNULL(pcp.gift_amount, 0),
                       IFNULL(pcp.recharge_amount, 0) - (IFNULL(pcp.refund_amount, 0) - IFNULL(pcp.gift_amount, 0)),
                       IFNULL(pcp.recharge_amount, 0))), 0)                               as rechargePayment,
               IFNULL(SUM(IF(
                       IFNULL(pcp.refund_amount, 0) > 0 and IFNULL(pcp.refund_amount, 0) > IFNULL(pcp.gift_amount, 0),
                       0, IFNULL(pcp.gift_amount, 0) - IFNULL(pcp.refund_amount, 0))), 0) as giftPayment
        from pos_cash_payment pcp
                 inner join pos_cash p on pcp.cash_id = p.id
                 inner join base_payment_type bpt on pcp.pay_type_id = bpt.id
            ${ew.customSqlSegment}
    </select>

    <select id="selectListAmountByPayTypeId" resultType="top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO">
        select COUNT(DISTINCT p.id)                                                       as num,
               pcp.pay_type_id                                                            as payTypeId,
               bpt.biz_type                                                               as bizType,
               IFNULL(ROUND(sum(ifnull(pcp.amount, 0) - ifnull(pcp.refund_amount, 0) - ifnull(pcp.change_amount, 0)),
                            2),
                      0)                                                                  as payment,
               IFNULL(ROUND(sum(ifnull(pcp.amount, 0)), 2), 0)                            as amount,
               IFNULL(ROUND(SUM(CASE
                                    WHEN p.type_ = '3' THEN (IFNULL(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
                                                             IFNULL(pcp.change_amount, 0)) END),
                            2),
                      0)                                                                  as rechargeAmount,
               IFNULL(ROUND(SUM(CASE
                                    WHEN p.type_ != '3' THEN (IFNULL(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
                                                              IFNULL(pcp.change_amount, 0)) END),
                            2),
                      0)                                                                  as turnoverAmount,
               IFNULL(SUM(IF(
                       IFNULL(pcp.refund_amount, 0) > 0 and IFNULL(pcp.refund_amount, 0) > IFNULL(pcp.gift_amount, 0),
                       IFNULL(pcp.recharge_amount, 0) - (IFNULL(pcp.refund_amount, 0) - IFNULL(pcp.gift_amount, 0)),
                       IFNULL(pcp.recharge_amount, 0))), 0)                               as rechargePayment,
               IFNULL(SUM(IF(
                       IFNULL(pcp.refund_amount, 0) > 0 and IFNULL(pcp.refund_amount, 0) > IFNULL(pcp.gift_amount, 0),
                       0, IFNULL(pcp.gift_amount, 0) - IFNULL(pcp.refund_amount, 0))), 0) as giftPayment
        from pos_cash_payment pcp
                 inner join pos_cash p on pcp.cash_id = p.id
                 inner join base_payment_type bpt on pcp.pay_type_id = bpt.id
            ${ew.customSqlSegment}
        group by bpt.biz_type
    </select>

    <select id="queryListByCashIds" resultType="top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO">
        select id,
        cash_id,
        pay_type_id,
        status,
        amount,
        round_amount,
        pay_time,
        recharge_amount,
        gift_amount,
        remarks,
        created_time,
        created_by,
        updated_time,
        updated_by,
        created_org_id,
        delete_flag,
        order_source,
        change_amount,
        pay_name,
        platform_id,
        order_id,
        securities_number,
        refund_amount,
        member_id,
        employee_id,
        auto_round_amount,
        is_auto_round,
        account_deduct_type,
        residue_gift_amount,
        residue_recharge_amount,
        member_card_id,
        mch_refund_no,
        account_pay_type_id,
        mch_order_no,
        pay_type,
        mch_fee_rate,
        mch_fee_amount,
        sn,
        is_prepaid,
        refund_gift_amount,
        group_buy_amount,
        platform_amount,
        merchant_amount,
        voucher_coupon_id,
        voucher_coupon_count,
        pay_discount_amount,
        is_new_cum,
        channel_user
        from pos_cash_payment
        where delete_flag = 0 and status = '2' and cash_id in
        <foreach collection="cashIds" item="cashId" open="(" separator="," close=")">
            #{cashId}
        </foreach>
    </select>

    <select id="transactionPageResultVO" resultType="top.kx.kxss.report.vo.PaymentTransactionResultVO">
        select DATE (pc.complete_time) as 'completeDate', DATE (pcpt.pay_time) as 'payDate', pc.created_time as 'orderCreatedTime', pc.complete_time as 'completeTime', pcpt.pay_time as 'payTime', pcpt.created_org_id as 'orgId', pcpt.type_ as 'type', pcpt.pay_type as 'payTypeName', pcpt.pay_type_detail as 'payTypeDetail', pcpt.status as 'status', if(pcpt.status = '1', pcpt.amount, - pcpt.amount) as 'amount', if(pcpt.status = '1', pcpt.fee_amount, - pcpt.fee_amount) as 'feeAmount', pcpt.order_id as 'orderId', pcpt.securities_type as 'securitiesType', pcpt.securities_number as 'securitiesNumber', pcpt.employee_id as 'employeeId', pcpt.code_ as 'code', pcpt.order_source as 'orderSource', pc.bill_state as 'billState', pc.table_name as 'tableName'
        from pos_cash_payment_transaction pcpt
            left join pos_cash pc
        on pcpt.cash_id = pc.id
            ${ew.customSqlSegment}
    </select>

    <select id="transactionSum" resultType="top.kx.kxss.report.vo.PaymentTransactionResultVO">
        select sum(if(pcpt.status = '2', - pcpt.amount, pcpt.amount))         as amount,
               sum(if(pcpt.status = '2', - pcpt.fee_amount, pcpt.fee_amount)) as feeAmount
        from pos_cash_payment_transaction pcpt
                 left join pos_cash pc on pcpt.cash_id = pc.id
            ${ew.customSqlSegment}
    </select>

    <select id="transactionList" resultType="top.kx.kxss.report.vo.PaymentTransactionResultVO">
        select DATE (pc.complete_time) as 'completeDate', DATE (pcpt.pay_time) as 'payDate', pc.created_time as 'orderCreatedTime', pc.complete_time as 'completeTime', pcpt.pay_time as 'payTime', pcpt.created_org_id as 'orgId', pcpt.type_ as 'type', pcpt.pay_type as 'payTypeName', pcpt.pay_type_detail as 'payTypeDetail', pcpt.status as 'status', if(pcpt.status = '1', pcpt.amount, - pcpt.amount) as 'amount', if(pcpt.status = '1', pcpt.fee_amount, - pcpt.fee_amount) as 'feeAmount', pcpt.order_id as 'orderId', pcpt.securities_type as 'securitiesType', pcpt.securities_number as 'securitiesNumber', pcpt.employee_id as 'employeeId', pcpt.code_ as 'code', pcpt.order_source as 'orderSource', pc.bill_state as 'billState', pc.table_name as 'tableName'
        from pos_cash_payment_transaction pcpt
            left join pos_cash pc
        on pcpt.cash_id = pc.id
            ${ew.customSqlSegment}
    </select>
    <select id="selectOnePayAmount" resultType="top.kx.kxss.app.vo.result.cash.payment.PosCashPaymentResultVO">
        select sum(IF(pcpt.status = 2,0-IFNULL(pcpt.amount, 0),IFNULL(pcpt.amount, 0))) as amount
        from pos_cash_payment_transaction pcpt
        inner join base_payment_type pt on pcpt.pay_type_id = pt.id
        left join pos_cash p on pcpt.cash_id = p.id
        where pcpt.delete_flag = 0 and pt.biz_type in ('2','3')
        and p.delete_flag = 0 and p.bill_state in ('2','5','6')
        and p.bill_type in ('0', '3', '4')
        <if test="model.startDate != null and model.startDate != ''">
            and p.complete_time <![CDATA[ >= ]]> #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.complete_time <![CDATA[ <= ]]> #{model.endDate}
        </if>
        <if test="orgId != null">
            and pcpt.created_org_id = #{orgId}
        </if>
        <if test="orgId != null">
            and p.org_id = #{orgId}
        </if>
    </select>
    <select id="selectPayAmount" resultType="top.kx.kxss.report.vo.PaymentTransactionResultVO">
        select IF(pcpt.status = 2,0-IFNULL(pcpt.amount, 0),IFNULL(pcpt.amount, 0)) as amount,
        pcpt.order_id as orderId,
        p.code,
        p.complete_time as completeTime
        from pos_cash_payment_transaction pcpt
        inner join base_payment_type pt on pcpt.pay_type_id = pt.id
        left join pos_cash p on pcpt.cash_id = p.id
        where pcpt.delete_flag = 0 and pt.biz_type in ('2','3')
        and p.delete_flag = 0 and p.bill_state in ('2','5','6')
        and p.bill_type in ('0', '3', '4')
        <if test="model.startDate != null and model.startDate != ''">
            and p.complete_time <![CDATA[ >= ]]> #{model.startDate}
        </if>
        <if test="model.endDate != null and model.endDate != ''">
            and p.complete_time <![CDATA[ <= ]]> #{model.endDate}
        </if>
        <if test="orgId != null">
            and pcpt.created_org_id = #{orgId}
        </if>
        <if test="orgId != null">
            and p.org_id = #{orgId}
        </if>
    </select>
    <select id="cashPaymentList" resultType="top.kx.kxss.report.vo.CashPaymentResultVO">
        select pcp.id,
        pc.code as code,
        pc.id as posCashId,
        pcp.order_id as orderId,
        pc.bill_state as billState,
        pc.bill_type as billType,
        pc.complete_time as completeTime,
        pcp.pay_time as payTime,
        t.biz_type as payType,
        pcp.status as status
        from pos_cash_payment pcp
        inner join pos_cash pc on pcp.cash_id = pc.id
        left join base_payment_type t on t.id = pcp.pay_type_id
        where pcp.delete_flag = 0
        and pc.delete_flag = 0 and t.biz_type in ('2','3')
        and pcp.order_id is not null
        <if test="model.orgId != null">
            and pcp.created_org_id = #{model.orgId}
        </if>
        <if test="model.orgId != null">
            and pc.org_id = #{model.orgId}
        </if>
        <if test="model.orderIds != null and model.orderIds.size() > 0">
            and pcp.order_id in
            <foreach collection="model.orderIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="cashList" resultType="top.kx.kxss.report.vo.CashPaymentResultVO">
        select pc.id,
        pc.code as code,
        pc.id as posCashId,
        pc.bill_state as billState,
        pc.bill_type as billType,
        pc.complete_time as completeTime,
        from pos_cash pc
        where pc.delete_flag = 0
        <if test="model.orgId != null">
            and pc.org_id = #{model.orgId}
        </if>
        <if test="model.orderIds != null and model.orderIds.size() > 0">
            and pc.org_id in
            <foreach collection="model.orderIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>


</mapper>
