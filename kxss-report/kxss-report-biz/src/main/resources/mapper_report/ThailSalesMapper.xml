<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.report.mapper.ThailSalesMapper">

    <select id="queryList" resultType="top.kx.kxss.report.vo.SalesDetailResultVO">
        SELECT pro.thail_name                          AS NAME,
               c.`name`                                AS categoryName,
               count(pro.id)                           AS salesNum,
               sum(
                       IFNULL(pro.orgin_price, 0))     AS amount,
               sum(
                       IFNULL(pro.discount_amount, 0)) + sum(
                       IFNULL(pro.assessed_amount, 0)) AS discountAmount,

               sum(
                       IFNULL(pro.amount, 0)) - sum(
                       IFNULL(pro.assessed_amount, 0)) - sum(
                       IFNULL(pro.refund_amount, 0))   AS payment,
               1                                       AS isThail
        FROM pos_cash_thail pro
                 INNER JOIN pos_cash p ON p.id = pro.cash_id
                 LEFT JOIN base_thail b ON b.id = pro.thail_id
                 LEFT JOIN base_product_category c ON c.id = b.categroy_id
            ${ew.customSqlSegment}
    </select>
</mapper>
