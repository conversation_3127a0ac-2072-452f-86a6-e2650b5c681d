<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.report.mapper.GroupBuyMapper">

    <select id="page" resultType="top.kx.kxss.report.vo.GroupBuyResultVO">
        select if(pct.group_buy_type = 'MEI_TUAN', pcp.securities_number, dp.code)                     as securitiesNumber,
               if(pct.group_buy_type = 'MEI_TUAN', nsp.created_time,
                  dp.created_time)                                                                     as prepareTime,
               concat(pct.thail_name, '[', pcp.group_buy_amount,
                      ']', '[', if(pct.group_buy_type = 'MEI_TUAN', nsp.dealgroup_id, dp.sku_id), ']') as groupBuy,
               ifnull(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
               ifnull(pcp.change_amount, 0)                                                            as amount,
               ROUND(IF(bpt.fee_rate is null, 0,
                        (IFNULL(pcp.amount, 0) - ifnull(pcp.refund_amount, 0) - IFNULL(pcp.change_amount, 0)) *
                        bpt.fee_rate /
                        100),
                     2)                                                                                as feeAmount,
               ifnull(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
               ifnull(pcp.change_amount, 0) - ROUND(IF(bpt.fee_rate is null, 0,
                                                       (IFNULL(pcp.amount, 0) - ifnull(pcp.refund_amount, 0) -
                                                        IFNULL(pcp.change_amount, 0)) *
                                                       bpt.fee_rate /
                                                       100),
                                                    2)                                                 as payment,
               if(pct.group_buy_type = 'MEI_TUAN', nsp.created_by, dp.created_by)                      as createdBy,
               if(pct.group_buy_type = 'MEI_TUAN', '美团', '抖音')                                     as type,
               pc.code                                                                                 as code,
               pc.complete_time                                                                        as completeTime
        from pos_cash_payment pcp
                 left join pos_cash_thail pct on pct.cash_id = pcp.cash_id and pcp.securities_number = pct.securities_number
                 left join base_payment_type bpt on pcp.pay_type_id = bpt.id
                 left join pos_cash pc on pct.cash_id = pc.id
                 left join north_start_prepare nsp
                           on nsp.receipt_code = pct.securities_number and pct.group_buy_type = 'MEI_TUAN' and
                              nsp.is_consume = true
                 left join douyin_prepare dp
                           on dp.receipt_code = pct.securities_number and pct.group_buy_type = 'DOU_YIN' and
                              dp.is_consume = true
        where pc.delete_flag = 0
        and pct.is_check_securities = true
        and pcp.delete_flag = 0
        and pct.delete_flag = 0
        AND pc.bill_type IN ('0', '3', '4')
        <if test="model.orgId != null">
            and pc.org_id = #{model.orgId}
        </if>
        <if test="(model.startConsumeTime != null and model.startConsumeTime !='') or (model.endConsumeTime != null and model.endConsumeTime !='')">
            AND pc.bill_state IN ('0', '2', '5', '6')
        </if>
        <if test="(model.startCompleteTime != null and model.startCompleteTime !='') or (model.endCompleteTime != null and model.endCompleteTime !='')">
            AND pc.bill_state IN ('2', '5', '6')
        </if>
        <if test="model.startConsumeTime != null and model.startConsumeTime !=''">
            and (dp.consume_time >= #{model.startConsumeTime} or nsp.consume_time >= #{model.startConsumeTime})
        </if>
        <if test="model.endConsumeTime != null and model.endConsumeTime !=''">
            and (dp.consume_time &lt;= #{model.endConsumeTime} or nsp.consume_time &lt;= #{model.endConsumeTime})
        </if>
        <if test="model.startCompleteTime != null and model.startCompleteTime !=''">
            and pc.complete_time >= #{model.startCompleteTime}
        </if>
        <if test="model.endCompleteTime != null and model.endCompleteTime !=''">
            and pc.complete_time &lt;= #{model.endCompleteTime}
        </if>
        <if test="model.keyword != null and model.keyword !=''">
            and (instr(dp.code, #{model.keyword}) or instr(nsp.receipt_code, #{model.keyword}) OR instr(pct.thail_name, #{model.keyword}))
        </if>
        <if test="model.groupBuy != null and model.groupBuy !=''">
            and pct.group_buy_type = #{model.groupBuy}
        </if>
        <choose>
        <when test="model.startConsumeTime != null and model.startConsumeTime !='' and model.endConsumeTime != null and model.endConsumeTime !=''">
            order by prepareTime desc
        </when>
        <when test="model.startCompleteTime != null and model.startCompleteTime !='' and model.endCompleteTime != null and model.endCompleteTime !=''">
            order by completeTime desc
        </when>
        <otherwise>
          order by prepareTime desc
        </otherwise>
        </choose>
    </select>

    <select id="sum" resultType="top.kx.kxss.report.vo.GroupBuyResultVO">
        select
               sum(ifnull(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
                   ifnull(pcp.change_amount, 0))                                                            as amount,
               sum(ROUND(IF(bpt.fee_rate is null, 0,
                            (IFNULL(pcp.amount, 0) - ifnull(pcp.refund_amount, 0) - IFNULL(pcp.change_amount, 0)) *
                            bpt.fee_rate /
                            100),
                         2))                                                                                as feeAmount,
               sum(ifnull(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
                   ifnull(pcp.change_amount, 0) - ROUND(IF(bpt.fee_rate is null, 0,
                                                           (IFNULL(pcp.amount, 0) - ifnull(pcp.refund_amount, 0) -
                                                            IFNULL(pcp.change_amount, 0)) *
                                                           bpt.fee_rate /
                                                           100),
                                                        2))                                                 as payment
        from pos_cash_payment pcp
                 left join pos_cash_thail pct on pct.cash_id = pcp.cash_id and pcp.securities_number = pct.securities_number
                 left join base_payment_type bpt on pcp.pay_type_id = bpt.id
                 left join pos_cash pc on pct.cash_id = pc.id
                 left join north_start_prepare nsp
                           on nsp.receipt_code = pct.securities_number and pct.group_buy_type = 'MEI_TUAN' and
                              nsp.is_consume = true
                 left join douyin_prepare dp
                           on dp.receipt_code = pct.securities_number and pct.group_buy_type = 'DOU_YIN' and
                              dp.is_consume = true
        where pc.delete_flag = 0
          and pct.is_check_securities = true
          and pcp.delete_flag = 0
          and pct.delete_flag = 0
          AND pc.bill_type IN ('0', '3', '4')
        <if test="model.orgId != null">
            and pc.org_id = #{model.orgId}
        </if>
        <if test="(model.startConsumeTime != null and model.startConsumeTime !='') or (model.endConsumeTime != null and model.endConsumeTime !='')">
            AND pc.bill_state IN ('0', '2', '5', '6')
        </if>
        <if test="(model.startCompleteTime != null and model.startCompleteTime !='') or (model.endCompleteTime != null and model.endCompleteTime !='')">
            AND pc.bill_state IN ('2', '5', '6')
        </if>
        <if test="model.startConsumeTime != null and model.startConsumeTime !=''">
            and (dp.consume_time >= #{model.startConsumeTime} or nsp.consume_time >= #{model.startConsumeTime})
        </if>
        <if test="model.endConsumeTime != null and model.endConsumeTime !=''">
            and (dp.consume_time &lt;= #{model.endConsumeTime} or nsp.consume_time &lt;= #{model.endConsumeTime})
        </if>
        <if test="model.startCompleteTime != null and model.startCompleteTime !=''">
            and pc.complete_time >= #{model.startCompleteTime}
        </if>
        <if test="model.endCompleteTime != null and model.endCompleteTime !=''">
            and pc.complete_time &lt;= #{model.endCompleteTime}
        </if>
        <if test="model.keyword != null and model.keyword !=''">
            and (instr(dp.code, #{model.keyword}) or instr(nsp.receipt_code, #{model.keyword}) OR instr(pct.thail_name, #{model.keyword}))
        </if>
        <if test="model.groupBuy != null and model.groupBuy !=''">
            and pct.group_buy_type = #{model.groupBuy}
        </if>
    </select>

    <select id="list" resultType="top.kx.kxss.report.vo.GroupBuyResultVO">
        select if(pct.group_buy_type = 'MEI_TUAN', pcp.securities_number, dp.code)                     as securitiesNumber,
               if(pct.group_buy_type = 'MEI_TUAN', nsp.created_time,
                  dp.created_time)                                                                     as prepareTime,
               concat(pct.thail_name, '[', pcp.group_buy_amount,
                      ']', '[', if(pct.group_buy_type = 'MEI_TUAN', nsp.dealgroup_id, dp.sku_id), ']') as groupBuy,
               ifnull(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
               ifnull(pcp.change_amount, 0)                                                            as amount,
               ROUND(IF(bpt.fee_rate is null, 0,
                        (IFNULL(pcp.amount, 0) - ifnull(pcp.refund_amount, 0) - IFNULL(pcp.change_amount, 0)) *
                        bpt.fee_rate /
                        100),
                     2)                                                                                as feeAmount,
               ifnull(pcp.amount, 0) - IFNULL(pcp.refund_amount, 0) -
               ifnull(pcp.change_amount, 0) - ROUND(IF(bpt.fee_rate is null, 0,
                                                       (IFNULL(pcp.amount, 0) - ifnull(pcp.refund_amount, 0) -
                                                        IFNULL(pcp.change_amount, 0)) *
                                                       bpt.fee_rate /
                                                       100),
                                                    2)                                                 as payment,
               if(pct.group_buy_type = 'MEI_TUAN', nsp.created_by, dp.created_by)                      as createdBy,
               if(pct.group_buy_type = 'MEI_TUAN', '美团', '抖音')                                     as type,
               pc.code                                                                                 as code,
               pc.complete_time                                                                        as completeTime
        from pos_cash_payment pcp
                 left join pos_cash_thail pct on pct.cash_id = pcp.cash_id and pcp.securities_number = pct.securities_number
                 left join base_payment_type bpt on pcp.pay_type_id = bpt.id
                 left join pos_cash pc on pct.cash_id = pc.id
                 left join north_start_prepare nsp
                           on nsp.receipt_code = pct.securities_number and pct.group_buy_type = 'MEI_TUAN' and
                              nsp.is_consume = true
                 left join douyin_prepare dp
                           on dp.receipt_code = pct.securities_number and pct.group_buy_type = 'DOU_YIN' and
                              dp.is_consume = true
        where pc.delete_flag = 0
          and pct.is_check_securities = true
          and pcp.delete_flag = 0
          and pct.delete_flag = 0
          AND pc.bill_type IN ('0', '3', '4')
        <if test="model.orgId != null">
            and pc.org_id = #{model.orgId}
        </if>
        <if test="(model.startConsumeTime != null and model.startConsumeTime !='') or (model.endConsumeTime != null and model.endConsumeTime !='')">
            AND pc.bill_state IN ('0', '2', '5', '6')
        </if>
        <if test="(model.startCompleteTime != null and model.startCompleteTime !='') or (model.endCompleteTime != null and model.endCompleteTime !='')">
            AND pc.bill_state IN ('2', '5', '6')
        </if>
        <if test="model.startConsumeTime != null and model.startConsumeTime !=''">
            and (dp.consume_time >= #{model.startConsumeTime} or nsp.consume_time >= #{model.startConsumeTime})
        </if>
        <if test="model.endConsumeTime != null and model.endConsumeTime !=''">
            and (dp.consume_time &lt;= #{model.endConsumeTime} or nsp.consume_time &lt;= #{model.endConsumeTime})
        </if>
        <if test="model.startCompleteTime != null and model.startCompleteTime !=''">
            and pc.complete_time >= #{model.startCompleteTime}
        </if>
        <if test="model.endCompleteTime != null and model.endCompleteTime !=''">
            and pc.complete_time &lt;= #{model.endCompleteTime}
        </if>
        <if test="model.keyword != null and model.keyword !=''">
            and (instr(dp.code, #{model.keyword}) or instr(nsp.receipt_code, #{model.keyword}) OR instr(pct.thail_name, #{model.keyword}))
        </if>
        <if test="model.groupBuy != null and model.groupBuy !=''">
            and pct.group_buy_type = #{model.groupBuy}
        </if>
        <choose>
            <when test="model.startConsumeTime != null and model.startConsumeTime !='' and model.endConsumeTime != null and model.endConsumeTime !=''">
                order by prepareTime desc
            </when>
            <when test="model.startCompleteTime != null and model.startCompleteTime !='' and model.endCompleteTime != null and model.endCompleteTime !=''">
                order by completeTime desc
            </when>
            <otherwise>
                order by prepareTime desc
            </otherwise>
        </choose>
    </select>


</mapper>
