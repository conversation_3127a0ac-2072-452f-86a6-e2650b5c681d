package top.kx.kxss.pay;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.pay.entity.MchApp;
import top.kx.kxss.pay.vo.query.MchAppPageQuery;
import top.kx.kxss.pay.vo.result.IsvReconciliationInfoResultVO;
import top.kx.kxss.pay.vo.result.MchAppResultVO;

import java.util.List;

/**
 * 支付记录信息
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pay-server}",
        path = "/mchApp")
public interface MchAppApi {


    @GetMapping("/getByAppId")
    R<MchApp> getByAppId(@RequestParam("appId") String appId);

    @PostMapping("/query")
    R<List<MchAppResultVO>> query(@RequestBody MchAppPageQuery query);

    @PostMapping("/queryReconciliationParam")
    R<IsvReconciliationInfoResultVO> queryReconciliationParam(@RequestParam("appId") String appId,
                                                           @RequestParam("ifCode") String ifCode);
}
