package top.kx.kxss.pay;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.constant.Constants;
import top.kx.kxss.pay.entity.PayOrder;
import top.kx.kxss.pay.vo.query.ReconciliationPageQuery;
import top.kx.kxss.pay.vo.query.ReconciliationQuery;
import top.kx.kxss.pay.vo.result.PayOrderResultVO;
import top.kx.kxss.pay.vo.result.ReconciliationResultVO;

/**
 * 支付记录信息
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pay-server}",
        path = "/payOrder")
public interface PayOrderApi {


    @ApiOperation("查询订单信息")
    @GetMapping("/getByPayOrderId")
    R<PayOrder> getByPayOrderId(@RequestParam("payOrderId") String payOrderId);

    @ApiOperation("查询流水统计-日统计或者月统计")
    @PostMapping("/reconciliationStatistics")
    R<Page<ReconciliationResultVO>> reconciliationStatistics(@RequestBody PageParams<ReconciliationPageQuery> params);


    @ApiOperation("查询流水汇总-指定时间内的流水汇总")
    @PostMapping("/reconciliation")
    R<ReconciliationResultVO> reconciliation(@RequestBody ReconciliationQuery params);


    @ApiOperation("查询流水列表-指定时间内的流水列表")
    @PostMapping("/reconciliationPage")
    R<Page<PayOrderResultVO>> reconciliationPage(@RequestBody PageParams<ReconciliationQuery> params);


    @ApiOperation("根据商户单号查询订单信息")
    @GetMapping("/getByMchOrderNo")
    R<PayOrder> getByMchOrderNo(@RequestParam("mchOrderNo") String mchOrderNo);
}
