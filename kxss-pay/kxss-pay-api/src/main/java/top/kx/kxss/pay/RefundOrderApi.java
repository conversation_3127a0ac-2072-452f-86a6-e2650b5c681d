package top.kx.kxss.pay;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.pay.entity.RefundOrder;

/**
 * 支付记录信息
 *
 * <AUTHOR>
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.tenant-server:kxss-pay-server}",
        path = "/payOrder")
public interface RefundOrderApi {


    @GetMapping("/getByMchRefundNo")
    R<RefundOrder> getByMchOrderNo(@RequestParam("mchRefundNo") String mchRefundNo);
}
