package top.kx.kxss.context;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.kxss.model.*;
import top.kx.kxss.model.constant.PayConstant;
import top.kx.kxss.pay.entity.IsvInfo;
import top.kx.kxss.pay.entity.MchApp;
import top.kx.kxss.pay.entity.MchInfo;
import top.kx.kxss.pay.entity.PayInterfaceConfig;
import top.kx.kxss.pay.service.*;
import top.kx.kxss.pay.vo.model.params.IsvParams;
import top.kx.kxss.pay.vo.model.params.IsvsubMchParams;
import top.kx.kxss.pay.vo.model.params.NormalMchParams;
import top.kx.kxss.pay.vo.model.params.alipay.AlipayIsvParams;
import top.kx.kxss.pay.vo.model.params.alipay.AlipayNormalMchParams;
import top.kx.kxss.pay.vo.model.params.pppay.PppayNormalMchParams;
import top.kx.kxss.pay.vo.model.params.wxpay.WxpayIsvParams;
import top.kx.kxss.pay.vo.model.params.wxpay.WxpayNormalMchParams;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 商户/服务商 配置信息上下文服务
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ConfigContextService {

    /**
     * <商户ID, 商户配置项>
     **/
    private static final Map<String, MchInfoConfigContext> MCH_INFO_CONFIG_CONTEXT_MAP = new ConcurrentHashMap<>();

    /**
     * <应用ID, 商户配置上下文>
     **/
    private static final Map<String, MchAppConfigContext> MCH_APP_CONFIG_CONTEXT_MAP = new ConcurrentHashMap<>();

    /**
     * <服务商号, 服务商配置上下文>
     **/
    private static final Map<String, IsvConfigContext> ISV_CONFIG_CONTEXT_MAP = new ConcurrentHashMap<>();

    @Autowired
    private MchInfoService mchInfoService;
    @Autowired
    private MchAppService mchAppService;
    @Autowired
    private IsvInfoService isvInfoService;
    @Autowired
    private PayInterfaceConfigService payInterfaceConfigService;


    /**
     * 获取 [商户配置信息]
     **/
    public MchInfoConfigContext getMchInfoConfigContext(String mchNo) {

        MchInfoConfigContext mchInfoConfigContext = MCH_INFO_CONFIG_CONTEXT_MAP.get(mchNo);

        //无此数据， 需要初始化
        if (mchInfoConfigContext == null) {
            initMchInfoConfigContext(mchNo);
        }

        return MCH_INFO_CONFIG_CONTEXT_MAP.get(mchNo);
    }

    /**
     * 获取 [商户应用支付参数配置信息]
     **/
    public MchAppConfigContext getMchAppConfigContext(String mchNo, String appId) {

        MchAppConfigContext mchAppConfigContext = MCH_APP_CONFIG_CONTEXT_MAP.get(appId);

        //无此数据， 需要初始化
        if (mchAppConfigContext == null) {
            initMchAppConfigContext(mchNo, appId);
        }

        return MCH_APP_CONFIG_CONTEXT_MAP.get(appId);
    }

    /**
     * 获取 [ISV支付参数配置信息]
     **/
    public IsvConfigContext getIsvConfigContext(String isvNo) {

        IsvConfigContext isvConfigContext = ISV_CONFIG_CONTEXT_MAP.get(isvNo);

        //无此数据， 需要初始化
        if (isvConfigContext == null) {
            initIsvConfigContext(isvNo);
        }

        return ISV_CONFIG_CONTEXT_MAP.get(isvNo);
    }


    /**
     * 初始化 [商户配置信息]
     **/
    public synchronized void initMchInfoConfigContext(String mchNo) {

        // 当前系统不进行缓存
        if (!isCache()) {
            return;
        }

        //商户主体信息
        MchInfo mchInfo = mchInfoService.getByMchNo(mchNo);
        // 查询不到商户主体， 可能已经删除
        if (mchInfo == null) {

            MchInfoConfigContext mchInfoConfigContext = MCH_INFO_CONFIG_CONTEXT_MAP.get(mchNo);

            // 删除所有的商户应用
            if (mchInfoConfigContext != null) {
                mchInfoConfigContext.getAppMap().forEach((k, v) -> MCH_APP_CONFIG_CONTEXT_MAP.remove(k));
            }

            MCH_INFO_CONFIG_CONTEXT_MAP.remove(mchNo);
            return;
        }

        MchInfoConfigContext mchInfoConfigContext = new MchInfoConfigContext();

        // 设置商户信息
        mchInfoConfigContext.setMchNo(mchInfo.getMchNo());
        mchInfoConfigContext.setMchType(mchInfo.getType());
        mchInfoConfigContext.setMchInfo(mchInfo);
        mchAppService.list(MchApp.gw().eq(MchApp::getMchNo, mchNo)).forEach(mchApp -> {

            //1. 更新商户内appId集合
            mchInfoConfigContext.putMchApp(mchApp);

            MchAppConfigContext mchAppConfigContext = MCH_APP_CONFIG_CONTEXT_MAP.get(mchApp.getAppId());
            if (mchAppConfigContext != null) {
                mchAppConfigContext.setMchApp(mchApp);
                mchAppConfigContext.setMchNo(mchInfo.getMchNo());
                mchAppConfigContext.setMchType(mchInfo.getType());
                mchAppConfigContext.setMchInfo(mchInfo);
            }
        });

        MCH_INFO_CONFIG_CONTEXT_MAP.put(mchNo, mchInfoConfigContext);
    }

    /**
     * 初始化 [商户应用支付参数配置信息]
     **/
    public synchronized void initMchAppConfigContext(String mchNo, String appId) {

        // 当前系统不进行缓存
        if (!isCache()) {
            return;
        }

        // 获取商户的配置信息
        MchInfoConfigContext mchInfoConfigContext = getMchInfoConfigContext(mchNo);
        // 商户信息不存在
        if (mchInfoConfigContext == null) {
            return;
        }

        // 查询商户应用信息主体
        MchApp dbMchApp = mchAppService.getByAppId(appId);

        //DB已经删除
        if (dbMchApp == null) {
            //清除缓存信息
            MCH_APP_CONFIG_CONTEXT_MAP.remove(appId);
            //清除主体信息中的appId
            mchInfoConfigContext.getAppMap().remove(appId);
            return;
        }


        // 商户应用mchNo 与参数不匹配
        if (!dbMchApp.getMchNo().equals(mchNo)) {
            return;
        }

        //更新商户信息主体中的商户应用
        mchInfoConfigContext.putMchApp(dbMchApp);

        //商户主体信息
        MchInfo mchInfo = mchInfoConfigContext.getMchInfo();
        MchAppConfigContext mchAppConfigContext = new MchAppConfigContext();

        // 设置商户信息
        mchAppConfigContext.setAppId(appId);
        mchAppConfigContext.setMchNo(mchInfo.getMchNo());
        mchAppConfigContext.setMchType(mchInfo.getType());
        mchAppConfigContext.setMchInfo(mchInfo);
        mchAppConfigContext.setMchApp(dbMchApp);

        // 查询商户的所有支持的参数配置
        List<PayInterfaceConfig> allConfigList = payInterfaceConfigService.list(PayInterfaceConfig.gw()
                .select(PayInterfaceConfig::getIfCode, PayInterfaceConfig::getIfParams)
                .eq(PayInterfaceConfig::getState, PayConstant.YES)
                .eq(PayInterfaceConfig::getInfoType, PayConstant.INFO_TYPE_MCH_APP)
                .eq(PayInterfaceConfig::getInfoId, appId)
        );

        // 普通商户
        if (mchInfo.getType() == PayConstant.MCH_TYPE_NORMAL) {

            for (PayInterfaceConfig payInterfaceConfig : allConfigList) {
                mchAppConfigContext.getNormalMchParamsMap().put(
                        payInterfaceConfig.getIfCode(),
                        NormalMchParams.factory(payInterfaceConfig.getIfCode(), payInterfaceConfig.getIfParams())
                );
            }

            //放置alipay client

            AlipayNormalMchParams alipayParams = mchAppConfigContext.getNormalMchParamsByIfCode(PayConstant.IF_CODE.ALIPAY, AlipayNormalMchParams.class);
            if (alipayParams != null) {
                mchAppConfigContext.setAlipayClientWrapper(AlipayClientWrapper.buildAlipayClientWrapper(alipayParams));
            }

            //放置 wxJavaService
            WxpayNormalMchParams wxpayParams = mchAppConfigContext.getNormalMchParamsByIfCode(PayConstant.IF_CODE.WXPAY, WxpayNormalMchParams.class);
            if (wxpayParams != null) {
                mchAppConfigContext.setWxServiceWrapper(WxServiceWrapper.buildWxServiceWrapper(wxpayParams));
            }

            //放置 pay client
            PppayNormalMchParams ppPayMchParams = mchAppConfigContext.getNormalMchParamsByIfCode(PayConstant.IF_CODE.PPPAY, PppayNormalMchParams.class);
            if (ppPayMchParams != null) {
                mchAppConfigContext.setPayWrapper(PayWrapper.buildPaypalWrapper(ppPayMchParams));
            }
        } else { //服务商模式商户
            for (PayInterfaceConfig payInterfaceConfig : allConfigList) {
                mchAppConfigContext.getIsvsubMchParamsMap().put(
                        payInterfaceConfig.getIfCode(),
                        IsvsubMchParams.factory(payInterfaceConfig.getIfCode(), payInterfaceConfig.getIfParams())
                );
            }

            //放置 当前商户的 服务商信息
            mchAppConfigContext.setIsvConfigContext(getIsvConfigContext(mchInfo.getIsvNo()));

        }

        MCH_APP_CONFIG_CONTEXT_MAP.put(appId, mchAppConfigContext);
    }


    /**
     * 初始化 [ISV支付参数配置信息]
     **/
    public synchronized void initIsvConfigContext(String isvNo) {

        // 当前系统不进行缓存
        if (!isCache()) {
            return;
        }

        //查询出所有商户的配置信息并更新
        List<String> mchNoList = new ArrayList<>();
        mchInfoService.list(MchInfo.gw().select(MchInfo::getMchNo).eq(MchInfo::getIsvNo, isvNo)).forEach(r -> mchNoList.add(r.getMchNo()));

        // 查询出所有 所属当前服务商的所有应用集合
        List<String> mchAppIdList = new ArrayList<>();
        if (!mchNoList.isEmpty()) {
            mchAppService.list(MchApp.gw().select(MchApp::getAppId).in(MchApp::getMchNo, mchNoList)).forEach(r -> mchAppIdList.add(r.getAppId()));
        }

        IsvConfigContext isvConfigContext = new IsvConfigContext();
        IsvInfo isvInfo = isvInfoService.getByIsvNo(isvNo);
        if (isvInfo == null) {

            for (String appId : mchAppIdList) {
                //将更新已存在缓存的商户配置信息 （每个商户下存储的为同一个 服务商配置的对象指针）
                MchAppConfigContext mchAppConfigContext = MCH_APP_CONFIG_CONTEXT_MAP.get(appId);
                if (mchAppConfigContext != null) {
                    mchAppConfigContext.setIsvConfigContext(null);
                }
            }

            // 服务商有商户不可删除， 此处不再更新商户下的配置信息
            ISV_CONFIG_CONTEXT_MAP.remove(isvNo);
            return;
        }

        // 设置商户信息
        isvConfigContext.setIsvNo(isvInfo.getIsvNo());
        isvConfigContext.setIsvInfo(isvInfo);

        // 查询商户的所有支持的参数配置
        List<PayInterfaceConfig> allConfigList = payInterfaceConfigService.list(PayInterfaceConfig.gw()
                .select(PayInterfaceConfig::getIfCode, PayInterfaceConfig::getIfParams)
                .eq(PayInterfaceConfig::getState, PayConstant.YES)
                .eq(PayInterfaceConfig::getInfoType, PayConstant.INFO_TYPE_ISV)
                .eq(PayInterfaceConfig::getInfoId, isvNo)
        );

        for (PayInterfaceConfig payInterfaceConfig : allConfigList) {
            isvConfigContext.getIsvParamsMap().put(
                    payInterfaceConfig.getIfCode(),
                    IsvParams.factory(payInterfaceConfig.getIfCode(), payInterfaceConfig.getIfParams())
            );
        }

        //放置alipay client
        AlipayIsvParams alipayParams = isvConfigContext.getIsvParamsByIfCode(PayConstant.IF_CODE.ALIPAY, AlipayIsvParams.class);
        if (alipayParams != null) {
            isvConfigContext.setAlipayClientWrapper(AlipayClientWrapper.buildAlipayClientWrapper(alipayParams));
        }

        //放置 wxJavaService
        WxpayIsvParams wxpayParams = isvConfigContext.getIsvParamsByIfCode(PayConstant.IF_CODE.WXPAY, WxpayIsvParams.class);
        if (wxpayParams != null) {
            isvConfigContext.setWxServiceWrapper(WxServiceWrapper.buildWxServiceWrapper(wxpayParams));
        }

        ISV_CONFIG_CONTEXT_MAP.put(isvNo, isvConfigContext);

        //查询出所有商户的配置信息并更新
        for (String appId : mchAppIdList) {
            //将更新已存在缓存的商户配置信息 （每个商户下存储的为同一个 服务商配置的对象指针）
            MchAppConfigContext mchAppConfigContext = MCH_APP_CONFIG_CONTEXT_MAP.get(appId);
            if (mchAppConfigContext != null) {
                mchAppConfigContext.setIsvConfigContext(isvConfigContext);
            }
        }
    }

    private boolean isCache() {
        return PayConfigService.IS_USE_CACHE;
    }

}
