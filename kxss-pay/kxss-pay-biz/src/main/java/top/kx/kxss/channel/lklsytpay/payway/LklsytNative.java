package top.kx.kxss.channel.lklsytpay.payway;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lkl.laop.sdk.LKLSDK;
import com.lkl.laop.sdk.request.V3CcssCounterOrderSpecialCreateRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.channel.lklpay.utils.LklHttpUtil;
import top.kx.kxss.channel.lklsytpay.LklsytpayPaymentService;
import top.kx.kxss.model.AbstractRS;
import top.kx.kxss.model.MchAppConfigContext;
import top.kx.kxss.model.msg.ChannelRetMsg;
import top.kx.kxss.model.payorder.UnifiedOrderRQ;
import top.kx.kxss.model.payway.LklsytNativeOrderRS;
import top.kx.kxss.pay.entity.PayOrder;
import top.kx.kxss.pay.vo.model.params.lklpay.LklpayIsvParams;
import top.kx.kxss.pay.vo.model.params.lklsytpay.LklsytpayIsvParams;
import top.kx.kxss.pay.vo.model.params.lklsytpay.LklsytpayIsvsubMchParams;
import top.kx.kxss.utils.ApiResBuilder;

/**
 * 拉卡拉聚合码
 *
 * <AUTHOR>
 */
@Service("lklPaymentByLklsytNativeService")
@Slf4j
public class LklsytNative extends LklsytpayPaymentService {

    @Override
    public String preCheck(UnifiedOrderRQ rq, PayOrder payOrder) {
        return null;
    }

    @Override
    public AbstractRS pay(UnifiedOrderRQ rq, PayOrder payOrder, MchAppConfigContext mchAppConfigContext) throws Exception {
        String logPrefix = "【拉卡拉(聚合码)支付】";
        LklsytNativeOrderRS res = ApiResBuilder.buildSuccess(LklsytNativeOrderRS.class);
        ChannelRetMsg channelRetMsg = new ChannelRetMsg();
        assert res != null;
        res.setChannelRetMsg(channelRetMsg);

        LklsytpayIsvParams isvParams = (LklsytpayIsvParams) configContextQueryService.queryIsvParams(mchAppConfigContext.getMchInfo().getIsvNo(), getIfCode());
        LklsytpayIsvsubMchParams isvSubMchParams = (LklsytpayIsvsubMchParams) configContextQueryService.queryIsvsubMchParams(mchAppConfigContext.getMchNo(), mchAppConfigContext.getAppId(), getIfCode());
        LklHttpUtil.doInit(BeanUtil.copyProperties(isvParams, LklpayIsvParams.class));
        V3CcssCounterOrderSpecialCreateRequest request = buildQrCodeOrderRequest(payOrder, isvSubMchParams);
        request.setNotifyUrl(getNotifyUrl(payOrder.getPayOrderId()));
        log.info("{},请求参数:{}", logPrefix, JSON.toJSONString(request));
        String response = LKLSDK.httpPost(request);
        log.info("{},返回结果:{}", logPrefix, response);
        JSONObject resJSON = JSONObject.parseObject(response);
        String respCode = resJSON.getString("code");
        //应答信息
        String respMsg = resJSON.getString("msg");
        try {
            if ("000000".equals(respCode)) {
                JSONObject respData = resJSON.getObject("resp_data", JSONObject.class);
                String counterUrl = respData.getString("counter_url");
                res.setPayData(counterUrl);
                res.setPayUrl(counterUrl);
                channelRetMsg.setNeedQuery(true);
                channelRetMsg.setChannelState(ChannelRetMsg.ChannelState.WAITING);
            } else {
                channelExceptionService.saveChannelException(payOrder, JSON.toJSONString(request), JSON.toJSONString(response));
                res.setPayData(respMsg);
                channelRetMsg.setChannelState(ChannelRetMsg.ChannelState.CONFIRM_FAIL);
                channelRetMsg.setChannelErrCode(respCode);
                channelRetMsg.setChannelErrMsg(respMsg);
            }
        } catch (Exception e) {
            channelExceptionService.saveChannelException(payOrder, JSON.toJSONString(request), JSON.toJSONString(response));
            channelRetMsg.setChannelErrCode(respCode);
            channelRetMsg.setChannelErrMsg(respMsg);
        }
        return res;
    }
}
