package top.kx.kxss.rabbitmq.mq;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.pay.service.impl.PayOrderDivisionProcessService;
import top.kx.kxss.rabbitmq.model.PayOrderDivisionMQ;

/**
 * 接收MQ消息
 * 业务： 支付订单分账处理逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class PayOrderDivisionMQReceiver implements PayOrderDivisionMQ.IMQReceiver {

    @Autowired
    private PayOrderDivisionProcessService payOrderDivisionProcessService;

    @Override
    public void receive(PayOrderDivisionMQ.MsgPayload payload) {

        try {
            ContextUtil.setDefTenantId();
            log.info("接收订单分账通知MQ, msg={}", payload.toString());
            payOrderDivisionProcessService.processPayOrderDivision(payload.getPayOrderId(), payload.getUseSysAutoDivisionReceivers(), payload.getReceiverList(), payload.getIsResend());

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        } finally {
            ContextUtil.remove();
        }
    }

}
