package top.kx.kxss.channel.leshuapay;

import cn.leshua.req.LeshuaTransQueryReq;
import cn.leshua.res.LeshuaTransQueryRes;
import cn.leshua.service.LeshuaTransQueryService;
import cn.leshua.util.LeshuaUtils;
import cn.leshua.util.RandomStringGenerator;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.kxss.channel.IPayOrderQueryService;
import top.kx.kxss.channel.saobei.utils.SaobeiHttpUtil;
import top.kx.kxss.context.ConfigContextQueryService;
import top.kx.kxss.model.MchAppConfigContext;
import top.kx.kxss.model.constant.PayConstant;
import top.kx.kxss.model.msg.ChannelRetMsg;
import top.kx.kxss.pay.entity.PayOrder;
import top.kx.kxss.pay.service.TChannelExceptionService;
import top.kx.kxss.pay.vo.model.params.leshuapay.LeshuapayNormalMchParams;

import java.util.Map;

/**
 * 乐刷查单接口
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LeshuapayPayOrderQueryService implements IPayOrderQueryService {

    @Autowired
    private ConfigContextQueryService configContextQueryService;
    @Autowired
    private TChannelExceptionService channelExceptionService;

    @Override
    public String getIfCode() {
        return PayConstant.IF_CODE.LESHUAPAY;
    }

    @Override
    public ChannelRetMsg query(PayOrder payOrder, MchAppConfigContext mchAppConfigContext) {
        String orderType = SaobeiHttpUtil.getOrderTypeByCommon(payOrder.getWayCode());
        String logPrefix = "【乐刷(" + orderType + ")查单】";
        LeshuapayNormalMchParams normalMchParams = (LeshuapayNormalMchParams) configContextQueryService.queryNormalMchParams(mchAppConfigContext.getMchInfo().getMchNo(),
                mchAppConfigContext.getAppId(),
                getIfCode());
        LeshuaTransQueryService service = new LeshuaTransQueryService();
        LeshuaTransQueryReq reqData = new LeshuaTransQueryReq();
        reqData.setService("query_status");
        reqData.setMerchant_id(normalMchParams.getMerchantNo());
        reqData.setThird_order_id(payOrder.getPayOrderId());
        reqData.setNonce_str(RandomStringGenerator.genUUIDString());
        log.info("{} payorderId:{}, 请求参数:{}", logPrefix, payOrder.getPayOrderId(), JSON.toJSONString(reqData));
        try {
            Map<String, String> reqMap = LeshuaUtils.toMap(reqData);
            LeshuaTransQueryRes response = service.request(reqMap,
                    normalMchParams.getMerchantKey());
            log.info("{} payorderId:{}, 返回结果:{}", logPrefix, payOrder.getPayOrderId(), JSON.toJSONString(response));
            String tradeState = response.getStatus();
            String payType = response.getPay_way();
//            if ("0".equals(response.getResult_code())) {
//                if ("SUCCESS".equals(tradeState)
//                        || "REFUND".equals(tradeState)
//                ) {
//                    ChannelRetMsg channelRetMsg = ChannelRetMsg.confirmSuccess(response.getOut_trade_no());
//                    channelRetMsg.setChannelOrderId(response.getOut_trade_no());
//                    channelRetMsg.setChannelUserId(response.getUser_id());
//                    channelRetMsg.setPayType(SaobeipayConfig.getPayType(payType));
//                    return channelRetMsg;
//                } else if ("NOTPAY".equals(tradeState)
//                        || "USERPAYING".equals(tradeState)) {
//                    ChannelRetMsg waiting = ChannelRetMsg.waiting();
//                    waiting.setNeedQuery(true);
//                    waiting.setChannelUserId(response.getUser_id());
//                    waiting.setPayType(SaobeipayConfig.getPayType(payType));
//                    return waiting;
//                } else if ("PAYERROR".equals(tradeState)
//                        || "NOPAY".equals(tradeState)) {
//                    channelExceptionService.saveChannelException(payOrder, JSON.toJSONString(request), JSON.toJSONString(response));
//                    ChannelRetMsg channelRetMsg = ChannelRetMsg.confirmFail(response.getOut_trade_no());
//                    channelRetMsg.setChannelUserId(response.getUser_id());
//                    channelRetMsg.setPayType(SaobeipayConfig.getPayType(payType));
//                    return channelRetMsg;
//                } else if ("CLOSED".equals(tradeState)
//                        || "REVOKED".equals(tradeState)) {
//                    channelExceptionService.saveChannelException(payOrder, JSON.toJSONString(request), JSON.toJSONString(response));
//                    return ChannelRetMsg.confirmClose(response.getOut_trade_no());
//                }
//
//
////                //支付成功
////                if ("01".equals(response.getResult_code())) {
////                    return ChannelRetMsg.confirmSuccess(response.getOut_trade_no());
////                } else if ("02".equals(response.getResult_code())) {
////                    //支付失败
////                    return ChannelRetMsg.confirmFail(response.getOut_trade_no());
////                } else if ("03".equals(response.getResult_code())) {
////                    //支付中
////                    return ChannelRetMsg.waiting();
////                }
//            } else {
////                if (!payOrder.getMchOrderNo().startsWith("GR")) {
////                    channelExceptionService.saveChannelException(payOrder, JSON.toJSONString(request), JSON.toJSONString(response));
////                    return ChannelRetMsg.confirmFail(response.getOut_trade_no());
////                }
//            }
            return ChannelRetMsg.unknown();
        } catch (Exception e) {
            return ChannelRetMsg.sysError(e.getMessage());
        }
    }

}
