package top.kx.kxss.channel.leshuapay;

import cn.leshua.req.LeshuaMicroPayReq;
import cn.leshua.util.RandomStringGenerator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.channel.AbstractPaymentService;
import top.kx.kxss.model.AbstractRS;
import top.kx.kxss.model.MchAppConfigContext;
import top.kx.kxss.model.constant.PayConstant;
import top.kx.kxss.model.payorder.UnifiedOrderRQ;
import top.kx.kxss.pay.entity.PayOrder;
import top.kx.kxss.pay.vo.model.params.leshuapay.LeshuapayNormalMchParams;
import top.kx.kxss.utils.PaywayUtil;

/**
 * 支付接口： 乐刷
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LeshuapayPaymentService extends AbstractPaymentService {

    @Override
    public String getIfCode() {
        return PayConstant.IF_CODE.LESHUAPAY;
    }

    @Override
    public boolean isSupport(String wayCode) {
        return true;
    }

    @Override
    public String preCheck(UnifiedOrderRQ rq, PayOrder payOrder) {
        return PaywayUtil.getRealPaywayService(this, payOrder.getWayCode()).preCheck(rq, payOrder);
    }

    @Override
    public AbstractRS pay(UnifiedOrderRQ rq, PayOrder payOrder, MchAppConfigContext mchAppConfigContext) throws Exception {
        return PaywayUtil.getRealPaywayService(this, payOrder.getWayCode()).pay(rq, payOrder, mchAppConfigContext);
    }


    /**
     * 统一下单请求数据
     *
     * @param payOrder
     * @return
     */
    public LeshuaMicroPayReq buildUnifiedOrderRequest(PayOrder payOrder, LeshuapayNormalMchParams normalMchParams) {
        String payOrderId = payOrder.getPayOrderId();
        // 统一下单请求对象
        LeshuaMicroPayReq request = new LeshuaMicroPayReq();
        request.setService("upload_authcode");
        request.setMerchant_id(normalMchParams.getMerchantNo());
        request.setSn(normalMchParams.getTermNo());
        //终端流水号，填写商户系统的订单号
        request.setThird_order_id(payOrderId);
        //金额，单位分
        request.setAmount(String.valueOf(payOrder.getAmount().longValue()));
        request.setBody(payOrder.getBody());
        request.setNotify_url(payOrder.getNotifyUrl());
        request.setNonce_str(RandomStringGenerator.genUUIDString());
        return request;
    }

}
