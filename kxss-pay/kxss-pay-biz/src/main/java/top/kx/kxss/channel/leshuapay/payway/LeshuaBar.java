package top.kx.kxss.channel.leshuapay.payway;

import cn.leshua.req.LeshuaMicroPayReq;
import cn.leshua.res.LeshuaMicroPayRes;
import cn.leshua.service.LeshuaMicroPayService;
import cn.leshua.util.LeshuaUtils;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.kx.basic.exception.BizException;
import top.kx.kxss.channel.leshuapay.LeshuapayPaymentService;
import top.kx.kxss.model.AbstractRS;
import top.kx.kxss.model.MchAppConfigContext;
import top.kx.kxss.model.msg.ChannelRetMsg;
import top.kx.kxss.model.payorder.UnifiedOrderRQ;
import top.kx.kxss.model.payway.LeshuaBarOrderRQ;
import top.kx.kxss.model.payway.LeshuaBarOrderRS;
import top.kx.kxss.pay.entity.PayOrder;
import top.kx.kxss.pay.vo.model.params.leshuapay.LeshuapayNormalMchParams;
import top.kx.kxss.utils.ApiResBuilder;

import java.util.Map;

/**
 * 乐刷条码支付
 *
 * <AUTHOR>
 */
@Service("leshuaPaymentByLeshuaBarService")
@Slf4j
public class LeshuaBar extends LeshuapayPaymentService {

    @Override
    public String preCheck(UnifiedOrderRQ rq, PayOrder payOrder) {
        LeshuaBarOrderRQ bizRq = (LeshuaBarOrderRQ) rq;
        if (StringUtils.isEmpty(bizRq.getAuthCode())) {
            throw new BizException("用户支付条码[authCode]不可为空");
        }
        return null;
    }

    @Override
    public AbstractRS pay(UnifiedOrderRQ rq, PayOrder payOrder, MchAppConfigContext mchAppConfigContext) throws Exception {
        long start = System.currentTimeMillis();

        String logPrefix = "【乐刷条码(unionpay)支付】";
        LeshuaBarOrderRQ bizRq = (LeshuaBarOrderRQ) rq;
        LeshuaBarOrderRS res = ApiResBuilder.buildSuccess(LeshuaBarOrderRS.class);
        ChannelRetMsg channelRetMsg = new ChannelRetMsg();
        assert res != null;
        res.setChannelRetMsg(channelRetMsg);
        LeshuapayNormalMchParams normalMchParams = (LeshuapayNormalMchParams) configContextQueryService.queryNormalMchParams(mchAppConfigContext.getMchNo(), mchAppConfigContext.getAppId(), getIfCode());
        log.info("{},支付查询执行时间：{}", logPrefix, (System.currentTimeMillis() - start));
        LeshuaMicroPayReq reqData = buildUnifiedOrderRequest(payOrder, normalMchParams);
        reqData.setAuth_code(bizRq.getAuthCode());
        reqData.setAttach(payOrder.getExtParam());
        reqData.setClient_ip(StringUtils.defaultIfEmpty(rq.getClientIp(), "0.0.0.0"));
        Map<String, String> reqMap = LeshuaUtils.toMap(reqData);
        LeshuaMicroPayService leshuaMicroPayService = new LeshuaMicroPayService();
        LeshuaMicroPayRes payRes = leshuaMicroPayService.request(reqMap,
                normalMchParams.getMerchantKey());
        log.info("{},返回结果:{}", logPrefix, JSON.toJSONString(payRes));
        payOrder.setChannelOrderNo(payRes != null ? payRes.getLeshua_order_id() : null);
        payOrder.setChannelUser(payRes != null ? payRes.getOpenid() : null);
        channelRetMsg.setChannelOrderId(payOrder.getChannelOrderNo());
        channelRetMsg.setChannelUserId(payOrder.getChannelUser());
        if ("0".equals(payRes.getResp_code()) && "0".equals(payRes.getResult_code())) {
            res.setPayData(JSON.toJSONString(payRes));
            channelRetMsg.setChannelState(ChannelRetMsg.ChannelState.CONFIRM_SUCCESS);
        } else if ("-4004".equals(payRes.getResult_code())) {
            channelExceptionService.saveChannelException(payOrder, JSON.toJSONString(reqData), JSON.toJSONString(payRes));
            res.setPayData(payRes.getError_msg());
            channelRetMsg.setNeedQuery(true);
            channelRetMsg.setChannelState(ChannelRetMsg.ChannelState.WAITING);
        } else {
            channelExceptionService.saveChannelException(payOrder, JSON.toJSONString(reqData), JSON.toJSONString(payRes));
            res.setPayData(payRes.getError_msg());
            channelRetMsg.setChannelState(ChannelRetMsg.ChannelState.CONFIRM_FAIL);
            channelRetMsg.setChannelErrCode(payRes.getResult_code());
            channelRetMsg.setChannelErrMsg(payRes.getError_msg());
        }
        return res;
    }
}
