package top.kx.kxss.rabbitmq.mq;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.pay.service.PayConfigService;
import top.kx.kxss.rabbitmq.model.ResetAppConfigMQ;

/**
 * 接收MQ消息
 * 业务： 更新系统配置参数
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ResetAppConfigMQReceiver implements ResetAppConfigMQ.IMQReceiver {

    @Autowired
    private PayConfigService sysConfigService;

    @Override
    public void receive(ResetAppConfigMQ.MsgPayload payload) {

        try {
            ContextUtil.setDefTenantId();
            log.info("成功接收更新系统配置的订阅通知, msg={}", payload);
            sysConfigService.initDBConfig(payload.getGroupKey());
            log.info("系统配置静态属性已重置");
        } finally {
            ContextUtil.remove();
        }
    }
}
