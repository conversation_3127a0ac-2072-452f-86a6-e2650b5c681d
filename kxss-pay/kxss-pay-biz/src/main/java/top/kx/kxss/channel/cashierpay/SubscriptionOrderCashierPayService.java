package top.kx.kxss.channel.cashierpay;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.query.DealOrderPaymentQuery;
import top.kx.kxss.channel.AbstractCashierPayService;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.model.enumeration.base.OrderSourceEnum;
import top.kx.kxss.model.enumeration.pos.DealOrderStatusEnum;
import top.kx.kxss.pay.entity.PayOrder;
import top.kx.kxss.system.entity.subscription.order.SubscriptionOrder;
import top.kx.kxss.system.vo.query.UpdateCashPaymentQuery;

/**
 * 内部收银台支付
 * 团购充值订单
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SubscriptionOrderCashierPayService extends AbstractCashierPayService {


    @Override
    public void pay(PayOrder payOrder, String payType, String channelUser) throws Exception {
        ContextUtil.setDefTenantId();
        JSONObject params = JSONObject.parseObject(payOrder.getExtParam(), JSONObject.class);
        boolean lock = false;
        try {
            lock = distributedLock.lock(payOrder.getPayOrderId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode(), 0);
            if (!lock) {
                return;
            }
            R<SubscriptionOrder> subscriptionOrderR = subscriptionOrderApi.getByOrderId(params.getLong("tmpOrderId"));
            if (subscriptionOrderR == null) {
                return;
            }
            ArgumentAssert.isFalse(!subscriptionOrderR.getIsSuccess(), "支付记录不存在!");
            if (subscriptionOrderR.getData() != null) {
                SubscriptionOrder subscriptionOrder = subscriptionOrderR.getData();
                if (subscriptionOrder == null) {
                    return;
                }
                if (!subscriptionOrder.getStatus().equals(DealOrderStatusEnum.COMPLETE.getCode())) {
                    subscriptionOrderApi.payment(DealOrderPaymentQuery.builder()
                            .orderId(subscriptionOrder.getId())
                            .paymentId(0L)
                            .platformId(payOrder.getPayOrderId())
                            .amount(payOrder.getAmount())
                            .orderSourceEnum(OrderSourceEnum.get(params.getString("orderSource")))
                            .build());
                }
            }
        } finally {
            if (lock) {
                distributedLock.releaseLock(payOrder.getPayOrderId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode());
            }
        }
    }


    @Override
    public void modifyStatus(PayOrder payOrder, String status, String payType, String channelUser) {
        JSONObject params = JSONObject.parseObject(payOrder.getExtParam(), JSONObject.class);
        ContextUtil.setDefTenantId();
        boolean lock = false;
        try {
            lock = distributedLock.lock(payOrder.getPayOrderId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode(), 0);
            if (!lock) {
                return;
            }
            if (status.equals(PosCashPaymentStatusEnum.PAY_FAIL.getCode())) {
                status = DealOrderStatusEnum.PAY_FAIL.getCode();
            } else if (status.equals(PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())) {
                status = DealOrderStatusEnum.COMPLETE.getCode();
            } else if (status.equals(PosCashPaymentStatusEnum.CLOSE.getCode())) {
                return;
            } else if (status.equals(PosCashPaymentStatusEnum.REFUNDED.getCode())) {
                status = DealOrderStatusEnum.REFUNDED.getCode();
            }
            subscriptionOrderApi.updatePayment(UpdateCashPaymentQuery.builder()
                    .id(params.getLong("tmpOrderId"))
                    .status(status)
                    .build());
        } finally {
            if (lock) {
                distributedLock.releaseLock(payOrder.getPayOrderId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode());
            }
        }
    }
}
