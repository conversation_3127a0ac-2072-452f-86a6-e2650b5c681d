package top.kx.kxss.channel.leshuapay.utils;

import top.kx.kxss.model.constant.PayConstant;
import top.kx.kxss.pay.vo.model.params.saobeipay.SaobeipayConfig;
import top.kx.kxss.pay.vo.model.params.saobeipay.SaobeipayIsvParams;

/**
 * 参考乐刷实现的 httputils
 *
 * <AUTHOR>
 */
public class LeshuaHttpUtil {

    private static final String DEFAULT_CHARSET = "UTF-8";
    // 60 秒超时
    private static final int DEFAULT_TIMEOUT = 60 * 1000;

    /**
     * 扫呗通用订单类型， 如查单
     **/
    public static String getOrderTypeByCommon(String wayCode) {

        if (PayConstant.PAY_WAY_CODE.LESHUA_LITE.equals(wayCode)) {
            return "wechat";
        } else if (PayConstant.PAY_WAY_CODE.LESHUA_BAR.equals(wayCode)) {
            return "unionpay";
        } else if (PayConstant.PAY_WAY_CODE.LESHUA_NATIVE.equals(wayCode)) {
            return "native";
        }
        return null;
    }

    /**
     * 获取正式环境/沙箱HOST地址
     **/
    public static String getPayHost4env(SaobeipayIsvParams isvParams) {
        return PayConstant.YES == isvParams.getSandbox() ? SaobeipayConfig.SANDBOX_SERVER_URL : SaobeipayConfig.PROD_SERVER_URL;
    }


}
