package top.kx.kxss.channel.cashierpay;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.exception.ArgumentException;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.query.BizPaymentQuery;
import top.kx.kxss.app.vo.query.cash.PosCashIdQuery;
import top.kx.kxss.channel.AbstractCashierPayService;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.model.enumeration.base.OrderSourceEnum;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.model.enumeration.base.PosCashTypeEnum;
import top.kx.kxss.model.enumeration.pos.PosCashPaymentTransactionStatusEnum;
import top.kx.kxss.model.enumeration.pos.PosCashPaymentTransactionTypeEnum;
import top.kx.kxss.pay.entity.PayOrder;
import top.kx.kxss.pos.query.payment.PayAmountQuery;
import top.kx.kxss.pos.vo.payment.PayAmountResultVO;
import top.kx.kxss.pos.vo.save.cash.PosCashPaymentTransactionSaveVO;
import top.kx.kxss.system.vo.query.UpdateCashPaymentQuery;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

/**
 * 内部收银台支付
 * 团购充值订单
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ConsumeOrderCashierPayService extends AbstractCashierPayService {


    @Override
    public void pay(PayOrder payOrder, String payType, String channelUser) throws Exception {
        log.info("商户订单处理：{}", payOrder.getPayOrderId());
        //商户消费订单

        boolean lock = false;
        try {
            JSONObject params = JSONObject.parseObject(payOrder.getExtParam(), JSONObject.class);
            ContextUtil.setTenantBasePoolName(params.getLong("tenantId"));
            ContextUtil.setTenantId(params.getLong("tenantId"));
            lock = distributedLock.lock(payOrder.getPayOrderId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode(), 0);
            if (!lock) {
                return;
            }
            R<PosCashPayment> paymentId = posPaymentApi.cashPaymentById(params.getLong("paymentId"));
            if (paymentId == null) {
                log.info("支付记录不存在：{}", params.getLong("paymentId"));
                return;
            }
            ArgumentAssert.isFalse(!paymentId.getIsSuccess(), "支付记录不存在!");
            if (paymentId.getData() != null) {
                ContextUtil.setCurrentCompanyId(paymentId.getData().getCreatedOrgId());
                ContextUtil.setEmployeeId(paymentId.getData().getEmployeeId());
                if (paymentId.getData().getStatus().equals(PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())) {
                    return;
                }
                Long cashId = paymentId.getData().getCashId();
                R<PosCash> posOrder = posOrderApi.getById(cashId);
                if (posOrder == null) {
                    log.info("t_pay_order支付订单不存在：{}", cashId);
                    return;
                }
                if (posOrder.getIsSuccess() && posOrder.getData() != null
                        && !posOrder.getData().getBillState().equals(PosCashBillStateEnum.COMPLETE.getCode())) {
                    if (!paymentId.getData().getStatus().equals(PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())) {
                        //添加支付流水
                        posOrderApi.savePaymentTransaction(PosCashPaymentTransactionSaveVO.builder()
                                .type(!StringUtils.equals(PosCashTypeEnum.RECHARGE.getCode(),
                                        posOrder.getData().getType()) ? PosCashPaymentTransactionTypeEnum.ORDER.getCode() : PosCashPaymentTransactionTypeEnum.RECHARGE.getCode())
                                .payTypeId(paymentId.getData().getPayTypeId())
                                .payType(paymentId.getData().getPayName())
                                .orderSource(ContextUtil.getOrderSource())
                                .payTypeDetail(null)
                                .securitiesType(null)
                                .securitiesNumber(null)
                                .status(PosCashPaymentTransactionStatusEnum.PAY_SUCCESS.getCode())
                                .cashId(paymentId.getData().getCashId())
                                .paymentId(paymentId.getData().getId())
                                .code(posOrder.getData().getCode())
                                .amount(paymentId.getData().getAmount().subtract(Objects.nonNull(paymentId.getData().getRoundAmount()) ? paymentId.getData().getRoundAmount() : BigDecimal.ZERO))
                                .feeAmount(Objects.isNull(paymentId.getData().getMchFeeAmount()) ? BigDecimal.ZERO : paymentId.getData().getMchFeeAmount())
                                .payTime(Objects.nonNull(paymentId.getData().getPayTime()) ? paymentId.getData().getPayTime() : LocalDateTime.now())
                                .orderId(payOrder.getPayOrderId())
                                .employeeId(paymentId.getData().getEmployeeId())
                                .build());
                        posPaymentApi.payment(BizPaymentQuery.builder()
                                .payTypeId(paymentId.getData().getPayTypeId())
                                .posCashId(paymentId.getData().getCashId())
                                .isPrepaid(paymentId.getData().getIsPrepaid() != null && paymentId.getData().getIsPrepaid())
                                .platformId(payOrder.getPayOrderId())
                                .amount(payOrder.getAmount())
                                .payType(payType).channelUser(channelUser)
                                .orderSourceEnum(OrderSourceEnum.get(params.getString("orderSource")))
                                .build());
                        PosCashPayment byId = paymentId.getData();
                        if (byId != null && ObjectUtil.isNotNull(byId.getAccountPayTypeId())) {
                            //刷新订单方式防止价格有误
                            posOrderApi.detail(PosCashIdQuery.builder()
                                    .posCashId(byId.getCashId()).build());
                            R<PayAmountResultVO> payAmount = posPaymentApi.payAmount(PayAmountQuery.builder()
                                    .payTypeId(byId.getAccountPayTypeId())
                                    .posCashId(byId.getCashId())
                                    .build());
                            log.info("账户支付金额：{}", payAmount);
                            ArgumentAssert.isFalse(!payAmount.getIsSuccess(), payAmount.getMsg());
                            if (payAmount.getIsSuccess() && ObjectUtil.isNotNull(payAmount.getData().getAccountDeduct())) {
                                BigDecimal accountAmount = payAmount.getData().getAccountDeduct().getChangeRechargeAmount();
                                if (accountAmount == null) {
                                    accountAmount = BigDecimal.ZERO;
                                }
                                BigDecimal giftAmount = payAmount.getData().getAccountDeduct().getChangeGiftAmount();
                                if (giftAmount == null) {
                                    giftAmount = BigDecimal.ZERO;
                                }
                                int count = 0;
                                //直接余额支付
                                accountPayment(payType, channelUser, byId, accountAmount, giftAmount, count);
                            }
                        }
                    }
                }
            }
        } finally {
            ContextUtil.remove();
            if (lock) {
                distributedLock.releaseLock(payOrder.getPayOrderId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode());
            }
        }
        ContextUtil.setDefTenantId();
    }

    private void accountPayment(String payType, String channelUser,
                                PosCashPayment byId, BigDecimal accountAmount, BigDecimal giftAmount, int count) {
        try {
            R<PosCash> payment = posPaymentApi.accPayment(BizPaymentQuery.builder()
                    .posCashId(byId.getCashId())
                    .payType(payType).channelUser(channelUser)
                    .amount(accountAmount.multiply(new BigDecimal(100)).longValue())
                    .giftAmount(giftAmount.multiply(new BigDecimal(100)).longValue())
                    .orderSourceEnum(OrderSourceEnum.get(byId.getOrderSource()))
                    .payTypeId(byId.getAccountPayTypeId())
                    .build());
            ArgumentAssert.isFalse(!payment.getIsSuccess(), payment.getErrorMsg());
            log.info("账户支付结果：{}", payment);
        } catch (ArgumentException e) {
            log.info("账户支付异常2：{}", e.getMessage());
            log.info("重试次数：{}", count);
            count++;
            //业务报错重试2次
            if (count > 3) {
                return;
            }
            accountPayment(payType, channelUser, byId, accountAmount, giftAmount, count);
        } catch (Exception e) {
            log.info("账户支付异常：{}", e.getMessage());
            if (e.getMessage().contains("\"code\":50001")) {
                count++;
                //业务报错重试2次
                if (count > 3) {
                    return;
                }
                accountPayment(payType, channelUser, byId, accountAmount, giftAmount, count);
            }
        }

    }


    @Override
    public void modifyStatus(PayOrder payOrder, String status, String payType, String channelUser) {
        JSONObject params = JSONObject.parseObject(payOrder.getExtParam(), JSONObject.class);
        ContextUtil.setTenantBasePoolName(params.getLong("tenantId"));
        boolean lock = false;
        try {
            lock = distributedLock.lock(payOrder.getPayOrderId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode(), 0);
            if (!lock) {
                return;
            }
            posPaymentApi.updateCashPayment(UpdateCashPaymentQuery.builder()
                    .id(params.getLong("paymentId"))
                    .status(status)
                    .channelUser(channelUser)
                    .payType(payType)
                    .build());
        } finally {
            if (lock) {
                distributedLock.releaseLock(payOrder.getPayOrderId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode());
            }
        }
        ContextUtil.setDefTenantId();
    }
}
