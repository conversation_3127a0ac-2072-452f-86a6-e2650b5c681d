package top.kx.kxss.channel.cashierpay;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.context.ContextUtil;
import top.kx.kxss.channel.AbstractCashierPayService;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.model.enumeration.base.PosCashBillStateEnum;
import top.kx.kxss.pay.entity.PayOrder;
import top.kx.kxss.pos.entity.cash.PosCashTemp;

/**
 * 内部收银台支付
 * 团购充值订单
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TempOrderCashierPayService extends AbstractCashierPayService {


    @Override
    public void pay(PayOrder payOrder, String payType, String channelUser) throws Exception {
        log.info("临时订单处理：{}", payOrder.getPayOrderId());
        JSONObject params = JSONObject.parseObject(payOrder.getExtParam(), JSONObject.class);
        boolean lock = false;
        try {
            lock = distributedLock.lock(payOrder.getPayOrderId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode(), 0);
            if (!lock) {
                return;
            }
            Long tenantId = params.getLong("tenantId");
            ContextUtil.setTenantBasePoolName(tenantId);
            ContextUtil.setTenantId(tenantId);
            PosCashTemp posCashTemp = tempOrderApi.getInfoById(params.getLong("tempOrderId"));
            if (posCashTemp == null) {
                log.info("临时订单记录不存在：{}", params.getLong("paymentId"));
                return;
            }
            ContextUtil.setEmployeeId(posCashTemp.getEmployeeId());
            ContextUtil.setCurrentCompanyId(posCashTemp.getOrgId());
            if (!posCashTemp.getBillState().equals(PosCashBillStateEnum.NO_PAY.getCode())
                    && !posCashTemp.getBillState().equals(PosCashBillStateEnum.NO_SETTLED.getCode())) {
                return;
            }
            posCashTemp.setChannelUser(channelUser);
            posCashTemp.setPayType(payType);
            tempOrderApi.updateById(posCashTemp);
            tempOrderApi.payment(posCashTemp.getId());
        } finally {
            ContextUtil.remove();
            if (lock) {
                distributedLock.releaseLock(payOrder.getPayOrderId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode());
            }
        }
    }

    @Override
    public void modifyStatus(PayOrder payOrder, String status, String payType, String channelUser) {

    }
}
