package top.kx.kxss.channel.sunmipay.payway;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.kx.basic.exception.BizException;
import top.kx.kxss.channel.sunmipay.SunmipayPaymentService;
import top.kx.kxss.model.AbstractRS;
import top.kx.kxss.model.MchAppConfigContext;
import top.kx.kxss.model.msg.ChannelRetMsg;
import top.kx.kxss.model.payorder.UnifiedOrderRQ;
import top.kx.kxss.model.payway.SunmiBarOrderRQ;
import top.kx.kxss.model.payway.SunmiBarOrderRS;
import top.kx.kxss.pay.entity.PayOrder;
import top.kx.kxss.pay.vo.model.params.sunmipay.SunmipayIsvsubMchParams;
import top.kx.kxss.utils.ApiResBuilder;

import java.util.Map;
import java.util.regex.Pattern;

/**
 * 云闪付 云闪付条码支付
 *
 * <AUTHOR>
 */
//Service Name需保持全局唯一性
@Service("sunmiPaymentBySunmiBarService")
@Slf4j
public class SunmiBar extends SunmipayPaymentService {

    @Override
    public String preCheck(UnifiedOrderRQ rq, PayOrder payOrder) {

        SunmiBarOrderRQ bizRq = (SunmiBarOrderRQ) rq;
        if (StringUtils.isEmpty(bizRq.getAuthCode())) {
            throw new BizException("用户支付条码[authCode]不可为空");
        }

        return null;
    }

    @Override
    public AbstractRS pay(UnifiedOrderRQ rq, PayOrder payOrder, MchAppConfigContext mchAppConfigContext) throws Exception {
        String logPrefix = "【商米条码(unionpay)支付】";
        long start = System.currentTimeMillis();

        SunmiBarOrderRQ bizRq = (SunmiBarOrderRQ) rq;
        SunmiBarOrderRS res = ApiResBuilder.buildSuccess(SunmiBarOrderRS.class);
        ChannelRetMsg channelRetMsg = new ChannelRetMsg();
        res.setChannelRetMsg(channelRetMsg);
        SunmipayIsvsubMchParams isvsubMchParams = (SunmipayIsvsubMchParams) configContextQueryService.queryIsvsubMchParams(mchAppConfigContext.getMchNo(), mchAppConfigContext.getAppId(), getIfCode());
        JSONObject reqParams = new JSONObject();
        //付款码： 用户 APP 展示的付款条码或二维码
        reqParams.put("authCode", bizRq.getAuthCode().trim());
        reqParams.put("merchantNo", isvsubMchParams.getMerchantNo().trim());
        // 商米 bar 统一参数赋值
        barParamsSet(reqParams, payOrder, getNotifyUrl());

        reqParams.put("payType", getPayType(bizRq.getAuthCode()).trim());
        reqParams.put("terminalNo", isvsubMchParams.getTerminalNo());
        reqParams.put("longitude", bizRq.getLongitude());
        reqParams.put("latitude", bizRq.getLatitude());
        reqParams.put("terminalIp", StringUtils.defaultIfEmpty(rq.getClientIp(), "127.0.0.1"));
        // 发送请求
        JSONObject resJSON = packageParamAndReq("/v2/sunmipay/payment/trade/pay", logPrefix, reqParams, mchAppConfigContext);
        log.info("商米支付执行时间：{}", (start - System.currentTimeMillis()));
        //请求 & 响应成功， 判断业务逻辑
        //应答码
        String respCode = resJSON.getString("code");
        //应答信息
        String respMsg = resJSON.getString("msg");
        try {
            //00-交易成功， 02-用户支付中 , 12-交易重复， 需要发起查询处理    其他认为失败
            Map data = resJSON.getObject("data", Map.class);
            channelRetMsg.setChannelOrderId(String.valueOf(data.get("misId")));
            if ("1".equals(respCode)) {
                res.setPayData(resJSON.getString("data"));
                String channelState = data.get("state").toString();
                if ("3".equals(channelState)) {
                    channelRetMsg.setChannelState(ChannelRetMsg.ChannelState.CONFIRM_SUCCESS);
                } else if ("1".equals(channelState) || "2".equals(channelState)) {
                    // 开启轮询查单
                    channelRetMsg.setNeedQuery(true);
                    channelRetMsg.setChannelState(ChannelRetMsg.ChannelState.WAITING);
                } else if ("5".equals(channelState)
                        || "4".equals(channelState)) {
                    channelExceptionService.saveChannelException(payOrder, reqParams.toJSONString(), resJSON.toJSONString());
                    // 开启轮询查单
                    channelRetMsg.setNeedQuery(true);
                    channelRetMsg.setChannelState(ChannelRetMsg.ChannelState.WAITING);
                }
            } else {
                channelExceptionService.saveChannelException(payOrder, reqParams.toJSONString(), resJSON.toJSONString());
                // 开启轮询查单
                channelRetMsg.setNeedQuery(true);
                channelRetMsg.setChannelState(ChannelRetMsg.ChannelState.WAITING);
            }
        } catch (Exception e) {
            channelRetMsg.setChannelErrCode(respCode);
            channelRetMsg.setChannelErrMsg(respMsg);
        }
        log.info("商米支付执行时间：{}", (start - System.currentTimeMillis()));
        return res;
    }

    private static String getPayType(String authCode) {
        if (StrUtil.isBlank(authCode)) {
            return "WX";
        }
        // 微信支付码以10、11、12、13、14、15开头，长度18位
        if (authCode.length() == 18 && Pattern.matches("^(10|11|12|13|14|15)(.*)", authCode)) {
            return "WX";
        }

        // 支付宝支付码以25、26、27、28、29开头，长度16到24位
        if (authCode.length() >= 16 && authCode.length() <= 24
                && Pattern.matches("^(25|26|27|28|29|30)(.*)", authCode)) {
            return "ALI";
        }

        // 银联支付码以62开头，长度不固定
        if (authCode.length() == 19 && Pattern.matches("^(62)(.*)", authCode)) {
            return "UNION";
        }
        return "未知支付应用";
    }

}
