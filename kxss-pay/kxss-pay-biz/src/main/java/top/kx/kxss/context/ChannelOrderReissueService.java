package top.kx.kxss.context;

import cn.hutool.extra.spring.SpringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.kxss.channel.IPayOrderQueryService;
import top.kx.kxss.channel.IRefundService;
import top.kx.kxss.model.MchAppConfigContext;
import top.kx.kxss.model.msg.ChannelRetMsg;
import top.kx.kxss.pay.entity.PayOrder;
import top.kx.kxss.pay.entity.RefundOrder;
import top.kx.kxss.pay.service.PayOrderService;
import top.kx.kxss.pay.service.impl.PayOrderProcessService;
import top.kx.kxss.pay.service.impl.RefundOrderProcessService;

/**
 * 查询上游订单， &  补单服务实现类
 *
 * <AUTHOR>
 */

@Service
@Slf4j
public class ChannelOrderReissueService {

    @Autowired
    private ConfigContextQueryService configContextQueryService;
    @Autowired
    private PayOrderService payOrderService;
    @Autowired
    private PayOrderProcessService payOrderProcessService;
    @Autowired
    private RefundOrderProcessService refundOrderProcessService;


    /**
     * 处理订单
     **/
    public ChannelRetMsg processPayOrder(PayOrder payOrder) {

        try {

            String payOrderId = payOrder.getPayOrderId();

            //查询支付接口是否存在
            IPayOrderQueryService queryService = SpringUtil.getBean(payOrder.getIfCode() + "PayOrderQueryService", IPayOrderQueryService.class);

            // 支付通道接口实现不存在
            if (queryService == null) {
                log.error("{} interface not exists!", payOrder.getIfCode());
                return null;
            }

            //查询出商户应用的配置信息
            MchAppConfigContext mchAppConfigContext = configContextQueryService.queryMchInfoAndAppInfo(payOrder.getMchNo(), payOrder.getAppId());

            ChannelRetMsg channelRetMsg = queryService.query(payOrder, mchAppConfigContext);
            if (channelRetMsg == null) {
                log.error("channelRetMsg is null");
                return null;
            }

            log.info("补单[{}]查询结果为：{}", payOrderId, channelRetMsg);

            // 查询成功
            if (channelRetMsg.getChannelState() == ChannelRetMsg.ChannelState.CONFIRM_SUCCESS) {
                if (payOrderService.updateIng2Success(payOrderId, channelRetMsg.getChannelOrderId(), channelRetMsg.getChannelUserId(), channelRetMsg.getPayType())) {

                    //订单支付成功，其他业务逻辑
                    payOrderProcessService.confirmSuccess(payOrder);
                }
                //确认失败
            } else if (channelRetMsg.getChannelState() == ChannelRetMsg.ChannelState.CONFIRM_FAIL) {
                //1. 更新支付订单表为失败状态
                payOrderService.updateIng2Fail(payOrderId, channelRetMsg.getChannelOrderId(), channelRetMsg.getChannelUserId(),
                        channelRetMsg.getChannelErrCode(), channelRetMsg.getChannelErrMsg(), channelRetMsg.getPayType())
                ;
            } else if (channelRetMsg.getChannelState() == ChannelRetMsg.ChannelState.CONFIRM_CLOSE) {
                payOrderService.updateInit2Close(payOrder.getPayOrderId(), channelRetMsg.getChannelOrderId());
            }

            return channelRetMsg;

        } catch (Exception e) {  //继续下一次迭代查询
            log.error("error payOrderId = {}", payOrder.getPayOrderId(), e);
            return null;
        }

    }

    /**
     * 处理退款订单
     **/
    public ChannelRetMsg processRefundOrder(RefundOrder refundOrder) {

        try {

            String refundOrderId = refundOrder.getRefundOrderId();

            //查询支付接口是否存在
            IRefundService queryService = SpringUtil.getBean(refundOrder.getIfCode() + "RefundService", IRefundService.class);

            // 支付通道接口实现不存在
            if (queryService == null) {
                log.error("退款补单：{} interface not exists!", refundOrder.getIfCode());
                return null;
            }

            //查询出商户应用的配置信息
            MchAppConfigContext mchAppConfigContext = configContextQueryService.queryMchInfoAndAppInfo(refundOrder.getMchNo(), refundOrder.getAppId());

            ChannelRetMsg channelRetMsg = queryService.query(refundOrder, mchAppConfigContext);
            if (channelRetMsg == null) {
                log.error("退款补单：channelRetMsg is null");
                return null;
            }

            log.info("退款补单：[{}]查询结果为：{}", refundOrderId, channelRetMsg);
            // 根据渠道返回结果，处理退款订单
            refundOrderProcessService.handleRefundOrder4Channel(channelRetMsg, refundOrder);

            return channelRetMsg;

        } catch (Exception e) {  //继续下一次迭代查询
            log.error("退款补单：error refundOrderId = {}", refundOrder.getRefundOrderId(), e);
            return null;
        }

    }


}
