package top.kx.kxss.channel.sunmipay.payway;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import top.kx.basic.exception.BizException;
import top.kx.kxss.channel.sunmipay.SunmipayPaymentService;
import top.kx.kxss.model.AbstractRS;
import top.kx.kxss.model.MchAppConfigContext;
import top.kx.kxss.model.msg.ChannelRetMsg;
import top.kx.kxss.model.payorder.UnifiedOrderRQ;
import top.kx.kxss.model.payway.SunmiLiteOrderRQ;
import top.kx.kxss.model.payway.SunmiLiteOrderRS;
import top.kx.kxss.pay.entity.PayOrder;
import top.kx.kxss.pay.vo.model.params.sunmipay.SunmipayIsvsubMchParams;
import top.kx.kxss.utils.ApiResBuilder;

import java.util.Map;

/**
 * 商米 jsapi
 *
 * <AUTHOR>
 */
//Service Name需保持全局唯一性
@Service("sunmipayPaymentBySunmiLiteService")
public class SunmiLite extends SunmipayPaymentService {

    @Override
    public String preCheck(UnifiedOrderRQ rq, PayOrder payOrder) {
        // 使用的是V2接口的预先校验
        SunmiLiteOrderRQ bizRQ = (SunmiLiteOrderRQ) rq;
        if (StringUtils.isEmpty(bizRQ.getOpenid())) {
            throw new BizException("[openid]不可为空");
        }
        return null;
    }

    @Override
    public AbstractRS pay(UnifiedOrderRQ rq, PayOrder payOrder, MchAppConfigContext mchAppConfigContext) throws Exception {
        String logPrefix = "【商米(SunmiLite)jsapi支付】";
        JSONObject reqParams = new JSONObject();
        SunmiLiteOrderRS res = ApiResBuilder.buildSuccess(SunmiLiteOrderRS.class);
        SunmipayIsvsubMchParams isvsubMchParams = (SunmipayIsvsubMchParams) configContextQueryService.queryIsvsubMchParams(mchAppConfigContext.getMchNo(), mchAppConfigContext.getAppId(), getIfCode());
        ChannelRetMsg channelRetMsg = new ChannelRetMsg();
        res.setChannelRetMsg(channelRetMsg);
        SunmiLiteOrderRQ bizRq = (SunmiLiteOrderRQ) rq;
        // 请求参数赋值
        jsapiParamsSet(reqParams, payOrder, getNotifyUrl());
        // openId
        reqParams.put("userId", bizRq.getOpenid());
        reqParams.put("merchantNo", isvsubMchParams.getMerchantNo());
        reqParams.put("miniAppId", isvsubMchParams.getAppId());
        //客户端IP
        reqParams.put("terminalIp", StringUtils.defaultIfEmpty(rq.getClientIp(), "127.0.0.1"));
        // 发送请求并返回订单状态
        JSONObject resJSON = packageLiteParamAndReq("/api/pay/unifiedorder", logPrefix, reqParams, mchAppConfigContext);
        //请求 & 响应成功， 判断业务逻辑
        //应答码
        String respCode = resJSON.getString("code");
        //应答信息
        String respMsg = resJSON.getString("msg");

        try {
            //00-交易成功， 02-用户支付中 , 12-交易重复， 需要发起查询处理    其他认为失败
            if ("1".equals(respCode)) {
                //付款信息
                Map data = resJSON.getObject("data", Map.class);
                res.setPayInfo(data.get("cloudPayInfo").toString());
            } else {
                channelExceptionService.saveChannelException(payOrder, reqParams.toJSONString(), resJSON.toJSONString());
                channelRetMsg.setChannelErrCode(respCode);
                channelRetMsg.setChannelErrMsg(respMsg);
            }
            channelRetMsg.setChannelState(ChannelRetMsg.ChannelState.WAITING);
            channelRetMsg.setNeedQuery(true);
        } catch (Exception e) {
            channelExceptionService.saveChannelException(payOrder, reqParams.toJSONString(), resJSON.toJSONString());
            channelRetMsg.setChannelErrCode(respCode);
            channelRetMsg.setChannelErrMsg(respMsg);
        }
        return res;
    }

}
