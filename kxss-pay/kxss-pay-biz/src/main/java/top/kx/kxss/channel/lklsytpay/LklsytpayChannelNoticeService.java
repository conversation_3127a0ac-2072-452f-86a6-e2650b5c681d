package top.kx.kxss.channel.lklsytpay;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import top.kx.kxss.channel.AbstractChannelNoticeService;
import top.kx.kxss.exception.ResponseException;
import top.kx.kxss.model.MchAppConfigContext;
import top.kx.kxss.model.constant.PayConstant;
import top.kx.kxss.model.msg.ChannelRetMsg;
import top.kx.kxss.pay.entity.PayOrder;

import javax.servlet.http.HttpServletRequest;

/**
 * 拉卡拉收银台回调
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LklsytpayChannelNoticeService extends AbstractChannelNoticeService {


    @Override
    public String getIfCode() {
        return PayConstant.IF_CODE.LKLSYTPAY;
    }

    @Override
    public MutablePair<String, Object> parseParams(HttpServletRequest request, String urlOrderId, NoticeTypeEnum noticeTypeEnum) {

        try {
            // spring-boot 2.x 直接调用读取请求提并验签方法,返回请求体
            JSONObject params = getReqParamJSON();
//            String body = LKLSDK.notificationHandle(request);
//            JSONObject params = JSONObject.parseObject(body);
            String payOrderId = params.getString("out_order_no");
            if (StrUtil.isBlank(payOrderId)) {
                payOrderId = params.getString("payOrderId");
            }
            return MutablePair.of(payOrderId, params);
        } catch (Exception e) {
            log.error("error", e);
            throw ResponseException.buildText("ERROR");
        }
    }

    @Override
    public ChannelRetMsg doNotice(HttpServletRequest request, Object params, PayOrder payOrder, MchAppConfigContext mchAppConfigContext, NoticeTypeEnum noticeTypeEnum) {
        try {

            ChannelRetMsg result = ChannelRetMsg.confirmSuccess(null);

            String logPrefix = "【处理拉卡拉支付回调】";
            // 获取请求参数
            JSONObject resJSON = (JSONObject) params;
            log.info("{} 回调参数, jsonParams：{}", logPrefix, resJSON);

            // 校验支付回调
            boolean verifyResult = verifyParams(resJSON, payOrder);
            // 验证参数失败
            if (!verifyResult) {
                throw ResponseException.buildText("ERROR");
            }
            JSONObject res = new JSONObject();
            log.info("{}验证支付通知数据", logPrefix);
            String orderStatus = resJSON.getString("order_status");
            if (StrUtil.isNotBlank(orderStatus)) {
                if ("2".equals(orderStatus) || "6".equals(orderStatus)) {
                    res.put("code", "SUCCESS");
                    res.put("message", "执行成功");
                    result.setChannelState(ChannelRetMsg.ChannelState.CONFIRM_SUCCESS);
                } else if ("0".equals(orderStatus) || "1".equals(orderStatus)) {
                    result.setChannelState(ChannelRetMsg.ChannelState.WAITING);
                    result.setNeedQuery(true);
                } else if ("3".equals(orderStatus)) {
                    result.setChannelState(ChannelRetMsg.ChannelState.CONFIRM_FAIL);
                    result.setNeedQuery(true);
                } else if ("5".equals(orderStatus) || "4".equals(orderStatus)) {
                    result.setChannelState(ChannelRetMsg.ChannelState.CONFIRM_CLOSE);
                }
            } else {
                Integer state = resJSON.getInteger("state");
                if (state != null && PayOrder.STATE_SUCCESS == state) {
                    res.put("code", "SUCCESS");
                    res.put("message", "执行成功");
                    ResponseEntity okResponse = jsonResp(res);
                    //响应数据
                    result.setResponseEntity(okResponse);
                } else {
                    channelExceptionService.saveChannelException(payOrder, logPrefix, resJSON.toJSONString());
                }
            }
            return result;
        } catch (Exception e) {
            log.error("error", e);
            throw ResponseException.buildText("ERROR");
        }
    }

    /**
     * 验证拉卡拉支付通知参数
     */
    public boolean verifyParams(JSONObject jsonParams, PayOrder payOrder) {

        // 商户订单号
        String orderNo = jsonParams.getString("out_trade_no");
        if (StrUtil.isBlank(orderNo)) {
            orderNo = jsonParams.getString("payOrderId");
        }
        // 支付金额
        String txnAmt = jsonParams.getString("total_amount");
        if (StrUtil.isBlank(txnAmt)) {
            txnAmt = jsonParams.getString("amount");
        }
        if (StringUtils.isEmpty(orderNo)) {
            log.info("订单ID为空 [orderNo]={}", orderNo);
            return false;
        }
        if (StringUtils.isEmpty(txnAmt)) {
            log.info("金额参数为空 [txnAmt] :{}", txnAmt);
            return false;
        }

        // 核对金额
        long dbPayAmt = payOrder.getAmount();
        if (dbPayAmt != Long.parseLong(txnAmt)) {
            log.info("订单金额与参数金额不符。 dbPayAmt={}, txnAmt={}, payOrderId={}", dbPayAmt, txnAmt, orderNo);
            return false;
        }
        return true;
    }

}
