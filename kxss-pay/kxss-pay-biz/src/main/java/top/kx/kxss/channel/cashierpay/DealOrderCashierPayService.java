package top.kx.kxss.channel.cashierpay;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.app.query.DealOrderPaymentQuery;
import top.kx.kxss.channel.AbstractCashierPayService;
import top.kx.kxss.common.constant.PosCashConstant;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.model.enumeration.base.OrderSourceEnum;
import top.kx.kxss.model.enumeration.pos.DealOrderStatusEnum;
import top.kx.kxss.pay.entity.PayOrder;
import top.kx.kxss.system.vo.query.UpdateCashPaymentQuery;
import top.kx.kxss.system.entity.deal.DealOrder;
import top.kx.kxss.system.entity.deal.DealOrderPayment;

/**
 * 内部收银台支付
 * 团购充值订单
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class DealOrderCashierPayService extends AbstractCashierPayService {


    @Override
    public void pay(PayOrder payOrder, String payType, String channelUser) throws Exception {
        ContextUtil.setDefTenantId();
        JSONObject params = JSONObject.parseObject(payOrder.getExtParam(), JSONObject.class);
        boolean lock = false;
        try {
            lock = distributedLock.lock(payOrder.getPayOrderId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode(), 0);
            if (!lock) {
                return;
            }
            R<DealOrderPayment> paymentId = dealOrderApi.paymentById(params.getLong("paymentId"));
            if (paymentId == null) {
                return;
            }
            ArgumentAssert.isFalse(!paymentId.getIsSuccess(), "支付记录不存在!");
            if (paymentId.getData() != null) {
                Long orderId = paymentId.getData().getOrderId();
                R<DealOrder> dealOrder = dealOrderApi.getByOrderId(orderId);
                if (dealOrder == null) {
                    return;
                }
                if (dealOrder.getIsSuccess() && dealOrder.getData() != null
                        && !dealOrder.getData().getStatus().equals(DealOrderStatusEnum.COMPLETE.getCode())) {
                    if (!paymentId.getData().getStatus().equals(PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())) {

                        dealOrderApi.payment(DealOrderPaymentQuery.builder()
                                .orderId(paymentId.getData().getOrderId())
                                .paymentId(paymentId.getData().getId())
                                .platformId(payOrder.getPayOrderId())
                                .amount(payOrder.getAmount())
                                .orderSourceEnum(OrderSourceEnum.get(params.getString("orderSource")))
                                .build());

                    }
                }
            }
        } finally {
            ContextUtil.remove();
            if (lock) {
                distributedLock.releaseLock(payOrder.getPayOrderId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode());
            }
        }
    }


    @Override
    public void modifyStatus(PayOrder payOrder, String status, String payType, String channelUser) {
        JSONObject params = JSONObject.parseObject(payOrder.getExtParam(), JSONObject.class);
        ContextUtil.setDefTenantId();
        boolean lock = false;
        try {
            lock = distributedLock.lock(payOrder.getPayOrderId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode(), 0);
            if (!lock) {
                return;
            }
            dealOrderApi.updatePayment(UpdateCashPaymentQuery.builder()
                    .id(params.getLong("paymentId"))
                    .status(status)
                    .build());
        } finally {
            if (lock) {
                distributedLock.releaseLock(payOrder.getPayOrderId() + "_" + PosCashConstant.Event.REFRESH_TABLE.getCode());
            }
        }
    }
}
