package top.kx.kxss.rabbitmq.model;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import top.kx.kxss.model.enumeration.pay.MQSendTypeEnum;
import top.kx.kxss.model.mq.AbstractMQ;

/**
 * 定义MQ消息格式
 * 业务场景： [ 支付订单补单（一般用于没有回调的接口，比如微信的条码支付） ]
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PayOrderReissueMQ extends AbstractMQ {

    /**
     * 【！重要配置项！】 定义MQ名称
     **/
    public static final String MQ_NAME = "QUEUE_PAY_ORDER_REISSUE";

    /**
     * 内置msg 消息体定义
     **/
    private MsgPayload payload;

    /**
     * 【！重要配置项！】 定义Msg消息载体
     **/
    @Data
    @AllArgsConstructor
    public static class MsgPayload {

        /**
         * 支付订单号
         **/
        private String payOrderId;

        /**
         * 通知次数
         **/
        private Integer count;

    }

    @Override
    public String getMQName() {
        return MQ_NAME;
    }

    /**
     * 【！重要配置项！】
     **/
    @Override
    public MQSendTypeEnum getMQType() {
        return MQSendTypeEnum.QUEUE;  // QUEUE - 点对点 、 BROADCAST - 广播模式
    }

    @Override
    public String toMessage() {
        return JSONObject.toJSONString(payload);
    }

    /**
     * 【！重要配置项！】 构造MQModel , 一般用于发送MQ时
     **/
    public static PayOrderReissueMQ build(String payOrderId, Integer count) {
        return new PayOrderReissueMQ(new MsgPayload(payOrderId, count));
    }

    /**
     * 解析MQ消息， 一般用于接收MQ消息时
     **/
    public static MsgPayload parse(String msg) {
        return JSON.parseObject(msg, MsgPayload.class);
    }

    /**
     * 定义 IMQReceiver 接口： 项目实现该接口则可接收到对应的业务消息
     **/
    public interface IMQReceiver {
        void receive(MsgPayload payload);
    }

}
