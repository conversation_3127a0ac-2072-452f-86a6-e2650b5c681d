package top.kx.kxss.channel.lklsytpay;

import com.lkl.laop.sdk.request.V3CcssCounterOrderSpecialCreateRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.channel.AbstractPaymentService;
import top.kx.kxss.model.AbstractRS;
import top.kx.kxss.model.MchAppConfigContext;
import top.kx.kxss.model.constant.PayConstant;
import top.kx.kxss.model.payorder.UnifiedOrderRQ;
import top.kx.kxss.pay.entity.PayOrder;
import top.kx.kxss.pay.vo.model.params.lklsytpay.LklsytpayIsvsubMchParams;
import top.kx.kxss.utils.PaywayUtil;

import java.time.LocalDateTime;

/**
 * 支付接口： 拉卡拉收银台
 * 支付方式： 自适应
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LklsytpayPaymentService extends AbstractPaymentService {

    @Override
    public String getIfCode() {
        return PayConstant.IF_CODE.LKLSYTPAY;
    }

    @Override
    public boolean isSupport(String wayCode) {
        return true;
    }

    @Override
    public String preCheck(UnifiedOrderRQ rq, PayOrder payOrder) {
        return PaywayUtil.getRealPaywayService(this, payOrder.getWayCode()).preCheck(rq, payOrder);
    }

    @Override
    public AbstractRS pay(UnifiedOrderRQ rq, PayOrder payOrder, MchAppConfigContext mchAppConfigContext) throws Exception {
        return PaywayUtil.getRealPaywayService(this, payOrder.getWayCode()).pay(rq, payOrder, mchAppConfigContext);
    }

    protected V3CcssCounterOrderSpecialCreateRequest buildQrCodeOrderRequest(PayOrder payOrder, LklsytpayIsvsubMchParams isvSubMchParams) {
        String payOrderId = payOrder.getPayOrderId();
        // 微信统一下单请求对象
        V3CcssCounterOrderSpecialCreateRequest request = new V3CcssCounterOrderSpecialCreateRequest();
        request.setMerchantNo(isvSubMchParams.getMerchantNo());
        request.setOutOrderNo(payOrderId);
        request.setTotalAmount(payOrder.getAmount());
        request.setOrderEfficientTime(DateUtils.format(LocalDateTime.now().plusMinutes(5).withNano(0),
                "yyyyMMddHHmmss"));
        request.setSupportRefund(1);
        request.setOrderInfo(payOrder.getSubject());
        request.setNotifyUrl(payOrder.getNotifyUrl());
        return request;
    }


}
