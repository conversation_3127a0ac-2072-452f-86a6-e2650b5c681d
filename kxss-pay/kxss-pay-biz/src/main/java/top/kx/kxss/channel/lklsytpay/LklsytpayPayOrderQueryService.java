package top.kx.kxss.channel.lklsytpay;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.lkl.laop.sdk.LKLSDK;
import com.lkl.laop.sdk.request.V3CcssCounterOrderQueryRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.kxss.channel.IPayOrderQueryService;
import top.kx.kxss.channel.lklpay.utils.LklHttpUtil;
import top.kx.kxss.context.ConfigContextQueryService;
import top.kx.kxss.model.MchAppConfigContext;
import top.kx.kxss.model.constant.PayConstant;
import top.kx.kxss.model.msg.ChannelRetMsg;
import top.kx.kxss.pay.entity.PayOrder;
import top.kx.kxss.pay.service.TChannelExceptionService;
import top.kx.kxss.pay.vo.model.params.lklpay.LklpayIsvParams;
import top.kx.kxss.pay.vo.model.params.lklsytpay.LklsytpayIsvParams;
import top.kx.kxss.pay.vo.model.params.lklsytpay.LklsytpayIsvsubMchParams;

/**
 * 拉卡拉查单接口
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class LklsytpayPayOrderQueryService implements IPayOrderQueryService {

    @Autowired
    private ConfigContextQueryService configContextQueryService;
    @Autowired
    private TChannelExceptionService channelExceptionService;

    @Override
    public String getIfCode() {
        return PayConstant.IF_CODE.LKLSYTPAY;
    }

    @Override
    public ChannelRetMsg query(PayOrder payOrder, MchAppConfigContext mchAppConfigContext) {
        String orderType = LklHttpUtil.getOrderTypeByCommon(payOrder.getWayCode());
        String logPrefix = "【拉卡拉收银台(" + orderType + ")查单】";
        LklsytpayIsvsubMchParams isvsubMchParams = (LklsytpayIsvsubMchParams) configContextQueryService.queryIsvsubMchParams(mchAppConfigContext.getMchNo(), mchAppConfigContext.getAppId(), getIfCode());
        LklsytpayIsvParams isvParams = (LklsytpayIsvParams) configContextQueryService.queryIsvParams(mchAppConfigContext.getMchInfo().getIsvNo(), getIfCode());
        V3CcssCounterOrderQueryRequest request = new V3CcssCounterOrderQueryRequest();
        request.setMerchantNo(isvsubMchParams.getMerchantNo());
        request.setOutOrderNo(payOrder.getPayOrderId());
        ChannelRetMsg result = new ChannelRetMsg();
        try {
            LklHttpUtil.doInit(BeanUtil.copyProperties(isvParams, LklpayIsvParams.class));
            log.info("{},请求参数:{}", logPrefix, JSON.toJSONString(request));
            String response = LKLSDK.httpPost(request);
            log.info("{},返回结果:{}", logPrefix, response);
            JSONObject resJSON = JSONObject.parseObject(response);
            String respCode = resJSON.getString("code");
            //应答信息
            String respMsg = resJSON.getString("msg");
            if ("000000".equals(respCode)) {
                JSONObject respData = resJSON.getObject("resp_data", JSONObject.class);
                String payOrderNo = respData.getString("pay_order_no");
                payOrder.setChannelOrderNo(payOrderNo);
                String orderStatus = respData.getString("order_status");
                if ("2".equals(orderStatus) || "6".equals(orderStatus)) {
                    result.setChannelState(ChannelRetMsg.ChannelState.CONFIRM_SUCCESS);
                } else if ("0".equals(orderStatus) || "1".equals(orderStatus)) {
                    result.setChannelState(ChannelRetMsg.ChannelState.WAITING);
                    result.setNeedQuery(true);
                } else if ("3".equals(orderStatus)) {
                    result.setChannelState(ChannelRetMsg.ChannelState.CONFIRM_FAIL);
                    result.setNeedQuery(true);
                } else if ("5".equals(orderStatus) || "4".equals(orderStatus)) {
                    result.setChannelState(ChannelRetMsg.ChannelState.CONFIRM_CLOSE);
                    result.setChannelErrMsg(respMsg);
                    result.setChannelErrCode(respCode);
                }
            }
            return result;
        } catch (Exception e) {
            return ChannelRetMsg.sysError(e.getMessage());
        }
    }

}
