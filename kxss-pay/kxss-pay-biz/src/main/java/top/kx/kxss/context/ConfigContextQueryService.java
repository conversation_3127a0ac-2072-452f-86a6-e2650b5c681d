package top.kx.kxss.context;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.kxss.model.*;
import top.kx.kxss.model.constant.PayConstant;
import top.kx.kxss.pay.entity.MchApp;
import top.kx.kxss.pay.entity.MchInfo;
import top.kx.kxss.pay.entity.PayInterfaceConfig;
import top.kx.kxss.pay.service.MchAppService;
import top.kx.kxss.pay.service.MchInfoService;
import top.kx.kxss.pay.service.PayConfigService;
import top.kx.kxss.pay.service.PayInterfaceConfigService;
import top.kx.kxss.pay.vo.model.params.IsvParams;
import top.kx.kxss.pay.vo.model.params.IsvsubMchParams;
import top.kx.kxss.pay.vo.model.params.NormalMchParams;
import top.kx.kxss.pay.vo.model.params.alipay.AlipayIsvParams;
import top.kx.kxss.pay.vo.model.params.alipay.AlipayNormalMchParams;
import top.kx.kxss.pay.vo.model.params.pppay.PppayNormalMchParams;
import top.kx.kxss.pay.vo.model.params.saobeipay.SaobeipayIsvParams;
import top.kx.kxss.pay.vo.model.params.saobeipay.SaobeipayIsvsubMchParams;
import top.kx.kxss.pay.vo.model.params.wxpay.WxpayIsvParams;
import top.kx.kxss.pay.vo.model.params.wxpay.WxpayNormalMchParams;
import top.kx.kxss.pay.vo.result.IsvReconciliationInfoResultVO;

/**
 * 配置信息查询服务 （兼容 缓存 和 直接查询方式）
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ConfigContextQueryService {

    @Autowired
    ConfigContextService configContextService;
    @Autowired
    private MchInfoService mchInfoService;
    @Autowired
    private MchAppService mchAppService;
    @Autowired
    private PayInterfaceConfigService payInterfaceConfigService;

    private boolean isCache() {
        return PayConfigService.IS_USE_CACHE;
    }

    public MchApp queryMchApp(String mchNo, String mchAppId) {

        if (isCache()) {
            return configContextService.getMchAppConfigContext(mchNo, mchAppId).getMchApp();
        }

        return mchAppService.getOneByMch(mchNo, mchAppId);
    }

    public MchAppConfigContext queryMchInfoAndAppInfo(String mchAppId) {
        return queryMchInfoAndAppInfo(mchAppService.getByAppId(mchAppId).getMchNo(), mchAppId);
    }

    public MchAppConfigContext queryMchInfoAndAppInfo(String mchNo, String mchAppId) {

        if (isCache()) {
            return configContextService.getMchAppConfigContext(mchNo, mchAppId);
        }

        MchInfo mchInfo = mchInfoService.getByMchNo(mchNo);
        MchApp mchApp = queryMchApp(mchNo, mchAppId);

        if (mchInfo == null || mchApp == null) {
            return null;
        }

        MchAppConfigContext result = new MchAppConfigContext();
        result.setMchInfo(mchInfo);
        result.setMchNo(mchNo);
        result.setMchType(mchInfo.getType());

        result.setMchApp(mchApp);
        result.setAppId(mchAppId);

        return result;
    }


    public NormalMchParams queryNormalMchParams(String mchNo, String mchAppId, String ifCode) {

        if (isCache()) {
            return configContextService.getMchAppConfigContext(mchNo, mchAppId).getNormalMchParamsByIfCode(ifCode);
        }

        // 查询商户的所有支持的参数配置
        PayInterfaceConfig payInterfaceConfig = payInterfaceConfigService.getOne(PayInterfaceConfig.gw()
                .select(PayInterfaceConfig::getIfCode, PayInterfaceConfig::getIfParams)
                .eq(PayInterfaceConfig::getState, PayConstant.YES)
                .eq(PayInterfaceConfig::getInfoType, PayConstant.INFO_TYPE_MCH_APP)
                .eq(PayInterfaceConfig::getInfoId, mchAppId)
                .eq(PayInterfaceConfig::getIfCode, ifCode)
        );

        if (payInterfaceConfig == null) {
            return null;
        }
        return NormalMchParams.factory(payInterfaceConfig.getIfCode(), payInterfaceConfig.getIfParams());
    }


    public IsvsubMchParams queryIsvsubMchParams(String mchNo, String mchAppId, String ifCode) {

        if (isCache()) {
            return configContextService.getMchAppConfigContext(mchNo, mchAppId).getIsvsubMchParamsByIfCode(ifCode);
        }

        // 查询商户的所有支持的参数配置
        PayInterfaceConfig payInterfaceConfig = payInterfaceConfigService.getOne(PayInterfaceConfig.gw()
                .select(PayInterfaceConfig::getIfCode, PayInterfaceConfig::getIfParams)
                .eq(PayInterfaceConfig::getState, PayConstant.YES)
                .eq(PayInterfaceConfig::getInfoType, PayConstant.INFO_TYPE_MCH_APP)
                .eq(PayInterfaceConfig::getInfoId, mchAppId)
                .eq(PayInterfaceConfig::getIfCode, ifCode)
        );

        if (payInterfaceConfig == null) {
            return null;
        }

        return IsvsubMchParams.factory(payInterfaceConfig.getIfCode(), payInterfaceConfig.getIfParams());
    }


    public IsvParams queryIsvParams(String isvNo, String ifCode) {

        if (isCache()) {
            IsvConfigContext isvConfigContext = configContextService.getIsvConfigContext(isvNo);
            return isvConfigContext == null ? null : isvConfigContext.getIsvParamsByIfCode(ifCode);
        }

        // 查询商户的所有支持的参数配置
        PayInterfaceConfig payInterfaceConfig = payInterfaceConfigService.getOne(PayInterfaceConfig.gw()
                .select(PayInterfaceConfig::getIfCode, PayInterfaceConfig::getIfParams)
                .eq(PayInterfaceConfig::getState, PayConstant.YES)
                .eq(PayInterfaceConfig::getInfoType, PayConstant.INFO_TYPE_ISV)
                .eq(PayInterfaceConfig::getInfoId, isvNo)
                .eq(PayInterfaceConfig::getIfCode, ifCode)
        );

        if (payInterfaceConfig == null) {
            return null;
        }
        return IsvParams.factory(payInterfaceConfig.getIfCode(), payInterfaceConfig.getIfParams());

    }

    public AlipayClientWrapper getAlipayClientWrapper(MchAppConfigContext mchAppConfigContext) {

        if (isCache()) {
            return
                    configContextService.getMchAppConfigContext(mchAppConfigContext.getMchNo(), mchAppConfigContext.getAppId()).getAlipayClientWrapper();
        }

        if (mchAppConfigContext.isIsvsubMch()) {

            AlipayIsvParams alipayParams = (AlipayIsvParams) queryIsvParams(mchAppConfigContext.getMchInfo().getIsvNo(), PayConstant.IF_CODE.ALIPAY);
            return AlipayClientWrapper.buildAlipayClientWrapper(alipayParams);
        } else {

            AlipayNormalMchParams alipayParams = (AlipayNormalMchParams) queryNormalMchParams(mchAppConfigContext.getMchNo(), mchAppConfigContext.getAppId(), PayConstant.IF_CODE.ALIPAY);
            return AlipayClientWrapper.buildAlipayClientWrapper(alipayParams);
        }

    }

    public WxServiceWrapper getWxServiceWrapper(MchAppConfigContext mchAppConfigContext) {

        if (isCache()) {
            return
                    configContextService.getMchAppConfigContext(mchAppConfigContext.getMchNo(), mchAppConfigContext.getAppId()).getWxServiceWrapper();
        }

        if (mchAppConfigContext.isIsvsubMch()) {

            WxpayIsvParams wxParams = (WxpayIsvParams) queryIsvParams(mchAppConfigContext.getMchInfo().getIsvNo(), PayConstant.IF_CODE.WXPAY);
            return WxServiceWrapper.buildWxServiceWrapper(wxParams);
        } else {
            WxpayNormalMchParams wxParams = (WxpayNormalMchParams) queryNormalMchParams(mchAppConfigContext.getMchNo(), mchAppConfigContext.getAppId(), PayConstant.IF_CODE.WXPAY);
            return WxServiceWrapper.buildWxServiceWrapper(wxParams);
        }

    }

    public PayWrapper getPaypalWrapper(MchAppConfigContext mchAppConfigContext) {
        if (isCache()) {
            return
                    configContextService.getMchAppConfigContext(mchAppConfigContext.getMchNo(), mchAppConfigContext.getAppId()).getPayWrapper();
        }
        PppayNormalMchParams ppPayNormalMchParams = (PppayNormalMchParams) queryNormalMchParams(mchAppConfigContext.getMchNo(), mchAppConfigContext.getAppId(), PayConstant.IF_CODE.PPPAY);
        ;
        return PayWrapper.buildPaypalWrapper(ppPayNormalMchParams);

    }


    public IsvReconciliationInfoResultVO queryReconciliationParam(String appId, String ifCode) {
        //商户主体信息
        MchApp mchApp = mchAppService.getByAppId(appId);
        if (mchApp == null) {
            return null;
        }
        MchInfo mchInfo = mchInfoService.getByMchNo(mchApp.getMchNo());
        if (mchInfo == null || StrUtil.isBlank(mchInfo.getIsvNo())) {
            return null;
        }
        SaobeipayIsvParams isvParams = (SaobeipayIsvParams) queryIsvParams(mchInfo.getIsvNo(), ifCode);
        if (isvParams == null) {
            return null;
        }
        SaobeipayIsvsubMchParams isvMchParams = (SaobeipayIsvsubMchParams) queryIsvsubMchParams(mchInfo.getMchNo(), mchApp.getAppId(), ifCode);
        if (isvMchParams == null) {
            return null;
        }
        IsvReconciliationInfoResultVO resultVO = BeanUtil.copyProperties(isvParams, IsvReconciliationInfoResultVO.class);
        resultVO.setMchNo(isvMchParams.getMerchantNo());
        return resultVO;
    }

}
