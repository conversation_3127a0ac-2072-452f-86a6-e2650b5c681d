package top.kx.kxss.pay.config;

import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.ControllerAdvice;
import top.kx.basic.base.R;
import top.kx.basic.boot.handler.AbstractGlobalResponseBodyAdvice;

/**
 * 自定义springMVC返回数据格式
 */
@ControllerAdvice
public class ApiResBodyAdvice extends AbstractGlobalResponseBodyAdvice {


    /**
     * 拦截返回数据处理
     */
    @Override
    public Object beforeBodyWrite(Object o, MethodParameter returnType, MediaType selectedContentType,
                                  Class selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {

        if (o == null) {
            return null;
        }
        if (o instanceof R) {
            return o;
        }
        //处理扩展字段
        return ApiResBodyAdviceKit.beforeBodyWrite(o);
    }

}
