package top.kx.kxss.pay.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import feign.FeignException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.DispatcherServlet;
import top.kx.basic.base.R;
import top.kx.basic.boot.handler.AbstractGlobalExceptionHandler;

import javax.servlet.Servlet;

/**
 * 支付模块-全局异常处理
 *
 * <AUTHOR>
 * @date 2024-05-21 15:06:26
 */
@Configuration
@ConditionalOnClass({Servlet.class, DispatcherServlet.class})
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
@RestControllerAdvice(annotations = {RestController.class, Controller.class})
@Slf4j
public class PayExceptionConfiguration extends AbstractGlobalExceptionHandler {

    @ExceptionHandler({FeignException.class})
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public R<?> handleFeignExceptionException(FeignException ex) {
        log.warn("FeignException:", ex);
        String message = ex.getMessage();
        String substring = message.substring(message.indexOf(": ") + 2);
        if (substring.startsWith("[")) {
            substring = substring.substring(substring.indexOf("[") + 1);
        }
        if (substring.endsWith("]")) {
            substring = substring.substring(0, substring.lastIndexOf("]"));
        }
        boolean isJson = false;
        if (substring.contains("bytes")) {
            substring = substring.substring(0, substring.lastIndexOf(",\"errorMsg\""));
            substring = substring.concat("}");
            isJson = true;
        }
        JSONObject userJson = JSONObject.parseObject(substring);
        if (isJson) {
            userJson.put("errorMsg", userJson.get("msg"));
        }
        return JSON.toJavaObject(userJson, R.class);
    }

}
