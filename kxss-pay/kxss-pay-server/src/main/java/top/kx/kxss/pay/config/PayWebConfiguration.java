package top.kx.kxss.pay.config;

import top.kx.basic.boot.config.BaseConfig;
import top.kx.basic.log.event.SysLogListener;
import top.kx.kxss.common.api.LogApi;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 支付模块-Web配置
 *
 * <AUTHOR>
 * @date 2024-05-21 15:06:26
 */
@Configuration
public class PayWebConfiguration extends BaseConfig {

    /**
     * kxss.log.enabled = true 并且 kxss.log.type=DB时实例该类
     */
    @Bean
    @ConditionalOnExpression("${kxss.log.enabled:true} && 'DB'.equals('${kxss.log.type:LOGGER}')")
    public SysLogListener getSysLogListener(LogApi logApi) {
        return new SysLogListener(logApi::save);
    }
}
