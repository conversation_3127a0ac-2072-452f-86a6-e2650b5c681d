package top.kx.kxss.generator.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import top.kx.basic.boot.config.BaseConfig;
import top.kx.basic.log.event.SysLogListener;
import top.kx.kxss.common.api.LogApi;

/**
 * 在线代码生成器模块-Web配置
 *
 * <AUTHOR>
 * @date 2022-02-28
 */
@Configuration
public class GeneratorWebConfiguration extends BaseConfig {

    /**
     * lamp.log.enabled = true 并且 lamp.log.type=DB时实例该类
     */
    @Bean
    @ConditionalOnExpression("${lamp.log.enabled:true} && 'DB'.equals('${lamp.log.type:LOGGER}')")
    public SysLogListener sysLogListener(LogApi logApi) {
        return new SysLogListener(logApi::save);
    }
}
