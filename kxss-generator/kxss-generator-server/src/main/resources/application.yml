# 代码生成
lamp:
  generator:
    outputDir: /Users/<USER>/gitlab/kxss-cloud-pro-datasource-column
    frontOutputDir: /Users/<USER>/gitlab/kxss-web-pro
    # 作者
    author: zuihou
    # 默认项目
    projectType: CLOUD
  #  # 去除表前缀
  #  tablePrefix:
  #    - xxx_
  #  # 去除字段前缀
  #  fieldPrefix:
  #    - xxx_
  #  # 去除字段后缀
  #  fieldSuffix:
  #    - xxx_
    # 项目前缀
    projectPrefix: 'kxss'
    superClass: SUPER_CLASS
    # 生成方式
    genType: GEN
    packageInfoConfig: # 其他配置建议保持PackageInfoConfig中的默认值
      # 生成代码位于 src/main/java 下的基础包
      parent: 'top.kx.kxss'
      utilParent: 'top.kx.basic'
    entity-config:
      # 时间类型对应策略  ONLY_DATE: java.util  SQL_PACK:java.sql  TIME_PACK: java.time
      dateType: TIME_PACK
      # Entity类的父类
      entitySuperClass: ENTITY
      # 指定生成的主键的ID类型 (${superClass} == NONE 时，新生成的实体才生效)
      idType: INPUT
      # 数据库表字段映射到实体的命名策略
      columnNaming: underline_to_camel
      # 忽略字段（字段名）
      # ignoreColumns:
      #   - xxx
      # 【实体】 是否生成字段常量
      columnConstant: false
      # 【实体、VO】是否为链式模型
      chain: true
      # 【实体、VO】 是否为lombok模型
      lombok: true
      # 乐观锁字段名称
      versionColumnName: ''
      # 乐观锁属性名称
      versionPropertyName: ''
      # 逻辑删除字段名称
      logicDeleteColumnName: ''
      # 逻辑删除属性名称
      logicDeletePropertyName: ''
      fillColumnName:
        - xxx: INSERT
      # 格式化SaveVO文件名称
      formatSaveVoFileName: ''
      # 格式化UpdateVO文件名称
      formatUpdateVoFileName: ''
      # 格式化ResultVO文件名称
      formatResultVoFileName: ''
      # 格式化 PageQuery 文件名称
      formatPageQueryFileName: ''
    mapperConfig:
      formatMapperFileName: ''
      formatXmlFileName: ''
      mapperAnnotation: false
  #    columnAnnotationTablePrefix:
  #      - xxx
      baseResultMap: true
      cache: false
      baseColumnList: true
      cacheClass: org.apache.ibatis.cache.decorators.LoggingCache
  #  serviceConfig:
  #  managerConfig:
  #  controllerConfig:
  #  webProConfig:
    fileOverrideStrategy:
      entityFileOverride: OVERRIDE
      sqlFileOverride: OVERRIDE
      mapperFileOverride: EXIST_IGNORE
      xmlFileOverride: OVERRIDE
      managerFileOverride: EXIST_IGNORE
      serviceFileOverride: EXIST_IGNORE
      controllerFileOverride: EXIST_IGNORE
      apiModelFileOverride: OVERRIDE
      langFileOverride: OVERRIDE
      indexEditTreeFileOverride: EXIST_IGNORE
      dataFileOverride: EXIST_IGNORE
    constantsPackage:
      # 业务服务 后台手动改动过的枚举
      FieldFill: com.baomidou.mybatisplus.annotation.FieldFill
      SuperClassEnum: top.kx.kxss.generator.enumeration.SuperClassEnum
      EntitySuperClassEnum: top.kx.kxss.generator.enumeration.EntitySuperClassEnum
      # common 常量
      EchoDictType: top.kx.kxss.model.constant.EchoDictType
      EchoApi: top.kx.kxss.model.constant.EchoApi
      # common 公共枚举
      HttpMethod: top.kx.kxss.model.enumeration.HttpMethod
      BooleanEnum: top.kx.kxss.model.enumeration.BooleanEnum
      StateEnum: top.kx.kxss.model.enumeration.StateEnum
      UserStatusEnum: top.kx.kxss.model.enumeration.base.UserStatusEnum
      RoleCategoryEnum: top.kx.kxss.model.enumeration.base.RoleCategoryEnum
      ActiveStatusEnum: top.kx.kxss.model.enumeration.base.ActiveStatusEnum
      OrgTypeEnum: top.kx.kxss.model.enumeration.base.OrgTypeEnum
      FileType: top.kx.kxss.model.enumeration.base.FileType
      DateType: top.kx.kxss.model.enumeration.base.DateType
      CouponStatusEnum: top.kx.kxss.model.enumeration.base.CouponStatusEnum
      SourceTypeEnum: top.kx.kxss.model.enumeration.base.SourceTypeEnum
      CodeIdentifyEnum: top.kx.kxss.model.enumeration.base.CodeIdentifyEnum
      DictClassifyEnum: top.kx.kxss.model.enumeration.system.DictClassifyEnum
      DefTenantStatusEnum: top.kx.kxss.model.enumeration.system.DefTenantStatusEnum
      DataTypeEnum: top.kx.kxss.model.enumeration.system.DataTypeEnum
      TenantConnectTypeEnum: top.kx.kxss.model.enumeration.system.TenantConnectTypeEnum
  swagger:
    docket:
      generator:
        title: 在线代码生成器模块服务
        base-package: top.kx.kxss.generator.controller
server:
  port: 18780
