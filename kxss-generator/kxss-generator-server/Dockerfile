#FROM openjdk:8-jre
FROM registry.cn-hangzhou.aliyuncs.com/hyszcm/openjdk:8-jdk-alpine
MAINTAINER Jin

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime \
    && echo "Asia/Shanghai" > /etc/timezone
ARG PROJECT_DIR
COPY ${PROJECT_DIR}/target/kxss-generator-server.jar /app.jar

ENTRYPOINT ["java", "-Xmx512m", "-Djava.security.egd=file:/dev/./urandom", "-Ddruid.mysql.usePingMethod=false", "-jar", "/app.jar"]
CMD ["--spring.profiles.active=dev"]
