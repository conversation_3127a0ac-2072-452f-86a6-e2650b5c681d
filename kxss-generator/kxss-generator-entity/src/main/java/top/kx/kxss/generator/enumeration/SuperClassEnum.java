package top.kx.kxss.generator.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import top.kx.basic.base.controller.SuperCacheController;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.controller.SuperPoiController;
import top.kx.basic.base.controller.SuperReadController;
import top.kx.basic.base.controller.SuperSimpleController;
import top.kx.basic.base.manager.SuperCacheManager;
import top.kx.basic.base.manager.SuperManager;
import top.kx.basic.base.manager.impl.SuperCacheManagerImpl;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.basic.base.service.SuperCacheService;
import top.kx.basic.base.service.SuperService;
import top.kx.basic.base.service.impl.SuperCacheServiceImpl;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.interfaces.BaseEnum;

/**
 * 父类
 *
 * <AUTHOR>
 * @date 2022/10/28 4:59 PM
 * @create [2022/10/28 4:59 PM ] [tangyh] [初始创建]
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum SuperClassEnum implements BaseEnum {
    SUPER_CLASS("01", SuperController.class.getName(),
            SuperService.class.getName(), SuperServiceImpl.class.getName(),
            SuperManager.class.getName(), SuperManagerImpl.class.getName(),
            SuperMapper.class.getName()),
    SUPER_POI_CLASS("02", SuperPoiController.class.getName(),
            SuperService.class.getName(), SuperServiceImpl.class.getName(),
            SuperManager.class.getName(), SuperManagerImpl.class.getName(),
            SuperMapper.class.getName()),
    SUPER_CACHE_CLASS("03", SuperCacheController.class.getName(),
            SuperCacheService.class.getName(), SuperCacheServiceImpl.class.getName(),
            SuperCacheManager.class.getName(), SuperCacheManagerImpl.class.getName(),
            SuperMapper.class.getName()),
    SUPER_SIMPLE_CLASS("04", SuperSimpleController.class.getName(),
            SuperService.class.getName(), SuperServiceImpl.class.getName(),
            SuperManager.class.getName(), SuperManagerImpl.class.getName(),
            SuperMapper.class.getName()),
    SUPER_READ_CLASS("05", SuperReadController.class.getName(),
            SuperService.class.getName(), SuperServiceImpl.class.getName(),
            SuperManager.class.getName(), SuperManagerImpl.class.getName(),
            SuperMapper.class.getName()),
    NONE_CS("06", "", "", "", SuperManager.class.getName(), SuperManagerImpl.class.getName(),
            SuperMapper.class.getName()),
    NONE("07", "", "", "", "", "", "");

    private String value;
    private String controller;
    private String service;
    private String serviceImpl;
    private String manager;
    private String managerImpl;
    private String mapper;

    @Override
    public String getCode() {
        return this.name();
    }

    @Override
    public String getDesc() {
        return this.name();
    }
}
