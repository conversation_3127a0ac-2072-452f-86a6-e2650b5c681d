package top.kx.kxss.generator.vo.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.kxss.generator.enumeration.EntitySuperClassEnum;
import top.kx.kxss.generator.enumeration.GenTypeEnum;
import top.kx.kxss.generator.enumeration.PopupTypeEnum;
import top.kx.kxss.generator.enumeration.SuperClassEnum;
import top.kx.kxss.generator.enumeration.TplEnum;

import java.io.Serializable;

/**
 * <p>
 * 实体类
 * 代码生成
 * </p>
 *
 * <AUTHOR>
 * @since 2022-03-01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefGenTablePageQuery", description = "代码生成")
public class DefGenTablePageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 表名称
     */
    @ApiModelProperty(value = "表名称")
    private String name;

    @ApiModelProperty(value = "数据源名称")
    private Long dsId;

    /**
     * 表描述
     */
    @ApiModelProperty(value = "表描述")
    private String comment;
    @ApiModelProperty(value = "swagger描述")
    private String swaggerComment;
    @ApiModelProperty(value = "作者")
    private String author;
    /**
     * 关联子表的表名
     */
    @ApiModelProperty(value = "关联子表的表名")
    private String subTableName;
    /**
     * 子表关联的外键名
     */
    @ApiModelProperty(value = "子表关联的外键名")
    private String subTableFkName;
    /**
     * 实体类名称
     */
    @ApiModelProperty(value = "实体类名称")
    private String entityName;
    /**
     * 非实体父类
     */
    @ApiModelProperty(value = "非实体父类")
    private SuperClassEnum superClass;
    /**
     * 实体父类
     */
    @ApiModelProperty(value = "实体父类")
    private EntitySuperClassEnum entitySuperClass;

    /**
     * 生成包路径
     */
    @ApiModelProperty(value = "生成包路径")
    private String parent;

    /**
     * 生成的服务名
     * <p>
     * 如： kxss-base-server 中的base
     * 如： kxss-system-server system
     */
    @ApiModelProperty(value = "生成的服务名")
    private String serviceName;
    /**
     * 前端应用名
     * 如：src/views目录下的 basic 和 devOperation
     * <p>
     * basic 表示基础平台
     * devOperation 表示开发运营系统
     * xxx 表示你们自己新建的xxx系统
     */
    @ApiModelProperty(value = "前端应用名")
    private String plusApplicationName;
    /**
     * 前端模块名
     * 如：src/views/devOperation 目录下的文件夹名
     * 如：src/views/basic 目录下的文件夹名
     * <p>
     */
    @ApiModelProperty(value = "前端模块名")
    private String plusModuleName;
    /**
     * 生成模块名
     * <p>
     * 如： top.kx.kxss.base.dao.common 包中的 base
     * 如： top.kx.kxss.file.dao.xxx 包中的 file
     */
    @ApiModelProperty(value = "生成模块名")
    private String moduleName;
    /**
     * 子包
     * <p>
     * 如： top.kx.kxss.base.dao.common 包中的 common
     * 如： top.kx.kxss.file.dao.xxx 包中的 xxx
     */
    @ApiModelProperty(value = "子包")
    private String childPackageName;
    /**
     * 是否添加行级租户注解
     */
    @ApiModelProperty(value = "是否添加行级租户注解")
    private Boolean isTenantLine;
    /**
     * 是否添加数据源级租户注解
     */
    @ApiModelProperty(value = "是否添加数据源级租户注解")
    private Boolean isDs;
    /**
     * 数据源级租户 数据源
     */
    @ApiModelProperty(value = "数据源")
    private String dsValue;

    /**
     * 是否为lombok模型
     */
    @ApiModelProperty(value = "是否为lombok模型")
    private Boolean isLombok;
    /**
     * 是否为链式模型
     */
    @ApiModelProperty(value = "是否为链式模型")
    private Boolean isChain;
    /**
     * 是否生成字段常量
     */
    @ApiModelProperty(value = "是否生成字段常量")
    private Boolean isColumnConstant;
    /**
     * 生成代码方式（0-打包下载 1-直接生成）
     */
    @ApiModelProperty(value = "生成代码方式")
    private GenTypeEnum genType;

    /**
     * 使用的模板
     */
    @ApiModelProperty(value = "使用的模板")
    private TplEnum tplType;
    /**
     * 弹窗方式
     */
    @ApiModelProperty(value = "弹窗方式")
    private PopupTypeEnum popupType;
    /**
     * 菜单名
     */
    @ApiModelProperty(value = "菜单名")
    private String menuName;

    /**
     * 后端生成路径（不填默认项目路径）
     */
    @ApiModelProperty(value = "后端生成路径")
    private String outputDir;
    /**
     * 前端生成路径（不填默认项目路径）
     */
    @ApiModelProperty(value = "前端生成路径")
    private String frontOutputDir;
    /**
     * 新增按钮权限编码
     */
    @ApiModelProperty(value = "新增按钮权限编码")
    private String addAuth;
    /**
     * 编辑按钮权限编码
     */
    @ApiModelProperty(value = "编辑按钮权限编码")
    private String editAuth;
    /**
     * 删除按钮权限编码
     */
    @ApiModelProperty(value = "删除按钮权限编码")
    private String deleteAuth;
    /**
     * 复制按钮权限编码
     */
    @ApiModelProperty(value = "复制按钮权限编码")
    private String copyAuth;
    /**
     * 新增按钮是否显示
     */
    @ApiModelProperty(value = "新增按钮是否显示")
    private Boolean addShow;

    @ApiModelProperty(value = "详情按钮是否显示")
    private Boolean viewShow;

    /**
     * 编辑按钮是否显示
     */
    @ApiModelProperty(value = "编辑按钮是否显示")
    private Boolean editShow;


    /**
     * 删除按钮是否显示
     */
    @ApiModelProperty(value = "删除按钮是否显示")
    private Boolean deleteShow;
    /**
     * 复制按钮是否显示
     */
    @ApiModelProperty(value = "复制按钮是否显示")
    private Boolean copyShow;
    /**
     * 其它生成选项
     */
    @ApiModelProperty(value = "其它生成选项")
    private String options;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}
