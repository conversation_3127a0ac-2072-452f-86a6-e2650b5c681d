package top.kx.kxss.generator.vo.save;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.kxss.generator.enumeration.FileOverrideStrategyEnum;
import top.kx.kxss.generator.enumeration.TemplateEnum;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/3/3 14:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = false)
@Builder
@ApiModel(value = "DefGenVO", description = "生成代码")
public class DefGenVO {

    @ApiModelProperty(value = "需生成表")
    @NotEmpty(message = "请选择需生成表")
    private List<Long> ids;

    @ApiModelProperty(value = "生成类型")
    @NotNull(message = "请选择需生成类型")
    private TemplateEnum template;
    /** 文件覆盖配置 */
    @ApiModelProperty(value = "文件覆盖配置")
    private Map<String, FileOverrideStrategyEnum> fileOverrideConfig;

}
