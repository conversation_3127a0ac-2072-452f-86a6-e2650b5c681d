package top.kx.kxss.common.pay.request;


import top.kx.kxss.common.pay.Pay;
import top.kx.kxss.common.pay.model.PayObject;
import top.kx.kxss.common.pay.net.RequestOptions;
import top.kx.kxss.common.pay.response.RefundOrderCreateResponse;

/**
 * 退款请求实现
 *
 * <AUTHOR>
 */
public class RefundOrderCreateRequest implements PayRequest<RefundOrderCreateResponse> {

    private String apiVersion = Pay.VERSION;
    private String apiUri = "api/pay/refund/refundOrder";
    private RequestOptions options;
    private PayObject bizModel = null;

    @Override
    public String getApiUri() {
        return this.apiUri;
    }

    @Override
    public String getApiVersion() {
        return this.apiVersion;
    }

    @Override
    public void setApiVersion(String apiVersion) {
        this.apiVersion = apiVersion;
    }

    @Override
    public RequestOptions getRequestOptions() {
        return this.options;
    }

    @Override
    public void setRequestOptions(RequestOptions options) {
        this.options = options;
    }

    @Override
    public PayObject getBizModel() {
        return this.bizModel;
    }

    @Override
    public void setBizModel(PayObject bizModel) {
        this.bizModel = bizModel;
    }

    @Override
    public Class<RefundOrderCreateResponse> getResponseClass() {
        return RefundOrderCreateResponse.class;
    }

}

