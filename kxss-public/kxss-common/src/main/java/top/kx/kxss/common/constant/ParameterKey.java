package top.kx.kxss.common.constant;

/**
 * 全局参数表（c_parameter）的key
 *
 * <AUTHOR>
 * @date 2020年04月02日22:37:05
 */
public interface ParameterKey {

    String LOGIN_POLICY = LoginPolicy.class.getSimpleName();

    enum LoginPolicy {
        /**
         * 一个用户在一个应用只能登录一次（如一个用户只能在一个APP上登录，也只能在一个PC端登录，但能同时登录PC和APP）
         * 后面的用户T掉前面的用户
         */
        ONLY_ONE_CLIENT,

        /**
         * 用户可以任意登录： token -> userid
         * <p>
         * 不用T人，所有人登录都行
         */
        MANY,
        /**
         * 一个用户只能登录一次， 后面的用户T掉前面的用户LOGIN_POLICY
         */
        ONLY_ONE,
        ;

        public boolean eq(String val) {
            return this.name().equalsIgnoreCase(val);
        }
    }


    /**
     * 临时开灯时长
     */
    String TEMP_LIGHT_DURATION = "TEMP_LIGHT_DURATION";
    /**
     * 关灯提醒时长
     */
    String CLOSE_LIGHT_REMIND = "CLOSE_LIGHT_REMIND";
    /**
     * 台桌预订时长
     */
    String RESERVE_TABLE_DURATION = "RESERVE_TABLE_DURATION";

    /**
     * 每日分账时间(整点)
     */
    String DISTRIBUTION_TIME = "DISTRIBUTION_TIME";

    /**
     * 关键客户
     */
    String KEY_CUSTOMER = "KEY_CUSTOMER";

    /**
     * 忙碌时,可以点助教
     */
    String BUSY_EMPLOYEE_ORDER = "BUSY_EMPLOYEE_ORDER";

    /**
     * 重要客户
     */
    String IMPORTANT_CUSTOMER = "IMPORTANT_CUSTOMER";

    /**
     * 普通客户
     */
    String ORDINARY_CUSTOMER = "ORDINARY_CUSTOMER";

    /**
     * 待维护客户
     */
    String MAINTAINED_CUSTOMER = "MAINTAINED_CUSTOMER";
    /**
     * 是否发送短信
     */
    String IS_SEND_MESSAGE = "IS_SEND_MESSAGE";
    /**
     * 是否打印
     */
    String IS_PRINT = "IS_PRINT";
    /**
     * 是否网络打印
     */
    String IS_NETWORK_PRINT = "IS_NETWORK_PRINT";
    /**
     * 配送打印
     */
    String IS_DISTRIBUTE_PRINT = "IS_DISTRIBUTE_PRINT";

    /**
     * 会员消费验证密码
     */
    String IS_MEMBER_PASSWORD = "IS_MEMBER_PASSWORD";

    /**
     * 会员绑定是否需要密码验证
     */
    String IS_MEMBER_PASSWORD_ON_BIND = "IS_MEMBER_PASSWORD_ON_BIND";


    /**
     * 会员手机号唯一
     */
    String IS_MEMBER_MOBILE = "IS_MEMBER_MOBILE";

    /**
     * 收银台验证密码
     */
    String POS_BUSINESS_AUTH = "POS_BUSINESS_AUTH";

    /**
     * 切换台桌验证密码
     */
    String CHANGE_TABLE_AUTH = "CHANGE_TABLE_AUTH";

    /**
     * 开台语音
     */
    String OPEN_VOICE = "OPEN_VOICE";

    /**
     * 绑定会员语音
     */
    String POS_CASH_BIND_MEMBER_VOICE = "POS_CASH_BIND_MEMBER_VOICE";

    /**
     * 会员余额支付语音
     */
    String MEMBER_BALANCE_PAYMENT_VOICE = "MEMBER_BALANCE_PAYMENT_VOICE";

    /**
     * 结账成功语音
     */
    String PAYMENT_SUCCESS_VOICE = "PAYMENT_SUCCESS_VOICE";
    /**
     * 会员充值结账语音
     */
    String MEMBER_RECHARGE_SUCCESS_VOICE = "MEMBER_RECHARGE_SUCCESS_VOICE";
    /**
     * 购物语音
     */
    String SHOPPING_VOICE = "SHOPPING_VOICE";
    /**
     * 会员购物语音
     */
    String MEMBER_SHOPPING_VOICE = "MEMBER_SHOPPING_VOICE";
    /**
     * 配送单语音
     */
    String DISTRIBUTE_VOICE = "DISTRIBUTE_VOICE";

    /**
     * 倒计时语音
     */
    String COUNTDOWN_VOICE = "COUNTDOWN_VOICE";

    /**
     * 自动挂单语言
     */
    String AUTO_REGISTRATION_VOICE = "AUTO_REGISTRATION_VOICE";

    /**
     * 呼叫服务语音
     */
    String CALL_OUT_SERVE_VOICE = "CALL_OUT_SERVE_VOICE";

    /**
     * 呼叫服务间隔时长
     */
    String CALL_OUT_SERVE_TIME = "CALL_OUT_SERVE_TIME";

    /**
     * 采购入库
     */
    String OUTIN_IN_STOCK_AUDIT = "OUTIN_IN_STOCK_AUDIT";

    /**
     * 采购退货
     */
    String OUTIN_RETURN_STOCK_AUDIT = "OUTIN_RETURN_STOCK_AUDIT";

    /**
     * 库存盘点审核
     */
    String OUTIN_STOCKTAKING_AUDIT = "OUTIN_STOCKTAKING_AUDIT";

    /**
     * 调库审核
     */
    String OUTIN_ADJUSTMENT_AUDIT = "OUTIN_ADJUSTMENT_AUDIT";

    /**
     * 其他出库
     */
    String OUTIN_OTHER_OUT_STOCK_AUDIT = "OUTIN_OTHER_OUT_STOCK_AUDIT";

    /**
     * 其他入库
     */
    String OUTIN_OTHER_IN_STOCK_AUDIT = "OUTIN_OTHER_IN_STOCK_AUDIT";

    /**
     * 采购入库红冲审核
     */
    String OUTIN_IN_REVERSAL_ENTRY_AUDIT = "OUTIN_IN_REVERSAL_ENTRY_AUDIT";


    /**
     * 采购退货红冲
     */
    String OUTIN_OUT_REVERSAL_ENTRY_AUDIT = "OUTIN_OUT_REVERSAL_ENTRY_AUDIT";


    /**
     * 是否手动控制灯光 开启当前设置后，收银系统的开台和结账将不会控制灯光
     */
    String MANUAL_CONTROL_LIGHTS = "MANUAL_CONTROL_LIGHTS";


    /**
     * 自动抹零方式
     */
    String AUTO_WIPE_ZERO_TYPE = "AUTO_WIPE_ZERO_TYPE";
    /**
     * 会员列表展示全部
     */
    String IS_MEMBER_ALL = "IS_MEMBER_ALL";
    /**
     * 待结账自动挂单时间
     */
    String AUTO_PENDING_ORDER_DURATION = "AUTO_PENDING_ORDER_DURATION";
    /**
     * 扫码购物是否挂台
     */
    String SCAN_SHOPPING_IS_REG_TABLE = "SCAN_SHOPPING_IS_REG_TABLE";

    /**
     * 反结账操作时间限制
     */
    String COUNTER_CHECKOUT_TIME = "COUNTER_CHECKOUT_TIME";

    /**
     * 商品下单语音
     */
    String ORDER_PRODUCT_SOUND = "ORDER_PRODUCT_SOUND";

    /**
     * 是否扫码开台
     */
    String IS_SCAN_OPENING = "IS_SCAN_OPENING";
    /**
     * 小程序点商品支付类型 商品预付 商品后付 两者都可以
     */
    String SCAN_SHOPPING_ONLINE_PAY_TYPE = "SCAN_SHOPPING_ONLINE_PAY_TYPE";

    /**
     * 助教时长展示方式
     */
    String SERVICE_STAFF_TIME = "SERVICE_STAFF_TIME";

    /**
     * 点单屏点单方式
     */
    String IPAD_ORDER_TYPE = "IPAD_ORDER_TYPE";

    /**
     * 是否完整展示助教手机号
     */
    String SHOW_SERVICE_STAFF_MOBILE = "SHOW_SERVICE_STAFF_MOBILE";


    /**
     * 是否允许助教时长超过台桌时长
     */
    String SERVICE_MORE_THAN_TABLE_DURATION = "SERVICE_MORE_THAN_TABLE_DURATION";

    /**
     * 购物是否支持点助教
     */
    String ADD_SERVICE_WHEN_SHOPPING = "ADD_SERVICE_WHEN_SHOPPING";

}
