package top.kx.kxss.common.utils;

import cn.hutool.core.util.StrUtil;

import java.time.Instant;
import java.util.concurrent.ThreadLocalRandom;

/**
 * <AUTHOR>
 * @date 2025/5/7 13:57
 */
public class TemplateCodeGenerator {
    // 基础前缀
    private static final String PREFIX = "ST_";
    // 编号长度（示例中474100461为9位）
    private static final int CODE_LENGTH = 9;

    /**
     * 生成短信模板编码
     */
    public static String generateTemplateCode(String prefix) {
        StringBuilder code = new StringBuilder(StrUtil.isEmptyIfStr(prefix) ? PREFIX : prefix);
        // 时间戳部分（取后6位）
        // 截取后6位
        String timestampPart = String.valueOf(Instant.now().toEpochMilli())
                .substring(7);

        // 随机数部分（补足剩余位数）
        int randomLength = CODE_LENGTH - timestampPart.length();
        String randomPart = String.valueOf(
                ThreadLocalRandom.current().nextLong(
                        (long) Math.pow(10, randomLength - 1),
                        (long) Math.pow(10, randomLength)
                )
        );
        return code
                .append(timestampPart)
                .append(randomPart)
                .toString();
    }

    public static void main(String[] args) {
        System.out.println(generateTemplateCode(null));
    }
}
