package top.kx.kxss.common.cache.base.common;


import top.kx.basic.model.cache.CacheKey;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.kxss.common.cache.CacheKeyModular;
import top.kx.kxss.common.cache.CacheKeyTable;

/**
 * <AUTHOR>
 */
public class CashPartRefundBuilder implements CacheKeyBuilder {

    public static CacheKey build(String posCashId) {
        return new CashPartRefundBuilder().key(posCashId);
    }


    @Override
    public String getPrefix() {
        return CacheKeyModular.PREFIX;
    }

    @Override
    public String getTable() {
        return CacheKeyTable.Base.PART_REFUND;
    }

    @Override
    public String getModular() {
        return CacheKeyModular.POS;
    }

    @Override
    public String getField() {
        return "key";
    }

    @Override
    public ValueType getValueType() {
        return ValueType.string;
    }
}
