package top.kx.kxss.common.cache.common;


import top.kx.basic.model.cache.CacheKey;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.kxss.common.cache.CacheKeyModular;

import java.time.Duration;

/**
 * 参数 KEY
 * <p>
 * #c_application
 *
 * <AUTHOR>
 * @date 2020/9/20 6:45 下午
 */
public class IdempotentCacheKeyBuilder implements CacheKeyBuilder {
    public static CacheKey builder(String key) {
        return new IdempotentCacheKeyBuilder().key(key);
    }

    @Override
    public String getTable() {
        return "idempotent";
    }

    @Override
    public String getPrefix() {
        return CacheKeyModular.PREFIX;
    }

    @Override
    public String getModular() {
        return CacheKeyModular.COMMON;
    }

    @Override
    public String getField() {
        return null;
    }

    @Override
    public String getOrgId() {
        return null;
    }

    @Override
    public ValueType getValueType() {
        return ValueType.string;
    }

    @Override
    public Duration getExpire() {
        return Duration.ofSeconds(5L);
    }
}
