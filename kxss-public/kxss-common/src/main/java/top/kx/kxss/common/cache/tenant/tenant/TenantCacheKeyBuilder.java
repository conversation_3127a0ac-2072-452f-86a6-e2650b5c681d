package top.kx.kxss.common.cache.tenant.tenant;

import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.model.cache.CacheKey;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.basic.utils.StrPool;
import top.kx.kxss.common.cache.CacheKeyModular;
import top.kx.kxss.common.cache.CacheKeyTable;

import java.time.Duration;

/**
 * 租户 KEY
 * [服务模块名:]业务类型[:业务字段][:value类型][:租户id] -> obj
 * tenant:def_tenant:id:obj:1 -> {}
 *
 * <p>
 * #def_tenant
 *
 * <AUTHOR>
 * @date 2020/9/20 6:45 下午
 */
public class TenantCacheKeyBuilder implements CacheKeyBuilder {
    public static CacheKey builder(Long id) {
        return new TenantCacheKeyBuilder().key(id);
    }

    @Override
    public String getPrefix() {
        return CacheKeyModular.PREFIX;
    }

    @Override
    public String getTenant() {
        return StrPool.EMPTY;
    }

    @Override
    public String getOrgId() {
        return StrPool.EMPTY;
    }

    @Override
    public String getModular() {
        return CacheKeyModular.SYSTEM;
    }

    @Override
    public String getTable() {
        return CacheKeyTable.System.TENANT;
    }

    @Override
    public String getField() {
        return SuperEntity.ID_FIELD;
    }

    @Override
    public ValueType getValueType() {
        return ValueType.obj;
    }

    @Override
    public Duration getExpire() {
        return Duration.ofHours(24);
    }
}
