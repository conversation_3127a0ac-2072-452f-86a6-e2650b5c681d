package top.kx.kxss.common.cache.wxapp;

import top.kx.basic.model.cache.CacheHashKey;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.kxss.common.cache.CacheKeyModular;
import top.kx.kxss.common.cache.CacheKeyTable;

import java.io.Serializable;
import java.time.Duration;

/**
 * 租户 KEY
 * [服务模块名:]业务类型[:业务字段][:value类型][:租户id] -> obj
 * tenant:def_tenant:id:obj:1 -> {}
 *
 * <p>
 * #def_tenant
 *
 * <AUTHOR>
 * @date 2020/9/20 6:45 下午
 */
public class TenantOrgUserCacheKeyBuilder implements CacheKeyBuilder {
    public static CacheHashKey builder(Serializable dictKey) {
        return new TenantOrgUserCacheKeyBuilder().hashKey(dictKey);
    }

    public static CacheHashKey builder(Long key, String field) {
        return new TenantOrgUserCacheKeyBuilder().hashFieldKey(field, key);
    }

    @Override
    public String getPrefix() {
        return CacheKeyModular.PREFIX;
    }

    @Override
    public String getTable() {
        return CacheKeyTable.Wxapp.TENANT_ORG_USER;
    }

    @Override
    public String getModular() {
        return CacheKeyModular.WXAPP;
    }

    @Override
    public String getField() {
        return "key";
    }

    @Override
    public String getTenant() {
        return null;
    }
    @Override
    public String getOrgId() {
        return null;
    }
    @Override
    public CacheKeyBuilder.ValueType getValueType() {
        return CacheKeyBuilder.ValueType.obj;
    }

    @Override
    public Duration getExpire() {
        return Duration.ofHours(10);
    }
}
