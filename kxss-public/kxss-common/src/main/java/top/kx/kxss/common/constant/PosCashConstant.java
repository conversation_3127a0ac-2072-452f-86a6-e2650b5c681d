package top.kx.kxss.common.constant;

import lombok.Getter;
import top.kx.kxss.common.event.EventModel;

import java.util.Objects;

/**
 * 点单事件
 *
 * <AUTHOR>
 * @date 2019/08/06
 */
public interface PosCashConstant {

    /**
     * 事件定义
     */
    enum Event implements EventModel {


        /**
         * 开台
         */
        OPENING_TABLE("1001", "OPENING_TABLE"),
        /**
         * 刷新台桌
         */
        REFRESH_TABLE("1002", "REFRESH_TABLE"),
        /**
         * 计算价格
         */
        CALC_PRICE("1003", "CALC_PRICE"),
        /**
         * 释放台桌
         */
        RELEASE_TABLE("1004", "RELEASE_TABLE"),
        /**
         * 支付信息
         */
        PAYMENT_INFO("1005", "PAYMENT_INFO"),
        /**
         * 整单取消
         */
        CANCEL("1006", "CANCEL"),
        /**
         * 支付成功
         */
        PAYMENT_SUCCESS("1009", "PAYMENT_SUCCESS"),
        /**
         * 余额支付
         */
        BALANCE_PAYMENT("1010", "BALANCE_PAYMENT"),
        /**
         * 创建充值
         */
        RECHARGE_CREATED("1011", "RECHARGE_CREATED"),
        /**
         * 充值成功
         */
        RECHARGE_SUCCESS("1012", "RECHARGE_SUCCESS"),
        /**
         * 刷新服务
         */
        REFRESH_SERVICE("10013", "REFRESH_SERVICE"),
        /**
         * 挂单
         */
        REGISTRATION("10014", "REGISTRATION"),


        /**
         * 审核
         */
        EXAMINE("10015", "EXAMINE"),

        /**
         * 反结账
         */
        COUNTER_CHECKOUT("10016", "COUNTER_CHECKOUT"),
        /**
         * 续费订单
         */
        RENEW_IMMEDIATELY("10017", "RENEW_IMMEDIATELY"),


        /**
         * 开灯
         */
        OPEN_LIGHT("2001", "OPEN_LIGHT"),
        /**
         * 关灯
         */
        CLOSE_LIGHT("2002", "CLOSE_LIGHT"),
        /**
         * 临时开灯
         */
        TEMP_OPEN_LIGHT("2004", "TEMP_OPEN_LIGHT"),
        /**
         * 临时关灯
         */
        TEMP_CLOSE_LIGHT("2003", "TEMP_CLOSE_LIGHT"),
        /**
         * 台桌预订
         */
        RESERVE_TABLE("2005", "RESERVE_TABLE"),
        /**
         * 取消台桌预订
         */
        CANCEL_RESERVE_TABLE("2006", "CANCEL_RESERVE_TABLE"),
        /**
         * 添加日志
         */
        ADD_BIZ_LOG("2007", "ADD_BIZ_LOG"),
        /**
         * 套餐验券
         */
        THAIL_CHECK_VOUCHER("2008", "THAIL_CHECK_VOUCHER"),


        /**
         * 台桌操作
         */
        TABLE("4001", "TABLE"),
        /**
         * 订单操作
         */
        ORDER("4002", "ORDER"),
        /**
         * 会员操作
         */
        MEMBER("4003", "MEMBER"),


        /**
         * 绑定卡
         */
        BIND_CARD("3002", "BIND_CARD"),

        /**
         * 添加商品
         */
        ADD_PRODUCT("3003", "ADD_PRODUCT"),
        /**
         * 添加服务
         */
        ADD_SERVICE("3004", "ADD_SERVICE"),

        /**
         * 绑定优惠券
         */
        BIND_COUPON("3005", "BIND_COUPON"),
        /**
         * 绑定会员
         */
        BIND_MEMBER("3006", "BIND_MEMBER"),
        /**
         * 绑定备注
         */
        BIND_REMARKS("3007", "BIND_REMARKS"),
        /**
         * 绑定折扣
         */
        BIND_DISCOUNT("3008", "BIND_DISCOUNT"),

        /**
         * 订单支付
         */
        ORDER_PAY("3009", "ORDER_PAY"),

        /**
         * 订单查询
         */
        ORDER_QUERY("3010", "ORDER_QUERY"),
        /**
         * 订单退款
         */
        ORDER_REFUND("3011", "ORDER_REFUND"),
        /**
         * 预付退
         */
        PREPAID_REFUND("3012", "PREPAID_REFUND"),

        /**
         * 创建购物
         */
        SHOPPING_CREATED("5001", "SHOPPING_CREATED"),
        /**
         * 打赏购物
         */
        REWARD_CREATED("5002", "REWARD_CREATED"),
        /**
         * 短信充值
         */
        SMS_RECHARGE("5003", "SMS_RECHARGE"),

        /**
         * 购卡
         */
        BUY_CARD_CREATED("6001", "BUY_CARD_CREATED"),

        /**
         * 提成人
         */
        COMMENTER("6002", "COMMENTER"),

        /**
         * 换桌
         */
        TURN("6003", "换桌"),
        /**
         * 临时订单
         */
        TEMP_OPEN_TABLE("7001", "临时订单"),


        ;


        /**
         * 编码
         */
        @Getter
        private final String code;

        @Getter
        private final String desc;

        //构造方法
        Event(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static Event getState(String state) {
            for (Event orderState : Event.values()) {
                if (Objects.equals(orderState.code, state)) {
                    return orderState;
                }
            }
            return null;
        }

        @Override
        public String getEventId() {
            return this.getCode();
        }
    }


}
