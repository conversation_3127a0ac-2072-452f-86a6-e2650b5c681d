package top.kx.kxss.common.pay.model;

import lombok.Getter;
import lombok.Setter;
import top.kx.kxss.common.pay.ApiField;

/**
 * 支付关闭请求实体类
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class PayOrderCloseReqModel extends PayObject {

    private static final long serialVersionUID = -5184554341263929245L;

    /**
     * 商户号
     */
    @ApiField("mchNo")
    private String mchNo;
    /**
     * 应用ID
     */
    @ApiField("appId")
    private String appId;
    /**
     * 商户订单号
     */
    @ApiField("mchOrderNo")
    String mchOrderNo;
    /**
     * 支付订单号
     */
    @ApiField("payOrderId")
    String payOrderId;

}
