package top.kx.kxss.common.pay.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import top.kx.kxss.common.pay.ApiField;

/**
 * 支付下单请求实体类
 *
 * <AUTHOR>
 * @date 2024/5/24 18:05
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PayOrderCreateReqModel extends PayObject {

    private static final long serialVersionUID = -3998573128290306948L;

    // 商户号
    @ApiField("appId")
    private String appId;
    // 应用ID
    @ApiField("mchNo")
    private String mchNo;
    // 商户订单号
    @ApiField("wayCode")
    String wayCode;
    // 支付方式
    @ApiField("mchOrderNo")
    String mchOrderNo;
    // 货币代码，当前只支持cny
    @ApiField("currency")
    String currency;
    // 支付金额
    @ApiField("amount")
    Long amount;

    // 商品标题
    @ApiField("subject")
    String subject;
    // 客户端IP
    @ApiField("clientIp")
    String clientIp;
    // 商品描述
    @ApiField("body")
    String body;
    // 异步通知地址
    @ApiField("notifyUrl")
    String notifyUrl;
    // 跳转通知地址
    @ApiField("returnUrl")
    String returnUrl;
    // 订单失效时间
    @ApiField("expiredTime")
    String expiredTime;
    // 特定渠道额外支付参数
    @ApiField("channelExtra")
    String channelExtra;
    // 渠道用户标识,如微信openId,支付宝账号
    @ApiField("channelUser")
    String channelUser;
    // 商户扩展参数
    @ApiField("extParam")
    String extParam;
    // 分账模式： 0-该笔订单不允许分账[默认], 1-支付成功按配置自动完成分账, 2-商户手动分账(解冻商户金额)
    @ApiField("divisionMode")
    private Integer divisionMode;

    public PayOrderCreateReqModel() {
    }

}
