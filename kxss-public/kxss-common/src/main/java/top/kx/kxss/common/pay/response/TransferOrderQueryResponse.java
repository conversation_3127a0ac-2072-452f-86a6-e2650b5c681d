package top.kx.kxss.common.pay.response;


import top.kx.kxss.common.pay.model.TransferOrderQueryResModel;

/**
 * 转账查单响应实现
 * <AUTHOR>
 */
public class TransferOrderQueryResponse extends PayResponse {

    private static final long serialVersionUID = 7419683269497002904L;

    public TransferOrderQueryResModel get() {
        if (getData() == null) {
            return new TransferOrderQueryResModel();
        }
        return getData().toJavaObject(TransferOrderQueryResModel.class);
    }

    @Override
    public boolean isSuccess(String apiKey) {
        if (super.isSuccess(apiKey)) {
            int state = get().getState();
            return state == 0 || state == 1 || state == 2;
        }
        return false;
    }

}
