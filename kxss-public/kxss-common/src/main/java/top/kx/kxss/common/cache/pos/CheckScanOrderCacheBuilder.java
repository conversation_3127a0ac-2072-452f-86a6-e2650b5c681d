package top.kx.kxss.common.cache.pos;

import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.model.cache.CacheKey;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.kxss.common.cache.CacheKeyModular;
import top.kx.kxss.common.cache.CacheKeyTable;

/**
 *
 * <AUTHOR>
 */
public class CheckScanOrderCacheBuilder implements CacheKeyBuilder {
    public static CacheKey build(Long posCashId) {
        return new CheckScanOrderCacheBuilder().key(posCashId);
    }

    @Override
    public String getTable() {
        return CacheKeyTable.Pos.CHECK_SCAN_ORDER;
    }

    @Override
    public String getPrefix() {
        return CacheKeyModular.PREFIX;
    }

    @Override
    public String getModular() {
        return CacheKeyModular.POS;
    }

    @Override
    public String getField() {
        return SuperEntity.ID_FIELD;
    }

    @Override
    public ValueType getValueType() {
        return ValueType.obj;
    }
}
