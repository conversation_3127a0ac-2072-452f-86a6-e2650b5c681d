package top.kx.kxss.common.pay.response;

import top.kx.kxss.common.pay.model.PayOrderQueryResModel;

/**
 * 支付查单响应实现
 * <AUTHOR>
 */
public class PayOrderQueryResponse extends PayResponse {

    private static final long serialVersionUID = 7654172640802954221L;

    public PayOrderQueryResModel get() {
        if (getData() == null) {
            return new PayOrderQueryResModel();
        }
        return getData().toJavaObject(PayOrderQueryResModel.class);
    }

}
