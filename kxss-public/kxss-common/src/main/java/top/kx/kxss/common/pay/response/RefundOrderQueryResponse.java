package top.kx.kxss.common.pay.response;


import top.kx.kxss.common.pay.model.RefundOrderQueryResModel;

/**
 * 退款查单响应实现
 * <AUTHOR>
 */
public class RefundOrderQueryResponse extends PayResponse {

    private static final long serialVersionUID = 7654172640802954221L;

    public RefundOrderQueryResModel get() {
        if (getData() == null) return new RefundOrderQueryResModel();
        return getData().toJavaObject(RefundOrderQueryResModel.class);
    }

}
