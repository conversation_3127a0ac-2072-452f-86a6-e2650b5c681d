package top.kx.kxss.common.pay.response;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;
import top.kx.kxss.common.utils.PayKit;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2024/5/24 17:46
 */
@Getter
@Setter
public abstract class PayResponse implements Serializable {

    @Getter
    private static final long serialVersionUID = -2637191198247207952L;

    private Integer code;
    private String msg;
    private String sign;
    private JSONObject data;

    /**
     * 校验响应数据签名是否正确
     */
    public boolean checkSign(String apiKey) {
        if (data == null && StrUtil.isBlank(getSign())) return true;
        return sign.equals(PayKit.getSign(getData(), apiKey));
    }

    /**
     * 校验是否成功(只判断code为0，具体业务要看实际情况)
     *
     * @param apiKey
     * @return
     */
    public boolean isSuccess(String apiKey) {
        if (StrUtil.isBlank(apiKey)) return code == 0;
        return code == 0 && checkSign(apiKey);
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
