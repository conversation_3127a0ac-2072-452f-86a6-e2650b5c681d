package top.kx.kxss.common.easyexcel;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 支付下单请求实体类
 *
 * <AUTHOR>
 * @date 2024/5/24 18:05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TableColsModel implements Serializable {


    /**
     * 列标识
     */
    private String columnField;

    /**
     * 列名称
     */
    private String columnName;

    /**
     * 父列名称
     */
    private String columnParent;
}
