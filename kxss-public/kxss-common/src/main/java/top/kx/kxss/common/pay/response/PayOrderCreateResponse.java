package top.kx.kxss.common.pay.response;


import top.kx.kxss.common.pay.model.PayOrderCreateResModel;

/**
 * 支付下单响应实现
 * <AUTHOR>
 */
public class PayOrderCreateResponse extends PayResponse {

    private static final long serialVersionUID = 7419683269497002904L;

    public PayOrderCreateResModel get() {
        if (getData() == null) {
            return new PayOrderCreateResModel();
        }
        return getData().toJavaObject(PayOrderCreateResModel.class);
    }

    @Override
    public boolean isSuccess(String apiKey) {
        if (super.isSuccess(apiKey)) {
            int orderState = get().getOrderState();
            return orderState == 0 || orderState == 1 || orderState == 2;
        }
        return false;
    }

}
