package top.kx.kxss.common.constant;

/**
 * 定时任务 常量
 *
 * <AUTHOR>
 * @date 2021/1/8 10:16 上午
 */
public interface JobConstant {

    /**
     * 默认的定时任务组
     */
    String DEF_BASE_JOB_GROUP_NAME = "kxss-base-executor";
    String DEF_EXTEND_JOB_GROUP_NAME = "kxss-extend-executor";

    /**
     * 任务调度名称
     */
    String JOB_GROUP_NAME = "kxss-datasource-executor";
    /**
     * 调度类型
     */
    String SCHEDULE_TYPE = "CRON";
    /**
     * 固定速度轮询
     */
    String SCHEDULE_TYPE_FIX_RATE = "FIX_RATE";

    /**
     * 调度类型
     */
    String MISFIRE_STRATEGY = "DO_NOTHING";
    /**
     * 执行器路由策略
     */
    String EXECUTOR_ROUTE_STRATEGY = "ROUND";
    /**
     * 执行器路由策略
     */
    String EXECUTOR_BLOCK_STRATEGY = "SERIAL_EXECUTION";
    /**
     * GLUE类型
     */
    String GLUE_TYPE = "BEAN";
    /**
     * 短信发送处理器
     */
    String SMS_SEND_JOB_HANDLER = "smsSendJobHandler";



}
