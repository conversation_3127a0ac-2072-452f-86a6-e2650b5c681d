package top.kx.kxss.common.pay.request;

import top.kx.kxss.common.pay.model.PayObject;
import top.kx.kxss.common.pay.net.RequestOptions;
import top.kx.kxss.common.pay.response.PayResponse;

/**
 * <AUTHOR>
 * @date 2024/5/24 17:45
 */
public interface PayRequest<T extends PayResponse>{

    /**
     * 获取当前接口的路径
     * @return
     */
    String getApiUri();

    /**
     * 获取当前接口的版本
     * @return
     */
    String getApiVersion();

    /**
     * 设置当前接口的版本
     * @return
     */
    void setApiVersion(String apiVersion);

    RequestOptions getRequestOptions();

    void setRequestOptions(RequestOptions options);

    PayObject getBizModel();

    void setBizModel(PayObject bizModel);

    Class<T> getResponseClass();
}
