package top.kx.kxss.common.annotation;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Sensitive {
    /**
     * 是否微信服务通知
     */
    boolean isWxNotice() default false;

    /**
     * 是否pos通知
     */
    boolean isPosNotice() default true;

    /**
     * 订单ID
     */
    String posCashId() default "";

}
