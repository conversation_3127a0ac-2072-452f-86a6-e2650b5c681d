package top.kx.kxss.common.config;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * BigDecimal返回转换为两位小数
 *
 * <AUTHOR>
 * @date 2023/8/27 12:13
 */
public class BigDecimalSerializer extends JsonSerializer<BigDecimal> {

    @Override
    public void serialize(BigDecimal value, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {
        if (ObjUtil.isNotNull(value)) {
            // 保留2位小数，向上取整
            jsonGenerator.writeString(NumberUtil.decimalFormat("##0.00", value, RoundingMode.HALF_UP));
        } else {
            jsonGenerator.writeString(BigDecimal.ZERO.toPlainString());
        }
    }

}
