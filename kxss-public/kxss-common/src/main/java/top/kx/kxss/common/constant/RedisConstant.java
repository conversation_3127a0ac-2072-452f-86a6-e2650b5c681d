package top.kx.kxss.common.constant;

/**
 * redis相关Key
 *
 * <AUTHOR>
 * @date 2023/8/25 19:24
 */
public class RedisConstant {

    /**
     * redis geo key
     */
    public static final String GEO = "def:tenant:geo";
    public static final String QR_CODE = "def:qr_code:";
    public static final String TEMP_QUERY = "tempquery:";
    public static final String TEMP_CONDITION = "tempquery:condition:";
    public static final String MQTT_RESPONSE = "mqtt:";
    public static final String WX_COMPONENT_TICKET = "wx:component:component_verify_ticket";
    public static final String WX_COMPONENT_TOKEN = "wx:component:component_access_token";
    public static final String WX_COMPONENT_PER_AUTH_CODE = "wx:component:per_auth_code:";
    public static final String WX_COMPONENT_QUERY_AUTH = "wx:component:query_auth:";
    public static final String WX_COMPONENT_PAYMENT_ORDER_ID = "wx:component:payment:order_id:";
    public static final String CALL_OUT_SERVE_TIME_ORDER_ID = "call:out:serve:time:order_id:";
    public static final String CALL_OUT_SERVE_TIME_TABLE_ID = "call:out:serve:time:table_id:";
    /**
     * 主屏
     */
    public static final String MAIN_SCREEN = "screen:main:";
    /**
     * 副屏
     */
    public static final String SECOND_SCREEN = "screen:second:";

    /**
     * 订单会员变动
     */
    public static final String CASH_BIND_MEMBER = "lc:cash:member:";
    /**
     * 扫码
     */
    public static final String DEF_SCAN = "def:scan:";

    /**
     * 修改密码通知
     */
    public static final String UPDATE_PASSWORD_MSG = "def:user:password:code:";

    /**
     * 修改手机号通知
     */
    public static final String UPDATE_MOBILE_MSG = "def:user:mobile:code:";

    /**
     * 修改手机号通知
     */
    public static final String UPDATE_EMPLOYEE_MOBILE_MSG = "employee:user:mobile:code:";

    /**
     * 获取在线用户信息
     */
    public static final String USER_ONLINE = "lc:oauth:online:";

    /**
     * 团购充值信息
     */
    public static final String GROUP_RECHARGE = "lc:pay:group_recharge:";
    /**
     * 订阅
     */
    public static final String SUBSCRIPTION_ORDER = "lc:pay:subscription_order:";

    /**
     * sse
     */
    public static final String SEE_EMITTER = "lc:sse:emitter:";

    /**
     *
     */
    public static final String TENANT_ORG_USER = "lc:wxapp:tenant_org_user:";
    /**
     * 后厨打印标识
     */
    public static final String TENANT_PRINT_KITCHEN = "lc:print:kitchen:";


    /**
     * 变动订单数量key
     */
    public static final String CASH_CHANGE_NUM = "lc:cash:change_num:";
}
