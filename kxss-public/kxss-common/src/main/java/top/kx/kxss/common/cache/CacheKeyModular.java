package top.kx.kxss.common.cache;

/**
 * 缓存模块
 *
 * <AUTHOR>
 * @date 2020/10/21
 */
public interface CacheKeyModular {
    String PREFIX = "lc";

    /**
     * 多个服务都会使用的缓存
     */
    String COMMON = "common";
    /**
     * 仅基础服务base使用的缓存
     */
    String BASE = "base";
    /**
     * 仅消息服务msg使用的缓存
     */
    String MSG = "msg";
    /**
     * 仅认证服务oauth使用的缓存
     */
    String OAUTH = "oauth";
    /**
     * 仅文件服务file使用的缓存
     */
    String FILE = "file";
    /**
     * 仅租户服务tenant使用的缓存
     */
    String SYSTEM = "system";
    /**
     * 仅网关服务gateway使用的缓存
     */
    String GATEWAY = "gateway";

    /**
     * 收银端服务
     */
    String POS = "pos";

    /**
     * 老板管理端
     */
    String WXAPP = "wxapp";
}
