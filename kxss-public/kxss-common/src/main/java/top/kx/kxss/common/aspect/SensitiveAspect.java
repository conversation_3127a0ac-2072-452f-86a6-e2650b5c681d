package top.kx.kxss.common.aspect;


import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.context.annotation.Configuration;
import top.kx.kxss.common.annotation.Sensitive;

import java.lang.reflect.Method;

/**
 * 敏感操作通知
 *
 * <AUTHOR>
 */
@Slf4j
@Aspect
@Configuration
public class SensitiveAspect {

    private static String posCashId = null;

    @Pointcut("@annotation(top.kx.kxss.common.annotation.Sensitive)")
    public void sensitivePointcut() {
        System.out.println("Pointcut: 不会被执行");
    }


    // returning的值和doAfterReturning的参数名一致
    @AfterReturning(pointcut = "sensitivePointcut()&&@annotation(anno)", returning = "ret")
    public void doAfterReturning(JoinPoint joinPoint, Sensitive anno, Object ret) throws Throwable {
        String params = JSONUtil.toJsonStr(joinPoint.getArgs());
        // 处理完请求，返回内容
        log.info("返回值 : " + params);
        log.info("返回值 : " + anno.posCashId());
        log.info("返回值 : " + anno.isPosNotice());
        log.info("返回值 : " + anno.isWxNotice());
        Sensitive targetAnnotation = getTargetAnnotation(joinPoint);
        // 处理完请求，返回内容
        log.info("返回值 : " + targetAnnotation.posCashId());
        log.info("返回值 : " + targetAnnotation.isPosNotice());
        log.info("返回值 : " + targetAnnotation.isWxNotice());
        log.info("返回值 : " + ret);
    }

    public Sensitive getTargetAnnotation(JoinPoint point) {
        try {
            Sensitive annotation = null;
            if (point.getSignature() instanceof MethodSignature) {
                Method method = ((MethodSignature) point.getSignature()).getMethod();
                if (method != null) {
                    annotation = method.getAnnotation(Sensitive.class);
                }
            }
            return annotation;
        } catch (Exception e) {
            log.warn("获取 {}.{} 的 @WebLog 注解失败", point.getSignature().getDeclaringTypeName(), point.getSignature().getName(), e);
            return null;
        }
    }
}
