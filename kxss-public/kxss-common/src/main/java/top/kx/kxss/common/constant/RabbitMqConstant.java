package top.kx.kxss.common.constant;

/**
 * redis相关Key
 *
 * <AUTHOR>
 * @date 2023/8/25 19:24
 */
public class RabbitMqConstant {

    /**
     * 主题交换机
     */
    public static final String TOPIC_EXCHANGE = "topicExchange";

    /**
     * 报表主题交换机
     */
    public static final String REPORT_EXCHANGE = "reportExchange";
    /**
     * 系统交换机
     */
    public static final String SYSTEM_EXCHANGE = "reportExchange";

    public static final String FANOUT_EXCHANGE = "fanoutExchange";


    // 普通交换机名称
    public static final String ORDER_EXCHANGE = "orderExchange";

    // 死信交换机名称
    public static final String DEAD_EXCHANGE = "deadExchange";

    public static final String ORDER_DEAD_ROUTING_KEY = "order";
    public static final String APPLET_DEAD_ROUTING_KEY = "applet";

    public static final String EXPIRE_DEAD_ROUTING_KEY = "expire";

    // 普通队列名称
    public static final String ORDER_QUEUE = "order.auto.cancel.queue";

    // 延迟队列名称
    public static final String DELAY_QUEUE = "order.auto.cancel.delay.queue";

    // BOSS 端购物订单取消队列

    // 普通交换机名称
    public static final String COUNTDOWN_ORDER_EXCHANGE = "countdownOrderExchange";

    // 死信交换机名称
    public static final String COUNTDOWN_DEAD_EXCHANGE = "countdownDeadExchange";

    public static final String COUNTDOWN_ORDER_DEAD_ROUTING_KEY = "countdownOrder";

    public static final String COUNTDOWN_EXPIRE_DEAD_ROUTING_KEY = "countdownExpire";

    // 普通队列
    public static final String COUNTDOWN_ORDER_QUEUE = "countdown.order.auto.cancel.queue";

    // 延迟队列名称
    public static final String COUNTDOWN_DELAY_QUEUE = "countdown.order.auto.cancel.delay.queue";

    // 普通交换机名称
    public static final String PRINT_KITCHEN_EXCHANGE = "printKitchenExchange";

    // 死信交换机名称
    public static final String PRINT_KITCHEN_DEAD_EXCHANGE = "printKitchenDeadExchange";

    public static final String PRINT_KITCHEN_DEAD_ROUTING_KEY = "printKitchen";

    public static final String PRINT_KITCHEN_EXPIRE_DEAD_ROUTING_KEY = "printExpire";

    // 普通队列
    public static final String PRINT_KITCHEN_QUEUE = "print.kitchen.queue";

    // 延迟队列名称
    public static final String PRINT_KITCHEN_DELAY_QUEUE = "print.kitchen.delay.queue";


    // 普通交换机名称
    public static final String BASE_AD_REFRESH_EXCHANGE = "baseAdRefreshExchange";

    // 死信交换机名称
    public static final String BASE_AD_REFRESH_DEAD_EXCHANGE = "baseAdRefreshDeadExchange";

    public static final String BASE_AD_REFRESH_DEAD_ROUTING_KEY = "baseAdRefresh";

    public static final String BASE_AD_REFRESH_EXPIRE_DEAD_ROUTING_KEY = "baseAdExpire";

    // 普通队列
    public static final String BASE_AD_REFRESH_QUEUE = "base.ad.refresh.queue";

    // 延迟队列名称
    public static final String BASE_AD_REFRESH_DELAY_QUEUE = "base.ad.refresh.delay.queue";


    /**
     * 日志通知
     */
    public static final String BIZ_LOG_NOTICE = "topic.order.biz.log.notice";

    /**
     * 定时任务
     */
    public static final String ISSUE_COUPON = "topic.issue.coupon";

    /**
     * 定时任务
     */
    public static final String XXL_JOB = "topic.xxl.job";

    /**
     * 微信模板通知
     */
    public static final String WX_TMPL_NOTICE = "topic.wx.tmpl.notice";

    /**
     * 下载文件
     */
    public static final String EXPORT_RECORD = "topic.export.record";

    /**
     * 异步发送mqtt 刷新助教
     */
    public static final String REFRESH_SERVICE_EMPLOYEE = "topic.refresh.service.employee";

    /**
     * 钉钉告警
     */
    public static final String DING_DING_ALARM = "fanout.ding.ding.alarm";

    /**
     * 业绩提成
     */
    public static final String PERFORMANCE_COMMISSION = "topic.performance.commission";


    /**
     * 取消配送单
     */
    public static final String CANCEL_POS_CASH_DISTRIBUTE = "topic.cancel.distribute";

    /**
     * 后厨打印
     */
//    public static final String KITCHEN_PRINT = "topic.kitchen.print";

    /**
     * 打印机
     */
    public static final String XP_PRINT = "topic.xp.print";

    /**
     * 会员短信信息
     */
    public static final String MEMBER_MESSAGE = "topic.member.message";

    /**
     * 修改密码验证码
     */
    public static final String UPDATE_PASSWORD_MESSAGE = "topic.update.password.message";

    /**
     * 微信公众号通知
     */
    public static final String WX_MP_TMPL_NOTICE = "topic.wx.mp.tmpl.notice";

    /**
     * 会员变动记录
     */
    public static final String MEMBER_BALANCE_CHANGE = "topic.member.balance.change";

    /**
     * 微信模板通知
     */
    public static final String WX_BOSS_AUTH_TMPL_NOTICE = "topic.wx.boss.auth.tmpl.notice";

    /**
     * 微信模板通知-小程序
     */
    public static final String WX_CLIENT_AUTH_TMPL_NOTICE = "topic.wx.client.auth.tmpl.notice";
    /**
     * 商品库存预警
     */
    public static final String INVENTORY_ALERT = "topic.pos.product.inventory.alert";
    /**
     * 业务缓存
     */
    public static final String BIZ_CACHE = "topic.biz.cache";

    /**
     * 业务缓存
     */
    public static final String BASE_AD = "base.ad";
    /**
     * 发放会员优惠券
     */
    public static final String GRANT_MEMBER_COUPON = "topic.grant.member.coupon";

    /**
     * 发券活动优惠券发放
     */
    public static final String GRANT_ACTIVITY_MEMBER_COUPON = "topic.grant.activity.member.coupon";

    /**
     * 积分计算消息队列
     */
    public static final String SCORE_RECORDS_COMPUTE = "topic.score.records.compute";
    /**
     * 订单利润
     */
    public static final String POS_CASH_PROFIT = "topic.pos.cash.profit";
    /**
     * 会员消费信息
     */
    public static final String MEMBER_CONSUME = "topic.member.consume";

    /**
     * 发放权益卡
     */
    public static final String GRANT_CARD = "topic.grant.card";
    /**
     * 发放会员权益卡
     */
    public static final String GRANT_MEMBER_CARD = "topic.grant.member.card";
    /**
     * 扫码日志
     */
    public static final String SCAN_LOG = "topic.scan.log";

    /**
     * 套餐分摊
     */
    public static final String THAIL_ASSESSED = "topic.order.thail.assessed";

    /**
     * 订单计入收入
     */
    public static final String CASH_ACCOUNTING_INFO = "topic.pos.cash.accounting";

    /**
     * 订单计入收入
     */
    public static final String CASH_REFUND_PRINT = "topic.pos.cash.refund.print";

    /**
     * 单品优惠补丁
     */
    public static final String PATCH_ITEM_DISCOUNT = "topic.patch.item.discount";

    /**
     * 员工门店记录
     */
    public static final String TENANT_ORG_USER = "topic.tenant.org.user";

    /**
     * 员工门店信息
     */
    public static final String TENANT_ORG = "topic.tenant.org.info";

    /**
     * 年终总结
     */
    public static final String YEAR_END = "topic.tenant.org.year.end";

    /**
     * 临时订单正常开台
     */
    public static final String TEMP_ORDER_OPEN_TABLE = "topic.temp.order.open.table";
    /**
     * 强制退出登录
     */
    public static final String FORCE_LOGOUT = "topic.force.logout";

    /**
     * 检查扫码订单支付
     */
    public static final String CHECK_SCAN_ORDER_PAY = "topic.check.scan.order.pay";
    /**
     * 自定义死信交换机
     */
    public static final String CUSTOM_DELAYED_EXCHANGE = "custom_delayed_exchange";
    /**
     * 租户订阅
     */
    public static final String TENANT_SUBSCRIPTION = "topic.tenant.subscription";
    /**
     * 订单收退
     */
    public static final String CASH_MIRROR = "topic.pos.cash.mirror";
}
