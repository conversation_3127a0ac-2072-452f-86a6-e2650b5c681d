package top.kx.kxss.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.util.BiFunction;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2025/5/21 18:44
 */
public class ExpressionEvaluator {

    // 注册支持的方法（类名.方法名 -> 实现逻辑）
    private static final Map<String, BiFunction<JSONObject, String, Boolean>> METHOD_REGISTRY = new HashMap<>();

    static {
        registerStringMethods();
        registerObjectMethods();
        registerCollMethods();
    }

    // 正则表达式匹配模式
    private static final Pattern EXPR_PATTERN =
            Pattern.compile("(\\w+)\\.(\\w+)\\(#\\{(.*?)\\}\\)");

    // 嵌套键分隔符
    private static final String KEY_SEPARATOR = "\\.";

    private static void registerStringMethods() {
        METHOD_REGISTRY.put("StrUtil.isEmpty", (params, key) ->
                StringUtils.isEmpty(getNestedValue(params, key, String.class)));

        METHOD_REGISTRY.put("StrUtil.isBlank", (params, key) ->
                StringUtils.isBlank(getNestedValue(params, key, String.class)));

        METHOD_REGISTRY.put("StrUtil.isNotBlank", (params, key) ->
                StringUtils.isNotBlank(getNestedValue(params, key, String.class)));

        METHOD_REGISTRY.put("StrUtil.equals", (params, key) -> {
            String[] keys = key.split("\\s*,\\s*");
            String val1 = getNestedValue(params, keys[0], String.class);
            String val2 = keys.length > 1 ? getNestedValue(params, keys[1], String.class) : "";
            return StringUtils.equals(val1, val2);
        });
    }

    private static void registerCollMethods() {
        METHOD_REGISTRY.put("CollUtil.isEmpty", (params, key) ->
                CollUtil.isEmpty(getNestedValue(params, key, List.class)));

        METHOD_REGISTRY.put("CollUtil.isNotEmpty", (params, key) ->
                CollUtil.isNotEmpty(getNestedValue(params, key, List.class)));

    }

    private static void registerObjectMethods() {
        METHOD_REGISTRY.put("ObjectUtil.isNotEmpty", (params, key) ->
                ObjectUtil.isNotEmpty(getNestedValue(params, key, List.class)));

        METHOD_REGISTRY.put("ObjectUtil.isNull", (params, key) ->
                getNestedValue(params, key, Object.class) == null);

        METHOD_REGISTRY.put("ObjectUtil.isNotNull", (params, key) ->
                getNestedValue(params, key, Object.class) != null);

        METHOD_REGISTRY.put("ObjectUtil.equals", (params, key) -> {
            String[] keys = key.split("\\s*,\\s*");
            String val1 = getNestedValue(params, keys[0], String.class);
            String val2 = keys.length > 1 ? getNestedValue(params, keys[1], String.class) : "";
            return ObjectUtil.equals(val1, val2);
        });

//        METHOD_REGISTRY.put("ObjectUtil.isInstanceOf", (params, key) -> {
//            String[] parts = key.split("\\s*,\\s*");
//            Object value = getNestedValue(params, parts[0], Object.class);
//            try {
//                Class<?> clazz = Class.forName(parts[1]);
//                return clazz.isInstance(value);
//            } catch (Exception e) {
//                return false;
//            }
//        });
    }

    /**
     * 获取嵌套键值（支持data.id格式）
     */
    private static <T> T getNestedValue(JSONObject params, String key, Class<T> clazz) {
        String[] keys = key.split(KEY_SEPARATOR);
        Object current = params;

        try {
            for (String k : keys) {
                if (current instanceof JSONObject) {
                    current = ((JSONObject) current).get(k);
                } else {
                    return null;
                }
            }
            return clazz.cast(current);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 执行动态表达式
     */
    public static boolean evaluate(String expression, JSONObject params) {
        Matcher matcher = EXPR_PATTERN.matcher(expression);
        if (matcher.find()) {
            String className = matcher.group(1);
            String methodName = matcher.group(2);
            String paramKey = matcher.group(3);

            String methodKey = className + "." + methodName;
            BiFunction<JSONObject, String, Boolean> method = METHOD_REGISTRY.get(methodKey);

            if (method != null) {
                return method.apply(params, paramKey);
            }
            throw new UnsupportedOperationException("Unsupported method: " + methodKey);
        }
        throw new IllegalArgumentException("Invalid expression format");
    }

    /**
     * 注册自定义方法
     */
    public static void registerMethod(
            String className,
            String methodName,
            BiFunction<JSONObject, String, Boolean> logic) {
        METHOD_REGISTRY.put(className + "." + methodName, logic);
    }

    public static void main(String[] args) {
        JSONObject params = JSON.parseObject("{"
                + "\"data\":{\"id\":\"123\"},"
                + "\"user\":{\"name\":\"Alice\",\"age\":25},"
                + "\"list\":[1,2,3]"
                + "}");

        // 测试用例
        testCase("ObjectUtil.isNotNull(#{data.id})", params);
        testCase("ObjectUtil.isNull(#{data.name})", params);
        testCase("StrUtil.isBlank(#{user.age})", params);
        testCase("StrUtil.equals(#{user.age},#{user.age})", params);
        testCase("ObjectUtil.isNotEmpty(#{list})", params);
    }

    private static void testCase(String expr, JSONObject params) {
        try {
            System.out.printf("表达式: %-40s => %s%n",
                    expr, evaluate(expr, params));
        } catch (Exception e) {
            System.out.println("执行出错: " + e.getMessage());
        }
    }
}
