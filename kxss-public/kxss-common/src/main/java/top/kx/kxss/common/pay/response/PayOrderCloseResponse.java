package top.kx.kxss.common.pay.response;


import top.kx.kxss.common.pay.model.PayOrderCloseResModel;

/**
 * 关闭订单响应实现
 *
 * <AUTHOR>
 */
public class PayOrderCloseResponse extends PayResponse {

    private static final long serialVersionUID = 7654172640802954221L;

    public PayOrderCloseResModel get() {
        if (getData() == null) {
            return new PayOrderCloseResModel();
        }
        return getData().toJavaObject(PayOrderCloseResModel.class);
    }

}

