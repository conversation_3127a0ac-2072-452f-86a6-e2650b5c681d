package top.kx.kxss.common.utils;

/**
 * <AUTHOR>
 * @date 2025/5/21 18:00
 */

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.function.Predicate;

public class JsonFlattener {

    private static final ObjectMapper mapper = new ObjectMapper();
    private static final String DEFAULT_ARRAY_PATTERN = "[%d]";

    /**
     * 带过滤条件的扁平化方法
     */
    public static String flattenJson(String nestedJson,
                                     Predicate<String> fieldFilter) throws Exception {
        JsonNode jsonNode = mapper.readTree(nestedJson);
        ObjectNode flattenedNode = flattenJsonNode(jsonNode, fieldFilter);
        return mapper.writeValueAsString(flattenedNode);
    }

    /**
     * 原始方法（保持向后兼容）
     */
    public static String flattenJson(String nestedJson) throws Exception {
        // 默认不过滤
        return flattenJson(nestedJson, path -> true);
    }

    private static ObjectNode flattenJsonNode(JsonNode node,
                                              Predicate<String> fieldFilter) {
        ObjectNode result = mapper.createObjectNode();
        flatten("", node, result, fieldFilter);
        return result;
    }

    private static void flatten(String currentPath,
                                JsonNode node,
                                ObjectNode result,
                                Predicate<String> fieldFilter) {
        // 应用字段过滤
        if (!fieldFilter.test(currentPath)) return;

        if (node.isObject()) {
            handleObject(currentPath, node, result, fieldFilter);
        } else if (node.isArray()) {
            handleArray(currentPath, node, result, fieldFilter);
        } else {
            addValueNode(currentPath, node, result);
        }
    }

    private static void handleObject(String currentPath,
                                     JsonNode node,
                                     ObjectNode result,
                                     Predicate<String> fieldFilter) {
        node.fields().forEachRemaining(entry -> {
            String newPath = buildNewPath(currentPath, entry.getKey());
            flatten(newPath, entry.getValue(), result, fieldFilter);
        });
    }

    private static void handleArray(String currentPath,
                                    JsonNode arrayNode,
                                    ObjectNode result,
                                    Predicate<String> fieldFilter) {
        int index = 0;
        for (JsonNode element : arrayNode) {
            String indexedPath = currentPath + String.format(DEFAULT_ARRAY_PATTERN, index++);
            flatten(indexedPath, element, result, fieldFilter);
        }
    }

    private static String buildNewPath(String currentPath, String key) {
        return currentPath.isEmpty() ? key : currentPath + "." + key;
    }

    private static void addValueNode(String path, JsonNode node, ObjectNode result) {
        if (node.isTextual()) {
            result.put(path, node.asText());
        } else if (node.isNumber()) {
            result.put(path, String.valueOf(node.numberValue()));
        } else if (node.isBoolean()) {
            result.put(path, node.asBoolean());
        } else {
            result.putNull(path);
        }
    }

    public static void main(String[] args) throws Exception {
        System.out.println(JsonFlattener.flattenJson("{\n" +
                "  \"memberId\": \"\",\n" +
                "  \"mode\": \"POS\",\n" +
                "  \"orderSource\": \"1\",\n" +
                "  \"tableId\": \"604638787227120486\",\n" +
                "  \"thailId\": \"406133473309360137\",\n" +
                "  \"thailList\": [\n" +
                "    {\n" +
                "      \"bizId\": \"609950055455241357\",\n" +
                "      \"num\": 1,\n" +
                "      \"type\": \"0\",\n" +
                "      \"thailDetailId\": \"609950055455241354\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"timingDuration\": \"\",\n" +
                "  \"securitiesNumber\": \"\",\n" +
                "  \"payBizType\": \"\",\n" +
                "  \"memberVerifyType\": \"\",\n" +
                "  \"memberVerifyValue\": \"\"\n" +
                "}"));
    }
}
