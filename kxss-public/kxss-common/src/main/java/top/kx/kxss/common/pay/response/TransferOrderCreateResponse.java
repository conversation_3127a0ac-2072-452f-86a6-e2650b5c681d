package top.kx.kxss.common.pay.response;


import top.kx.kxss.common.pay.model.TransferOrderCreateResModel;

/***
 * 转账响应实现
 *
 * <AUTHOR>
 */
public class TransferOrderCreateResponse extends PayResponse {

    private static final long serialVersionUID = 7419683269497002904L;

    public TransferOrderCreateResModel get() {
        if (getData() == null) {
            return new TransferOrderCreateResModel();
        }
        return getData().toJavaObject(TransferOrderCreateResModel.class);
    }

    @Override
    public boolean isSuccess(String apiKey) {
        if (super.isSuccess(apiKey)) {
            int state = get().getState();
            return state == 0 || state == 1 || state == 2;
        }
        return false;
    }

}
