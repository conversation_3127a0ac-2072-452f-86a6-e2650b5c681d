package top.kx.kxss.common.cache.base.sse;


import top.kx.basic.model.cache.CacheHashKey;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.kxss.common.cache.CacheKeyModular;

import java.io.Serializable;

/**
 * 参数 KEY
 * <p>
 * key: dict:{dict_key}
 * field1: {item_key1} --> item_name
 * field2: {item_key2} --> item_name
 *
 * <p>
 * #c_dictionary_item
 *
 * <AUTHOR>
 * @date 2020/9/20 6:45 下午
 */
public class SseEmitterCacheKeyBuilder implements CacheKeyBuilder {
    public static CacheHashKey builder(Serializable dictKey) {
        return new SseEmitterCacheKeyBuilder().hashKey(dictKey);
    }

    public static CacheHashKey builder(String field) {
        return new SseEmitterCacheKeyBuilder().hashFieldKey(field, field);
    }

    @Override
    public String getPrefix() {
        return CacheKeyModular.PREFIX;
    }

    @Override
    public String getTable() {
        return "sse:emitter";
    }

    @Override
    public String getModular() {
        return null;
    }

    @Override
    public String getField() {
        return "key";
    }

    @Override
    public ValueType getValueType() {
        return ValueType.string;
    }
}
