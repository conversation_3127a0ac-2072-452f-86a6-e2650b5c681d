package top.kx.kxss.common.utils;

/**
 * <AUTHOR>
 * @date 2025/6/26 17:55
 */

import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SmsTemplateGenerator {

    // JSON解析和模板处理工具
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 生成短信内容
     *
     * @param template   短信模板
     * @param jsonParams JSON格式的参数数组
     * @return 替换后的短信内容
     */
    public static String generateSms(String template, String jsonParams) {
        try {
            // 1. 解析JSON参数
            List<Map<String, String>> paramList = objectMapper.readValue(
                    jsonParams,
                    objectMapper.getTypeFactory().constructCollectionType(
                            List.class,
                            Map.class
                    )
            );

            // 2. 转换为Map格式
            Map<String, String> params = new HashMap<>();
            for (Map<String, String> item : paramList) {
                params.put(item.get("key"), item.get("value"));
            }

            // 3. 执行模板替换
            return replaceTemplate(template, params);

        } catch (Exception e) {
            throw new RuntimeException("短信生成失败: " + e.getMessage(), e);
        }
    }

    /**
     * 模板替换核心方法
     */
    private static String replaceTemplate(String template, Map<String, String> params) {
        Pattern pattern = Pattern.compile("\\$\\{(\\w+)\\}");
        Matcher matcher = pattern.matcher(template);

        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String key = matcher.group(1);
            String value = params.getOrDefault(key, "");
            matcher.appendReplacement(result, Matcher.quoteReplacement(value));
        }
        matcher.appendTail(result);

        return result.toString();
    }
}
