package top.kx.kxss.common.pay.model;


import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 支付查单响应实体类
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PayOrderQueryResModel extends PayObject {

    /**
     * 支付订单号
     */
    private String payOrderId;

    /**
     * 商户号
     */
    private String mchNo;

    /**
     * 商户订单号
     */
    private String mchOrderNo;

    /**
     * 支付接口
     */
    private String ifCode;

    /**
     * 支付方式
     */
    private String wayCode;

    /**
     * 支付金额,单位分
     */
    private Long amount;

    /**
     * 三位货币代码,人民币:cny
     */
    private String currency;

    /**
     * 支付订单状态
     * 0-订单生成
     * 1-支付中
     * 2-支付成功
     * 3-支付失败
     * 4-已撤销
     * 5-已退款
     * 6-订单关闭
     */
    private int state;

    /**
     * 客户端IPV4地址
     */
    private String clientIp;

    /**
     * 商品标题
     */
    private String subject;

    /**
     * 商品描述
     */
    private String body;

    /**
     * 渠道订单号
     */
    private String channelOrderNo;

    /**
     * 渠道错误码
     */
    private String errCode;

    /**
     * 渠道错误描述
     */
    private String errMsg;

    /**
     * 扩展参数
     */
    private String extParam;

    /**
     * 订单创建时间,13位时间戳
     */
    private LocalDateTime createdTime;

    /**
     * 订单支付成功时间,13位时间戳
     */
    private LocalDateTime successTime;
}
