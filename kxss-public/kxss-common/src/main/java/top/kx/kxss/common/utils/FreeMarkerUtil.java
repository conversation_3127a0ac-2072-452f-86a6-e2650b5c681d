package top.kx.kxss.common.utils;

import cn.hutool.crypto.digest.DigestUtil;
import freemarker.cache.MruCacheStorage;
import freemarker.cache.MultiTemplateLoader;
import freemarker.cache.StringTemplateLoader;
import freemarker.cache.TemplateLoader;
import freemarker.ext.beans.BeansWrapper;
import freemarker.template.Configuration;
import freemarker.template.Template;
import freemarker.template.TemplateHashModel;
import freemarker.template.TemplateModelException;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import top.kx.basic.utils.StrPool;

import java.io.StringWriter;
import java.util.Map;

/**
 * 模板引擎工具类
 *
 * <AUTHOR>
 * @version v1.0
 * @date 2022/7/25 12:24 PM
 * @create [2022/7/25 12:24 PM ] [tangyh] [初始创建]
 */
@Slf4j
public class FreeMarkerUtil {
    private final static Configuration freeMarkerCfg;
    private final static StringTemplateLoader sl;

    static {
        freeMarkerCfg = new Configuration(Configuration.DEFAULT_INCOMPATIBLE_IMPROVEMENTS);
        sl = new StringTemplateLoader();
        freeMarkerCfg.setBooleanFormat("c");
        freeMarkerCfg.setNumberFormat("0.##");
        generateSharedVariable();
        freeMarkerCfg.setCacheStorage(new MruCacheStorage(2000, Integer.MAX_VALUE));
        freeMarkerCfg.setTemplateUpdateDelayMilliseconds(6000000L);
        TemplateLoader[] loaders = new TemplateLoader[]{sl};
        MultiTemplateLoader mt = new MultiTemplateLoader(loaders);
        freeMarkerCfg.setTemplateLoader(mt);
    }

    private static void generateSharedVariable() {
        try {
            BeansWrapper wrapper = new BeansWrapper(Configuration.VERSION_2_3_30);
            TemplateHashModel staticModels = wrapper.getStaticModels();
            TemplateHashModel strPool = (TemplateHashModel) staticModels.get("top.kx.basic.utils.StrPool");
            freeMarkerCfg.setSharedVariable("StrPool", strPool);
            TemplateHashModel dateUtils = (TemplateHashModel) staticModels.get("top.kx.basic.utils.DateUtils");
            freeMarkerCfg.setSharedVariable("DateUtils", dateUtils);
            TemplateHashModel argumentAssert = (TemplateHashModel) staticModels.get("top.kx.basic.utils.ArgumentAssert");
            freeMarkerCfg.setSharedVariable("ArgumentAssert", argumentAssert);
            TemplateHashModel beanPlusUtil = (TemplateHashModel) staticModels.get("top.kx.basic.utils.BeanPlusUtil");
            freeMarkerCfg.setSharedVariable("BeanPlusUtil", beanPlusUtil);
            TemplateHashModel collHelper = (TemplateHashModel) staticModels.get("top.kx.basic.utils.CollHelper");
            freeMarkerCfg.setSharedVariable("CollHelper", collHelper);
            TemplateHashModel springUtils = (TemplateHashModel) staticModels.get("top.kx.basic.utils.SpringUtils");
            freeMarkerCfg.setSharedVariable("SpringUtils", springUtils);
            TemplateHashModel strHelper = (TemplateHashModel) staticModels.get("top.kx.basic.utils.StrHelper");
            freeMarkerCfg.setSharedVariable("StrHelper", strHelper);
            TemplateHashModel treeUtil = (TemplateHashModel) staticModels.get("top.kx.basic.utils.TreeUtil");
            freeMarkerCfg.setSharedVariable("TreeUtil", treeUtil);
        } catch (TemplateModelException e) {
            log.error(e.getMessage(), e);
        }
    }

    @SneakyThrows
    public static String generateString(String strTemplate, Map<String, Object> parameters) {
        String templateName = DigestUtil.md5Hex(strTemplate);
        if (sl.findTemplateSource(templateName) == null) {
            sl.putTemplate(templateName, strTemplate);
        }

        StringWriter writer = new StringWriter();
        Template template = freeMarkerCfg.getTemplate(templateName, StrPool.UTF8);
        template.process(parameters, writer);
        return writer.toString();
    }
}
