package top.kx.kxss.common.properties;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import top.kx.basic.constant.Constants;

/**
 * 登录配置
 *
 * <AUTHOR>
 * @date 2021/1/28 7:57 下午
 */
@Setter
@Getter
@RefreshScope
@ConfigurationProperties(prefix = TaskProperties.PREFIX)
public class TaskProperties {
    public static final String PREFIX = Constants.PROJECT_PREFIX + ".task";
    /**
     * 核心线程数
     */
    private Integer corePoolSize = 8;
    /**
     * 最大线程数
     */
    private Integer maxPoolSize = 200;
    /**
     * 缓冲队列200：用来缓冲执行任务的队列
     */
    private Integer queueCapacity = 10;
    /**
     * 线程活路时间 60 秒
     */
    private Integer keepAliveSeconds = 60;
}
