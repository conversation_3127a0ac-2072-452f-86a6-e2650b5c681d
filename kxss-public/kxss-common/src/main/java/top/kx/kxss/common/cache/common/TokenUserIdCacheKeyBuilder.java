package top.kx.kxss.common.cache.common;


import top.kx.basic.model.cache.CacheKey;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.kxss.common.cache.CacheKeyModular;
import top.kx.kxss.common.cache.CacheKeyTable;

import java.time.Duration;

/**
 * 参数 KEY
 * <p>
 * #c_application
 *
 * <AUTHOR>
 * @date 2020/9/20 6:45 下午
 */
public class TokenUserIdCacheKeyBuilder implements CacheKeyBuilder {
    public static CacheKey builder(String uuid) {
        return new TokenUserIdCacheKeyBuilder().key(uuid);
    }

    @Override
    public String getTable() {
        return CacheKeyTable.TOKEN_USER_ID;
    }

    @Override
    public String getPrefix() {
        return CacheKeyModular.PREFIX;
    }

    @Override
    public String getTenant() {
        return null;
    }
    @Override
    public String getOrgId() {
        return null;
    }
    @Override
    public String getModular() {
        return CacheKeyModular.OAUTH;
    }

    @Override
    public String getField() {
        return null;
    }

    @Override
    public ValueType getValueType() {
        return ValueType.string;
    }

    @Override
    public Duration getExpire() {
        return Duration.ofHours(8L);
    }
}
