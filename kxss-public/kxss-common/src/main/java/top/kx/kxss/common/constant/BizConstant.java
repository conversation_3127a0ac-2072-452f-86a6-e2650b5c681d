package top.kx.kxss.common.constant;

/**
 * 业务常量
 *
 * <AUTHOR>
 * @date 2019/08/06
 */
public interface BizConstant {
    /**
     * 工具类 需要扫描的包
     */
    String UTIL_PACKAGE = "top.kx.basic";
    /**
     * 业务项目 需要扫描的包
     */
    String BUSINESS_PACKAGE = "top.kx.kxss";
    /**
     * 被T
     */
    String LOGIN_STATUS = "T";

    String BASE = "kxss-base-server";

    String APP = "kxss-app-server";
    String FILE = "kxss-file-server";
    String MSG = "kxss-msg-server";
    String OAUTH = "kxss-oauth-server";
    String GATE = "kxss-gateway-server";
    String TENANT = "kxss-system-server";
    String BASE_EXECUTOR = "kxss-base-executor";
    String EXTEND_EXECUTOR = "kxss-extend-executor";
    String ORDER = "kxss-example-server";
    String IOT = "kxss-iot-server";

    Long DEFAULT_ID = 1L;

    Long DEFAULT_GRADE = 0L;

    /**
     * 初始化数据源时json的参数，
     * method 的可选值为 {INIT_DS_PARAM_METHOD_INIT} 和 {INIT_DS_PARAM_METHOD_REMOVE}
     */
    String INIT_DS_PARAM_METHOD = "method";
    /**
     * 初始化数据源时json的参数，
     * tenant 的值为 需要初始化的租户编码
     */
    String INIT_DS_PARAM_TENANT = "tenant";
    /**
     * 初始化数据源时，需要执行的方法
     * init 表示初始化数据源
     * remove 表示删除数据源
     */
    String INIT_DS_PARAM_METHOD_INIT = "init";
    /**
     * 初始化数据源时，需要执行的方法
     * init 表示初始化数据源
     * remove 表示删除数据源
     */
    String INIT_DS_PARAM_METHOD_REMOVE = "remove";
    /**
     * 框架布局
     */
    String IFRAME = "IFRAME";
    /**
     * 页面布局
     */
    String LAYOUT = "LAYOUT";
    /**
     * 绑定范围类型 机构
     */
    String SCOPE_TYPE_ORG = "2";
    /**
     * 绑定范围类型 员工
     */
    String SCOPE_TYPE_EMPLOYEE = "1";
    /**
     * 绑定范围 已绑定
     */
    String SCOPE_BIND = "1";
    /**
     * 绑定范围 未绑定
     */
    String SCOPE_UN_BIND = "2";

    /**
     * 坐标KEY
     */
    String GEO_KEY = "GEO_KEY";

    /**
     * 收银参数密码 key
     */
    String PARAM_PASSWORD_SALT = "xkdlfzrvqtn4as7whnor";

    interface FLOW {
        /**
         * 默认计算价格
         */
        String DEFAULT_PRICE_CHAIN = "defaultPriceChain";
        /**
         * 绑定优惠券
         */
        String BIND_COUPON_CHAIN = "bindCouponChain";
        /**
         * 移除优惠券
         */
        String REMOVE_COUPON_CHAIN = "removeCouponChain";
        /**
         * 订单完成流程
         */
        String ORDER_COMPLETE = "orderCompleteChain";

        /**
         * 取消订单流程
         */
        String CANCEL_ORDER = "cancelOrderChain";

        /**
         * 挂单流程
         */
        String REGISTRATION = "registrationChain";


        /**
         * 挂单开台流程
         */
        String REGISTRATION_OPEN = "registrationOpenChain";

        /**
         * 动态支付流程
         */
        String TRENDS_PAYMENT = "trendsPaymentChain";

        /**
         * 撤销支付流程
         */
        String PAYMENT_REVOKE = "paymentRevokeChain";

        /**
         * 订单详情流程
         */
        String ORDER_DETAIL_CHAIN = "orderDetailChain";
        /**
         * 开台流程
         */
        String OPEN_TABLE_CHAIN = "openTableChain";
        /**
         * 添加套餐
         */
        String ADD_THAIL_CHAIN = "addThailChain";
        /**
         * 计算支付金额
         */
        String CALC_PAY_AMOUNT = "calcPayAmount";
        /**
         * 取消整单分摊金额
         */
        String CANCEL_ASSESSED_CHAIN = "cancelAssessedChain";
        /**
         * 反结账
         */
        String COUNTER_CHECKOUT_CHAIN = "counterCheckoutChain";
        /**
         * 整单退款
         */
        String FULL_REFUND_CHAIN = "fullRefundChain";
        /**
         * 部分退款
         */
        String PART_REFUND_CHAIN = "partRefundChain";
        /**
         * 部分退款(新)
         */
        String PART_REFUND_NEW_CHAIN = "partRefundNChain";
        /**
         * 首页价格
         */
        String HOME_PRICE_CHAIN = "homePriceChain";
        /**
         * 首页价格
         */
        String HOME_REFRESH_PRICE_CHAIN = "homeRefreshPriceChain";
        /**
         * 停止计时
         */
        String STOP_TIME_CHAIN = "stopTimeChain";
        /**
         * 计算权益卡价格
         */
        String CALC_CARD_PRICE_CHAIN = "calcCardPriceChain";

        /**
         * 计算权益卡价格
         */
        String CALC_CARD_DETAIL_PRICE_CHAIN = "calcCardDetailPriceChain";
        /**
         * 绑定卡
         */
        String BIND_CARD_CHAIN = "bindCardChain";
        /**
         * 移除权益卡
         */
        String REMOVE_CARD_CHAIN = "removeCardChain";
        /**
         * 计算台桌价格
         */
        String CALC_TABLE_PRICE = "calcTablePriceChain";
        /**
         * 预付退
         */
        String PREPAID_REFUND_CHAIN = "prepaidRefundChain";
        /**
         * 可退款明细
         */
        String PART_REFUND_ITEM_DETAIL_CHAIN = "partRefundItemDetailChain";

        /**
         * 团购续费订单完成流程
         */
        String DEAL_ORDER_COMPLETE = "delOrderCompleteChain";

        /**
         * 计算抵用券价格
         */
        String CALC_VOUCHER_COUPON_PRICE_CHAIN = "calcVoucherCouponPriceChain";

        /**
         * 计算抵用券详情价格
         */
        String CALC_VOUCHER_COUPON_DETAIL_PRICE_CHAIN = "calcVoucherCouponDetailPriceChain";

        /**
         * 临时订单
         */
        String TEMP_ORDER = "tempOrderChain";

        /**
         * 计算快捷优惠
         */
        String CALC_FAST_DISCOUNT_CHAIN = "calcFastDiscountChain";

    }

}
