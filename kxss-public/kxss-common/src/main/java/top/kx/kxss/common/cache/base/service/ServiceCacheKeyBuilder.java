package top.kx.kxss.common.cache.base.service;

import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.model.cache.CacheKey;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.kxss.common.cache.CacheKeyModular;
import top.kx.kxss.common.cache.CacheKeyTable;

import java.time.Duration;

/**
 * <AUTHOR>
 * @Date 2023/8/28 11:53
 * @description TODO
 */
public class ServiceCacheKeyBuilder  implements CacheKeyBuilder {
    public static CacheKey build(Long employeeId) {
        return new ServiceCacheKeyBuilder().key(employeeId);
    }

    @Override
    public String getTable() {
        return CacheKeyTable.Base.BASE_SERVICE;
    }

    @Override
    public String getPrefix() {
        return CacheKeyModular.PREFIX;
    }

    @Override
    public String getModular() {
        return CacheKeyModular.BASE;
    }

    @Override
    public String getField() {
        return SuperEntity.ID_FIELD;
    }

    @Override
    public ValueType getValueType() {
        return ValueType.obj;
    }

    @Override
    public Duration getExpire() {
        return Duration.ofHours(24);
    }
}
