package top.kx.kxss.common.utils;

import java.security.SecureRandom;
import java.util.EnumSet;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 */
public class AdvancedPrefixSuffixGenerator {

    // 定义字符集枚举
    public enum CharSet {
        DIGITS("0123456789"),
        UPPER_CASE("ABCDEFGHIJKLMNOPQRSTUVWXYZ"),
        LOWER_CASE("abcdefghijklmnopqrstuvwxyz"),
        ALPHANUMERIC(DIGITS.chars + UPPER_CASE.chars + LOWER_CASE.chars);

        final String chars;

        CharSet(String chars) {
            this.chars = chars;
        }
    }

    // 使用SecureRandom增强安全性
    private static final SecureRandom SECURE_RANDOM = new SecureRandom();

    // 生成随机字符串的函数式接口
    private static final Supplier<String> RANDOM_STRING_GENERATOR = () ->
            IntStream.range(0, 5)
                    .mapToObj(i -> String.valueOf(CharSet.ALPHANUMERIC.chars.charAt(
                            SECURE_RANDOM.nextInt(CharSet.ALPHANUMERIC.chars.length()))))
                    .collect(Collectors.joining());

    // 生成随机数字的函数式接口
    private static final Supplier<String> RANDOM_NUMBER_GENERATOR = () ->
            String.format("%05d", SECURE_RANDOM.nextInt(100000));

    // 主生成方法
    public static String generateFormattedString() {
        return String.join("_", RANDOM_STRING_GENERATOR.get(), RANDOM_NUMBER_GENERATOR.get());
    }

    // 可配置的生成方法
    public static String generateFormattedString(EnumSet<CharSet> prefixCharSets, int prefixLength, int suffixLength) {
        String combinedChars = prefixCharSets.stream()
                .map(cs -> cs.chars)
                .collect(Collectors.joining());

        String prefix = IntStream.range(0, prefixLength)
                .mapToObj(i -> String.valueOf(combinedChars.charAt(
                        SECURE_RANDOM.nextInt(combinedChars.length()))))
                .collect(Collectors.joining());

        String suffix = String.format("%0" + suffixLength + "d",
                SECURE_RANDOM.nextInt((int) Math.pow(10, suffixLength)));

        return String.join("_", prefix, suffix);
    }

    public static void main(String[] args) {
        // 基础用法
        System.out.println("Basic generation:");
        IntStream.range(0, 5)
                .mapToObj(i -> generateFormattedString())
                .forEach(System.out::println);

        // 高级用法 - 自定义配置
        System.out.println("\nCustom generation (UPPER_CASE only, 5 chars prefix, 8 digits suffix):");
        IntStream.range(0, 5)
                .mapToObj(i -> generateFormattedString(
                        EnumSet.of(CharSet.UPPER_CASE), 5, 8))
                .forEach(System.out::println);
    }
}
