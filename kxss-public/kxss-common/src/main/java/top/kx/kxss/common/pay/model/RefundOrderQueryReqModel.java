package top.kx.kxss.common.pay.model;

import lombok.Getter;
import lombok.Setter;
import top.kx.kxss.common.pay.ApiField;

/**
 * 退款查单请求实体类
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class RefundOrderQueryReqModel extends PayObject {

    private static final long serialVersionUID = -5184554341263929245L;

    /**
     * 商户号
     */
    @ApiField("mchNo")
    private String mchNo;
    /**
     * 应用ID
     */
    @ApiField("appId")
    private String appId;
    /**
     * 商户退款单号
     */
    @ApiField("mchRefundNo")
    String mchRefundNo;
    /**
     * 支付系统退款订单号
     */
    @ApiField("refundOrderId")
    String refundOrderId;

}
