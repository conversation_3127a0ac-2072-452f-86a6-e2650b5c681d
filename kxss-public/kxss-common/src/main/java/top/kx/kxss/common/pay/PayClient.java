package top.kx.kxss.common.pay;

import lombok.Getter;
import lombok.Setter;
import top.kx.kxss.common.pay.exception.PayException;
import top.kx.kxss.common.pay.net.APIResource;
import top.kx.kxss.common.pay.net.RequestOptions;
import top.kx.kxss.common.pay.request.PayRequest;
import top.kx.kxss.common.pay.response.PayResponse;

/**
 * <AUTHOR>
 * @date 2024/5/24 17:51
 */
@Setter
@Getter
public class PayClient extends APIResource {

    private String signType = Pay.DEFAULT_SIGN_TYPE;
    private String apiKey = Pay.apiKey;
    private String apiBase = Pay.getApiBase();

    public PayClient(String apiBase, String signType, String apiKey) {
        this.apiBase = apiBase;
        this.signType = signType;
        this.apiKey = apiKey;
    }

    public PayClient(String apiBase, String apiKey) {
        this.apiBase = apiBase;
        this.apiKey = apiKey;
    }

    public PayClient(String apiKey) {
        this.apiKey = apiKey;
    }

    public PayClient() {
    }

    public <T extends PayResponse> T execute(PayRequest<T> request) throws PayException {

        // 支持用户自己设置RequestOptions
        if(request.getRequestOptions() == null) {
            RequestOptions options = RequestOptions.builder()
                    .setVersion(request.getApiVersion())
                    .setUri(request.getApiUri())
                    .setApiKey(this.apiKey)
                    .build();
            request.setRequestOptions(options);
        }
        return execute(request, RequestMethod.POST, this.apiBase);
    }

}
