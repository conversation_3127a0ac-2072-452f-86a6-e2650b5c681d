package top.kx.kxss.common.pay.exception;

/**
 * 异常抽象类
 *
 * <AUTHOR>
 * @date 2024/5/24 17:52
 */
public abstract class PayException extends Exception {

    private static final long serialVersionUID = 2566087783987900120L;

    private int statusCode;

    public PayException(String message) {
        super(message, null);
    }

    public PayException(String message, Throwable e) {
        super(message, e);
    }

    public PayException(String message, int statusCode, Throwable e) {
        super(message, e);
        this.statusCode = statusCode;
    }

    public int getStatusCode() {
        return statusCode;
    }

    @Override
    public String toString() {
        return super.toString();
    }
}
