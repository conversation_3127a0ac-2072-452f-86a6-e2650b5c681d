<?xml version="1.0" encoding="UTF-8"?>
<included>
    <!-- logback/defaults-prod.xml 文件位于kxss-util/kxss-log-starter/src/main/resources -->
    <include resource="logback/defaults-prod.xml"/>

    <!-- 基础服务 -->
    <logger name="top.kx.kxss.base.controller" additivity="true" level="${log.level.controller}">
        <appender-ref ref="ASYNC_CONTROLLER_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.base.service" additivity="true" level="${log.level.service}">
        <appender-ref ref="ASYNC_SERVICE_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.base.mapper" additivity="false" level="${log.level.mapper}">
        <appender-ref ref="ASYNC_MAPPER_APPENDER"/>
    </logger>
    <!-- 文件服务 -->
    <logger name="top.kx.kxss.file.controller" additivity="true" level="${log.level.controller}">
        <appender-ref ref="ASYNC_CONTROLLER_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.file.service" additivity="true" level="${log.level.service}">
        <appender-ref ref="ASYNC_SERVICE_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.file.mapper" additivity="false" level="${log.level.mapper}">
        <appender-ref ref="ASYNC_MAPPER_APPENDER"/>
    </logger>

    <!-- 租户服务 -->
    <logger name="top.kx.kxss.system.controller" additivity="true" level="${log.level.controller}">
        <appender-ref ref="ASYNC_CONTROLLER_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.system.service" additivity="true" level="${log.level.service}">
        <appender-ref ref="ASYNC_SERVICE_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.system.mapper" additivity="false" level="${log.level.mapper}">
        <appender-ref ref="ASYNC_MAPPER_APPENDER"/>
    </logger>

    <!-- 认证服务 -->
    <logger name="top.kx.kxss.oauth.controller" additivity="true" level="${log.level.controller}">
        <appender-ref ref="ASYNC_CONTROLLER_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.oauth.mapper" additivity="false" level="${log.level.mapper}">
        <appender-ref ref="ASYNC_MAPPER_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.oauth" additivity="true" level="${log.level.service}">
        <appender-ref ref="ASYNC_SERVICE_APPENDER"/>
    </logger>

    <!-- 消息服务 -->
    <logger name="top.kx.kxss.msg.controller" additivity="true" level="${log.level.controller}">
        <appender-ref ref="ASYNC_CONTROLLER_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.msg.service" additivity="true" level="${log.level.service}">
        <appender-ref ref="ASYNC_SERVICE_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.msg.mapper" additivity="false" level="${log.level.mapper}">
        <appender-ref ref="ASYNC_MAPPER_APPENDER"/>
    </logger>
</included>
