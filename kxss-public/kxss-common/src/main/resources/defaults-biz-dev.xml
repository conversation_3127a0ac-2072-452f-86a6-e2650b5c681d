<?xml version="1.0" encoding="UTF-8"?>
<included>
    <!-- 本地开发时，在bootstrap-xxx.yml中通过 logging.config=classpath:logback-spring-dev.xml 文件，表示本地的日志实时打印出来 -->
    <!-- logback/defaults-dev.xml 文件位于kxss-util/kxss-log-starter/src/main/resources -->
    <include resource="logback/defaults-dev.xml"/>

    <logger name="top.kx.kxss.tenant.controller" additivity="true" level="${log.level.controller}">
        <appender-ref ref="CONTROLLER_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.system.service" additivity="true" level="${log.level.service}">
        <appender-ref ref="SERVICE_APPENDER"/>
    </logger>

    <logger name="top.kx.kxss.base.controller" additivity="true" level="${log.level.controller}">
        <appender-ref ref="CONTROLLER_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.base.service" additivity="true" level="${log.level.service}">
        <appender-ref ref="SERVICE_APPENDER"/>
    </logger>

    <logger name="top.kx.kxss.oauth.controller" additivity="true" level="${log.level.controller}">
        <appender-ref ref="CONTROLLER_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.oauth.service" additivity="true" level="${log.level.service}">
        <appender-ref ref="SERVICE_APPENDER"/>
    </logger>

    <logger name="top.kx.kxss.file.controller" additivity="true" level="${log.level.controller}">
        <appender-ref ref="CONTROLLER_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.file.service" additivity="true" level="${log.level.service}">
        <appender-ref ref="SERVICE_APPENDER"/>
    </logger>


    <logger name="top.kx.kxss.msg.controller" additivity="true" level="${log.level.controller}">
        <appender-ref ref="CONTROLLER_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.msg.service" additivity="true" level="${log.level.service}">
        <appender-ref ref="SERVICE_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.sms.controller" additivity="true" level="${log.level.controller}">
        <appender-ref ref="CONTROLLER_APPENDER"/>
    </logger>
    <logger name="top.kx.kxss.sms.service" additivity="true" level="${log.level.service}">
        <appender-ref ref="SERVICE_APPENDER"/>
    </logger>
</included>
