package top.kx.kxss.model.constant;

/**
 * Echo注解中dictType的常量
 * <p>
 * 存放系统中常用的类型
 * <p>
 * 本类中的 @lamp.generator auto insert 请勿删除
 *
 * <AUTHOR>
 * @date 2019/07/26
 */
public interface EchoDictType {
    // @lamp.generator auto insert EchoDictType

    /**
     * 全局字典类型
     */
    interface Global {
        // @lamp.generator auto insert Global

        /**
         * 行政级别
         * [10-国家 20-省份/直辖市 30-地市 40-区县 50-乡镇]
         */
        String AREA_LEVEL = "GLOBAL_AREA_LEVEL";
        /**
         * 民族
         * [01-汉族 02-...]
         */
        String NATION = "GLOBAL_NATION";
        /**
         * 学历
         * [01-小学 02-中学 03-高中 04-专科 05-本科 06-硕士 07-博士 08-博士后 99-其他]
         */
        String EDUCATION = "GLOBAL_EDUCATION";
        /**
         * 性别
         */
        String SEX = "GLOBAL_SEX";
        /**
         * 激活状态
         * [10-未激活 20-已激活]
         */
        String ACTIVE_STATUS = "GLOBAL_ACTIVE_STATUS";
        /**
         * 数据类型
         * [10-系统值 20-业务值]
         */
        String DATA_TYPE = "GLOBAL_DATA_TYPE";
    }

    /**
     * 基础服务
     */
    interface Base {
        // @lamp.generator auto insert Base
        String MSG_INTERFACE_LOGGING_STATUS = "MSG_INTERFACE_LOGGING_STATUS";
        String INTERFACE_EXEC_MODE = "INTERFACE_EXEC_MODE";
        String MSG_TEMPLATE_TYPE = "MSG_TEMPLATE_TYPE";
        String NOTICE_TARGET = "NOTICE_TARGET";
        String NOTICE_REMIND_MODE = "NOTICE_REMIND_MODE";

        /**
         * 职位状态
         * [10-在职 20-离职]
         */
        String POSITION_STATUS = "BASE_POSITION_STATUS";

        /**
         * 机构类型
         * [10-单位 20-部门]
         */
        String ORG_TYPE = "BASE_ORG_TYPE";
        /**
         * 运营模式
         * 01-台球、02-棋牌
         */
        String OPERATION_TYPE = "OPERATION_TYPE";

        /**
         * 经营方式
         * 01-直营、02-加盟、03-联营
         */
        String BUSINESS_TYPE = "BUSINESS_TYPE";
        /**
         * 授权记录类型
         */
        String BUSINESS_RECORD_TYPE = "BUSINESS_RECORD_TYPE";

        /**
         * 角色类别
         * [10-功能角色 20-桌面角色 30-数据角色]
         */
        String ROLE_CATEGORY = "BASE_ROLE_CATEGORY";

        /**
         * 婚姻状况
         * [10-未婚 20-已婚]
         */
        String MARITAL_STATUS = "MARITAL_STATUS";
        /**
         * 助教服务展示标签
         */
        String SERVICE_TAGS = "SERVICE_TAGS";

        /**
         * 台桌状态
         * [10-空闲 20-使用中 30-已预订]
         */
        String TABLE_STATUS = "TABLE_STATUS";

        /**
         * 台桌类型
         */
        String BASE_TABLE_TYPE = "BASE_TABLE_TYPE";

        /**
         * 用户属性
         * [10-平台 20-单门店 30-连锁门店]
         */
        String USER_ATTRIBUTE = "USER_ATTRIBUTE";

        /**
         * 会员来源
         * [1-门店销售]
         */
        String MEMBER_SOURCE = "MEMBER_SOURCE";
        /**
         * 可用范围
         * [1-台费 2-商品 3-服务 4-权益卡]
         */
        String USABLE_RANGE = "USABLE_RANGE";
        /**
         * 可用范围
         * [0台费 1-服务 2-商品 3-套餐]
         */
        String BIZ_USABLE_RANGE = "BIZ_USABLE_RANGE";
        /**
         * 支付渠道
         */
        String PAY_CHANNEL = "PAY_CHANNEL";
        /**
         * 优惠劵类型
         * [1代金券 2折扣券 3兑换券 4时长券]
         */
        String COUPON_TYPE = "COUPON_TYPE";
        /**
         * 会员优惠劵状态
         * [1 未使用 2 已使用 3已过期]
         */
        String MEMBER_COUPON_STATUS = "MEMBER_COUPON_STATUS";
        /**
         * 卡类型
         * [ 1 折扣卡 2 无限次卡 3 有限次卡 4 时长卡]
         */
        String CARD_TYPE = "CARD_TYPE";
        /**
         * 卡状态
         * [1 未使用 2 已使用 3已过期]
         */
        String MEMBER_CARD_STATUS = "MEMBER_CARD_STATUS";
        /**
         * 库存类型
         */
        String OUTIN_TYPE = "OUTIN_TYPE";
        /**
         * 单据来源渠道;[0-web 1-pos  2-app]
         */
        String OUTIN_SOURCE_TYPE = "SOURCE_TYPE";
        /**
         * 积分来源渠道;[0-web 1-pos  2-app]
         */
        String SCORE_RECORD_SOURCE_TYPE = "SCORE_RECORD_SOURCE_TYPE";
        /**
         * 单据状态   0正常结算  1挂单
         */
        String BILL_STATE = "BILL_STATE";
        /**
         * 1代金券 2商品券
         */
        String PAYMENT_COUPON = "PAYMENT_COUPON";

        /**
         * 审核状态 0-待审核, 1-已审核, 2-作废
         */
        String BASE_OUTIN_STATE = "BASE_OUTIN_STATE";

        /**
         * 上班状态
         */
        String WORKING_STATUS = "WORKING_STATUS";
        /**
         * 单据类型   0正单  1退单  2 取消
         */
        String BILL_TYPE = "BILL_TYPE";
        /**
         * 商品计量单位
         */
        String PRODUCT_UNIT = "PRODUCT_UNIT";
        /**
         * 权益类型
         */
        String EQUITY_TYPE = "EQUITY_TYPE";
        /**
         * 星期
         */
        String WEEK = "WEEK";
        /**
         * 订单类型 0开台 1购物 2卡券 3充值
         */
        String CASH_TYPE = "CASH_TYPE";

        /**
         * 重算类型
         */
        String RECALC_TYPE = "RECALC_TYPE";

        /**
         * 重算类型
         */
        String RECALC_STATUS = "RECALC_STATUS";
        /**
         * 营销活动类型
         */
        String PROMOTION_ACTIVITY_TYPE = "PROMOTION_ACTIVITY_TYPE";
        /**
         * 营销活动状态
         */
        String PROMOTION_ACTIVITY_STATUS = "PROMOTION_ACTIVITY_STATUS";
        /**
         * 分类 0开台 1购物 2卡券 3充值 4调整 5兑换
         */
        String SCORE_RECORDS_TYPE = "SCORE_RECORDS_TYPE";
        /**
         * 轮播类型 1 首页
         */
        String BANNER_TYPE = "BANNER_TYPE";
        /**
         * 链接类型 1 外链 2 内链
         */
        String LINK_TYPE = "LINK_TYPE";
        /**
         * 订单来源 1 pos 2 自助 3 扫码
         */
        String ORDER_SOURCE = "ORDER_SOURCE";

        /**
         * 支付类型业务逻辑
         */
        String PAYMENT_BIZ_TYPE = "PAYMENT_BIZ_TYPE";
        /**
         * 支付类型
         */
        String PAYMENT_TYPE = "PAYMENT_TYPE";
        /**
         * 记账类型
         */
        String ACCOUNTING_TYPE = "ACCOUNTING_TYPE";
        /**
         * 记账收入类型
         */
        String PAY_IN_TYPE = "PAY_IN_TYPE";
        /**
         * 记账支出类型
         */
        String PAY_OUT_TYPE = "PAY_OUT_TYPE";
        /**
         * 交班状态 10 未交班 20 已交班"
         */
        String SHIFT_HANDOVER_STATUS = "SHIFT_HANDOVER_STATUS";
        /**
         * 点钟方式
         */
        String CLOCK_TYPE = "CLOCK_TYPE";
        /**
         * 语音模板类型
         */
        String VOICE_TEMPLATE_TYPE = "VOICE_TEMPLATE_TYPE";
        /**
         * 提成方式
         */
        String COMMISSION_TYPE = "COMMISSION_TYPE";
        /**
         * 体验时长方式
         */
        String OVER_TYPE = "OVER_TYPE";

        /**
         * 收银申请备注
         */
        String BUSINESS_AUTH_TAG = "BUSINESS_AUTH_TAG";
        /**
         * 会员类型
         */
        String MEMBER_TYPE = "MEMBER_TYPE";
        /**
         * 配送单状态
         */
        String CASH_DISTRIBUTE_STATUS = "CASH_DISTRIBUTE_STATUS";
        String COUPON_STATUS = "COUPON_STATUS";
        String COUPON_ISSUE_TYPE = "COUPON_ISSUE_TYPE";
        String COUPON_USED_METHOD = "COUPON_USED_METHOD";
        String USED_REPEAT = "USED_REPEAT";


        /**
         * 评价分类标签
         */
        String HIGH_APPRAISAL_TAGS = "HIGH_APPRAISAL_TAGS";
        String MEDIUM_APPRAISAL_TAGS = "MEDIUM_APPRAISAL_TAGS";
        String LOW_APPRAISAL_TAGS = "LOW_APPRAISAL_TAGS";

        /**
         * 叫号状态
         */
        String QUEUING_CALLING_STATUS = "QUEUING_CALLING_STATUS";

        /**
         * 撤销收款标签
         */
        String REVOKE_PAY_TAG = "REVOKE_PAY_TAG";

        /**
         * 支付流水类型
         */
        String PAYMENT_TRANSACTION_TYPE = "PAYMENT_TRANSACTION_TYPE";

        /**
         * 支付流水状态
         */
        String PAYMENT_TRANSACTION_STATUS = "PAYMENT_TRANSACTION_STATUS";

        /**
         * 余额扣除方式
         */
        String ACCOUNT_DEDUCT_TYPE = "ACCOUNT_DEDUCT_TYPE";
        String BIZ_EQUITY_TYPE = "BIZ_EQUITY_TYPE";

    }

    /**
     * app字典类型
     */
    interface App {
        /**
         * 支付方式
         */
        String PAY_TYPE = "PAY_TYPE";
        /**
         * 来源
         */
        String ORDER_SOURCE = "ORDER_SOURCE";

        /**
         * 单品备注标签
         */
        String ITEM_TAGS = "ITEM_TAGS";

        /**
         * 单品退备注标签
         */
        String ITEM_RETREAT = "ITEM_RETREAT";
        /**
         * 商品退备注标签
         */
        String PRODUCT_ITEM_RETREAT = "PRODUCT_ITEM_RETREAT";

        /**
         * 商品赠送备注标签
         */
        String PRODUCT_ITEM_GIFT = "PRODUCT_ITEM_GIFT";
        /**
         * 结开备注
         */
        String KNOT_TAGS = "KNOT_TAGS";
        /**
         * 修改结开备注
         */
        String MODIFY_KNOT_TAGS = "MODIFY_KNOT_TAGS";
        /**
         * 改价备注
         */
        String CHANGE_PRICE_TAGS = "CHANGE_PRICE_TAGS";
        /**
         * 修改改价备注
         */
        String MODIFY_CHANGE_PRICE_TAGS = "MODIFY_CHANGE_PRICE_TAGS";
        /**
         * 单品优惠备注标签
         */
        String ITEM_DISCOUNT_TAGS = "ITEM_DISCOUNT_TAGS";
        /**
         * 整单备注标签
         */
        String ORDER_TAGS = "ORDER_TAGS";
        /**
         * 整单优惠备注标签
         */
        String ORDER_DISCOUNT_TAGS = "ORDER_DISCOUNT_TAGS";

        /**
         * 免单备注标签
         */
        String ORDER_FREE_TAGS = "ORDER_FREE_TAGS";
        /**
         * 撤单备注标签
         */
        String ORDER_CANCEL_TAGS = "ORDER_CANCEL_TAGS";

        /**
         * 挂单备注标签
         */
        String REGISTRATION_TAGS = "REGISTRATION_TAGS";
        /**
         * 反结账备注标签
         */
        String COUNTER_CHECKOUT_TAGS = "COUNTER_CHECKOUT_TAGS";
        /**
         * 整单退款备注标签
         */
        String FULL_REFUND_TAGS = "FULL_REFUND_TAGS";
        /**
         * 部分退款备注标签
         */
        String PART_REFUND_TAGS = "PART_REFUND_TAGS";
        /**
         * 配送单取消备注标签
         */
        String ORDER_DISTRIBUTE_CANCEL_TAGS = "ORDER_DISTRIBUTE_CANCEL_TAGS";
        /**
         * 配送单备注标签
         */
        String ORDER_DISTRIBUTE_TAGS = "ORDER_DISTRIBUTE_TAGS";
    }

    /**
     * 租户服务
     */
    interface System {
        // @lamp.generator auto insert System

        /**
         * 数据范围 [01-全部 02-本单位及子级 03-本单位 04-本部门 05-本部门及子级 06-个人 07-自定义]
         */
        String RESOURCE_DATA_SCOPE = "TENANT_RESOURCE_DATA_SCOPE";
        /**
         * 资源类型 [10-应用 20-菜单 30-视图 40-功能 50-字段 06-数据]
         */
        String RESOURCE_TYPE = "TENANT_RESOURCE_TYPE";
        /**
         * 打开方式 [01-组件 02-内链 03-外链]
         */
        String RESOURCE_OPEN_WITH = "TENANT_RESOURCE_OPEN_WITH";

        /**
         * 字典分类 [10-系统字典 20-业务字典]
         */
        String DICT_CLASSIFY = "TENANT_DICT_CLASSIFY";

        /**
         * 应用类型 [10-自建应用 20-第三方应用]
         */
        String APPLICATION_TYPE = "TENANT_APPLICATION_TYPE";
        /**
         * 授权类型 [10-应用授权 20-应用续期 30-取消授权]
         */
        String APPLICATION_GRANT_TYPE = "TENANT_APPLICATION_GRANT_TYPE";
        /**
         * 参数类型 [10-系统参数 20-业务参数]
         */
        String PARAMETER_TYPE = "TENANT_PARAMETER_TYPE";
        /**
         * 参数单位 【10 分钟 20 点】
         */
        String PARAM_UNIT = "PARAM_UNIT";
        /**
         * 参数类型
         */
        String PARAM_TYPE = "PARAM_TYPE";
        /**
         * 地区来源
         * [10-爬取 20-新增]
         */
        String AREA_SOURCE = "TENANT_AREA_SOURCE";
        /**
         * 客户端类型
         * [10-WEB网站;15-移动端应用;20-手机H5网页;25-内部服务; 30-第三方应用]
         */
        String CLIENT_TYPE = "TENANT_CLIENT_TYPE";
        /**
         * 租户审批状态
         * [05-正常 10-待初始化 15-已撤回 20-待审核 25-已拒绝 30-已同意]
         */
        String TENANT_STATUS = "TENANT_TENANT_STATUS";
        /**
         * 登录状态
         * [01-登录成功 02-验证码错误 03-密码错误 04-账号锁定 05-切换租户 06-短信验证码错误]
         */
        String LOGIN_STATUS = "SYSTEM_LOGIN_STATUS";
        /**
         * 小程序版本
         */
        String VERSION = "VERSION";
        /**
         * 收银通知类型
         */
        String NOTICE_TYPE = "NOTICE_TYPE";
        /**
         * 小程序模板类型
         */
        String APPLET_TMPL_TYPE = "APPLET_TMPL_TYPE";
        /**
         * 查询模板类型
         */
        String QUERY_TEMPLATE_CATEGORY = "QUERY_TEMPLATE_CATEGORY";
        /**
         * 查询逻辑
         */
        String QUERY_WHERE_LOGIC = "QUERY_WHERE_LOGIC";
        /**
         * 查询条件
         */
        String QUERY_WHERE_CONDITION = "QUERY_WHERE_CONDITION";
        /**
         * 查询参照
         */
        String QueryConsult = "QueryConsult";
        /**
         * 业务收银授权类型
         */
        String BUSINESS_AUTH_TYPE = "BUSINESS_AUTH_TYPE";
        /**
         * 团购充值订单状态
         */
        String DEAL_ORDER_STATUS = "DEAL_ORDER_STATUS";
        /**
         * 许可证功能类型
         */
        String SUBSCRIPTION_FEATURE_TYPE = "SUBSCRIPTION_FEATURE_TYPE";
        /**
         * 许可证功能模块
         */
        String SUBSCRIPTION_FEATURE_MODULE = "SUBSCRIPTION_FEATURE_MODULE";
        /**
         * 许可证套餐类型
         */
        String SUBSCRIPTION_TEMPLATE_TYPE = "SUBSCRIPTION_TEMPLATE_TYPE";
        /**
         * 许可证租户状态
         */
        String SUBSCRIPTION_TENANT_STATUS = "SUBSCRIPTION_TENANT_STATUS";

        /**
         * 删除台桌描述
         */
        String CLEAR_TABLE_COMMENT = "CLEAR_TABLE_COMMENT";
        /**
         * 短信充值状态
         */
        String SMS_RECHARGE_STATE = "SMS_RECHARGE_STATE";
        /**
         * 短信发送类型
         */
        String SMS_SEND_TYPE = "SMS_SEND_TYPE";

    }

    /**
     * 认证服务
     */
    interface Oauth {
        // @lamp.generator auto insert Oauth

    }

    /**
     * 文件服务
     */
    interface File {
        // @lamp.generator auto insert File

    }

    /**
     * 消息服务
     */
    interface Msg {
        // @lamp.generator auto insert Msg

    }

    /**
     * 网关服务
     */
    interface Gateway {
        // @lamp.generator auto insert Gateway

    }

    /**
     * 支付服务
     */
    interface Pay {
        /**
         * 商户类型
         */
        String MCH_TYPE = "MCH_TYPE";
    }

    /**
     * POS收银
     */
    interface Pos {
        /**
         * 打赏订单状态
         */
        String REWARD_ORDER_STATUS = "REWARD_ORDER_STATUS";
    }

    // 新增内部 Xxx 接口后，请在PackageUtils的static代码块中新增 putDictType(EchoDictType.Xxx.class)， 否则代码生成器会重复生成
}
