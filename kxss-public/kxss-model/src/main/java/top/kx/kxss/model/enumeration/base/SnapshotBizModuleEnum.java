package top.kx.kxss.model.enumeration.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import top.kx.basic.interfaces.BaseEnum;

import java.util.stream.Stream;

/**
 * 单据状态
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@ApiModel(value = "SnapshotBizModuleEnum", description = "单据状态-枚举")
public enum SnapshotBizModuleEnum implements BaseEnum {
    /**
     * 服务员工
     */
    SERVICE_EMPLOYEE("1", "助教"),

    /**
     * 台桌计费
     */
    TABLE_CHARGING("2", "台桌计费"),
    /**
     * 员工
     */
    EMPLOYEE("3", "员工"),

    /**
     * 收银参数
     */
    PARAMETERS("4", "收银参数"),

    /**
     * 结账方式
     */
    PAYMENT_TYPE("5", "结账方式"),

    /**
     * 角色
     */
    BASE_ROLE("6", "角色"),

    /**
     * 台桌
     */
    BASE_TABLE("7", "台桌"),

    /**
     * 角色权限
     */
    BASE_ROLE_PERMISSION("8", "角色权限"),

    /**
     * 套餐
     */
    BASE_THAIL("9", "套餐"),

    /**
     * 会员等级
     */
    MEMBER_GRADE("10", "会员等级"),

    /**
     * 商品
     */
    PRODUCT("11", "商品"),


    ;

    @ApiModelProperty(value = "状态")
    private String code;
    @ApiModelProperty(value = "描述")
    private String desc;

    public static SnapshotBizModuleEnum match(String val, SnapshotBizModuleEnum def) {
        return Stream.of(values()).parallel().filter((item) -> item.getCode().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static SnapshotBizModuleEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(SnapshotBizModuleEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "MONTH,WEEK,DAY,NUL", example = "NUL")
    public String getCode() {
        return code;
    }
}
