package top.kx.kxss.model.enumeration.base;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import top.kx.basic.interfaces.BaseEnum;

import java.util.stream.Stream;

/**
 * 支付渠道
 *
 * <AUTHOR>
 * @date 2018/12/29
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@ApiModel(value = "PayChannelEnum", description = "支付渠道-枚举")
public enum PayChannelEnum implements BaseEnum {
    /**
     * 商米
     */
    SUNMI("10", "SUNMI", "商米"),
    /**
     * 扫呗
     */
    SAOBEI("20", "SAOBEI", "扫呗"),
    /**
     * 拉卡拉
     */
    LKL("30", "LKL", "拉卡拉"),
    ;

    @ApiModelProperty(value = "类型")
    private String code;
    @ApiModelProperty(value = "标识")
    private String index;
    @ApiModelProperty(value = "描述")
    private String desc;

    public static PayChannelEnum match(String val, PayChannelEnum def) {
        return Stream.of(values()).parallel().filter((item) -> item.getCode().equalsIgnoreCase(val)).findAny().orElse(def);
    }

    public static PayChannelEnum get(String val) {
        return match(val, null);
    }

    public boolean eq(PayChannelEnum val) {
        return val != null && eq(val.name());
    }

    @Override
    @ApiModelProperty(value = "编码", allowableValues = "MONTH,WEEK,DAY,NUL", example = "NUL")
    public String getCode() {
        return code;
    }
}
