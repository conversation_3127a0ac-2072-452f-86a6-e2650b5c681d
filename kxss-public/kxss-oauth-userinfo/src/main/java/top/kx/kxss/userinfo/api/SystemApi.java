package top.kx.kxss.userinfo.api;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import top.kx.basic.base.R;
import top.kx.basic.constant.Constants;
import top.kx.kxss.model.entity.system.SysUser;

/**
 * <AUTHOR>
 * @version v1.0
 * @date 2022/9/29 11:05 PM
 * @create [2022/9/29 11:05 PM ] [tangyh] [初始创建]
 */
@FeignClient(name = "${" + Constants.PROJECT_PREFIX + ".feign.system-server:kxss-system-server}")
public interface SystemApi {
    /**
     * 查询用户信息
     *
     * @param id 用户ID
     * @return top.kx.basic.base.R<top.kx.kxss.model.entity.system.SysUser>
     * <AUTHOR>
     * @date 2022/11/18 2:22 PM
     * @create [2022/11/18 2:22 PM ] [tangyh] [初始创建]
     */
    @GetMapping("/defUser/{id}")
    R<SysUser> getUserById(@PathVariable Long id);
}
