package top.kx.kxss.base.manager.scan.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.scan.BaseScanLog;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.scan.BaseScanLogManager;
import top.kx.kxss.base.mapper.scan.BaseScanLogMapper;

/**
 * <p>
 * 通用业务实现类
 * 扫码日志
 * </p>
 *
 * <AUTHOR>
 * @date 2024-05-17 13:44:53
 * @create [2024-05-17 13:44:53] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseScanLogManagerImpl extends SuperManagerImpl<BaseScanLogMapper, BaseScanLog> implements BaseScanLogManager {

}


