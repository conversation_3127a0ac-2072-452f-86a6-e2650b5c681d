package top.kx.kxss.base.manager.biz;

import com.baomidou.mybatisplus.core.metadata.IPage;
import top.kx.basic.base.manager.SuperManager;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.kxss.base.entity.biz.BaseBizLog;
import top.kx.kxss.base.vo.result.biz.BaseBizLogResultVO;

/**
 * <p>
 * 通用业务接口
 * 模块业务日志
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-22 13:55:39
 * @create [2023-04-22 13:55:39] [dou] [代码生成器生成]
 */
public interface BaseBizLogManager extends SuperManager<BaseBizLog> {

    void createBizLog(BaseBizLog model);

    IPage<BaseBizLogResultVO> selectPageResultVO(IPage<BaseBizLog> page, QueryWrap<BaseBizLog> wrap);

    IPage<BaseBizLogResultVO> selectMemberPageResultVO(IPage<BaseBizLog> page, QueryWrap<BaseBizLog> wrap);
}


