package top.kx.kxss.base.manager.user.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.manager.impl.SuperCacheManagerImpl;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.basic.utils.CollHelper;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.manager.user.BaseEmployeeManager;
import top.kx.kxss.base.mapper.user.BaseEmployeeMapper;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.common.cache.base.user.EmployeeCacheKeyBuilder;
import top.kx.kxss.common.constant.DsConstant;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用业务实现类
 * 员工
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 * @create [2021-10-18] [zuihou] [代码生成器生成]
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BaseEmployeeManagerImpl extends SuperCacheManagerImpl<BaseEmployeeMapper, BaseEmployee> implements BaseEmployeeManager {

    @Override
    protected CacheKeyBuilder cacheKeyBuilder() {
        return new EmployeeCacheKeyBuilder();
    }

    @Override
    public IPage<BaseEmployeeResultVO> selectPageResultVO(IPage<BaseEmployee> page, Wrapper<BaseEmployee> wrapper) {
        return baseMapper.selectPageResultVO(page, wrapper);
    }

    @Transactional(readOnly = true)
    @Override
    @DS(DsConstant.BASE_TENANT)
    public Map<Serializable, Object> findByIds(Set<Serializable> params) {
        if (CollUtil.isEmpty(params)) {
            return Collections.emptyMap();
        }
        Set<Serializable> ids = new HashSet<>();
        params.forEach(item -> {
            if (item instanceof Collection) {
                ids.addAll((Collection<? extends Serializable>) item);
            } else {
                ids.add(item);
            }
        });
        List<BaseEmployee> list = findByIds(ids, null)
                .stream().filter(Objects::nonNull).collect(Collectors.toList());
        for (BaseEmployee baseEmployee : list) {
            if (StrUtil.isBlank(baseEmployee.getRealName())) {
                baseEmployee.setRealName(baseEmployee.getName());
            }
        }
        return CollHelper.uniqueIndex(list,
                BaseEmployee::getId, BaseEmployee::getRealName);
    }

    @Override
    public List<BaseEmployeeResultVO> findList(Wrapper<BaseEmployee> wrapper) {
        List<BaseEmployeeResultVO> list = baseMapper.findList(wrapper);
        for (BaseEmployeeResultVO resultVO : list) {
            if (resultVO.getDeleteFlag() == 1) {
                resultVO.setRealName(resultVO.getRealName().concat("(已删除)"));
            }
            if (StrUtil.isBlank(resultVO.getName())) {
                resultVO.setName(resultVO.getRealName());
            }
        }
        return list;
    }
}
