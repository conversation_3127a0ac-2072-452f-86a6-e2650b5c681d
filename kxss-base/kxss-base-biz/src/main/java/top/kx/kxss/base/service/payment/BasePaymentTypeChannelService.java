package top.kx.kxss.base.service.payment;

import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.kxss.base.entity.payment.BasePaymentTypeChannel;
import top.kx.kxss.base.vo.result.payment.BaseMchAppResultVO;
import top.kx.kxss.base.vo.save.payment.BasePaymentTypeChannelSaveVO;
import top.kx.kxss.base.vo.update.payment.BasePaymentTypeChannelUpdateVO;
import top.kx.kxss.base.vo.result.payment.BasePaymentTypeChannelResultVO;
import top.kx.kxss.base.vo.query.payment.BasePaymentTypeChannelPageQuery;
import top.kx.kxss.pay.vo.query.MchAppPageQuery;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 支付方式对应渠道
 * </p>
 *
 * <AUTHOR>
 * @date 2024-06-12 10:18:05
 * @create [2024-06-12 10:18:05] [dou] [代码生成器生成]
 */
public interface BasePaymentTypeChannelService extends SuperService<Long, BasePaymentTypeChannel, BasePaymentTypeChannelSaveVO,
    BasePaymentTypeChannelUpdateVO, BasePaymentTypeChannelPageQuery, BasePaymentTypeChannelResultVO> {

    BasePaymentTypeChannel getOne(LbQueryWrap<BasePaymentTypeChannel> eq);

    List<BaseMchAppResultVO> queryMchApp();

    List<BaseMchAppResultVO> queryMchApp(MchAppPageQuery query);

    boolean saveBankCard(BasePaymentTypeChannel data);
}


