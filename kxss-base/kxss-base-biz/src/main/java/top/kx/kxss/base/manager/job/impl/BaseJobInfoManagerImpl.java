package top.kx.kxss.base.manager.job.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.job.BaseJobInfo;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.job.BaseJobInfoManager;
import top.kx.kxss.base.mapper.job.BaseJobInfoMapper;

/**
 * <p>
 * 通用业务实现类
 * 定时任务信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-06 11:35:50
 * @create [2023-09-06 11:35:50] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseJobInfoManagerImpl extends SuperManagerImpl<BaseJobInfoMapper, BaseJobInfo> implements BaseJobInfoManager {

}


