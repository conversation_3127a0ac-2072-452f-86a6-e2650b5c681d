package top.kx.kxss.base.service.outin.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.basic.exception.BizException;
import top.kx.basic.exception.code.ExceptionCode;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.jackson.JsonUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.base.entity.attribute.BaseAttribute;
import top.kx.kxss.base.entity.attribute.BaseAttributeSetting;
import top.kx.kxss.base.entity.outin.*;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.stock.BaseProductStock;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.entity.warehouse.BaseWarehouse;
import top.kx.kxss.base.manager.outin.BaseOutinManager;
import top.kx.kxss.base.manager.outin.BaseOutinProductManager;
import top.kx.kxss.base.manager.product.BaseProductManager;
import top.kx.kxss.base.manager.stock.BaseProductStockManager;
import top.kx.kxss.base.mapper.outin.BaseOutinMapper;
import top.kx.kxss.base.service.attribute.BaseAttributeService;
import top.kx.kxss.base.service.attribute.BaseAttributeSettingService;
import top.kx.kxss.base.service.biz.BaseBizLogService;
import top.kx.kxss.base.service.outin.*;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.service.warehouse.BaseWarehouseService;
import top.kx.kxss.base.vo.query.outin.BaseOutinPageQuery;
import top.kx.kxss.base.vo.query.outin.SellOutinQuery;
import top.kx.kxss.base.vo.result.outin.*;
import top.kx.kxss.base.vo.result.product.BaseProductResultVO;
import top.kx.kxss.base.vo.save.biz.BaseBizLogSaveVO;
import top.kx.kxss.base.vo.save.outin.*;
import top.kx.kxss.base.vo.update.outin.BaseOutinAdjustmentUpdateVO;
import top.kx.kxss.base.vo.update.outin.BaseOutinStocktakingUpdateVO;
import top.kx.kxss.base.vo.update.outin.BaseOutinUpdateVO;
import top.kx.kxss.common.api.HelperApi;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.ParameterKey;
import top.kx.kxss.model.constant.EchoDictType;
import top.kx.kxss.model.enumeration.base.*;
import top.kx.kxss.pos.vo.ItemRemarksResultVO;
import top.kx.kxss.pos.vo.OrderRemarksResultVO;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static top.kx.basic.context.ContextUtil.getCurrentCompanyId;

/**
 * <p>
 * 业务实现类
 * 商品出入库主表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-06 14:51:59
 * @create [2023-04-06 14:51:59] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseOutinServiceImpl extends SuperServiceImpl<BaseOutinManager, Long, BaseOutin, BaseOutinSaveVO,
        BaseOutinUpdateVO, BaseOutinPageQuery, BaseOutinResultVO> implements BaseOutinService {

    @Autowired
    private BaseOutinProductManager baseOutinProductManager;
    @Autowired
    private BaseProductStockManager baseProductStockManager;
    @Autowired
    private BaseProductManager baseProductManager;
    @Autowired
    private BaseOutinMapper baseOutinMapper;
    @Autowired
    private EchoService echoService;
    @Autowired
    private BaseOutinStocktakingService baseOutinStocktakingService;
    @Autowired
    private BaseOutinAdjustmentService baseOutinAdjustmentService;
    @Autowired
    private BaseBizLogService bizLogService;
    @Autowired
    private BaseWarehouseService baseWarehouseService;
    @Autowired
    private BaseOutinApprovalService baseOutinApprovalService;
    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private BaseAttributeService baseAttributeService;
    @Autowired
    private BaseAttributeSettingService baseAttributeSettingService;
    @Autowired
    private BaseOutinProductAttributeService baseOutinProductAttributeService;
    @Autowired
    private BaseEmployeeService baseEmployeeService;
    @Autowired
    private HelperApi helperApi;

    @Override
    public String getCode() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        String time = LocalDateTime.now().format(formatter);
        BaseOutin baseOutin = superManager.getOne(Wraps.<BaseOutin>lbQ().like(BaseOutin::getCode, time)
                .notLike(BaseOutin::getCode, "KX")
                .orderByDesc(BaseOutin::getCode).last("limit 1"));
        if (baseOutin == null) {
            return time + "0001";
        }
        long num = Long.parseLong(baseOutin.getCode()) + 1;
        return String.format("%012d", num);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseOutin saveOutin(BaseOutinSaveVO model) {
        BaseOutin baseOutin = BeanUtil.copyProperties(model, BaseOutin.class);
        baseOutin.setCode(StrUtil.isBlank(model.getCode()) ? getCode() : model.getCode());
        baseOutin.setBillState(0);
        Long orgId = ContextUtil.getCurrentCompanyId();
        baseOutin.setCreatedOrgId(orgId);
        baseOutin.setCreatedBy(ContextUtil.getUserId());
        baseOutin.setUpdatedBy(ContextUtil.getUserId());
        baseOutin.setEmployeeId(ContextUtil.getEmployeeId());
        baseOutin.setOrgId(orgId);
        baseOutin.setSourceType(model.getSourceType());
        baseOutin.setWarehouseId(Objects.nonNull(model.getWarehouseId()) ? model.getWarehouseId() : 1L);
        ArgumentAssert.isFalse(CollUtil.isEmpty(model.getOutinProductList()), "请选择商品");
        List<BaseOutinProduct> outinProductList = model.getOutinProductList().stream().map(vo -> {
            BaseOutinProduct baseOutinProduct = BeanUtil.copyProperties(vo, BaseOutinProduct.class);
            baseOutinProduct.setCreatedOrgId(orgId);
            baseOutinProduct.setUpdatedBy(ContextUtil.getUserId());
            baseOutinProduct.setCreatedBy(ContextUtil.getUserId());
            baseOutinProduct.setCostPrice(model.getCostPrice());
            baseOutinProduct.setRemarks(model.getRemarks());
            baseOutinProduct.setEmployeeId(ContextUtil.getEmployeeId());
            baseOutinProduct.setCostPrice(vo.getCostPrice());
            baseOutinProduct.setWarehouseId(model.getWarehouseId());
            return baseOutinProduct;
        }).collect(Collectors.toList());
        Map<Long, BaseOutinProduct> outinProductMap = outinProductList.stream().collect(Collectors.toMap(BaseOutinProduct::getProductId, k -> k,
                (v1, v2) -> v2.setNum(v1.getNum() + v2.getNum())));
        outinProductList = new ArrayList<>(outinProductMap.values());
        List<BaseProductStock> stockListWithLock = baseProductStockManager.getStockListWithLock(outinProductList.stream().map(BaseOutinProduct::getProductId).distinct().collect(Collectors.toList()));
        //检查库存现有量
        ArgumentAssert.isFalse(!baseProductStockManager.checkStock(baseOutin, outinProductList, stockListWithLock), "商品库存不足");
        //新增商品库存主表
        ArgumentAssert.isFalse(!superManager.save(baseOutin), "库存新增失败");
        //商品明细信息
        baseOutinProductManager.remove(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getOutinId, baseOutin.getId()));
        outinProductList.forEach(baseOutinProduct -> {
            baseOutinProduct.setOutinId(baseOutin.getId());
            baseOutinProduct.setEmployeeId(ContextUtil.getEmployeeId());
            baseOutinProduct.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        });
        //回写商品库存现有量
        ArgumentAssert.isFalse(!baseProductStockManager.rewriteStock(outinProductList, baseOutin, stockListWithLock), "库存现有量操作失败");
        // 待审核的不需要设置剩余库存
        if (Objects.equals(model.getState(), 0)) {
            outinProductList.forEach(s -> {
                s.setResidueNum(null);
                s.setLockNum(null);
            });
        }
        ArgumentAssert.isFalse(!baseOutinProductManager.saveBatch(outinProductList), "库存明细操作失败");
        return baseOutin;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseOutin updateOutin(BaseOutinUpdateVO model) {
        BaseOutin outin = getById(model.getId());
        ArgumentAssert.notNull(outin, "单据不存在");
        ArgumentAssert.isTrue(Objects.equals(outin.getState(), 0), "只有待审核的才允许修改");
        if (StringUtils.equals(outin.getType(), OutinTypeEnum.PURCHASE_IN.getCode())) {
            ArgumentAssert.isTrue(Objects.nonNull(model.getAmount()), "采购单总金额不能为空");
            ArgumentAssert.isTrue(model.getOutinProductList().stream().allMatch(s -> Objects.nonNull(s.getAmount())), "采购单商品进货总价不能为空");
        }
        model.setState(outin.getState());
        BaseOutin baseOutin = updateById(model);
        outin = getById(model.getId());
        ArgumentAssert.isTrue(CollUtil.isNotEmpty(model.getOutinProductList()), "请选择商品");
        List<BaseOutinProduct> outinProductList = model.getOutinProductList()
                .stream().map(vo -> BeanUtil.copyProperties(vo, BaseOutinProduct.class))
                .collect(Collectors.toList());
        Map<Long, BaseOutinProduct> outinProductMap = outinProductList.stream().collect(Collectors.toMap(BaseOutinProduct::getProductId, k -> k,
                (v1, v2) -> v2.setNum(v1.getNum() + v2.getNum())));
        outinProductList = new ArrayList<>(outinProductMap.values());

        //商品明细信息
        if (Objects.nonNull(model.getWarehouseId())) {
            outinProductList.forEach(s -> s.setWarehouseId(model.getWarehouseId()));
        }
        List<BaseProductStock> stockListWithLock = baseProductStockManager.getStockListWithLock(outinProductList.stream().map(BaseOutinProduct::getProductId).distinct().collect(Collectors.toList()));
        //检查库存现有量
        ArgumentAssert.isFalse(!baseProductStockManager.checkStock(outin, outinProductList, stockListWithLock), "商品库存不足");
        BaseOutin finalOutin = outin;
        outinProductList.forEach(baseOutinProduct -> {
            baseOutinProduct.setOutinId(finalOutin.getId());
            baseOutinProduct.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        });
        baseOutinProductManager.remove(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getOutinId, outin.getId()));
        ArgumentAssert.isFalse(!baseOutinProductManager.saveBatch(outinProductList), "库存明细操作失败");
        //回写商品库存现有量
        ArgumentAssert.isFalse(!baseProductStockManager.rewriteStock(outinProductList, outin, stockListWithLock), "库存现有量操作失败");
        return baseOutin;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateState(BaseOutinStateVO model) {
        boolean lock = false;
        try {
            lock = distributedLock.lock("OUTIN_STATE:" + model.getId(), 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }
            OutinTypeEnum outinTypeEnum = OutinTypeEnum.get(model.getType());
            switch (outinTypeEnum) {
                case PURCHASE_IN:
                    BaseOutin purchaseOutin = superManager.getById(model.getId());
                    ArgumentAssert.notNull(purchaseOutin, "采购入库单不存在, 无需审核");
                    ArgumentAssert.isFalse(Objects.equals(purchaseOutin.getState(), BaseOutinStateEnum.INVALID.getState()), "已经作废的采购入库单,不允许处理");
                    ArgumentAssert.isFalse(Objects.equals(purchaseOutin.getState(), BaseOutinStateEnum.REVERSAL_ENTRY.getState()), "红冲已经审核通过的采购入库单,不允许处理");
                    purchaseOutin.setIsLockNum(false);
                    purchaseOutin.setIsNum(true);
                    // 待审核状态, 可以 已审核, 作废
                    if (Objects.equals(purchaseOutin.getState(), BaseOutinStateEnum.NO_REVIEWED.getState())) {
                        // 作废
                        if (Objects.equals(model.getState(), BaseOutinStateEnum.INVALID.getState())) {
                            purchaseOutin.setState(model.getState());
                            purchaseOutin.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                            superManager.updateById(purchaseOutin);
                            break;
                        }
                        // 通过
                        if (Objects.equals(model.getState(), BaseOutinStateEnum.REVIEWED.getState())) {
                            // 查询订单详情
                            List<BaseOutinProduct> purchaseBaseOutinProductList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getDeleteFlag, 0)
                                    .eq(BaseOutinProduct::getOutinId, purchaseOutin.getId()));
                            List<BaseProductStock> stockListWithLock = baseProductStockManager.getStockListWithLock(purchaseBaseOutinProductList.stream().map(BaseOutinProduct::getProductId).distinct().collect(Collectors.toList()));
                            purchaseOutin.setState(model.getState());
                            purchaseOutin.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                            superManager.updateById(purchaseOutin);
                            // baseOutinProduct 存在门店ID为空的情况, 强制赋值
                            purchaseBaseOutinProductList.forEach(baseOutinProduct -> {
                                if (Objects.isNull(baseOutinProduct.getCreatedOrgId())) {
                                    baseOutinProduct.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                                }
                            });
                            // 校验库存量
                            ArgumentAssert.isFalse(!baseProductStockManager.checkStock(purchaseOutin, purchaseBaseOutinProductList, stockListWithLock), "商品库存不足");
                            // 更新
                            ArgumentAssert.isFalse(!baseOutinProductManager.updateBatchById(purchaseBaseOutinProductList));
                            //回写商品库存现有量
                            ArgumentAssert.isFalse(!baseProductStockManager.rewriteStock(purchaseBaseOutinProductList, purchaseOutin, stockListWithLock), "库存现有量操作失败");
                            break;
                        }
                        ArgumentAssert.isTrue(false, "待审核的库存单, 只能审核通过和作废");
                    }

                    // 审核通过的只能作废
                    if (Objects.equals(purchaseOutin.getState(), BaseOutinStateEnum.REVIEWED.getState())) {
                        ArgumentAssert.isTrue(Objects.equals(model.getState(), BaseOutinStateEnum.INVALID.getState()), "已审核的库存单, 只能作废");
                        // 查询订单详情
                        List<BaseOutinProduct> purchaseBaseOutinProductList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getDeleteFlag, 0)
                                .eq(BaseOutinProduct::getOutinId, purchaseOutin.getId()));
                        // 将数据减少
                        purchaseBaseOutinProductList.forEach(s -> {
                            // 直接数据取反
                            s.setNum(s.getNum());
                            s.setNumType(StringUtils.equals(s.getNumType(), "1") ? "2" : "1");
                        });
                        purchaseOutin.setState(model.getState());
                        purchaseOutin.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                        superManager.updateById(purchaseOutin);
                        List<BaseProductStock> stockListWithLock = baseProductStockManager.getStockListWithLock(purchaseBaseOutinProductList.stream().map(BaseOutinProduct::getProductId).distinct().collect(Collectors.toList()));
                        // 校验库存量
                        ArgumentAssert.isFalse(!baseProductStockManager.checkStock(purchaseOutin, purchaseBaseOutinProductList, stockListWithLock), "商品库存不足");
                        // 更新, 不更新类型和数量
                        List<BaseOutinProduct> updateVOList = BeanPlusUtil.toBeanList(purchaseBaseOutinProductList, BaseOutinProduct.class);
                        updateVOList.forEach(s -> {
                            s.setNum(null);
                            s.setNumType(null);
                            // baseOutinProduct 存在门店ID为空的情况, 强制赋值
                            if (Objects.isNull(s.getCreatedOrgId())) {
                                s.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                            }
                        });
                        ArgumentAssert.isFalse(!baseOutinProductManager.updateBatchById(updateVOList));
                        //回写商品库存现有量
                        ArgumentAssert.isFalse(!baseProductStockManager.rewriteStock(purchaseBaseOutinProductList, purchaseOutin, stockListWithLock), "库存现有量操作失败");
                        break;
                    }
                    // 红冲待审核的只能红冲, 或者红冲驳回
                    if (Objects.equals(purchaseOutin.getState(), BaseOutinStateEnum.REVERSAL_ENTRY_NO_REVIEWED.getState())) {
                        ArgumentAssert.isTrue(Objects.equals(model.getState(), BaseOutinStateEnum.INVALID.getState())
                                || Objects.equals(model.getState(), BaseOutinStateEnum.REVERSAL_ENTRY.getState()), "红冲待审核的红冲单，只能通过或者驳回");
                        if (Objects.equals(model.getState(), BaseOutinStateEnum.INVALID.getState())) {
                            // 红冲待审核的驳回，就是审核通过
                            purchaseOutin.setState(BaseOutinStateEnum.REVIEWED.getState());
                            purchaseOutin.setBillState(1);
                            superManager.updateById(purchaseOutin);
                            break;
                        }
                        if (Objects.equals(model.getState(), BaseOutinStateEnum.REVERSAL_ENTRY.getState())) {
                            // 新增一条记录，改为已红冲
                            purchaseOutin.setState(BaseOutinStateEnum.INVALID.getState());
                            purchaseOutin.setBillState(1);
                            superManager.updateById(purchaseOutin);
                            createdPurchaseInReversalEntry(purchaseOutin);
                        }
                    }

                    break;
                case PURCHASE_OUT:
                    BaseOutin purchaseOut = superManager.getById(model.getId());
                    ArgumentAssert.notNull(purchaseOut, "采购退货单不存在, 无需审核");
                    ArgumentAssert.isFalse(Objects.equals(purchaseOut.getState(), BaseOutinStateEnum.INVALID.getState()), "已经作废的采购退货单,不允许处理");
                    ArgumentAssert.isFalse(Objects.equals(purchaseOut.getState(), BaseOutinStateEnum.REVERSAL_ENTRY.getState()), "红冲已经审核通过的采购退货单,不允许处理");
                    purchaseOut.setIsLockNum(false);
                    // 待审核状态, 可以 已审核, 作废
                    if (Objects.equals(purchaseOut.getState(), BaseOutinStateEnum.NO_REVIEWED.getState())) {
                        // 作废
                        if (Objects.equals(model.getState(), BaseOutinStateEnum.INVALID.getState())) {
                            purchaseOut.setState(model.getState());
                            purchaseOut.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                            superManager.updateById(purchaseOut);
                            break;
                        }
                        // 通过
                        if (Objects.equals(model.getState(), BaseOutinStateEnum.REVIEWED.getState())) {
                            // 查询订单详情
                            List<BaseOutinProduct> purchaseBaseOutinProductList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getDeleteFlag, 0)
                                    .eq(BaseOutinProduct::getOutinId, purchaseOut.getId()));
                            purchaseOut.setState(model.getState());
                            purchaseOut.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                            superManager.updateById(purchaseOut);
                            // baseOutinProduct 存在门店ID为空的情况, 强制赋值
                            purchaseBaseOutinProductList.forEach(baseOutinProduct -> {
                                if (Objects.isNull(baseOutinProduct.getCreatedOrgId())) {
                                    baseOutinProduct.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                                }
                            });
                            List<BaseProductStock> stockListWithLock = baseProductStockManager.getStockListWithLock(purchaseBaseOutinProductList.stream().map(BaseOutinProduct::getProductId).distinct().collect(Collectors.toList()));
                            // 校验库存量
                            purchaseOut.setIsNum(true);
                            ArgumentAssert.isFalse(!baseProductStockManager.checkStock(purchaseOut, purchaseBaseOutinProductList, stockListWithLock), "商品库存不足");
                            // 更新
                            ArgumentAssert.isFalse(!baseOutinProductManager.updateBatchById(purchaseBaseOutinProductList));
                            //回写商品库存现有量
                            ArgumentAssert.isFalse(!baseProductStockManager.rewriteStock(purchaseBaseOutinProductList, purchaseOut, stockListWithLock), "库存现有量操作失败");
                            break;
                        }
                        ArgumentAssert.isTrue(false, "待审核的库存单, 只能审核通过和作废");
                    }

                    // 审核通过的只能作废
                    if (Objects.equals(purchaseOut.getState(), BaseOutinStateEnum.REVIEWED.getState())) {
                        ArgumentAssert.isTrue(Objects.equals(model.getState(), BaseOutinStateEnum.INVALID.getState()), "已审核的库存单, 只能作废");
                        // 查询订单详情
                        List<BaseOutinProduct> purchaseBaseOutinProductList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getDeleteFlag, 0)
                                .eq(BaseOutinProduct::getOutinId, purchaseOut.getId()));
                        // 将数据减少
                        purchaseBaseOutinProductList.forEach(s -> {
                            // 直接数据取反
                            s.setNum(s.getNum());
                            s.setNumType(StringUtils.equals(s.getNumType(), "1") ? "2" : "1");
                        });
                        purchaseOut.setState(model.getState());
                        purchaseOut.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                        superManager.updateById(purchaseOut);
                        purchaseOut.setIsLockNum(false);
                        purchaseOut.setIsNum(true);
                        List<BaseProductStock> stockListWithLock = baseProductStockManager.getStockListWithLock(purchaseBaseOutinProductList.stream().map(BaseOutinProduct::getProductId).distinct().collect(Collectors.toList()));
                        // 校验库存量
                        ArgumentAssert.isFalse(!baseProductStockManager.checkStock(purchaseOut, purchaseBaseOutinProductList, stockListWithLock), "商品库存不足");
                        // 更新, 不更新类型和数量
                        List<BaseOutinProduct> updateVOList = BeanPlusUtil.toBeanList(purchaseBaseOutinProductList, BaseOutinProduct.class);
                        updateVOList.forEach(s -> {
                            s.setNum(null);
                            s.setNumType(null);
                            // baseOutinProduct 存在门店ID为空的情况, 强制赋值
                            if (Objects.isNull(s.getCreatedOrgId())) {
                                s.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                            }
                        });
                        ArgumentAssert.isFalse(!baseOutinProductManager.updateBatchById(updateVOList));
                        //回写商品库存现有量
                        ArgumentAssert.isFalse(!baseProductStockManager.rewriteStock(purchaseBaseOutinProductList, purchaseOut, stockListWithLock), "库存现有量操作失败");
                    }

                    // 红冲待审核的只能红冲, 或者红冲驳回
                    if (Objects.equals(purchaseOut.getState(), BaseOutinStateEnum.REVERSAL_ENTRY_NO_REVIEWED.getState())) {
                        ArgumentAssert.isTrue(Objects.equals(model.getState(), BaseOutinStateEnum.INVALID.getState())
                                || Objects.equals(model.getState(), BaseOutinStateEnum.REVERSAL_ENTRY.getState()), "红冲待审核的红冲单，只能通过或者驳回");
                        if (Objects.equals(model.getState(), BaseOutinStateEnum.INVALID.getState())) {
                            // 红冲待审核的驳回，就是审核通过
                            purchaseOut.setBillState(1);
                            purchaseOut.setState(BaseOutinStateEnum.REVIEWED.getState());
                            superManager.updateById(purchaseOut);
                            break;
                        }
                        if (Objects.equals(model.getState(), BaseOutinStateEnum.REVERSAL_ENTRY.getState())) {
                            purchaseOut.setState(BaseOutinStateEnum.INVALID.getState());
                            purchaseOut.setBillState(1);
                            superManager.updateById(purchaseOut);
                            createdPurchaseOutReversalEntry(purchaseOut);
                        }
                    }

                    break;
                case INVENTORY_PROFIT_IN:
                case INVENTORY_LOSS_OUT:
                    BaseOutinStocktakingResultVO stocktakingDetail = baseOutinStocktakingService.getDetail(model.getId());
                    ArgumentAssert.notNull(stocktakingDetail, "订单不存在, 无需审核");
                    ArgumentAssert.isFalse(Objects.equals(stocktakingDetail.getState(), BaseOutinStateEnum.INVALID.getState()), "盘点库存单已经作废, 不允许更改");
                    // 待审核状态, 可以 已审核, 作废
                    if (Objects.equals(stocktakingDetail.getState(), BaseOutinStateEnum.NO_REVIEWED.getState())) {
                        // 作废
                        if (Objects.equals(model.getState(), BaseOutinStateEnum.INVALID.getState())) {
                            baseOutinStocktakingService.updateById(BaseOutinStocktakingUpdateVO.builder()
                                    .id(stocktakingDetail.getId())
                                    .state(model.getState())
                                    .build());
                            superManager.update(BaseOutin.builder().state(model.getState())
                                            .createdOrgId(ContextUtil.getCurrentCompanyId()).build(),
                                    Wraps.<BaseOutin>lbQ().eq(BaseOutin::getStocktakingId, stocktakingDetail.getId()));
                            break;
                        }
                        // 通过
                        if (Objects.equals(model.getState(), BaseOutinStateEnum.REVIEWED.getState())) {
                            List<BaseOutin> stocktakingBaseOutinList = superManager.list(Wraps.<BaseOutin>lbQ().eq(BaseOutin::getStocktakingId, stocktakingDetail.getId())
                                    .eq(BaseOutin::getDeleteFlag, 0));
                            // 查询订单详情
                            List<BaseOutinProduct> stocktakingBaseOutinProductList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getDeleteFlag, 0)
                                    .in(BaseOutinProduct::getOutinId, stocktakingBaseOutinList.stream().map(SuperEntity::getId).collect(Collectors.toList())));
                            Map<Long, List<BaseOutinProduct>> outinProductMap = stocktakingBaseOutinProductList.stream().collect(Collectors.groupingBy(BaseOutinProduct::getOutinId));
                            baseOutinStocktakingService.updateById(BaseOutinStocktakingUpdateVO.builder()
                                    .id(stocktakingDetail.getId())
                                    .state(model.getState())
                                    .build());
                            for (BaseOutin baseOutin : stocktakingBaseOutinList) {
                                baseOutin.setState(model.getState());
                                superManager.update(BaseOutin.builder().state(model.getState())
                                                .createdOrgId(ContextUtil.getCurrentCompanyId()).build(),
                                        Wraps.<BaseOutin>lbQ().eq(BaseOutin::getStocktakingId, stocktakingDetail.getId()));
                                baseOutin.setIsLockNum(false);
                                baseOutin.setIsNum(true);
                                List<BaseOutinProduct> baseOutinProductList = outinProductMap.get(baseOutin.getId());
                                // baseOutinProduct 存在门店ID为空的情况, 强制赋值
                                baseOutinProductList.forEach(s -> {
                                    if (Objects.isNull(s.getCreatedOrgId())) {
                                        s.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                                    }
                                });
                                List<BaseProductStock> stockListWithLock = baseProductStockManager.getStockListWithLock(baseOutinProductList.stream().map(BaseOutinProduct::getProductId).distinct().collect(Collectors.toList()));
                                // 校验库存量
                                ArgumentAssert.isFalse(!baseProductStockManager.checkStock(baseOutin, baseOutinProductList, stockListWithLock), "商品库存不足");
                                // 更新
                                ArgumentAssert.isFalse(!baseOutinProductManager.updateBatchById(baseOutinProductList));
                                //回写商品库存现有量
                                ArgumentAssert.isFalse(!baseProductStockManager.rewriteStock(baseOutinProductList, baseOutin, stockListWithLock), "库存现有量操作失败");
                            }
                            break;
                        }
                        ArgumentAssert.isTrue(false, "待审核的库存单, 只能审核通过和作废");
                    }
                    ArgumentAssert.isTrue(false, "已经审核通过盘点单不允许修改和审核");
                    break;
                case ADJUSTMENT_IN:
                case ADJUSTMENT_OUT:
                    BaseOutinAdjustmentResultVO adjustmentDetail = baseOutinAdjustmentService.getDetail(model.getId());
                    ArgumentAssert.notNull(adjustmentDetail, "订单不存在, 无需审核");
                    ArgumentAssert.isFalse(Objects.equals(adjustmentDetail.getState(), BaseOutinStateEnum.INVALID.getState()), "已经作废的调库单, 不允许更改");
                    // 待审核状态, 可以 已审核, 作废
                    if (Objects.equals(adjustmentDetail.getState(), BaseOutinStateEnum.NO_REVIEWED.getState())) {
                        // 作废
                        if (Objects.equals(model.getState(), BaseOutinStateEnum.INVALID.getState())) {
                            baseOutinAdjustmentService.updateById(BaseOutinAdjustmentUpdateVO.builder()
                                    .id(adjustmentDetail.getId())
                                    .state(model.getState())
                                    .build());
                            superManager.update(BaseOutin.builder().state(model.getState())
                                    .createdOrgId(ContextUtil.getCurrentCompanyId()).build(), Wraps.<BaseOutin>lbQ().eq(BaseOutin::getAdjustmentId, adjustmentDetail.getId()));
                            break;
                        }
                        // 通过
                        if (Objects.equals(model.getState(), BaseOutinStateEnum.REVIEWED.getState())) {
                            List<BaseOutin> adjustmentBaseOutinList = superManager.list(Wraps.<BaseOutin>lbQ().eq(BaseOutin::getAdjustmentId, adjustmentDetail.getId())
                                    .eq(BaseOutin::getDeleteFlag, 0));
                            // 查询订单详情
                            List<BaseOutinProduct> adjustmentBaseOutinProductList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getDeleteFlag, 0)
                                    .in(BaseOutinProduct::getOutinId, adjustmentBaseOutinList.stream().map(SuperEntity::getId).collect(Collectors.toList())));
                            Map<Long, List<BaseOutinProduct>> outinProductMap = adjustmentBaseOutinProductList.stream().collect(Collectors.groupingBy(BaseOutinProduct::getOutinId));
                            baseOutinAdjustmentService.updateById(BaseOutinAdjustmentUpdateVO.builder()
                                    .id(adjustmentDetail.getId())
                                    .state(model.getState())
                                    .build());
                            for (BaseOutin baseOutin : adjustmentBaseOutinList) {
                                baseOutin.setState(model.getState());
                                superManager.update(BaseOutin.builder().state(model.getState())
                                        .createdOrgId(ContextUtil.getCurrentCompanyId()).build(), Wraps.<BaseOutin>lbQ().eq(BaseOutin::getAdjustmentId, adjustmentDetail.getId()));
                                baseOutin.setIsLockNum(false);
                                baseOutin.setIsNum(true);
                                List<BaseOutinProduct> baseOutinProductList = outinProductMap.get(baseOutin.getId());
                                // baseOutinProduct 存在门店ID为空的情况, 强制赋值
                                baseOutinProductList.forEach(s -> {
                                    if (Objects.isNull(s.getCreatedOrgId())) {
                                        s.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                                    }
                                });
                                List<BaseProductStock> stockListWithLock = baseProductStockManager.getStockListWithLock(baseOutinProductList.stream().map(BaseOutinProduct::getProductId).distinct().collect(Collectors.toList()));
                                // 校验库存量
                                ArgumentAssert.isFalse(!baseProductStockManager.checkStock(baseOutin, baseOutinProductList, stockListWithLock), "商品库存不足");
                                ArgumentAssert.isFalse(!baseOutinProductManager.updateBatchById(baseOutinProductList));
                                //回写商品库存现有量
                                ArgumentAssert.isFalse(!baseProductStockManager.rewriteStock(baseOutinProductList, baseOutin, stockListWithLock), "库存现有量操作失败");
                            }
                            break;
                        }
                        ArgumentAssert.isTrue(false, "待审核的库存单, 只能审核通过和作废");
                    }

                    // 审核通过的只能作废
                    if (Objects.equals(adjustmentDetail.getState(), BaseOutinStateEnum.REVIEWED.getState())) {
                        ArgumentAssert.isTrue(Objects.equals(model.getState(), BaseOutinStateEnum.INVALID.getState()), "已审核的库存单, 只能作废");
                        List<BaseOutin> adjustmentBaseOutinList = superManager.list(Wraps.<BaseOutin>lbQ()
                                .eq(BaseOutin::getAdjustmentId, adjustmentDetail.getId()).eq(BaseOutin::getDeleteFlag, 0));
                        // 查询订单详情
                        List<BaseOutinProduct> adjustmentBaseOutinProductList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getDeleteFlag, 0)
                                .in(BaseOutinProduct::getOutinId, adjustmentBaseOutinList.stream().map(SuperEntity::getId).collect(Collectors.toList())));
                        // 将库存减少
                        adjustmentBaseOutinProductList.forEach(s -> {
                            // 直接数据取反
                            s.setNum(s.getNum());
                            s.setNumType(StringUtils.equals(s.getNumType(), "1") ? "2" : "1");
                        });
                        Map<Long, List<BaseOutinProduct>> outinProductMap = adjustmentBaseOutinProductList.stream()
                                .collect(Collectors.groupingBy(BaseOutinProduct::getOutinId));
                        baseOutinAdjustmentService.updateById(BaseOutinAdjustmentUpdateVO.builder()
                                .id(adjustmentDetail.getId())
                                .state(model.getState())
                                .build());
                        for (BaseOutin baseOutin : adjustmentBaseOutinList) {
                            baseOutin.setState(model.getState());
                            superManager.update(BaseOutin.builder().state(model.getState())
                                    .createdOrgId(ContextUtil.getCurrentCompanyId()).build(), Wraps.<BaseOutin>lbQ().eq(BaseOutin::getAdjustmentId, adjustmentDetail.getId()));
                            baseOutin.setIsLockNum(false);
                            baseOutin.setIsNum(true);
                            List<BaseProductStock> stockListWithLock = baseProductStockManager.getStockListWithLock(outinProductMap.get(baseOutin.getId()).stream().map(BaseOutinProduct::getProductId).distinct().collect(Collectors.toList()));
                            // 校验库存量
                            ArgumentAssert.isFalse(!baseProductStockManager.checkStock(baseOutin, outinProductMap.get(baseOutin.getId()), stockListWithLock), "商品库存不足");
                            List<BaseOutinProduct> updateVOList = BeanPlusUtil.toBeanList(outinProductMap.get(baseOutin.getId()), BaseOutinProduct.class);
                            updateVOList.forEach(s -> {
                                s.setNum(null);
                                s.setNumType(null);
                                // baseOutinProduct 存在门店ID为空的情况, 强制赋值
                                if (Objects.isNull(s.getCreatedOrgId())) {
                                    s.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                                }
                            });
                            ArgumentAssert.isFalse(!baseOutinProductManager.updateBatchById(updateVOList));
                            //回写商品库存现有量
                            ArgumentAssert.isFalse(!baseProductStockManager.rewriteStock(outinProductMap.get(baseOutin.getId()), baseOutin, stockListWithLock), "库存现有量操作失败");
                        }

                    }
                    break;
                case OTHER_IN:
                    BaseOutin otherIn = superManager.getById(model.getId());
                    ArgumentAssert.notNull(otherIn, "其他入库单不存在, 无需审核");
                    ArgumentAssert.isFalse(Objects.equals(otherIn.getState(), BaseOutinStateEnum.INVALID.getState()), "已经作废的其他入库单,不允许处理");
                    otherIn.setIsLockNum(false);
                    otherIn.setIsNum(true);
                    // 待审核状态, 可以 已审核, 作废
                    if (Objects.equals(otherIn.getState(), BaseOutinStateEnum.NO_REVIEWED.getState())) {
                        // 作废
                        if (Objects.equals(model.getState(), BaseOutinStateEnum.INVALID.getState())) {
                            otherIn.setState(model.getState());
                            otherIn.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                            superManager.updateById(otherIn);
                            break;
                        }
                        // 通过
                        if (Objects.equals(model.getState(), BaseOutinStateEnum.REVIEWED.getState())) {
                            // 查询订单详情
                            List<BaseOutinProduct> purchaseBaseOutinProductList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getDeleteFlag, 0)
                                    .eq(BaseOutinProduct::getOutinId, otherIn.getId()));
                            List<BaseProductStock> stockListWithLock = baseProductStockManager.getStockListWithLock(purchaseBaseOutinProductList.stream().map(BaseOutinProduct::getProductId).distinct().collect(Collectors.toList()));
                            otherIn.setState(model.getState());
                            otherIn.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                            superManager.updateById(otherIn);
                            // baseOutinProduct 存在门店ID为空的情况, 强制赋值
                            purchaseBaseOutinProductList.forEach(baseOutinProduct -> {
                                if (Objects.isNull(baseOutinProduct.getCreatedOrgId())) {
                                    baseOutinProduct.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                                }
                            });
                            // 校验库存量
                            ArgumentAssert.isFalse(!baseProductStockManager.checkStock(otherIn, purchaseBaseOutinProductList, stockListWithLock), "商品库存不足");
                            // 更新
                            ArgumentAssert.isFalse(!baseOutinProductManager.updateBatchById(purchaseBaseOutinProductList));
                            //回写商品库存现有量
                            ArgumentAssert.isFalse(!baseProductStockManager.rewriteStock(purchaseBaseOutinProductList, otherIn, stockListWithLock), "库存现有量操作失败");
                            break;
                        }
                        ArgumentAssert.isTrue(false, "待审核的库存单, 只能审核通过和作废");
                    }

                    // 审核通过的只能作废
                    if (Objects.equals(otherIn.getState(), BaseOutinStateEnum.REVIEWED.getState())) {
                        ArgumentAssert.isTrue(Objects.equals(model.getState(), BaseOutinStateEnum.INVALID.getState()), "已审核的库存单, 只能作废");
                        // 查询订单详情
                        List<BaseOutinProduct> purchaseBaseOutinProductList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getDeleteFlag, 0)
                                .eq(BaseOutinProduct::getOutinId, otherIn.getId()));
                        // 将数据减少
                        purchaseBaseOutinProductList.forEach(s -> {
                            // 直接数据取反
                            s.setNum(s.getNum());
                            s.setNumType(StringUtils.equals(s.getNumType(), "1") ? "2" : "1");
                        });
                        otherIn.setState(model.getState());
                        otherIn.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                        superManager.updateById(otherIn);
                        List<BaseProductStock> stockListWithLock = baseProductStockManager.getStockListWithLock(purchaseBaseOutinProductList.stream().map(BaseOutinProduct::getProductId).distinct().collect(Collectors.toList()));
                        // 校验库存量
                        ArgumentAssert.isFalse(!baseProductStockManager.checkStock(otherIn, purchaseBaseOutinProductList, stockListWithLock), "商品库存不足");
                        // 更新, 不更新类型和数量
                        List<BaseOutinProduct> updateVOList = BeanPlusUtil.toBeanList(purchaseBaseOutinProductList, BaseOutinProduct.class);
                        updateVOList.forEach(s -> {
                            s.setNum(null);
                            s.setNumType(null);
                            // baseOutinProduct 存在门店ID为空的情况, 强制赋值
                            if (Objects.isNull(s.getCreatedOrgId())) {
                                s.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                            }
                        });
                        ArgumentAssert.isFalse(!baseOutinProductManager.updateBatchById(updateVOList));
                        //回写商品库存现有量
                        ArgumentAssert.isFalse(!baseProductStockManager.rewriteStock(purchaseBaseOutinProductList, otherIn, stockListWithLock), "库存现有量操作失败");
                    }
                    break;
                case OTHER_OUT:
                    BaseOutin otherOut = superManager.getById(model.getId());
                    ArgumentAssert.notNull(otherOut, "其他出库单不存在, 无需审核");
                    ArgumentAssert.isFalse(Objects.equals(otherOut.getState(), BaseOutinStateEnum.INVALID.getState()), "已经作废的其他出库单,不允许处理");
                    otherOut.setIsLockNum(false);
                    // 待审核状态, 可以 已审核, 作废
                    if (Objects.equals(otherOut.getState(), BaseOutinStateEnum.NO_REVIEWED.getState())) {
                        // 作废
                        if (Objects.equals(model.getState(), BaseOutinStateEnum.INVALID.getState())) {
                            otherOut.setState(model.getState());
                            otherOut.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                            superManager.updateById(otherOut);
                            break;
                        }
                        // 通过
                        if (Objects.equals(model.getState(), BaseOutinStateEnum.REVIEWED.getState())) {
                            // 查询订单详情
                            List<BaseOutinProduct> purchaseBaseOutinProductList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getDeleteFlag, 0)
                                    .eq(BaseOutinProduct::getOutinId, otherOut.getId()));
                            otherOut.setState(model.getState());
                            otherOut.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                            superManager.updateById(otherOut);
                            // baseOutinProduct 存在门店ID为空的情况, 强制赋值
                            purchaseBaseOutinProductList.forEach(baseOutinProduct -> {
                                if (Objects.isNull(baseOutinProduct.getCreatedOrgId())) {
                                    baseOutinProduct.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                                }
                            });
                            List<BaseProductStock> stockListWithLock = baseProductStockManager.getStockListWithLock(purchaseBaseOutinProductList.stream().map(BaseOutinProduct::getProductId).distinct().collect(Collectors.toList()));
                            // 校验库存量
                            otherOut.setIsNum(true);
                            ArgumentAssert.isFalse(!baseProductStockManager.checkStock(otherOut, purchaseBaseOutinProductList, stockListWithLock), "商品库存不足");
                            // 更新
                            ArgumentAssert.isFalse(!baseOutinProductManager.updateBatchById(purchaseBaseOutinProductList));
                            //回写商品库存现有量
                            ArgumentAssert.isFalse(!baseProductStockManager.rewriteStock(purchaseBaseOutinProductList, otherOut, stockListWithLock), "库存现有量操作失败");
                            break;
                        }
                        ArgumentAssert.isTrue(false, "待审核的库存单, 只能审核通过和作废");
                    }

                    // 审核通过的只能作废
                    if (Objects.equals(otherOut.getState(), BaseOutinStateEnum.REVIEWED.getState())) {
                        ArgumentAssert.isTrue(Objects.equals(model.getState(), BaseOutinStateEnum.INVALID.getState()), "已审核的库存单, 只能作废");
                        // 查询订单详情
                        List<BaseOutinProduct> purchaseBaseOutinProductList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getDeleteFlag, 0)
                                .eq(BaseOutinProduct::getOutinId, otherOut.getId()));
                        // 将数据减少
                        purchaseBaseOutinProductList.forEach(s -> {
                            // 直接数据取反
                            s.setNum(s.getNum());
                            s.setNumType(StringUtils.equals(s.getNumType(), "1") ? "2" : "1");
                        });
                        otherOut.setState(model.getState());
                        otherOut.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                        superManager.updateById(otherOut);
                        otherOut.setIsLockNum(false);
                        otherOut.setIsNum(true);
                        List<BaseProductStock> stockListWithLock = baseProductStockManager.getStockListWithLock(purchaseBaseOutinProductList.stream().map(BaseOutinProduct::getProductId).distinct().collect(Collectors.toList()));
                        // 校验库存量
                        ArgumentAssert.isFalse(!baseProductStockManager.checkStock(otherOut, purchaseBaseOutinProductList, stockListWithLock), "商品库存不足");
                        // 更新, 不更新类型和数量
                        List<BaseOutinProduct> updateVOList = BeanPlusUtil.toBeanList(purchaseBaseOutinProductList, BaseOutinProduct.class);
                        updateVOList.forEach(s -> {
                            s.setNum(null);
                            s.setNumType(null);
                            // baseOutinProduct 存在门店ID为空的情况, 强制赋值
                            if (Objects.isNull(s.getCreatedOrgId())) {
                                s.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                            }
                        });
                        ArgumentAssert.isFalse(!baseOutinProductManager.updateBatchById(updateVOList));
                        //回写商品库存现有量
                        ArgumentAssert.isFalse(!baseProductStockManager.rewriteStock(purchaseBaseOutinProductList, otherOut, stockListWithLock), "库存现有量操作失败");
                    }
                    break;
                default:
                    ArgumentAssert.isTrue(false, "当前库存订单,无需审核");
            }

            // 保存审核记录
            baseOutinApprovalService.save(BaseOutinApprovalSaveVO.builder()
                    .bizId(model.getId())
                    .type(model.getType())
                    .state(model.getState())
                    .remarks(model.getRemarks())
                    .employeeId(ContextUtil.getEmployeeId())
                    .createdOrgId(ContextUtil.getCurrentCompanyId())
                    .build());
        } finally {
            if (lock) {
                distributedLock.releaseLock("OUTIN_STATE:" + model.getId());
            }
        }
        return true;
    }

    /**
     * 采购入库红冲单
     *
     * @param purchaseOut
     */
    private void createdPurchaseOutReversalEntry(BaseOutin purchaseOut) {
        List<BaseOutinProduct> outinProductList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getOutinId, purchaseOut.getId()));
        purchaseOut.setCode(getCode());
        purchaseOut.setType(OutinTypeEnum.PURCHASE_OUT_REVERSAL_ENTRY.getCode());
        purchaseOut.setState(BaseOutinStateEnum.REVERSAL_ENTRY.getState());
        purchaseOut.setBillState(0);
        purchaseOut.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        purchaseOut.setEmployeeId(ContextUtil.getEmployeeId());
        purchaseOut.setOrgId(ContextUtil.getCurrentCompanyId());
        purchaseOut.setSourceId(purchaseOut.getId());
        purchaseOut.setCreatedBy(null);
        purchaseOut.setCreatedTime(null);
        purchaseOut.setUpdatedBy(null);
        purchaseOut.setUpdatedTime(null);
        purchaseOut.setId(null);
        List<BaseProductStock> stockListWithLock = baseProductStockManager.getStockListWithLock(outinProductList.stream().map(BaseOutinProduct::getProductId).distinct().collect(Collectors.toList()));
        //检查库存现有量
        ArgumentAssert.isFalse(!baseProductStockManager.checkStock(purchaseOut, outinProductList, stockListWithLock), "商品库存不足");
        //新增商品库存主表
        ArgumentAssert.isFalse(!superManager.save(purchaseOut), "库存新增失败");
        //商品明细信息
        baseOutinProductManager.remove(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getOutinId, purchaseOut.getId()));
        outinProductList.forEach(baseOutinProduct -> {
            baseOutinProduct.setSourceId(baseOutinProduct.getId());
            baseOutinProduct.setOutinId(purchaseOut.getId());
            baseOutinProduct.setEmployeeId(ContextUtil.getEmployeeId());
            baseOutinProduct.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
            baseOutinProduct.setNumType("1");
            baseOutinProduct.setId(null);
            baseOutinProduct.setCreatedBy(null);
            baseOutinProduct.setCreatedTime(null);
            baseOutinProduct.setUpdatedBy(null);
            baseOutinProduct.setUpdatedTime(null);
        });
        //回写商品库存现有量
        ArgumentAssert.isFalse(!baseProductStockManager.rewriteStock(outinProductList, purchaseOut, stockListWithLock), "库存现有量操作失败");
        ArgumentAssert.isFalse(!baseOutinProductManager.saveBatch(outinProductList), "库存明细操作失败");
    }

    /**
     * 创建采购入库单红冲单
     */
    public void createdPurchaseInReversalEntry(BaseOutin purchaseOutin) {
        List<BaseOutinProduct> outinProductList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getOutinId, purchaseOutin.getId()));
        purchaseOutin.setCode(getCode());
        purchaseOutin.setType(OutinTypeEnum.PURCHASE_IN_REVERSAL_ENTRY.getCode());
        purchaseOutin.setState(BaseOutinStateEnum.REVERSAL_ENTRY.getState());
        purchaseOutin.setBillState(1);
        purchaseOutin.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        purchaseOutin.setEmployeeId(ContextUtil.getEmployeeId());
        purchaseOutin.setOrgId(ContextUtil.getCurrentCompanyId());
        purchaseOutin.setSourceId(purchaseOutin.getId());
        purchaseOutin.setCreatedBy(null);
        purchaseOutin.setCreatedTime(null);
        purchaseOutin.setUpdatedBy(null);
        purchaseOutin.setUpdatedTime(null);
        purchaseOutin.setId(null);
        List<BaseProductStock> stockListWithLock = baseProductStockManager.getStockListWithLock(outinProductList.stream().map(BaseOutinProduct::getProductId).distinct().collect(Collectors.toList()));
        //检查库存现有量
        ArgumentAssert.isFalse(!baseProductStockManager.checkStock(purchaseOutin, outinProductList, stockListWithLock), "商品库存不足");
        //新增商品库存主表
        ArgumentAssert.isFalse(!superManager.save(purchaseOutin), "库存新增失败");
        //商品明细信息
        baseOutinProductManager.remove(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getOutinId, purchaseOutin.getId()));
        outinProductList.forEach(baseOutinProduct -> {
            baseOutinProduct.setSourceId(baseOutinProduct.getId());
            baseOutinProduct.setOutinId(purchaseOutin.getId());
            baseOutinProduct.setEmployeeId(ContextUtil.getEmployeeId());
            baseOutinProduct.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
            baseOutinProduct.setNumType("2");
            baseOutinProduct.setId(null);
            baseOutinProduct.setCreatedBy(null);
            baseOutinProduct.setCreatedTime(null);
            baseOutinProduct.setUpdatedBy(null);
            baseOutinProduct.setUpdatedTime(null);
        });
        //回写商品库存现有量
        ArgumentAssert.isFalse(!baseProductStockManager.rewriteStock(outinProductList, purchaseOutin, stockListWithLock), "库存现有量操作失败");
        ArgumentAssert.isFalse(!baseOutinProductManager.saveBatch(outinProductList), "库存明细操作失败");
    }

    @Override
    public IPage<BaseOutinItemResultVO> pageList(PageParams<BaseOutinPageQuery> params) {
        params.setSort(null);
        params.setOrder(null);
        BaseOutinPageQuery model = params.getModel();
        model.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        if (!model.getStartDate().contains(":")) {
            model.setStartDate(model.getStartDate() + " 00:00:00");
        }
        if (!model.getEndDate().contains(":")) {
            model.setEndDate(model.getEndDate() + " 23:59:59");
        }
        IPage<BaseOutinItemResultVO> page = baseOutinMapper.pageList(params.buildPage(BaseOutinItemResultVO.class), model);
        // 1. 收集所有业务单据ID
        List<Long> ids = page.getRecords().stream()
                .map(BaseOutinItemResultVO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        if (!ids.isEmpty()) {
            // 2. 批量查所有相关的审核记录
            List<BaseOutinApproval> approvalList = baseOutinApprovalService.list(Wraps.<BaseOutinApproval>lbQ()
                    .in(BaseOutinApproval::getBizId, ids));
            // 3. 转VO
            List<BaseOutinApprovalResultVO> approvalVOList = BeanPlusUtil.toBeanList(approvalList, BaseOutinApprovalResultVO.class);
            // echoService.action(approvalVOList);
            // 4. 按biz_id分组
            Map<Long, List<BaseOutinApprovalResultVO>> approvalMap = approvalVOList.stream()
                    .collect(Collectors.groupingBy(BaseOutinApprovalResultVO::getBizId));
            // 5. 组装到VO
            for (BaseOutinItemResultVO vo : page.getRecords()) {
                List<BaseOutinApprovalResultVO> voList = approvalMap.get(vo.getId());
                vo.setApprovalList(voList);
                if (voList != null) {
                    voList.stream().findFirst().ifPresent(approved -> vo.setApprovalTime(approved.getCreatedTime()));
                    voList.stream().findFirst().ifPresent(approved -> vo.setApprovalEmployeeId(approved.getEmployeeId()));
                }
            }
        }
        echoService.action(page.getRecords());
        return page;
    }

    @Override
    public BaseOutinResultVO getDetail(Long aLong) {
        BaseOutin byId = superManager.getById(aLong);
        if (byId == null) {
            return null;
        }
        BaseOutinResultVO baseOutinResultVO = BeanUtil.copyProperties(byId, BaseOutinResultVO.class);
        List<BaseOutinProductResultVO> list = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getOutinId, byId.getId()).orderByAsc(SuperEntity::getCreatedTime))
                .stream().map(v -> BeanUtil.copyProperties(v, BaseOutinProductResultVO.class)).collect(Collectors.toList());
        //获取商品信息
        List<Long> productIds = list.stream().map(BaseOutinProductResultVO::getProductId).collect(Collectors.toList());
        Map<Long, BaseProductResultVO> productMap = CollUtil.isNotEmpty(productIds) ? baseProductManager.list(Wraps.<BaseProduct>lbQ().in(BaseProduct::getId, productIds))
                .stream().map(v -> BeanUtil.copyProperties(v, BaseProductResultVO.class)).collect(Collectors.toList())
                .stream().collect(Collectors.toMap(BaseProductResultVO::getId, k -> k)) : MapUtil.newHashMap();
        for (BaseOutinProductResultVO baseOutinProductResultVO : list) {
            BaseProductResultVO productResultVO = productMap.get(baseOutinProductResultVO.getProductId());
            echoService.action(productResultVO);
            if (ObjectUtil.isNotNull(productResultVO)) {
                Map<String, Object> echoMap = baseOutinProductResultVO.getEchoMap();
                echoMap.put("categoryName", productResultVO.getEchoMap().get("categoryId"));
                echoMap.put("categoryId", productResultVO.getEchoMap().get("categoryId"));
                echoMap.put("name", productResultVO.getName());
                echoMap.put("spec", productResultVO.getSpec());
                echoMap.put("code", productResultVO.getCode());
                echoMap.put("measuringUnit", productResultVO.getEchoMap().get("measuringUnit"));
            }
            if (baseOutinProductResultVO.getCostPrice() == null) {
                baseOutinProductResultVO.setCostPrice(BigDecimal.ZERO);
            }
            baseOutinProductResultVO.setTotalCostPrice(baseOutinProductResultVO.getCostPrice()
                    .multiply(BigDecimal.valueOf(baseOutinProductResultVO.getNum() == null ? 0 : baseOutinProductResultVO.getNum())).setScale(2, RoundingMode.HALF_UP));
            if (Objects.nonNull(baseOutinProductResultVO.getResidueNum()) && Objects.nonNull(baseOutinProductResultVO.getNum())) {
                if (StrUtil.isNotBlank(baseOutinProductResultVO.getNumType())
                        && "1".equals(baseOutinProductResultVO.getNumType())) {
                    baseOutinProductResultVO.setOldStock(baseOutinProductResultVO.getResidueNum()
                            - baseOutinProductResultVO.getNum());
                } else {
                    baseOutinProductResultVO.setOldStock(baseOutinProductResultVO.getResidueNum()
                            + baseOutinProductResultVO.getNum());
                }
            }
        }
        baseOutinResultVO.setOutinProductList(list);

        // 审核列表
        List<BaseOutinApprovalResultVO> approvalList = baseOutinApprovalService.list(Wraps.<BaseOutinApproval>lbQ()
                        .eq(BaseOutinApproval::getBizId, byId.getId())
                        .eq(BaseOutinApproval::getType, byId.getType()))
                .stream().map(v -> BeanUtil.copyProperties(v, BaseOutinApprovalResultVO.class)).collect(Collectors.toList());
        baseOutinResultVO.setApprovalList(approvalList);

        // 总成本价
        if (CollUtil.isNotEmpty(list)) {
            BigDecimal totalCostPrice = list.stream().map(v -> v.getCostPrice().multiply(BigDecimal.valueOf(v.getNum()))).reduce(BigDecimal.ZERO, BigDecimal::add);
            baseOutinResultVO.setTotalCostPrice(totalCostPrice.setScale(2, RoundingMode.HALF_UP));
        }
        echoService.action(baseOutinResultVO);
        Long updatedBy = baseOutinResultVO.getUpdatedBy();
        if (Objects.nonNull(updatedBy)) {
            BaseEmployee baseEmployee = baseEmployeeService.getOne(Wraps.<BaseEmployee>lbQ().eq(BaseEmployee::getUserId, updatedBy).inSql(BaseEmployee::getId, "select distinct employee_id from base_employee_org_rel where  delete_flag = 0 and org_id =" + ContextUtil.getCurrentCompanyId()).last(" limit 1"));
            if (Objects.nonNull(baseEmployee)) {
                if (CollUtil.isEmpty(baseOutinResultVO.getEchoMap())) {
                    baseOutinResultVO.setEchoMap(MapUtil.newHashMap());
                }
                baseOutinResultVO.getEchoMap().put("updatedBy", baseEmployee.getRealName());
            }
        }
        return baseOutinResultVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseOutinStocktaking stocktaking(BaseOutinStocktakingVO model) {
        ArgumentAssert.notEmpty(model.getOutinProductList(), "请选择商品");
        List<Long> productIds = model.getOutinProductList().stream()
                .map(BaseOutinProductSaveVO::getProductId).collect(Collectors.toList());
        Map<Long, BaseProductStock> productStockMap = baseProductStockManager.list(Wraps.<BaseProductStock>lbQ()
                        .in(BaseProductStock::getProductId, productIds)
                        .eq(BaseProductStock::getWarehouseId, model.getWarehouseId()))
                .stream().collect(Collectors.toMap(BaseProductStock::getProductId, Function.identity()));
        Map<Long, BaseProduct> baseProductMap = baseProductManager.list(Wraps.<BaseProduct>lbQ()
                        .in(BaseProduct::getId, productIds))
                .stream().collect(Collectors.toMap(BaseProduct::getId, Function.identity()));

        // 判断有没有进行中的盘点单, 如果存在,不允许新增
        List<BaseOutinStocktaking> stocktakingList = baseOutinStocktakingService.list(Wraps.<BaseOutinStocktaking>lbQ()
                .eq(BaseOutinStocktaking::getDeleteFlag, 0)
                .eq(BaseOutinStocktaking::getWarehouseId, model.getWarehouseId())
                .eq(BaseOutinStocktaking::getState, BaseOutinStateEnum.NO_REVIEWED.getState()));
        ArgumentAssert.isTrue(CollUtil.isEmpty(stocktakingList), "存在进行中的盘点单,不允许再次盘点");

        BaseWarehouse warehouse = baseWarehouseService.getOneBySn();

        //新增盘点单
        BaseOutinStocktaking stocktaking = baseOutinStocktakingService
                .save(BaseOutinStocktakingSaveVO.builder()
                        .code(getCode()).billDate(LocalDate.now())
                        .billState(1).warehouseId(Objects.nonNull(model.getWarehouseId()) ? model.getWarehouseId() : warehouse.getId())
                        .orgId(ContextUtil.getCurrentCompanyId()).createdOrgId(ContextUtil.getCurrentCompanyId())
                        .employeeId(ContextUtil.getEmployeeId()).deleteFlag(0).remarks(model.getRemarks())
                        .state(model.getState())
                        .sourceType(model.getSourceType()).build());
        //新增盘点明细
        List<BaseOutinProductSaveVO> outProductSaveVOList = Lists.newArrayList();
        List<BaseOutinProductSaveVO> inProductSaveVOList = Lists.newArrayList();
        for (BaseOutinProductSaveVO outinProductSaveVO : model.getOutinProductList()) {
            BaseProductStock baseProductStock = productStockMap.get(outinProductSaveVO.getProductId());
            // 如果库存记录为空, 则新增一条
            if (ObjectUtil.isNull(baseProductStock)) {
                BaseProduct baseProduct = baseProductMap.get(outinProductSaveVO.getProductId());
                ArgumentAssert.notNull(baseProduct, "商品不存在，请联系管理员");
                //初始化入库记录 商品库存表的价格都是一样的, 直接取一个
                BaseProductStock productStock = baseProductStockManager.getOne(Wraps.<BaseProductStock>lbQ()
                        .eq(BaseProductStock::getProductId, baseProduct.getId())
                        .eq(SuperEntity::getDeleteFlag, 0).last(" limit 1"));
                baseProductStock = BaseProductStock.builder()
                        .productId(baseProduct.getId())
                        .warehouseId(Objects.nonNull(model.getWarehouseId()) ? model.getWarehouseId() : warehouse.getId())
                        .num(0)
                        .createdOrgId(ContextUtil.getCurrentCompanyId())
                        .lockNum(0)
                        .storageNum(0)
                        .outNum(0)
                        .costPrice(productStock.getCostPrice())
                        .build();
                baseProductStockManager.save(baseProductStock);
            }
            ArgumentAssert.notNull(baseProductStock, "库存异常，请联系管理员");
            outinProductSaveVO.setIsGift(false);
            outinProductSaveVO.setStocktakingId(stocktaking.getId());
            outinProductSaveVO.setStocktakingValue(outinProductSaveVO.getNum());
            outinProductSaveVO.setDesc("盘点");
            outinProductSaveVO.setRemarks("盘点");
            outinProductSaveVO.setAddTime(LocalDateTime.now());
            outinProductSaveVO.setWarehouseId(Objects.nonNull(model.getWarehouseId()) ? model.getWarehouseId() : warehouse.getId());
            //补充的数量
            int num = outinProductSaveVO.getNum();
            if ((baseProductStock.getNum() - baseProductStock.getLockNum()) - num > 0) {
                //盘点出库
                outinProductSaveVO.setNum((baseProductStock.getNum() - baseProductStock.getLockNum()) - num);
                outinProductSaveVO.setNumType("2");
                outinProductSaveVO.setAddTime(LocalDateTime.now().plusSeconds(1));
                outProductSaveVOList.add(outinProductSaveVO);

            } else {
                //盘点入库
                outinProductSaveVO.setNum(num - (baseProductStock.getNum() - baseProductStock.getLockNum()));
                outinProductSaveVO.setNumType("1");
                inProductSaveVOList.add(outinProductSaveVO);
            }
        }
        //盘点入库
        if (CollUtil.isNotEmpty(inProductSaveVOList)) {
            BaseOutinStockSaveVO saveVO = BaseOutinStockSaveVO.builder()
                    .type(OutinTypeEnum.INVENTORY_PROFIT_IN.getCode())
                    .sourceType(model.getSourceType())
                    .warehouseId(Objects.nonNull(model.getWarehouseId()) ? model.getWarehouseId() : warehouse.getId()).billState(1)
                    .billDate(LocalDate.now())
                    .orgId(ContextUtil.getCurrentCompanyId())
                    .remarks(StrUtil.isBlank(model.getRemarks()) ? "盘点" : model.getRemarks())
                    .employeeId(ContextUtil.getEmployeeId()).isLockNum(false)
                    .isNum(true)
                    .state(model.getState())
                    .outinProductList(inProductSaveVOList)
                    .stocktakingId(stocktaking.getId())
                    .build();
            this.saveStock(saveVO);
        }
        //盘点出库
        if (CollUtil.isNotEmpty(outProductSaveVOList)) {
            BaseOutinStockSaveVO saveVO = BaseOutinStockSaveVO.builder()
                    .type(OutinTypeEnum.INVENTORY_LOSS_OUT.getCode())
                    .sourceType(model.getSourceType())
                    .warehouseId(Objects.nonNull(model.getWarehouseId()) ? model.getWarehouseId() : warehouse.getId()).billState(1)
                    .billDate(LocalDate.now())
                    .orgId(ContextUtil.getCurrentCompanyId())
                    .remarks(StrUtil.isBlank(model.getRemarks()) ? "盘点" : model.getRemarks())
                    .employeeId(ContextUtil.getEmployeeId())
                    .isNum(true)
                    .isLockNum(false)
                    .state(model.getState())
                    .outinProductList(outProductSaveVOList)
                    .stocktakingId(stocktaking.getId())
                    .build();
            this.saveStock(saveVO);
        }
        return stocktaking;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseOutinStocktaking updateStocktaking(BaseOutinStocktakingVO model) {
        ArgumentAssert.notEmpty(model.getOutinProductList(), "请选择商品");
        List<Long> productIds = model.getOutinProductList().stream()
                .map(BaseOutinProductSaveVO::getProductId).collect(Collectors.toList());
        Map<Long, BaseProductStock> productStockMap = baseProductStockManager.list(Wraps.<BaseProductStock>lbQ()
                        .in(BaseProductStock::getProductId, productIds)
                        .eq(BaseProductStock::getWarehouseId, model.getWarehouseId()))
                .stream().collect(Collectors.toMap(BaseProductStock::getProductId, Function.identity()));
        Map<Long, BaseProduct> baseProductMap = baseProductManager.list(Wraps.<BaseProduct>lbQ()
                        .in(BaseProduct::getId, productIds))
                .stream().collect(Collectors.toMap(BaseProduct::getId, Function.identity()));
        BaseOutinStocktakingUpdateVO updateVO = BeanPlusUtil.copyProperties(model, BaseOutinStocktakingUpdateVO.class);

        BaseOutinStocktaking outinStocktaking = baseOutinStocktakingService.getById(updateVO.getId());

        ArgumentAssert.isTrue(Objects.equals(outinStocktaking.getState(), 0), "只有待审核的才允许修改");

        //更新
        BaseOutinStocktaking stocktaking = baseOutinStocktakingService.updateById(updateVO);

        // 删除之前的盘点详情
        superManager.remove(Wraps.<BaseOutin>lbQ().eq(BaseOutin::getStocktakingId, updateVO.getId()));
        baseOutinProductManager.remove(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getStocktakingId, updateVO.getId()));

        //新增盘点明细
        List<BaseOutinProductSaveVO> outProductSaveVOList = Lists.newArrayList();
        List<BaseOutinProductSaveVO> inProductSaveVOList = Lists.newArrayList();
        for (BaseOutinProductSaveVO outinProductSaveVO : model.getOutinProductList()) {
            BaseProductStock baseProductStock = productStockMap.get(outinProductSaveVO.getProductId());
            // 如果库存记录为空, 则新增一条
            if (ObjectUtil.isNull(baseProductStock)) {
                BaseProduct baseProduct = baseProductMap.get(outinProductSaveVO.getProductId());
                ArgumentAssert.notNull(baseProduct, "商品不存在，请联系管理员");
                //初始化入库记录 商品库存表的价格都是一样的, 直接取一个
                BaseProductStock productStock = baseProductStockManager.getOne(Wraps.<BaseProductStock>lbQ()
                        .eq(BaseProductStock::getProductId, baseProduct.getId())
                        .eq(SuperEntity::getDeleteFlag, 0).last(" limit 1"));
                baseProductStock = BaseProductStock.builder()
                        .productId(baseProduct.getId())
                        .warehouseId(Objects.nonNull(model.getWarehouseId()) ? model.getWarehouseId() : stocktaking.getWarehouseId())
                        .num(0)
                        .createdOrgId(ContextUtil.getCurrentCompanyId())
                        .lockNum(0)
                        .storageNum(0)
                        .outNum(0)
                        .costPrice(productStock.getCostPrice())
                        .build();
                baseProductStockManager.save(baseProductStock);
            }
            ArgumentAssert.notNull(baseProductStock, "库存异常，请联系管理员");
            outinProductSaveVO.setIsGift(false);
            outinProductSaveVO.setStocktakingId(updateVO.getId());
            outinProductSaveVO.setStocktakingValue(outinProductSaveVO.getNum());
            outinProductSaveVO.setDesc("盘点");
            outinProductSaveVO.setRemarks("盘点");
            outinProductSaveVO.setAddTime(LocalDateTime.now());
            if (Objects.nonNull(model.getWarehouseId())) {
                outinProductSaveVO.setWarehouseId(model.getWarehouseId());
            } else {
                outinProductSaveVO.setWarehouseId(stocktaking.getWarehouseId());
            }

            //补充的数量
            int num = outinProductSaveVO.getNum();
            if ((baseProductStock.getNum() - baseProductStock.getLockNum()) - num > 0) {
                //盘点出库
                outinProductSaveVO.setNum((baseProductStock.getNum() - baseProductStock.getLockNum()) - num);
                outinProductSaveVO.setNumType("2");
                outinProductSaveVO.setAddTime(LocalDateTime.now().plusSeconds(1));
                outProductSaveVOList.add(outinProductSaveVO);

            } else {
                //盘点入库
                outinProductSaveVO.setNum(num - (baseProductStock.getNum() - baseProductStock.getLockNum()));
                outinProductSaveVO.setNumType("1");
                inProductSaveVOList.add(outinProductSaveVO);
            }
        }
        //盘点入库
        if (CollUtil.isNotEmpty(inProductSaveVOList)) {
            BaseOutinStockSaveVO saveVO = BaseOutinStockSaveVO.builder()
                    .type(OutinTypeEnum.INVENTORY_PROFIT_IN.getCode())
                    .sourceType(model.getSourceType())
                    .warehouseId(Objects.nonNull(model.getWarehouseId()) ? model.getWarehouseId() : stocktaking.getWarehouseId()).billState(1)
                    .billDate(LocalDate.now())
                    .orgId(ContextUtil.getCurrentCompanyId())
                    .remarks(StrUtil.isBlank(model.getRemarks()) ? "盘点" : model.getRemarks())
                    .employeeId(ContextUtil.getEmployeeId()).isLockNum(false)
                    .isNum(true)
                    .state(model.getState())
                    .outinProductList(inProductSaveVOList)
                    .stocktakingId(stocktaking.getId())
                    .build();
            this.saveStock(saveVO);
        }
        //盘点出库
        if (CollUtil.isNotEmpty(outProductSaveVOList)) {
            BaseOutinStockSaveVO saveVO = BaseOutinStockSaveVO.builder()
                    .type(OutinTypeEnum.INVENTORY_LOSS_OUT.getCode())
                    .sourceType(model.getSourceType())
                    .warehouseId(Objects.nonNull(model.getWarehouseId()) ? model.getWarehouseId() : stocktaking.getWarehouseId()).billState(1)
                    .billDate(LocalDate.now())
                    .orgId(ContextUtil.getCurrentCompanyId())
                    .remarks(StrUtil.isBlank(model.getRemarks()) ? "盘点" : model.getRemarks())
                    .employeeId(ContextUtil.getEmployeeId())
                    .isNum(true)
                    .isLockNum(false)
                    .state(model.getState())
                    .outinProductList(outProductSaveVOList)
                    .stocktakingId(stocktaking.getId())
                    .build();
            this.saveStock(saveVO);
        }
        return stocktaking;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseOutin saveStock(BaseOutinStockSaveVO model) {
        BaseOutin baseOutin = null;
        //是否为订单信息
        if (ObjectUtil.isNull(model.getBillState())) {
            //完成
            model.setBillState(1);
        }
        // 状态为空的话, 默认审核通过
        if (ObjectUtil.isNull(model.getState())) {
            //完成
            model.setState(1);
        }

        if (Objects.isNull(model.getWarehouseId())) {
            log.warn("未设置仓库,使用默认仓库");
            model.setWarehouseId(baseWarehouseService.getOneBySn().getId());
        }

        if (StrUtil.isNotBlank(model.getCode())) {
            baseOutin = superManager.getOne(Wraps.<BaseOutin>lbQ()
                    .eq(BaseOutin::getType, model.getType())
                    .eq(BaseOutin::getDeleteFlag, 0)
                    .eq(!ObjectUtil.equal(model.getType(), OutinTypeEnum.SELL_OUT.getCode()),
                            BaseOutin::getBillState, model.getBillState())
                    .eq(BaseOutin::getCode, model.getCode()).orderByDesc(BaseOutin::getCreatedTime)
                    .last("limit 1"));
        }
        if (ObjectUtil.isNull(baseOutin)) {
            baseOutin = BeanUtil.copyProperties(model, BaseOutin.class);
            baseOutin.setBillDate(LocalDate.now());
        } else {
            BeanUtil.copyProperties(model, baseOutin);
        }
        //订单状态
        baseOutin.setBillState(model.getBillState());
        if (ObjectUtil.isNotNull(model.getBillState())) {
            //滞单
            baseOutin.setBillState(model.getBillState());
        }
        baseOutin.setCode(StrUtil.isBlank(model.getCode()) ? getCode() : model.getCode());

        Long orgId = ContextUtil.getCurrentCompanyId();
        baseOutin.setCreatedOrgId(orgId);
        baseOutin.setCreatedBy(ContextUtil.getUserId());
        baseOutin.setUpdatedBy(ContextUtil.getUserId());
        baseOutin.setEmployeeId(ContextUtil.getEmployeeId());
        baseOutin.setOrgId(orgId);
        baseOutin.setSourceType(model.getSourceType());
        baseOutin.setUpdatedTime(LocalDateTime.now());
        baseOutin.setWarehouseId(model.getWarehouseId());
        ArgumentAssert.isFalse(CollUtil.isEmpty(model.getOutinProductList()), "请选择商品");
        List<BaseOutinProduct> outinProductList = model.getOutinProductList().stream().map(vo -> {
            vo.setAddTime(vo.getAddTime() == null ? LocalDateTime.now() : vo.getAddTime());
            BaseOutinProduct baseOutinProduct = BeanUtil.copyProperties(vo, BaseOutinProduct.class);
            baseOutinProduct.setCreatedOrgId(orgId);
            baseOutinProduct.setUpdatedBy(ContextUtil.getUserId());
            baseOutinProduct.setCreatedTime(vo.getAddTime());
            baseOutinProduct.setUpdatedTime(vo.getAddTime());
            baseOutinProduct.setCreatedBy(ContextUtil.getUserId());
            baseOutinProduct.setCostPrice(vo.getCostPrice());
            if (Objects.isNull(vo.getWarehouseId())) {
                baseOutinProduct.setWarehouseId(model.getWarehouseId());
            }
            if (StringUtils.isNotBlank(vo.getRemarks())) {
                baseOutinProduct.setRemarks(vo.getRemarks());
            }
            baseOutinProduct.setEmployeeId(ContextUtil.getEmployeeId());
            if (StrUtil.isBlank(baseOutinProduct.getNumType())) {
                baseOutinProduct.setNumType("2");
                if (Arrays.asList(OutinTypeEnum.PURCHASE_IN.getCode(),
                        OutinTypeEnum.INVENTORY_PROFIT_IN.getCode(),
                        OutinTypeEnum.REFUND_IN.getCode(),
                        OutinTypeEnum.ADJUSTMENT_IN.getCode()).contains(model.getType())) {
                    baseOutinProduct.setNumType("1");
                }
            }
            return baseOutinProduct;
        }).collect(Collectors.toList());
        List<BaseProductStock> stockListWithLock = baseProductStockManager.getStockListWithLock(outinProductList.stream().map(BaseOutinProduct::getProductId).distinct().collect(Collectors.toList()));
        //检查库存现有量
        ArgumentAssert.isFalse(!baseProductStockManager.checkStock(baseOutin, outinProductList, stockListWithLock), "商品库存不足");
        //新增商品库存主表
        if (ObjectUtil.isNotNull(baseOutin.getId())) {
            baseOutin.setUpdatedTime(LocalDateTime.now());
            ArgumentAssert.isFalse(!superManager.updateById(baseOutin), "库存操作失败");
        } else {
            baseOutin.setCreatedTime(LocalDateTime.now());
            ArgumentAssert.isFalse(!superManager.save(baseOutin), "库存操作失败");
        }
        for (BaseOutinProduct baseOutinProduct : outinProductList) {
            baseOutinProduct.setOutinId(baseOutin.getId());
            baseOutinProduct.setEmployeeId(ContextUtil.getEmployeeId());
            baseOutinProduct.setLockNum(0);
            baseOutinProduct.setCreatedTime(baseOutinProduct.getCreatedTime() == null ? LocalDateTime.now() : baseOutinProduct.getCreatedTime());
            baseOutinProduct.setUpdatedTime(baseOutinProduct.getCreatedTime());
        }
        //回写商品库存现有量
        ArgumentAssert.isFalse(!baseProductStockManager.rewriteStock(outinProductList, baseOutin, stockListWithLock), "库存现有量操作失败");
        // 如果需要审核的库存在, 不设置
        if (ObjectUtil.equal(baseOutin.getState(), BaseOutinStateEnum.NO_REVIEWED.getState())) {
            outinProductList.forEach(s -> {
                s.setResidueNum(null);
                s.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
            });
        }
        //商品明细信息
        ArgumentAssert.isFalse(!baseOutinProductManager.saveBatch(outinProductList), "库存明细操作失败");

        // 添加属性
        List<Long> attributeSettingIds = new ArrayList<>();
        for (BaseOutinProduct outinProduct : outinProductList) {
            if (StrUtil.isNotBlank(outinProduct.getAttributeSetting())) {
                attributeSettingIds.addAll(Arrays.stream(outinProduct.getAttributeSetting().split(","))
                        .map(Long::valueOf).collect(Collectors.toSet()));
            }
        }
        if (CollUtil.isNotEmpty(attributeSettingIds)) {
            // 所有属性
            List<BaseAttributeSetting> baseAttributeSettingList = baseAttributeSettingService.listByIds(attributeSettingIds);
            List<Long> attributeIds = baseAttributeSettingList.stream().map(BaseAttributeSetting::getAttributeId).collect(Collectors.toList());
            List<BaseAttribute> attributeList = baseAttributeService.listByIds(attributeIds);
            Map<Long, BaseAttribute> baseAttributeMap = attributeList.stream().collect(Collectors.toMap(BaseAttribute::getId, Function.identity()));
            Map<Long, BaseAttributeSetting> baseAttributeSettingMap = baseAttributeSettingList.stream().collect(Collectors.toMap(BaseAttributeSetting::getId, Function.identity()));
            List<BaseOutinProductAttribute> baseOutinProductAttributeList = new ArrayList<>();
            for (BaseOutinProduct outinProduct : outinProductList) {
                if (StringUtils.isNotBlank(outinProduct.getAttributeSetting())) {
                    List<Long> settingIds = Arrays.stream(outinProduct.getAttributeSetting().split(","))
                            .map(Long::valueOf).collect(Collectors.toList());
                    for (Long settingId : settingIds) {
                        baseOutinProductAttributeList.add(BaseOutinProductAttribute.builder()
                                .outinProductId(outinProduct.getId())
                                .productId(outinProduct.getProductId())
                                .attributeId(baseAttributeSettingMap.get(settingId).getAttributeId())
                                .attributeName(baseAttributeMap.get(baseAttributeSettingMap.get(settingId).getAttributeId()).getName())
                                .attributeSettingId(settingId)
                                .attributeSettingName(baseAttributeSettingMap.get(settingId).getName())
                                .changeType(baseAttributeSettingMap.get(settingId).getChangeType())
                                .changeValue(baseAttributeSettingMap.get(settingId).getChangeValue())
                                .createdOrgId(ContextUtil.getCurrentCompanyId())
                                .build());
                    }
                }
            }
            baseOutinProductAttributeService.saveBatch(baseOutinProductAttributeList);
        }

        return baseOutin;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean remove(LbQueryWrap<BaseOutin> eq) {
        return superManager.remove(eq);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(LbUpdateWrap<BaseOutin> wrap) {
        return superManager.update(wrap);
    }

    @Override
    public BaseOutin getByCode(String code) {
        return superManager.getOne(Wraps.<BaseOutin>lbQ().eq(BaseOutin::getCode, code)
                .orderByDesc(BaseOutin::getCreatedTime).last("limit 1"));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean productReturn(PosCashProduct posCashProduct, Integer num, PosCash posCash) {
//        BaseProductStock baseProductStock = baseProductStockManager.getByProductId(posCashProduct.getProductId());
//        ArgumentAssert.notNull(baseProductStock, "库存明细不存在");
//        ArgumentAssert.isFalse(baseProductStock.getLockNum() < num,
//                "库存异常，请联系管理员");
        String desc = PosCashTypeEnum.get(posCash.getType()).getDesc()
                .concat("-")
                .concat(PosCashBillTypeEnum.get(posCash.getBillType()).getDesc())
                .concat("-")
                .concat(PosCashBillStateEnum.get(posCash.getBillState()).getDesc());
        saveStock(BaseOutinStockSaveVO.builder()
                .type(OutinTypeEnum.SELL_OUT.getCode())
                .sourceType(SourceTypeEnum.POS.getCode())
                .warehouseId(posCashProduct.getWarehouseId()).code(posCash.getCode())
                .billDate(LocalDate.now()).billState(0)
                .orgId(ContextUtil.getCurrentCompanyId())
                .remarks(desc).employeeId(ContextUtil.getEmployeeId())
                .outinProductList(Collections.singletonList(BaseOutinProductSaveVO.builder()
                        .isGift(false)
                        .name(posCashProduct.getProductName())
                        .productId(posCashProduct.getProductId())
                        .warehouseId(posCashProduct.getWarehouseId())
                        .num(num).desc(
                                (posCash.getType().equals(PosCashTypeEnum.START_TABLE.getCode())
                                        ? posCash.getTableName() : PosCashTypeEnum.get(posCash.getType()).getDesc())
                                        .concat("-商品退货")).numType("1")
                        .price(posCashProduct.getPrice())
                        .cashProductId(posCashProduct.getId())
                        .build()))
                .build());
        return true;
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public boolean productListReturn(List<BaseOutinProductSaveVO> baseOutinList, PosCash posCash) {
        if (CollUtil.isEmpty(baseOutinList) || ObjectUtil.isNull(posCash)) {
            return false;
        }
        String desc = PosCashTypeEnum.get(posCash.getType()).getDesc()
                .concat("-")
                .concat(PosCashBillTypeEnum.get(posCash.getBillType()).getDesc())
                .concat("-")
                .concat(PosCashBillStateEnum.get(posCash.getBillState()).getDesc());
        saveStock(BaseOutinStockSaveVO.builder()
                .type(OutinTypeEnum.RETURN.getCode())
                .sourceType(SourceTypeEnum.POS.getCode())
                .warehouseId(1L).code(posCash.getCode())
                .billDate(LocalDate.now()).billState(1)
                .orgId(ContextUtil.getCurrentCompanyId())
                .remarks(desc).employeeId(ContextUtil.getEmployeeId())
                .outinProductList(baseOutinList)
                .build());
        return true;
    }

    @Override
    public List<BaseOutinPurchaseOutExportResultVO> purchaseOutExport(Long id) {
        BaseOutin baseOutin = superManager.getById(id);
        if (ObjectUtil.isNull(baseOutin)) {
            return Collections.emptyList();
        }
        BaseOutinResultVO baseOutinResultVO = BeanPlusUtil.toBean(baseOutin, BaseOutinResultVO.class);
        List<BaseOutinProductResultVO> baseOutinProductResultVOList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ()
                        .eq(BaseOutinProduct::getDeleteFlag, 0)
                        .eq(BaseOutinProduct::getOutinId, baseOutin.getId())
                ).stream()
                .map(v -> BeanUtil.copyProperties(v, BaseOutinProductResultVO.class))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(baseOutinProductResultVOList)) {
            return Collections.emptyList();
        }
        BaseOutinApprovalResultVO approvalResultVO;
        BaseOutinApproval approval = baseOutinApprovalService.getOne(Wraps.<BaseOutinApproval>lbQ()
                .eq(BaseOutinApproval::getType, OutinTypeEnum.PURCHASE_OUT.getCode())
                .eq(BaseOutinApproval::getBizId, baseOutinResultVO.getId())
                .in(BaseOutinApproval::getState, Arrays.asList(1, 2))
                .orderByDesc(BaseOutinApproval::getCreatedTime).last("limit 1"));
        if (Objects.nonNull(approval)) {
            approvalResultVO = BeanPlusUtil.toBean(approval, BaseOutinApprovalResultVO.class);
            echoService.action(approvalResultVO);
        } else {
            approvalResultVO = null;
        }
        echoService.action(baseOutinResultVO);
        return baseOutinProductResultVOList.stream().map(
                v -> BaseOutinPurchaseOutExportResultVO.builder()
                        .productName(v.getName())
                        .changeNum(v.getNum())
                        .costPrice(v.getCostPrice())
                        .warehouse(Objects.nonNull(v.getEchoMap().get("warehouseId")) ? v.getEchoMap().get("warehouseId").toString() : "-")
                        .createdTime(baseOutinResultVO.getCreatedTime())
                        .auditTime(Objects.nonNull(approvalResultVO) ? approvalResultVO.getCreatedTime() : null)
                        .employee(Objects.nonNull(baseOutinResultVO.getEchoMap().get("employeeId")) ? baseOutinResultVO.getEchoMap().get("employeeId").toString() : "-")
                        .auditEmployee(Objects.nonNull(approvalResultVO) ? approvalResultVO.getEchoMap().get("employeeId").toString() : "-")
                        .remarks(baseOutinResultVO.getRemarks())
                        .org(Objects.nonNull(baseOutinResultVO.getEchoMap().get("orgId")) ? baseOutinResultVO.getEchoMap().get("orgId").toString() : "-")
                        .build()
        ).collect(Collectors.toList());
    }

    @Override
    public List<BaseOutinPurchaseInExportResultVO> purchaseInExport(Long id) {
        BaseOutin baseOutin = superManager.getById(id);
        if (ObjectUtil.isNull(baseOutin)) {
            return Collections.emptyList();
        }
        BaseOutinResultVO baseOutinResultVO = BeanPlusUtil.toBean(baseOutin, BaseOutinResultVO.class);
        List<BaseOutinProductResultVO> baseOutinProductResultVOList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ()
                        .eq(BaseOutinProduct::getDeleteFlag, 0)
                        .eq(BaseOutinProduct::getOutinId, baseOutin.getId())
                ).stream()
                .map(v -> BeanUtil.copyProperties(v, BaseOutinProductResultVO.class))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(baseOutinProductResultVOList)) {
            return Collections.emptyList();
        }
        List<Long> productIds = baseOutinProductResultVOList.stream().map(BaseOutinProductResultVO::getProductId).distinct().collect(Collectors.toList());
        List<BaseProductResultVO> productManagerList = baseProductManager.findList(Wraps.<BaseProduct>lbQ().in(BaseProduct::getId, productIds));
        Map<Long, BaseProductResultVO> productMap = productManagerList.stream().collect(Collectors.toMap(BaseProductResultVO::getId, Function.identity()));
        echoService.action(baseOutinResultVO);
        List<BaseOutinPurchaseInExportResultVO> resultVOList = baseOutinProductResultVOList.stream().map(
                v -> {
                    BaseProductResultVO productResultVO = productMap.get(v.getProductId());
                    return BaseOutinPurchaseInExportResultVO.builder()
                            .echoMap(MapUtil.newHashMap())
                            .createdTime(v.getCreatedTime())
                            .code(baseOutinResultVO.getCode())
                            .categoryId(Objects.nonNull(productResultVO) ? productResultVO.getCategoryId() : null)
                            .productName(Objects.nonNull(productResultVO) ? productResultVO.getName() : null)
                            .measuringUnitKey(Objects.nonNull(productResultVO) ? productResultVO.getMeasuringUnit() : null)
                            .num(v.getNum())
                            .costPrice(v.getCostPrice())
                            .totalCostPrice(v.getAmount())
                            .remarks(v.getRemarks())
                            .employeeId(baseOutinResultVO.getEmployeeId())
                            .orgId(baseOutinResultVO.getOrgId())
                            .build();
                }
        ).collect(Collectors.toList());
        echoService.action(resultVOList);
        AtomicInteger serialNum = new AtomicInteger();
        serialNum.getAndIncrement();
        resultVOList.forEach(s -> {
            s.setSerialNum(serialNum.getAndIncrement());
            Map<String, Object> echoMap = s.getEchoMap();
            if (CollUtil.isNotEmpty(echoMap)) {
                if (echoMap.containsKey("categoryId") && Objects.nonNull(echoMap.get("categoryId"))) {
                    s.setCategory(echoMap.get("categoryId").toString());
                } else {
                    s.setCategory("-");
                }
                if (echoMap.containsKey("measuringUnitKey") && Objects.nonNull(echoMap.get("measuringUnitKey"))) {
                    s.setMeasuringUnit(echoMap.get("measuringUnitKey").toString());
                } else {
                    s.setMeasuringUnit("-");
                }
                if (echoMap.containsKey("employeeId") && Objects.nonNull(echoMap.get("employeeId"))) {
                    s.setEmployee(echoMap.get("employeeId").toString());
                } else {
                    s.setEmployee("-");
                }
                if (echoMap.containsKey("orgId") && Objects.nonNull(echoMap.get("orgId"))) {
                    s.setOrg(echoMap.get("orgId").toString());
                } else {
                    s.setOrg("-");
                }
            }
        });
        return resultVOList;
    }

    @Override
    public IPage<SellOutinResultVO> sell(PageParams<SellOutinQuery> params) {
        SellOutinQuery query = params.getModel();
        params.setSort("");
        params.setOrder("");
        QueryWrapper<PosCash> wrapper = new QueryWrapper<>();
        wrapper.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(),
                        PosCashBillStateEnum.PART_REFUND.getCode()))
                .notIn("p.bill_type", Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode(),
                        PosCashBillTypeEnum.CHARGEBACK.getCode())).eq("p.delete_flag", 0)
                .between(StringUtils.isNotBlank(query.getCompleteTime_st()) && StringUtils.isNotBlank(query.getCompleteTime_ed()), "p.complete_time", query.getCompleteTime_st(), query.getCompleteTime_ed())
                .between(StringUtils.isNotBlank(query.getCreatedTime_st()) && StringUtils.isNotBlank(query.getCreatedTime_ed()), "p.created_time", query.getCreatedTime_st(), query.getCreatedTime_ed())
                .eq("pcp.delete_flag", 0)
                .like(StringUtils.isNotBlank(query.getKeyword()), "p.code", query.getKeyword())
                .inSql(StringUtils.isNotBlank(query.getUpdateBy()), "p.complete_emp", "select id from base_employee where real_name like '%" + query.getUpdateBy() + "%' or name like '%" + query.getUpdateBy() + "%'" )
                .eq("p.org_id", ContextUtil.getCurrentCompanyId()).orderByDesc("p.complete_time");
        IPage<SellOutinResultVO> pagedList = baseOutinMapper.sellPage(params.buildPage(SellOutinResultVO.class), wrapper);
        for (SellOutinResultVO posCash : pagedList.getRecords()) {
            //相关备注
            if (StrUtil.isNotBlank(posCash.getRemarks())) {
                OrderRemarksResultVO orderRemarksResultVO = JSON.parseObject(posCash.getRemarks(), OrderRemarksResultVO.class);
                if (StringUtils.isNotBlank(orderRemarksResultVO.getItemTags())) {
                    List<String> iteamRemarkList = Arrays.stream(orderRemarksResultVO.getItemTags().split(",")).collect(Collectors.toList());
                    posCash.setRemarks(setRemarksDesc(iteamRemarkList, orderRemarksResultVO.getRemarks(), posCash.getRemarks(), EchoDictType.App.ITEM_TAGS));
                }
            }
        }
        echoService.action(pagedList.getRecords());
        return pagedList;
    }

    public String setRemarksDesc(List<String> tags, String remarks, String remark, String key) {
        ItemRemarksResultVO itemRemarksResultVO = new ItemRemarksResultVO();
        //获取原始备注
        if (StrUtil.isNotBlank(remark)) {
            itemRemarksResultVO = com.alibaba.fastjson.JSON.parseObject(remark, ItemRemarksResultVO.class);
        }
        String itemTags = tags == null ? "" : String.join(",", tags);
        //保存新的备注
        if (ObjectUtil.equal(key, EchoDictType.App.ITEM_TAGS)) {
            itemRemarksResultVO.setItemTags(itemTags);
            itemRemarksResultVO.setRemarks(remarks);
        } else if (ObjectUtil.equal(key, EchoDictType.App.ITEM_DISCOUNT_TAGS)) {
            itemRemarksResultVO.setDiscountTags(itemTags);
            itemRemarksResultVO.setDiscountRemarks(remarks);
        } else if (ObjectUtil.equal(key, EchoDictType.App.ITEM_RETREAT)) {
            itemRemarksResultVO.setRetreatTags(itemTags);
            itemRemarksResultVO.setRetreatRemarks(remarks);
        } else if (ObjectUtil.equal(key, EchoDictType.App.PRODUCT_ITEM_RETREAT)) {
            itemRemarksResultVO.setProductReturnTags(itemTags);
            itemRemarksResultVO.setProductReturnRemarks(remarks);
        } else if (ObjectUtil.equal(key, EchoDictType.App.KNOT_TAGS)) {
            itemRemarksResultVO.setKnotTags(itemTags);
            itemRemarksResultVO.setKnotRemarks(remarks);
        } else if (ObjectUtil.equal(key, EchoDictType.App.CHANGE_PRICE_TAGS)) {
            itemRemarksResultVO.setChangePriceTags(itemTags);
            itemRemarksResultVO.setChangePriceRemarks(remarks);
        } else if (ObjectUtil.equal(key, EchoDictType.App.PRODUCT_ITEM_GIFT)) {
            itemRemarksResultVO.setProductGiftTags(itemTags);
            itemRemarksResultVO.setProductGiftRemarks(remarks);
        }
        echoService.action(itemRemarksResultVO);
        String tagDesc = null;
        if (ObjectUtil.equal(key, EchoDictType.App.ITEM_TAGS)) {
            if (itemRemarksResultVO.getEchoMap().get("itemTags") != null) {
                tagDesc = itemRemarksResultVO.getEchoMap().get("itemTags").toString();
            }
        } else if (ObjectUtil.equal(key, EchoDictType.App.ITEM_DISCOUNT_TAGS)) {
            if (itemRemarksResultVO.getEchoMap().get("discountTags") != null) {
                tagDesc = itemRemarksResultVO.getEchoMap().get("discountTags").toString();
            }
        } else if (ObjectUtil.equal(key, EchoDictType.App.ITEM_RETREAT)) {
            if (itemRemarksResultVO.getEchoMap().get("retreatTags") != null) {
                tagDesc = itemRemarksResultVO.getEchoMap().get("retreatTags").toString();
            }
        } else if (ObjectUtil.equal(key, EchoDictType.App.PRODUCT_ITEM_RETREAT)) {
            if (itemRemarksResultVO.getEchoMap().get("productReturnTags") != null) {
                tagDesc = itemRemarksResultVO.getEchoMap().get("productReturnTags").toString();
            }
        } else if (ObjectUtil.equal(key, EchoDictType.App.KNOT_TAGS)) {
            if (itemRemarksResultVO.getEchoMap().get("knotTags") != null) {
                tagDesc = itemRemarksResultVO.getEchoMap().get("knotTags").toString();
            }
        } else if (ObjectUtil.equal(key, EchoDictType.App.CHANGE_PRICE_TAGS)) {
            if (itemRemarksResultVO.getEchoMap().get("changePriceTags") != null) {
                tagDesc = itemRemarksResultVO.getEchoMap().get("changePriceTags").toString();
            }
        } else if (ObjectUtil.equal(key, EchoDictType.App.PRODUCT_ITEM_GIFT)) {
            if (itemRemarksResultVO.getEchoMap().get("productGiftTags") != null) {
                tagDesc = itemRemarksResultVO.getEchoMap().get("productGiftTags").toString();
            }
        }
        if (StrUtil.isBlank(remarks)) {
            return tagDesc;
        }
        if (StrUtil.isBlank(tagDesc)) {
            return remarks;
        }
        return tagDesc.concat(",").concat(remarks);
    }

    @Override
    public SellOutinResultVO sellDetail(Long id) {
        QueryWrapper<PosCash> wrapper = new QueryWrapper<>();
        wrapper.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(),
                        PosCashBillStateEnum.PART_REFUND.getCode()))
                .notIn("p.bill_type", Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode(),
                        PosCashBillTypeEnum.CHARGEBACK.getCode())).eq("p.delete_flag", 0)
                .eq("pcp.delete_flag", 0)
                .eq("p.id", id)
                .eq("p.org_id", ContextUtil.getCurrentCompanyId()).last("limit 1");
        SellOutinResultVO sellOutinResultVO = baseOutinMapper.sellOne(wrapper);
        if (StrUtil.isNotBlank(sellOutinResultVO.getRemarks())) {
            OrderRemarksResultVO orderRemarksResultVO = JSON.parseObject(sellOutinResultVO.getRemarks(), OrderRemarksResultVO.class);
            echoService.action(orderRemarksResultVO);
            // 整单备注
            String orderTags = ((CollUtil.isNotEmpty(orderRemarksResultVO.getEchoMap()) && ObjectUtil.isNotNull(orderRemarksResultVO.getEchoMap().get("orderTags"))) ? orderRemarksResultVO.getEchoMap().get("orderTags").toString() : "");
            // 挂单备注
            String registrationTags = ((CollUtil.isNotEmpty(orderRemarksResultVO.getEchoMap()) && ObjectUtil.isNotNull(orderRemarksResultVO.getEchoMap().get("registrationTags"))) ? orderRemarksResultVO.getEchoMap().get("registrationTags").toString() : "");
            if (StrUtil.isNotBlank(orderTags)) {
                if (StringUtils.isNotBlank(orderRemarksResultVO.getRemarks())) {
                    orderTags = orderTags + "," + orderRemarksResultVO.getRemarks();
                }
            } else {
                if (StringUtils.isNotBlank(orderRemarksResultVO.getRemarks())) {
                    orderTags = orderRemarksResultVO.getRemarks();
                }
            }
            if (StringUtils.isNotBlank(registrationTags)) {
                if (StringUtils.isNotBlank(orderRemarksResultVO.getRegistrationRemarks())) {
                    registrationTags = registrationTags + "," + orderRemarksResultVO.getRegistrationRemarks();
                }
            } else {
                if (StringUtils.isNotBlank(orderRemarksResultVO.getRegistrationRemarks())) {
                    registrationTags = orderRemarksResultVO.getRegistrationRemarks();
                }
            }
            if (StringUtils.isNotBlank(orderTags)) {
                sellOutinResultVO.setOrderRemarks(orderTags);
            } else {
                sellOutinResultVO.setOrderRemarks("-");
            }
            if (StringUtils.isNotBlank(registrationTags)) {
                sellOutinResultVO.setRegistrationRemarks(registrationTags);
            } else {
                sellOutinResultVO.setRegistrationRemarks("-");
            }
        }
        List<SellOutinDetailsResultVO> outinDetailsResultVOList = baseOutinMapper.sellDetailList(id);
        echoService.action(outinDetailsResultVOList);
        sellOutinResultVO.setOutinProductList(outinDetailsResultVOList);
        echoService.action(sellOutinResultVO);
        return sellOutinResultVO;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean reversalEntry(BaseOutinReversalEntryVO model) {
        // 查询ID, 只有采购入库和采购退货有红冲
        BaseOutin baseOutin = superManager.getById(model.getId());
        ArgumentAssert.notNull(baseOutin, "单据不存在");

        ArgumentAssert.isTrue(StringUtils.equals(baseOutin.getType(), OutinTypeEnum.PURCHASE_IN.getCode())
                || StringUtils.equals(baseOutin.getType(), OutinTypeEnum.PURCHASE_OUT.getCode()), "只有采购入库和采购退货有红冲");
        ArgumentAssert.isTrue(!Objects.equals(baseOutin.getState(), BaseOutinStateEnum.REVERSAL_ENTRY_NO_REVIEWED.getState()), "单据处于红冲审核状态， 不能重复红冲");
        ArgumentAssert.isTrue(!Objects.equals(baseOutin.getState(), BaseOutinStateEnum.REVERSAL_ENTRY.getState()), "单据审核通过， 不能重复红冲");
        // 采购入库单
        if (StringUtils.equals(baseOutin.getType(), OutinTypeEnum.PURCHASE_IN.getCode())) {
            boolean reversalEntryAudit = true;
            Map<String, String> voiceParams = helperApi.findParams(Collections.singletonList(ParameterKey.OUTIN_IN_REVERSAL_ENTRY_AUDIT)).getData();
            log.info("个性参数：{}", JsonUtil.toJson(voiceParams));
            if (CollUtil.isNotEmpty(voiceParams) && StrUtil.isNotBlank(voiceParams.get(ParameterKey.OUTIN_IN_REVERSAL_ENTRY_AUDIT))
                    && StringUtils.equals(voiceParams.get(ParameterKey.OUTIN_IN_REVERSAL_ENTRY_AUDIT), "0")) {
                reversalEntryAudit = false;
            }
            if (reversalEntryAudit) {
                baseOutin.setState(BaseOutinStateEnum.REVERSAL_ENTRY_NO_REVIEWED.getState());
                baseOutin.setBillState(0);
                superManager.updateById(baseOutin);
                baseOutinApprovalService.save(BaseOutinApprovalSaveVO.builder()
                        .bizId(model.getId())
                        .type(baseOutin.getType())
                        .state(baseOutin.getState())
                        .remarks(model.getRemarks())
                        .employeeId(ContextUtil.getEmployeeId())
                        .createdOrgId(ContextUtil.getCurrentCompanyId())
                        .build());
                return true;
            }
            // 不需要审核，直接审核通过， 状态改为作废， 新增一条记录，改为已红冲
            baseOutin.setState(BaseOutinStateEnum.INVALID.getState());
            baseOutin.setBillState(1);
            superManager.updateById(baseOutin);
            createdPurchaseInReversalEntry(baseOutin);
            return true;
        }
        // 采购退货单
        boolean reversalEntryAudit = true;
        Map<String, String> voiceParams = helperApi.findParams(Collections.singletonList(ParameterKey.OUTIN_OUT_REVERSAL_ENTRY_AUDIT)).getData();
        log.info("个性参数：{}", JsonUtil.toJson(voiceParams));
        if (CollUtil.isNotEmpty(voiceParams) && StrUtil.isNotBlank(voiceParams.get(ParameterKey.OUTIN_OUT_REVERSAL_ENTRY_AUDIT))
                && StringUtils.equals(voiceParams.get(ParameterKey.OUTIN_OUT_REVERSAL_ENTRY_AUDIT), "0")) {
            reversalEntryAudit = false;
        }
        if (reversalEntryAudit) {
            baseOutin.setState(BaseOutinStateEnum.REVERSAL_ENTRY_NO_REVIEWED.getState());
            superManager.updateById(baseOutin);
            baseOutinApprovalService.save(BaseOutinApprovalSaveVO.builder()
                    .bizId(model.getId())
                    .type(baseOutin.getType())
                    .state(baseOutin.getState())
                    .remarks(model.getRemarks())
                    .employeeId(ContextUtil.getEmployeeId())
                    .createdOrgId(ContextUtil.getCurrentCompanyId())
                    .build());
            return true;
        }
        // 不需要审核，直接审核通过， 状态改为作废， 新增一条记录，改为已红冲
        baseOutin.setState(BaseOutinStateEnum.INVALID.getState());
        superManager.updateById(baseOutin);
        createdPurchaseOutReversalEntry(baseOutin);
        baseOutinApprovalService.save(BaseOutinApprovalSaveVO.builder()
                .bizId(model.getId())
                .type(baseOutin.getType())
                .state(BaseOutinStateEnum.REVERSAL_ENTRY.getState())
                .remarks(model.getRemarks())
                .employeeId(ContextUtil.getEmployeeId())
                .createdOrgId(ContextUtil.getCurrentCompanyId())
                .build());
        return true;
    }

    @Override
    public Boolean updateOutinPrice(BaseOutinPriceUpdateVO model) {
        BaseOutin baseOutin = superManager.getById(model.getId());
        ArgumentAssert.notNull(baseOutin, "单据不存在");
        ArgumentAssert.isTrue(Objects.equals(baseOutin.getState(), BaseOutinStateEnum.REVIEWED.getState()), "只有审核通过的单据可以修改");
        ArgumentAssert.isTrue(StrUtil.equals(baseOutin.getType(), OutinTypeEnum.PURCHASE_IN.getCode()) || StrUtil.equals(baseOutin.getType(), OutinTypeEnum.PURCHASE_OUT.getCode()), "只有采购入库和采购退货可以审核完成后修改");
        this.updateById(BaseOutinUpdateVO.builder().id(model.getId()).remarks(model.getRemarks()).build());
        List<BaseOutinPriceDetailUpdateVO> productOutinList = model.getProductOutinList();
        if (CollUtil.isNotEmpty(productOutinList)) {
            // 方案一：使用自定义批量更新SQL（推荐）
            int updateCount = baseOutinProductManager.batchUpdatePrice(productOutinList);
            log.info("批量更新出入库商品价格信息，更新记录数：{}", updateCount);
        }
        return true;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean forceUpdatePrice(BaseOutinForceUpdateVO updateVO) {
        BaseOutin baseOutin = superManager.getById(updateVO.getId());
        ArgumentAssert.notNull(baseOutin, "单据不存在");
        ArgumentAssert.isTrue(Objects.equals(baseOutin.getState(), BaseOutinStateEnum.REVIEWED.getState()), "只有审核通过的单据才需要强制修改");
        ArgumentAssert.isTrue(StrUtil.equals(baseOutin.getType(), OutinTypeEnum.PURCHASE_IN.getCode()) || StrUtil.equals(baseOutin.getType(), OutinTypeEnum.PURCHASE_OUT.getCode()), "只有采购入库和采购退货可以审核完成后修改");
        List<BaseOutinProduct> outinProductList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getOutinId, baseOutin.getId()));
        ArgumentAssert.isTrue(CollUtil.isNotEmpty(outinProductList), "单据详情不存在，无法强制修改");
        ArgumentAssert.isTrue(outinProductList.stream().allMatch(s -> Objects.equals(s.getOutinId(), updateVO.getId())), "单据详情与单据不匹配");
        this.updateById(BaseOutinUpdateVO.builder().id(updateVO.getId())
                .amount(Objects.nonNull(updateVO.getAmount()) ? updateVO.getAmount().doubleValue() : 0D)
                .discountAmount(Objects.nonNull(updateVO.getDiscountAmount()) ? updateVO.getDiscountAmount().doubleValue() : 0D)
                .remarks(updateVO.getRemarks())
                .build());
        if (CollUtil.isNotEmpty(updateVO.getOutinProductList())) {
            List<BaseOutinPriceDetailUpdateVO> updateVOList = updateVO.getOutinProductList().stream()
                    .map(s -> BaseOutinPriceDetailUpdateVO.builder()
                            .id(s.getId())
                            .costPrice(s.getCostPrice())
                            .amount(s.getAmount())
                            .build()).collect(Collectors.toList());
            baseOutinProductManager.batchUpdatePrice(updateVOList);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseOutin purchaseOut(BaseOutinStockSaveVO model) {
        ArgumentAssert.notEmpty(model.getOutinProductList(), "请选择商品");
        List<Long> productIds = model.getOutinProductList().stream()
                .map(BaseOutinProductSaveVO::getProductId).collect(Collectors.toList());
        Map<Long, BaseProductStock> productStockMap = baseProductStockManager.list(Wraps.<BaseProductStock>lbQ()
                        .in(BaseProductStock::getProductId, productIds)
                        .eq(BaseProductStock::getWarehouseId, model.getWarehouseId()))
                .stream().collect(Collectors.toMap(BaseProductStock::getProductId, Function.identity()));
        Map<Long, BaseProduct> baseProductMap = baseProductManager.list(Wraps.<BaseProduct>lbQ()
                        .in(BaseProduct::getId, productIds))
                .stream().collect(Collectors.toMap(BaseProduct::getId, Function.identity()));

        BaseWarehouse warehouse = baseWarehouseService.getOneBySn();

        List<BaseOutinProductSaveVO> outProductSaveVOList = Lists.newArrayList();
        for (BaseOutinProductSaveVO outinProductSaveVO : model.getOutinProductList()) {
            BaseProductStock baseProductStock = productStockMap.get(outinProductSaveVO.getProductId());
            ArgumentAssert.notNull(baseProductStock, "库存异常，请联系管理员");
            BaseProduct baseProduct = baseProductMap.get(outinProductSaveVO.getProductId());
            ArgumentAssert.notNull(baseProduct, "商品不存在，请联系管理员");
            outinProductSaveVO.setIsGift(false);
            outinProductSaveVO.setDesc(StrUtil.isBlank(model.getRemarks()) ? "采购退货" : model.getRemarks());
            outinProductSaveVO.setRemarks(StrUtil.isBlank(model.getRemarks()) ? "" : model.getRemarks());
            outinProductSaveVO.setAddTime(LocalDateTime.now());
            outinProductSaveVO.setWarehouseId(Objects.nonNull(model.getWarehouseId()) ? model.getWarehouseId() : warehouse.getId());
            //补充的数量
            int num = baseProductStock.getNum() - baseProductStock.getLockNum();
            if (num - outinProductSaveVO.getNum() >= 0) {
                outinProductSaveVO.setNum(outinProductSaveVO.getNum());
                outinProductSaveVO.setNumType("2");
                outinProductSaveVO.setAddTime(LocalDateTime.now().plusSeconds(1));
                outProductSaveVOList.add(outinProductSaveVO);
            } else {
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1),
                        "库存不足，请联系管理员");
            }
        }
        //采购退货
        if (CollUtil.isNotEmpty(outProductSaveVOList)) {
            BaseOutinStockSaveVO saveVO = BaseOutinStockSaveVO.builder()
                    .type(OutinTypeEnum.PURCHASE_OUT.getCode())
                    .sourceType(model.getSourceType())
                    .warehouseId(Objects.nonNull(model.getWarehouseId()) ? model.getWarehouseId() : warehouse.getId()).billState(1)
                    .billDate(LocalDate.now())
                    .orgId(ContextUtil.getCurrentCompanyId())
                    .remarks(StrUtil.isBlank(model.getRemarks()) ? "" : model.getRemarks())
                    .employeeId(ContextUtil.getEmployeeId())
                    .isNum(true)
                    .isLockNum(false)
                    .state(model.getState())
                    .outinProductList(outProductSaveVOList)
                    .build();
            return this.saveStock(saveVO);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseOutin otherOut(BaseOutinStockSaveVO model) {
        ArgumentAssert.notEmpty(model.getOutinProductList(), "请选择商品");
        List<Long> productIds = model.getOutinProductList().stream()
                .map(BaseOutinProductSaveVO::getProductId).collect(Collectors.toList());
        Map<Long, BaseProductStock> productStockMap = baseProductStockManager.list(Wraps.<BaseProductStock>lbQ()
                        .in(BaseProductStock::getProductId, productIds)
                        .eq(BaseProductStock::getWarehouseId, model.getWarehouseId()))
                .stream().collect(Collectors.toMap(BaseProductStock::getProductId, Function.identity()));
        Map<Long, BaseProduct> baseProductMap = baseProductManager.list(Wraps.<BaseProduct>lbQ()
                        .in(BaseProduct::getId, productIds))
                .stream().collect(Collectors.toMap(BaseProduct::getId, Function.identity()));

        BaseWarehouse warehouse = baseWarehouseService.getOneBySn();

        List<BaseOutinProductSaveVO> outProductSaveVOList = Lists.newArrayList();
        for (BaseOutinProductSaveVO outinProductSaveVO : model.getOutinProductList()) {
            BaseProduct baseProduct = baseProductMap.get(outinProductSaveVO.getProductId());
            ArgumentAssert.notNull(baseProduct, "商品不存在，请联系管理员");
            outinProductSaveVO.setIsGift(false);
            outinProductSaveVO.setDesc(StrUtil.isBlank(model.getRemarks()) ? "其他出库" : model.getRemarks());
            outinProductSaveVO.setRemarks(StrUtil.isBlank(model.getRemarks()) ? "" : model.getRemarks());
            outinProductSaveVO.setAddTime(LocalDateTime.now());
            outinProductSaveVO.setWarehouseId(Objects.nonNull(model.getWarehouseId()) ? model.getWarehouseId() : warehouse.getId());
            outinProductSaveVO.setNum(outinProductSaveVO.getNum());
            outinProductSaveVO.setNumType("2");
            outinProductSaveVO.setAddTime(LocalDateTime.now().plusSeconds(1));
            outProductSaveVOList.add(outinProductSaveVO);
        }
        ArgumentAssert.isTrue(CollUtil.isNotEmpty(outProductSaveVOList), "请选择商品");

        BaseOutinStockSaveVO saveVO = BaseOutinStockSaveVO.builder()
                .type(OutinTypeEnum.OTHER_OUT.getCode())
                .sourceType(model.getSourceType())
                .warehouseId(Objects.nonNull(model.getWarehouseId()) ? model.getWarehouseId() : warehouse.getId())
                .billState(1)
                .amount(model.getAmount())
                .discountAmount(model.getDiscountAmount())
                .billDate(LocalDate.now())
                .orgId(ContextUtil.getCurrentCompanyId())
                .remarks(StrUtil.isBlank(model.getRemarks()) ? "" : model.getRemarks())
                .employeeId(ContextUtil.getEmployeeId())
                .isNum(true)
                .isLockNum(false)
                .state(model.getState())
                .outinProductList(outProductSaveVOList)
                .build();
        return this.saveStock(saveVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseOutin otherIn(BaseOutinStockSaveVO model) {
        ArgumentAssert.notEmpty(model.getOutinProductList(), "请选择商品");
        List<Long> productIds = model.getOutinProductList().stream()
                .map(BaseOutinProductSaveVO::getProductId).collect(Collectors.toList());
        Map<Long, BaseProductStock> productStockMap = baseProductStockManager.list(Wraps.<BaseProductStock>lbQ()
                        .in(BaseProductStock::getProductId, productIds)
                        .eq(BaseProductStock::getWarehouseId, model.getWarehouseId()))
                .stream().collect(Collectors.toMap(BaseProductStock::getProductId, Function.identity()));
        Map<Long, BaseProduct> baseProductMap = baseProductManager.list(Wraps.<BaseProduct>lbQ()
                        .in(BaseProduct::getId, productIds))
                .stream().collect(Collectors.toMap(BaseProduct::getId, Function.identity()));

        BaseWarehouse warehouse = baseWarehouseService.getOneBySn();

        List<BaseOutinProductSaveVO> outProductSaveVOList = Lists.newArrayList();
        for (BaseOutinProductSaveVO outinProductSaveVO : model.getOutinProductList()) {
            BaseProductStock baseProductStock = productStockMap.get(outinProductSaveVO.getProductId());
            ArgumentAssert.notNull(baseProductStock, "库存异常，请联系管理员");
            BaseProduct baseProduct = baseProductMap.get(outinProductSaveVO.getProductId());
            ArgumentAssert.notNull(baseProduct, "商品不存在，请联系管理员");
            outinProductSaveVO.setIsGift(false);
            outinProductSaveVO.setDesc(StrUtil.isBlank(model.getRemarks()) ? "其他入库" : model.getRemarks());
            outinProductSaveVO.setRemarks(StrUtil.isBlank(model.getRemarks()) ? "" : model.getRemarks());
            outinProductSaveVO.setAddTime(LocalDateTime.now());
            outinProductSaveVO.setWarehouseId(Objects.nonNull(model.getWarehouseId()) ? model.getWarehouseId() : warehouse.getId());
            //补充的数量
            int num = baseProductStock.getNum() - baseProductStock.getLockNum();
            if (num - outinProductSaveVO.getNum() >= 0) {
                outinProductSaveVO.setNum(outinProductSaveVO.getNum());
                outinProductSaveVO.setNumType("2");
                outinProductSaveVO.setAddTime(LocalDateTime.now().plusSeconds(1));
                outProductSaveVOList.add(outinProductSaveVO);
            } else {
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1),
                        "库存不足，请联系管理员");
            }
        }
        ArgumentAssert.isTrue(CollUtil.isNotEmpty(outProductSaveVOList), "请选择商品");
        BaseOutinStockSaveVO saveVO = BaseOutinStockSaveVO.builder()
                .type(OutinTypeEnum.OTHER_IN.getCode())
                .sourceType(model.getSourceType())
                .amount(model.getAmount())
                .discountAmount(model.getDiscountAmount())
                .warehouseId(Objects.nonNull(model.getWarehouseId()) ? model.getWarehouseId() : warehouse.getId())
                .billState(1)
                .billDate(LocalDate.now())
                .orgId(ContextUtil.getCurrentCompanyId())
                .remarks(StrUtil.isBlank(model.getRemarks()) ? "" : model.getRemarks())
                .employeeId(ContextUtil.getEmployeeId())
                .isNum(true)
                .isLockNum(false)
                .state(model.getState())
                .outinProductList(outProductSaveVOList)
                .build();
        return this.saveStock(saveVO);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean adjustmentStock(BaseOutinStockAdjustmentVO model) {
        ArgumentAssert.notEmpty(model.getOutinProductList(), "请选择商品");
        List<Long> productIds = model.getOutinProductList().stream()
                .map(BaseOutinProductSaveVO::getProductId).collect(Collectors.toList());
        // 转入仓库
//        Map<Long, BaseProductStock> productStockInMap = baseProductStockManager.list(Wraps.<BaseProductStock>lbQ()
//                        .in(BaseProductStock::getProductId, productIds)
//                        .eq(BaseProductStock::getWarehouseId, model.getInWarehouseId()))
//                .stream().collect(Collectors.toMap(BaseProductStock::getProductId, Function.identity()));
        // 转出仓库
        Map<Long, BaseProductStock> productStockOutMap = baseProductStockManager.list(Wraps.<BaseProductStock>lbQ()
                        .in(BaseProductStock::getProductId, productIds)
                        .eq(BaseProductStock::getWarehouseId, model.getOutWarehouseId()))
                .stream().collect(Collectors.toMap(BaseProductStock::getProductId, Function.identity()));

        Map<Long, BaseProduct> baseProductMap = baseProductManager.list(Wraps.<BaseProduct>lbQ()
                        .in(BaseProduct::getId, productIds))
                .stream().collect(Collectors.toMap(BaseProduct::getId, Function.identity()));

        Map<Long, BaseWarehouse> baseWarehouseMap = baseWarehouseService.list(Wraps.<BaseWarehouse>lbQ()
                        .in(BaseWarehouse::getId, Arrays.asList(model.getInWarehouseId(), model.getOutWarehouseId())))
                .stream().collect(Collectors.toMap(BaseWarehouse::getId, Function.identity()));

        //新增盘点单
        BaseOutinAdjustment adjustment = baseOutinAdjustmentService
                .save(BaseOutinAdjustmentSaveVO.builder()
                        .code(getCode()).billDate(LocalDate.now())
                        .inWarehouseId(model.getInWarehouseId())
                        .outWarehouseId(model.getOutWarehouseId())
                        .createdOrgId(ContextUtil.getCurrentCompanyId())
                        .employeeId(ContextUtil.getEmployeeId()).deleteFlag(0).remarks(model.getRemarks())
                        .state(model.getState())
                        .sourceType(model.getSourceType()).build());

        List<BaseOutinProductSaveVO> outProductSaveVOList = Lists.newArrayList();
        for (BaseOutinProductSaveVO outinProductSaveVO : model.getOutinProductList()) {
            BaseProductStock baseProductStock = productStockOutMap.get(outinProductSaveVO.getProductId());
            ArgumentAssert.notNull(baseProductStock, "库存异常，请联系管理员");
            BaseProduct baseProduct = baseProductMap.get(outinProductSaveVO.getProductId());
            ArgumentAssert.notNull(baseProduct, "商品不存在，请联系管理员");
            outinProductSaveVO.setIsGift(false);
            outinProductSaveVO.setAdjustmentId(adjustment.getId());
            outinProductSaveVO.setAdjustmentValue(outinProductSaveVO.getNum());
            outinProductSaveVO.setDesc(StrUtil.isBlank(model.getRemarks()) ? "调库出库" : model.getRemarks());
            outinProductSaveVO.setRemarks(StrUtil.isBlank(model.getRemarks()) ? "" : model.getRemarks());
            outinProductSaveVO.setAddTime(LocalDateTime.now());
            outinProductSaveVO.setWarehouseId(model.getOutWarehouseId());
            //补充的数量
            int num = baseProductStock.getNum() - baseProductStock.getLockNum();
            if (num - outinProductSaveVO.getNum() >= 0) {
                outinProductSaveVO.setNum(outinProductSaveVO.getNum());
                outinProductSaveVO.setNumType("2");
                outinProductSaveVO.setAddTime(LocalDateTime.now().plusSeconds(1));
                outProductSaveVOList.add(outinProductSaveVO);
            } else {
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1),
                        "库存不足，请联系管理员");
            }
        }
        BaseOutin outBaseOutin = null;
        if (CollUtil.isNotEmpty(outProductSaveVOList)) {
            BaseOutinStockSaveVO saveVO = BaseOutinStockSaveVO.builder()
                    .type(OutinTypeEnum.ADJUSTMENT_OUT.getCode())
                    .sourceType(model.getSourceType())
                    .warehouseId(model.getOutWarehouseId()).billState(1)
                    .billDate(LocalDate.now())
                    .orgId(ContextUtil.getCurrentCompanyId())
                    .remarks(StrUtil.isBlank(model.getRemarks()) ? "" : model.getRemarks())
                    .employeeId(ContextUtil.getEmployeeId())
                    .isNum(true)
                    .isLockNum(false)
                    .state(model.getState())
                    .outinProductList(outProductSaveVOList)
                    .adjustmentId(adjustment.getId())
                    .build();
            outBaseOutin = this.saveStock(saveVO);
        }

        // 调库入库
        List<BaseOutinProductSaveVO> inProductSaveVOList = Lists.newArrayList();
        for (BaseOutinProductSaveVO outinProductSaveVO : model.getOutinProductList()) {
            outinProductSaveVO.setIsGift(false);
            outinProductSaveVO.setDesc(StrUtil.isBlank(model.getRemarks()) ? "调库入库" : model.getRemarks());
            outinProductSaveVO.setRemarks(StrUtil.isBlank(model.getRemarks()) ? "" : model.getRemarks());
            outinProductSaveVO.setAddTime(LocalDateTime.now());
            outinProductSaveVO.setNum(outinProductSaveVO.getNum());
            outinProductSaveVO.setNumType("1");
            outinProductSaveVO.setAddTime(LocalDateTime.now().plusSeconds(1));
            outinProductSaveVO.setWarehouseId(model.getInWarehouseId());
            inProductSaveVOList.add(outinProductSaveVO);
        }

        BaseOutin inBaseOutin = null;
        if (CollUtil.isNotEmpty(inProductSaveVOList)) {
            BaseOutinStockSaveVO saveVO = BaseOutinStockSaveVO.builder()
                    .type(OutinTypeEnum.ADJUSTMENT_IN.getCode())
                    .sourceType(model.getSourceType())
                    .warehouseId(model.getInWarehouseId()).billState(1)
                    .billDate(LocalDate.now())
                    .orgId(ContextUtil.getCurrentCompanyId())
                    .remarks(StrUtil.isBlank(model.getRemarks()) ? "调整入库" : model.getRemarks())
                    .employeeId(ContextUtil.getEmployeeId())
                    .isLockNum(false)
                    .isNum(true)
                    .state(model.getState())
                    .outinProductList(inProductSaveVOList)
                    .adjustmentId(adjustment.getId())
                    .build();
            inBaseOutin = this.saveStock(saveVO);
        }

        //新增操作日志
        if (ObjectUtil.isNotNull(inBaseOutin)) {
            StringBuilder desc = new StringBuilder();
            if (ObjectUtil.isNotNull(baseWarehouseMap.get(model.getInWarehouseId()))) {
                desc.append("调库调整-入库：").append(baseWarehouseMap.get(model.getInWarehouseId()).getName());
            }
            if (ObjectUtil.isNotNull(baseWarehouseMap.get(model.getOutWarehouseId()))) {
                desc.append("==>").append(baseWarehouseMap.get(model.getOutWarehouseId()).getName()).append(";");
            }
            for (BaseOutinProductSaveVO baseOutinProductSaveVO : inProductSaveVOList) {
                desc.append(baseProductMap.get(baseOutinProductSaveVO.getProductId()).getName()).append("*").append(baseOutinProductSaveVO.getNum()).append(",");
            }
            bizLogService.save(BaseBizLogSaveVO.builder()
                    .orgId(getCurrentCompanyId())
                    .description(desc.toString())
                    .bizModule(BizLogModuleEnum.PRODUCT_ADJUSTMENT_IN.getCode())
                    .type(BizLogTypeEnum.CREATED.getCode())
                    .employeeId(ContextUtil.getEmployeeId()).createdOrgId(getCurrentCompanyId())
                    .opearteTime(LocalDateTime.now())
                    .sourceId(adjustment.getId())
                    .remarks("调库入库")
                    .build());
        }
        if (ObjectUtil.isNotNull(outBaseOutin)) {
            StringBuilder desc = new StringBuilder();
            if (ObjectUtil.isNotNull(baseWarehouseMap.get(model.getInWarehouseId()))) {
                desc.append("调库调整-出库：").append(baseWarehouseMap.get(model.getInWarehouseId()).getName());
            }
            if (ObjectUtil.isNotNull(baseWarehouseMap.get(model.getOutWarehouseId()))) {
                desc.append("==>").append(baseWarehouseMap.get(model.getOutWarehouseId()).getName()).append(";");
            }
            for (BaseOutinProductSaveVO baseOutinProductSaveVO : outProductSaveVOList) {
                desc.append(baseProductMap.get(baseOutinProductSaveVO.getProductId()).getName()).append("*").append(baseOutinProductSaveVO.getNum()).append(",");
            }
            bizLogService.save(BaseBizLogSaveVO.builder()
                    .orgId(getCurrentCompanyId())
                    .description(desc.toString())
                    .bizModule(BizLogModuleEnum.PRODUCT_ADJUSTMENT_OUT.getCode())
                    .type(BizLogTypeEnum.CREATED.getCode())
                    .employeeId(ContextUtil.getEmployeeId())
                    .createdOrgId(getCurrentCompanyId())
                    .opearteTime(LocalDateTime.now())
                    .sourceId(adjustment.getId())
                    .remarks("调库出库")
                    .build());
        }
        return true;
    }

    @Override
    public Boolean updateAdjustmentStock(BaseOutinStockAdjustmentVO model) {

        ArgumentAssert.notEmpty(model.getOutinProductList(), "请选择商品");
        List<Long> productIds = model.getOutinProductList().stream()
                .map(BaseOutinProductSaveVO::getProductId).collect(Collectors.toList());
        // 转入仓库
        Map<Long, BaseProductStock> productStockInMap = baseProductStockManager.list(Wraps.<BaseProductStock>lbQ()
                        .in(BaseProductStock::getProductId, productIds)
                        .eq(BaseProductStock::getWarehouseId, model.getInWarehouseId()))
                .stream().collect(Collectors.toMap(BaseProductStock::getProductId, Function.identity()));
        // 转出仓库
        Map<Long, BaseProductStock> productStockOutMap = baseProductStockManager.list(Wraps.<BaseProductStock>lbQ()
                        .in(BaseProductStock::getProductId, productIds)
                        .eq(BaseProductStock::getWarehouseId, model.getOutWarehouseId()))
                .stream().collect(Collectors.toMap(BaseProductStock::getProductId, Function.identity()));

        Map<Long, BaseProduct> baseProductMap = baseProductManager.list(Wraps.<BaseProduct>lbQ()
                        .in(BaseProduct::getId, productIds))
                .stream().collect(Collectors.toMap(BaseProduct::getId, Function.identity()));

        Map<Long, BaseWarehouse> baseWarehouseMap = baseWarehouseService.list(Wraps.<BaseWarehouse>lbQ()
                        .in(BaseWarehouse::getId, Arrays.asList(model.getInWarehouseId(), model.getOutWarehouseId())))
                .stream().collect(Collectors.toMap(BaseWarehouse::getId, Function.identity()));

        BaseOutinAdjustmentUpdateVO updateVO = BeanPlusUtil.copyProperties(model, BaseOutinAdjustmentUpdateVO.class);
        BaseOutinAdjustment adjustment = baseOutinAdjustmentService.updateById(updateVO);

        ArgumentAssert.isTrue(Objects.equals(adjustment.getState(), 0), "只有待审核的才允许修改");

        // 删除之前的盘点详情
        superManager.remove(Wraps.<BaseOutin>lbQ().eq(BaseOutin::getAdjustmentId, updateVO.getId()));
        baseOutinProductManager.remove(Wraps.<BaseOutinProduct>lbQ().eq(BaseOutinProduct::getAdjustmentId, updateVO.getId()));


        List<BaseOutinProductSaveVO> outProductSaveVOList = Lists.newArrayList();
        for (BaseOutinProductSaveVO outinProductSaveVO : model.getOutinProductList()) {
            BaseProductStock baseProductStock = productStockOutMap.get(outinProductSaveVO.getProductId());
            ArgumentAssert.notNull(baseProductStock, "库存异常，请联系管理员");
            BaseProduct baseProduct = baseProductMap.get(outinProductSaveVO.getProductId());
            ArgumentAssert.notNull(baseProduct, "商品不存在，请联系管理员");
            outinProductSaveVO.setIsGift(false);
            outinProductSaveVO.setAdjustmentId(adjustment.getId());
            outinProductSaveVO.setAdjustmentValue(outinProductSaveVO.getNum());
            outinProductSaveVO.setDesc(StrUtil.isBlank(model.getRemarks()) ? "调库出库" : model.getRemarks());
            outinProductSaveVO.setRemarks(StrUtil.isBlank(model.getRemarks()) ? "" : model.getRemarks());
            outinProductSaveVO.setAddTime(LocalDateTime.now());
            outinProductSaveVO.setWarehouseId(model.getOutWarehouseId());
            //补充的数量
            int num = baseProductStock.getNum() - baseProductStock.getLockNum();
            if (num - outinProductSaveVO.getNum() >= 0) {
                outinProductSaveVO.setNum(outinProductSaveVO.getNum());
                outinProductSaveVO.setNumType("2");
                outinProductSaveVO.setAddTime(LocalDateTime.now().plusSeconds(1));
                outProductSaveVOList.add(outinProductSaveVO);
            } else {
                ArgumentAssert.isFalse(ObjectUtil.equal(1, 1),
                        "库存不足，请联系管理员");
            }
        }
        BaseOutin outBaseOutin = null;
        if (CollUtil.isNotEmpty(outProductSaveVOList)) {
            BaseOutinStockSaveVO saveVO = BaseOutinStockSaveVO.builder()
                    .type(OutinTypeEnum.ADJUSTMENT_OUT.getCode())
                    .sourceType(model.getSourceType())
                    .warehouseId(model.getOutWarehouseId()).billState(1)
                    .billDate(LocalDate.now())
                    .orgId(ContextUtil.getCurrentCompanyId())
                    .remarks(StrUtil.isBlank(model.getRemarks()) ? "" : model.getRemarks())
                    .employeeId(ContextUtil.getEmployeeId())
                    .isNum(true)
                    .isLockNum(false)
                    .state(model.getState())
                    .outinProductList(outProductSaveVOList)
                    .adjustmentId(adjustment.getId())
                    .build();
            outBaseOutin = this.saveStock(saveVO);
        }

        // 调库入库
        List<BaseOutinProductSaveVO> inProductSaveVOList = Lists.newArrayList();
        for (BaseOutinProductSaveVO outinProductSaveVO : model.getOutinProductList()) {
            outinProductSaveVO.setIsGift(false);
            outinProductSaveVO.setDesc(StrUtil.isBlank(model.getRemarks()) ? "调库入库" : model.getRemarks());
            outinProductSaveVO.setRemarks(StrUtil.isBlank(model.getRemarks()) ? "" : model.getRemarks());
            outinProductSaveVO.setAddTime(LocalDateTime.now());
            outinProductSaveVO.setNum(outinProductSaveVO.getNum());
            outinProductSaveVO.setNumType("1");
            outinProductSaveVO.setAddTime(LocalDateTime.now().plusSeconds(1));
            outinProductSaveVO.setWarehouseId(model.getInWarehouseId());
            inProductSaveVOList.add(outinProductSaveVO);
        }

        BaseOutin inBaseOutin = null;
        if (CollUtil.isNotEmpty(inProductSaveVOList)) {
            BaseOutinStockSaveVO saveVO = BaseOutinStockSaveVO.builder()
                    .type(OutinTypeEnum.ADJUSTMENT_IN.getCode())
                    .sourceType(model.getSourceType())
                    .warehouseId(model.getInWarehouseId()).billState(1)
                    .billDate(LocalDate.now())
                    .orgId(ContextUtil.getCurrentCompanyId())
                    .remarks(StrUtil.isBlank(model.getRemarks()) ? "调整入库" : model.getRemarks())
                    .employeeId(ContextUtil.getEmployeeId())
                    .isLockNum(false)
                    .isNum(true)
                    .state(model.getState())
                    .outinProductList(inProductSaveVOList)
                    .adjustmentId(adjustment.getId())
                    .build();
            inBaseOutin = this.saveStock(saveVO);
        }

        //新增操作日志
        if (ObjectUtil.isNotNull(inBaseOutin)) {
            StringBuilder desc = new StringBuilder();
            if (ObjectUtil.isNotNull(baseWarehouseMap.get(model.getInWarehouseId()))) {
                desc.append("修改调库调整-入库：").append(baseWarehouseMap.get(model.getInWarehouseId()).getName());
            }
            if (ObjectUtil.isNotNull(baseWarehouseMap.get(model.getOutWarehouseId()))) {
                desc.append("==>").append(baseWarehouseMap.get(model.getOutWarehouseId()).getName()).append(";");
            }
            for (BaseOutinProductSaveVO baseOutinProductSaveVO : inProductSaveVOList) {
                desc.append(baseProductMap.get(baseOutinProductSaveVO.getProductId()).getName()).append("*").append(baseOutinProductSaveVO.getNum()).append(",");
            }
            bizLogService.save(BaseBizLogSaveVO.builder()
                    .orgId(getCurrentCompanyId())
                    .description(desc.toString())
                    .bizModule(BizLogModuleEnum.PRODUCT_ADJUSTMENT_IN.getCode())
                    .type(BizLogTypeEnum.CREATED.getCode())
                    .employeeId(ContextUtil.getEmployeeId()).createdOrgId(getCurrentCompanyId())
                    .opearteTime(LocalDateTime.now())
                    .sourceId(adjustment.getId())
                    .remarks("调库入库")
                    .build());
        }
        if (ObjectUtil.isNotNull(outBaseOutin)) {
            StringBuilder desc = new StringBuilder();
            if (ObjectUtil.isNotNull(baseWarehouseMap.get(model.getInWarehouseId()))) {
                desc.append("修改调库调整-出库：").append(baseWarehouseMap.get(model.getInWarehouseId()).getName());
            }
            if (ObjectUtil.isNotNull(baseWarehouseMap.get(model.getOutWarehouseId()))) {
                desc.append("==>").append(baseWarehouseMap.get(model.getOutWarehouseId()).getName()).append(";");
            }
            for (BaseOutinProductSaveVO baseOutinProductSaveVO : outProductSaveVOList) {
                desc.append(baseProductMap.get(baseOutinProductSaveVO.getProductId()).getName()).append("*").append(baseOutinProductSaveVO.getNum()).append(",");
            }
            bizLogService.save(BaseBizLogSaveVO.builder()
                    .orgId(getCurrentCompanyId())
                    .description(desc.toString())
                    .bizModule(BizLogModuleEnum.PRODUCT_ADJUSTMENT_OUT.getCode())
                    .type(BizLogTypeEnum.CREATED.getCode())
                    .employeeId(ContextUtil.getEmployeeId())
                    .createdOrgId(getCurrentCompanyId())
                    .opearteTime(LocalDateTime.now())
                    .sourceId(adjustment.getId())
                    .remarks("调库出库")
                    .build());
        }
        return true;

    }

    @Override
    public Long noReviewedCount() {
        long count = superManager.count(Wraps.<BaseOutin>lbQ().eq(BaseOutin::getDeleteFlag, 0)
                .eq(BaseOutin::getState, BaseOutinStateEnum.NO_REVIEWED.getState())
                .eq(BaseOutin::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .in(BaseOutin::getType, Arrays.asList(OutinTypeEnum.PURCHASE_IN.getCode(), OutinTypeEnum.PURCHASE_OUT.getCode())));
        count += baseOutinStocktakingService.getSuperManager().count(Wraps.<BaseOutinStocktaking>lbQ()
                .eq(BaseOutinStocktaking::getDeleteFlag, 0)
                .eq(BaseOutinStocktaking::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(BaseOutinStocktaking::getState, BaseOutinStateEnum.NO_REVIEWED.getState()));
        count += baseOutinAdjustmentService.getSuperManager().count(Wraps.<BaseOutinAdjustment>lbQ()
                .eq(BaseOutinAdjustment::getDeleteFlag, 0)
                .eq(BaseOutinAdjustment::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(BaseOutinAdjustment::getState, BaseOutinStateEnum.NO_REVIEWED.getState()));
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean mergeOutin(List<String> sources, PosCash mergePosCash,
                              List<PosCashProduct> posCashProductList, String remark) {
        List<BaseOutinProduct> outinProductList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(sources)) {
            List<BaseOutin> baseOutinList = superManager.list(Wraps.<BaseOutin>lbQ()
                    .eq(BaseOutin::getType, OutinTypeEnum.SELL_OUT.getCode())
                    .eq(BaseOutin::getDeleteFlag, 0)
                    .in(BaseOutin::getCode, sources));
            if (CollUtil.isNotEmpty(baseOutinList)) {
                List<Long> baseOutinIds = baseOutinList.stream().map(BaseOutin::getId).collect(Collectors.toList());
                outinProductList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ()
                        .in(CollUtil.isNotEmpty(posCashProductList), BaseOutinProduct::getCashProductId, posCashProductList.stream()
                                .map(PosCashProduct::getOldCashProductId)
                                .filter(ObjectUtil::isNotNull)
                                .collect(Collectors.toList()))
                        .in(BaseOutinProduct::getOutinId, baseOutinIds));
            }
            if (StrUtil.isNotBlank(mergePosCash.getCode())) {
                BaseOutin baseOutin = superManager.getOne(Wraps.<BaseOutin>lbQ()
                        .eq(BaseOutin::getType, OutinTypeEnum.SELL_OUT.getCode())
                        .eq(BaseOutin::getDeleteFlag, 0)
                        .eq(BaseOutin::getCode, mergePosCash.getCode()).last("limit 1"));
                //无库存信息
                if (ObjectUtil.isNull(baseOutin)
                        && CollUtil.isEmpty(baseOutinList)) {
                    return true;
                }
                //目标有库存记录  待合并的无库存记录
                if (ObjectUtil.isNotNull(baseOutin)
                        && CollUtil.isEmpty(baseOutinList)) {
                    return true;
                }
                //目标库存无库存记录  待合并的有库存记录
                BaseOutin newBaseOutin = null;
                if (ObjectUtil.isNull(baseOutin)
                        && CollUtil.isNotEmpty(baseOutinList)) {
                    String desc = PosCashTypeEnum.get(mergePosCash.getType()).getDesc()
                            .concat("-")
                            .concat(PosCashBillTypeEnum.get(mergePosCash.getBillType()).getDesc())
                            .concat("-")
                            .concat(PosCashBillStateEnum.get(mergePosCash.getBillState()).getDesc());
                    newBaseOutin = BeanUtil.copyProperties(baseOutinList.get(0), BaseOutin.class);
                    newBaseOutin.setId(null);
                    newBaseOutin.setCode(mergePosCash.getCode());
                    newBaseOutin.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                    newBaseOutin.setEmployeeId(ContextUtil.getEmployeeId());
                    newBaseOutin.setUpdatedTime(null);
                    newBaseOutin.setRemarks(desc);
                    newBaseOutin.setDeleteFlag(0);
                    ArgumentAssert.isFalse(!superManager.save(newBaseOutin), "保存库存单失败");
                }
                if (ObjectUtil.isNotNull(baseOutin)) {
                    newBaseOutin = BeanUtil.copyProperties(baseOutin, BaseOutin.class);
                }
                if (CollUtil.isNotEmpty(outinProductList)) {
                    List<BaseOutinProduct> baseOutinProductList = BeanUtil.copyToList(outinProductList, BaseOutinProduct.class);
                    for (BaseOutinProduct outinProduct : baseOutinProductList) {
                        outinProduct.setDesc(outinProduct.getDesc().concat(StrUtil.isBlank(remark) ? "-合并至-" : remark)
                                .concat((mergePosCash.getType().equals(PosCashTypeEnum.START_TABLE.getCode())
                                        ? mergePosCash.getTableName() :
                                        PosCashTypeEnum.get(mergePosCash.getType()).getDesc())));
                        outinProduct.setUpdatedTime(LocalDateTime.now());
                        outinProduct.setDeleteFlag(1);
                    }
                    ArgumentAssert.isFalse(!baseOutinProductManager.updateBatchById(baseOutinProductList), "库存单操作失败");
                    for (BaseOutinProduct outinProduct : outinProductList) {
                        outinProduct.setId(null);
                        assert newBaseOutin != null;
                        outinProduct.setOutinId(newBaseOutin.getId());
                        outinProduct.setDeleteFlag(0);
                        outinProduct.setEmployeeId(ContextUtil.getEmployeeId());
                        outinProduct.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                        outinProduct.setUpdatedTime(LocalDateTime.now());
                        outinProduct.setDesc(outinProduct.getDesc().concat(StrUtil.isBlank(remark) ? "-合并至-" : remark)
                                .concat((mergePosCash.getType().equals(PosCashTypeEnum.START_TABLE.getCode())
                                        ? mergePosCash.getTableName() :
                                        PosCashTypeEnum.get(mergePosCash.getType()).getDesc())));
                        if (ObjectUtil.isNull(outinProduct.getCashProductId())) {
                            continue;
                        }
                        if (CollUtil.isEmpty(posCashProductList)) {
                            continue;
                        }
                        if (posCashProductList.stream().anyMatch(v -> ObjectUtil.isNotNull(v.getOldCashProductId())
                                && v.getOldCashProductId()
                                .equals(outinProduct.getCashProductId()))) {
                            outinProduct.setCashProductId(posCashProductList.stream().filter(v -> v.getOldCashProductId()
                                    .equals(outinProduct.getCashProductId())).findFirst().get().getId());
                        }
                    }
                    ArgumentAssert.isFalse(!baseOutinProductManager.saveBatch(outinProductList), "库存单操作失败");
                }
                if (CollUtil.isNotEmpty(baseOutinList)) {
                    for (BaseOutin outin : baseOutinList) {
                        outin.setUpdatedTime(LocalDateTime.now());
                        outin.setDeleteFlag(1);
                        outin.setRemarks(outin.getRemarks().concat("-合并至-")
                                .concat((mergePosCash.getType().equals(PosCashTypeEnum.START_TABLE.getCode())
                                        ? mergePosCash.getTableName() :
                                        PosCashTypeEnum.get(mergePosCash.getType()).getDesc())));
                    }

                    boolean b = superManager.updateBatchById(baseOutinList);
                    ArgumentAssert.isFalse(!b, "库存单操作失败");
                }
            }
        }
        return true;
    }

    @Override
    public void mergeOutinCheckWarehouse(List<String> sources, String mergeCode) {
        List<BaseOutin> baseOutinList = superManager.list(Wraps.<BaseOutin>lbQ()
                .eq(BaseOutin::getType, OutinTypeEnum.SELL_OUT.getCode())
                .eq(BaseOutin::getDeleteFlag, 0)
                .in(BaseOutin::getCode, sources));
        BaseOutin baseOutin = superManager.getOne(Wraps.<BaseOutin>lbQ()
                .eq(BaseOutin::getType, OutinTypeEnum.SELL_OUT.getCode())
                .eq(BaseOutin::getDeleteFlag, 0)
                .eq(BaseOutin::getCode, mergeCode).last("limit 1"));
        // 并单时, 如果销售仓不同, 不允许并桌
        BaseWarehouse warehouse = baseWarehouseService.getOneBySn();
        if (!CollUtil.isEmpty(baseOutinList)) {
            if (ObjectUtil.isNotNull(baseOutin)) {
                ArgumentAssert.isTrue(baseOutinList.stream().allMatch(s -> Objects.equals(s.getWarehouseId(), baseOutin.getWarehouseId())), "销售仓不同,不支持合并");
            }
            ArgumentAssert.isTrue(baseOutinList.stream().allMatch(s -> Objects.equals(s.getWarehouseId(), warehouse.getId())), "其他仓库的订单,不支持合并");
        }
        if (ObjectUtil.isNotNull(baseOutin)) {
            ArgumentAssert.isTrue(Objects.equals(baseOutin.getWarehouseId(), warehouse.getId()), "其他仓库的订单,该收银机不支持操作");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean splitOutin(List<String> sources, PosCash splitPosCash, List<PosCashProduct> posCashProductList, String remark) {
        // 查询执勤啊的订单
        List<BaseOutin> baseOutinList = superManager.list(Wraps.<BaseOutin>lbQ()
                .eq(BaseOutin::getType, OutinTypeEnum.SELL_OUT.getCode())
                .eq(BaseOutin::getDeleteFlag, 0)
                .in(BaseOutin::getCode, sources));

        List<Long> baseOutinIds = baseOutinList.stream().map(BaseOutin::getId).collect(Collectors.toList());
        List<BaseOutinProduct> outinProductList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ()
                .in(CollUtil.isNotEmpty(posCashProductList), BaseOutinProduct::getCashProductId, posCashProductList.stream()
                        .map(PosCashProduct::getOldCashProductId)
                        .filter(ObjectUtil::isNotNull)
                        .collect(Collectors.toList()))
                .in(BaseOutinProduct::getOutinId, baseOutinIds).groupBy(BaseOutinProduct::getProductId)
                .groupBy(BaseOutinProduct::getCashProductId));

        // 新增出库记录, 只需要 num 和净库存
        List<Long> productIdS = posCashProductList.stream().map(PosCashProduct::getProductId).collect(Collectors.toList());

        List<BaseProductStock> productStockList = baseProductStockManager.list(Wraps.<BaseProductStock>lbQ().in(BaseProductStock::getProductId, productIdS));

        for (BaseOutinProduct outinProduct : outinProductList) {
            PosCashProduct posCashProduct = posCashProductList.stream().filter(s -> Objects.equals(s.getProductId(), outinProduct.getProductId())
                    && Objects.equals(s.getOldCashProductId(), outinProduct.getCashProductId())).findFirst().get();
            BaseProductStock baseProductStock = productStockList.stream().filter(s -> Objects.equals(
                    s.getProductId(), outinProduct.getProductId()) && Objects.equals(s.getWarehouseId(), posCashProduct.getWarehouseId())).findFirst().get();

            outinProduct.setId(null);
            outinProduct.setNumType("1");
            outinProduct.setDeleteFlag(0);
            outinProduct.setNum(posCashProduct.getNum());

            // 将库存记录的库存数量和锁定数量进行更新
//            productStockList.stream().filter(s -> Objects.equals(
//                            s.getProductId(), outinProduct.getProductId()) && Objects.equals(s.getWarehouseId(), posCashProduct.getWarehouseId())).findFirst().get()
//                    .setNum(baseProductStock.getNum() + outinProduct.getNum());
//
//            baseProductStock = productStockList.stream().filter(s -> Objects.equals(
//                    s.getProductId(), outinProduct.getProductId()) && Objects.equals(s.getWarehouseId(), posCashProduct.getWarehouseId())).findFirst().get();

            baseProductStock.setLockNum(baseProductStock.getLockNum() - outinProduct.getNum());

            outinProduct.setLockNum(baseProductStock.getLockNum());
            outinProduct.setResidueNum(baseProductStock.getNum() - baseProductStock.getLockNum());
            outinProduct.setAmount(outinProduct.getPrice().multiply(new BigDecimal(outinProduct.getNum()))
                    .setScale(2, RoundingMode.HALF_UP));
            BigDecimal profitPrice = outinProduct.getPrice().multiply(new BigDecimal(outinProduct.getNum()))
                    .subtract(baseProductStock.getCostPrice().multiply(new BigDecimal(outinProduct.getNum())))
                    .setScale(2, RoundingMode.HALF_UP);
            outinProduct.setProfitPrice(profitPrice);
            outinProduct.setEmployeeId(ContextUtil.getEmployeeId());
            outinProduct.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
            outinProduct.setCreatedTime(null);
            outinProduct.setDesc(outinProduct.getDesc().concat(StrUtil.isBlank(remark) ? "-拆单-" : remark)
                    .concat((splitPosCash.getType().equals(PosCashTypeEnum.START_TABLE.getCode())
                            ? splitPosCash.getTableName() :
                            PosCashTypeEnum.get(splitPosCash.getType()).getDesc())));
            outinProduct.setCashProductId(posCashProduct.getOldCashProductId());
        }
        boolean b = baseOutinProductManager.saveBatch(outinProductList);
        ArgumentAssert.isFalse(!b, "库存单操作失败");

        // 新增一条出库记录
        String desc = PosCashTypeEnum.get(splitPosCash.getType()).getDesc()
                .concat("-")
                .concat(PosCashBillTypeEnum.get(splitPosCash.getBillType()).getDesc())
                .concat("-")
                .concat(PosCashBillStateEnum.get(splitPosCash.getBillState()).getDesc());
        BaseOutin newBaseOutin = BeanUtil.copyProperties(baseOutinList.get(0), BaseOutin.class);
        newBaseOutin.setId(null);
        newBaseOutin.setCode(splitPosCash.getCode());
        newBaseOutin.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        newBaseOutin.setEmployeeId(ContextUtil.getEmployeeId());
        newBaseOutin.setUpdatedTime(null);
        newBaseOutin.setRemarks(desc);
        newBaseOutin.setDeleteFlag(0);
        ArgumentAssert.isFalse(!superManager.save(newBaseOutin), "保存库存单失败");

        for (BaseOutinProduct outinProduct : outinProductList) {
            PosCashProduct posCashProduct = posCashProductList.stream().filter(s -> Objects.equals(s.getProductId(), outinProduct.getProductId())
                    && Objects.equals(s.getOldCashProductId(), outinProduct.getCashProductId())).findFirst().get();
            BaseProductStock baseProductStock = productStockList.stream().filter(s -> Objects.equals(
                    s.getProductId(), outinProduct.getProductId()) && Objects.equals(s.getWarehouseId(), posCashProduct.getWarehouseId())).findFirst().get();

            outinProduct.setId(null);
            outinProduct.setOutinId(newBaseOutin.getId());
            outinProduct.setNumType("2");
            outinProduct.setDeleteFlag(0);
            outinProduct.setNum(posCashProduct.getNum());

            // 将库存记录的库存数量和锁定数量进行更新
//            productStockList.stream().filter(s -> Objects.equals(
//                            s.getProductId(), outinProduct.getProductId()) && Objects.equals(s.getWarehouseId(), posCashProduct.getWarehouseId())).findFirst().get()
//                    .setNum(baseProductStock.getNum() - outinProduct.getNum());
//            baseProductStock = productStockList.stream().filter(s -> Objects.equals(
//                    s.getProductId(), outinProduct.getProductId()) && Objects.equals(s.getWarehouseId(), posCashProduct.getWarehouseId())).findFirst().get();

            baseProductStock.setLockNum(baseProductStock.getLockNum() + outinProduct.getNum());

            outinProduct.setLockNum(baseProductStock.getLockNum());
            outinProduct.setResidueNum(baseProductStock.getNum() - baseProductStock.getLockNum());
            outinProduct.setAmount(outinProduct.getPrice().multiply(new BigDecimal(outinProduct.getNum()))
                    .setScale(2, RoundingMode.HALF_UP));
            BigDecimal profitPrice = outinProduct.getPrice().multiply(new BigDecimal(outinProduct.getNum()))
                    .subtract(baseProductStock.getCostPrice().multiply(new BigDecimal(outinProduct.getNum())))
                    .setScale(2, RoundingMode.HALF_UP);
            outinProduct.setProfitPrice(profitPrice);
            outinProduct.setEmployeeId(ContextUtil.getEmployeeId());
            outinProduct.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
            outinProduct.setCreatedTime(null);
            outinProduct.setDesc(outinProduct.getDesc().concat(StrUtil.isBlank(remark) ? "-拆单(新单)-" : remark)
                    .concat((splitPosCash.getType().equals(PosCashTypeEnum.START_TABLE.getCode())
                            ? splitPosCash.getTableName() :
                            PosCashTypeEnum.get(splitPosCash.getType()).getDesc())));
            outinProduct.setCashProductId(posCashProduct.getId());
        }
        ArgumentAssert.isFalse(!baseOutinProductManager.saveBatch(outinProductList), "库存单操作失败");

        return true;
    }
}


