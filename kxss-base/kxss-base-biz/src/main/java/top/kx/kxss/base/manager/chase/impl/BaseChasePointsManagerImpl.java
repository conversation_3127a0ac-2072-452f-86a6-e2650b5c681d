package top.kx.kxss.base.manager.chase.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.chase.BaseChasePoints;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.chase.BaseChasePointsManager;
import top.kx.kxss.base.mapper.chase.BaseChasePointsMapper;

/**
 * <p>
 * 通用业务实现类
 * 追分设置
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-20 11:07:10
 * @create [2025-06-20 11:07:10] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseChasePointsManagerImpl extends SuperManagerImpl<BaseChasePointsMapper, BaseChasePoints> implements BaseChasePointsManager {

}


