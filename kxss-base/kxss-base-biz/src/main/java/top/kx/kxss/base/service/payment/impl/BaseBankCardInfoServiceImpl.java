package top.kx.kxss.base.service.payment.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.kxss.base.entity.payment.BaseBankCardInfo;
import top.kx.kxss.base.manager.payment.BaseBankCardInfoManager;
import top.kx.kxss.base.service.payment.BaseBankCardInfoService;
import top.kx.kxss.base.vo.query.payment.BaseBankCardInfoPageQuery;
import top.kx.kxss.base.vo.result.payment.BaseBankCardInfoResultVO;
import top.kx.kxss.base.vo.save.payment.BaseBankCardInfoSaveVO;
import top.kx.kxss.base.vo.update.payment.BaseBankCardInfoUpdateVO;
import top.kx.kxss.common.constant.DsConstant;

/**
 * <p>
 * 业务实现类
 * 银行卡信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-04 15:36:19
 * @create [2025-07-04 15:36:19] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class BaseBankCardInfoServiceImpl extends SuperServiceImpl<BaseBankCardInfoManager, Long, BaseBankCardInfo, BaseBankCardInfoSaveVO,
    BaseBankCardInfoUpdateVO, BaseBankCardInfoPageQuery, BaseBankCardInfoResultVO> implements BaseBankCardInfoService {


    @Override
    public boolean save(BaseBankCardInfo build) {
        return superManager.save(build);
    }
}


