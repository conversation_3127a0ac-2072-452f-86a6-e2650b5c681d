package top.kx.kxss.base.service.commission.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.beust.jcommander.internal.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.R;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.exception.BizException;
import top.kx.basic.exception.code.ExceptionCode;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.jackson.JsonUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.app.query.PerformanceCommissionConsumeQuery;
import top.kx.kxss.base.entity.commission.*;
import top.kx.kxss.base.entity.common.BaseDict;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.product.category.BaseProductCategory;
import top.kx.kxss.base.entity.recalc.BaseRecalc;
import top.kx.kxss.base.entity.recalc.BaseRecalcDetail;
import top.kx.kxss.base.entity.service.BaseServicePersonal;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.manager.commission.*;
import top.kx.kxss.base.manager.recalc.BaseRecalcDetailManager;
import top.kx.kxss.base.manager.recalc.BaseRecalcManager;
import top.kx.kxss.base.manager.service.BaseServicePersonalManager;
import top.kx.kxss.base.manager.user.BaseEmployeeManager;
import top.kx.kxss.base.service.commission.BaseCommissionService;
import top.kx.kxss.base.service.performance.BasePerformanceService;
import top.kx.kxss.base.service.product.BaseProductService;
import top.kx.kxss.base.service.product.category.BaseProductCategoryService;
import top.kx.kxss.base.service.recalc.BaseRecalcDetailService;
import top.kx.kxss.base.service.recalc.BaseRecalcService;
import top.kx.kxss.base.vo.query.CashIdQueryVO;
import top.kx.kxss.base.vo.query.commission.BaseCommissionPageQuery;
import top.kx.kxss.base.vo.result.commission.*;
import top.kx.kxss.base.vo.save.commission.*;
import top.kx.kxss.base.vo.save.recalc.BaseRecalcSaveVO;
import top.kx.kxss.base.vo.update.commission.BaseCommissionUpdateVO;
import top.kx.kxss.base.vo.update.recalc.BaseRecalcUpdateVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.RabbitMqConstant;
import top.kx.kxss.model.enumeration.base.*;
import top.kx.kxss.pos.PosOrderApi;
import top.kx.kxss.wxapp.api.custom.CustomApi;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <p>
 * 业务实现类
 * 提成基本信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-11-27 20:21:53
 * @create [2023-11-27 20:21:53] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class BaseCommissionServiceImpl extends SuperServiceImpl<BaseCommissionManager, Long, BaseCommission, BaseCommissionSaveVO,
        BaseCommissionUpdateVO, BaseCommissionPageQuery, BaseCommissionResultVO> implements BaseCommissionService {

    @Autowired
    private BaseCommissionPositionManager commissionPositionManager;

    @Autowired
    private BaseCommissionServiceManager commissionServiceManager;

    @Autowired
    private BaseCommissionSettingManager commissionSettingManager;
    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private EchoService echoService;
    private final BaseCommissionApplyManager commissionApplyManager;
    private final BaseCommissionExcludeManager commissionExcludeManager;
    private final BaseProductCategoryService productCategoryService;
    private final BaseProductService productService;
    private final BaseCommissionEmployeeManager commissionEmployeeManager;
    private final CustomApi customApi;
    private final PosOrderApi posOrderApi;
    private final BasePerformanceService basePerformanceService;
    private final BaseRecalcManager baseRecalcManager;
    private final BaseRecalcDetailManager baseRecalcDetailManager;
    private final RabbitTemplate template;
    private final BaseCommissionEquityManager commissionEquityManager;
    private final BaseEmployeeManager baseEmployeeManager;
    private final BaseServicePersonalManager baseServicePersonalManager;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseCommission saveCommission(BaseCommissionSaveVO model) {
        ArgumentAssert.isFalse(checkName(model.getName(), model.getType(), null), "名称已存在");
        ArgumentAssert.isTrue(CollUtil.isNotEmpty(model.getSettingList()), "提成设置不能为空");
        checkParam(model);
        // 查询最新的排序值
        if (Objects.nonNull(model.getSort())) {
            BaseCommission baseCommission = superManager.getOne(Wraps.<BaseCommission>lbQ()
                    .eq(SuperEntity::getDeleteFlag, 0)
                    .eq(BaseCommission::getType, model.getType())
                    .eq(BaseCommission::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                    .eq(BaseCommission::getSort, model.getSort()).last("limit 1"));
            ArgumentAssert.isNull(baseCommission, "优先级不允许重复");
        } else {
            BaseCommission baseCommission = superManager.getOne(Wraps.<BaseCommission>lbQ().eq(SuperEntity::getDeleteFlag, 0).orderByDesc(BaseCommission::getSort).last("limit 1"));
            model.setSort(baseCommission == null ? 10 : baseCommission.getSort() + 10);
        }
        if (StringUtils.equals(model.getValidityType(), "0")) {
            model.setValidityStartDate(null);
            model.setValidityEndDate(null);
        }
        BaseCommission baseCommission = save(model);
        saveCommissionSetting(model.getSettingList(), baseCommission, model.getExcludeProductList());
        saveCommissionService(model.getServiceIds(), baseCommission);
        saveCommissionPosition(model.getPositionSaveVOList(), baseCommission);
        saveCommissionEmployee(model.getEmployeeSaveVOList(), baseCommission);
        return baseCommission;
    }

    private static void checkParam(BaseCommissionSaveVO model) {
        if (StringUtils.equals(CommissionTypeEnum.CLOCK.getCode(), model.getType())) {
            ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getCalculationMethod()), "请选择提成计算方式");
            ArgumentAssert.isTrue(CollUtil.isNotEmpty(model.getServiceIds()), "请选择服务项目");
        }
        if (StringUtils.equals(CommissionTypeEnum.RECHARGE.getCode(), model.getType())) {
            ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getCalculationMethod()), "请选择提成计算方式");
        }
        if (StringUtils.equals(CommissionTypeEnum.PRODUCT_SALE.getCode(), model.getType())) {
            ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getApplyProductType()), "请选择商品范围");
        }
        if (StringUtils.equals(CommissionTypeEnum.STORE_TURNOVER.getCode(), model.getType())) {
            ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getSchemeType()), "请选择提成方案类型");
            ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getCycle()), "请选择核算周期");
            ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getCalculationMethod()), "请选择提成计算方式");
            ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getCalculationMethodRule()), "请选择详细计算规则");
            ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getDistributionType()), "请选择提成分配方式");
            // 岗位分配
            if (StringUtils.equals(model.getDistributionType(), CommissionDistributeTypeEnum.POSITION.getCode())) {
                ArgumentAssert.isTrue(CollUtil.isNotEmpty(model.getPositionSaveVOList()), "请选择岗位");
                // 求和岗位的比例
                ArgumentAssert.isTrue(model.getPositionSaveVOList().stream().map(BaseCommissionPositionSaveVO::getRatio).reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(new BigDecimal(100)) == 0, "岗位分配比例之和必须为100");
                model.setEmployeeSaveVOList(null);
                return;
            }
            // 员工分配
            model.setPositionSaveVOList(null);
            ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getDistributionRatio()), "请选择提成分配比例");
            if (StringUtils.equals(model.getDistributionRatio(), "1")) {
                ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getDistributionRange()), "请选择提成分适用员工");
                if (StringUtils.equals(model.getDistributionRange(), "2")) {
                    ArgumentAssert.isTrue(CollUtil.isNotEmpty(model.getEmployeeSaveVOList()), "请选择员工");
                }
                return;
            }
            ArgumentAssert.isTrue(CollUtil.isNotEmpty(model.getEmployeeSaveVOList()), "请选择员工");
            ArgumentAssert.isTrue(model.getEmployeeSaveVOList().stream().map(BaseCommissionEmployeeSaveVO::getRatio).reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(new BigDecimal(100)) == 0, "员工分配比例之和必须为100");
        }
    }

    private static void checkUpdateParam(BaseCommissionUpdateVO model) {
        if (StringUtils.equals(CommissionTypeEnum.CLOCK.getCode(), model.getType())) {
            ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getCalculationMethod()), "请选择提成计算方式");
            ArgumentAssert.isTrue(CollUtil.isNotEmpty(model.getServiceIds()), "请选择服务项目");
        }
        if (StringUtils.equals(CommissionTypeEnum.RECHARGE.getCode(), model.getType())) {
            ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getCalculationMethod()), "请选择提成计算方式");
        }
        if (StringUtils.equals(CommissionTypeEnum.PRODUCT_SALE.getCode(), model.getType())) {
            ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getApplyProductType()), "请选择商品范围");
        }
        if (StringUtils.equals(CommissionTypeEnum.STORE_TURNOVER.getCode(), model.getType())) {
            ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getSchemeType()), "请选择提成方案类型");
            ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getCycle()), "请选择核算周期");
            ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getCalculationMethod()), "请选择提成计算方式");
            ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getCalculationMethodRule()), "请选择详细计算规则");
            ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getDistributionType()), "请选择提成分配方式");
            // 岗位分配
            if (StringUtils.equals(model.getDistributionType(), CommissionDistributeTypeEnum.POSITION.getCode())) {
                ArgumentAssert.isTrue(CollUtil.isNotEmpty(model.getPositionSaveVOList()), "请选择岗位");
                // 求和岗位的比例
                ArgumentAssert.isTrue(model.getPositionSaveVOList().stream().map(BaseCommissionPositionSaveVO::getRatio).reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(new BigDecimal(100)) == 0, "岗位分配比例之和必须为100");
                model.setEmployeeSaveVOList(null);
                return;
            }
            // 员工分配
            model.setPositionSaveVOList(null);
            ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getDistributionRatio()), "请选择提成分配比例");
            if (StringUtils.equals(model.getDistributionRatio(), "1")) {
                ArgumentAssert.isTrue(StringUtils.isNotBlank(model.getDistributionRange()), "请选择提成分适用员工");
                if (StringUtils.equals(model.getDistributionRange(), "2")) {
                    ArgumentAssert.isTrue(CollUtil.isNotEmpty(model.getEmployeeSaveVOList()), "请选择员工");
                }
                return;
            }
            ArgumentAssert.isTrue(CollUtil.isNotEmpty(model.getEmployeeSaveVOList()), "请选择员工");
            ArgumentAssert.isTrue(model.getEmployeeSaveVOList().stream().map(BaseCommissionEmployeeSaveVO::getRatio).reduce(BigDecimal.ZERO, BigDecimal::add).compareTo(new BigDecimal(100)) == 0, "员工分配比例之和必须为100");
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseCommission updateCommission(BaseCommissionUpdateVO model) {
        ArgumentAssert.isFalse(checkName(model.getName(), model.getType(), model.getId()), "名称已存在");
        // 查询最新的排序值
        if (Objects.nonNull(model.getSort())) {
            BaseCommission baseCommission = superManager.getOne(Wraps.<BaseCommission>lbQ().eq(SuperEntity::getDeleteFlag, 0)
                    .eq(BaseCommission::getType, model.getType())
                    .eq(BaseCommission::getCreatedOrgId, model.getId())
                    .eq(BaseCommission::getSort, model.getSort()).ne(SuperEntity::getId, model.getId()).last("limit 1"));
            ArgumentAssert.isNull(baseCommission, "优先级不允许重复");
        } else {
            BaseCommission baseCommission = superManager.getOne(Wraps.<BaseCommission>lbQ().eq(SuperEntity::getDeleteFlag, 0).orderByDesc(BaseCommission::getSort).last("limit 1"));
            model.setSort(baseCommission == null ? 10 : baseCommission.getSort() + 10);
        }
        if (StringUtils.equals(model.getValidityType(), "0")) {
            model.setValidityStartDate(null);
            model.setValidityEndDate(null);
        }
        checkUpdateParam(model);
        BaseCommission baseCommission = updateById(model);
        saveCommissionSetting(model.getSettingList(), baseCommission, model.getExcludeProductList());
        saveCommissionService(model.getServiceIds(), baseCommission);
        saveCommissionPosition(model.getPositionSaveVOList(), baseCommission);
        saveCommissionEmployee(model.getEmployeeSaveVOList(), baseCommission);
        return baseCommission;
    }

    @Override
    public BaseCommissionResultVO getDetail(Long aLong) {
        BaseCommission baseCommission = superManager.getById(aLong);
        if (ObjectUtil.isNull(baseCommission)) {
            return null;
        }
        BaseCommissionResultVO baseCommissionResultVO = BeanUtil.copyProperties(baseCommission, BaseCommissionResultVO.class);
        baseCommissionResultVO.setServiceIds(commissionServiceManager.listObjs(Wraps.<top.kx.kxss.base.entity.commission.BaseCommissionService>lbQ()
                        .select(top.kx.kxss.base.entity.commission.BaseCommissionService::getServiceId)
                        .eq(top.kx.kxss.base.entity.commission.BaseCommissionService::getCommissionId, baseCommission.getId()),
                Convert::toLong));

        baseCommissionResultVO.setPositionIds(commissionPositionManager.listObjs(Wraps.<BaseCommissionPosition>lbQ()
                        .select(BaseCommissionPosition::getPositionId)
                        .eq(BaseCommissionPosition::getCommissionId, baseCommission.getId()),
                Convert::toLong));
        List<BaseCommissionApply> applyList = commissionApplyManager.list(Wraps.<BaseCommissionApply>lbQ()
                .eq(BaseCommissionApply::getCommissionId, baseCommission.getId())
                .eq(BaseCommissionApply::getDeleteFlag, 0)
        );
        List<BaseCommissionExclude> excludeList = commissionExcludeManager.list(Wraps.<BaseCommissionExclude>lbQ()
                .eq(BaseCommissionExclude::getCommissionId, baseCommission.getId())
                .eq(BaseCommissionExclude::getDeleteFlag, 0)
        );
        List<BaseCommissionEquity> commissionEquityList = commissionEquityManager.list(Wraps.<BaseCommissionEquity>lbQ().eq(BaseCommissionEquity::getCommissionId, baseCommission.getId()));
        List<BaseCommissionEquityResultVO> equityResultVOList = BeanUtil.copyToList(commissionEquityList, BaseCommissionEquityResultVO.class);
        List<BaseCommissionApplyResultVO> applyResultVOList = BeanUtil.copyToList(applyList, BaseCommissionApplyResultVO.class);
        List<BaseCommissionExcludeResultVO> excludeResultVOList = BeanUtil.copyToList(excludeList, BaseCommissionExcludeResultVO.class);
        List<BaseCommissionSetting> commissionSettingList = commissionSettingManager.list(Wraps.<BaseCommissionSetting>lbQ()
                .eq(BaseCommissionSetting::getCommissionId, baseCommission.getId()));
        List<BaseCommissionSettingResultVO> settingResultVOList = BeanUtil.copyToList(commissionSettingList, BaseCommissionSettingResultVO.class);
        handlerEquityResultVOList(settingResultVOList, equityResultVOList, applyResultVOList, excludeResultVOList);
        handlerApplyResultVOList(baseCommissionResultVO, settingResultVOList, applyResultVOList);
        handlerExcludeResultVOList(baseCommissionResultVO, excludeResultVOList);
        handlerPositionResultVOList(baseCommissionResultVO);
        handlerEmployeeResultVOList(baseCommissionResultVO);
        echoService.action(settingResultVOList);
        baseCommissionResultVO.setSettingList(settingResultVOList);
        return baseCommissionResultVO;
    }

    private void handlerEquityResultVOList(List<BaseCommissionSettingResultVO> settingResultVOList, List<BaseCommissionEquityResultVO> equityResultVOList, List<BaseCommissionApplyResultVO> applyResultVOList, List<BaseCommissionExcludeResultVO> excludeResultVOList) {
        if (CollUtil.isEmpty(equityResultVOList)) {
            return;
        }
        Map<Long, List<BaseCommissionEquityResultVO>> equityMap = equityResultVOList.stream().collect(Collectors.groupingBy(BaseCommissionEquityResultVO::getCommissionSettingId));
        for (BaseCommissionSettingResultVO baseCommissionSettingResultVO : settingResultVOList) {
            List<BaseCommissionEquityResultVO> equityList = equityMap.get(baseCommissionSettingResultVO.getId());
            if (CollUtil.isNotEmpty(equityList)) {
                equityList.forEach(s -> {
                    if (CollUtil.isNotEmpty(applyResultVOList)) {
                        List<BaseCommissionApplyResultVO> applyList = applyResultVOList.stream().filter(v -> Objects.equals(v.getCommissionSettingId(), baseCommissionSettingResultVO.getId()) && Objects.equals(v.getEquityId(), s.getId())).collect(Collectors.toList());
                        s.setApplyResultVOList(applyList);
                    }
                    if (CollUtil.isNotEmpty(excludeResultVOList)) {
                        List<BaseCommissionExcludeResultVO> excludeList = excludeResultVOList.stream().filter(v -> Objects.equals(v.getCommissionSettingId(), baseCommissionSettingResultVO.getId()) && Objects.equals(v.getEquityId(), s.getId())).collect(Collectors.toList());
                        s.setExcludeResultVOList(excludeList);
                    }

                });
                baseCommissionSettingResultVO.setEquityResultVOList(equityList);
            }
        }
    }

    /**
     * 检测门店名称是否可用
     *
     * @param name 门店名称
     * @return java.lang.Boolean
     */
    @Override
    public Boolean checkName(String name, String type, Long id) {
        return superManager.count(Wraps.<BaseCommission>lbQ().ne(BaseCommission::getId, id)
                .eq(BaseCommission::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(BaseCommission::getType, type).eq(BaseCommission::getName, name)) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean retryCommission(BaseCommissionRetryVO query) {
        boolean lock = false;
        try {
            lock = distributedLock.lock(ContextUtil.getTenantId() + "_" + ContextUtil.getCurrentCompanyId() + "_RETRY_COMMISSION", 0);
            if (!lock) {
                throw BizException.wrap(ExceptionCode.REQUEST_BUSY);
            }

            // 判断是否存在重算中的订单
            BaseRecalc baseRecalcEntity = baseRecalcManager.getOne(Wraps.<BaseRecalc>lbQ()
                    .eq(BaseRecalc::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                    .eq(BaseRecalc::getType, "COMMISSION")
                    .in(BaseRecalc::getStatus, Arrays.asList("INIT", "PROCESSING"))
                    .last("limit 1"));
            ArgumentAssert.isNull(baseRecalcEntity, "存在未完成的重算任务, 请稍后再试");

            // 查询有没有提成设置
            List<BaseCommission> commissionList = superManager.list(Wraps.<BaseCommission>lbQ()
                    .eq(SuperEntity::getDeleteFlag, 0)
                    .eq(BaseCommission::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                    .eq(BaseCommission::getState, true));
            ArgumentAssert.isTrue(CollUtil.isNotEmpty(commissionList), "未设置提成规则，或提成规则已禁用，无需重算");

            R<DataOverviewQuery> storeTimeR = customApi.getStoreTime(query);
            log.info("处理报表中时间,返回结果:{}", storeTimeR);
            ArgumentAssert.isTrue(storeTimeR.getIsSuccess(), storeTimeR.getMsg());
            query.setStartDate(storeTimeR.getData().getStartDate());
            query.setEndDate(storeTimeR.getData().getEndDate());
            // 查询订单
            CashIdQueryVO queryVO = new CashIdQueryVO();
            queryVO.setStartDate(query.getStartDate());
            queryVO.setEndDate(query.getEndDate());
            queryVO.setBillStateList(Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(), PosCashBillStateEnum.PART_REFUND.getCode(), PosCashBillStateEnum.REFUNDED.getCode()));
            if (CollUtil.isNotEmpty(query.getCommissionTypeList()) && query.getCommissionTypeList().size() == 1 && query.getCommissionTypeList().contains(CommissionTypeEnum.RECHARGE.getCode())) {
                queryVO.setTypeList(Collections.singletonList(PosCashTypeEnum.RECHARGE.getCode()));
            } else {
                queryVO.setTypeList(Arrays.asList(PosCashTypeEnum.START_TABLE.getCode(), PosCashTypeEnum.SHOPPING.getCode(), PosCashTypeEnum.RECHARGE.getCode()));
            }
            R<List<Long>> idsR = posOrderApi.getIds(queryVO);
            log.info("处理报表中订单,返回结果:{}", idsR);
            List<Long> ids = idsR.getData();
            if (CollUtil.isEmpty(ids)) {
                log.info("处理报表中订单,没有数据, 无需开始定时任务");
                return true;
            }
            ids = ids.stream().distinct().collect(Collectors.toList());

            // 1. 创建重算任务主记录
            String taskCode = "CM" + randomOrderCode();
            BaseRecalc baseRecalc = BaseRecalc.builder()
                    .type("COMMISSION")
                    .code(taskCode)
                    .status("INIT")
                    .employeeId(ContextUtil.getEmployeeId())
                    .commissionType(String.join(",", query.getCommissionTypeList()))
                    .startTime(DateUtil.parseLocalDateTime(query.getStartDate()))
                    .endTime(DateUtil.parseLocalDateTime(query.getEndDate()))
                    .totalCount(ids.size())
                    .completedCount(0)
                    .successCount(0)
                    .failedCount(0)
                    .createdOrgId(ContextUtil.getCurrentCompanyId())
                    .build();

            Boolean flag = baseRecalcManager.save(baseRecalc);
            log.info("创建重算任务主记录成功, taskCode: {}, recalcId: {}", taskCode, baseRecalc.getId());

            // 2. 批量保存明细记录
            List<BaseRecalcDetail> detailSaveVOList = ids.stream()
                    .map(id -> BaseRecalcDetail.builder()
                            .recalcId(baseRecalc.getId())
                            .sourceId(id)
                            .status("WAITING")
                            .retryCount(0)
                            .createdOrgId(ContextUtil.getCurrentCompanyId())
                            .build())
                    .collect(Collectors.toList());

            baseRecalcDetailManager.saveBatch(detailSaveVOList);
            log.info("批量保存重算明细记录成功, 总数: {}", detailSaveVOList.size());

            // 3. 更新主表状态为处理中
            baseRecalc.setStatus("PROCESSING");
            baseRecalcManager.updateById(baseRecalc);

            // 4. 分批发送MQ消息，每次200条
            int batchSize = 200;
            List<Long> finalIds = ids;
            List<List<Long>> groupedIds = IntStream.range(0, (ids.size() + batchSize - 1) / batchSize)
                    .mapToObj(i -> finalIds.subList(i * batchSize, Math.min((i + 1) * batchSize, finalIds.size())))
                    .collect(Collectors.toList());

            log.info("开始分批发送MQ消息, 总批次: {}, 每批数量: {}", groupedIds.size(), batchSize);

            for (int i = 0; i < groupedIds.size(); i++) {
                List<Long> batchIds = groupedIds.get(i);
                PerformanceCommissionConsumeQuery consumeQuery = PerformanceCommissionConsumeQuery.builder()
                        .posCashIds(batchIds)
                        .recalcId(baseRecalc.getId())
                        .tenantId(ContextUtil.getTenantId())
                        .orgId(ContextUtil.getCurrentCompanyId())
                        .build();

                // 发送MQ消息
                template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.PERFORMANCE_COMMISSION,
                        JsonUtil.toJson(consumeQuery));

                log.info("发送第{}批MQ消息成功, 包含订单数: {}", i + 1, batchIds.size());
            }
            log.info("提成重算任务创建完成, taskCode: {}, 总订单数: {}, 分批数: {}", taskCode, ids.size(), groupedIds.size());
            return true;
        } finally {
            if (lock) {
                distributedLock.releaseLock(ContextUtil.getTenantId() + "_" + ContextUtil.getCurrentCompanyId() + "_RETRY_COMMISSION");
            }
        }
    }

    @Override
    public Boolean updateStatus(BaseCommissionUpdateStatusVO query) {
        return superManager.update(Wraps.<BaseCommission>lbU()
                .set(BaseCommission::getState, query.getState())
                .eq(BaseCommission::getId, query.getId()));
    }

    /**
     * 配置
     *
     * @param settingList
     * @param baseCommission
     */
    private void saveCommissionSetting(List<BaseCommissionSettingSaveVO> settingList, BaseCommission baseCommission, List<Long> excludeProductList) {
        if (CollUtil.isEmpty(settingList)) {
            return;
        }
        commissionSettingManager.remove(Wraps.<BaseCommissionSetting>lbU()
                .eq(BaseCommissionSetting::getCommissionId, baseCommission.getId()));
        commissionEquityManager.remove(Wraps.<BaseCommissionEquity>lbQ().eq(BaseCommissionEquity::getCommissionId, baseCommission.getId()));
        commissionApplyManager.remove(Wraps.<BaseCommissionApply>lbQ().eq(BaseCommissionApply::getCommissionId, baseCommission.getId()));
        commissionExcludeManager.remove(Wraps.<BaseCommissionExclude>lbQ().eq(BaseCommissionExclude::getCommissionId, baseCommission.getId()));
        for (BaseCommissionSettingSaveVO baseCommissionSettingSaveVO : settingList) {
            BaseCommissionSetting setting = BaseCommissionSetting.builder()
                    .commissionId(baseCommission.getId()).amount(baseCommissionSettingSaveVO.getAmount())
                    .beginAmount(baseCommissionSettingSaveVO.getBeginAmount() == null ? BigDecimal.ZERO : baseCommissionSettingSaveVO.getBeginAmount())
                    .endAmount(baseCommissionSettingSaveVO.getEndAmount() == null ? BigDecimal.ZERO : baseCommissionSettingSaveVO.getEndAmount())
                    .clockType(baseCommissionSettingSaveVO.getClockType()).rechargeType(baseCommissionSettingSaveVO.getRechargeType()).isRegular(baseCommissionSettingSaveVO.getIsRegular())
                    .createdOrgId(ContextUtil.getCurrentCompanyId())
                    .build();
            commissionSettingManager.save(setting);
            // 商品特殊处理
            if (StringUtils.equals(baseCommission.getType(), CommissionTypeEnum.PRODUCT_SALE.getCode())) {
                handlerItems(setting, baseCommissionSettingSaveVO.getApplyProductBizId(), EquityTypeEnum.PRODUCT.getCode());
            } else {
                saveEquity(setting, baseCommissionSettingSaveVO.getEquitySaveVOList());
            }
        }
        // 商品特殊处理
        if (StringUtils.equals(baseCommission.getType(), CommissionTypeEnum.PRODUCT_SALE.getCode())) {
            saveExclude(excludeProductList, baseCommission, EquityTypeEnum.PRODUCT.getCode());
        }
    }

    private void saveEquity(BaseCommissionSetting setting, List<BaseCommissionEquitySaveVO> equitySaveVOList) {
        List<BaseCommissionApply> applyList = Lists.newArrayList();
        List<BaseCommissionExclude> excludeList = Lists.newArrayList();
        if (CollUtil.isEmpty(equitySaveVOList)) {
            return;
        }
        for (BaseCommissionEquitySaveVO baseCommissionEquitySaveVO : equitySaveVOList) {
            BaseCommissionEquity equity = BaseCommissionEquity.builder()
                    .commissionId(setting.getCommissionId())
                    .commissionSettingId(setting.getId())
                    .type(baseCommissionEquitySaveVO.getType())
                    .bizType(baseCommissionEquitySaveVO.getBizType())
                    .createdOrgId(ContextUtil.getCurrentCompanyId())
                    .build();
            commissionEquityManager.save(equity);
            if (CollUtil.isNotEmpty(baseCommissionEquitySaveVO.getApplySaveVOList())) {
                applyList.addAll(baseCommissionEquitySaveVO.getApplySaveVOList().stream().map(v -> BaseCommissionApply.builder()
                        .commissionId(setting.getCommissionId())
                        .commissionSettingId(setting.getId())
                        .equityId(equity.getId())
                        .bizId(v.getBizId())
                        .bizType(equity.getBizType())
                        .createdOrgId(ContextUtil.getCurrentCompanyId())
                        .build()).collect(Collectors.toList()));
            }
            if (CollUtil.isNotEmpty(baseCommissionEquitySaveVO.getExcludeSaveVOList())) {
                excludeList.addAll(baseCommissionEquitySaveVO.getExcludeSaveVOList().stream().map(v -> BaseCommissionExclude.builder()
                        .commissionId(setting.getCommissionId())
                        .commissionSettingId(setting.getId())
                        .equityId(equity.getId())
                        .bizId(v.getBizId())
                        .bizType(equity.getBizType())
                        .createdOrgId(ContextUtil.getCurrentCompanyId())
                        .build()).collect(Collectors.toList()));
            }
        }
        if (CollUtil.isNotEmpty(applyList)) {
            commissionApplyManager.saveBatch(applyList);
        }
        if (CollUtil.isNotEmpty(excludeList)) {
            commissionExcludeManager.saveBatch(excludeList);
        }
    }

    /**
     * 保存服务项目
     *
     * @param serviceIds
     * @param baseCommission
     */
    private void saveCommissionService(List<Long> serviceIds, BaseCommission baseCommission) {
        if (!StringUtils.equals(CommissionTypeEnum.CLOCK.getCode(), baseCommission.getType())) {
            return;
        }
        ArgumentAssert.notEmpty(serviceIds, "请选择服务项目");
        commissionServiceManager.remove(Wraps.<top.kx.kxss.base.entity.commission.BaseCommissionService>lbU()
                .eq(top.kx.kxss.base.entity.commission.BaseCommissionService::getCommissionId, baseCommission.getId()));
        commissionServiceManager.saveBatch(serviceIds.stream().map(v -> top.kx.kxss.base.entity.commission.BaseCommissionService.builder()
                .commissionId(baseCommission.getId()).serviceId(v)
                .createdOrgId(ContextUtil.getCurrentCompanyId())
                .build()).collect(Collectors.toList()));
    }

    /**
     * 保存岗位
     *
     * @param positionSaveVOList
     * @param baseCommission
     */
    private void saveCommissionPosition(List<BaseCommissionPositionSaveVO> positionSaveVOList, BaseCommission baseCommission) {
        commissionPositionManager.remove(Wraps.<BaseCommissionPosition>lbU()
                .eq(BaseCommissionPosition::getCommissionId, baseCommission.getId()));
        if (CollUtil.isEmpty(positionSaveVOList)) {
            return;
        }
        commissionPositionManager.saveBatch(positionSaveVOList.stream().map(v -> BaseCommissionPosition.builder()
                .commissionId(baseCommission.getId()).positionId(v.getPositionId()).ratio(v.getRatio())
                .createdOrgId(ContextUtil.getCurrentCompanyId()).build()).collect(Collectors.toList()));
    }

    /**
     * 保存分配员工
     *
     * @param employeeSaveVOList
     * @param baseCommission
     */
    private void saveCommissionEmployee(List<BaseCommissionEmployeeSaveVO> employeeSaveVOList, BaseCommission baseCommission) {
        commissionEmployeeManager.remove(Wraps.<BaseCommissionEmployee>lbU()
                .eq(BaseCommissionEmployee::getCommissionId, baseCommission.getId()));
        employeeSaveVOList = Optional.ofNullable(employeeSaveVOList).orElse(new ArrayList<>());
        // 全部员工，直接返回
        if (StringUtils.equals(baseCommission.getDistributionRange(), "1")) {
            return;
        }
        // 未删除的员工
        List<BaseEmployee> baseEmployeeList = baseEmployeeManager.list(Wraps.<BaseEmployee>lbQ().eq(SuperEntity::getDeleteFlag, 0).inSql(BaseEmployee::getId,
                " select distinct eor.employee_id from base_employee_org_rel eor where eor.delete_flag = 0 and eor.org_id =  " + ContextUtil.getCurrentCompanyId()));
        if (CollUtil.isNotEmpty(baseEmployeeList) && CollUtil.isNotEmpty(employeeSaveVOList)) {
            List<Long> employeeIds = baseEmployeeList.stream().map(SuperEntity::getId).filter(Objects::nonNull).collect(Collectors.toList());
            employeeSaveVOList = employeeSaveVOList.stream().filter(v -> employeeIds.contains(v.getEmployeeId())).collect(Collectors.toList());

        }
        if (CollUtil.isEmpty(employeeSaveVOList)) {
            return;
        }
        commissionEmployeeManager.saveBatch(employeeSaveVOList.stream().map(v -> BaseCommissionEmployee.builder()
                .commissionId(baseCommission.getId()).employeeId(v.getEmployeeId()).ratio(v.getRatio())
                .createdOrgId(ContextUtil.getCurrentCompanyId()).build()).collect(Collectors.toList()));
    }

    public void handlerItems(BaseCommissionSetting commissionSetting,
                             Long applyProductBizId,
                             String bizType) {
        if (Objects.isNull(applyProductBizId)) {
            return;
        }
        BaseCommissionApply applySave = BaseCommissionApply.builder()
                .bizId(applyProductBizId.toString())
                .bizType(bizType)
                .commissionId(commissionSetting.getCommissionId())
                .commissionSettingId(commissionSetting.getId())
                .createdOrgId(ContextUtil.getCurrentCompanyId())
                .build();
        commissionApplyManager.save(applySave);
    }


    private void saveExclude(List<Long> excludeList, BaseCommission baseCommission, String bizType) {
        if (CollUtil.isNotEmpty(excludeList)) {
            List<BaseCommissionExclude> excludeSaveList = new ArrayList<>();
            for (Long bizId : excludeList) {
                BaseCommissionExclude excludeSaveVO = new BaseCommissionExclude();
                excludeSaveVO.setCommissionId(baseCommission.getId());
                //excludeSaveVO.setCommissionSettingId();
                excludeSaveVO.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                excludeSaveVO.setBizType(bizType);
                excludeSaveVO.setBizId(bizId);
                excludeSaveList.add(excludeSaveVO);
            }
            commissionExcludeManager.saveBatch(excludeSaveList);
        }
    }


    private void handlerApplyResultVOList(BaseCommissionResultVO baseCommissionResultVO, List<BaseCommissionSettingResultVO> settingResultVOList, List<BaseCommissionApplyResultVO> applyResultVOList) {
        if (CollUtil.isEmpty(applyResultVOList) || CollUtil.isEmpty(settingResultVOList)) {
            return;
        }
        Map<Long, String> productMap = new HashMap<>();
        List<Long> productBizIdList = applyResultVOList.stream().filter(v -> StringUtils.equals(v.getBizType(), EquityTypeEnum.PRODUCT.getCode())).map(s -> Long.parseLong(s.getBizId())).collect(Collectors.toList());
        if (StringUtils.equals(baseCommissionResultVO.getApplyProductType(), "2") && CollUtil.isNotEmpty(productBizIdList)) {
            productMap = productCategoryService.list(Wraps.<BaseProductCategory>lbQ().in(BaseProductCategory::getId, productBizIdList))
                    .stream().collect(Collectors.toMap(BaseProductCategory::getId, BaseProductCategory::getName));
        } else if (StringUtils.equals(baseCommissionResultVO.getApplyProductType(), "3") && CollUtil.isNotEmpty(productBizIdList)) {
            productMap = productService.list(Wraps.<BaseProduct>lbQ().in(BaseProduct::getId, productBizIdList))
                    .stream().collect(Collectors.toMap(BaseProduct::getId, BaseProduct::getName));
        }

        for (BaseCommissionSettingResultVO baseCommissionSettingResultVO : settingResultVOList) {
            baseCommissionSettingResultVO.setApplyProductBizId(applyResultVOList.stream()
                    .filter(v -> Objects.equals(v.getCommissionSettingId(), baseCommissionSettingResultVO.getId()) && StringUtils.equals(v.getBizType(), EquityTypeEnum.PRODUCT.getCode()))
                    .map(s -> Long.parseLong(s.getBizId())).findFirst().orElse(null));
            baseCommissionSettingResultVO.setApplyProductBizName(productMap.get(baseCommissionSettingResultVO.getApplyProductBizId()));
        }
    }


    private void handlerExcludeResultVOList(BaseCommissionResultVO resultVO, List<BaseCommissionExcludeResultVO> excludeResultVOList) {
        resultVO.setExcludeProductList(excludeResultVOList.stream().filter(s -> StringUtils.equals(s.getBizType(), EquityTypeEnum.PRODUCT.getCode())).map(BaseCommissionExcludeResultVO::getBizId).collect(Collectors.toList()));

    }

    private void handlerPositionResultVOList(BaseCommissionResultVO resultVO) {
        List<BaseCommissionPosition> baseCommissionPositionList = commissionPositionManager.list(Wraps.<BaseCommissionPosition>lbQ().eq(BaseCommissionPosition::getCommissionId, resultVO.getId()));
        if (CollUtil.isEmpty(baseCommissionPositionList)) {
            return;
        }
        List<BaseCommissionPositionResultVO> positionResultVOS = BeanPlusUtil.toBeanList(baseCommissionPositionList, BaseCommissionPositionResultVO.class);
        echoService.action(positionResultVOS);
        resultVO.setPositionResultVOList(positionResultVOS);
    }

    private void handlerEmployeeResultVOList(BaseCommissionResultVO resultVO) {
        List<BaseCommissionEmployee> baseCommissionPositionList = commissionEmployeeManager.list(Wraps.<BaseCommissionEmployee>lbQ()
                .eq(BaseCommissionEmployee::getCommissionId, resultVO.getId()));
        if (CollUtil.isEmpty(baseCommissionPositionList)) {
            resultVO.setEmployeeResultVOList(Collections.emptyList());
            return;
        }
        List<BaseCommissionEmployeeResultVO> commissionEmployeeResultVOList = BeanPlusUtil.toBeanList(baseCommissionPositionList, BaseCommissionEmployeeResultVO.class);
        echoService.action(commissionEmployeeResultVOList);
        resultVO.setEmployeeResultVOList(commissionEmployeeResultVOList);
    }

    @Override
    public List<BaseCommissionEmployeeInfoResultVO> getEmployeeCommissionTemplate() {
        // 创建员工提成信息模板数据
        List<BaseCommissionEmployeeInfoResultVO> templateList = new ArrayList<>();
        List<BaseEmployee> baseEmployeeList = baseEmployeeManager.list(Wraps.<BaseEmployee>lbQ()
                .inSql(SuperEntity::getId, "select distinct beor.employee_id from base_employee_org_rel beor where beor.delete_flag = 0 and beor.org_id = " + ContextUtil.getCurrentCompanyId()));
        if (CollUtil.isEmpty(baseEmployeeList)) {
            return templateList;
        }
        List<BaseCommissionEmployeeInfoResultVO> employeeInfoResultVOList = baseEmployeeList.stream().map(s -> BaseCommissionEmployeeInfoResultVO.builder()
                .echoMap(MapUtil.newHashMap())
                .employeeId(s.getId())
                .name(s.getName())
                .hireDate(s.getHireDate())
                .positionId(s.getPositionId())
                .state(s.getState())
                .build()).collect(Collectors.toList());
        // 获取所有的提成设置
        List<BaseCommission> baseCommissionList = superManager.list(Wraps.<BaseCommission>lbQ().eq(BaseCommission::getCreatedOrgId, ContextUtil.getCurrentCompanyId()));
        if (CollUtil.isEmpty(baseCommissionList)) {
            echoService.action(employeeInfoResultVOList);
            return employeeInfoResultVOList;
        }
        List<Long> commissionIds = baseCommissionList.stream().map(SuperEntity::getId).distinct().collect(Collectors.toList());
        List<BaseCommissionEmployee> commissionEmployeeList = commissionEmployeeManager.list(Wraps.<BaseCommissionEmployee>lbQ().in(BaseCommissionEmployee::getCommissionId, commissionIds));
        Map<Long, List<BaseCommissionEmployee>> commissionEmployeeMap = commissionEmployeeList.stream().collect(Collectors.groupingBy(BaseCommissionEmployee::getCommissionId));
        List<top.kx.kxss.base.entity.commission.BaseCommissionService> commissionServiceList = commissionServiceManager.list(Wraps.<top.kx.kxss.base.entity.commission.BaseCommissionService>lbQ().in(top.kx.kxss.base.entity.commission.BaseCommissionService::getCommissionId, commissionIds));
        Map<Long, List<top.kx.kxss.base.entity.commission.BaseCommissionService>> commissionServiceMap = commissionServiceList.stream().collect(Collectors.groupingBy(top.kx.kxss.base.entity.commission.BaseCommissionService::getCommissionId));
        List<BaseServicePersonal> persionalList = baseServicePersonalManager.list(Wraps.<BaseServicePersonal>lbQ().in(BaseServicePersonal::getEmployeeId, baseEmployeeList.stream().map(SuperEntity::getId).collect(Collectors.toList())));
        Map<Long, List<BaseServicePersonal>> servicePersonalMap = persionalList.stream().collect(Collectors.groupingBy(BaseServicePersonal::getServiceId));

        List<BaseCommission> serviceCommissionList = baseCommissionList.stream().filter(s -> StringUtils.equals(s.getType(), CommissionTypeEnum.CLOCK.getCode())).collect(Collectors.toList());
        List<BaseCommission> rechargeCommissionList = baseCommissionList.stream().filter(s -> StringUtils.equals(s.getType(), CommissionTypeEnum.RECHARGE.getCode())).collect(Collectors.toList());
        List<BaseCommission> productCommissionList = baseCommissionList.stream().filter(s -> StringUtils.equals(s.getType(), CommissionTypeEnum.PRODUCT_SALE.getCode())).collect(Collectors.toList());
        List<BaseCommission> wholeOrderCommissionList = baseCommissionList.stream().filter(s -> StringUtils.equals(s.getType(), CommissionTypeEnum.WHOLE_ORDER.getCode())).collect(Collectors.toList());
        employeeInfoResultVOList.forEach(s -> {
            // 充值提成
            List<Long> rechargeCommissionIds = getCommissionIds(s, rechargeCommissionList, commissionEmployeeMap);
            // 商品
            List<Long> productCommissionIs = getCommissionIds(s, productCommissionList, commissionEmployeeMap);

            // 整单
            List<Long> wholeOrderCommissionIds = getCommissionIds(s, wholeOrderCommissionList, commissionEmployeeMap);

            // 服务
            List<Long> serviceCommissionIds = new ArrayList<>();
            for (BaseCommission baseCommission : serviceCommissionList) {
                List<top.kx.kxss.base.entity.commission.BaseCommissionService> baseCommissionServiceList = commissionServiceMap.get(baseCommission.getId());
                if (CollUtil.isEmpty(baseCommissionServiceList)) {
                    continue;
                }
                List<Long> serviceIds = baseCommissionServiceList.stream().map(top.kx.kxss.base.entity.commission.BaseCommissionService::getServiceId).collect(Collectors.toList());
                for (Long serviceId : serviceIds) {
                    List<BaseServicePersonal> baseServicePersonalList = servicePersonalMap.get(serviceId);
                    if (CollUtil.isEmpty(baseServicePersonalList)) {
                        continue;
                    }
                    if (baseServicePersonalList.stream().anyMatch(v -> Objects.equals(v.getEmployeeId(), s.getEmployeeId()))) {
                        serviceCommissionIds.add(baseCommission.getId());
                    }
                }
            }
            s.setRechargeCommissionIds(rechargeCommissionIds);
            s.setProductCommissionIds(productCommissionIs);
            s.setWholeOrderCommissionIds(wholeOrderCommissionIds);
            s.setServiceCommissionIds(serviceCommissionIds);

        });
        echoService.action(templateList);
        return templateList;
    }

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        return superManager.findByIds(ids);
    }

    @Override
    public Boolean retryCompensation(List<Long> sourceIds, Long recalcId) {
        // 4. 分批发送MQ消息，每次200条
        int batchSize = 200;
        List<List<Long>> groupedIds = IntStream.range(0, (sourceIds.size() + batchSize - 1) / batchSize)
                .mapToObj(i -> sourceIds.subList(i * batchSize, Math.min((i + 1) * batchSize, sourceIds.size())))
                .collect(Collectors.toList());

        for (int i = 0; i < groupedIds.size(); i++) {
            List<Long> batchIds = groupedIds.get(i);
            PerformanceCommissionConsumeQuery consumeQuery = PerformanceCommissionConsumeQuery.builder()
                    .posCashIds(batchIds)
                    .recalcId(recalcId)
                    .tenantId(ContextUtil.getTenantId())
                    .orgId(ContextUtil.getCurrentCompanyId())
                    .build();

            // 发送MQ消息
            template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.PERFORMANCE_COMMISSION,
                    JsonUtil.toJson(consumeQuery));
        }
        return true;
    }

    private List<Long> getCommissionIds(BaseCommissionEmployeeInfoResultVO s, List<BaseCommission> rechargeCommissionList, Map<Long, List<BaseCommissionEmployee>> commissionEmployeeMap) {
        List<Long> ids = new ArrayList<>();
        if (CollUtil.isEmpty(rechargeCommissionList)) {
            return ids;
        }
        rechargeCommissionList.forEach(b -> {
            if (StringUtils.equals(b.getDistributionRange(), "1")) {
                ids.add(b.getId());
            } else {
                if (commissionEmployeeMap.containsKey(b.getId()) && commissionEmployeeMap.get(b.getId()).stream().anyMatch(v -> Objects.equals(v.getEmployeeId(), s.getEmployeeId()))) {
                    ids.add(b.getId());
                }
            }
        });
        return ids.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    public String randomOrderCode() {
        String timePart = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
        long nanoPart = System.nanoTime() % 100000;  // 取后5位
        return timePart + String.format("%05d", nanoPart);
    }

}


