package top.kx.kxss.base.manager.chase.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.chase.BaseChasePointsRule;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.chase.BaseChasePointsRuleManager;
import top.kx.kxss.base.mapper.chase.BaseChasePointsRuleMapper;

/**
 * <p>
 * 通用业务实现类
 * 追分规则设置
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-20 11:07:10
 * @create [2025-06-20 11:07:10] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseChasePointsRuleManagerImpl extends SuperManagerImpl<BaseChasePointsRuleMapper, BaseChasePointsRule> implements BaseChasePointsRuleManager {

}


