package top.kx.kxss.base.mapper.outin;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.base.entity.outin.BaseOutin;
import org.springframework.stereotype.Repository;
import top.kx.kxss.base.vo.query.outin.BaseOutinPageQuery;
import top.kx.kxss.base.vo.result.outin.BaseOutinItemResultVO;
import top.kx.kxss.base.entity.outin.SellOutinDetailsResultVO;
import top.kx.kxss.base.entity.outin.SellOutinResultVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * 商品出入库主表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-06 14:51:59
 * @create [2023-04-06 14:51:59] [dou] [代码生成器生成]
 */
@Repository
public interface BaseOutinMapper extends SuperMapper<BaseOutin> {


    IPage<BaseOutinItemResultVO> pageList(@Param("page") IPage<BaseOutinItemResultVO> page, @Param("model") BaseOutinPageQuery model);

    IPage<SellOutinResultVO> sellPage(@Param("page") IPage<SellOutinResultVO> page, @Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    SellOutinResultVO sellOne(@Param(Constants.WRAPPER) Wrapper<PosCash> wrapper);

    List<SellOutinDetailsResultVO> sellDetailList(@Param("id") Long id);
}


