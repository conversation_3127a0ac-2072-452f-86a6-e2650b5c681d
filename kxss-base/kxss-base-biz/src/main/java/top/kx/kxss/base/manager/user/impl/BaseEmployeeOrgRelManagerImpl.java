package top.kx.kxss.base.manager.user.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.basic.cache.redis2.CacheResult;
import top.kx.basic.cache.repository.CacheOps;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.model.cache.CacheKey;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.user.BaseEmployeeOrgRel;
import top.kx.kxss.base.entity.user.BaseOrg;
import top.kx.kxss.base.manager.user.BaseEmployeeOrgRelManager;
import top.kx.kxss.base.mapper.user.BaseEmployeeOrgRelMapper;
import top.kx.kxss.base.mapper.user.BaseOrgMapper;
import top.kx.kxss.common.cache.base.user.EmployeeOrgCacheKeyBuilder;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用业务实现类
 * 员工所在部门
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 * @create [2021-10-18] [zuihou] [代码生成器生成]
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BaseEmployeeOrgRelManagerImpl extends SuperManagerImpl<BaseEmployeeOrgRelMapper, BaseEmployeeOrgRel> implements BaseEmployeeOrgRelManager {
    private final CacheOps cacheOps;
    private final BaseOrgMapper orgMapper;

    @Override
    public List<Long> findOrgIdByEmployeeId(Long employeeId) {
        CacheKey eoKey = EmployeeOrgCacheKeyBuilder.build(employeeId);
        CacheResult<List<Long>> orgIdResult = cacheOps.get(eoKey, k -> orgMapper.selectOrgByEmployeeId(employeeId), false);
        return orgIdResult.asList();
    }

    @Override
    public boolean removeByEmployeeIds(Collection<Long> employeeIds) {
        ArgumentAssert.notEmpty(employeeIds, "员工ID不能为空");

        boolean remove = remove(Wraps.<BaseEmployeeOrgRel>lbQ().in(BaseEmployeeOrgRel::getEmployeeId, employeeIds));
        cacheOps.del(employeeIds.stream().map(EmployeeOrgCacheKeyBuilder::build).collect(Collectors.toList()));
        return remove;
    }

    @Override
    public void deleteByOrg(Collection<Long> orgIdList) {
        if (CollUtil.isEmpty(orgIdList)) {
            return;
        }
        LbQueryWrap<BaseEmployeeOrgRel> wrap = Wraps.<BaseEmployeeOrgRel>lbQ().in(BaseEmployeeOrgRel::getOrgId, orgIdList);
        List<BaseEmployeeOrgRel> list = list(wrap);
        remove(wrap);
        List<CacheKey> keys = list.stream()
                .map(BaseEmployeeOrgRel::getEmployeeId)
                .distinct()
                .map(EmployeeOrgCacheKeyBuilder::build)
                .collect(Collectors.toList());
        cacheOps.del(keys);
    }

    @Override
    public boolean bindOrg(List<Long> employeeIdList, long id) {
        BaseOrg baseOrg = orgMapper.selectById(id);
        if (baseOrg == null) {
            return true;
        }
        List<BaseEmployeeOrgRel> erList = employeeIdList.stream().map(employeeId -> {
            BaseEmployeeOrgRel employeeRole = new BaseEmployeeOrgRel();
            employeeRole.setEmployeeId(employeeId);
            employeeRole.setOrgId(baseOrg.getId());
            return employeeRole;
        }).collect(Collectors.toList());
        boolean flag = saveBatch(erList);
        cacheOps.del(employeeIdList.stream().map(EmployeeOrgCacheKeyBuilder::build).collect(Collectors.toList()));
        return flag;
    }
}
