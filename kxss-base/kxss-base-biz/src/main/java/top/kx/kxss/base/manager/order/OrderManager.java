package top.kx.kxss.base.manager.order;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.app.vo.query.cash.PosCashPageQuery;
import top.kx.kxss.app.vo.result.cash.PosCashResultVO;

/**
 * <p>
 * 通用业务接口
 * 权益卡
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-14 11:01:58
 * @create [2023-04-14 11:01:58] [dou] [代码生成器生成]
 */
public interface OrderManager {


    R<Page<PosCashResultVO>> page(PageParams<PosCashPageQuery> params);

    /**
     * 部分退款
     * @return
     */
    Boolean partialRefund();

}


