package top.kx.kxss.base.service.ad.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.ad.BaseAd;
import top.kx.kxss.base.entity.ad.BaseAdTable;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.manager.ad.BaseAdManager;
import top.kx.kxss.base.manager.ad.BaseAdTableManager;
import top.kx.kxss.base.service.ad.BaseAdService;
import top.kx.kxss.base.service.table.BaseTableInfoService;
import top.kx.kxss.base.vo.query.ad.BaseAdPageQuery;
import top.kx.kxss.base.vo.result.ad.BaseAdResultVO;
import top.kx.kxss.base.vo.save.ad.BaseAdSaveVO;
import top.kx.kxss.base.vo.update.ad.BaseAdSortValueUpdateVO;
import top.kx.kxss.base.vo.update.ad.BaseAdUpdateVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.RabbitMqConstant;
import top.kx.kxss.file.api.FileApi;
import top.kx.kxss.file.vo.result.FileResultVO;
import top.kx.kxss.model.enumeration.base.FileBizTypeEnum;
import top.kx.kxss.pos.WsPushApi;
import top.kx.kxss.pos.push.PushNoticeDeviceMsgBean;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 广告
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-14 17:02:36
 * @create [2025-03-14 17:02:36] [yan] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class BaseAdServiceImpl extends SuperServiceImpl<BaseAdManager, Long, BaseAd, BaseAdSaveVO,
        BaseAdUpdateVO, BaseAdPageQuery, BaseAdResultVO> implements BaseAdService {

    private final BaseAdTableManager baseAdTableManager;
    private final FileApi fileApi;
    private final WsPushApi wsPushApi;
    private final BaseTableInfoService baseTableInfoService;
    private final RabbitTemplate template;


    @Override
    public BaseAd save(BaseAdSaveVO baseAdSaveVO) {
        int sortValue = 10;
        BaseAd sourceAd = superManager.getOne(Wraps.<BaseAd>lbQ().orderByDesc(BaseAd::getSortValue).last("limit 1"));
        if (Objects.nonNull(sourceAd)) {
            sortValue = sourceAd.getSortValue() + 10;
        }
        baseAdSaveVO.setSortValue(sortValue);
        baseAdSaveVO.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        if (Objects.nonNull(baseAdSaveVO.getFile())) {
            R<FileResultVO> fileResultVOR = fileApi.upload(baseAdSaveVO.getFile(), FileBizTypeEnum.BASE_AD.getCode(), null, null, null);
            FileResultVO data = fileResultVOR.getData();
            baseAdSaveVO.setFileId(data.getId());
            baseAdSaveVO.setFileUrl(data.getUrl());
            baseAdSaveVO.setType(data.getFileType().getCode());
        }
        List<BaseAdTable> adTableList = new ArrayList<>();
        if (CollUtil.isNotEmpty(baseAdSaveVO.getTableIds())) {
            baseAdSaveVO.setIsBindTable(true);
            adTableList = baseAdSaveVO.getTableIds().stream().map(tableId ->
                    BaseAdTable.builder()
                            //.adId(entity.getId())
                            .tableId(tableId)
                            .createdOrgId(ContextUtil.getCurrentCompanyId())
                            .build()).collect(Collectors.toList());
        } else {
            baseAdSaveVO.setIsBindTable(false);
        }
        BaseAd entity = super.save(baseAdSaveVO);
        if (CollUtil.isNotEmpty(adTableList)) {
            adTableList.forEach(s-> s.setAdId(entity.getId()));
        }
        if (CollUtil.isNotEmpty(adTableList)) {
            baseAdTableManager.saveBatch(adTableList);
        }
        // 是否有台桌, 有台桌, 就刷新指定台桌, 否则刷新所有
        pushMsg(baseAdSaveVO.getTableIds());
        return entity;
    }


    @Override
    public BaseAd updateById(BaseAdUpdateVO baseAdUpdateVO) {
        BaseAd baseAd = superManager.getById(baseAdUpdateVO.getId());
        baseAdTableManager.remove(Wraps.<BaseAdTable>lbQ().eq(BaseAdTable::getAdId, baseAdUpdateVO.getId()));
        if (CollUtil.isNotEmpty(baseAdUpdateVO.getTableIds())) {
            baseAdTableManager.saveBatch(baseAdUpdateVO.getTableIds().stream().map(tableId ->
                    BaseAdTable.builder()
                            .adId(baseAdUpdateVO.getId())
                            .tableId(tableId)
                            .createdOrgId(ContextUtil.getCurrentCompanyId())
                            .build()
            ).collect(Collectors.toList()));
            // 设置已绑定台桌
            baseAdUpdateVO.setIsBindTable(true);
        } else {
            // 如果没有绑定台桌，设置为false
            baseAdUpdateVO.setIsBindTable(false);
        }
        BaseAd update = super.updateById(baseAdUpdateVO);
        List<Long> tableIds = baseAdUpdateVO.getTableIds();
        // 修改之前和修改之后, 如果没有绑定台桌, 全部台桌都推送
        if (!baseAd.getIsBindTable() || !baseAdUpdateVO.getIsBindTable()) {
            tableIds = new ArrayList<>();
        } else {
            tableIds.addAll(baseAdTableManager.list(Wraps.<BaseAdTable>lbQ().eq(BaseAdTable::getAdId, baseAdUpdateVO.getId()))
                    .stream().map(BaseAdTable::getTableId).distinct().collect(Collectors.toList()));
        }
        tableIds = tableIds.stream().distinct().collect(Collectors.toList());
        pushMsg(tableIds);
        return update;
    }

    @Override
    public Boolean batchSave(List<BaseAdSaveVO> model) {
        int sortValue = 10;
        BaseAd sourceAd = superManager.getOne(Wraps.<BaseAd>lbQ().orderByDesc(BaseAd::getSortValue).last("limit 1"));
        if (Objects.nonNull(sourceAd)) {
            sortValue = sourceAd.getSortValue() + 10;
        }
        for (BaseAdSaveVO baseAdSaveVO : model) {
            baseAdSaveVO.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
            baseAdSaveVO.setSortValue(sortValue);
            sortValue += 10;
            if (Objects.nonNull(baseAdSaveVO.getFile())) {
                R<FileResultVO> fileResultVOR = fileApi.upload(baseAdSaveVO.getFile(), FileBizTypeEnum.BASE_AD.getCode(), null, null, null);
                FileResultVO data = fileResultVOR.getData();
                baseAdSaveVO.setFileId(data.getId());
                baseAdSaveVO.setFileUrl(data.getUrl());
                baseAdSaveVO.setType(data.getFileType().getCode());
            }
        }
        List<BaseAdTable> baseAdTableList = new ArrayList<>();
        for (BaseAdSaveVO baseAdSaveVO : model) {
            BaseAd baseAd = BeanPlusUtil.toBean(baseAdSaveVO, BaseAd.class);
            superManager.save(baseAd);
            if (CollUtil.isNotEmpty(baseAdSaveVO.getTableIds())) {
                baseAdTableList.addAll(baseAdSaveVO.getTableIds().stream().map(tableId ->
                        BaseAdTable.builder()
                                .createdOrgId(ContextUtil.getCurrentCompanyId())
                                .tableId(tableId)
                                .adId(baseAd.getId())
                                .build()).collect(Collectors.toList()));
            }
        }
        baseAdTableManager.saveBatch(baseAdTableList);
        pushMsg(null);
        return true;
    }

    @Override
    public Boolean batchUpdate(List<BaseAdUpdateVO> model) {
        for (BaseAdUpdateVO baseAdUpdateVO : model) {
            if (Objects.nonNull(baseAdUpdateVO.getFile())) {
                R<FileResultVO> fileResultVOR = fileApi.upload(baseAdUpdateVO.getFile(), FileBizTypeEnum.BASE_AD.getCode(), null, null, null);
                FileResultVO data = fileResultVOR.getData();
                baseAdUpdateVO.setFileId(data.getId());
                baseAdUpdateVO.setFileUrl(data.getUrl());
                baseAdUpdateVO.setType(data.getFileType().getCode());
            }
        }
        // 批量跟新
        superManager.updateBatchById(BeanPlusUtil.toBeanList(model, BaseAd.class));
        // 删除绑定的台桌, 重新添加
        baseAdTableManager.remove(Wraps.<BaseAdTable>lbQ().in(BaseAdTable::getAdId, model.stream().map(BaseAdUpdateVO::getId).collect(Collectors.toList())));
        List<BaseAdTable> baseAdTableList = model.stream()
                .filter(s -> CollUtil.isNotEmpty(s.getTableIds()))
                .flatMap(baseAdUpdateVO -> baseAdUpdateVO.getTableIds().stream()
                        .map(tableId -> BaseAdTable.builder()
                                .createdOrgId(ContextUtil.getCurrentCompanyId())
                                .tableId(tableId)
                                .adId(baseAdUpdateVO.getId())
                                .build()))
                .collect(Collectors.toList());
        baseAdTableManager.saveBatch(baseAdTableList);
        pushMsg(null);
        return true;
    }

    @Override
    public Boolean batchUpdateSortValue(List<BaseAdSortValueUpdateVO> model) {
        List<BaseAd> baseAdList = BeanPlusUtil.toBeanList(model, BaseAd.class);
        boolean b = superManager.updateBatchById(baseAdList);
        pushMsg(null);
        return b;
    }

    @Override
    public boolean removeByIds(Collection<Long> idList) {
        List<BaseAd> baseAdList = superManager.listByIds(idList);
        boolean b = super.removeByIds(idList);
        // 是否存在全部的台桌可用的
        List<Long> tableIds = new ArrayList<>();
        if (baseAdList.stream().allMatch(BaseAd::getIsBindTable)) {
            List<BaseAdTable> baseAdTableList = baseAdTableManager.list(Wraps.<BaseAdTable>lbQ().in(BaseAdTable::getAdId, idList));
            tableIds = baseAdTableList.stream().map(BaseAdTable::getTableId).distinct().collect(Collectors.toList());
        }
        pushMsg(tableIds);
        return b;
    }



    private void pushMsg(List<Long> tableIds) {
        if (CollUtil.isNotEmpty(tableIds)) {
            template.convertAndSend(RabbitMqConstant.BASE_AD_REFRESH_EXCHANGE, RabbitMqConstant.BASE_AD_REFRESH_DEAD_ROUTING_KEY,
                    JSON.toJSONString(PushNoticeDeviceMsgBean.builder().tableIds(tableIds)
                            .refreshServiceEmployee(null)
                            .isUnused(null)
                            .refresh(null)
                            .isOpen(null)
                            .refreshAd(true)
                            .refreshAdSettings(false)
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId())
                            .employeeId(ContextUtil.getEmployeeId())
                            .build()), message -> {
                        //延迟3秒钟
                        message.getMessageProperties().setExpiration((2 * 1000) + "");
                        return message;
                    });
            return;
        }
        List<BaseTableInfo> tableInfoList = baseTableInfoService.list(Wraps.<BaseTableInfo>lbQ().eq(SuperEntity::getDeleteFlag, 0)
                .eq(BaseTableInfo::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(BaseTableInfo::getIsVirtual, false)
                .eq(BaseTableInfo::getDisplay, true));
        if (CollUtil.isNotEmpty(tableInfoList)) {
            template.convertAndSend(RabbitMqConstant.BASE_AD_REFRESH_EXCHANGE, RabbitMqConstant.BASE_AD_REFRESH_DEAD_ROUTING_KEY,
                    JSON.toJSONString(PushNoticeDeviceMsgBean.builder().tableIds(tableInfoList.stream().map(BaseTableInfo::getId).distinct().collect(Collectors.toList()))
                            .refreshServiceEmployee(null)
                            .isUnused(null)
                            .refresh(null)
                            .isOpen(null)
                            .refreshAd(true)
                            .refreshAdSettings(false)
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId())
                            .employeeId(ContextUtil.getEmployeeId())
                            .build()), message -> {
                        //延迟3秒钟
                        message.getMessageProperties().setExpiration((2 * 1000) + "");
                        return message;
                    });
        }
    }
}


