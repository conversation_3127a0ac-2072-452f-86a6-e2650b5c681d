package top.kx.kxss.base.manager.ad.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.ad.BaseAdSettings;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.ad.BaseAdSettingsManager;
import top.kx.kxss.base.mapper.ad.BaseAdSettingsMapper;

/**
 * <p>
 * 通用业务实现类
 * 广告设置
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-17 17:40:01
 * @create [2025-03-17 17:40:01] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseAdSettingsManagerImpl extends SuperManagerImpl<BaseAdSettingsMapper, BaseAdSettings> implements BaseAdSettingsManager {

}


