package top.kx.kxss.base.manager.accounting;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import top.kx.basic.base.manager.SuperManager;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.vo.result.cash.AmountResultVO;
import top.kx.kxss.base.entity.accounting.BaseAccountingInfo;
import top.kx.kxss.base.vo.result.accounting.AccountingCalenderResultVO;
import top.kx.kxss.base.vo.result.accounting.AccountingCalenderSumResultVO;

import java.util.List;

/**
 * <p>
 * 通用业务接口
 * 记账明细
 * </p>
 *
 * <AUTHOR>
 * @date 2023-10-09 16:35:55
 * @create [2023-10-09 16:35:55] [dou] [代码生成器生成]
 */
public interface BaseAccountingInfoManager extends SuperManager<BaseAccountingInfo> {

    List<AccountingCalenderResultVO> calendar(String field, Wrapper<BaseAccountingInfo> wrapper);

    AccountingCalenderSumResultVO calendarSum(Wrapper<BaseAccountingInfo> wrapper);

    List<AmountResultVO> calendarIn(QueryWrapper<PosCash> wrapper, Integer hour,  String field);

    List<AmountResultVO> calendarInList(QueryWrapper<PosCash> wrapper);
}


