package top.kx.kxss.base.manager.biz.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.biz.BaseBizAvailableTime;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.biz.BaseBizAvailableTimeManager;
import top.kx.kxss.base.mapper.biz.BaseBizAvailableTimeMapper;

/**
 * <p>
 * 通用业务实现类
 * 可用时间段
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-19 15:45:20
 * @create [2024-03-19 15:45:20] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseBizAvailableTimeManagerImpl extends SuperManagerImpl<BaseBizAvailableTimeMapper, BaseBizAvailableTime> implements BaseBizAvailableTimeManager {

}


