package top.kx.kxss.base.manager.chase.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.chase.BaseChasePointsPunter;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.chase.BaseChasePointsPunterManager;
import top.kx.kxss.base.mapper.chase.BaseChasePointsPunterMapper;

/**
 * <p>
 * 通用业务实现类
 * 追分规则顾客分数
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-24 13:33:46
 * @create [2025-06-24 13:33:46] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseChasePointsPunterManagerImpl extends SuperManagerImpl<BaseChasePointsPunterMapper, BaseChasePointsPunter> implements BaseChasePointsPunterManager {

}


