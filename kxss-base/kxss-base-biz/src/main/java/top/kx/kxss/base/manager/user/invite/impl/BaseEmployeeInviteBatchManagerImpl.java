package top.kx.kxss.base.manager.user.invite.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.user.invite.BaseEmployeeInviteBatch;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.user.invite.BaseEmployeeInviteBatchManager;
import top.kx.kxss.base.mapper.user.invite.BaseEmployeeInviteBatchMapper;

/**
 * <p>
 * 通用业务实现类
 * 员工邀请批次
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-18 18:11:02
 * @create [2025-06-18 18:11:02] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseEmployeeInviteBatchManagerImpl extends SuperManagerImpl<BaseEmployeeInviteBatchMapper, BaseEmployeeInviteBatch> implements BaseEmployeeInviteBatchManager {

}


