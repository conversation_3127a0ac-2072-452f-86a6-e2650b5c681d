package top.kx.kxss.base.manager.ad.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.ad.BaseAd;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.ad.BaseAdManager;
import top.kx.kxss.base.mapper.ad.BaseAdMapper;

/**
 * <p>
 * 通用业务实现类
 * 广告
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-14 17:02:36
 * @create [2025-03-14 17:02:36] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseAdManagerImpl extends SuperManagerImpl<BaseAdMapper, BaseAd> implements BaseAdManager {

}


