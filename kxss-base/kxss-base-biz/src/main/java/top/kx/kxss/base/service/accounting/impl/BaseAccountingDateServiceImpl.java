package top.kx.kxss.base.service.accounting.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.Week;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.cache.lock.DistributedLock;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.app.vo.result.cash.AmountResultVO;
import top.kx.kxss.base.entity.accounting.BaseAccountingDate;
import top.kx.kxss.base.entity.accounting.BaseAccountingInfo;
import top.kx.kxss.base.entity.payment.BasePaymentType;
import top.kx.kxss.base.manager.accounting.BaseAccountingDateManager;
import top.kx.kxss.base.manager.accounting.BaseAccountingInfoManager;
import top.kx.kxss.base.service.accounting.BaseAccountingDateService;
import top.kx.kxss.base.service.accounting.BaseAccountingInfoService;
import top.kx.kxss.base.service.payment.BasePaymentTypeService;
import top.kx.kxss.base.vo.query.accounting.AccountingQuery;
import top.kx.kxss.base.vo.query.accounting.BaseAccountingDatePageQuery;
import top.kx.kxss.base.vo.result.accounting.*;
import top.kx.kxss.base.vo.save.accounting.BaseAccountingDateSaveVO;
import top.kx.kxss.base.vo.save.accounting.BaseAccountingSaveVO;
import top.kx.kxss.base.vo.update.accounting.BaseAccountingDateUpdateVO;
import top.kx.kxss.base.vo.update.accounting.BaseAccountingUpdateVO;
import top.kx.kxss.common.constant.BizConstant;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.app.PosCashPaymentStatusEnum;
import top.kx.kxss.model.enumeration.base.*;
import top.kx.kxss.wxapp.api.custom.CustomApi;
import top.kx.kxss.wxapp.vo.query.statistics.DataOverviewQuery;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 记账日期
 * </p>
 *
 * <AUTHOR>
 * @date 2023-10-09 16:35:54
 * @create [2023-10-09 16:35:54] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseAccountingDateServiceImpl extends SuperServiceImpl<BaseAccountingDateManager, Long, BaseAccountingDate, BaseAccountingDateSaveVO,
        BaseAccountingDateUpdateVO, BaseAccountingDatePageQuery, BaseAccountingDateResultVO> implements BaseAccountingDateService {

    @Autowired
    private BaseAccountingInfoManager baseAccountingInfoManager;
    @Autowired
    private BaseAccountingInfoService baseAccountingInfoService;
    @Autowired
    private BasePaymentTypeService paymentTypeService;
    @Autowired
    private EchoService echoService;
    @Autowired
    private DistributedLock distributedLock;
    @Autowired
    private CustomApi customersApi;


    @Override
    public IPage<BaseAccountingResultVO> pageList(PageParams<BaseAccountingDatePageQuery> params) {
        params.setSort("");
        params.setOrder("");
        IPage<BaseAccountingDate> page = params.buildPage(BaseAccountingDate.class);
        BaseAccountingDatePageQuery model = params.getModel();
        LbQueryWrap<BaseAccountingDate> wrap = Wraps.lbQ();
        if (model.getStartDate() != null) {
            wrap.between(BaseAccountingDate::getRecordingDate, model.getStartDate(), model.getEndDate());
        }
        wrap.eq(BaseAccountingDate::getCreatedOrgId, ContextUtil.getCurrentCompanyId());

        if (StringUtils.isNotBlank(model.getType())) {
            wrap.inSql(BaseAccountingDate::getId, "select distinct accounting_date_id from base_accounting_info where delete_flag = 0 and type = '" + model.getType() + "'");
        }

        // 支付方式
        if (Objects.nonNull(model.getPayTypeId())) {
            wrap.inSql(BaseAccountingDate::getId, "select distinct accounting_date_id from base_accounting_info where delete_flag = 0 and type = " + model.getPayTypeId());
        }

        // 备注模糊
        if (StringUtils.isNotBlank(model.getRemarks())) {
            wrap.inSql(BaseAccountingDate::getId, "select distinct accounting_date_id from base_accounting_info where delete_flag = 0 and remarks like '" + model.getRemarks() + "'");
        }
        wrap.orderByDesc(BaseAccountingDate::getRecordingDate);
        IPage<BaseAccountingDate> accountingDatePage = page(page, wrap);
        IPage<BaseAccountingResultVO> accountingPage = BeanPlusUtil.toBeanPage(accountingDatePage, BaseAccountingResultVO.class);
        if (CollUtil.isEmpty(accountingPage.getRecords())) {
            return new Page<>(params.getCurrent(), params.getSize());
        }
        List<Long> accountingDateIds = accountingPage.getRecords().stream().map(BaseAccountingResultVO::getId)
                .distinct().collect(Collectors.toList());
        List<BaseAccountingInfo> accountingInfoList = baseAccountingInfoManager.list(Wraps.<BaseAccountingInfo>lbQ()
                .eq(BaseAccountingInfo::getDeleteFlag, 0)
                .in(BaseAccountingInfo::getAccountingDateId, accountingDateIds));
        //封装数据
        List<BaseAccountingInfoResultVO> baseAccountingInfoResultVOList = BeanUtil.copyToList(accountingInfoList, BaseAccountingInfoResultVO.class);
        //订单收入
        List<BaseAccountingInfoResultVO> orderList = baseAccountingInfoResultVOList.stream().filter(v -> "ORDER".equals(v.getSource()))
                .collect(Collectors.toList());
        //移除订单收入
        baseAccountingInfoResultVOList.removeIf(v -> "ORDER".equals(v.getSource()));
        orderList.stream()
                .collect(Collectors.groupingBy(item -> (
                        item.getAccountingDateId().toString().concat("_").concat(
                                        (StrUtil.isBlank(item.getType()) ? "0" : item.getType())).concat("_")
                                .concat(StrUtil.isBlank(item.getPayInType()) ? "0" : item.getPayInType()).concat("_")
                                .concat(StrUtil.isBlank(item.getPayOutType()) ? "0" : item.getPayOutType())
                                .concat("_").concat(item.getPayTypeId().toString())), Collectors.toList()))
                .forEach((id, transfer) -> {
                    transfer.stream()
                            .reduce((a, b) ->
                                    new BaseAccountingInfoResultVO(a.getId(),
                                            a.getType(),
                                            a.getPayInType(),
                                            a.getPayTypeId(),
                                            a.getPayOutType(),
                                            a.getRecordingDate(),
                                            a.getAmount().add(b.getAmount()),
                                            StrUtil.isBlank(a.getRemarks()) ? b.getRemarks() : a.getRemarks(),
                                            a.getCreatedTime(),
                                            a.getSource(),
                                            a.getAccountingDateId(),
                                            a.getManagerId(),
                                            MapUtil.newHashMap()
                                    ))
                            .ifPresent(baseAccountingInfoResultVOList::add);
                });
        echoService.action(baseAccountingInfoResultVOList);
        Map<Long, List<BaseAccountingInfoResultVO>> accountingInfoMap = baseAccountingInfoResultVOList.stream()
                .collect(Collectors.groupingBy(BaseAccountingInfoResultVO::getAccountingDateId));
        for (BaseAccountingResultVO record : accountingPage.getRecords()) {
            List<BaseAccountingInfoResultVO> baseAccountingInfos = accountingInfoMap.get(record.getId());
            if (CollUtil.isNotEmpty(baseAccountingInfos)) {
                record.setAccountingInfoList(baseAccountingInfos.stream().sorted(Comparator.comparing(BaseAccountingInfoResultVO::getCreatedTime))
                        .collect(Collectors.toList()));
            }
        }
        return accountingPage;
    }

    @Override
    public Map<String, Object> statistics(BaseAccountingDatePageQuery query) {
        LbQueryWrap<BaseAccountingDate> wrap = Wraps.lbQ();
        Map<String, Object> map = MapUtil.newHashMap();
        if (query.getStartDate() != null) {
            wrap.between(BaseAccountingDate::getRecordingDate, query.getStartDate(), query.getEndDate());
        }
        wrap.eq(BaseAccountingDate::getCreatedOrgId, ContextUtil.getCurrentCompanyId());

        if (StringUtils.isNotBlank(query.getType())) {
            wrap.inSql(BaseAccountingDate::getId, "select distinct accounting_date_id from base_accounting_info where delete_flag = 0 and type = '" + query.getType() + "'");
        }

        // 支付方式
        if (Objects.nonNull(query.getPayTypeId())) {
            wrap.inSql(BaseAccountingDate::getId, "select distinct accounting_date_id from base_accounting_info where delete_flag = 0 and type = " + query.getPayTypeId());
        }

        // 备注模糊
        if (StringUtils.isNotBlank(query.getRemarks())) {
            wrap.inSql(BaseAccountingDate::getId, "select distinct accounting_date_id from base_accounting_info where delete_flag = 0 and remarks like '" + query.getRemarks() + "'");
        }

        List<BaseAccountingDate> accountingDateList = superManager.list(wrap);
        if (CollUtil.isEmpty(accountingDateList)) {
            map.put("payIn", BigDecimal.ZERO);
            map.put("payOut", BigDecimal.ZERO);
            map.put("total", BigDecimal.ZERO);
            map.put("handPayIn", BigDecimal.ZERO);
            map.put("handPayOut", BigDecimal.ZERO);
            return map;
        }
        BigDecimal payIn = accountingDateList.stream().map(BaseAccountingDate::getPayIn)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        map.put("payIn", payIn);
        BigDecimal payOut = accountingDateList.stream().map(BaseAccountingDate::getPayOut)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        map.put("payOut", payOut);
        map.put("total", payIn.subtract(payOut).setScale(2, RoundingMode.HALF_UP));
        List<BaseAccountingInfo> accountingInfoList = baseAccountingInfoManager.list(Wraps.<BaseAccountingInfo>lbQ()
                .eq(BaseAccountingInfo::getDeleteFlag, 0)
                .in(BaseAccountingInfo::getAccountingDateId, accountingDateList.stream().map(BaseAccountingDate::getId).distinct().collect(Collectors.toList())));
        BigDecimal decimal = accountingInfoList.stream().filter(v ->
                        !"ORDER".equals(v.getSource())
                                && AccountingTypeEnum.PAY_IN.getCode().equals(v.getType())
                )
                .map(BaseAccountingInfo::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        map.put("handPayIn", decimal);
        decimal = accountingInfoList.stream().filter(v ->
                        !"ORDER".equals(v.getSource())
                                && AccountingTypeEnum.PAY_OUT.getCode().equals(v.getType())
                )
                .map(BaseAccountingInfo::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        map.put("handPayOut", decimal);
        return map;
    }

    @Override
    @GlobalTransactional
    public boolean saveAccounting(BaseAccountingSaveVO model) {
        ArgumentAssert.isFalse(
                StrUtil.isNotBlank(model.getPayInType())
                        && model.getPayInType().equals(BizConstant.DEFAULT_GRADE.toString())
                , "此收入类型无法手动录入");
//        if (ObjectUtil.isAllEmpty(model.getAssessedStartTime(), model.getAssessedEndTime())) {
//            ArgumentAssert.isFalse(true
//                    , "请选择分摊时间");
//        }
        LocalDate date = model.getRecordingDate();
        String key = DateUtils.format(date, DateUtils.DEFAULT_DATE_FORMAT).concat("_ACCOUNTING");
        boolean lock = false;
        try {
            lock = distributedLock.lock(key, 3);
            if (!lock) {
                ArgumentAssert.isFalse(true
                        , "请稍候再试");
            }
            BigDecimal amount = model.getAmount();
            if (model.getAssessedStartTime() != null && model.getAssessedEndTime() != null) {
                List<LocalDate> middleDate = getMiddleDate(model.getAssessedStartTime(), model.getAssessedEndTime());
                BigDecimal divide = model.getAmount().divide(new BigDecimal(middleDate.size()), 2, RoundingMode.HALF_UP);
                List<BaseAccountingDate> accountingDateList = middleDate.stream().map(localDate -> {
                    model.setRecordingDate(localDate);
                    model.setAmount(divide);
                    //最后一个用减法
                    if (localDate.toString().equals(middleDate.get(middleDate.size() - 1).toString())) {
                        model.setAmount(amount.subtract(divide.multiply(new BigDecimal(middleDate.size() - 1)))
                                .setScale(2, RoundingMode.HALF_UP));
                    }
                    return getAccountingDate(model);
                }).collect(Collectors.toList());
                superManager.saveOrUpdateBatch(accountingDateList);
                Map<LocalDate, BaseAccountingDate> accountingDateMap = accountingDateList.stream().collect(Collectors
                        .toMap(BaseAccountingDate::getRecordingDate, Function.identity()));
                String uuid = IdUtil.simpleUUID();
                List<BaseAccountingInfo> collect = middleDate.stream().map(localDate -> {
                    model.setRecordingDate(localDate);
                    model.setAmount(divide);
                    //最后一个用减法
                    if (localDate.toString().equals(middleDate.get(middleDate.size() - 1).toString())) {
                        model.setAmount(amount.subtract(divide.multiply(new BigDecimal(middleDate.size() - 1)))
                                .setScale(2, RoundingMode.HALF_UP));
                    }
                    BaseAccountingDate baseAccountingDate = accountingDateMap.get(localDate);
                    BaseAccountingInfo accountingInfo = getAccountingInfo(baseAccountingDate, model);
                    accountingInfo.setUuid(uuid);
                    accountingInfo.setIsAssessed(true);
                    return accountingInfo;
                }).collect(Collectors.toList());
                return baseAccountingInfoService.saveBatch(collect);
            }
            BaseAccountingDate accountingDate = getAccountingDate(model);
            superManager.saveOrUpdate(accountingDate);
            //新增记账明细
            return baseAccountingInfoManager.save(getAccountingInfo(accountingDate, model));
        } finally {
            if (lock) {
                distributedLock.releaseLock(key);
            }
        }
    }

    @Override
    @GlobalTransactional
    public boolean updateAccounting(BaseAccountingUpdateVO model) {
        BaseAccountingInfo accountingInfo = baseAccountingInfoManager.getById(model.getId());
        ArgumentAssert.notNull(accountingInfo, "记账信息不存在");
        //修改时记账时间和原先不一致，则删除，并重新新增
        if (!accountingInfo.getRecordingDate().isEqual(model.getRecordingDate())
                || (accountingInfo.getIsAssessed() != null && accountingInfo.getIsAssessed())) {
            //删除记账信息
            del(accountingInfo.getId());
            //新增记账信息
            return saveAccounting(BeanUtil.copyProperties(model, BaseAccountingSaveVO.class));
        }
        //修改
        BaseAccountingDate accountingDate = superManager.getById(accountingInfo.getAccountingDateId());
        ArgumentAssert.notNull(accountingDate, "记账不存在");
        LocalDate date = accountingDate.getRecordingDate();
        String key = DateUtils.format(date, DateUtils.DEFAULT_DATE_FORMAT).concat("_ACCOUNTING");
        boolean lock = false;
        try {
            lock = distributedLock.lock(key, 3);
            if (!lock) {
                ArgumentAssert.isFalse(true
                        , "请稍候再试");
            }
            //增加相应金额
            if (Objects.equals(accountingInfo.getType(), AccountingTypeEnum.PAY_IN.getCode())
                    && Objects.equals(model.getType(), AccountingTypeEnum.PAY_IN.getCode())) {
                accountingDate.setPayIn(accountingDate.getPayIn().subtract(accountingInfo.getAmount()).add(model.getAmount()));
            } else if (Objects.equals(accountingInfo.getType(), AccountingTypeEnum.PAY_OUT.getCode())
                    && Objects.equals(model.getType(), AccountingTypeEnum.PAY_IN.getCode())) {
                accountingDate.setPayOut(accountingDate.getPayOut().subtract(accountingInfo.getAmount()));
                accountingDate.setPayIn(accountingDate.getPayIn().subtract(model.getAmount()));
            } else if (Objects.equals(accountingInfo.getType(), AccountingTypeEnum.PAY_OUT.getCode())
                    && Objects.equals(model.getType(), AccountingTypeEnum.PAY_OUT.getCode())) {
                accountingDate.setPayOut(accountingDate.getPayOut().subtract(accountingInfo.getAmount()).add(model.getAmount()));
            } else if (Objects.equals(accountingInfo.getType(), AccountingTypeEnum.PAY_IN.getCode())
                    && Objects.equals(model.getType(), AccountingTypeEnum.PAY_OUT.getCode())) {
                accountingDate.setPayIn(accountingDate.getPayIn().subtract(accountingInfo.getAmount()));
                accountingDate.setPayOut(accountingDate.getPayOut().subtract(model.getAmount()));
            }
            accountingInfo.setAmount(model.getAmount());
            accountingInfo.setUpdatedTime(LocalDateTime.now());
            if (Objects.nonNull(model.getPayTypeId())) {
                accountingInfo.setPayTypeId(model.getPayTypeId());
            }
            if (StringUtils.isNotBlank(model.getPayInType())) {
                accountingInfo.setPayInType(model.getPayInType());
            }
            if (StringUtils.isNotBlank(model.getPayOutType())) {
                accountingInfo.setPayOutType(model.getPayOutType());
            }
            baseAccountingInfoService.updateById(accountingInfo);
            return superManager.updateById(accountingDate);
        } finally {
            if (lock) {
                distributedLock.releaseLock(key);
            }
        }
    }

    @Override
    @GlobalTransactional
    public boolean del(Long id) {
        BaseAccountingInfo accountingInfo = baseAccountingInfoManager.getById(id);
        ArgumentAssert.notNull(accountingInfo, "记账信息不存在");
        BaseAccountingDate accountingDate = superManager.getById(accountingInfo.getAccountingDateId());
        ArgumentAssert.notNull(accountingDate, "记账不存在");
        LocalDate date = accountingDate.getRecordingDate();
        String key = DateUtils.format(date, DateUtils.DEFAULT_DATE_FORMAT).concat("_ACCOUNTING");
        boolean lock = false;
        try {
            lock = distributedLock.lock(key, 3);
            if (!lock) {
                ArgumentAssert.isFalse(true
                        , "请稍候再试");
            }
            if (accountingInfo.getIsAssessed()) {
                return delByAssessed(accountingInfo);
            }
            long count = baseAccountingInfoManager.count(Wraps.<BaseAccountingInfo>lbQ()
                    .eq(BaseAccountingInfo::getAccountingDateId, accountingInfo.getAccountingDateId()));
            //判断是否为最后一条明细 为最后一条明细则删除
            if (count == 1) {
                baseAccountingInfoManager.removeById(accountingInfo);
                return superManager.removeById(accountingInfo.getAccountingDateId());
            }
            //减少相应金额
            if (Objects.equals(accountingInfo.getType(), AccountingTypeEnum.PAY_IN.getCode())) {
                accountingDate.setPayIn(accountingDate.getPayIn().subtract(accountingInfo.getAmount()));
            }
            if (Objects.equals(accountingInfo.getType(), AccountingTypeEnum.PAY_OUT.getCode())) {
                accountingDate.setPayOut(accountingDate.getPayOut().subtract(accountingInfo.getAmount()));
            }
            baseAccountingInfoManager.removeById(accountingInfo);
            return superManager.updateById(accountingDate);
        } finally {
            if (lock) {
                distributedLock.releaseLock(key);
            }
        }
    }

    private boolean delByAssessed(BaseAccountingInfo accountingInfo) {
        List<BaseAccountingInfo> accountingInfos = baseAccountingInfoService.list(Wraps.<BaseAccountingInfo>lbQ()
                .eq(BaseAccountingInfo::getDeleteFlag, 0)
                .eq(BaseAccountingInfo::getIsAssessed, true)
                .eq(BaseAccountingInfo::getUuid, accountingInfo.getUuid())
        );
        if (CollUtil.isEmpty(accountingInfos)) {
            return false;
        }
        Map<Long, List<BaseAccountingInfo>> accountingInfoMap = accountingInfos.stream().collect(Collectors.groupingBy(BaseAccountingInfo::getAccountingDateId));
        Map<Long, BaseAccountingDate> accountingDateMap = superManager.list(Wraps.<BaseAccountingDate>lbQ()
                        .eq(BaseAccountingDate::getDeleteFlag, 0)
                        .in(BaseAccountingDate::getId, accountingInfoMap.keySet()))
                .stream().collect(Collectors.toMap(BaseAccountingDate::getId, Function.identity()));
        for (BaseAccountingDate accountingDate : accountingDateMap.values()) {
            List<BaseAccountingInfo> accountingInfoList = accountingInfoMap.get(accountingDate.getId());
            BigDecimal amount = accountingInfoList.stream().map(BaseAccountingInfo::getAmount)
                    .filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            String type = accountingInfoList.get(0).getType();
            if (type.equals(AccountingTypeEnum.PAY_IN.getCode())) {
                accountingDate.setPayIn(accountingDate.getPayIn().subtract(amount));
            } else {
                accountingDate.setPayOut(accountingDate.getPayOut().subtract(amount));
            }
            if (accountingDate.getPayIn().add(accountingDate.getPayOut()).compareTo(BigDecimal.ZERO) <= 0) {
                accountingDate.setDeleteFlag(1);
            }
        }
        List<BaseAccountingDate> accountingDateList = accountingDateMap.values().stream().filter(v -> v.getDeleteFlag() == 0).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(accountingDateMap)) {
            superManager.updateBatchById(accountingDateList);
        }
        superManager.removeByIds(accountingDateMap.values().stream().filter(v -> v.getDeleteFlag() == 1)
                .map(BaseAccountingDate::getId).collect(Collectors.toList()));
        return baseAccountingInfoService.removeByIds(accountingInfos.stream().map(BaseAccountingInfo::getId).collect(Collectors.toList()));
    }


    @Override
    public void addAccounting(List<PosCashPayment> cashPaymentList, LocalDate localDate) {
        List<Long> collect = cashPaymentList.stream().map(PosCashPayment::getPayTypeId).distinct().collect(Collectors.toList());
        Map<Long, BasePaymentType> basePaymentTypeMap = paymentTypeService.list(Wraps.<BasePaymentType>lbQ()
                        .in(BasePaymentType::getId, collect).eq(BasePaymentType::getDeleteFlag, 0))
                .stream().collect(Collectors.toMap(BasePaymentType::getId, k -> k));
        Map<Long, List<PosCashPayment>> cashPaymentMap = cashPaymentList.stream().collect(Collectors.groupingBy(PosCashPayment::getPayTypeId));
        List<BaseAccountingInfo> list = cashPaymentMap.entrySet().stream()
                .filter(entry -> basePaymentTypeMap.containsKey(entry.getKey()))
                .map(entry -> {
                    Long key = entry.getKey();
                    BasePaymentType basePaymentType = basePaymentTypeMap.get(key);
                    BigDecimal amount = calculateAmount(basePaymentType, entry.getValue());
                    BigDecimal giftAmount = calculateGiftAmount(basePaymentType, entry.getValue());
                    amount = amount.add(giftAmount);

                    if (amount.compareTo(BigDecimal.ZERO) > 0) {
                        return BaseAccountingInfo.builder()
                                .accountingDateId(0L).createdOrgId(ContextUtil.getCurrentCompanyId())
                                .recordingDate(localDate).amount(amount).giftAmount(giftAmount)
                                .payInType(BizConstant.DEFAULT_GRADE.toString()).payTypeId(key)
                                .payOutType("").remarks("订单结算").source("ORDER").isAssessed(false)
                                .uuid("0")
                                .sourceId(entry.getValue().get(0).getCashId()).type(AccountingTypeEnum.PAY_IN.getCode())
                                .managerId(ContextUtil.getEmployeeId())
                                .build();
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(list)) {
            return;
        }
        BigDecimal add = list.stream().map(BaseAccountingInfo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                .add(list.stream().map(BaseAccountingInfo::getGiftAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        BaseAccountingDate accountingDate = superManager.getOne(Wraps.<BaseAccountingDate>lbQ()
                .eq(BaseAccountingDate::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(BaseAccountingDate::getRecordingDate, localDate));
        //新增记账日期
        if (ObjectUtil.isNull(accountingDate)) {
            accountingDate = BaseAccountingDate.builder()
                    .createdOrgId(ContextUtil.getCurrentCompanyId()).recordingDate(localDate)
                    .week(Week.valueOf(localDate.getDayOfWeek().name()).toChinese("星期"))
                    .payIn(add)
                    .payOut(BigDecimal.ZERO)
                    .build();
            accountingDate.setUpdatedBy(ContextUtil.getUserId());
            accountingDate.setUpdatedTime(LocalDateTime.now());
            accountingDate.setCreatedBy(ContextUtil.getUserId());
            accountingDate.setCreatedTime(LocalDateTime.now());
            superManager.save(accountingDate);
        } else {
            accountingDate.setPayIn(accountingDate.getPayIn().add(add));
            accountingDate.setUpdatedBy(ContextUtil.getUserId());
            accountingDate.setUpdatedTime(LocalDateTime.now());
            superManager.updateById(accountingDate);
        }
        BaseAccountingDate finalAccountingDate = accountingDate;
        list.forEach(model -> {
            model.setAccountingDateId(finalAccountingDate.getId());
            model.setRecordingDate(finalAccountingDate.getRecordingDate());
        });
        baseAccountingInfoService.saveBatch(list);
    }

    private BaseAccountingInfo getAccountingInfo(BaseAccountingDate accountingDate, BaseAccountingSaveVO model) {
        return BaseAccountingInfo.builder()
                .accountingDateId(accountingDate.getId()).createdOrgId(ContextUtil.getCurrentCompanyId())
                .recordingDate(accountingDate.getRecordingDate()).amount(model.getAmount())
                .payInType(model.getPayInType()).payTypeId(model.getPayTypeId())
                .payOutType(model.getPayOutType()).remarks(model.getRemarks())
                .source("HAND").sourceId(0L).isAssessed(false).uuid("0").managerId(model.getManagerId())
                .type(model.getType()).build();
    }

    private BaseAccountingDate getAccountingDate(BaseAccountingSaveVO model) {
        BaseAccountingDate accountingDate = superManager.getOne(Wraps.<BaseAccountingDate>lbQ()
                .eq(BaseAccountingDate::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(BaseAccountingDate::getRecordingDate, model.getRecordingDate()));
        //新增记账日期
        if (ObjectUtil.isNull(accountingDate)) {
            accountingDate = BaseAccountingDate.builder()
                    .createdOrgId(ContextUtil.getCurrentCompanyId()).recordingDate(model.getRecordingDate())
                    .week(Week.valueOf(model.getRecordingDate().getDayOfWeek().name()).toChinese("星期"))
                    .payIn(Objects.equals(model.getType(), AccountingTypeEnum.PAY_IN.getCode()) ? model.getAmount() : BigDecimal.ZERO)
                    .payOut(Objects.equals(model.getType(), AccountingTypeEnum.PAY_OUT.getCode()) ? model.getAmount() : BigDecimal.ZERO)
                    .build();
            accountingDate.setUpdatedBy(ContextUtil.getUserId());
            accountingDate.setUpdatedTime(LocalDateTime.now());
            accountingDate.setCreatedBy(ContextUtil.getUserId());
            accountingDate.setCreatedTime(LocalDateTime.now());
        } else {
            accountingDate.setPayIn(Objects.equals(model.getType(), AccountingTypeEnum.PAY_IN.getCode())
                    ? accountingDate.getPayIn().add(model.getAmount()) : accountingDate.getPayIn());
            accountingDate.setPayOut(Objects.equals(model.getType(), AccountingTypeEnum.PAY_OUT.getCode())
                    ? accountingDate.getPayOut().add(model.getAmount()) : accountingDate.getPayOut());
            accountingDate.setUpdatedBy(ContextUtil.getUserId());
            accountingDate.setUpdatedTime(LocalDateTime.now());
        }
        return accountingDate;
    }


    private BigDecimal calculateAmount(BasePaymentType basePaymentType, List<PosCashPayment> payments) {
        if (basePaymentType.getIncomeFlag() != null && basePaymentType.getIncomeFlag() == 1) {
            return payments.stream()
                    .map(payment -> ObjectUtil.defaultIfNull(payment.getAmount(), BigDecimal.ZERO)
                            .subtract(ObjectUtil.defaultIfNull(payment.getChangeAmount(), BigDecimal.ZERO))
                            .subtract(ObjectUtil.defaultIfNull(payment.getRefundAmount(), BigDecimal.ZERO)))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        return BigDecimal.ZERO;
    }

    private BigDecimal calculateGiftAmount(BasePaymentType basePaymentType, List<PosCashPayment> payments) {
        if (basePaymentType.getBizType().equals(PaymentBizTypeEnum.ACCOUNT.getCode()) &&
                basePaymentType.getGiftIncomeFlag() != null && basePaymentType.getGiftIncomeFlag() == 1) {
            return payments.stream().map(PosCashPayment::getGiftAmount)
                    .filter(ObjectUtil::isNotNull)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        }
        return BigDecimal.ZERO;
    }

    @Override
    public void reductionAccounting(List<PosCashPayment> cashPaymentList, LocalDate localDate) {
        List<BaseAccountingInfo> list = baseAccountingInfoService.list(Wraps.<BaseAccountingInfo>lbQ()
                .eq(BaseAccountingInfo::getSource, "ORDER")
                .eq(BaseAccountingInfo::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(BaseAccountingInfo::getSourceId, cashPaymentList.get(0).getCashId())
                .eq(BaseAccountingInfo::getType, AccountingTypeEnum.PAY_IN.getCode())
                .eq(BaseAccountingInfo::getPayInType, BizConstant.DEFAULT_GRADE.toString())
                .eq(BaseAccountingInfo::getDeleteFlag, 0));
        if (CollUtil.isEmpty(list)) {
            return;
        }
        BaseAccountingDate accountingDate = superManager.getOne(Wraps.<BaseAccountingDate>lbQ()
                .eq(BaseAccountingDate::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(BaseAccountingDate::getId, list.get(0).getAccountingDateId()));
        BigDecimal add = list.stream().map(BaseAccountingInfo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add)
                .add(list.stream().map(BaseAccountingInfo::getGiftAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        accountingDate.setPayIn(accountingDate.getPayIn().subtract(add).setScale(2, RoundingMode.HALF_UP));
        if ((accountingDate.getPayIn().add(accountingDate.getPayOut())).compareTo(BigDecimal.ZERO) <= 0) {
            accountingDate.setDeleteFlag(1);
        }
        ArgumentAssert.isFalse(!superManager.updateById(accountingDate), "操作失败！");
        ArgumentAssert.isFalse(!baseAccountingInfoService.remove(Wraps.<BaseAccountingInfo>lbQ()
                .eq(BaseAccountingInfo::getSource, "ORDER")
                .eq(BaseAccountingInfo::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(BaseAccountingInfo::getSourceId, cashPaymentList.get(0).getCashId())
                .eq(BaseAccountingInfo::getType, AccountingTypeEnum.PAY_IN.getCode())
                .eq(BaseAccountingInfo::getPayInType, BizConstant.DEFAULT_GRADE.toString())
                .eq(BaseAccountingInfo::getDeleteFlag, 0)), "操作失败！");

    }

    /**
     * @param begin 开始日期
     * @param end   结束日期
     * @return 开始与结束之间的所以日期，包括起止
     */
    public List<LocalDate> getMiddleDate(LocalDate begin, LocalDate end) {
        List<LocalDate> localDateList = new ArrayList<>();
        long length = end.toEpochDay() - begin.toEpochDay();
        for (long i = length; i >= 0; i--) {
            localDateList.add(end.minusDays(i));
        }
        localDateList.sort(Comparator.comparing(LocalDate::toEpochDay));
        return localDateList;
    }

    @Override
    public BaseAccountingVO detail(Long id) {
        BaseAccountingInfo accountingInfo = baseAccountingInfoManager.getById(id);
        ArgumentAssert.notNull(accountingInfo, "记账信息不存在");
        BaseAccountingDate accountingDate = superManager.getById(accountingInfo.getAccountingDateId());
        ArgumentAssert.notNull(accountingDate, "记账不存在");
        BaseAccountingVO build = BaseAccountingVO.builder()
                .id(accountingInfo.getId())
                .amount(accountingInfo.getAmount())
                .isAssessed(accountingInfo.getIsAssessed())
                .recordingDate(accountingDate.getRecordingDate())
                .payInType(accountingInfo.getPayInType())
                .payOutType(accountingInfo.getPayOutType())
                .payTypeId(accountingInfo.getPayTypeId())
                .remarks(accountingInfo.getRemarks())
                .type(accountingInfo.getType()).managerId(accountingInfo.getManagerId())
                .build();
        if (accountingInfo.getIsAssessed()) {
            List<BaseAccountingInfo> list = baseAccountingInfoService.list(Wraps.<BaseAccountingInfo>lbQ()
                    .eq(BaseAccountingInfo::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                    .eq(BaseAccountingInfo::getUuid, accountingInfo.getUuid())
                    .eq(BaseAccountingInfo::getDeleteFlag, 0));
            build.setAmount(list.stream().map(BaseAccountingInfo::getAmount)
                    .filter(ObjectUtil::isNotNull).reduce(BigDecimal.ZERO, BigDecimal::add));
            List<BaseAccountingDate> accountingDateList = superManager.list(Wraps.<BaseAccountingDate>lbQ()
                    .eq(BaseAccountingDate::getDeleteFlag, 0)
                    .in(BaseAccountingDate::getId, list.stream().map(BaseAccountingInfo::getAccountingDateId).collect(Collectors.toList())));
            Optional<LocalDate> maxBirthDate = accountingDateList.stream()
                    .map(BaseAccountingDate::getRecordingDate)
                    .max(Comparator.naturalOrder());
            build.setAssessedEndTime(maxBirthDate.get());
            maxBirthDate = accountingDateList.stream()
                    .map(BaseAccountingDate::getRecordingDate)
                    .min(Comparator.naturalOrder());
            build.setAssessedStartTime(maxBirthDate.get());
        }
        return build;
    }

    @Override
    public List<AccountingCalenderResultVO> calendar(AccountingQuery params) {
        AccountingCalendarTypeEnum accountingCalendarTypeEnum = AccountingCalendarTypeEnum.get(params.getType());
        // 记账管理
        DataOverviewQuery incomeQuery = new DataOverviewQuery();
        incomeQuery.setStartDate(params.getStartDate());
        incomeQuery.setEndDate(params.getEndDate());
        R<DataOverviewQuery> storeTime = customersApi.getStoreTime(incomeQuery);
        DataOverviewQuery overviewQuery = storeTime.getData();
        incomeQuery.setStartDate(overviewQuery.getStartDate());
        incomeQuery.setEndDate(overviewQuery.getEndDate());
        incomeQuery.setHour(overviewQuery.getHour());
        QueryWrapper<PosCash> wrapper = getPosCashQueryWrapper(incomeQuery);
        String field = "";
        switch (accountingCalendarTypeEnum) {
            case WEEK:
            case MONTH:
                field = "%Y-%m-%d";
                break;
            case YEAR:
                field = "%Y-%m";
                break;
            default:
                ArgumentAssert.isTrue(false, "暂不支持此时间类型");
                break;
        }
        Map<String, AmountResultVO> incomeAmountMap = new HashMap<>();
        List<AmountResultVO> amountResultVOList = baseAccountingInfoManager.calendarIn(wrapper, incomeQuery.getHour(), field);
        if (CollUtil.isNotEmpty(amountResultVOList)) {
            incomeAmountMap = amountResultVOList.stream().collect(Collectors.toMap(AmountResultVO::getField, Function.identity()));
        }
        QueryWrapper<BaseAccountingInfo> queryWrapper = getBaseAccountingInfoQueryWrapper(params);
        switch (accountingCalendarTypeEnum) {
            case WEEK:
            case MONTH:
                field = "bai.recording_date";
                break;
            case YEAR:
                field = "DATE_FORMAT(bai.recording_date, '%Y-%m')";
                break;
            default:
                ArgumentAssert.isTrue(false, "暂不支持此时间类型");
                break;
        }
        List<AccountingCalenderResultVO> calendar = baseAccountingInfoManager.calendar(field, queryWrapper);
        List<AccountingCalenderResultVO> resultVOList = new ArrayList<>();
        // 获取所有的时间范围， 只有周月和年
        switch (accountingCalendarTypeEnum) {
            case WEEK:
            case MONTH:
                List<String> betweenDateList = getBetweenDateList(params.getStartDate(), params.getEndDate());
                for (String date : betweenDateList) {
                    resultVOList.add(AccountingCalenderResultVO.builder()
                            .recordingDate(date)
                            .build());
                }
                break;
            case YEAR:
                List<String> betweenMonthList = getBetweenMonthList(params.getStartDate(), params.getEndDate());
                for (String date : betweenMonthList) {
                    resultVOList.add(AccountingCalenderResultVO.builder()
                            .recordingDate(date)
                            .build());
                }
                break;
            default:
                ArgumentAssert.isTrue(false, "暂不支持此时间类型");
                break;
        }
        if (CollUtil.isNotEmpty(calendar)) {
            Map<String, AccountingCalenderResultVO> map = calendar.stream().collect(Collectors.toMap(AccountingCalenderResultVO::getRecordingDate, Function.identity()));
            Map<String, AmountResultVO> finalIncomeAmountMap = incomeAmountMap;
            resultVOList.forEach(s -> {
                AmountResultVO amountResultVO = finalIncomeAmountMap.get(s.getRecordingDate());
                AccountingCalenderResultVO accountingCalenderResultVO = map.get(s.getRecordingDate());
                if (Objects.nonNull(amountResultVO)) {
                    s.setPayInAmount(amountResultVO.getAmount());
                } else {
                    s.setPayInAmount(BigDecimal.ZERO);
                }
                if (Objects.nonNull(accountingCalenderResultVO)) {
                    //s.setPayInAmount(accountingCalenderResultVO.getPayInAmount());
                    s.setPayOutAmount(accountingCalenderResultVO.getPayOutAmount());
                    s.setManualPayInAmount(accountingCalenderResultVO.getManualPayInAmount());
                    s.setManualPayOutAmount(accountingCalenderResultVO.getManualPayOutAmount());
                    //s.setBalance(accountingCalenderResultVO.getBalance());
                    s.setPayInAmount(s.getPayInAmount().add(s.getManualPayInAmount()));
                } else {
                    //s.setPayInAmount(BigDecimal.ZERO);
                    s.setPayOutAmount(BigDecimal.ZERO);
                    s.setManualPayInAmount(BigDecimal.ZERO);
                    s.setManualPayOutAmount(BigDecimal.ZERO);
                    //s.setBalance(BigDecimal.ZERO);
                }
                s.setBalance(s.getPayInAmount().subtract(s.getPayOutAmount()));
            });
        }
        return resultVOList;
    }

    @Override
    public AccountingCalenderSumResultVO calendarSum(AccountingQuery params) {
        AccountingCalendarTypeEnum accountingCalendarTypeEnum = AccountingCalendarTypeEnum.get(params.getType());
        DataOverviewQuery incomeQuery = new DataOverviewQuery();
        incomeQuery.setStartDate(params.getStartDate());
        incomeQuery.setEndDate(params.getEndDate());
        R<DataOverviewQuery> storeTime = customersApi.getStoreTime(incomeQuery);
        DataOverviewQuery overviewQuery = storeTime.getData();
        incomeQuery.setStartDate(overviewQuery.getStartDate());
        incomeQuery.setEndDate(overviewQuery.getEndDate());
        incomeQuery.setHour(overviewQuery.getHour());
        QueryWrapper<PosCash> wrapper = getPosCashQueryWrapper(incomeQuery);
        List<AmountResultVO> amountResultVOList = baseAccountingInfoManager.calendarInList(wrapper);
        BigDecimal incomeAmount = amountResultVOList.stream().map(AmountResultVO::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        AccountingCalenderSumResultVO calendarSum = baseAccountingInfoManager.calendarSum(getBaseAccountingInfoQueryWrapper(params));
        calendarSum.setTotalPayInAmount(incomeAmount.add(calendarSum.getManualPayInAmount()));
        calendarSum.setTotalBalance(calendarSum.getTotalPayInAmount().subtract(calendarSum.getTotalPayOutAmount()));
        //calendarSum.setAveragePayInAmount(incomeAmount.add(calendarSum.getManualPayInAmount()));
        Long betweenDay = null;
        switch (accountingCalendarTypeEnum) {
            case WEEK:
            case MONTH:
                betweenDay = DateUtil.betweenDay(DateUtil.parse(params.getStartDate()), DateUtil.parse(params.getEndDate()), true) + 1;
                break;
            case YEAR:
                betweenDay = DateUtil.betweenMonth(DateUtil.parse(params.getStartDate()), DateUtil.parse(params.getEndDate()), true) + 1;
                break;
            default:
                ArgumentAssert.isTrue(false, "暂不支持此时间类型");
                break;
        }
        if (Objects.nonNull(betweenDay)) {
            calendarSum.setAveragePayInAmount(calendarSum.getTotalPayInAmount().divide(new BigDecimal(betweenDay), 2, RoundingMode.HALF_UP));
            calendarSum.setAveragePayOutAmount(calendarSum.getTotalPayOutAmount().divide(new BigDecimal(betweenDay), 2, RoundingMode.HALF_UP));
        }
        return calendarSum;
    }

    @NotNull
    private static QueryWrapper<BaseAccountingInfo> getBaseAccountingInfoQueryWrapper(AccountingQuery params) {
        QueryWrapper<BaseAccountingInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("bai.delete_flag", 0).eq("bai.created_org_id", ContextUtil.getCurrentCompanyId());
        if (StringUtils.isNotBlank(params.getStartDate())) {
            queryWrapper.ge("bai.recording_date", params.getStartDate());
        }
        if (StringUtils.isNotBlank(params.getEndDate())) {
            queryWrapper.le("bai.recording_date", params.getEndDate());
        }
        if (CollUtil.isNotEmpty(params.getPayInTypeList())) {
            queryWrapper.in("bai.pay_in_type", params.getPayInTypeList());
        }
        if (CollUtil.isNotEmpty(params.getPayOutTypeList())) {
            queryWrapper.in("bai.pay_out_type", params.getPayOutTypeList());
        }
        if (StringUtils.isNotBlank(params.getKeywords())) {
            queryWrapper.like("bai.remarks", params.getKeywords());
        }
        if (ObjectUtil.isNotNull(params.getManagerId())) {
            queryWrapper.eq("bai.manager_id", params.getManagerId());
        }
        return queryWrapper;
    }

    @NotNull
    private static QueryWrapper<PosCash> getPosCashQueryWrapper(DataOverviewQuery incomeQuery) {
        QueryWrapper<PosCash> wrapper = new QueryWrapper<>();
        //营业收入
        wrapper.in("p.bill_state", Arrays.asList(PosCashBillStateEnum.COMPLETE.getCode(),
                        PosCashBillStateEnum.PART_REFUND.getCode(), PosCashBillStateEnum.REFUNDED.getCode()))
                .notIn("p.bill_type", Arrays.asList(PosCashBillTypeEnum.CANCELLATION.getCode(),
                        PosCashBillTypeEnum.CHARGEBACK.getCode())).eq("p.delete_flag", 0)
                .between("p.complete_time", incomeQuery.getStartDate(), incomeQuery.getEndDate())
                .notInSql("t.pay_type_id", "select id from base_payment_type where " +
                        "biz_type in ('1','8') and delete_flag = 0 ")
                .eq("t.status", PosCashPaymentStatusEnum.PAY_SUCCESS.getCode())
                .eq("t.delete_flag", 0)
                .eq("p.org_id", ContextUtil.getCurrentCompanyId());
        return wrapper;
    }

    @Override
    public IPage<BaseAccountingInfoResultVO> calendarDetail(PageParams<AccountingQuery> params) {
        params.setSort("");
        params.setOrder("");
        IPage<BaseAccountingInfo> page = params.buildPage(BaseAccountingInfo.class);
        AccountingQuery model = params.getModel();
        LbQueryWrap<BaseAccountingInfo> queryWrap = Wraps.<BaseAccountingInfo>lbQ();
        queryWrap.eq(BaseAccountingInfo::getDeleteFlag, 0)
                .in(CollUtil.isNotEmpty(model.getPayInTypeList()), BaseAccountingInfo::getPayInType, model.getPayInTypeList())
                .in(CollUtil.isNotEmpty(model.getPayOutTypeList()), BaseAccountingInfo::getPayOutType, model.getPayOutTypeList())
                .like(StringUtils.isNotBlank(model.getKeywords()), BaseAccountingInfo::getRemarks, model.getKeywords())
                .eq(ObjectUtil.isNotNull(model.getManagerId()), BaseAccountingInfo::getManagerId, model.getManagerId())
                .eq(StringUtils.isNotBlank(model.getAccountingType()), BaseAccountingInfo::getType, model.getAccountingType())
                .eq(BaseAccountingInfo::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .orderByDesc(SuperEntity::getCreatedTime);
        if (StringUtils.isNotBlank(model.getStartDate())) {
            queryWrap.ge(BaseAccountingInfo::getRecordingDate, model.getStartDate());
        }
        if (StringUtils.isNotBlank(model.getEndDate())) {
            queryWrap.le(BaseAccountingInfo::getRecordingDate, model.getEndDate());
        }
        IPage<BaseAccountingInfo> iPage = baseAccountingInfoManager.page(page, queryWrap);
        IPage<BaseAccountingInfoResultVO> resultVOIPage = BeanPlusUtil.toBeanPage(iPage, BaseAccountingInfoResultVO.class);
        echoService.action(resultVOIPage);
        return resultVOIPage;
    }

    @Override
    public List<BaseAccountingInfoResultVO> calendarDetailList(AccountingQuery model) {
        LbQueryWrap<BaseAccountingInfo> queryWrap = Wraps.<BaseAccountingInfo>lbQ();
        queryWrap.eq(BaseAccountingInfo::getDeleteFlag, 0)
                .in(CollUtil.isNotEmpty(model.getPayInTypeList()), BaseAccountingInfo::getPayInType, model.getPayInTypeList())
                .in(CollUtil.isNotEmpty(model.getPayOutTypeList()), BaseAccountingInfo::getPayOutType, model.getPayOutTypeList())
                .like(StringUtils.isNotBlank(model.getKeywords()), BaseAccountingInfo::getRemarks, model.getKeywords())
                .eq(ObjectUtil.isNotNull(model.getManagerId()), BaseAccountingInfo::getManagerId, model.getManagerId())
                .eq(StringUtils.isNotBlank(model.getAccountingType()), BaseAccountingInfo::getType, model.getAccountingType())
                .eq(BaseAccountingInfo::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .orderByDesc(SuperEntity::getCreatedTime);
        if (StringUtils.isNotBlank(model.getStartDate())) {
            queryWrap.ge(BaseAccountingInfo::getRecordingDate, model.getStartDate());
        }
        if (StringUtils.isNotBlank(model.getEndDate())) {
            queryWrap.le(BaseAccountingInfo::getRecordingDate, model.getEndDate());
        }
        List<BaseAccountingInfo> accountingInfoList = baseAccountingInfoManager.list(queryWrap);
        List<BaseAccountingInfoResultVO> resultVOList = BeanPlusUtil.toBeanList(accountingInfoList, BaseAccountingInfoResultVO.class);
        // 判断是不是收入, 收入只手动记账 + 订单的收入
        if (StringUtils.equals(model.getAccountingType(), AccountingTypeEnum.PAY_IN.getCode())) {
            resultVOList = resultVOList.stream().filter(s -> StringUtils.equals(s.getSource(), "HAND")).collect(Collectors.toList());
            // 记账收入
            DataOverviewQuery incomeQuery = new DataOverviewQuery();
            incomeQuery.setStartDate(model.getStartDate());
            incomeQuery.setEndDate(model.getEndDate());
            R<DataOverviewQuery> storeTime = customersApi.getStoreTime(incomeQuery);
            DataOverviewQuery overviewQuery = storeTime.getData();
            incomeQuery.setStartDate(overviewQuery.getStartDate());
            incomeQuery.setEndDate(overviewQuery.getEndDate());
            incomeQuery.setHour(overviewQuery.getHour());
            QueryWrapper<PosCash> wrapper = getPosCashQueryWrapper(incomeQuery);
            List<AmountResultVO> amountResultVOList = baseAccountingInfoManager.calendarInList(wrapper);
            if (CollUtil.isNotEmpty(amountResultVOList)) {
                for (AmountResultVO amountResultVO : amountResultVOList) {
                    resultVOList.add(BaseAccountingInfoResultVO.builder()
                            .echoMap(MapUtil.newHashMap())
                            .payName(amountResultVO.getName())
                            .accountingDateId(BizConstant.DEFAULT_GRADE)
                            .type(AccountingTypeEnum.PAY_IN.getCode())
                            .recordingDate(amountResultVO.getCompleteTime().toLocalDate())
                            .createdTime(amountResultVO.getCompleteTime())
                            .amount(amountResultVO.getAmount())
                            .payTypeId(Long.parseLong(amountResultVO.getField()))
                            .payInType(BizConstant.DEFAULT_GRADE.toString())
                            .source("ORDER")
                            .remarks("订单结算")
                            .sourceId(amountResultVO.getSourceId())
                            .isAssessed(false)
                            .amount(amountResultVO.getAmount())
                            .uuid("0")
                            .managerId(amountResultVO.getEmployeeId())
                            .build());
                }
            }
        }
        resultVOList.sort(Comparator.comparing(BaseAccountingInfoResultVO::getCreatedTime));
        echoService.action(resultVOList);
        resultVOList.forEach(s-> {
            if (StringUtils.isNotBlank(s.getPayName())) {
                s.getEchoMap().put("payTypeId", s.getPayName());
            }
        });
        return resultVOList;
    }


    public static List<String> getBetweenDateList(String startDate, String endDate) {
        List<String> dateList = new ArrayList<>();
        Date start = DateUtil.parse(startDate);
        Date end = DateUtil.parse(endDate);

        Date current = start;
        while (DateUtil.compare(current, end) <= 0) {
            dateList.add(DateUtil.format(current, "yyyy-MM-dd"));
            current = DateUtil.offsetDay(current, 1);
        }
        return dateList;
    }

    public static List<String> getBetweenMonthList(String startDate, String endDate) {
        List<String> monthList = new ArrayList<>();

        // 解析日期字符串为 DateTime 对象
        DateTime start = DateUtil.parse(startDate);
        DateTime end = DateUtil.parse(endDate);

        // 获取每个月的第一天
        DateTime currentMonth = DateUtil.beginOfMonth(start);
        DateTime endMonth = DateUtil.beginOfMonth(end);

        // 循环获取每个月
        while (!currentMonth.after(endMonth)) {
            // 格式化为 yyyy-MM
            String monthStr = DateUtil.format(currentMonth, "yyyy-MM");
            monthList.add(monthStr);
            // 增加一个月
            currentMonth = DateUtil.offsetMonth(currentMonth, 1);
        }

        return monthList;
    }

    public static void main(String[] args) {
        System.out.println(LocalDate.now().atStartOfDay());
    }
}


