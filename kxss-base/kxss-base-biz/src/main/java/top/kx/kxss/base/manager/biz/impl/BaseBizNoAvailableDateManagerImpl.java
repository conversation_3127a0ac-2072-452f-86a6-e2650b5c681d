package top.kx.kxss.base.manager.biz.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.biz.BaseBizNoAvailableDate;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.biz.BaseBizNoAvailableDateManager;
import top.kx.kxss.base.mapper.biz.BaseBizNoAvailableDateMapper;

/**
 * <p>
 * 通用业务实现类
 * 不可用日期
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-19 15:45:21
 * @create [2024-03-19 15:45:21] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseBizNoAvailableDateManagerImpl extends SuperManagerImpl<BaseBizNoAvailableDateMapper, BaseBizNoAvailableDate> implements BaseBizNoAvailableDateManager {

}


