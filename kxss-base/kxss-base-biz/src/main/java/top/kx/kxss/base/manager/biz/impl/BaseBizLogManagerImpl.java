package top.kx.kxss.base.manager.biz.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.jackson.JsonUtil;
import top.kx.kxss.base.entity.biz.BaseBizLog;
import top.kx.kxss.base.manager.biz.BaseBizLogManager;
import top.kx.kxss.base.mapper.biz.BaseBizLogMapper;
import top.kx.kxss.base.vo.result.biz.BaseBizLogResultVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.common.constant.RabbitMqConstant;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * <p>
 * 通用业务实现类
 * 模块业务日志
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-22 13:55:39
 * @create [2023-04-22 13:55:39] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
@DS(DsConstant.BASE_TENANT)
public class BaseBizLogManagerImpl extends SuperManagerImpl<BaseBizLogMapper, BaseBizLog> implements BaseBizLogManager {


    @Autowired
    private RabbitTemplate template;

    @Override
    public void createBizLog(BaseBizLog build) {
//        ContextUtil.setTenantBasePoolName(build.getTenantId());
//        ContextUtil.setCurrentCompanyId(build.getOrgId());
        build.setOpearteTime(LocalDateTime.now());
//        if (StringUtils.isBlank(build.getSn())) {
//            build.setSn(ContextUtil.getSn());
//        }
//        if (ObjectUtil.isNull(build.getMemberId())) {
//            build.setMemberId(ContextUtil.getMemberId());
//        }
//        if (ObjectUtil.isNull(build.getOrderSource())) {
//            build.setOrderSource(ContextUtil.getOrderSource());
//        }
//        if (ObjectUtil.isNull(build.getEmployeeId())) {
//            build.setEmployeeId(ContextUtil.getEmployeeId());
//        }
        build.setCreatedOrgId(build.getOrgId());
        build.setCreatedBy(ContextUtil.getUserId());
        build.setUpdatedBy(ContextUtil.getUserId());
        build.setUuid(UUID.randomUUID().toString());
//        build.setSn(StringUtils.isBlank(build.getSn()) ? ContextUtil.getSn() : build.getSn());
        template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.BIZ_LOG_NOTICE, JsonUtil.toJson(build));
    }

    @Override
    public IPage<BaseBizLogResultVO> selectPageResultVO(IPage<BaseBizLog> page, QueryWrap<BaseBizLog> wrap) {
        return baseMapper.selectPageResultVO(page, wrap);
    }

    @Override
    public IPage<BaseBizLogResultVO> selectMemberPageResultVO(IPage<BaseBizLog> page, QueryWrap<BaseBizLog> wrap) {
        return baseMapper.selectMemberPageResultVO(page, wrap);
    }
}


