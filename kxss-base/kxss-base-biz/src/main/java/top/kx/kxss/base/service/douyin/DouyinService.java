package top.kx.kxss.base.service.douyin;

import top.kx.kxss.base.entity.douyin.DouyinPrepare;
import top.kx.kxss.base.entity.groupBuy.BaseGroupBuy;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 *  抖音
 *
 * <AUTHOR>
 */
public interface DouyinService {

    /**
     * 获取 抖音 client_token
     * @return
     */
    String clientToken();

    /**
     * 获取授权地址
     *
     * @return
     */
    String authUrl();

    /**
     * 是否绑定
     *
     * @return
     */
    Boolean isBind();

    /**
     * 获取团购券码信息
     * @param securitiesNumber 券码,存在两种情况,一个是纯数字,一个是短连接,需要哦到后端去换取加密后的信息
     */
    DouyinPrepare prepare(String securitiesNumber);

    void getconsumed(DouyinPrepare prepare, String securitiesNumber);

    /**
     * 核销券
     * @param securitiesNumber 券码
     * @return
     */
    DouyinPrepare consume(String securitiesNumber);

    /**
     * 撤销验券
     *
     * @param securitiesNumber
     * @return
     */
    Boolean reverseConsume(String securitiesNumber);

    /**
     * 获取团购信息
     * @return
     */
    List<BaseGroupBuy> groupBuyList();

    DouyinPrepare consume(DouyinPrepare prepare, String securitiesNumber);

    DouyinPrepare consume(String securitiesNumber, String dealGroupId);

    void subscribeEvent(Map<String, Object> map, HttpServletRequest request, HttpServletResponse response);

}
