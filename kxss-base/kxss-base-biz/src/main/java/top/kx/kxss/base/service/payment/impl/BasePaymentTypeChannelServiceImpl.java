package top.kx.kxss.base.service.payment.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.payment.BaseBankCardInfo;
import top.kx.kxss.base.entity.payment.BasePaymentType;
import top.kx.kxss.base.entity.payment.BasePaymentTypeChannel;
import top.kx.kxss.base.manager.payment.BasePaymentTypeChannelManager;
import top.kx.kxss.base.service.payment.BaseBankCardInfoService;
import top.kx.kxss.base.service.payment.BasePaymentTypeChannelService;
import top.kx.kxss.base.service.payment.BasePaymentTypeService;
import top.kx.kxss.base.vo.query.payment.BasePaymentTypeChannelPageQuery;
import top.kx.kxss.base.vo.result.payment.BaseMchAppResultVO;
import top.kx.kxss.base.vo.result.payment.BasePaymentTypeChannelResultVO;
import top.kx.kxss.base.vo.save.payment.BasePaymentTypeChannelSaveVO;
import top.kx.kxss.base.vo.update.payment.BasePaymentTypeChannelUpdateVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.base.PayChannelEnum;
import top.kx.kxss.pay.MchAppApi;
import top.kx.kxss.pay.entity.MchApp;
import top.kx.kxss.pay.vo.query.MchAppPageQuery;
import top.kx.kxss.pay.vo.result.IsvReconciliationInfoResultVO;
import top.kx.kxss.pay.vo.result.MchAppResultVO;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 业务实现类
 * 支付方式对应渠道
 * </p>
 *
 * <AUTHOR>
 * @date 2024-06-12 10:18:05
 * @create [2024-06-12 10:18:05] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
public class BasePaymentTypeChannelServiceImpl extends SuperServiceImpl<BasePaymentTypeChannelManager, Long, BasePaymentTypeChannel, BasePaymentTypeChannelSaveVO,
        BasePaymentTypeChannelUpdateVO, BasePaymentTypeChannelPageQuery, BasePaymentTypeChannelResultVO> implements BasePaymentTypeChannelService {

    @Autowired
    private BasePaymentTypeService paymentTypeService;
    @Autowired
    private MchAppApi mchAppApi;
    @Autowired
    private BaseBankCardInfoService baseBankCardInfoService;

    @Override
    protected BasePaymentTypeChannel saveBefore(BasePaymentTypeChannelSaveVO model) {
        model.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        BasePaymentType byId = paymentTypeService.getById(model.getPayTypeId());
        ArgumentAssert.notNull(byId, "不存在支付方式");
        model.setPayName(byId.getName());
        R<MchApp> detail = mchAppApi.getByAppId(model.getAppId());
        ArgumentAssert.isFalse(!detail.getIsSuccess(), detail.getMsg());
        ArgumentAssert.notNull(detail.getData(), "不存在商户");
        MchApp mchApp = detail.getData();
        model.setMchNo(mchApp.getMchNo());
        model.setAppId(mchApp.getAppId());
        model.setAppSecret(mchApp.getAppSecret());
        String remarks = "【" + mchApp.getAppName() + "】";
        if (StrUtil.isNotBlank(model.getRemarks())) {
            remarks = remarks.concat(model.getRemarks());
        }
        model.setRemarks(remarks);
        return super.saveBefore(model);
    }

    @Override
    protected BasePaymentTypeChannel updateBefore(BasePaymentTypeChannelUpdateVO model) {
        BasePaymentType byId = paymentTypeService.getById(model.getPayTypeId());
        ArgumentAssert.notNull(byId, "不存在支付方式");
        model.setPayName(byId.getName());
        R<MchApp> detail = mchAppApi.getByAppId(model.getAppId());
        ArgumentAssert.isFalse(!detail.getIsSuccess(), detail.getMsg());
        ArgumentAssert.notNull(detail.getData(), "不存在商户");
        MchApp mchApp = detail.getData();
        model.setMchNo(mchApp.getMchNo());
        model.setAppId(mchApp.getAppId());
        model.setAppSecret(mchApp.getAppSecret());
        String remarks = "【" + mchApp.getAppName() + "】";
        if (StrUtil.isNotBlank(model.getRemarks())) {
            remarks = remarks.concat(model.getRemarks());
        }
        model.setRemarks(remarks);
        return super.updateBefore(model);
    }

    @Override
    public BasePaymentTypeChannel getOne(LbQueryWrap<BasePaymentTypeChannel> eq) {
        return superManager.getOne(eq);
    }

    @Override
    public List<BaseMchAppResultVO> queryMchApp() {
        MchAppPageQuery build = MchAppPageQuery.builder().build();
        R<List<MchAppResultVO>> query = mchAppApi.query(build);
        ArgumentAssert.isFalse(!query.getIsSuccess(), query.getMsg());
        return BeanUtil.copyToList(query.getData(), BaseMchAppResultVO.class);
    }

    @Override
    public List<BaseMchAppResultVO> queryMchApp(MchAppPageQuery pageQuery) {
        if (StrUtil.isBlank(pageQuery.getAppId())) {
            return Collections.emptyList();
        }
        R<List<MchAppResultVO>> query = mchAppApi.query(pageQuery);
        ArgumentAssert.isFalse(!query.getIsSuccess(), query.getMsg());
        return BeanUtil.copyToList(query.getData(), BaseMchAppResultVO.class);
    }

    @Override
    public boolean saveBankCard(BasePaymentTypeChannel data) {
        String payChannel = data.getPayChannel();
        if (StrUtil.isBlank(payChannel)) {
            return false;
        }
        PayChannelEnum payChannelEnum = PayChannelEnum.get(payChannel);
        ArgumentAssert.notNull(payChannelEnum, "不存在渠道信息");
        String ifCode = payChannelEnum.getIndex().concat("pay").toLowerCase();
        R<IsvReconciliationInfoResultVO> resultVO = mchAppApi.queryReconciliationParam(data.getAppId(), ifCode);
        ArgumentAssert.isFalse(!resultVO.getIsSuccess(), resultVO.getMsg());
        baseBankCardInfoService.removeByIds(Collections.singletonList(data.getId()));
        IsvReconciliationInfoResultVO resultVOData = resultVO.getData();
        BaseBankCardInfo build = BaseBankCardInfo.builder().payChannel(data.getPayChannel())
                .bankName("-").cardNumber("-").merchantName(data.getPayName())
                .payeeType("-").payeeName("-").createdOrgId(ContextUtil.getCurrentCompanyId())
                .effectiveDate(LocalDate.now())
                .feeRate(new BigDecimal("0.0038")).merchantCode(resultVOData.getMchNo())
                .platformAppId(data.getAppId()).status(true).paymentPlatform(payChannelEnum.getDesc().concat("支付"))
                .build();
        build.setId(null);
        return baseBankCardInfoService.save(build);
    }
}


