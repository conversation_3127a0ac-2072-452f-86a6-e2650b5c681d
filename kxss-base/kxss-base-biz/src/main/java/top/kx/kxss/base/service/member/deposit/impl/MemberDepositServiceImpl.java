package top.kx.kxss.base.service.member.deposit.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.CodeUtils;
import top.kx.kxss.base.entity.member.deposit.*;
import top.kx.kxss.base.manager.member.deposit.*;
import top.kx.kxss.base.service.member.deposit.MemberDepositService;
import top.kx.kxss.base.vo.query.member.deposit.MemberDepositPageQuery;
import top.kx.kxss.base.vo.result.member.deposit.MemberDepositResultVO;
import top.kx.kxss.base.vo.save.member.deposit.MemberDepositRuleSaveVO;
import top.kx.kxss.base.vo.save.member.deposit.MemberDepositSaveVO;
import top.kx.kxss.base.vo.update.member.deposit.MemberDepositRuleUpdateVO;
import top.kx.kxss.base.vo.update.member.deposit.MemberDepositUpdateVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.base.CodeIdentifyEnum;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 储值信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-14 09:59:09
 * @create [2023-04-14 09:59:09] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class MemberDepositServiceImpl extends SuperServiceImpl<MemberDepositManager, Long, MemberDeposit, MemberDepositSaveVO,
        MemberDepositUpdateVO, MemberDepositPageQuery, MemberDepositResultVO> implements MemberDepositService {

    @Autowired
    private MemberDepositRuleManager depositRuleManager;
    @Autowired
    private MemberDepositCouponManager couponManager;
    @Autowired
    private MemberDepositProductManager productManager;
    @Autowired
    private MemberDepositCardManager cardManager;


    @Override
    public String getCode() {
        String code = CodeUtils.generateCode(CodeIdentifyEnum.CZ.getCode(), true, CodeUtils.YYYY_MM_DD, 6);
        if (checkCode(code, null)) {
            return getCode();
        }
        return code;
    }

    @Override
    public Boolean checkCode(String code, Long id) {
        return depositRuleManager.count(Wraps.<MemberDepositRule>lbQ().ne(MemberDepositRule::getId, id)
                .eq(MemberDepositRule::getCode, code)) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MemberDeposit saveDeposit(MemberDepositSaveVO model) {
        if (model.getIsDesposit() && CollUtil.isEmpty(model.getDepositRuleList())) {
            ArgumentAssert.isTrue(false, "请设置规则信息");
        }
        Long orgId = ContextUtil.getCurrentCompanyId();
        MemberDeposit deposit = superManager.getOne(Wraps.<MemberDeposit>lbQ().eq(MemberDeposit::getCreatedOrgId, orgId));
        if (ObjectUtil.isNotNull(deposit)) {
            MemberDepositUpdateVO memberDepositUpdateVO = BeanUtil.copyProperties(model, MemberDepositUpdateVO.class);
            memberDepositUpdateVO.setId(deposit.getId());
            MemberDeposit memberDeposit = updateById(memberDepositUpdateVO);
            depositRule(BeanUtil.copyProperties(memberDepositUpdateVO, MemberDepositSaveVO.class), memberDeposit);
            return memberDeposit;
        }
        model.setCreatedOrgId(orgId);
        model.setIsDef(model.getIsDef() != null && model.getIsDef());
        MemberDeposit memberDeposit = save(model);
        depositRule(model, memberDeposit);
        return memberDeposit;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MemberDeposit updateDeposit(MemberDepositUpdateVO memberDepositUpdateVO) {
        if (memberDepositUpdateVO.getIsDesposit() && CollUtil.isEmpty(memberDepositUpdateVO.getDepositRuleList())) {
            ArgumentAssert.isTrue(false, "请设置规则信息");
        }
        Long orgId = ContextUtil.getCurrentCompanyId();
        MemberDeposit memberDeposit;
        if (ObjectUtil.isNull(memberDepositUpdateVO)) {
            memberDepositUpdateVO.setCreatedOrgId(orgId);
            memberDeposit = save(BeanUtil.copyProperties(memberDepositUpdateVO, MemberDepositSaveVO.class));
        } else {
            memberDeposit = updateById(memberDepositUpdateVO);
        }
        depositRule(BeanUtil.copyProperties(memberDepositUpdateVO, MemberDepositSaveVO.class), memberDeposit);
        return memberDeposit;
    }

    @Override
    public MemberDepositRule addDepositRule(MemberDepositRuleSaveVO depositRuleSaveVO) {
        MemberDepositRule memberDepositRule = BeanUtil.copyProperties(depositRuleSaveVO, MemberDepositRule.class);
        if (StrUtil.isBlank(memberDepositRule.getCode())) {
            memberDepositRule.setCode(getCode());
        }
        if (ObjectUtil.isNull(memberDepositRule.getFreeHours())) {
            memberDepositRule.setFreeHours(0);
        }
        if (ObjectUtil.isNull(memberDepositRule.getIssueNum())) {
            memberDepositRule.setIssueNum(0);
        }
        if (ObjectUtil.isNull(memberDepositRule.getLimitedNum())) {
            memberDepositRule.setLimitedNum(0);
        }
        if (ObjectUtil.isNull(memberDepositRule.getClaimedNum())) {
            memberDepositRule.setClaimedNum(0);
        }
        if (CollUtil.isEmpty(depositRuleSaveVO.getGradeIds())) {
            memberDepositRule.setGradeIds(null);
        }
        depositRuleManager.save(memberDepositRule);
        List<MemberDepositProduct> productList = Lists.newArrayList();
        List<MemberDepositCard> cardList = Lists.newArrayList();
        List<MemberDepositCoupon> couponList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(depositRuleSaveVO.getProductList())) {
            productManager.remove(Wraps.<MemberDepositProduct>lbQ().eq(MemberDepositProduct::getDepositRuleId, memberDepositRule.getId()));
            productList.addAll(depositRuleSaveVO.getProductList().stream().map(p -> {
                MemberDepositProduct product = BeanUtil.copyProperties(p, MemberDepositProduct.class);
                product.setDepositRuleId(memberDepositRule.getId());
                product.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                return product;
            }).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(depositRuleSaveVO.getCardList())) {
            cardManager.remove(Wraps.<MemberDepositCard>lbQ().eq(MemberDepositCard::getDepositRuleId, memberDepositRule.getId()));
            cardList.addAll(depositRuleSaveVO.getCardList().stream().map(p -> {
                MemberDepositCard card = BeanUtil.copyProperties(p, MemberDepositCard.class);
                card.setDepositRuleId(memberDepositRule.getId());
                card.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                return card;
            }).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(depositRuleSaveVO.getCouponList())) {
            couponManager.remove(Wraps.<MemberDepositCoupon>lbQ().eq(MemberDepositCoupon::getDepositRuleId, memberDepositRule.getId()));
            couponList.addAll(depositRuleSaveVO.getCouponList().stream().map(p -> {
                MemberDepositCoupon coupon = BeanUtil.copyProperties(p, MemberDepositCoupon.class);
                coupon.setDepositRuleId(memberDepositRule.getId());
                coupon.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                return coupon;
            }).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(couponList)) {
            couponManager.saveBatch(couponList);
        }
        if (CollUtil.isNotEmpty(cardList)) {
            cardManager.saveBatch(cardList);
        }
        if (CollUtil.isNotEmpty(productList)) {
            productManager.saveBatch(productList);
        }
        return memberDepositRule;
    }

    @Override
    public MemberDepositRule updateDepositRule(MemberDepositRuleUpdateVO depositRuleUpdateVO) {
        MemberDepositRule depositRule = BeanUtil.copyProperties(depositRuleUpdateVO, MemberDepositRule.class);
        depositRuleManager.updateById(depositRule);
        List<MemberDepositProduct> productList = Lists.newArrayList();
        List<MemberDepositCard> cardList = Lists.newArrayList();
        List<MemberDepositCoupon> couponList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(depositRuleUpdateVO.getProductList())) {
            productManager.remove(Wraps.<MemberDepositProduct>lbQ().eq(MemberDepositProduct::getDepositRuleId, depositRuleUpdateVO.getId()));
            productList.addAll(depositRuleUpdateVO.getProductList().stream().map(p -> {
                MemberDepositProduct product = BeanUtil.copyProperties(p, MemberDepositProduct.class);
                product.setDepositRuleId(depositRuleUpdateVO.getId());
                product.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                return product;
            }).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(depositRuleUpdateVO.getCardList())) {
            cardManager.remove(Wraps.<MemberDepositCard>lbQ().eq(MemberDepositCard::getDepositRuleId, depositRuleUpdateVO.getId()));
            cardList.addAll(depositRuleUpdateVO.getCardList().stream().map(p -> {
                MemberDepositCard card = BeanUtil.copyProperties(p, MemberDepositCard.class);
                card.setDepositRuleId(depositRuleUpdateVO.getId());
                card.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                return card;
            }).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(depositRuleUpdateVO.getCouponList())) {
            couponManager.remove(Wraps.<MemberDepositCoupon>lbQ().eq(MemberDepositCoupon::getDepositRuleId, depositRuleUpdateVO.getId()));
            couponList.addAll(depositRuleUpdateVO.getCouponList().stream().map(p -> {
                MemberDepositCoupon coupon = BeanUtil.copyProperties(p, MemberDepositCoupon.class);
                coupon.setDepositRuleId(depositRuleUpdateVO.getId());
                coupon.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                return coupon;
            }).collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(couponList)) {
            couponManager.saveBatch(couponList);
        }
        if (CollUtil.isNotEmpty(cardList)) {
            cardManager.saveBatch(cardList);
        }
        if (CollUtil.isNotEmpty(productList)) {
            productManager.saveBatch(productList);
        }
        return depositRule;
    }

    @Override
    public Boolean removeDepositRule(List<Long> ids) {
        boolean b = depositRuleManager.removeByIds(ids);
        productManager.remove(Wraps.<MemberDepositProduct>lbQ().in(MemberDepositProduct::getDepositRuleId, ids));
        cardManager.remove(Wraps.<MemberDepositCard>lbQ().in(MemberDepositCard::getDepositRuleId, ids));
        couponManager.remove(Wraps.<MemberDepositCoupon>lbQ().in(MemberDepositCoupon::getDepositRuleId, ids));
        return b;
    }

    /**
     * 新增储值规则
     */
    private void depositRule(MemberDepositSaveVO model, MemberDeposit memberDeposit) {
        if (CollUtil.isNotEmpty(model.getDepositRuleList())) {
            List<MemberDepositRule> depositRuleList = depositRuleManager.list(Wraps.<MemberDepositRule>lbQ()
                    .eq(MemberDepositRule::getDepositId, memberDeposit.getId()));
            depositRuleManager.remove(Wraps.<MemberDepositRule>lbQ().eq(MemberDepositRule::getDepositId, memberDeposit.getId()));
            model.getDepositRuleList().forEach(depositRule -> {
                depositRule.setDepositId(memberDeposit.getId());
                depositRule.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
            });
            List<MemberDepositProduct> productList = Lists.newArrayList();
            List<MemberDepositCard> cardList = Lists.newArrayList();
            List<MemberDepositCoupon> couponList = Lists.newArrayList();
            for (MemberDepositRuleSaveVO depositRuleSaveVO : model.getDepositRuleList()) {
                MemberDepositRule memberDepositRule = BeanUtil.copyProperties(depositRuleSaveVO, MemberDepositRule.class);
                if (StrUtil.isBlank(memberDepositRule.getCode())) {
                    memberDepositRule.setCode(getCode());
                }
                if (ObjectUtil.isNull(memberDepositRule.getFreeHours())) {
                    memberDepositRule.setFreeHours(0);
                }
                if (ObjectUtil.isNull(memberDepositRule.getIssueNum())) {
                    memberDepositRule.setIssueNum(0);
                }
                if (ObjectUtil.isNull(memberDepositRule.getLimitedNum())) {
                    memberDepositRule.setLimitedNum(0);
                }
                List<MemberDepositRule> depositRules = depositRuleList.stream().filter(v -> StrUtil.isNotBlank(v.getCode()) && memberDepositRule.getCode().equals(v.getCode()))
                        .collect(Collectors.toList());
                if (CollUtil.isNotEmpty(depositRules)) {
                    memberDepositRule.setCode(depositRules.get(0).getCode());
                    memberDepositRule.setClaimedNum(depositRules.get(0).getClaimedNum());
                }
                if (ObjectUtil.isNull(memberDepositRule.getClaimedNum())) {
                    memberDepositRule.setClaimedNum(0);
                }
                if (CollUtil.isEmpty(depositRuleSaveVO.getGradeIds())) {
                    memberDepositRule.setGradeIds(null);
                }
                depositRuleManager.save(memberDepositRule);
                if (CollUtil.isNotEmpty(depositRuleSaveVO.getProductList())) {
                    productManager.remove(Wraps.<MemberDepositProduct>lbQ().eq(MemberDepositProduct::getDepositRuleId, memberDepositRule.getId()));
                    productList.addAll(depositRuleSaveVO.getProductList().stream().map(p -> {
                        MemberDepositProduct product = BeanUtil.copyProperties(p, MemberDepositProduct.class);
                        product.setDepositRuleId(memberDepositRule.getId());
                        product.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                        return product;
                    }).collect(Collectors.toList()));
                }
                if (CollUtil.isNotEmpty(depositRuleSaveVO.getCardList())) {
                    cardManager.remove(Wraps.<MemberDepositCard>lbQ().eq(MemberDepositCard::getDepositRuleId, memberDepositRule.getId()));
                    cardList.addAll(depositRuleSaveVO.getCardList().stream().map(p -> {
                        MemberDepositCard card = BeanUtil.copyProperties(p, MemberDepositCard.class);
                        card.setDepositRuleId(memberDepositRule.getId());
                        card.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                        return card;
                    }).collect(Collectors.toList()));
                }
                if (CollUtil.isNotEmpty(depositRuleSaveVO.getCouponList())) {
                    couponManager.remove(Wraps.<MemberDepositCoupon>lbQ().eq(MemberDepositCoupon::getDepositRuleId, memberDepositRule.getId()));
                    couponList.addAll(depositRuleSaveVO.getCouponList().stream().map(p -> {
                        MemberDepositCoupon coupon = BeanUtil.copyProperties(p, MemberDepositCoupon.class);
                        coupon.setDepositRuleId(memberDepositRule.getId());
                        coupon.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                        return coupon;
                    }).collect(Collectors.toList()));
                }
            }
            if (CollUtil.isNotEmpty(couponList)) {
                couponManager.saveBatch(couponList);
            }
            if (CollUtil.isNotEmpty(cardList)) {
                cardManager.saveBatch(cardList);
            }
            if (CollUtil.isNotEmpty(productList)) {
                productManager.saveBatch(productList);
            }
        }
    }
}


