package top.kx.kxss.base.manager.user.invite.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.user.invite.BaseEmployeeInviteBatchDetail;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.user.invite.BaseEmployeeInviteBatchDetailManager;
import top.kx.kxss.base.mapper.user.invite.BaseEmployeeInviteBatchDetailMapper;

/**
 * <p>
 * 通用业务实现类
 * 员工邀请批次明细
 * </p>
 *
 * <AUTHOR>
 * @date 2025-06-18 18:11:06
 * @create [2025-06-18 18:11:06] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseEmployeeInviteBatchDetailManagerImpl extends SuperManagerImpl<BaseEmployeeInviteBatchDetailMapper, BaseEmployeeInviteBatchDetail> implements BaseEmployeeInviteBatchDetailManager {

}


