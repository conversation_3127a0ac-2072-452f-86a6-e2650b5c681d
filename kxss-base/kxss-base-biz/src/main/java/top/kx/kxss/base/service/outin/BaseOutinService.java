package top.kx.kxss.base.service.outin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.SuperService;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.update.LbUpdateWrap;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.entity.cash.product.PosCashProduct;
import top.kx.kxss.base.entity.outin.BaseOutin;
import top.kx.kxss.base.entity.outin.BaseOutinStocktaking;
import top.kx.kxss.base.vo.query.outin.BaseOutinPageQuery;
import top.kx.kxss.base.vo.result.outin.BaseOutinItemResultVO;
import top.kx.kxss.base.vo.result.outin.BaseOutinPurchaseInExportResultVO;
import top.kx.kxss.base.vo.result.outin.BaseOutinPurchaseOutExportResultVO;
import top.kx.kxss.base.vo.result.outin.BaseOutinResultVO;
import top.kx.kxss.base.vo.save.outin.*;
import top.kx.kxss.base.vo.update.outin.BaseOutinUpdateVO;
import top.kx.kxss.base.vo.query.outin.SellOutinQuery;
import top.kx.kxss.base.entity.outin.SellOutinDetailsResultVO;
import top.kx.kxss.base.entity.outin.SellOutinResultVO;

import java.util.List;


/**
 * <p>
 * 业务接口
 * 商品出入库主表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-06 14:51:59
 * @create [2023-04-06 14:51:59] [dou] [代码生成器生成]
 */
public interface BaseOutinService extends SuperService<Long, BaseOutin, BaseOutinSaveVO,
        BaseOutinUpdateVO, BaseOutinPageQuery, BaseOutinResultVO> {

    /**
     * 单据号
     *
     * @return
     */
    String getCode();

    /**
     * 新增出/入库记录
     *
     * @param model
     * @return
     */
    BaseOutin saveOutin(BaseOutinSaveVO model);

    /**
     * 更新出/入库记录
     *
     * @param model
     * @return
     */
    BaseOutin updateOutin(BaseOutinUpdateVO model);


    Boolean updateState(BaseOutinStateVO model);


    IPage<BaseOutinItemResultVO> pageList(PageParams<BaseOutinPageQuery> params);

    /**
     * 查看详情
     *
     * @param aLong
     * @return
     */
    BaseOutinResultVO getDetail(Long aLong);

    BaseOutinStocktaking stocktaking(BaseOutinStocktakingVO model);

    BaseOutinStocktaking updateStocktaking(BaseOutinStocktakingVO model);

    BaseOutin saveStock(BaseOutinStockSaveVO build);

    boolean remove(LbQueryWrap<BaseOutin> eq);

    /**
     * 退货
     *
     * @param baseProduct 商品信息
     * @param num         数量
     * @param posCash     单据
     * @return
     */
    boolean productReturn(PosCashProduct baseProduct, Integer num, PosCash posCash);

    boolean update(LbUpdateWrap<BaseOutin> wrap);

    BaseOutin getByCode(String code);

    boolean mergeOutin(List<String> sources, PosCash mergePosCash,
                       List<PosCashProduct> posCashProductList,String remark);

    /**
     * 不同仓库的订单不允许合并
     * @param sources
     * @param mergeCode
     */
    @Deprecated
    void mergeOutinCheckWarehouse(List<String> sources, String mergeCode);


    boolean splitOutin(List<String> sources, PosCash splitPosCash,
                       List<PosCashProduct> posCashProductList,String remark);

    BaseOutin purchaseOut(BaseOutinStockSaveVO model);

    BaseOutin otherOut(BaseOutinStockSaveVO model);

    BaseOutin otherIn(BaseOutinStockSaveVO model);

    Boolean adjustmentStock(BaseOutinStockAdjustmentVO model);

    Boolean updateAdjustmentStock(BaseOutinStockAdjustmentVO model);

    /**
     * 统计数量
     * @param eq
     * @return
     */
    Long noReviewedCount();

    boolean productListReturn(List<BaseOutinProductSaveVO> baseOutinList, PosCash cash);

    List<BaseOutinPurchaseOutExportResultVO> purchaseOutExport(Long id);

    List<BaseOutinPurchaseInExportResultVO> purchaseInExport(Long id);

    IPage<SellOutinResultVO> sell(PageParams<SellOutinQuery> params);

    SellOutinResultVO sellDetail(Long id);

    Boolean reversalEntry(BaseOutinReversalEntryVO model);

    Boolean updateOutinPrice(BaseOutinPriceUpdateVO model);

    Boolean forceUpdatePrice(BaseOutinForceUpdateVO updateVO);
}


