package top.kx.kxss.base.manager.goals.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.goals.BaseBusinessGoals;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.goals.BaseBusinessGoalsManager;
import top.kx.kxss.base.mapper.goals.BaseBusinessGoalsMapper;

/**
 * <p>
 * 通用业务实现类
 * 营运目标
 * </p>
 *
 * <AUTHOR>
 * @date 2024-11-01 18:07:28
 * @create [2024-11-01 18:07:28] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseBusinessGoalsManagerImpl extends SuperManagerImpl<BaseBusinessGoalsMapper, BaseBusinessGoals> implements BaseBusinessGoalsManager {

}


