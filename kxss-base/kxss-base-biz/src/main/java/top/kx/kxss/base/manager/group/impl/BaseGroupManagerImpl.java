package top.kx.kxss.base.manager.group.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.utils.CollHelper;
import top.kx.kxss.base.entity.group.BaseGroup;
import top.kx.kxss.base.manager.group.BaseGroupManager;
import top.kx.kxss.base.mapper.group.BaseGroupMapper;
import top.kx.kxss.base.vo.result.group.BaseGroupResultVO;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.common.constant.DsConstant;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用业务实现类
 * 组
 * </p>
 *
 * <AUTHOR>
 * @date 2025-01-06 15:47:18
 * @create [2025-01-06 15:47:18] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseGroupManagerImpl extends SuperManagerImpl<BaseGroupMapper, BaseGroup> implements BaseGroupManager {


    @Transactional(readOnly = true)
    @Override
    @DS(DsConstant.BASE_TENANT)
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {

        LbQueryWrap<BaseGroup> wrap = Wraps.lbQ();
        wrap.in(BaseGroup::getId, ids);

        List<BaseGroup> baseGroupList = list(wrap);
        if (CollUtil.isNotEmpty(baseGroupList)) {
            List<BaseGroup> list = baseGroupList.stream().filter(Objects::nonNull).collect(Collectors.toList());
            return CollHelper.uniqueIndex(list, BaseGroup::getId, BaseGroup::getName);
        } else {
            return new HashMap<>();
        }

    }

    @Override
    public List<BaseGroupResultVO> findList(LbQueryWrap<BaseGroup> wrap) {
        List<BaseGroupResultVO> list = baseMapper.findList(wrap);
        for (BaseGroupResultVO resultVO : list) {
            if (resultVO.getDeleteFlag() == 1) {
                resultVO.setName(resultVO.getName().concat("(已删除)"));
            }
        }
        return list;
    }
}


