package top.kx.kxss.base.service.payment;

import top.kx.basic.base.service.SuperService;
import top.kx.kxss.base.entity.payment.BaseBankCardInfo;
import top.kx.kxss.base.vo.save.payment.BaseBankCardInfoSaveVO;
import top.kx.kxss.base.vo.update.payment.BaseBankCardInfoUpdateVO;
import top.kx.kxss.base.vo.result.payment.BaseBankCardInfoResultVO;
import top.kx.kxss.base.vo.query.payment.BaseBankCardInfoPageQuery;


/**
 * <p>
 * 业务接口
 * 银行卡信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-04 15:36:19
 * @create [2025-07-04 15:36:19] [dou] [代码生成器生成]
 */
public interface BaseBankCardInfoService extends SuperService<Long, BaseBankCardInfo, BaseBankCardInfoSaveVO,
    BaseBankCardInfoUpdateVO, BaseBankCardInfoPageQuery, BaseBankCardInfoResultVO> {

    boolean save(BaseBankCardInfo build);
}


