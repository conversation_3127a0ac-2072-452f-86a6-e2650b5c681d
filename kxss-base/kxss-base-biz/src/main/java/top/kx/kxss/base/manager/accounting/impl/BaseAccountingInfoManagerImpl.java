package top.kx.kxss.base.manager.accounting.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.vo.result.cash.AmountResultVO;
import top.kx.kxss.base.entity.accounting.BaseAccountingInfo;
import top.kx.kxss.base.manager.accounting.BaseAccountingInfoManager;
import top.kx.kxss.base.mapper.accounting.BaseAccountingInfoMapper;
import top.kx.kxss.base.vo.result.accounting.AccountingCalenderResultVO;
import top.kx.kxss.base.vo.result.accounting.AccountingCalenderSumResultVO;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 通用业务实现类
 * 记账明细
 * </p>
 *
 * <AUTHOR>
 * @date 2023-10-09 16:35:55
 * @create [2023-10-09 16:35:55] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseAccountingInfoManagerImpl extends SuperManagerImpl<BaseAccountingInfoMapper, BaseAccountingInfo> implements BaseAccountingInfoManager {

    @Override
    public List<AccountingCalenderResultVO> calendar(String field, Wrapper<BaseAccountingInfo> wrapper) {
        return baseMapper.calendar(field, wrapper);
    }

    @Override
    public AccountingCalenderSumResultVO calendarSum(Wrapper<BaseAccountingInfo> wrapper) {
        return baseMapper.calendarSum(wrapper);
    }

    @Override
    public List<AmountResultVO> calendarIn(QueryWrapper<PosCash> wrapper, Integer hour,  String field) {
        return baseMapper.calendarIn(wrapper, hour,  field);
    }

    @Override
    public List<AmountResultVO> calendarInList(QueryWrapper<PosCash> wrapper) {
        return baseMapper.calendarInList(wrapper);
    }
}


