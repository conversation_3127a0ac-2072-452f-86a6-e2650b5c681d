package top.kx.kxss.base.manager.tag.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.manager.impl.SuperCacheManagerImpl;
import top.kx.basic.model.cache.CacheKeyBuilder;
import top.kx.basic.utils.CollHelper;
import top.kx.kxss.base.entity.tag.BaseTag;
import top.kx.kxss.base.manager.tag.BaseTagManager;
import top.kx.kxss.base.mapper.tag.BaseTagMapper;
import top.kx.kxss.common.cache.base.tag.TagCacheKeyBuilder;
import top.kx.kxss.common.constant.DsConstant;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用业务实现类
 * 标签表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-03-25 09:55:50
 * @create [2023-03-25 09:55:50] [Wang] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseTagManagerImpl extends SuperCacheManagerImpl<BaseTagMapper, BaseTag> implements BaseTagManager {

    @Override
    protected CacheKeyBuilder cacheKeyBuilder() {
        return new TagCacheKeyBuilder();
    }
    @Transactional(readOnly = true)
    @Override
    @DS(DsConstant.BASE_TENANT)
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        if(CollUtil.isNotEmpty(ids)){
            ids.removeAll(Collections.singletonList(null));
        }
        List<BaseTag> list = findByIds(ids, null).stream().filter(Objects::nonNull).collect(Collectors.toList());
        return CollHelper.uniqueIndex(list, BaseTag::getId, BaseTag::getName);
    }
}


