package top.kx.kxss.base.biz.user;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.cache.redis2.CacheResult;
import top.kx.basic.cache.repository.CacheOps;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.exception.BizException;
import top.kx.basic.model.cache.CacheKey;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.CollHelper;
import top.kx.kxss.app.cash.PosCashApi;
import top.kx.kxss.app.query.TenantOrgUserConsumeQuery;
import top.kx.kxss.base.entity.service.BaseServicePersonal;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.entity.user.BaseEmployeeOrgRel;
import top.kx.kxss.base.entity.user.BaseEmployeeRoleRel;
import top.kx.kxss.base.entity.user.BaseOrg;
import top.kx.kxss.base.service.service.BaseServicePersonalService;
import top.kx.kxss.base.service.system.BaseRoleService;
import top.kx.kxss.base.service.user.*;
import top.kx.kxss.base.vo.query.user.BaseEmployeePageQuery;
import top.kx.kxss.base.vo.result.user.BaseEmployeeDiscountAuthResultVO;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.base.vo.save.user.BaseEmployeeOrgRelSaveVO;
import top.kx.kxss.base.vo.save.user.BaseEmployeeSaveVO;
import top.kx.kxss.base.vo.save.user.EmployeeUpdateMobileByCodeVO;
import top.kx.kxss.common.constant.RabbitMqConstant;
import top.kx.kxss.common.constant.RedisConstant;
import top.kx.kxss.common.constant.RoleConstant;
import top.kx.kxss.file.entity.File;
import top.kx.kxss.file.service.FileService;
import top.kx.kxss.model.entity.system.SysUser;
import top.kx.kxss.model.enumeration.base.ActiveStatusEnum;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.entity.tenant.DefUserTenantRel;
import top.kx.kxss.system.service.tenant.DefUserService;
import top.kx.kxss.system.service.tenant.DefUserTenantRelService;
import top.kx.kxss.system.vo.query.tenant.DefUserPageQuery;
import top.kx.kxss.system.vo.save.tenant.DefTenantBindEmployeeVO;
import top.kx.kxss.system.vo.save.tenant.DefTenantBindUserVO;
import top.kx.kxss.system.vo.save.tenant.DefUserSaveVO;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 员工大业务层
 *
 * <AUTHOR>
 * @date 2021/10/22 10:37
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BaseEmployeeBiz {
    private final BaseEmployeeService baseEmployeeService;
    private final BaseEmployeeOrgRelService baseEmployeeOrgRelService;
    private final BaseEmployeeRoleRelService baseEmployeeRoleRelService;
    private final BaseEmployeeDiscountAuthService baseEmployeeDiscountAuthService;
    private final BaseEmployeeBtnAuthService baseEmployeeBtnAuthService;
    private final DefUserService defUserService;
    private final DefUserTenantRelService defUserTenantRelService;
    private final FileService fileService;
    private final BaseOrgService baseOrgService;
    private final PosCashApi posCashApi;
    private final RabbitTemplate template;
    private final CacheOps cacheOps;
    private final BaseServicePersonalService baseServicePersonalService;
    private final BaseRoleService baseRoleService;

    /**
     * 根据员工ID 查询员工、用户和他所在的机构 信息
     *
     * @param employeeId 员工ID
     * @return top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO
     * <AUTHOR>
     * @date 2022/10/28 12:13 AM
     * @create [2022/10/28 12:13 AM ] [tangyh] [初始创建]
     */
    public BaseEmployeeResultVO getEmployeeUserById(Long employeeId) {
        // 租户库
        BaseEmployee employee = baseEmployeeService.getById(employeeId);
        if (employee == null) {
            return null;
        }
        // def
        DefUserTenantRel utr = defUserTenantRelService.getById(employeeId);
        if (utr == null) {
            return null;
        }
        // 员工信息
        BaseEmployeeResultVO resultVO = new BaseEmployeeResultVO();
        BeanUtil.copyProperties(employee, resultVO);
        if (ObjectUtil.isNotNull(resultVO.getHealthCodeId())) {
            resultVO.setHealthCodeIdFile(fileService.getById(resultVO.getHealthCodeId()));
        }
        if (ObjectUtil.isNotNull(resultVO.getPhotoId())) {
            resultVO.setPhotoIdFile(fileService.getById(resultVO.getPhotoId()));
        }
        // 机构信息
        resultVO.setOrgIdList(baseEmployeeOrgRelService.findOrgIdListByEmployeeId(employeeId));
        // 机构信息
        resultVO.setRoleIdList(baseEmployeeRoleRelService.findRoleIdListByEmployeeId(employeeId));
        // 用户信息
        DefUser defUser = defUserService.getById(employee.getUserId());
        resultVO.setDefUser(BeanUtil.toBean(defUser, SysUser.class));
        resultVO.setNation(defUser.getNation());
        resultVO.setEducation(defUser.getEducation());
        resultVO.setSex(defUser.getSex());
        resultVO.setBtnAuthList(baseEmployeeBtnAuthService.findBthTypeListByEmployeeId(employeeId));
        resultVO.setDiscountAuth(BeanPlusUtil.toBean(baseEmployeeDiscountAuthService.selectDiscountAuth(employeeId), BaseEmployeeDiscountAuthResultVO.class));
        return resultVO;
    }

    /**
     * 删除员工
     *
     * @param ids 员工ID
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/10/28 12:14 AM
     * @create [2022/10/28 12:14 AM ] [tangyh] [初始创建]
     */
    @GlobalTransactional
    public Boolean delete(List<Long> ids) {
        //判断订单是否存在套餐
        //判断订单是否存在套餐
        R<List<String>> booleanR = posCashApi.checkEmpIsUseCode(ids);
        List<String> data = booleanR.getData();
        if (CollUtil.isNotEmpty(data)) {
            //data取后8位组成string字符串用逗号隔开
            String code = CollUtil.join(data.stream().map(item -> item.substring(item.length() - 8)).collect(Collectors.toList()), ",");
            throw new BizException("【".concat(code).concat("】正在使用中，无法删除"));
        }
        // 删除基础库的 员工
        List<BaseEmployee> employeeList = baseEmployeeService.list(Wraps.<BaseEmployee>lbQ()
                .in(BaseEmployee::getId, ids));
        boolean b = baseEmployeeService.deleteByIds(ids);
        if (b) {
            for (BaseEmployee baseEmployee : employeeList) {
                if (ObjectUtil.isNull(baseEmployee.getUserId())) {
                    continue;
                }
                template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.TENANT_ORG_USER,
                        JSON.toJSONString(TenantOrgUserConsumeQuery.builder()
                                .tenantId(ContextUtil.getTenantId())
                                .employeeId(null)
                                .userId(baseEmployee.getUserId())
                                .build()));

            }
            List<Long> userIds = employeeList.stream().map(BaseEmployee::getUserId).collect(Collectors.toList());
            for (DefUser defUser : defUserService.listByIds(userIds)) {
                //清除账号登录
                template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.FORCE_LOGOUT,
                        defUser.getMobile());
            }
        }
        return b;
    }

    /**
     * 保存员工信息
     *
     * @param saveVO saveVO
     * @return top.kx.kxss.base.entity.user.BaseEmployee
     * <AUTHOR>
     * @date 2022/10/28 12:15 AM
     * @create [2022/10/28 12:15 AM ] [tangyh] [初始创建]
     */
    @GlobalTransactional
    public BaseEmployee save(BaseEmployeeSaveVO saveVO) {
//        boolean existDefUser = defUserService.checkMobile(saveVO.getMobile(), null);
//        if (existDefUser) {
//            throw new BizException("手机号已被注册,请重新输入手机号 或 直接邀请它加入贵公司。");
//        }
        DefUser entity = defUserService.getUserByMobile(saveVO.getMobile());
        if (ObjectUtil.isNotNull(entity)) {
            List<BaseEmployee> employeeList = baseEmployeeService.list(Wraps.<BaseEmployee>lbQ()
                    .eq(BaseEmployee::getDeleteFlag, 0)
                    .eq(BaseEmployee::getUserId, entity.getId()).last("limit 1"));
            if (CollUtil.isNotEmpty(employeeList)) {
                List<BaseEmployeeOrgRel> employeeOrgRelList = baseEmployeeOrgRelService.list(Wraps.<BaseEmployeeOrgRel>lbQ()
                        .eq(BaseEmployeeOrgRel::getOrgId, ContextUtil.getCurrentCompanyId())
                        .eq(BaseEmployeeOrgRel::getDeleteFlag, 0)
                        .eq(BaseEmployeeOrgRel::getEmployeeId, employeeList.get(0).getId()));
                if (CollUtil.isNotEmpty(employeeOrgRelList)) {
                    ArgumentAssert.isFalse(ObjectUtil.equals(1, 1), "已存在手机号");
                } else {
                    //保存员工和门店关系
                    BaseEmployeeOrgRelSaveVO employeeOrgSaveVO = BaseEmployeeOrgRelSaveVO.builder()
                            .employeeId(employeeList.get(0).getId())
                            .orgId(ContextUtil.getCurrentCompanyId())
                            .build();
                    baseEmployeeOrgRelService.save(employeeOrgSaveVO);
                    return employeeList.get(0);
                }
            }
        }
        String username = StrUtil.isBlank(saveVO.getUsername()) ? IdUtil.simpleUUID() : saveVO.getUsername();
        // 保存默认库的 用户表 和 员工表
        DefUserSaveVO userSaveVO = DefUserSaveVO.builder().username(username).nickName(saveVO.getRealName()).build();
        BeanUtil.copyProperties(saveVO, userSaveVO);
        DefUserTenantRel defUserTenantRel = defUserService.saveUserAndEmployee(ContextUtil.getTenantId(), userSaveVO);
        // 保存 基础库的员工表
        boolean mobileUser = baseEmployeeService.getByUserId(defUserTenantRel.getUserId());
        if (mobileUser) {
            throw new BizException("手机号已存在，请联系管理员");
        }
        saveVO.setUserId(defUserTenantRel.getUserId());
        saveVO.setId(defUserTenantRel.getId());
        saveVO.setActiveStatus(ActiveStatusEnum.ACTIVATED.getCode());
        saveVO.setIsDefault(true);
        saveVO.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        if (StrUtil.isBlank(saveVO.getName())) {
            saveVO.setName(saveVO.getRealName());
        }
        BaseEmployee save = baseEmployeeService.save(saveVO);
        //默认绑定当前门店ID
        BaseEmployeeOrgRelSaveVO employeeOrgSaveVO = BaseEmployeeOrgRelSaveVO.builder()
                .employeeId(save.getId())
                .orgId(ContextUtil.getCurrentCompanyId())
                .build();
        baseEmployeeOrgRelService.save(employeeOrgSaveVO);
//        //按钮权限
//        baseEmployeeBtnAuthService.saveBtnAuth(saveVO.getBtnAuthList(), save.getId());
        //优惠权限
        baseEmployeeDiscountAuthService.saveDiscountAuth(saveVO.getDiscountAuth(), save.getId());
        return save;
    }

    /**
     * 分页查员工数据
     *
     * @param params 参数
     * @return IPage
     * <AUTHOR>
     * @date 2022/10/28 12:19 AM
     * @create [2022/10/28 12:19 AM ] [tangyh] [初始创建]
     */
    public IPage<BaseEmployeeResultVO> findPageResultVO(PageParams<BaseEmployeePageQuery> params) {
        BaseEmployeePageQuery pageQuery = params.getModel();
        List<Long> userIdList;
        if (!StrUtil.isAllEmpty(pageQuery.getMobile(), pageQuery.getEmail(), pageQuery.getUsername(), pageQuery.getIdCard())) {
            userIdList = defUserService.findUserIdList(BeanUtil.toBean(pageQuery, DefUserPageQuery.class));
            if (CollUtil.isEmpty(userIdList)) {
                return new Page<>(params.getCurrent(), params.getSize());
            }
            params.getModel().setUserIdList(userIdList);
        }
        if (CollUtil.isEmpty(params.getModel().getOrgIdList())) {
            params.getModel().setOrgIdList(Lists.newArrayList());
        }
        params.getModel().getOrgIdList().add(ContextUtil.getCurrentCompanyId());
        if (params.getModel().getIsWebAdmin() != null && params.getModel().getIsWebAdmin()) {
            if (ObjectUtil.equal(ContextUtil.getCurrentCompanyId(), 1L)) {
                params.getModel().setOrgIdList(Lists.newArrayList());
            }
        }
        params.getModel().setUserMobileList(Lists.newArrayList());
        if (StrUtil.isNotBlank(pageQuery.getRealName())) {
            List<Long> list = defUserService.list(Wraps.<DefUser>lbQ().like(DefUser::getMobile, pageQuery.getRealName()))
                    .stream().map(DefUser::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(list)) {
                params.getModel().getUserMobileList().addAll(list);
            }
        }
        if (StrUtil.isNotBlank(pageQuery.getKeyword())) {
            List<Long> list = defUserService.list(Wraps.<DefUser>lbQ().like(DefUser::getMobile, pageQuery.getKeyword()))
                    .stream().map(DefUser::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(list)) {
                params.getModel().getUserMobileList().addAll(list);
            }
        }
//        DataScopeHelper.startDataScope("e");
        IPage<BaseEmployeeResultVO> pageResultVO = baseEmployeeService.findPageResultVO(params);

        if (CollUtil.isNotEmpty(pageResultVO.getRecords())) {
            List<Long> userIds = pageResultVO.getRecords().stream().map(BaseEmployeeResultVO::getUserId).collect(Collectors.toList());
            List<DefUser> defUsers = defUserService.listByIds(userIds);
            List<SysUser> userResultVos = BeanUtil.copyToList(defUsers, SysUser.class);
            ImmutableMap<Long, SysUser> map = CollHelper.uniqueIndex(userResultVos, SysUser::getId, user -> user);
            List<Long> photoIds = pageResultVO.getRecords().stream().map(BaseEmployeeResultVO::getPhotoId)
                    .filter(ObjectUtil::isNotNull)
                    .collect(Collectors.toList());
            Map<Long, File> fileMap = CollectionUtil.isNotEmpty(photoIds) ? fileService.list(Wraps.<File>lbQ().in(File::getId, photoIds))
                    .stream().collect(Collectors.toMap(File::getId, k -> k)) : new HashMap<>();
            List<Long> employeeIds = pageResultVO.getRecords().stream().map(BaseEmployeeResultVO::getId).collect(Collectors.toList());
            // 所有角色

            Map<Long, List<BaseEmployeeRoleRel>> employeeRoleRelMap = baseEmployeeRoleRelService.list(Wraps.<BaseEmployeeRoleRel>lbQ()
                            .eq(BaseEmployeeRoleRel::getDeleteFlag, 0)
                            .in(BaseEmployeeRoleRel::getEmployeeId, employeeIds))
                    .stream().collect(Collectors.groupingBy(BaseEmployeeRoleRel::getEmployeeId, Collectors.toList()));


            Map<Long, List<BaseEmployeeOrgRel>> employeeOrgRelMap = baseEmployeeOrgRelService.list(Wraps.<BaseEmployeeOrgRel>lbQ()
                            .eq(BaseEmployeeOrgRel::getDeleteFlag, 0)
                            .in(BaseEmployeeOrgRel::getEmployeeId, employeeIds))
                    .stream().collect(Collectors.groupingBy(BaseEmployeeOrgRel::getEmployeeId, Collectors.toList()));

            for (BaseEmployeeResultVO item : pageResultVO.getRecords()) {
                item.setDefUser(map.get(item.getUserId()));
                if (ObjectUtil.isNotNull(item.getPhotoId())) {
                    item.setPhotoId(item.getPhotoId());
                    if (ObjectUtil.isNotNull(fileMap) &&
                            ObjectUtil.isNotNull(fileMap.get(item.getPhotoId()))) {
                        item.setPhotoIdFile(fileMap.get(item.getPhotoId()));
                        item.setAvatar(fileMap.get(item.getPhotoId()).getUrl());
                    }
                }
                if (item.getIsDiscount() == null) {
                    item.setIsDiscount(false);
                }
                if (item.getIsAccount() == null) {
                    item.setIsAccount(true);
                }
                if (Objects.nonNull(item.getDefUser())) {
                    item.setSex(item.getDefUser().getSex());
                }
                if (CollUtil.isNotEmpty(employeeOrgRelMap)
                        && employeeOrgRelMap.containsKey(item.getId())) {
                    List<BaseEmployeeOrgRel> employeeOrgRelList = employeeOrgRelMap.get(item.getId());
                    item.setOrgIdList(employeeOrgRelList.stream().map(BaseEmployeeOrgRel::getOrgId)
                            .distinct().collect(Collectors.toList()));
                }
                if (CollUtil.isNotEmpty(employeeRoleRelMap)
                        && employeeRoleRelMap.containsKey(item.getId())) {
                    List<BaseEmployeeRoleRel> employeeOrgRelList = employeeRoleRelMap.get(item.getId());
                    item.setRoleIdList(employeeOrgRelList.stream().map(BaseEmployeeRoleRel::getRoleId)
                            .distinct().collect(Collectors.toList()));
                }
            }
            pageResultVO.getRecords().forEach(item -> item.setDefUser(map.get(item.getUserId())));
        }

        return pageResultVO;
    }

    /**
     * 将用户绑定为租户管理员
     *
     * @param param param
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/10/28 12:21 AM
     * @create [2022/10/28 12:21 AM ] [tangyh] [初始创建]
     */
    @GlobalTransactional
    public Boolean bindTenantAdmin(DefTenantBindUserVO param) {
        List<Long> employeeIdList = findEmployeeIdList(param);
        return baseEmployeeRoleRelService.bindRole(employeeIdList, RoleConstant.TENANT_ADMIN);
    }

    private List<Long> findEmployeeIdList(DefTenantBindUserVO param) {
        List<DefUserTenantRel> defEmployeeList = defUserTenantRelService.list(Wraps.<DefUserTenantRel>lbQ().eq(DefUserTenantRel::getTenantId, param.getTenantId()).in(DefUserTenantRel::getUserId, param.getUserIdList()));
        ArgumentAssert.notEmpty(defEmployeeList, "对不起，您选择的用户不是该企业的员工");
        List<Long> employeeIdList = defEmployeeList.stream().map(DefUserTenantRel::getId).collect(Collectors.toList());
        // 保存到指定租户的 base库的员工 + 租户管理员角色
        ContextUtil.setTenantBasePoolName(param.getTenantId());
        return employeeIdList;
    }

    /**
     * 在运营平台 将用户解绑为某个租户的 租户管理员
     *
     * @param param param
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/10/28 12:21 AM
     * @create [2022/10/28 12:21 AM ] [tangyh] [初始创建]
     */
    @GlobalTransactional
    public Boolean unBindTenantAdmin(DefTenantBindUserVO param) {
        List<Long> employeeIdList = findEmployeeIdList(param);
        return baseEmployeeRoleRelService.unBindRole(employeeIdList, RoleConstant.TENANT_ADMIN);
    }

    /**
     * 将用户绑定某个租户的员工
     *
     * @param param param
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/10/28 12:21 AM
     * @create [2022/10/28 12:21 AM ] [tangyh] [初始创建]
     */
    @GlobalTransactional
    public Boolean bindUser(DefTenantBindUserVO param) {
        List<BaseEmployee> baseEmployeeList = findEmployeeList(param);
        boolean b = baseEmployeeService.saveBatchBaseEmployeeAndRole(baseEmployeeList);
        if (b) {
            tenantOrgUser(baseEmployeeList);
        }
        return b;
    }

    @GlobalTransactional
    public Boolean bindUserByOrgId(DefTenantBindEmployeeVO param) {
        List<BaseEmployee> baseEmployeeList = findEmployeeList(param);
        boolean b = baseEmployeeService.saveBatchBaseEmployeeAndRole(baseEmployeeList);
        if (b) {
            tenantOrgUser(baseEmployeeList);
        }
        return b;
    }

    private List<BaseEmployee> findEmployeeList(DefTenantBindUserVO param) {
        Long tenantId = param.getTenantId();
        ArgumentAssert.notNull(tenantId, "请选择租户");
        List<DefUser> defUsers = defUserService.listByIds(param.getUserIdList());
        ArgumentAssert.notEmpty(defUsers, "请选择用户");
        long employeeCount = defUserTenantRelService.count(Wraps.<DefUserTenantRel>lbQ().eq(DefUserTenantRel::getTenantId, tenantId)
                .in(DefUserTenantRel::getUserId, param.getUserIdList()));
        ArgumentAssert.isFalse(employeeCount > 0, "对不起，您选择的用户已经是该企业的员工");

        // 保存def库的员工
        List<DefUserTenantRel> employeeList = param.getUserIdList().stream().map(userId -> {
            DefUserTenantRel employee = new DefUserTenantRel();
            employee.setUserId(userId);
            employee.setTenantId(tenantId);
            employee.setState(true);
            employee.setIsDefault(false);
            return employee;
        }).collect(Collectors.toList());
        defUserTenantRelService.saveBatch(employeeList);

        ImmutableMap<Long, String> userMap = CollHelper.uniqueIndex(defUsers, DefUser::getId, DefUser::getNickName);
        List<BaseEmployee> baseEmployeeList = BeanUtil.copyToList(employeeList, BaseEmployee.class);
        // 保存到指定租户的 base库的员工 + 租户管理员角色
        ContextUtil.setTenantBasePoolName(tenantId);
        BaseOrg baseOrg = baseOrgService.getById(ContextUtil.getCurrentCompanyId());
        baseEmployeeList.forEach(employee -> {
            employee.setActiveStatus(ActiveStatusEnum.ACTIVATED.getCode());
            employee.setState(true);
            employee.setCreatedOrgId(baseOrg != null ? ContextUtil.getCurrentCompanyId() : 1);
            employee.setRealName(userMap.get(employee.getUserId()));
        });
        return baseEmployeeList;
    }

    private List<BaseEmployee> findEmployeeList(DefTenantBindEmployeeVO param) {
        Long tenantId = param.getTenantId();
        ArgumentAssert.notNull(tenantId, "请选择租户");
        List<BaseEmployee> baseEmployees = baseEmployeeService.listByIds(param.getEmployeeIdList());
        ArgumentAssert.notEmpty(baseEmployees, "请选择用户");
        long employeeCount = baseEmployeeOrgRelService.count(Wraps.<BaseEmployeeOrgRel>lbQ()
                .eq(BaseEmployeeOrgRel::getOrgId, param.getOrgId())
                .in(BaseEmployeeOrgRel::getEmployeeId, param.getEmployeeIdList()));
        ArgumentAssert.isFalse(employeeCount > 0, "对不起，您选择的用户已经是该门店的员工");
        return baseEmployees;

    }


    /**
     * 邀请某个用户加入 他自己所在的租户
     *
     * @param param param
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/10/28 12:22 AM
     * @create [2022/10/28 12:22 AM ] [tangyh] [初始创建]
     */
    @GlobalTransactional
    public Boolean invitationUser(DefTenantBindUserVO param) {
        Long tenantId = ContextUtil.getTenantId();
        param.setTenantId(tenantId);
        List<BaseEmployee> baseEmployeeList = findEmployeeList(param);
        boolean b = baseEmployeeService.saveBatch(baseEmployeeList);
        if (b && ContextUtil.getCurrentCompanyId() != null) {
            baseEmployeeOrgRelService.saveBatch(baseEmployeeList.stream().map(employee -> BaseEmployeeOrgRel.builder()
                    .createdOrgId(ContextUtil.getCurrentCompanyId())
                    .orgId(ContextUtil.getCurrentCompanyId())
                    .employeeId(employee.getId()).build()).collect(Collectors.toList()));
        }
        return b;
    }

    /**
     * 在基础平台 将用户取消保定到自己所在的企业
     *
     * @param param param
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/10/28 12:22 AM
     * @create [2022/10/28 12:22 AM ] [tangyh] [初始创建]
     * @update [2022/10/28 12:22 AM ] [tangyh] [变更描述]
     */
    @GlobalTransactional
    public Boolean unInvitationUser(DefTenantBindUserVO param) {
        Long tenantId = ContextUtil.getTenantId();
        List<Long> employeeIdList = findEmployeeIdList(param, tenantId);
        List<BaseEmployee> employeeList = baseEmployeeService.list(Wraps.<BaseEmployee>lbQ()
                .in(BaseEmployee::getId, employeeIdList));
        boolean b = baseEmployeeService.removeByIds(employeeIdList);
        if (b) {
            tenantOrgUser(employeeList);
        }
        return b;
    }

    private void tenantOrgUser(List<BaseEmployee> employeeList) {
        if (CollUtil.isEmpty(employeeList)) {
            return;
        }
        for (BaseEmployee baseEmployee : employeeList) {
            if (ObjectUtil.isNull(baseEmployee.getUserId())) {
                continue;
            }
            template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.TENANT_ORG_USER,
                    JSON.toJSONString(TenantOrgUserConsumeQuery.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .employeeId(null)
                            .userId(baseEmployee.getUserId())
                            .build()));
        }
    }

    private List<Long> findEmployeeIdList(DefTenantBindUserVO param, Long tenantId) {
        List<DefUser> defUsers = defUserService.listByIds(param.getUserIdList());
        ArgumentAssert.notEmpty(defUsers, "请选择用户");
        List<DefUserTenantRel> defEmployeeList = defUserTenantRelService.list(Wraps.<DefUserTenantRel>lbQ().eq(DefUserTenantRel::getTenantId, tenantId).in(DefUserTenantRel::getUserId, param.getUserIdList()));
        ArgumentAssert.notEmpty(defEmployeeList, "对不起，您选择的用户不是该企业的员工");
        List<Long> employeeIdList = defEmployeeList.stream().map(DefUserTenantRel::getId).collect(Collectors.toList());
        defUserTenantRelService.removeByIds(employeeIdList);
        return employeeIdList;
    }

    private List<Long> findEmployeeIdList(DefTenantBindEmployeeVO param, Long tenantId) {
        List<BaseEmployee> defUsers = baseEmployeeService.listByIds(param.getEmployeeIdList());
        ArgumentAssert.notEmpty(defUsers, "请选择用户");
        List<DefUserTenantRel> defEmployeeList = defUserTenantRelService.list(Wraps.<DefUserTenantRel>lbQ().eq(DefUserTenantRel::getTenantId, tenantId).in(DefUserTenantRel::getId, param.getEmployeeIdList()));
        ArgumentAssert.notEmpty(defEmployeeList, "对不起，您选择的用户不是该企业的员工");
        return param.getEmployeeIdList();
    }

    /**
     * 在运营平台 将用户取消绑定到某个企业
     *
     * @param param param
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/10/28 12:23 AM
     * @create [2022/10/28 12:23 AM ] [tangyh] [初始创建]
     */
    @GlobalTransactional
    public Boolean unBindUser(DefTenantBindUserVO param) {
        Long tenantId = param.getTenantId();
        ArgumentAssert.notNull(tenantId, "请选择租户");

        // 演示环境专用标识，用于WriteInterceptor拦截器判断演示环境需要禁止用户执行sql，若您无需搭建演示环境，可以删除下面一行代码
        ContextUtil.setStop();

        List<Long> employeeIdList = findEmployeeIdList(param, tenantId);

        ContextUtil.setTenantBasePoolName(tenantId);
        List<BaseEmployee> employeeList = baseEmployeeService.list(Wraps.<BaseEmployee>lbQ()
                .in(BaseEmployee::getId, employeeIdList));
        boolean b = baseEmployeeService.removeByIds(employeeIdList);
        if (b) {
            tenantOrgUser(employeeList);
        }
        return b;
    }

    @GlobalTransactional
    public Boolean unBindUser(DefTenantBindEmployeeVO param) {
        Long tenantId = param.getTenantId();
        ArgumentAssert.notNull(tenantId, "请选择租户");

        // 演示环境专用标识，用于WriteInterceptor拦截器判断演示环境需要禁止用户执行sql，若您无需搭建演示环境，可以删除下面一行代码
        ContextUtil.setStop();

        List<Long> employeeIdList = findEmployeeIdList(param, tenantId);

        ContextUtil.setTenantBasePoolName(tenantId);
        List<BaseEmployee> employeeList = baseEmployeeService.list(Wraps.<BaseEmployee>lbQ()
                .in(BaseEmployee::getId, employeeIdList));
        boolean b = delete(employeeIdList);
        if (b) {
            tenantOrgUser(employeeList);
        }
        return b;
    }

    @GlobalTransactional
    public Boolean mobileByCode(EmployeeUpdateMobileByCodeVO data, BaseEmployee baseEmployee, Boolean isResetPassword) {
        CacheKey cacheKey = new CacheKey(RedisConstant.UPDATE_PASSWORD_MSG + data.getKey());
        CacheResult<String> cacheResult = cacheOps.get(cacheKey);
        ArgumentAssert.notEmpty(cacheResult.getValue(), "验证码已失效～");
        ArgumentAssert.equals(data.getCode(), cacheResult.getValue(), "验证码输入错误");
        //新手机号
        DefUser userByMobile = defUserService.getUserByMobile(data.getMobile());
        //老手机号
        DefUser oldUserByMobile = defUserService.getById(baseEmployee.getUserId());
        Long userId = null;
        //新手机号不存在
        if (ObjectUtil.isNull(userByMobile)) {
//            // 保存默认库的 用户表 和 员工表
//            String username = defUserService.genLoginAccount();
//            DefUserSaveVO userSaveVO = DefUserSaveVO.builder().username(username)
//                    .nickName(baseEmployee.getRealName())
//                    .mobile(data.getMobile()).sex(oldUserByMobile != null && StrUtil.isNotBlank(oldUserByMobile.getSex())
//                            ? oldUserByMobile.getSex() : "1")
//                    .state(true).idCard(baseEmployee.getIdNum())
//                    .build();
//            DefUserTenantRel defUserTenantRel = defUserService.saveUserAndUpdateEmployee(baseEmployee.getId(), userSaveVO);
//            userId = defUserTenantRel.getUserId();
            oldUserByMobile.setMobile(data.getMobile());
            if (isResetPassword) {
                oldUserByMobile.setSalt(RandomUtil.randomString(20));
                String sub = StrUtil.sub(oldUserByMobile.getMobile(), oldUserByMobile.getMobile().length() - 6, userByMobile.getMobile().length());
                String password = SecureUtil.sha256(sub + userByMobile.getSalt());
                oldUserByMobile.setPassword(password);
            }
            ArgumentAssert.isFalse(!defUserService.updateEntityById(oldUserByMobile), "重置密码失败，请联系管理员");
            userId = oldUserByMobile.getId();
        } else {
            BaseEmployee employee = baseEmployeeService.getOne(Wraps.<BaseEmployee>lbQ()
                    .eq(BaseEmployee::getUserId, userByMobile.getId())
                    .eq(BaseEmployee::getDeleteFlag, 0)
                    .last("limit 1")
            );
            //整合账号 将baseEmployee数据整合到employee
            if (ObjectUtil.isNotNull(employee)) {
                List<Long> orgIdListByEmployeeId = baseEmployeeOrgRelService.findOrgIdListByEmployeeId(employee.getId());
                if (CollUtil.isNotEmpty(orgIdListByEmployeeId)
                        && orgIdListByEmployeeId.contains(ContextUtil.getCurrentCompanyId())) {
                    ArgumentAssert.isFalse(true, "手机号已存在门店");
                }
                //绑定门店
                baseEmployeeOrgRelService.save(BaseEmployeeOrgRelSaveVO.builder()
                        .employeeId(employee.getId()).orgId(ContextUtil.getCurrentCompanyId())
                        .build());
                //绑定角色
                baseEmployeeRoleRelService.update(Wraps.<BaseEmployeeRoleRel>lbU()
                        .set(BaseEmployeeRoleRel::getEmployeeId, employee.getId())
                        .eq(BaseEmployeeRoleRel::getEmployeeId, baseEmployee.getId())
                        .eq(BaseEmployeeRoleRel::getDeleteFlag, 0)
                        .eq(BaseEmployeeRoleRel::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                );
                //修改助教设置
                baseServicePersonalService.update(Wraps.<BaseServicePersonal>lbU()
                        .set(BaseServicePersonal::getEmployeeId, employee.getId())
                        .eq(BaseServicePersonal::getEmployeeId, baseEmployee.getId())
                        .eq(BaseServicePersonal::getDeleteFlag, 0)
                        .eq(BaseServicePersonal::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                );
                posCashApi.updateServiceWraps(baseEmployee.getId(), employee.getId());
                return baseEmployeeService.deleteByIds(Collections.singletonList(baseEmployee.getId()));
            }
            DefUserTenantRel u = defUserTenantRelService.getById(baseEmployee.getId());
            u.setUserId(userByMobile.getId());
            ArgumentAssert.isFalse(!defUserTenantRelService.updateById(u), "操作失败，请联系管理员");
            userId = userByMobile.getId();
            if (isResetPassword) {
                userByMobile.setSalt(RandomUtil.randomString(20));
                String sub = StrUtil.sub(userByMobile.getMobile(), userByMobile.getMobile().length() - 6, userByMobile.getMobile().length());
                String password = SecureUtil.sha256(sub + userByMobile.getSalt());
                userByMobile.setPassword(password);
            }
            ArgumentAssert.isFalse(!defUserService.updateEntityById(userByMobile), "重置密码失败，请联系管理员");
        }
        baseEmployee.setUserId(userId);
        ArgumentAssert.isFalse(!baseEmployeeService.updateById(baseEmployee), "操作失败，请联系管理员");
        cacheOps.del(cacheKey);
        cacheKey = new CacheKey(RedisConstant.UPDATE_EMPLOYEE_MOBILE_MSG + data.getMobile());
        cacheOps.del(cacheKey);
        //清除账号登录
        template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.FORCE_LOGOUT,
                data.getMobile());
        //清除账号登录
        if (oldUserByMobile != null && StrUtil.isNotBlank(oldUserByMobile.getMobile())) {
            template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.FORCE_LOGOUT,
                    oldUserByMobile.getMobile());
        }
        return true;
    }
}
