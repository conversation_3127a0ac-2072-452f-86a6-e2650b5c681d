package top.kx.kxss.base.manager.user.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.user.BaseEmployeeBtnAuth;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.user.BaseEmployeeBtnAuthManager;
import top.kx.kxss.base.mapper.user.BaseEmployeeBtnAuthMapper;

/**
 * <p>
 * 通用业务实现类
 * 员工的按钮权限
 * </p>
 *
 * <AUTHOR>
 * @date 2023-12-11 17:40:40
 * @create [2023-12-11 17:40:40] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseEmployeeBtnAuthManagerImpl extends SuperManagerImpl<BaseEmployeeBtnAuthMapper, BaseEmployeeBtnAuth> implements BaseEmployeeBtnAuthManager {

}


