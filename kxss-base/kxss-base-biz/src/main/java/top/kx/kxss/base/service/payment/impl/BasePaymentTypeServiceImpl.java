package top.kx.kxss.base.service.payment.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperCacheServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.payment.BasePaymentType;
import top.kx.kxss.base.entity.payment.BasePaymentTypeExclude;
import top.kx.kxss.base.manager.payment.BasePaymentTypeExcludeManager;
import top.kx.kxss.base.manager.payment.BasePaymentTypeManager;
import top.kx.kxss.base.service.payment.BasePaymentTypeService;
import top.kx.kxss.base.vo.BizItemVO;
import top.kx.kxss.base.vo.BizTypeVO;
import top.kx.kxss.base.vo.query.payment.BasePaymentTypePageQuery;
import top.kx.kxss.base.vo.result.payment.BasePaymentTypeResultVO;
import top.kx.kxss.base.vo.save.payment.BasePaymentTypeSaveVO;
import top.kx.kxss.base.vo.update.payment.BasePaymentTypeUpdateVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.base.EquityTypeEnum;
import top.kx.kxss.model.enumeration.base.PayChannelEnum;
import top.kx.kxss.model.enumeration.base.PaymentBizTypeEnum;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 支付类型
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-19 14:43:55
 * @create [2023-09-19 14:43:55] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
public class BasePaymentTypeServiceImpl extends SuperCacheServiceImpl<BasePaymentTypeManager, Long, BasePaymentType, BasePaymentTypeSaveVO,
        BasePaymentTypeUpdateVO, BasePaymentTypePageQuery, BasePaymentTypeResultVO> implements BasePaymentTypeService {

    @Autowired
    private BasePaymentTypeExcludeManager excludeManager;

    @Override
    public Map<Serializable, Object> findByIds(Set<Serializable> ids) {
        return superManager.findByIds(ids.stream().map(Convert::toLong).collect(Collectors.toSet()));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public BasePaymentType getOne(LbQueryWrap<BasePaymentType> last) {
        return superManager.getOne(last);
    }

    @Override
    public BasePaymentTypeResultVO getDetail(Long id) {
        BasePaymentType basePaymentType = superManager.getById(id);
        ArgumentAssert.notNull(basePaymentType, "支付方式不存在");
        BasePaymentTypeResultVO basePaymentTypeResultVO = BeanUtil.copyProperties(basePaymentType, BasePaymentTypeResultVO.class);
        List<BasePaymentTypeExclude> excludeDbList = excludeManager.list(Wraps.<BasePaymentTypeExclude>lbQ()
                .eq(BasePaymentTypeExclude::getPaymentTypeId, id)
                .eq(BasePaymentTypeExclude::getDeleteFlag, 0)
        );
        if (CollectionUtils.isEmpty(excludeDbList)) {
            return basePaymentTypeResultVO;
        }
        Map<String, List<BizItemVO>> excludeMap = excludeDbList.stream()
                .collect(Collectors.groupingBy(BasePaymentTypeExclude::getBizType,
                        Collectors.mapping(e -> {
                            BizItemVO build = BizItemVO.builder().bizId(e.getBizId()).build();
                            if (Objects.equals(e.getBizType(), EquityTypeEnum.TABLE.getCode())) {
                                build.setTableId(build.getBizId());
                            } else if (Objects.equals(e.getBizType(), EquityTypeEnum.SERVICE.getCode())) {
                                build.setServiceId(build.getBizId());
                            } else if (Objects.equals(e.getBizType(), EquityTypeEnum.PRODUCT.getCode())) {
                                build.setProductId(build.getBizId());
                            }
                            return build;
                        }, Collectors.toList())));
        List<BizTypeVO> excludeList = new ArrayList<>();
        excludeMap.forEach((bizType, bizIdList) -> {
            excludeList.add(BizTypeVO.builder()
                    .bizType(bizType)
                    .itemList(bizIdList).build());
        });
        basePaymentTypeResultVO.setExcludeList(excludeList);
        return basePaymentTypeResultVO;
    }

    @Override
    public Boolean checkName(String name, Long id) {
        return superManager.count(Wraps.<BasePaymentType>lbQ().ne(ObjectUtil.isNotNull(id), BasePaymentType::getId, id).eq(BasePaymentType::getName, name)
                .eq(BasePaymentType::getCreatedOrgId, ContextUtil.getCurrentCompanyId())) > 0;
    }

    @Override
    protected BasePaymentType saveBefore(BasePaymentTypeSaveVO model) {
        ArgumentAssert.isFalse(checkName(model.getName(), null), "支付名称已存在");
        model.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        if (StrUtil.isBlank(model.getBizType())) {
            model.setBizType(PaymentBizTypeEnum.DIRECT.getCode());
        }
        if (model.getBizType().equals(PaymentBizTypeEnum.POLYMERIZATION.getCode())) {
            model.setIncomeFlag(1);
            model.setGiftIncomeFlag(2);
            if (StrUtil.isBlank(model.getPayChannel())) {
                model.setPayChannel(PayChannelEnum.SAOBEI.getCode());
            }
        }
        if (model.getBizType().equals(PaymentBizTypeEnum.WECHAT.getCode())) {
            model.setIncomeFlag(1);
            model.setGiftIncomeFlag(2);
            if (StrUtil.isBlank(model.getPayChannel())) {
                model.setPayChannel(PayChannelEnum.SAOBEI.getCode());
            }
        }
        ArgumentAssert.notNull(model.getIncomeFlag(), "请设置收入规则");
        if (model.getBizType().equals(PaymentBizTypeEnum.ACCOUNT.getCode())) {
            ArgumentAssert.notNull(model.getGiftIncomeFlag(), "请设置赠金收入规则");
        }
        if (ObjectUtil.isNull(model.getFeeRate())) {
            model.setFeeRate(BigDecimal.ZERO);
        }
        if (CollUtil.isNotEmpty(model.getUsableRange())) {
            List<String> collect = model.getUsableRange().stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
            model.setUsableRange(collect);
        }
        return super.saveBefore(model);
    }

    @Override
    protected BasePaymentType updateBefore(BasePaymentTypeUpdateVO model) {
        ArgumentAssert.isFalse(checkName(model.getName(), model.getId()), "支付名称已存在");
        if (CollUtil.isNotEmpty(model.getUsableRange())) {
            List<String> collect = model.getUsableRange().stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
            model.setUsableRange(collect);
        }
        if (ObjectUtil.isNull(model.getFeeRate())) {
            model.setFeeRate(BigDecimal.ZERO);
        }
        if (model.getBizType().equals(PaymentBizTypeEnum.POLYMERIZATION.getCode())) {
            model.setIncomeFlag(1);
            model.setGiftIncomeFlag(2);
            if (StrUtil.isBlank(model.getPayChannel())) {
                model.setPayChannel(PayChannelEnum.SAOBEI.getCode());
            }
        }
        if (model.getBizType().equals(PaymentBizTypeEnum.WECHAT.getCode())) {
            model.setIncomeFlag(1);
            model.setGiftIncomeFlag(2);
            if (StrUtil.isBlank(model.getPayChannel())) {
                model.setPayChannel(PayChannelEnum.SAOBEI.getCode());
            }
        }
        ArgumentAssert.notNull(model.getIncomeFlag(), "请设置收入规则");
        if (model.getBizType().equals(PaymentBizTypeEnum.ACCOUNT.getCode())) {
            ArgumentAssert.notNull(model.getGiftIncomeFlag(), "请设置赠金收入规则");
        }
        return super.updateBefore(model);
    }

    @Override
    protected void saveAfter(BasePaymentTypeSaveVO basePaymentTypeSaveVO, BasePaymentType entity) {
        List<BizTypeVO> excludeList = basePaymentTypeSaveVO.getExcludeList();
        if (CollectionUtils.isEmpty(excludeList)) {
            super.saveAfter(basePaymentTypeSaveVO, entity);
            return;
        }
        List<BasePaymentTypeExclude> excludeSaveList = new ArrayList<>();
        for (BizTypeVO bizTypeVO : excludeList) {
            List<BizItemVO> bizItemList = bizTypeVO.getItemList();
            for (BizItemVO bizItemVO : bizItemList) {
                excludeSaveList.add(BasePaymentTypeExclude.builder()
                        .bizType(bizTypeVO.getBizType())
                        .bizId(bizItemVO.getBizId())
                        .paymentTypeId(entity.getId())
                        .createdOrgId(entity.getCreatedOrgId())
                        .build()
                );
            }
        }
        excludeManager.saveBatch(excludeSaveList);
        super.saveAfter(basePaymentTypeSaveVO, entity);
    }

    @Override
    protected void updateAfter(BasePaymentTypeUpdateVO basePaymentTypeUpdateVO, BasePaymentType entity) {
        excludeManager.update(BasePaymentTypeExclude.builder().deleteFlag(1).build(),
                Wraps.<BasePaymentTypeExclude>lbU().eq(BasePaymentTypeExclude::getPaymentTypeId, entity.getId()));
        List<BizTypeVO> excludeList = basePaymentTypeUpdateVO.getExcludeList();
        if (CollectionUtils.isEmpty(excludeList)) {
            super.updateAfter(basePaymentTypeUpdateVO, entity);
            return;
        }
        List<BasePaymentTypeExclude> excludeSaveList = new ArrayList<>();
        for (BizTypeVO bizTypeVO : excludeList) {
            List<BizItemVO> bizItemList = bizTypeVO.getItemList();
            for (BizItemVO bizItemVO : bizItemList) {
                excludeSaveList.add(BasePaymentTypeExclude.builder()
                        .bizType(bizTypeVO.getBizType())
                        .bizId(bizItemVO.getBizId())
                        .paymentTypeId(entity.getId())
                        .createdOrgId(entity.getCreatedOrgId())
                        .build()
                );
            }
        }
        excludeManager.saveBatch(excludeSaveList);
        super.updateAfter(basePaymentTypeUpdateVO, entity);
    }

    @Override
    public Boolean updateState(Long id, Boolean state) {
        BasePaymentType build = BasePaymentType.builder().state(state).build();
        build.setId(id);
        return superManager.updateById(build);
    }

    @Override
    public Boolean updateApplet(Long id, Boolean state) {
        BasePaymentType build = BasePaymentType.builder()
                .isApplet(state).build();
        build.setId(id);
        return superManager.updateById(build);
    }

    @Override
    public List<BasePaymentTypeResultVO> queryPolymerize() {
        List<BasePaymentType> list = superManager.list(Wraps.<BasePaymentType>lbQ()
                .eq(BasePaymentType::getDeleteFlag, 0)
                .eq(BasePaymentType::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .in(BasePaymentType::getBizType,
                        Arrays.asList(PaymentBizTypeEnum.POLYMERIZATION.getCode(), PaymentBizTypeEnum.WECHAT.getCode())));
        return BeanUtil.copyToList(list, BasePaymentTypeResultVO.class);
    }
}


