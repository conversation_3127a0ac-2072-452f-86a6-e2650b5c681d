package top.kx.kxss.base.manager.card.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.card.BaseCard;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.card.BaseCardManager;
import top.kx.kxss.base.mapper.card.BaseCardMapper;

/**
 * <p>
 * 通用业务实现类
 * 权益卡
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-19 14:16:12
 * @create [2024-03-19 14:16:12] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseCardManagerImpl extends SuperManagerImpl<BaseCardMapper, BaseCard> implements BaseCardManager {

}


