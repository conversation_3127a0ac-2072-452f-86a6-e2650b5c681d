package top.kx.kxss.base.service.accounting;

import com.baomidou.mybatisplus.core.metadata.IPage;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.base.service.SuperService;
import top.kx.kxss.app.entity.cash.payment.PosCashPayment;
import top.kx.kxss.base.entity.accounting.BaseAccountingDate;
import top.kx.kxss.base.vo.query.accounting.AccountingQuery;
import top.kx.kxss.base.vo.query.accounting.BaseAccountingDatePageQuery;
import top.kx.kxss.base.vo.result.accounting.*;
import top.kx.kxss.base.vo.save.accounting.BaseAccountingDateSaveVO;
import top.kx.kxss.base.vo.save.accounting.BaseAccountingSaveVO;
import top.kx.kxss.base.vo.update.accounting.BaseAccountingDateUpdateVO;
import top.kx.kxss.base.vo.update.accounting.BaseAccountingUpdateVO;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;


/**
 * <p>
 * 业务接口
 * 记账日期
 * </p>
 *
 * <AUTHOR>
 * @date 2023-10-09 16:35:54
 * @create [2023-10-09 16:35:54] [dou] [代码生成器生成]
 */
public interface BaseAccountingDateService extends SuperService<Long, BaseAccountingDate, BaseAccountingDateSaveVO,
    BaseAccountingDateUpdateVO, BaseAccountingDatePageQuery, BaseAccountingDateResultVO> {

    boolean saveAccounting(BaseAccountingSaveVO model);

    boolean updateAccounting(BaseAccountingUpdateVO model);

    boolean del(Long id);

    IPage<BaseAccountingResultVO> pageList(PageParams<BaseAccountingDatePageQuery> params);

    Map<String ,Object> statistics(BaseAccountingDatePageQuery query);

    void addAccounting(List<PosCashPayment> cashPaymentList, LocalDate localDate);

    void reductionAccounting(List<PosCashPayment> cashPaymentList, LocalDate localDate);

    BaseAccountingVO detail(Long id);

    List<AccountingCalenderResultVO> calendar(AccountingQuery params);

    AccountingCalenderSumResultVO calendarSum(AccountingQuery params);

    IPage<BaseAccountingInfoResultVO> calendarDetail(PageParams<AccountingQuery> params);

    List<BaseAccountingInfoResultVO> calendarDetailList(AccountingQuery model);
}


