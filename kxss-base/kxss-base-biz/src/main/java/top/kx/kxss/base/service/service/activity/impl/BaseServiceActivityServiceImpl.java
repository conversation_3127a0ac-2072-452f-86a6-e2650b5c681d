package top.kx.kxss.base.service.service.activity.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.base.entity.biz.equity.BaseBizEquity;
import top.kx.kxss.base.entity.biz.equity.BaseBizEquityApply;
import top.kx.kxss.base.entity.biz.equity.BaseBizEquityExclude;
import top.kx.kxss.base.entity.service.activity.BaseServiceActivity;
import top.kx.kxss.base.entity.service.activity.BaseServiceActivityDetail;
import top.kx.kxss.base.manager.biz.equity.BaseBizEquityApplyManager;
import top.kx.kxss.base.manager.biz.equity.BaseBizEquityExcludeManager;
import top.kx.kxss.base.manager.biz.equity.BaseBizEquityManager;
import top.kx.kxss.base.manager.service.activity.BaseServiceActivityDetailManager;
import top.kx.kxss.base.manager.service.activity.BaseServiceActivityManager;
import top.kx.kxss.base.service.biz.BaseBizAvailableTimeService;
import top.kx.kxss.base.service.service.activity.BaseServiceActivityService;
import top.kx.kxss.base.vo.query.service.activity.BaseServiceActivityPageQuery;
import top.kx.kxss.base.vo.result.service.activity.BaseServiceActivityResultVO;
import top.kx.kxss.base.vo.save.biz.BaseBizAvailableTimeSaveVO;
import top.kx.kxss.base.vo.save.biz.equity.BaseBizEquityApplySaveVO;
import top.kx.kxss.base.vo.save.biz.equity.BaseBizEquityExcludeSaveVO;
import top.kx.kxss.base.vo.save.biz.equity.BaseBizEquitySaveVO;
import top.kx.kxss.base.vo.save.service.activity.BaseServiceActivitySaveVO;
import top.kx.kxss.base.vo.update.service.activity.BaseServiceActivityUpdateVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.base.BaseEquityTypeEnum;
import top.kx.kxss.model.enumeration.base.BizModuleEnum;
import top.kx.kxss.model.enumeration.base.EquityBizTypeEnum;
import top.kx.kxss.model.enumeration.base.WeekEnum;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 服务活动
 * </p>
 *
 * <AUTHOR>
 * @date 2024-08-12 14:14:42
 * @create [2024-08-12 14:14:42] [dou] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseServiceActivityServiceImpl extends SuperServiceImpl<BaseServiceActivityManager, Long, BaseServiceActivity, BaseServiceActivitySaveVO,
        BaseServiceActivityUpdateVO, BaseServiceActivityPageQuery, BaseServiceActivityResultVO> implements BaseServiceActivityService {

    @Autowired
    private BaseBizAvailableTimeService availableTimeService;
    @Autowired
    private BaseBizEquityManager equityManager;
    @Autowired
    private BaseBizEquityApplyManager equityApplyManager;
    @Autowired
    private BaseBizEquityExcludeManager equityExcludeManager;
    @Autowired
    private BaseServiceActivityDetailManager serviceActivityDetailManager;
    @Autowired
    private EchoService echoService;

    @Override
    public Boolean checkName(String name, Long id) {
        return superManager.count(Wraps.<BaseServiceActivity>lbQ().ne(ObjectUtil.isNotNull(id), BaseServiceActivity::getId, id)
                .eq(BaseServiceActivity::getName, name)
                .eq(BaseServiceActivity::getDeleteFlag, 0)
                .eq(BaseServiceActivity::getCreatedOrgId, ContextUtil.getCurrentCompanyId())) > 0;
    }

    @Override
    protected BaseServiceActivity saveBefore(BaseServiceActivitySaveVO model) {
        ArgumentAssert.isFalse(checkName(model.getName(), null), "活动名称已存在");
        if (ObjectUtil.isNull(model.getState())) {
            model.setState(true);
        }
        if (ObjectUtil.isNull(model.getIsSuperpose())) {
            model.setIsSuperpose(true);
        }
        if (model.getSortValue() == null) {
            BaseServiceActivity one = superManager.getOne(Wraps.<BaseServiceActivity>lbQ()
                    .isNotNull(BaseServiceActivity::getSortValue)
                    .orderByDesc(BaseServiceActivity::getSortValue)
                    .last(" limit 1"));
            model.setSortValue(one == null || one.getSortValue() == null ? 10 : one.getSortValue() + 10);
        }
        model.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        ArgumentAssert.isFalse(model.getDiscountValue().compareTo(new BigDecimal("9.9")) > 0
                || model.getDiscountValue().compareTo(BigDecimal.ZERO) < 0, "请输入0-9.9折扣值");
        if (CollUtil.isNotEmpty(model.getNoAvailableDateList())) {
            ArgumentAssert.isFalse(model.getNoAvailableDateList().size() > 7, "最多填写七个不可用日期");
        }
        ArgumentAssert.notEmpty(model.getEquityList(), "请配置权益");
        for (BaseBizEquitySaveVO baseBizEquitySaveVO : model.getEquityList()) {
            if (!baseBizEquitySaveVO.getBizType().equals(EquityBizTypeEnum.ALL.getCode())) {
                ArgumentAssert.notEmpty(baseBizEquitySaveVO.getApplyList(), "请配置指定权益");
            }
        }
        return super.saveBefore(model);
    }

    @Override
    protected BaseServiceActivity updateBefore(BaseServiceActivityUpdateVO model) {
        ArgumentAssert.isFalse(checkName(model.getName(), model.getId()), "活动名称已存在");
        ArgumentAssert.isFalse(model.getDiscountValue().compareTo(new BigDecimal("9.9")) > 0
                || model.getDiscountValue().compareTo(BigDecimal.ZERO) < 0, "请输入0-9.9折扣值");
        if (CollUtil.isNotEmpty(model.getNoAvailableDateList())) {
            ArgumentAssert.isFalse(model.getNoAvailableDateList().size() > 7, "最多填写七个不可用日期");
        }
        if (model.getSortValue() == null) {
            BaseServiceActivity one = superManager.getOne(Wraps.<BaseServiceActivity>lbQ()
                    .isNotNull(BaseServiceActivity::getSortValue)
                    .orderByDesc(BaseServiceActivity::getSortValue)
                    .last(" limit 1"));
            model.setSortValue(one == null || one.getSortValue() == null ? 10 : one.getSortValue() + 10);
        }
        ArgumentAssert.notEmpty(model.getEquityList(), "请配置权益");
        for (BaseBizEquitySaveVO baseBizEquitySaveVO : model.getEquityList()) {
            if (!baseBizEquitySaveVO.getBizType().equals(EquityBizTypeEnum.ALL.getCode())) {
                ArgumentAssert.notEmpty(baseBizEquitySaveVO.getApplyList(), "请配置指定权益");
            }
        }
        return super.updateBefore(model);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseServiceActivity saveServiceActivity(BaseServiceActivitySaveVO model) {
        //保存卡基本信息
        BaseServiceActivity baseServiceActivity = save(model);
        //保存卡使用时间
        availableTimeService.saveUsageTime(baseServiceActivity.getId(), BizModuleEnum.SERVICE_ACTIVITY, model);
        //使用权益信息
        usageEquity(baseServiceActivity, model.getEquityList());
        //使用服务项目
        usageService(baseServiceActivity, model.getServiceList());
        return baseServiceActivity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseServiceActivity updateServiceActivity(BaseServiceActivityUpdateVO model) {
        BaseServiceActivity baseServiceActivity = updateById(model);
        //保存卡使用时间
        availableTimeService.saveUsageTime(baseServiceActivity.getId(), BizModuleEnum.SERVICE_ACTIVITY, model);
        //使用权益信息
        usageEquity(baseServiceActivity, model.getEquityList());
        //使用服务项目
        usageService(baseServiceActivity, model.getServiceList());
        return baseServiceActivity;
    }

    @Override
    public BaseServiceActivityResultVO detail(Long aLong) {
        BaseServiceActivity baseServiceActivity = superManager.getById(aLong);
        BaseServiceActivityResultVO resultVO = BeanUtil.copyProperties(baseServiceActivity, BaseServiceActivityResultVO.class);
        availableTimeService.getResultVO(baseServiceActivity.getId(), BizModuleEnum.SERVICE_ACTIVITY, resultVO);

        List<BaseBizEquity> equityList = equityManager.list(Wraps.<BaseBizEquity>lbQ()
                .eq(BaseBizEquity::getModule, BizModuleEnum.SERVICE_ACTIVITY.getCode())
                .eq(BaseBizEquity::getSourceId, baseServiceActivity.getId()));
        if (CollUtil.isEmpty(equityList)) {
            return resultVO;
        }
        List<BaseBizEquitySaveVO> voList = BeanUtil.copyToList(equityList, BaseBizEquitySaveVO.class);
        List<Long> equityIds = equityList.stream().map(BaseBizEquity::getId).collect(Collectors.toList());
        Map<Long, List<BaseBizEquityApply>> applyMap = equityApplyManager.list(Wraps.<BaseBizEquityApply>lbQ()
                .eq(BaseBizEquityApply::getModule, BizModuleEnum.SERVICE_ACTIVITY.getCode())
                .eq(BaseBizEquityApply::getSourceId, baseServiceActivity.getId())
                .in(BaseBizEquityApply::getEquityId, equityIds)
        ).stream().collect(Collectors.groupingBy(BaseBizEquityApply::getEquityId));

        Map<Long, List<BaseBizEquityExclude>> excludeMap = equityExcludeManager.list(Wraps.<BaseBizEquityExclude>lbQ()
                .eq(BaseBizEquityExclude::getModule, BizModuleEnum.SERVICE_ACTIVITY.getCode())
                .eq(BaseBizEquityExclude::getSourceId, baseServiceActivity.getId())
                .in(BaseBizEquityExclude::getEquityId, equityIds)
        ).stream().collect(Collectors.groupingBy(BaseBizEquityExclude::getEquityId));
        for (BaseBizEquitySaveVO equity : voList) {
            List<BaseBizEquityApply> applyList = applyMap.get(equity.getId());
            equity.setApplyList(BeanUtil.copyToList(applyList, BaseBizEquityApplySaveVO.class));
            List<BaseBizEquityExclude> excludeList = excludeMap.get(equity.getId());
            equity.setExcludeList(BeanUtil.copyToList(excludeList, BaseBizEquityExcludeSaveVO.class));
        }
        resultVO.setEquityList(voList);
        if (StrUtil.isNotBlank(baseServiceActivity.getWeeks())) {
            resultVO.setWeek(String.join(",", baseServiceActivity.getWeeks()));
        }
        resultVO.setServiceList(serviceActivityDetailManager.listObjs(Wraps.<BaseServiceActivityDetail>lbQ()
                .select(BaseServiceActivityDetail::getServiceId)
                .eq(BaseServiceActivityDetail::getActivityId, baseServiceActivity.getId()), Convert::toLong));
        echoService.action(resultVO);
        usageRule(resultVO);
        return resultVO;
    }

    @Override
    public Boolean updateState(Long id) {
        ArgumentAssert.notNull(id, "服务活动不能为空");
        BaseServiceActivity baseServiceActivity = superManager.getById(id);
        ArgumentAssert.notNull(baseServiceActivity, "服务活动信息不存在");
        baseServiceActivity.setState(!baseServiceActivity.getState());
        return superManager.updateById(baseServiceActivity);
    }

    private void usageRule(BaseServiceActivityResultVO resultVO) {
        if (CollUtil.isNotEmpty(resultVO.getAvailableTimeList())) {
            resultVO.getAvailableTimeList().stream()
                    .sorted(Comparator.comparing(BaseBizAvailableTimeSaveVO::getStartTime));
            List<String> collect = resultVO.getAvailableTimeList().stream().map(v -> v.getStartTime() + "-" + v.getEndTime())
                    .collect(Collectors.toList());

            resultVO.getUsageTime().add(String.join("/", collect));
        }
        if (CollUtil.isNotEmpty(resultVO.getWeeks())) {
            if (resultVO.getWeeks().size() < 7) {
                resultVO.getUsageDate()
                        .add(resultVO.getWeeks().stream().map(v -> WeekEnum.get(v).getDesc())
                                .collect(Collectors.joining("/")));
            } else {
                resultVO.getUsageDate()
                        .add("全天可用");
            }
        }
        if (CollUtil.isNotEmpty(resultVO.getNoAvailableDateList())) {
            List<String> collect = resultVO.getNoAvailableDateList().stream().map(v ->
                            DateUtils.format(v.getStartDate(), "yyyy/MM/dd")
                                    + "-" + DateUtils.format(v.getEndDate(), "yyyy/MM/dd"))
                    .collect(Collectors.toList());
            resultVO.getNoUsageTime().add(String.join(",", collect));
        }
        if (resultVO.getIsHoliday()) {
            resultVO.getUsageDate().add("节假日可用");
        } else {
            resultVO.getNoUsageTime().add("节假日不可用");
        }
    }


    private void usageEquity(BaseServiceActivity baseServiceActivity, List<BaseBizEquitySaveVO> equityList) {
        equityManager.remove(Wraps.<BaseBizEquity>lbQ()
                .eq(BaseBizEquity::getModule, BizModuleEnum.SERVICE_ACTIVITY.getCode())
                .eq(BaseBizEquity::getSourceId, baseServiceActivity.getId()));
        equityApplyManager.remove(Wraps.<BaseBizEquityApply>lbQ()
                .eq(BaseBizEquityApply::getModule, BizModuleEnum.SERVICE_ACTIVITY.getCode())
                .eq(BaseBizEquityApply::getSourceId, baseServiceActivity.getId()));
        equityExcludeManager.remove(Wraps.<BaseBizEquityExclude>lbQ()
                .eq(BaseBizEquityExclude::getModule, BizModuleEnum.SERVICE_ACTIVITY.getCode())
                .eq(BaseBizEquityExclude::getSourceId, baseServiceActivity.getId()));
        List<BaseBizEquityApply> applyList = Lists.newArrayList();
        List<BaseBizEquityExclude> excludeList = Lists.newArrayList();
        for (BaseBizEquitySaveVO equity : equityList) {
            BaseBizEquity build = BeanUtil.copyProperties(equity, BaseBizEquity.class);
            build.setModule(BizModuleEnum.SERVICE_ACTIVITY.getCode());
            build.setSourceId(baseServiceActivity.getId());
            build.setDeleteFlag(0);
            build.setId(null);
            build.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
            build.setName(BaseEquityTypeEnum.get(equity.getType()).getDesc());
            equityManager.save(build);
            List<BaseBizEquityApplySaveVO> applyIdList = equity.getApplyList();
            if (CollUtil.isNotEmpty(applyIdList)) {
                applyList.addAll(applyIdList.stream().map(v -> {
                    BaseBizEquityApply apply = new BaseBizEquityApply();
                    apply.setModule(BizModuleEnum.SERVICE_ACTIVITY.getCode());
                    apply.setSourceId(baseServiceActivity.getId());
                    apply.setEquityId(build.getId());
                    apply.setBizId(v.getBizId());
                    apply.setName(v.getName());
                    apply.setDeleteFlag(0);
                    apply.setId(null);
                    apply.setBizType(build.getBizType());
                    apply.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                    apply.setType(build.getType());
                    return apply;
                }).collect(Collectors.toList()));
            }
            List<BaseBizEquityExcludeSaveVO> excludeIdList = equity.getExcludeList();
            if (CollUtil.isNotEmpty(excludeIdList)) {
                excludeList.addAll(excludeIdList.stream().map(v -> {
                    BaseBizEquityExclude exclude = new BaseBizEquityExclude();
                    exclude.setModule(BizModuleEnum.SERVICE_ACTIVITY.getCode());
                    exclude.setSourceId(baseServiceActivity.getId());
                    exclude.setEquityId(build.getId());
                    exclude.setBizId(v.getBizId());
                    exclude.setName(v.getName());
                    exclude.setDeleteFlag(0);
                    exclude.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                    exclude.setType(build.getType());
                    exclude.setId(null);
                    exclude.setBizType(build.getBizType());
                    return exclude;
                }).collect(Collectors.toList()));
            }
        }
        if (CollUtil.isNotEmpty(applyList)) {
            equityApplyManager.saveBatch(applyList);
        }
        if (CollUtil.isNotEmpty(excludeList)) {
            equityExcludeManager.saveBatch(excludeList);
        }
    }

    private void usageService(BaseServiceActivity baseServiceActivity, List<Long> serviceList) {
        serviceActivityDetailManager.remove(Wraps.<BaseServiceActivityDetail>lbQ()
                .eq(BaseServiceActivityDetail::getActivityId, baseServiceActivity.getId()));
        if (CollUtil.isNotEmpty(serviceList)) {
            List<BaseServiceActivityDetail> collect = serviceList.stream().map(v -> BaseServiceActivityDetail.builder()
                    .activityId(baseServiceActivity.getId())
                    .serviceId(v).deleteFlag(0)
                    .createdOrgId(ContextUtil.getCurrentCompanyId())
                    .build()).collect(Collectors.toList());
            serviceActivityDetailManager.saveBatch(collect);
        }
    }
}


