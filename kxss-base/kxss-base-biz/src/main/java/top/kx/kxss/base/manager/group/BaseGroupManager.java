package top.kx.kxss.base.manager.group;

import top.kx.basic.base.manager.SuperManager;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.interfaces.echo.LoadService;
import top.kx.kxss.base.entity.group.BaseGroup;
import top.kx.kxss.base.vo.result.group.BaseGroupResultVO;

import java.util.List;

/**
 * <p>
 * 通用业务接口
 * 组
 * </p>
 *
 * <AUTHOR>
 * @date 2025-01-06 15:47:18
 * @create [2025-01-06 15:47:18] [yan] [代码生成器生成]
 */
public interface BaseGroupManager extends SuperManager<BaseGroup>, LoadService {

    List<BaseGroupResultVO> findList(LbQueryWrap<BaseGroup> wrap);
}


