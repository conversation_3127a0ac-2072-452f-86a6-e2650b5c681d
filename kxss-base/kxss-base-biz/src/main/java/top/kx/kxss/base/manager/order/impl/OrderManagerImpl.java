package top.kx.kxss.base.manager.order.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.request.PageParams;
import top.kx.kxss.app.cash.PosCashApi;
import top.kx.kxss.app.vo.query.cash.PosCashPageQuery;
import top.kx.kxss.app.vo.result.cash.PosCashResultVO;
import top.kx.kxss.base.manager.order.OrderManager;

/**
 * <p>
 * 通用业务实现类
 * 权益卡
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-14 11:01:58
 * @create [2023-04-14 11:01:58] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class OrderManagerImpl implements OrderManager {

    @Autowired
    private PosCashApi posCashApi;


    @Override
    public R<Page<PosCashResultVO>> page(PageParams<PosCashPageQuery> params) {
        return posCashApi.page(params);
    }

    @Override
    public Boolean partialRefund() {
        return true;
    }
}


