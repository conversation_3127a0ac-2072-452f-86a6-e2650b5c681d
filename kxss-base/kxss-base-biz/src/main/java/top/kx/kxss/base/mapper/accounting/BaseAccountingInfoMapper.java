package top.kx.kxss.base.mapper.accounting;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import top.kx.basic.base.mapper.SuperMapper;
import top.kx.kxss.app.entity.cash.PosCash;
import top.kx.kxss.app.vo.result.cash.AmountResultVO;
import top.kx.kxss.base.entity.accounting.BaseAccountingInfo;
import top.kx.kxss.base.vo.result.accounting.AccountingCalenderResultVO;
import top.kx.kxss.base.vo.result.accounting.AccountingCalenderSumResultVO;

import java.util.List;

/**
 * <p>
 * Mapper 接口
 * 记账明细
 * </p>
 *
 * <AUTHOR>
 * @date 2023-10-09 16:35:55
 * @create [2023-10-09 16:35:55] [dou] [代码生成器生成]
 */
@Repository
public interface BaseAccountingInfoMapper extends SuperMapper<BaseAccountingInfo> {

    List<AccountingCalenderResultVO> calendar(@Param("field") String field, @Param(Constants.WRAPPER) Wrapper<BaseAccountingInfo> wrapper);

    AccountingCalenderSumResultVO calendarSum(@Param(Constants.WRAPPER) Wrapper<BaseAccountingInfo> wrapper);

    List<AmountResultVO> calendarIn(@Param(Constants.WRAPPER) QueryWrapper<PosCash> wrapper, @Param("hour") Integer hour, @Param("field") String field);

    List<AmountResultVO> calendarInList(@Param(Constants.WRAPPER) QueryWrapper<PosCash> wrapper);
}


