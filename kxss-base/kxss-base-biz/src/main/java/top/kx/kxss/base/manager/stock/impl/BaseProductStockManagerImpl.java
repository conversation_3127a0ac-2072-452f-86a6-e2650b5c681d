package top.kx.kxss.base.manager.stock.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.outin.BaseOutin;
import top.kx.kxss.base.entity.outin.BaseOutinProduct;
import top.kx.kxss.base.entity.outin.BaseOutinStocktaking;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.stock.BaseProductStock;
import top.kx.kxss.base.entity.warehouse.BaseWarehouse;
import top.kx.kxss.base.manager.outin.BaseOutinProductManager;
import top.kx.kxss.base.manager.stock.BaseProductStockManager;
import top.kx.kxss.base.manager.warehouse.BaseWarehouseManager;
import top.kx.kxss.base.mapper.product.BaseProductMapper;
import top.kx.kxss.base.mapper.stock.BaseProductStockMapper;
import top.kx.kxss.base.service.outin.BaseOutinStocktakingService;
import top.kx.kxss.base.vo.result.product.BasePrintHandoverStockResultVO;
import top.kx.kxss.model.enumeration.base.BaseOutinStateEnum;
import top.kx.kxss.model.enumeration.base.OutinTypeEnum;
import top.kx.kxss.pos.query.print.ProductStockPrintQuery;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用业务实现类
 * 商品现存量
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-10 11:34:30
 * @create [2023-04-10 11:34:30] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseProductStockManagerImpl extends SuperManagerImpl<BaseProductStockMapper, BaseProductStock> implements BaseProductStockManager {

    @Autowired
    private BaseProductMapper baseProductMapper;
    @Autowired
    private BaseOutinProductManager baseOutinProductManager;
    @Autowired
    private BaseWarehouseManager baseWarehouseManager;
    @Autowired
    private BaseOutinStocktakingService baseOutinStocktakingService;

    @Override
    public List<BaseProductStock> getStockListWithLock(List<Long> productIds) {
        return list(Wraps.<BaseProductStock>lbQ()
                .in(BaseProductStock::getProductId, productIds)
                .last("FOR UPDATE"));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean checkStock(BaseOutin baseOutin, List<BaseOutinProduct> outinProductList, List<BaseProductStock> baseProductStockList) {
        log.info("开始检查商品库存 - 单据类型: {}, 单据ID: {}", baseOutin.getType(), baseOutin.getId());
        //查询商品现有量
        List<Long> productIds = outinProductList.stream().map(BaseOutinProduct::getProductId).collect(Collectors.toList());

        // 如果不是盘点, 需要校验是不是存在盘点单
        if (!ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.INVENTORY_PROFIT_IN.getCode())
                && !ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.INVENTORY_LOSS_OUT.getCode())) {
            // 判断是否有盘点单
            List<Long> warehouseIds = outinProductList.stream().map(BaseOutinProduct::getWarehouseId).distinct().collect(Collectors.toList());
            List<BaseOutinStocktaking> stocktakingList = baseOutinStocktakingService.list(Wraps.<BaseOutinStocktaking>lbQ()
                    .eq(BaseOutinStocktaking::getDeleteFlag, 0)
                    .in(BaseOutinStocktaking::getWarehouseId, warehouseIds)
                    .eq(BaseOutinStocktaking::getState, BaseOutinStateEnum.NO_REVIEWED.getState()));
            if (CollUtil.isNotEmpty(stocktakingList)) {
                List<Long> collect = stocktakingList.stream().map(BaseOutinStocktaking::getId).collect(Collectors.toList());
                List<BaseOutinProduct> productList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ().in(BaseOutinProduct::getStocktakingId, collect)
                        .in(BaseOutinProduct::getProductId, productIds)
                        .eq(BaseOutinProduct::getDeleteFlag, 0));
                ArgumentAssert.isTrue(CollUtil.isEmpty(productList), "当前有盘点单未完成, 请先完成盘点单");
            }
        }

        Map<String, BaseProductStock> productStockMap = baseProductStockList.stream()
                .collect(Collectors.toMap(k -> k.getProductId() + "_" + k.getWarehouseId(), k -> k));
        List<BaseProduct> baseProducts = baseProductMapper.selectList(Wraps.<BaseProduct>lbQ().in(BaseProduct::getId, productIds));
        Map<Long, BaseProduct> baseProductMap =
                CollUtil.isEmpty(baseProducts) ? MapUtil.newHashMap() :
                        baseProducts.stream().collect(Collectors.toMap(BaseProduct::getId, Function.identity()));

        // 创建Map来跟踪每个商品和仓库组合的累计变化量
        Map<String, Integer> accumulatedChanges = new HashMap<>();
        Map<String, Integer> accumulatedLockChanges = new HashMap<>();

        //计算库存信息
        for (BaseOutinProduct baseOutinProduct : outinProductList) {
            BaseProduct baseProduct = baseProductMap.get(baseOutinProduct.getProductId());
            if (ObjectUtil.isNull(baseProduct)) {
                continue;
            }
            // 空指针- 不知道为啥空指针
            if (ObjectUtil.isNull(baseOutinProduct.getNum())) {
                baseOutinProduct.setNum(0);
            }
            baseOutinProduct.setName(baseProduct.getName());
            baseOutinProduct.setOrginPrice(baseProduct.getRetailPrice());
            baseOutinProduct.setPrice(baseProduct.getRetailPrice());
            // 如果是积分兑换,设置为空
            if (ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.SCORE_EXCHANGE.getCode())) {
                baseOutinProduct.setPrice(BigDecimal.ZERO);
            }

            BaseProductStock baseProductStock = productStockMap.get(baseOutinProduct.getProductId() + "_" + baseOutinProduct.getWarehouseId());
            if (ObjectUtil.isNull(baseProductStock)) {
                baseProductStock = BaseProductStock.builder()
                        .costPrice(baseProduct.getBuyingPrice())
                        .lockNum(0).num(0).build();
            }

            // 获取当前商品和仓库的累计变化量
            String stockKey = baseOutinProduct.getProductId() + "_" + baseOutinProduct.getWarehouseId();
            int accumulatedChange = accumulatedChanges.getOrDefault(stockKey, 0);
            int accumulatedLockChange = accumulatedLockChanges.getOrDefault(stockKey, 0);

            // 记录检查前的库存状态
            log.info("库存检查前 - 商品ID: {}, 商品名称: {}, 当前库存: {}, 锁定库存: {}, 操作数量: {}, 操作类型: {}, 累计库存变化: {}, 累计锁定变化: {}",
                    baseProduct.getId(),
                    baseProduct.getName(),
                    baseProductStock.getNum(),
                    baseProductStock.getLockNum(),
                    baseOutinProduct.getNum(),
                    baseOutinProduct.getNumType(),
                    accumulatedChange,
                    accumulatedLockChange);

            // 设置默认值，但不修改原始库存对象
            int currentLockNum = ObjectUtil.isNull(baseProductStock.getLockNum()) ? 0 : baseProductStock.getLockNum();
            int currentNum = baseProductStock.getNum();

            if ((ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_IN.getCode()) && !Objects.equal(baseOutin.getState(), BaseOutinStateEnum.INVALID.getState()))
                    || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.REFUND_IN.getCode())
                    || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_OUT_REVERSAL_ENTRY.getCode())
                    || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.OTHER_IN.getCode())
                    || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.RETURN.getCode())
                    || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.INVENTORY_PROFIT_IN.getCode())
                    || (ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.ADJUSTMENT_IN.getCode()) && !Objects.equal(baseOutin.getState(), BaseOutinStateEnum.INVALID.getState()))
            ) {
                //实际库存，考虑累计变化
                baseOutinProduct.setResidueNum(
                        (currentNum - currentLockNum)
                                + accumulatedChange + baseOutinProduct.getNum());

                // 更新累计变化
                accumulatedChanges.put(stockKey, accumulatedChange + baseOutinProduct.getNum());
                continue;
            }

            // 计算预期的锁定库存变化
            int expectedLockNum = currentLockNum;
            if (baseOutin.getIsLockNum() == null || baseOutin.getIsLockNum()) {
                if ("2".equals(baseOutinProduct.getNumType())) {
                    expectedLockNum = currentLockNum + baseOutinProduct.getNum() + accumulatedLockChange;
                } else {
                    expectedLockNum = currentLockNum - baseOutinProduct.getNum() + accumulatedLockChange;
                }
                // 更新累计锁定变化
                int lockChange = "2".equals(baseOutinProduct.getNumType()) ? baseOutinProduct.getNum() : -baseOutinProduct.getNum();
                accumulatedLockChanges.put(stockKey, accumulatedLockChange + lockChange);
            }

            // 计算预期的库存数量变化，考虑累计变化
            int expectedNum = currentNum;
            if (baseOutin.getIsNum() != null && baseOutin.getIsNum()) {
                if ("2".equals(baseOutinProduct.getNumType())) {
                    expectedNum = currentNum - baseOutinProduct.getNum();
                } else {
                    expectedNum = currentNum + baseOutinProduct.getNum();
                }
            }

            // 计算residueNum时考虑累计变化
            baseOutinProduct.setLockNum(expectedLockNum);
            int residueNum = expectedNum - expectedLockNum;
            baseOutinProduct.setResidueNum(residueNum);
            baseOutinProduct.setAmount(baseOutinProduct.getPrice().multiply(new BigDecimal(baseOutinProduct.getNum()))
                    .setScale(2, RoundingMode.HALF_UP));

            // 更新累计变化
            int change = "2".equals(baseOutinProduct.getNumType()) ? -baseOutinProduct.getNum() : baseOutinProduct.getNum();
            accumulatedChanges.put(stockKey, accumulatedChange + change);

            // 记录检查后的库存状态
            log.info("库存检查后 - 商品ID: {}, 商品名称: {}, 当前库存: {}, 预期锁定库存: {}, 预期剩余库存: {}, 累计库存变化: {}, 累计锁定变化: {}",
                    baseProduct.getId(),
                    baseProduct.getName(),
                    currentNum,
                    expectedLockNum,
                    residueNum,
                    accumulatedChanges.get(stockKey),
                    accumulatedLockChanges.get(stockKey));
        }

        //入库直接返回true
        if ((ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_IN.getCode()) && !Objects.equal(baseOutin.getState(), BaseOutinStateEnum.INVALID.getState()))
                // 作废情况下的采购退货
                || (ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_OUT.getCode()) && Objects.equal(baseOutin.getState(), BaseOutinStateEnum.INVALID.getState()))
                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.REFUND_IN.getCode())
                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.OTHER_IN.getCode())
                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_OUT_REVERSAL_ENTRY.getCode())
                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.RETURN.getCode())
                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.INVENTORY_PROFIT_IN.getCode())
                || (ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.ADJUSTMENT_IN.getCode()) && !Objects.equal(baseOutin.getState(), BaseOutinStateEnum.INVALID.getState()))
                // 作废情况下的调库出库
                || (ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.ADJUSTMENT_OUT.getCode()) && Objects.equal(baseOutin.getState(), BaseOutinStateEnum.INVALID.getState()))
        ) {
            return true;
        }

        //出库时验证库存
        for (BaseOutinProduct baseOutinProduct : outinProductList) {
            if ("1".equals(baseOutinProduct.getNumType())) {
                continue;
            }
            //验证商品现有量为空时，是否支持负库存
            BaseProductStock baseProductStock = productStockMap.get(baseOutinProduct.getProductId() + "_" + baseOutinProduct.getWarehouseId());
            BaseProduct baseProduct = baseProductMap.get(baseOutinProduct.getProductId());
            if (ObjectUtil.isNull(baseProductStock)) {
                if (baseProduct == null) {
                    log.error("商品不存在 - 商品ID: {}", baseOutinProduct.getProductId());
                    return false;
                }
                if (!baseProduct.getIsNegative()) {
                    log.error("商品不支持负库存 - 商品ID: {}, 商品名称: {}", baseProduct.getId(), baseProduct.getName());
                    return false;
                }
            }
            //不支付负库存
            if (baseProduct != null && !baseProduct.getIsNegative()) {
                log.info("出库库存检查 - 商品ID: {}, 商品名称: {}, 剩余库存: {}, 是否足够: {}",
                        baseProduct.getId(),
                        baseProduct.getName(),
                        baseOutinProduct.getResidueNum(),
                        baseOutinProduct.getResidueNum() >= 0);
                if (baseOutinProduct.getNumType() != null && "2".equals(baseOutinProduct.getNumType())) {
                    return baseOutinProduct.getResidueNum() >= 0;
                } else {
                    return baseOutinProduct.getResidueNum() >= 0;
                }
            }
        }
        return true;
    }

    @Override
    public boolean checkTotalStock(BaseOutin baseOutin, List<BaseOutinProduct> outinProductList) {
        //查询商品现有量
        List<Long> productIds = outinProductList.stream().map(BaseOutinProduct::getProductId).collect(Collectors.toList());
        ArgumentAssert.notEmpty(productIds, "无可用商品，请联系管理员");
        List<Long> warehouseIds = outinProductList.stream().map(BaseOutinProduct::getWarehouseId).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList());
        //获取所有库存数
        List<BaseWarehouse> warehouseList = baseWarehouseManager.list(Wraps.<BaseWarehouse>lbQ().eq(BaseWarehouse::getDeleteFlag, 0)
                .in(CollUtil.isNotEmpty(warehouseIds), BaseWarehouse::getId, warehouseIds)
                .eq(BaseWarehouse::getState, 1).eq(BaseWarehouse::getCreatedOrgId, ContextUtil.getCurrentCompanyId()));
        ArgumentAssert.notEmpty(warehouseList, "无可用仓库，请联系管理员");
        warehouseIds = warehouseList.stream().map(BaseWarehouse::getId).filter(ObjectUtil::isNotNull).distinct().collect(Collectors.toList());
        // 如果不是盘点, 需要校验是不是存在盘点单
        if (!ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.INVENTORY_PROFIT_IN.getCode())
                && !ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.INVENTORY_LOSS_OUT.getCode())) {
            List<BaseOutinStocktaking> stocktakingList = baseOutinStocktakingService.list(Wraps.<BaseOutinStocktaking>lbQ()
                    .eq(BaseOutinStocktaking::getDeleteFlag, 0)
                    .in(BaseOutinStocktaking::getWarehouseId, warehouseIds)
                    .eq(BaseOutinStocktaking::getState, BaseOutinStateEnum.NO_REVIEWED.getState()));
            if (CollUtil.isNotEmpty(stocktakingList)) {
                List<Long> collect = stocktakingList.stream().map(BaseOutinStocktaking::getId).collect(Collectors.toList());
                List<BaseOutinProduct> productList = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ().in(BaseOutinProduct::getStocktakingId, collect)
                        .in(BaseOutinProduct::getProductId, productIds)
                        .eq(BaseOutinProduct::getDeleteFlag, 0));
                ArgumentAssert.isTrue(CollUtil.isEmpty(productList), "当前有盘点单未完成, 请先完成盘点单");
            }
        }
        Map<Long, BaseProductStock> productStockMap = list(Wraps.<BaseProductStock>lbQ()
                .in(BaseProductStock::getWarehouseId, warehouseIds)
                .in(BaseProductStock::getProductId, productIds)).stream()
                .collect(Collectors.toMap(
                        BaseProductStock::getProductId,
                        Function.identity(),
                        (oldStock, newStock) -> {
                            // 创建新对象（避免修改原对象）
                            BaseProductStock merged = BeanUtil.copyProperties(newStock, BaseProductStock.class);
                            merged.setNum(oldStock.getNum() + newStock.getNum());
                            merged.setLockNum(oldStock.getLockNum() + newStock.getLockNum());
                            return merged;
                        }
                ));
        List<BaseProduct> baseProducts = baseProductMapper.selectList(Wraps.<BaseProduct>lbQ().in(BaseProduct::getId, productIds));
        Map<Long, BaseProduct> baseProductMap =
                CollUtil.isEmpty(baseProducts) ? MapUtil.newHashMap() :
                        baseProducts.stream().collect(Collectors.toMap(BaseProduct::getId, Function.identity()));

        //计算库存信息
        for (BaseOutinProduct baseOutinProduct : outinProductList) {
            BaseProduct baseProduct = baseProductMap.get(baseOutinProduct.getProductId());
            if (ObjectUtil.isNull(baseProduct)) {
                continue;
            }
            BaseProductStock baseProductStock = productStockMap.get(baseOutinProduct.getProductId());
            if (ObjectUtil.isNull(baseProductStock)) {
                return false;
            }
            //当前库存
            int currNum = baseProductStock.getNum() - baseProductStock.getLockNum();
            //ResidueNum
            if (baseProduct.getIsNegative() != null && baseProduct.getIsNegative()) {
                continue;
            }
            if (currNum < baseOutinProduct.getNum()) {
                return false;
            }
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean rewriteStock(List<BaseOutinProduct> outinProductList, BaseOutin baseOutin, List<BaseProductStock> baseProductStockList) {
        log.info("开始更新商品库存 - 单据类型: {}, 单据ID: {}", baseOutin.getType(), baseOutin.getId());

        // 判断是不是待审核, 待审核不需要, 改写库存量
        if (ObjectUtil.equal(baseOutin.getState(), BaseOutinStateEnum.NO_REVIEWED.getState())) {
            return true;
        }
        BaseWarehouse warehouse = baseWarehouseManager.getOneBySn();

        List<Long> productIds = outinProductList.stream().map(BaseOutinProduct::getProductId).collect(Collectors.toList());
        Map<String, BaseProductStock> productStockMap = baseProductStockList.stream()
                .collect(Collectors.toMap(k -> k.getProductId() + "_" + k.getWarehouseId(), k -> k));
        List<Long> productStockIds = productStockMap.values().stream()
                .map(BaseProductStock::getId).filter(ObjectUtil::isNotNull).collect(Collectors.toList());
        Map<Long, List<BaseProductStock>> productStockAllMap = list(Wraps.<BaseProductStock>lbQ()
//                .notIn(CollUtil.isNotEmpty(productStockIds), BaseProductStock::getId, productStockIds)
                .in(BaseProductStock::getProductId, productIds))
                .stream().collect(Collectors.groupingBy(BaseProductStock::getProductId));

        List<BaseProductStock> saveList = Lists.newArrayList();
        List<BaseProductStock> updateList = Lists.newArrayList();
        //入库存信息 计算成本价
        Map<Long, List<BaseOutinProduct>> baseOutinProductMap = MapUtil.newHashMap();
        if (ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_IN.getCode())
                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.INVENTORY_PROFIT_IN.getCode())
        ) {
            baseOutinProductMap = baseOutinProductManager.list(Wraps.<BaseOutinProduct>lbQ()
                            .inSql(BaseOutinProduct::getOutinId, "select id from base_outin where delete_flag = 0" +
                                    " and type_ in(" + String.join(",", Arrays.asList(OutinTypeEnum.PURCHASE_IN.getCode(),
                                    OutinTypeEnum.INVENTORY_PROFIT_IN.getCode())) + ")")
                            .in(BaseOutinProduct::getProductId, productIds))
                    .stream().collect(Collectors.groupingBy(BaseOutinProduct::getProductId));
        }
        for (BaseOutinProduct baseOutinProduct : outinProductList) {
            BigDecimal costPrice = baseOutinProduct.getCostPrice() == null ? BigDecimal.ZERO : baseOutinProduct.getCostPrice();
            //新增商品库存现有量
            BaseProductStock baseProductStock = productStockMap.get(baseOutinProduct.getProductId() + "_" + baseOutinProduct.getWarehouseId());
            List<BaseProductStock> productStockList = productStockAllMap.get(baseOutinProduct.getProductId());
            if (ObjectUtil.isNull(baseProductStock)) {
                baseProductStock = BaseProductStock.builder()
                        .productId(baseOutinProduct.getProductId())
                        .lockNum((ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.SELL_OUT.getCode()))
                                ? baseOutinProduct.getNum() : 0).costPrice(costPrice)
                        .warehouseId(baseOutinProduct.getWarehouseId() == null ? warehouse.getId() : baseOutinProduct.getWarehouseId())
                        .num((ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_IN.getCode())
                                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.REFUND_IN.getCode())
                                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.RETURN.getCode())
                                // 采购退货红冲单
                                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_OUT_REVERSAL_ENTRY.getCode())
                                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.OTHER_IN.getCode())
                                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.INVENTORY_PROFIT_IN.getCode())
                                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.ADJUSTMENT_IN.getCode()))
                                ? baseOutinProduct.getNum() : ((ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.SELL_OUT.getCode()))
                                ? 0 : -baseOutinProduct.getNum()))
                        .storageNum((ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_IN.getCode())
                                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.REFUND_IN.getCode())
                                // 采购退货红冲单
                                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_OUT_REVERSAL_ENTRY.getCode())
                                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.OTHER_IN.getCode())
                                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.RETURN.getCode())
                                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.INVENTORY_PROFIT_IN.getCode())
                                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.ADJUSTMENT_IN.getCode()))
                                ? baseOutinProduct.getNum() : 0)
                        .outNum((ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_IN.getCode())
                                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.REFUND_IN.getCode())
                                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.OTHER_IN.getCode())
                                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_OUT_REVERSAL_ENTRY.getCode())
                                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.RETURN.getCode())
                                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.INVENTORY_PROFIT_IN.getCode())
                                || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.ADJUSTMENT_IN.getCode()))
                                ? 0 : baseOutinProduct.getNum())
                        .createdOrgId(ContextUtil.getCurrentCompanyId())
                        .build();
                baseProductStock.setUpdatedBy(ContextUtil.getUserId());
                baseProductStock.setCreatedBy(ContextUtil.getUserId());
                baseProductStock.setCreatedTime(LocalDateTime.now());
                baseProductStock.setUpdatedTime(LocalDateTime.now());
                saveList.add(baseProductStock);
                if (ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_IN.getCode())
                        || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.REFUND_IN.getCode())
                        || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.RETURN.getCode())
                        // 采购退货红冲单
                        || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_OUT_REVERSAL_ENTRY.getCode())
                        || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.OTHER_IN.getCode())
                        || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.INVENTORY_PROFIT_IN.getCode())
                        || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.ADJUSTMENT_IN.getCode())) {
                    int sumStockNum = CollUtil.isEmpty(productStockList) ? 0 : productStockList.stream()
                            .filter(v -> ObjectUtil.isNotNull(v) && ObjectUtil.isNotNull(v.getNum()))
                            .mapToInt(BaseProductStock::getNum).sum();
                    BigDecimal oldCostPrice = baseProductStock.getCostPrice();
                    if (CollUtil.isNotEmpty(productStockList)) {
                        oldCostPrice = productStockList.get(0).getCostPrice();
                    }
                    if (baseOutinProduct.getCostPrice() == null) {
                        baseOutinProduct.setCostPrice(costPrice);
                    }
                    if (baseOutinProduct.getNum() == null) {
                        baseOutinProduct.setNum(0);
                    }
                    BigDecimal decimal = oldCostPrice.multiply(new BigDecimal(sumStockNum))
                            .add(new BigDecimal(baseOutinProduct.getNum()).multiply(baseOutinProduct.getCostPrice()));
                    if (sumStockNum + baseOutinProduct.getNum() != 0) {
                        BigDecimal bigDecimal = decimal.divide(new BigDecimal(sumStockNum + baseOutinProduct.getNum()),
                                4, RoundingMode.HALF_UP);
                        baseProductStock.setCostPrice(bigDecimal);
                        if (CollUtil.isNotEmpty(productStockList)) {
                            productStockList.forEach(k -> k.setCostPrice(bigDecimal));
                            updateList.addAll(productStockList);
                        }
                    }
                }
                continue;
            }
            if (ObjectUtil.isNull(baseProductStock.getLockNum())) {
                baseProductStock.setLockNum(0);
            }
            if (baseProductStock.getCostPrice() == null) {
                baseProductStock.setCostPrice(baseOutinProduct.getCostPrice());
            }
            int sumStockNum = CollUtil.isEmpty(productStockList) ? 0 : productStockList.stream().mapToInt(BaseProductStock::getNum).sum();
            // 记录更新前的库存状态
            log.info("库存更新前 - 商品ID: {}, 商品名称: {}, 当前库存: {}, 锁定库存: {}, 操作数量: {}, 操作类型: {}",
                    baseOutinProduct.getProductId(),
                    baseOutinProduct.getName(),
                    baseProductStock.getNum(),
                    baseProductStock.getLockNum(),
                    baseOutinProduct.getNum(),
                    baseOutinProduct.getNumType());
            if (ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_IN.getCode())
                    || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.INVENTORY_PROFIT_IN.getCode())
                    || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_OUT_REVERSAL_ENTRY.getCode())
            ) {
                if (CollUtil.isNotEmpty(baseOutinProductMap)
                        && CollUtil.isNotEmpty(baseOutinProductMap.get(baseOutinProduct.getProductId()))) {
                    //移动成本价=（（当前库存数量*当前商品成本价）+入库数量*入库价格）/(库存数量+入库数量)
                    //写入当时实际商品那个时点的库存数量（现存量+采购数量）
                    // 如果是作废, 成本价, 根据采购退货的方式计算

                    if (ObjectUtil.equal(baseOutin.getState(), BaseOutinStateEnum.INVALID.getState())) {
                        int num = sumStockNum - baseOutinProduct.getNum();
                        if (num <= 0) {
                            baseProductStock.setCostPrice(baseOutinProduct.getCostPrice());
                            if (CollUtil.isNotEmpty(productStockList)) {
                                productStockList.forEach(k -> k.setCostPrice(baseOutinProduct.getCostPrice()));
                                updateList.addAll(productStockList);
                            }
                        } else {
                            BigDecimal oldCostPrice = baseProductStock.getCostPrice();
                            if (CollUtil.isNotEmpty(productStockList)) {
                                oldCostPrice = productStockList.get(0).getCostPrice();
                            }
                            BigDecimal decimal = oldCostPrice.multiply(new BigDecimal(sumStockNum))
                                    .subtract(new BigDecimal(baseOutinProduct.getNum()).multiply(baseOutinProduct.getCostPrice()));
                            //移动成本价=（（当前库存数量*当前商品成本价）+入库数量*入库价格）/(库存数量+入库数量)
                            //写入当时实际商品那个时点的库存数量（现存量+采购数量）
                            BigDecimal bigDecimal = decimal.divide(new BigDecimal(sumStockNum - baseOutinProduct.getNum()),
                                    4, RoundingMode.HALF_UP);
                            baseProductStock.setCostPrice(bigDecimal);
                            if (CollUtil.isNotEmpty(productStockList)) {
                                productStockList.forEach(k -> k.setCostPrice(bigDecimal));
                                updateList.addAll(productStockList);
                            }
                        }
                    } else {
                        if (ObjectUtil.isNull(baseOutinProduct.getCostPrice())) {
                            baseOutinProduct.setCostPrice(BigDecimal.ZERO);
                        }
                        BigDecimal oldCostPrice = baseProductStock.getCostPrice();
                        if (CollUtil.isNotEmpty(productStockList)) {
                            oldCostPrice = productStockList.get(0).getCostPrice();
                        }
                        //移动成本价=（（当前库存数量*当前商品成本价）+入库数量*入库价格）/(库存数量+入库数量)
                        //写入当时实际商品那个时点的库存数量（现存量+采购数量）
                        BigDecimal decimal = oldCostPrice.multiply(new BigDecimal(sumStockNum))
                                .add(new BigDecimal(baseOutinProduct.getNum()).multiply(baseOutinProduct.getCostPrice()));
                        if (sumStockNum + baseOutinProduct.getNum() != 0) {
                            BigDecimal bigDecimal = decimal.divide(new BigDecimal(sumStockNum + baseOutinProduct.getNum()),
                                    4, RoundingMode.HALF_UP);
                            baseProductStock.setCostPrice(bigDecimal);
                            if (CollUtil.isNotEmpty(productStockList)) {
                                productStockList.forEach(k -> k.setCostPrice(bigDecimal));
                                updateList.addAll(productStockList);
                            }
                        }
                    }
                }
            }
            if (ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_OUT.getCode())
                    || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_IN_REVERSAL_ENTRY.getCode())) {
                // 作废
                if (ObjectUtil.equal(baseOutin.getState(), BaseOutinStateEnum.INVALID.getState())) {
                    BigDecimal decimal = baseProductStock.getCostPrice().multiply(new BigDecimal(sumStockNum))
                            .add(new BigDecimal(baseOutinProduct.getNum()).multiply(baseOutinProduct.getCostPrice()));
                    if (sumStockNum + baseOutinProduct.getNum() != 0) {
                        BigDecimal bigDecimal = decimal.divide(new BigDecimal(sumStockNum + baseOutinProduct.getNum()),
                                4, RoundingMode.HALF_UP);
                        baseProductStock.setCostPrice(bigDecimal);
                        if (CollUtil.isNotEmpty(productStockList)) {
                            productStockList.forEach(k -> k.setCostPrice(bigDecimal));
                            updateList.addAll(productStockList);
                        }
                    }
                } else {
                    int num = sumStockNum - baseOutinProduct.getNum();
                    if (num <= 0) {
                        baseProductStock.setCostPrice(baseOutinProduct.getCostPrice());
                        if (CollUtil.isNotEmpty(productStockList)) {
                            productStockList.forEach(k -> k.setCostPrice(baseOutinProduct.getCostPrice()));
                            updateList.addAll(productStockList);
                        }
                    } else {
                        BigDecimal decimal = baseProductStock.getCostPrice().multiply(new BigDecimal(sumStockNum))
                                .subtract(new BigDecimal(baseOutinProduct.getNum()).multiply(baseOutinProduct.getCostPrice()));
                        //移动成本价=（（当前库存数量*当前商品成本价）-入库数量*入库价格）/(库存数量+入库数量)
                        //写入当时实际商品那个时点的库存数量（现存量+采购数量）
                        BigDecimal bigDecimal = decimal.divide(new BigDecimal(sumStockNum - baseOutinProduct.getNum()),
                                4, RoundingMode.HALF_UP);
                        baseProductStock.setCostPrice(bigDecimal);
                        if (CollUtil.isNotEmpty(productStockList)) {
                            productStockList.forEach(k -> k.setCostPrice(bigDecimal));
                            updateList.addAll(productStockList);
                        }
                    }
                }
            }
            //更新商品库存现有量
            if ((ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_IN.getCode())
                    || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.INVENTORY_PROFIT_IN.getCode())
                    || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.PURCHASE_OUT_REVERSAL_ENTRY.getCode())
                    || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.REFUND_IN.getCode())
                    || ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.ADJUSTMENT_IN.getCode()))) {
                // 判断是否是作废
                if (ObjectUtil.equal(baseOutin.getState(), BaseOutinStateEnum.INVALID.getState())) {
                    if (baseProductStock.getStorageNum() == null) {
                        baseProductStock.setStorageNum(-baseProductStock.getNum());
                    }
                    baseProductStock.setStorageNum(baseProductStock.getStorageNum() - baseOutinProduct.getNum());
                    baseProductStock.setNum(baseProductStock.getNum() - baseOutinProduct.getNum());
                } else {
                    if (baseProductStock.getStorageNum() == null) {
                        baseProductStock.setStorageNum(baseProductStock.getNum());
                    }
                    baseProductStock.setStorageNum(baseProductStock.getStorageNum() + baseOutinProduct.getNum());
                    baseProductStock.setNum(baseProductStock.getNum() + baseOutinProduct.getNum());
                }
                baseOutinProduct.setCostPrice(costPrice);
            } else if (ObjectUtil.equal(baseOutin.getType(), OutinTypeEnum.ADJUSTMENT_OUT.getCode())) {
                if (ObjectUtil.equal(baseOutin.getState(), BaseOutinStateEnum.INVALID.getState())) {
                    baseProductStock.setOutNum(baseProductStock.getOutNum() - baseOutinProduct.getNum());
                    baseProductStock.setNum(baseProductStock.getNum() + baseOutinProduct.getNum());
                } else {
                    baseProductStock.setOutNum(baseProductStock.getOutNum() + baseOutinProduct.getNum());
                    baseProductStock.setNum(baseProductStock.getNum() - baseOutinProduct.getNum());
                }
            } else {
                baseOutinProduct.setCostPrice(costPrice);
                if (baseProductStock.getOutNum() == null) {
                    baseProductStock.setOutNum(0);
                }

                if (baseOutin.getIsLockNum() == null || baseOutin.getIsLockNum()) {
                    if ("2".equals(baseOutinProduct.getNumType())) {
                        baseProductStock.setLockNum(baseProductStock.getLockNum() + baseOutinProduct.getNum());
                    } else {
                        baseProductStock.setLockNum(baseProductStock.getLockNum() - baseOutinProduct.getNum());
                    }
                }
                if (baseOutin.getIsNum() != null && baseOutin.getIsNum()) {
                    if (baseOutin.getIsLockNum() != null && !baseOutin.getIsLockNum()) {
                        if ("2".equals(baseOutinProduct.getNumType())) {
                            baseProductStock.setNum(baseProductStock.getNum() - baseOutinProduct.getNum());
                        } else {
                            baseProductStock.setNum(baseProductStock.getNum() + baseOutinProduct.getNum());
                        }
                    }
                    if ("2".equals(baseOutinProduct.getNumType())) {
                        baseProductStock.setOutNum(baseProductStock.getOutNum() + baseOutinProduct.getNum());
                    } else {
                        baseProductStock.setOutNum(baseProductStock.getOutNum() - baseOutinProduct.getNum());
                    }
                }
            }
            baseProductStock.setUpdatedBy(ContextUtil.getUserId());
            baseProductStock.setUpdatedTime(LocalDateTime.now());
            updateList.add(baseProductStock);

            // 记录更新后的库存状态
            log.info("库存更新后 - 商品ID: {}, 商品名称: {}, 当前库存: {}, 锁定库存: {}, 剩余库存: {}",
                    baseOutinProduct.getProductId(),
                    baseOutinProduct.getName(),
                    baseProductStock.getNum(),
                    baseProductStock.getLockNum(),
                    baseProductStock.getNum() - baseProductStock.getLockNum());
        }
        if (CollUtil.isNotEmpty(saveList)) {
            saveBatch(saveList);
        }
        if (CollUtil.isNotEmpty(updateList)) {
            updateBatchById(updateList);
            // 更新其他的成本价
            for (BaseProductStock baseProductStock : updateList) {
                update(BaseProductStock.builder().costPrice(baseProductStock.getCostPrice()).build(),
                        Wraps.<BaseProductStock>lbU()
                                .eq(BaseProductStock::getProductId, baseProductStock.getProductId())
                                .eq(SuperEntity::getDeleteFlag, 0).ne(SuperEntity::getId, baseProductStock.getId()));
            }
        }
        return true;
    }

    @Override
    public BaseProductStock getByProductId(Long productId) {
        BaseWarehouse baseWarehouse = baseWarehouseManager.getOneBySn();
        ArgumentAssert.notNull(baseWarehouse, "未绑定销售仓库,请绑定后重试");
        return getOne(Wraps.<BaseProductStock>lbQ().eq(BaseProductStock::getDeleteFlag, 0)
                .eq(BaseProductStock::getProductId, productId)
                .eq(BaseProductStock::getWarehouseId, baseWarehouse.getId())
                .orderByAsc(BaseProductStock::getCreatedTime).last("limit 1"));
    }

    @Override
    public List<BasePrintHandoverStockResultVO> productStockPrintList(ProductStockPrintQuery params) {
        return baseMapper.productStockPrintList(params);
    }
}


