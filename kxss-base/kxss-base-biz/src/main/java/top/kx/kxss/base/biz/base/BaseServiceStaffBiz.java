package top.kx.kxss.base.biz.base;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.base.R;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.exception.BizException;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.CollHelper;
import top.kx.kxss.app.cash.PosCashApi;
import top.kx.kxss.app.query.TenantOrgUserConsumeQuery;
import top.kx.kxss.base.entity.employee.BaseEmployeeBanner;
import top.kx.kxss.base.entity.employee.BaseEmployeeTags;
import top.kx.kxss.base.entity.service.BaseServicePersonal;
import top.kx.kxss.base.entity.service.BaseServiceStaff;
import top.kx.kxss.base.entity.system.BaseRole;
import top.kx.kxss.base.entity.user.BaseEmployeeOrgRel;
import top.kx.kxss.base.entity.user.invite.BaseEmployeeInviteBatch;
import top.kx.kxss.base.entity.user.invite.BaseEmployeeInviteBatchDetail;
import top.kx.kxss.base.manager.service.BaseServicePersonalManager;
import top.kx.kxss.base.service.employee.BaseEmployeeBannerService;
import top.kx.kxss.base.service.employee.BaseEmployeeTagsService;
import top.kx.kxss.base.service.service.BaseServiceStaffService;
import top.kx.kxss.base.service.system.BaseRoleService;
import top.kx.kxss.base.service.user.BaseEmployeeOrgRelService;
import top.kx.kxss.base.service.user.BaseEmployeeRoleRelService;
import top.kx.kxss.base.vo.query.service.BaseServiceStaffPageQuery;
import top.kx.kxss.base.vo.result.employee.BaseEmployeeBannerResultVO;
import top.kx.kxss.base.vo.result.employee.BaseEmployeeTagsResultVO;
import top.kx.kxss.base.vo.result.service.BaseServiceStaffResultVO;
import top.kx.kxss.base.vo.save.employee.BaseEmployeeBannerSaveVO;
import top.kx.kxss.base.vo.save.employee.BaseEmployeeTagsSaveVO;
import top.kx.kxss.base.vo.save.service.BaseServiceStaffSaveVO;
import top.kx.kxss.base.vo.save.user.BaseEmployeeOrgRelSaveVO;
import top.kx.kxss.base.vo.save.user.BaseEmployeeRoleRelSaveVO;
import top.kx.kxss.base.vo.update.service.BaseServiceStaffUpdateVO;
import top.kx.kxss.common.constant.BizConstant;
import top.kx.kxss.common.constant.RabbitMqConstant;
import top.kx.kxss.common.constant.RoleConstant;
import top.kx.kxss.file.api.FileApi;
import top.kx.kxss.file.entity.File;
import top.kx.kxss.file.service.FileService;
import top.kx.kxss.model.entity.system.SysUser;
import top.kx.kxss.model.enumeration.base.ActiveStatusEnum;
import top.kx.kxss.model.enumeration.base.PostionStatusEnum;
import top.kx.kxss.model.enumeration.base.TableStatus;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.entity.tenant.DefUserTenantRel;
import top.kx.kxss.system.service.tenant.DefUserService;
import top.kx.kxss.system.service.tenant.DefUserTenantRelService;
import top.kx.kxss.system.vo.query.tenant.DefUserPageQuery;
import top.kx.kxss.system.vo.save.tenant.DefTenantBindUserVO;
import top.kx.kxss.system.vo.save.tenant.DefUserSaveVO;
import top.kx.kxss.system.vo.update.tenant.DefUserUpdateVO;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 员工大业务层
 *
 * <AUTHOR>
 * @date 2021/10/22 10:37
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class BaseServiceStaffBiz {
    private final BaseServiceStaffService baseServiceStaffService;
    private final BaseEmployeeOrgRelService baseEmployeeOrgRelService;
    private final BaseEmployeeRoleRelService baseEmployeeRoleRelService;
    private final DefUserService defUserService;
    private final DefUserTenantRelService defUserTenantRelService;
    private final BaseServicePersonalManager baseServicePersonalManager;
    private final FileService fileService;
    private final PosCashApi posCashApi;
    private final BaseEmployeeBannerService baseEmployeeBannerService;
    private final FileApi fileApi;
    private final RabbitTemplate template;
    private final BaseRoleService baseRoleService;

    @Autowired
    private BaseEmployeeTagsService baseEmployeeTagsService;
    @Autowired
    private EchoService echoService;

    /**
     * 根据员工ID 查询员工、用户和他所在的机构 信息
     *
     * @param employeeId 员工ID
     * @return top.kx.kxss.base.vo.result.user.BaseServiceStaffResultVO
     * <AUTHOR>
     * @date 2022/10/28 12:13 AM
     * @create [2022/10/28 12:13 AM ] [tangyh] [初始创建]
     */
    public BaseServiceStaffResultVO getEmployeeUserById(Long employeeId) {
        // 租户库
        BaseServiceStaff employee = baseServiceStaffService.getById(employeeId);
        if (employee == null) {
            return null;
        }
        // def
        DefUserTenantRel utr = defUserTenantRelService.getById(employeeId);
        if (utr == null) {
            return null;
        }
        // 员工信息
        BaseServiceStaffResultVO resultVO = new BaseServiceStaffResultVO();
        BeanUtil.copyProperties(employee, resultVO);

        // 机构信息
        resultVO.setOrgIdList(baseEmployeeOrgRelService.findOrgIdListByEmployeeId(employeeId));

        // 用户信息
        DefUser defUser = defUserService.getById(employee.getUserId());
        if (ObjectUtil.isNull(defUser)) {
            defUser = new DefUser();
        }
        resultVO.setUsername(defUser.getUsername());
        resultVO.setMobile(defUser.getMobile());
        resultVO.setSex(defUser.getSex());
        resultVO.setEducation(defUser.getEducation());
        resultVO.setNation(defUser.getNation());
        resultVO.setSkill(defUser.getSkill());
        resultVO.setWorkDescribe(defUser.getWorkDescribe());
        // 机构信息
        resultVO.setOrgIdList(baseEmployeeOrgRelService.findOrgIdListByEmployeeId(employeeId));
        // 机构信息
        resultVO.setRoleIdList(baseEmployeeRoleRelService.findRoleIdListByEmployeeId(employeeId));
        // 服务信息
        resultVO.setServiceList(baseServicePersonalManager.findServiceListByEmployeeId(employeeId));
        if (ObjectUtil.isNotNull(resultVO.getPhotoId())) {
            resultVO.setPhotoIdFile(fileService.getById(resultVO.getPhotoId()));
        }
        if (ObjectUtil.isNotNull(resultVO.getHealthCodeId())) {
            resultVO.setHealthCodeIdFile(fileService.getById(resultVO.getHealthCodeId()));
        }

        // 标签及分数
        List<BaseEmployeeTags> labelList = baseEmployeeTagsService.list(Wraps.<BaseEmployeeTags>lbQ().eq(BaseEmployeeTags::getDeleteFlag, 0).eq(BaseEmployeeTags::getEmployeeId, employeeId));
        if (CollUtil.isNotEmpty(labelList)) {
            List<BaseEmployeeTagsResultVO> beanList = BeanPlusUtil.toBeanList(labelList, BaseEmployeeTagsResultVO.class);
            echoService.action(beanList);
            resultVO.setTagsResultVOList(beanList);
        }

        // 所有的图片信息
        List<BaseEmployeeBanner> BaseEmployeeBannerList = baseEmployeeBannerService.list(Wraps.<BaseEmployeeBanner>lbQ().eq(BaseEmployeeBanner::getEmployeeId, employee.getId()).eq(SuperEntity::getDeleteFlag, 0));
        if (CollUtil.isNotEmpty(BaseEmployeeBannerList)) {
            List<BaseEmployeeBannerResultVO> resultVOList = BeanPlusUtil.toBeanList(BaseEmployeeBannerList, BaseEmployeeBannerResultVO.class);
            resultVO.setBannerList(resultVOList);
        }

        return resultVO;
    }

    /**
     * 删除员工
     *
     * @param ids 员工ID
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/10/28 12:14 AM
     * @create [2022/10/28 12:14 AM ] [tangyh] [初始创建]
     */
    @GlobalTransactional
    public Boolean delete(List<Long> ids) {
        //判断订单是否存在套餐
        R<List<String>> booleanR = posCashApi.checkEmpIsUseCode(ids);
        List<String> data = booleanR.getData();
        if (CollUtil.isNotEmpty(data)) {
            //data取后8位组成string字符串用逗号隔开
            String code = CollUtil.join(data.stream().map(item -> item.substring(item.length() - 8)).collect(Collectors.toList()), ",");
            throw new BizException("【".concat(code).concat("】正在使用中，无法删除"));
        }
//        // 删除默认库的 员工
//        defUserTenantRelService.removeByIds(ids);
//        baseEmployeeOrgRelService.removeByEmployeeIds(ids);
//        baseEmployeeRoleRelService.removeByEmployeeIds(ids);
        List<BaseServiceStaff> employeeList = baseServiceStaffService.list(Wraps.<BaseServiceStaff>lbQ()
                .in(BaseServiceStaff::getId, ids));
        boolean b = baseServiceStaffService.deleteByIds(ids);
        if (b) {
            for (BaseServiceStaff baseEmployee : employeeList) {
                if (ObjectUtil.isNull(baseEmployee.getUserId())) {
                    continue;
                }
                template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.TENANT_ORG_USER,
                        JSON.toJSONString(TenantOrgUserConsumeQuery.builder()
                                .tenantId(ContextUtil.getTenantId())
                                .employeeId(null)
                                .userId(baseEmployee.getUserId())
                                .build()));
            }
            List<Long> userIds = employeeList.stream().map(BaseServiceStaff::getUserId).collect(Collectors.toList());
            for (DefUser defUser : defUserService.listByIds(userIds)) {
                //清除账号登录
                template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.FORCE_LOGOUT,
                        defUser.getMobile());
            }
        }
        return b;
        // 删除基础库的 员工
    }


    /**
     * 保存员工信息
     *
     * @param saveVO saveVO
     * @return top.kx.kxss.base.entity.user.BaseServiceStaff
     * <AUTHOR>
     * @date 2022/10/28 12:15 AM
     * @create [2022/10/28 12:15 AM ] [tangyh] [初始创建]
     */
    @GlobalTransactional
    public BaseServiceStaff save(BaseServiceStaffSaveVO saveVO) {
//        boolean existDefUser = defUserService.checkMobile(saveVO.getMobile(), null);
//        if (existDefUser) {
//            throw new BizException("手机号已被注册,请重新输入手机号 或 直接邀请它加入贵公司。");
//        }
        DefUser entity = defUserService.getUserByMobile(saveVO.getMobile());
        if (ObjectUtil.isNotNull(entity)) {
            List<BaseServiceStaff> employeeList = baseServiceStaffService.list(Wraps.<BaseServiceStaff>lbQ()
                    .eq(BaseServiceStaff::getDeleteFlag, 0)
                    .eq(BaseServiceStaff::getUserId, entity.getId()).last("limit 1"));
            if (CollUtil.isNotEmpty(employeeList)) {
                List<BaseEmployeeOrgRel> employeeOrgRelList = baseEmployeeOrgRelService.list(Wraps.<BaseEmployeeOrgRel>lbQ()
                        .eq(BaseEmployeeOrgRel::getOrgId, ContextUtil.getCurrentCompanyId())
                        .eq(BaseEmployeeOrgRel::getDeleteFlag, 0)
                        .eq(BaseEmployeeOrgRel::getEmployeeId, employeeList.get(0).getId()));
                if (CollUtil.isNotEmpty(employeeOrgRelList)) {
                    ArgumentAssert.isFalse(ObjectUtil.equals(1, 1), "已存在手机号");
                } else {
                    //保存员工和门店关系
                    BaseEmployeeOrgRelSaveVO employeeOrgSaveVO = BaseEmployeeOrgRelSaveVO.builder()
                            .employeeId(employeeList.get(0).getId())
                            .orgId(ContextUtil.getCurrentCompanyId())
                            .build();
                    baseEmployeeOrgRelService.save(employeeOrgSaveVO);
                    return employeeList.get(0);
                }
            }
        }
        String username = StrUtil.isBlank(saveVO.getUsername()) ? IdUtil.simpleUUID() : saveVO.getUsername();
        // 保存默认库的 用户表 和 员工表
        DefUserSaveVO userSaveVO = DefUserSaveVO.builder().username(username).nickName(saveVO.getRealName()).build();
        BeanUtil.copyProperties(saveVO, userSaveVO);
        userSaveVO.setWorkDescribe(saveVO.getWorkDescribe());
        userSaveVO.setSex(saveVO.getSex());
        DefUserTenantRel defUserTenantRel = defUserService.saveUserAndEmployee(ContextUtil.getTenantId(), userSaveVO);
        // 保存 基础库的员工表
        boolean mobileUser = baseServiceStaffService.getByUserId(defUserTenantRel.getUserId());
        if (mobileUser) {
            throw new BizException("手机号已存在，请联系管理员");
        }
        // 保存 基础库的员工表
        saveVO.setUserId(defUserTenantRel.getUserId());
        saveVO.setId(defUserTenantRel.getId());
        saveVO.setActiveStatus(ActiveStatusEnum.ACTIVATED.getCode());
        saveVO.setPositionStatus(PostionStatusEnum.POSED.getCode());
        saveVO.setPositionStatus(PostionStatusEnum.POSED.getCode());
        saveVO.setIsDefault(true);
        saveVO.setStatus(TableStatus.UNUSED.getCode());
        saveVO.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        saveVO.setType("2");
        saveVO.setIsMobileUse(saveVO.getIsMobileUse());
        //保存默认角色
        BaseServiceStaff serviceStaff = baseServiceStaffService.save(saveVO);
        BaseRole baseRole = baseRoleService.getByKey("SERVICE_STAFF");
        ArgumentAssert.notNull(baseRole, "未找到默认助教角色");
        //服务人员角色ID
        BaseEmployeeRoleRelSaveVO employeeRoleSaveVO = BaseEmployeeRoleRelSaveVO.builder()
                .flag(true)
                .employeeId(serviceStaff.getId())
                .roleIdList(Collections.singletonList(baseRole.getId()))
                .build();
        baseServiceStaffService.saveEmployeeRole(employeeRoleSaveVO);
        //服务人员绑定门店
        Long orgId = ContextUtil.getCurrentCompanyId();
        //服务人员门店ID
        BaseEmployeeOrgRelSaveVO employeeOrgSaveVO = BaseEmployeeOrgRelSaveVO.builder()
                .employeeId(serviceStaff.getId())
                .orgIdList(Collections.singletonList(orgId))
                .build();
        baseServiceStaffService.saveEmployeeOrg(employeeOrgSaveVO);

        // 服务人员标签
        if (CollUtil.isNotEmpty(saveVO.getTagsList())) {
            List<BaseEmployeeTagsSaveVO> labelSaveVOList = saveVO.getTagsList();
            labelSaveVOList.forEach(s -> {
                s.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                s.setEmployeeId(serviceStaff.getId());
            });
            baseEmployeeTagsService.saveBatch(BeanPlusUtil.toBeanList(labelSaveVOList, BaseEmployeeTags.class));
        }
        saveEmployeeBanner(saveVO.getBannerList(), serviceStaff.getId());

        //新增服务信息
        if (CollUtil.isNotEmpty(saveVO.getServiceList())) {
            baseServicePersonalManager.saveBatch(saveVO.getServiceList().stream().map(v -> {
                return BaseServicePersonal.builder()
                        .serviceId(v)
                        .employeeId(serviceStaff.getId())
                        .createdOrgId(orgId)
                        .build();
            }).collect(Collectors.toList()));
        }

        return serviceStaff;
    }

    private void saveEmployeeBanner(List<BaseEmployeeBannerSaveVO> saveVO, Long serviceStaff) {
        baseEmployeeBannerService.remove(Wraps.<BaseEmployeeBanner>lbQ().eq(BaseEmployeeBanner::getEmployeeId, serviceStaff));
        if (CollUtil.isNotEmpty(saveVO)) {
            List<BaseEmployeeBanner> balanceList = new ArrayList<>();
            for (BaseEmployeeBannerSaveVO fileParamVO : saveVO) {
                balanceList.add(BaseEmployeeBanner.builder()
                        .employeeId(serviceStaff)
                        .fileId(fileParamVO.getFileId())
                        .fileUrl(fileParamVO.getFileUrl())
                        .type(fileParamVO.getType())
                        .createdOrgId(ContextUtil.getCurrentCompanyId())
                        .build());
            }
            if (CollUtil.isNotEmpty(balanceList)) {
                baseEmployeeBannerService.saveBatch(balanceList);
            }
        }
    }


    /**
     * 分页查员工数据
     *
     * @param params 参数
     * @return IPage
     * <AUTHOR>
     * @date 2022/10/28 12:19 AM
     * @create [2022/10/28 12:19 AM ] [tangyh] [初始创建]
     */
    public IPage<BaseServiceStaffResultVO> findPageResultVO(PageParams<BaseServiceStaffPageQuery> params) {
        params.getModel().setScope(BizConstant.SCOPE_BIND);
        BaseServiceStaffPageQuery pageQuery = params.getModel();
        List<Long> userIdList;
        if (!StrUtil.isAllEmpty(pageQuery.getMobile(), pageQuery.getEmail(), pageQuery.getUsername(), pageQuery.getIdCard())) {
            userIdList = defUserService.findUserIdList(BeanUtil.toBean(pageQuery, DefUserPageQuery.class));
            if (CollUtil.isEmpty(userIdList)) {
                return new Page<>(params.getCurrent(), params.getSize());
            }
            params.getModel().setUserIdList(userIdList);
        }
        if (CollUtil.isEmpty(params.getModel().getOrgIdList())) {
            params.getModel().setOrgIdList(Lists.newArrayList());
        }
        params.getModel().getOrgIdList().add(ContextUtil.getCurrentCompanyId());
        if (StrUtil.isNotBlank(pageQuery.getRealName())) {
            List<Long> list = defUserService.list(Wraps.<DefUser>lbQ().like(DefUser::getMobile, pageQuery.getRealName()))
                    .stream().map(DefUser::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(list)) {
                params.getModel().setUserMobileList(list);
            }
        }
//        DataScopeHelper.startDataScope("e");
        IPage<BaseServiceStaffResultVO> pageResultVO = baseServiceStaffService.findPageResultVO(params);

        if (CollUtil.isNotEmpty(pageResultVO.getRecords())) {
            List<Long> userIds = pageResultVO.getRecords().stream().map(BaseServiceStaffResultVO::getUserId).collect(Collectors.toList());

            List<DefUser> defUsers = defUserService.listByIds(userIds);
            List<SysUser> userResultVos = BeanUtil.copyToList(defUsers, SysUser.class);
            ImmutableMap<Long, SysUser> map = CollHelper.uniqueIndex(userResultVos, SysUser::getId, user -> user);

            Map<Long, BaseServiceStaffResultVO> staffResultVOMap = pageResultVO.getRecords()
                    .stream().collect(Collectors.toMap(BaseServiceStaffResultVO::getUserId, v -> v));

            List<BaseServicePersonal> list = baseServicePersonalManager
                    .list(Wraps.<BaseServicePersonal>lbQ()
                            .eq(BaseServicePersonal::getDeleteFlag, 0)
                            .eq(BaseServicePersonal::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                            .in(BaseServicePersonal::getEmployeeId, pageResultVO.getRecords()
                                    .stream().map(BaseServiceStaffResultVO::getId).collect(Collectors.toList())));
            Map<Long, List<BaseServicePersonal>> collect =
                    list.stream().collect(Collectors.groupingBy(BaseServicePersonal::getEmployeeId));

            List<Long> photoIds = pageResultVO.getRecords().stream().map(BaseServiceStaffResultVO::getPhotoId)
                    .filter(ObjectUtil::isNotNull)
                    .collect(Collectors.toList());
            Map<Long, File> fileMap = CollectionUtil.isNotEmpty(photoIds) ? fileService.list(Wraps.<File>lbQ().in(File::getId, photoIds))
                    .stream().collect(Collectors.toMap(File::getId, k -> k)) : new HashMap<>();

            pageResultVO.getRecords().forEach(item -> {
                SysUser sysUser = map.get(item.getUserId());
                if (ObjectUtil.isNull(sysUser)) {
                    sysUser = new SysUser();
                }
                sysUser.setEmployeeId(ObjectUtil.isNull(staffResultVOMap) ||
                        ObjectUtil.isNull(staffResultVOMap.get(sysUser.getId()))
                        ? null : staffResultVOMap.get(sysUser.getId()).getId());

                if (ObjectUtil.isNotNull(item.getPhotoId())) {
                    item.setPhotoId(item.getPhotoId());
                    if (ObjectUtil.isNotNull(fileMap) &&
                            ObjectUtil.isNotNull(fileMap.get(item.getPhotoId()))) {
                        item.setPhotoIdFile(fileMap.get(item.getPhotoId()));
                        item.setAvatar(fileMap.get(item.getPhotoId()).getUrl());
                    }
                }
                item.setUsername(sysUser.getUsername());
                item.setMobile(sysUser.getMobile());
                item.setSex(sysUser.getSex());
                item.setEducation(sysUser.getEducation());
                item.setIdNum(sysUser.getIdCard());
                item.setSkill(sysUser.getSkill());
                item.setWorkDescribe(sysUser.getWorkDescribe());

                item.setServiceList(collect.get(sysUser.getEmployeeId())
                        != null ? collect.get(sysUser.getEmployeeId()).stream().map(BaseServicePersonal::getServiceId).collect(Collectors.toList())
                        : Lists.newArrayList());
            });
        }

        return pageResultVO;
    }

    /**
     * 将用户绑定为租户管理员
     *
     * @param param param
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/10/28 12:21 AM
     * @create [2022/10/28 12:21 AM ] [tangyh] [初始创建]
     */
    @GlobalTransactional
    public Boolean bindTenantAdmin(DefTenantBindUserVO param) {
        List<Long> employeeIdList = findEmployeeIdList(param);
        return baseEmployeeRoleRelService.bindRole(employeeIdList, RoleConstant.TENANT_ADMIN);
    }

    private List<Long> findEmployeeIdList(DefTenantBindUserVO param) {
        List<DefUserTenantRel> defEmployeeList = defUserTenantRelService.list(Wraps.<DefUserTenantRel>lbQ().eq(DefUserTenantRel::getTenantId, param.getTenantId()).in(DefUserTenantRel::getUserId, param.getUserIdList()));
        ArgumentAssert.notEmpty(defEmployeeList, "对不起，您选择的用户不是该企业的员工");
        List<Long> employeeIdList = defEmployeeList.stream().map(DefUserTenantRel::getId).collect(Collectors.toList());
        // 保存到指定租户的 base库的员工 + 租户管理员角色
        ContextUtil.setTenantBasePoolName(param.getTenantId());
        return employeeIdList;
    }

    /**
     * 在运营平台 将用户解绑为某个租户的 租户管理员
     *
     * @param param param
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/10/28 12:21 AM
     * @create [2022/10/28 12:21 AM ] [tangyh] [初始创建]
     */
    @GlobalTransactional
    public Boolean unBindTenantAdmin(DefTenantBindUserVO param) {
        List<Long> employeeIdList = findEmployeeIdList(param);
        return baseEmployeeRoleRelService.unBindRole(employeeIdList, RoleConstant.TENANT_ADMIN);
    }

    /**
     * 将用户绑定某个租户的员工
     *
     * @param param param
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/10/28 12:21 AM
     * @create [2022/10/28 12:21 AM ] [tangyh] [初始创建]
     */
    @GlobalTransactional
    public Boolean bindUser(DefTenantBindUserVO param) {
        List<BaseServiceStaff> baseEmployeeList = findEmployeeList(param);
        return baseServiceStaffService.saveBatchBaseServiceStaffAndRole(baseEmployeeList);
    }

    private List<BaseServiceStaff> findEmployeeList(DefTenantBindUserVO param) {
        Long tenantId = param.getTenantId();
        ArgumentAssert.notNull(tenantId, "请选择租户");


        List<DefUser> defUsers = defUserService.listByIds(param.getUserIdList());
        ArgumentAssert.notEmpty(defUsers, "请选择用户");
        long employeeCount = defUserTenantRelService.count(Wraps.<DefUserTenantRel>lbQ().eq(DefUserTenantRel::getTenantId, tenantId).in(DefUserTenantRel::getUserId, param.getUserIdList()));
        ArgumentAssert.isFalse(employeeCount > 0, "对不起，您选择的用户已经是该企业的员工");

        // 保存def库的员工
        List<DefUserTenantRel> employeeList = param.getUserIdList().stream().map(userId -> {
            DefUserTenantRel employee = new DefUserTenantRel();
            employee.setUserId(userId);
            employee.setTenantId(tenantId);
            employee.setState(true);
            employee.setIsDefault(false);
            return employee;
        }).collect(Collectors.toList());
        defUserTenantRelService.saveBatch(employeeList);

        ImmutableMap<Long, String> userMap = CollHelper.uniqueIndex(defUsers, DefUser::getId, DefUser::getNickName);
        List<BaseServiceStaff> baseEmployeeList = BeanUtil.copyToList(employeeList, BaseServiceStaff.class);
        baseEmployeeList.forEach(employee -> {
            employee.setActiveStatus(ActiveStatusEnum.ACTIVATED.getCode());
            employee.setState(true);
            employee.setRealName(userMap.get(employee.getUserId()));
        });

        // 保存到指定租户的 base库的员工 + 租户管理员角色
        ContextUtil.setTenantBasePoolName(tenantId);
        return baseEmployeeList;
    }

    /**
     * 邀请某个用户加入 他自己所在的租户
     *
     * @param param param
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/10/28 12:22 AM
     * @create [2022/10/28 12:22 AM ] [tangyh] [初始创建]
     */
    @GlobalTransactional
    public Boolean invitationUser(DefTenantBindUserVO param) {
        Long tenantId = ContextUtil.getTenantId();
        param.setTenantId(tenantId);
        List<BaseServiceStaff> baseEmployeeList = findEmployeeList(param);
        return baseServiceStaffService.saveBatch(baseEmployeeList);
    }

    /**
     * 在基础平台 将用户取消保定到自己所在的企业
     *
     * @param param param
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/10/28 12:22 AM
     * @create [2022/10/28 12:22 AM ] [tangyh] [初始创建]
     * @update [2022/10/28 12:22 AM ] [tangyh] [变更描述]
     */
    @GlobalTransactional
    public Boolean unInvitationUser(DefTenantBindUserVO param) {
        Long tenantId = ContextUtil.getTenantId();
        List<Long> employeeIdList = findEmployeeIdList(param, tenantId);
        List<BaseServiceStaff> employeeList = baseServiceStaffService.list(Wraps.<BaseServiceStaff>lbQ()
                .in(BaseServiceStaff::getId, employeeIdList));
        boolean b = baseServiceStaffService.removeByIds(employeeIdList);
        if (b) {
            for (BaseServiceStaff baseEmployee : employeeList) {
                if (ObjectUtil.isNull(baseEmployee.getUserId())) {
                    continue;
                }
                template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.TENANT_ORG_USER,
                        JSON.toJSONString(TenantOrgUserConsumeQuery.builder()
                                .tenantId(ContextUtil.getTenantId())
                                .employeeId(null)
                                .userId(baseEmployee.getUserId())
                                .build()));
            }
        }
        return b;
    }

    private List<Long> findEmployeeIdList(DefTenantBindUserVO param, Long tenantId) {
        List<DefUser> defUsers = defUserService.listByIds(param.getUserIdList());
        ArgumentAssert.notEmpty(defUsers, "请选择用户");
        List<DefUserTenantRel> defEmployeeList = defUserTenantRelService.list(Wraps.<DefUserTenantRel>lbQ().eq(DefUserTenantRel::getTenantId, tenantId).in(DefUserTenantRel::getUserId, param.getUserIdList()));
        ArgumentAssert.notEmpty(defEmployeeList, "对不起，您选择的用户不是该企业的员工");
        List<Long> employeeIdList = defEmployeeList.stream().map(DefUserTenantRel::getId).collect(Collectors.toList());
        defUserTenantRelService.removeByIds(employeeIdList);
        return employeeIdList;
    }

    /**
     * 在运营平台 将用户取消绑定到某个企业
     *
     * @param param param
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/10/28 12:23 AM
     * @create [2022/10/28 12:23 AM ] [tangyh] [初始创建]
     */
    @GlobalTransactional
    public Boolean unBindUser(DefTenantBindUserVO param) {
        Long tenantId = param.getTenantId();
        ArgumentAssert.notNull(tenantId, "请选择租户");

        // 演示环境专用标识，用于WriteInterceptor拦截器判断演示环境需要禁止用户执行sql，若您无需搭建演示环境，可以删除下面一行代码
        ContextUtil.setStop();

        List<Long> employeeIdList = findEmployeeIdList(param, tenantId);

        ContextUtil.setTenantBasePoolName(tenantId);
        List<BaseServiceStaff> employeeList = baseServiceStaffService.list(Wraps.<BaseServiceStaff>lbQ()
                .in(BaseServiceStaff::getId, employeeIdList));
        boolean b = baseServiceStaffService.removeByIds(employeeIdList);
        if (b) {
            for (BaseServiceStaff baseEmployee : employeeList) {
                if (ObjectUtil.isNull(baseEmployee.getUserId())) {
                    continue;
                }
                template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.TENANT_ORG_USER,
                        JSON.toJSONString(TenantOrgUserConsumeQuery.builder()
                                .tenantId(ContextUtil.getTenantId())
                                .employeeId(null)
                                .userId(baseEmployee.getUserId())
                                .build()));
            }
        }
        return b;
    }

    @GlobalTransactional
    public BaseServiceStaff update(BaseServiceStaffUpdateVO baseServiceStaffUpdateVO) {
        BaseServiceStaff byId = baseServiceStaffService.getById(baseServiceStaffUpdateVO.getId());
        if (byId == null) {
            return null;
        }
        if (ObjectUtil.isNull(byId.getIsDiscount())
                && ObjectUtil.isNull(baseServiceStaffUpdateVO.getIsDiscount())) {
            baseServiceStaffUpdateVO.setIsDiscount(false);
        }
        BaseServiceStaff update = baseServiceStaffService.updateById(baseServiceStaffUpdateVO);
        DefUserUpdateVO updateVO = DefUserUpdateVO.builder()
                .id(byId.getUserId())
                .skill(baseServiceStaffUpdateVO.getSkill())
                .workDescribe(baseServiceStaffUpdateVO.getWorkDescribe())
                .idCard(baseServiceStaffUpdateVO.getIdNum())
                .build();
        defUserService.updateById(updateVO);
        Long orgId = ContextUtil.getCurrentCompanyId();
        //新增服务信息
        baseServicePersonalManager.remove(Wraps.<BaseServicePersonal>lbQ().eq(BaseServicePersonal::getDeleteFlag, 0)
                .eq(BaseServicePersonal::getEmployeeId, update.getId()));
        if (CollUtil.isNotEmpty(baseServiceStaffUpdateVO.getServiceList())) {
            baseServicePersonalManager.saveBatch(baseServiceStaffUpdateVO.getServiceList().stream().map(v -> {
                return BaseServicePersonal.builder()
                        .serviceId(v)
                        .employeeId(update.getId())
                        .createdOrgId(orgId)
                        .build();
            }).collect(Collectors.toList()));
        }

        // 服务人员标签
        if (CollUtil.isNotEmpty(baseServiceStaffUpdateVO.getTagsList())) {
            List<BaseEmployeeTags> employeeTagsList = baseEmployeeTagsService.list(Wraps.<BaseEmployeeTags>lbQ().eq(BaseEmployeeTags::getEmployeeId, baseServiceStaffUpdateVO.getId()).eq(SuperEntity::getDeleteFlag, 0));
            if (CollUtil.isNotEmpty(employeeTagsList)) {
                baseEmployeeTagsService.removeByIds(employeeTagsList.stream().map(SuperEntity::getId).collect(Collectors.toList()));
            }
            List<BaseEmployeeTagsSaveVO> labelList = baseServiceStaffUpdateVO.getTagsList();
            labelList.forEach(s -> {
                s.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
                s.setEmployeeId(baseServiceStaffUpdateVO.getId());
            });
            baseEmployeeTagsService.saveBatch(BeanPlusUtil.toBeanList(labelList, BaseEmployeeTags.class));
        }

        // 删除之外的所有的数据
        baseEmployeeBannerService.remove(Wraps.<BaseEmployeeBanner>lbQ().eq(SuperEntity::getDeleteFlag, 0)
                .eq(BaseEmployeeBanner::getEmployeeId, baseServiceStaffUpdateVO.getId()));


        saveEmployeeBanner(baseServiceStaffUpdateVO.getBannerList(), baseServiceStaffUpdateVO.getId());

        return update;
    }


    public Boolean createAccount(BaseEmployeeInviteBatch inviteBatch, BaseEmployeeInviteBatchDetail inviteBatchDetail) {
        String mobile = inviteBatchDetail.getMobile();
        DefUser defUser = defUserService.getUserByMobile(mobile);
        if (ObjectUtil.isNull(defUser)) {
            String username = IdUtil.simpleUUID();
            // 保存默认库的 用户表 和 员工表
            defUser = DefUser.builder().username(username).nickName(inviteBatchDetail.getRealName())
                    .sex(inviteBatchDetail.getSex()).state(true).passwordErrorNum(0)
                    .readonly(false).password(inviteBatchDetail.getPassword())
                    .salt(inviteBatchDetail.getSalt()).mobile(inviteBatchDetail.getMobile())
                    .idCard(inviteBatchDetail.getIdNum())
                    .build();
            defUserService.save(defUser);
        } else {
            defUser.setSalt(inviteBatchDetail.getSalt());
            defUser.setPassword(inviteBatchDetail.getPassword());
            defUser.setUpdatePasswordTime(LocalDateTime.now());
            defUserService.updateEntityById(defUser);
        }
        DefUserTenantRel defUserTenantRel = defUserTenantRelService.getEmployeeByTenantAndUser(ContextUtil.getTenantId(), defUser.getId());
        if (ObjectUtil.isNull(defUserTenantRel)) {
            defUserTenantRel = DefUserTenantRel.builder().tenantId(ContextUtil.getTenantId())
                    .userId(defUser.getId()).state(true).isDefault(false).build();
            defUserTenantRelService.save(defUserTenantRel);
        }

        BaseServiceStaff baseServiceStaff = baseServiceStaffService.getOne(Wraps.<BaseServiceStaff>lbQ()
                .eq(BaseServiceStaff::getUserId, defUser.getId())
                .eq(BaseServiceStaff::getDeleteFlag, 0)
        );
        BaseRole baseRole = baseRoleService.getById(inviteBatch.getRoleId());
        ArgumentAssert.notNull(baseRole, "未找到默认助教角色");
        if (ObjectUtil.isNull(baseServiceStaff)) {
            baseServiceStaff = BeanUtil.copyProperties(inviteBatchDetail, BaseServiceStaff.class);
            baseServiceStaff.setUserId(defUser.getId());
            baseServiceStaff.setRealName(StrUtil.isBlank(inviteBatchDetail.getStageName()) ?
                    inviteBatchDetail.getRealName() : inviteBatchDetail.getStageName());
            baseServiceStaff.setName(inviteBatchDetail.getRealName());
            baseServiceStaff.setActiveStatus(ActiveStatusEnum.ACTIVATED.getCode());
            baseServiceStaff.setPositionStatus(PostionStatusEnum.POSED.getCode());
            baseServiceStaff.setIsDefault(false);
            baseServiceStaff.setIsAccount(true);
            baseServiceStaff.setIsDiscount(false);
            if (baseRole != null && baseRole.getCode().equalsIgnoreCase(RoleConstant.SERVICE_STAFF)) {
                baseServiceStaff.setType("2");
            }
            baseServiceStaff.setIsMobileUse(true);
            baseServiceStaff.setId(defUserTenantRel.getId());
            baseServiceStaff.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
            baseServiceStaff.setCreatedTime(LocalDateTime.now());
            baseServiceStaff.setUpdatedTime(LocalDateTime.now());
            baseServiceStaffService.save(baseServiceStaff);
        }
        //服务人员角色ID
        BaseEmployeeRoleRelSaveVO employeeRoleSaveVO = BaseEmployeeRoleRelSaveVO.builder()
                .flag(true)
                .employeeId(baseServiceStaff.getId())
                .roleIdList(Collections.singletonList(baseRole.getId()))
                .build();
        baseServiceStaffService.saveEmployeeRole(employeeRoleSaveVO);
        //服务人员绑定门店
        Long orgId = ContextUtil.getCurrentCompanyId();
        //服务人员门店ID
        BaseEmployeeOrgRelSaveVO employeeOrgSaveVO = BaseEmployeeOrgRelSaveVO.builder()
                .employeeId(baseServiceStaff.getId())
                .orgIdList(Collections.singletonList(orgId))
                .build();
        baseServiceStaffService.saveEmployeeOrg(employeeOrgSaveVO);
        template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.TENANT_ORG_USER,
                JSON.toJSONString(TenantOrgUserConsumeQuery.builder()
                        .tenantId(ContextUtil.getTenantId())
                        .employeeId(baseServiceStaff.getId())
                        .userId(baseServiceStaff.getUserId())
                        .build()));
        return true;
    }
}
