package top.kx.kxss.base.manager.user;

import top.kx.basic.base.manager.SuperCacheManager;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.interfaces.echo.LoadService;
import top.kx.kxss.base.entity.user.BasePosition;
import top.kx.kxss.base.vo.result.user.BasePositionResultVO;

import java.util.List;

/**
 * <p>
 * 通用业务接口
 * 岗位
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
public interface BasePositionManager extends SuperCacheManager<BasePosition>, LoadService {

    List<BasePositionResultVO> findList(LbQueryWrap<BasePosition> wrap);
}
