package top.kx.kxss.base.manager.biz.equity.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.kxss.base.entity.biz.equity.BaseBizEquityApply;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.kxss.base.manager.biz.equity.BaseBizEquityApplyManager;
import top.kx.kxss.base.mapper.biz.equity.BaseBizEquityApplyMapper;

/**
 * <p>
 * 通用业务实现类
 * 权益应用子项
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-19 16:55:07
 * @create [2024-03-19 16:55:07] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class BaseBizEquityApplyManagerImpl extends SuperManagerImpl<BaseBizEquityApplyMapper, BaseBizEquityApply> implements BaseBizEquityApplyManager {

}


