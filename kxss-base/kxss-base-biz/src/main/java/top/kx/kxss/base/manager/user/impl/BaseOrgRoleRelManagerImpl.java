package top.kx.kxss.base.manager.user.impl;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import top.kx.basic.base.manager.impl.SuperManagerImpl;
import top.kx.basic.cache.repository.CacheOps;
import top.kx.basic.cache.repository.CachePlusOps;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.model.cache.CacheHashKey;
import top.kx.kxss.base.entity.user.BaseOrgRoleRel;
import top.kx.kxss.base.manager.user.BaseOrgRoleRelManager;
import top.kx.kxss.base.mapper.user.BaseOrgRoleRelMapper;
import top.kx.kxss.common.cache.base.user.OrgRoleCacheKeyBuilder;
import top.kx.kxss.common.cache.wxapp.TenantOrgCacheKeyBuilder;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 通用业务实现类
 * 组织的角色
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 * @create [2021-10-18] [zuihou] [代码生成器生成]
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BaseOrgRoleRelManagerImpl extends SuperManagerImpl<BaseOrgRoleRelMapper, BaseOrgRoleRel> implements BaseOrgRoleRelManager {
    private final CacheOps cacheOps;
    private final CachePlusOps cachePlusOps;

    @Override
    public void deleteByOrg(Collection<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        super.remove(Wraps.<BaseOrgRoleRel>lbQ().in(BaseOrgRoleRel::getOrgId, idList));
        cacheOps.del(idList.stream().distinct().map(OrgRoleCacheKeyBuilder::build).collect(Collectors.toList()));
        try {
            CacheHashKey[] hashKeys = idList.stream().map(v -> TenantOrgCacheKeyBuilder.builder(ContextUtil.getTenantId().toString(),
                    ContextUtil.getTenantId().toString().concat("_").concat(v.toString()))).toArray(CacheHashKey[]::new);
            cachePlusOps.del(hashKeys);
        } catch (Exception ignored) {
        }
    }

    @Override
    public void deleteByRole(Collection<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        LbQueryWrap<BaseOrgRoleRel> wrap = Wraps.<BaseOrgRoleRel>lbQ()
                .in(BaseOrgRoleRel::getRoleId, idList);
        List<BaseOrgRoleRel> list = list(wrap);
        remove(wrap);
        cacheOps.del(list.stream().map(BaseOrgRoleRel::getOrgId).distinct().map(OrgRoleCacheKeyBuilder::build).collect(Collectors.toList()));
    }
}
