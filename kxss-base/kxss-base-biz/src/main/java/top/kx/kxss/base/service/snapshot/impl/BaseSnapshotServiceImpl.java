package top.kx.kxss.base.service.snapshot.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.annotation.log.FieldChangeLog;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.service.impl.SuperServiceImpl;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.EchoFieldUtil;
import top.kx.kxss.base.biz.base.BaseServiceStaffBiz;
import top.kx.kxss.base.biz.system.BaseRoleBiz;
import top.kx.kxss.base.biz.user.BaseEmployeeBiz;
import top.kx.kxss.base.entity.common.BaseParameter;
import top.kx.kxss.base.entity.payment.BasePaymentType;
import top.kx.kxss.base.entity.snapshot.BaseSnapshot;
import top.kx.kxss.base.entity.system.BaseRole;
import top.kx.kxss.base.entity.table.BaseTableInfo;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.manager.snapshot.BaseSnapshotManager;
import top.kx.kxss.base.service.common.BaseParameterService;
import top.kx.kxss.base.service.member.grade.MemberGradeService;
import top.kx.kxss.base.service.payment.BasePaymentTypeService;
import top.kx.kxss.base.service.product.BaseProductService;
import top.kx.kxss.base.service.score.BaseBizScoreService;
import top.kx.kxss.base.service.snapshot.BaseSnapshotService;
import top.kx.kxss.base.service.system.BaseRoleService;
import top.kx.kxss.base.service.table.BaseTableInfoService;
import top.kx.kxss.base.service.tableCharging.BaseTableChargingService;
import top.kx.kxss.base.service.thail.BaseThailService;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.vo.query.snapshot.BaseSnapshotPageQuery;
import top.kx.kxss.base.vo.result.common.BaseParameterResultVO;
import top.kx.kxss.base.vo.result.member.grade.MemberGradeResultVO;
import top.kx.kxss.base.vo.result.payment.BasePaymentTypeResultVO;
import top.kx.kxss.base.vo.result.product.BaseProductResultVO;
import top.kx.kxss.base.vo.result.score.BaseBizScoreResultVO;
import top.kx.kxss.base.vo.result.service.BaseServiceStaffResultVO;
import top.kx.kxss.base.vo.result.snapshot.BaseSnapshotResultVO;
import top.kx.kxss.base.vo.result.system.BaseRoleApplicationPermissionResultVO;
import top.kx.kxss.base.vo.result.system.BaseRoleApplicationResourceResultVO;
import top.kx.kxss.base.vo.result.system.BaseRolePermissionResultVO;
import top.kx.kxss.base.vo.result.system.BaseRoleResultVO;
import top.kx.kxss.base.vo.result.table.BaseTableInfoResultVO;
import top.kx.kxss.base.vo.result.tableCharging.BaseTableChargingResultVO;
import top.kx.kxss.base.vo.result.thail.BaseThailResultVO;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.base.vo.save.snapshot.BaseSnapshotSaveVO;
import top.kx.kxss.base.vo.save.snapshot.SnapshotSaveVO;
import top.kx.kxss.base.vo.update.snapshot.BaseSnapshotUpdateVO;
import top.kx.kxss.common.constant.DsConstant;
import top.kx.kxss.model.enumeration.base.SnapshotBizModuleEnum;
import top.kx.kxss.system.entity.application.DefApplication;
import top.kx.kxss.system.entity.application.DefResource;
import top.kx.kxss.system.service.application.DefApplicationService;
import top.kx.kxss.system.service.application.DefResourceService;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 业务实现类
 * 业务镜像日志
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-27 11:46:01
 * @create [2025-05-27 11:46:01] [yan] [代码生成器生成]
 */
@DS(DsConstant.BASE_TENANT)
@Slf4j
@RequiredArgsConstructor
@Service
//@Transactional(readOnly = true)
public class BaseSnapshotServiceImpl extends SuperServiceImpl<BaseSnapshotManager, Long, BaseSnapshot, BaseSnapshotSaveVO, BaseSnapshotUpdateVO, BaseSnapshotPageQuery, BaseSnapshotResultVO> implements BaseSnapshotService {

    private final BaseServiceStaffBiz baseServiceStaffBiz;
    private final BaseTableChargingService baseTableChargingService;
    private final BaseEmployeeBiz baseEmployeeBiz;
    private final BaseEmployeeService baseEmployeeService;
    private final BaseParameterService baseParameterService;
    private final BasePaymentTypeService basePaymentTypeService;
    private final BaseRoleService baseRoleService;
    private final BaseTableInfoService baseTableInfoService;
    private final BaseRoleBiz baseRoleBiz;
    private final DefResourceService defResourceService;
    private final DefApplicationService defApplicationService;
    private final BaseThailService baseThailService;
    private final MemberGradeService memberGradeService;
    private final BaseBizScoreService baseBizScoreService;
    private final BaseProductService baseProductService;
    @Autowired
    private EchoService echoService;

    @Override
    public void saveSnapshot(SnapshotSaveVO saveVO) {
        List<BaseSnapshot> baseSnapshotList = new ArrayList<>();
        Set<String> keySet = new HashSet<>();
        String employee = "";
        Long employeeId = ContextUtil.getEmployeeId();
        Long orgId = ContextUtil.getCurrentCompanyId();
        if (CollUtil.isNotEmpty(saveVO.getOldMap())) {
            keySet.addAll(saveVO.getOldMap().keySet());
        }
        if (CollUtil.isNotEmpty(saveVO.getNewMap())) {
            keySet.addAll(saveVO.getNewMap().keySet());
        }
        String fieldName = saveVO.getFieldName();
        keySet.forEach(key -> {
            Map<String, Object> oldData = getObjectMap(saveVO.getOldMap().get(key));
            Map<String, Object> newData = getObjectMap(saveVO.getNewMap().get(key));
            String source = "";
            Object dataSnapshot = null;
            switch (saveVO.getOperationType()) {
                case CREATE:
                    source = getFieldSource(fieldName, newData, source);
                    dataSnapshot = saveVO.getNewMap().get(key);
                    break;
                case UPDATE:
                case DELETE:
                    source = getFieldSource(fieldName, oldData, source);
                    dataSnapshot = saveVO.getOldMap().get(key);
                    break;
                default:
                    break;
            }
            BaseSnapshot baseSnapshot = BaseSnapshot.builder()
                    .bizModule(saveVO.getBizModule().getCode())
                    .operationType(saveVO.getOperationType().getCode())
                    .description(saveVO.getOperationType().getDesc() + source + saveVO.getBizModule().getDesc())
                    .employeeId(employeeId)
                    .dataSnapshot(Objects.nonNull(dataSnapshot) ? JSONUtil.toJsonStr(dataSnapshot) : null)
                    .oldData(JSONUtil.toJsonStr(oldData))
                    .newData(JSONUtil.toJsonStr(newData))
                    .source(key)
                    .createdOrgId(orgId)
                    .build();
            baseSnapshotList.add(baseSnapshot);
        });
        if (CollUtil.isEmpty(baseSnapshotList)) {
            log.info("没有需要保存的数据");
            return;
        }
        superManager.saveBatch(baseSnapshotList);
    }

    private static String getFieldSource(String fieldName, Map<String, Object> oldData, String source) {
        try {
            if (StringUtils.isNotBlank(fieldName)) {
                Object oldObj = oldData.get(fieldName);
                if (Objects.nonNull(oldObj)) {
                    Map<String, Object> map = (Map<String, Object>) oldObj;
                    if (CollUtil.isNotEmpty(map)) {
                        source = map.getOrDefault("value", "").toString();
                    }
                }
            }
            return source;
        } catch (Exception e) {
            log.error("获取字段值失败: fieldName={}, error={}", fieldName, e.getMessage());
        }
        return source;
    }

    @Override
    public Map<String, Object> getObjMap(SnapshotBizModuleEnum bizModule, List<String> sourceList) {
        Map<String, Object> map = new HashMap<>();
        switch (bizModule) {
            case SERVICE_EMPLOYEE:
                for (String source : sourceList) {
                    BaseServiceStaffResultVO baseServiceStaffResultVO = baseServiceStaffBiz.getEmployeeUserById(Long.valueOf(source));
                    if (Objects.nonNull(baseServiceStaffResultVO)) {
                        if (Objects.nonNull(baseServiceStaffResultVO.getHireDate())) {
                            baseServiceStaffResultVO.setHireDateStr(LocalDateTimeUtil.formatNormal(baseServiceStaffResultVO.getHireDate()));
                        }
                    }
                    echoService.action(baseServiceStaffResultVO);
                    map.put(source, baseServiceStaffResultVO);
                }
                break;
            case TABLE_CHARGING:
                for (String source : sourceList) {
                    BaseTableChargingResultVO tableChargingResultVO = baseTableChargingService.getInfoByTableType(source);
                    echoService.action(tableChargingResultVO);
                    map.put(source, tableChargingResultVO);
                }
                break;
            case EMPLOYEE:
                for (String source : sourceList) {
                    BaseEmployeeResultVO baseEmployeeResultVO = baseEmployeeBiz.getEmployeeUserById(Long.valueOf(source));
                    if (Objects.nonNull(baseEmployeeResultVO)) {
                        if (Objects.nonNull(baseEmployeeResultVO.getHireDate())) {
                            baseEmployeeResultVO.setHireDateStr(LocalDateTimeUtil.formatNormal(baseEmployeeResultVO.getHireDate()));
                        }
                    }
                    echoService.action(baseEmployeeResultVO);
                    map.put(source, baseEmployeeResultVO);
                }
                break;
            case PARAMETERS:
                for (String source : sourceList) {
                    BaseParameter baseParameter = baseParameterService.getById(Long.valueOf(source));
                    map.put(source, BeanPlusUtil.toBean(baseParameter, BaseParameterResultVO.class));
                }
                break;
            case PAYMENT_TYPE:
                for (String source : sourceList) {
                    BasePaymentType paymentType = basePaymentTypeService.getById(Long.valueOf(source));
                    BasePaymentTypeResultVO paymentTypeResultVO = BeanPlusUtil.toBean(paymentType, BasePaymentTypeResultVO.class);
                    echoService.action(paymentTypeResultVO);
                    map.put(source, paymentTypeResultVO);
                }
                break;
            case BASE_ROLE:
                for (String source : sourceList) {
                    BaseRole baseRole = baseRoleService.getById(Long.valueOf(source));
                    BaseRoleResultVO baseRoleResultVO = BeanPlusUtil.toBean(baseRole, BaseRoleResultVO.class);
                    echoService.action(baseRoleResultVO);
                    map.put(source, baseRoleResultVO);
                }
                break;
            case BASE_TABLE:
                for (String source : sourceList) {
                    BaseTableInfo baseTableInfo = baseTableInfoService.getById(Long.valueOf(source));
                    BaseTableInfoResultVO baseTableInfoResultVO = BeanPlusUtil.toBean(baseTableInfo, BaseTableInfoResultVO.class);
                    echoService.action(baseTableInfoResultVO);
                    map.put(source, baseTableInfoResultVO);
                }
                break;
            case BASE_ROLE_PERMISSION:
                List<DefApplication> defApplicationList = defApplicationService.list(Wraps.<DefApplication>lbQ());
                Map<Long, DefApplication> applicationMap = defApplicationList.stream().collect(Collectors.toMap(DefApplication::getId, defApplication -> defApplication));
                for (String source : sourceList) {
                    BaseRolePermissionResultVO permissionResultVO = new BaseRolePermissionResultVO();
                    permissionResultVO.setRoleId(Long.valueOf(source));
                    Map<Long, Collection<Long>> resourceIdByRoleId = baseRoleBiz.findResourceIdByRoleId(Long.valueOf(source));
                    List<BaseRoleApplicationPermissionResultVO> permissionList = new ArrayList<>();
                    for (Long applicationId : resourceIdByRoleId.keySet()) {
                        List<DefResource> defResourceList = defResourceService.list(Wraps.<DefResource>lbQ().in(SuperEntity::getId, resourceIdByRoleId.get(applicationId)));
                        permissionList.add(BaseRoleApplicationPermissionResultVO.builder().applicationId(applicationId).applicationName(CollUtil.isNotEmpty(applicationMap) && applicationMap.containsKey(applicationId) ? applicationMap.get(applicationId).getName() : "-").resourceList(BeanPlusUtil.toBeanList(defResourceList, BaseRoleApplicationResourceResultVO.class)).build());
                    }
                    permissionResultVO.setPermissionList(permissionList);
                    echoService.action(permissionResultVO);
                    if (CollUtil.isNotEmpty(permissionResultVO.getEchoMap()) && Objects.nonNull(permissionResultVO.getEchoMap().get("roleId"))) {
                        permissionResultVO.setRoleName(permissionResultVO.getEchoMap().get("roleId").toString());
                    }
                    map.put(source, permissionResultVO);
                }
                break;
            case BASE_THAIL:
                for (String source : sourceList) {
                    BaseThailResultVO resultVO = baseThailService.getDetail(Long.valueOf(source));
                    map.put(source, resultVO);
                }
                break;
            case MEMBER_GRADE:
                for (String source : sourceList) {
                    MemberGradeResultVO memberGradeResultVO = memberGradeService.detail(Long.valueOf(source));
                    if (Objects.nonNull(memberGradeResultVO)) {
                        List<BaseBizScoreResultVO> resultVOList = baseBizScoreService.query(Long.valueOf(source));
                        memberGradeResultVO.setScoreList(resultVOList);
                    }
                    echoService.action(memberGradeResultVO);
                    map.put(source, memberGradeResultVO);
                }
                break;
            case PRODUCT:
                for (String source : sourceList) {
                    BaseProductResultVO baseProductResultVO = baseProductService.getDetail(Long.valueOf(source));
                    map.put(source, baseProductResultVO);
                }
                break;
            default:
                break;
        }
        return map;
    }


    private Map<String, Object> getObjectMap(Object obj) {
        if (Objects.isNull(obj)) {
            return MapUtil.newHashMap();
        }
        Map<String, Object> changedFields = new HashMap<>();

        // 获取当前类及其所有父类的字段
        List<Field> allFields = getAllFieldsWithFieldChangeLog(obj.getClass());

        for (Field field : allFields) {
            // 检查是否需要记录该字段的变更
            FieldChangeLog fieldLog = field.getAnnotation(FieldChangeLog.class);
            if (fieldLog == null || fieldLog.ignore()) {
                continue;
            }

            try {
                // 获取字段值
                Object objValue;
                // 在 compareDataChanges 方法中修改处理 valueGetter 的部分
                if (StrUtil.isNotBlank(fieldLog.valueGetter())) {
                    // 使用 valueGetter 获取值
                    objValue = getValueByGetter(obj, fieldLog.valueGetter());
                } else {
                    // 使用 Echo 值, 判断是否是 Echo 如果不是的话, , 判断是否是list, 如果是list,需要便来 遍历
                    Echo echo = field.getAnnotation(Echo.class);
                    // 配置了echoMap, 就尽量去echoMap
                    if (Objects.nonNull(echo)) {
                        objValue = EchoFieldUtil.getEchoValue(obj, field);
                    } else {
                        field.setAccessible(true);
                        Object source = field.get(obj);
                        // 判断是否是list
                        if (source instanceof Collection) {
                            Collection<?> collection = (Collection<?>) source;
                            if (collection.isEmpty()) {
                                continue;
                            }
                            List<Map<String, Object>> list = new ArrayList<>();
                            for (Object o : collection) {
                                list.add(getObjectMap(o));
                            }
                            objValue = list;
                        } else if (source != null && !isBasicType(source.getClass())) {
                            // 如果不是基本类型，递归处理
                            objValue = getObjectMap(source);
                        } else {
                            objValue = source;
                        }
                    }
                }
                // 如果值没有变化，直接跳过
                objValue = getEnumMappingObject(fieldLog, objValue);
                Map<String, Object> fieldInfo = new HashMap<>();
                fieldInfo.put("value", objValue);
                fieldInfo.put("fieldDesc", fieldLog.value());
                changedFields.put(field.getName(), fieldInfo);
            } catch (Exception e) {
                log.error("处理字段变更记录失败: field={}, error={}", field.getName(), e.getMessage());
            }
        }
        return changedFields;
    }

    /**
     * 获取当前类及其所有父类中带有 FieldChangeLog 注解的字段
     *
     * @param clazz 目标类
     * @return 所有带有 FieldChangeLog 注解的字段列表
     */
    private List<Field> getAllFieldsWithFieldChangeLog(Class<?> clazz) {
        List<Field> allFields = new ArrayList<>();

        // 递归获取当前类及其所有父类的字段
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            Field[] declaredFields = currentClass.getDeclaredFields();
            for (Field field : declaredFields) {
                // 只添加带有 FieldChangeLog 注解的字段
                if (field.getAnnotation(FieldChangeLog.class) != null) {
                    allFields.add(field);
                }
            }
            currentClass = currentClass.getSuperclass();
        }

        return allFields;
    }

    /**
     * 判断是否是基本类型
     */
    private boolean isBasicType(Class<?> clazz) {
        return clazz.isPrimitive() || clazz == String.class || clazz == Integer.class || clazz == Long.class || clazz == Double.class || clazz == Float.class || clazz == Boolean.class || clazz == Byte.class || clazz == Character.class || clazz == Short.class || clazz == Date.class || clazz == LocalDate.class || clazz == LocalDateTime.class || clazz == BigDecimal.class;
    }

    /**
     * 获取枚举映射值
     *
     * @param fieldLog
     * @param value
     * @return
     */
    private Object getEnumMappingObject(FieldChangeLog fieldLog, Object value) {
        String enumMapping = fieldLog.enumMapping();
        if (StringUtils.isNotBlank(enumMapping)) {
            Map<String, String> mapping = parseEnumMapping(enumMapping);
            value = mapping.getOrDefault(value.toString(), mapping.getOrDefault("_null", "未知"));
        }
        return value;
    }

    /**
     * 根据 valueGetter 获取值
     */
    private Object getValueByGetter(Object obj, String valueGetter) {
        if (obj == null || StrUtil.isBlank(valueGetter)) {
            log.debug("getValueByGetter: obj is null or valueGetter is blank");
            return null;
        }

        // 集合
        if (valueGetter.contains("[*]")) {
            //  tagsResultVOList[*]echoMap.tags-score 解析成  tagsResultVOList 一个字符串 和 echoMap.tags 和 score 的列表， echoMap.tags-score 可能是不止两个，可能是三个或者多个
            // 取 "[*]" 前面的值
            try {
                String sourceStr = valueGetter.split("\\[\\*]")[0];
                String partStr = valueGetter.split("\\[\\*]")[1];
                Field field = obj.getClass().getDeclaredField(sourceStr);
                field.setAccessible(true);
                Object sourceList = field.get(obj);
                List<String> allResults = new ArrayList<>();
                if (sourceList instanceof Collection) {
                    Collection<?> collection = (Collection<?>) sourceList;
                    if (collection.isEmpty()) {
                        return new ArrayList<>();
                    }
                    for (Object item : collection) {
                        if (item != null) {
                            List<String> result = new ArrayList<>();
                            if (partStr.contains("-")) {
                                String[] parts = valueGetter.split("-");
                                for (String part : parts) {
                                    if (part.contains("[*]")) {
                                        part = part.split("\\[\\*]")[1];
                                    }
                                    // 判断是否包含echoMap
                                    if (part.contains("echoMap")) {
                                        // 提取echoMap
                                        String[] echoMapParts = part.split("\\.");
                                        String echoMapFieldStr = echoMapParts[1];
                                        Field echoMapField = item.getClass().getDeclaredField("echoMap");
                                        echoMapField.setAccessible(true);
                                        Map<String, Object> echoMap = (Map<String, Object>) echoMapField.get(item);
                                        if (CollUtil.isNotEmpty(echoMap)) {
                                            Object value = echoMap.get(echoMapFieldStr);
                                            if (value != null) {
                                                result.add(value.toString());
                                            }
                                        }
                                    } else {
                                        Field partField = item.getClass().getDeclaredField(part);
                                        partField.setAccessible(true);
                                        Object value = partField.get(item);
                                        if (value != null) {
                                            result.add(value.toString());
                                        }
                                    }
                                }
                            } else {
                                // 没有分隔符， 就是单个
                                Field partField = item.getClass().getDeclaredField(partStr);
                                partField.setAccessible(true);
                                Object value = partField.get(item);
                                if (value != null) {
                                    result.add(value.toString());
                                }
                            }
                            if (CollUtil.isNotEmpty(result)) {
                                allResults.add(StrUtil.join("  ", result));
                            }
                        }
                    }
                    return allResults;
                } else {
                    return new ArrayList<>();
                }
            } catch (Exception e) {
                log.error("getValueByGetter: error={}", e.getMessage());
            }
        } else {
            // 单个对象 tagsResultVOList:echoMap.tags-source
            try {
                String sourceStr = valueGetter.split(":")[0];
                String partStr = valueGetter.split(":")[1];
                Field field = obj.getClass().getDeclaredField(sourceStr);
                field.setAccessible(true);
                Object source = field.get(obj);
                List<String> result = new ArrayList<>();
                if (partStr.contains("-")) {
                    String[] parts = valueGetter.split("-");
                    for (String part : parts) {
                        if (part.contains(":")) {
                            part = part.split(":")[1];
                        }
                        // 判断是否包含echoMap
                        if (part.contains("echoMap")) {
                            // 提取echoMap
                            String[] echoMapParts = part.split("\\.");
                            String echoMapFieldStr = echoMapParts[1];
                            Field echoMapField = source.getClass().getDeclaredField("echoMap");
                            echoMapField.setAccessible(true);
                            Map<String, Object> echoMap = (Map<String, Object>) echoMapField.get(source);
                            if (CollUtil.isNotEmpty(echoMap)) {
                                Object value = echoMap.get(echoMapFieldStr);
                                if (value != null) {
                                    result.add(value.toString());
                                }
                            }
                        } else {
                            Field partField = source.getClass().getDeclaredField(part);
                            partField.setAccessible(true);
                            Object value = partField.get(source);
                            if (value != null) {
                                result.add(value.toString());
                            }
                        }
                    }
                    if (CollUtil.isNotEmpty(result)) {
                        return StrUtil.join("  ", result);
                    }
                } else {
                    // 没有分隔符， 就是单个
                    Field partField = source.getClass().getDeclaredField(partStr);
                    partField.setAccessible(true);
                    Object value = partField.get(source);
                    if (value != null) {
                        result.add(value.toString());
                    }
                }
                if (CollUtil.isNotEmpty(result)) {
                    return StrUtil.join("  ", result);
                }
            } catch (Exception e) {
                log.error("getValueByGetter: error={}", e.getMessage());
            }
        }
        return null;
    }

    /**
     * 解析枚举映射
     *
     * @param enumMapping
     * @return
     */
    private Map<String, String> parseEnumMapping(String enumMapping) {
        Map<String, String> map = new HashMap<>();
        if (StrUtil.isBlank(enumMapping)) {
            return map;
        }
        String[] pairs = enumMapping.split(",");
        for (String pair : pairs) {
            String[] kv = pair.split("-", 2);
            if (kv.length == 2) {
                map.put(kv[0], kv[1]);
            }
        }
        return map;
    }

}


