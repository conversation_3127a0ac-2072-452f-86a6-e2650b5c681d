<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.base.mapper.accounting.BaseAccountingInfoMapper">
<!--
    代码生成器 by 2023-10-09 17:13:29
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.base.entity.accounting.BaseAccountingInfo">
        <id column="id" property="id" />
        <result column="type" property="type" />
        <result column="recording_date" property="recordingDate" />
        <result column="amount" property="amount" />
        <result column="pay_type_id" property="payTypeId" />
        <result column="remarks" property="remarks" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_org_id" property="createdOrgId" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="pay_in_type" property="payInType" />
        <result column="pay_out_type" property="payOutType" />
        <result column="accounting_date_id" property="accountingDateId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type, recording_date, amount, pay_type_id, remarks, 
        created_time, created_by, updated_time, updated_by, created_org_id, delete_flag, 
        pay_in_type, pay_out_type, accounting_date_id
    </sql>

    <select id="calendar" resultType="top.kx.kxss.base.vo.result.accounting.AccountingCalenderResultVO">
        select ${field}                     as recordingDate,
               sum(if(bai.type = '1', bai.amount, 0)) as payInAmount,
               sum(if(bai.type = '1' and bai.source = 'HAND', bai.amount, 0))          as manualPayInAmount,
               sum(if(bai.type = '2', bai.amount, 0)) as payOutAmount,
               sum(if(bai.type = '2' and bai.source = 'HAND', bai.amount, 0))          as manualPayOutAmount,
               sum(if(bai.type = '1', ifnull(bai.amount, 0), - ifnull(bai.amount, 0))) as balance
        from base_accounting_info bai
            ${ew.customSqlSegment}
        group by recordingDate
        order by recordingDate
    </select>

    <select id="calendarSum" resultType="top.kx.kxss.base.vo.result.accounting.AccountingCalenderSumResultVO">
        select ifnull(sum(if(bai.type = '1', bai.amount, 0)), 0) as totalPayInAmount,
               ifnull(sum(if(bai.type = '1' and bai.source = 'HAND', bai.amount, 0)), 0)           as manualPayInAmount,
               ifnull(sum(if(bai.type = '2', bai.amount, 0)), 0) as totalPayOutAmount,
               ifnull(sum(if(bai.type = '2' and bai.source = 'HAND', bai.amount, 0)), 0)          as manualPayOutAmount,
               ifnull(sum(if(bai.type = '1', ifnull(bai.amount, 0), - ifnull(bai.amount, 0))), 0) as totalBalance
        from base_accounting_info bai
            ${ew.customSqlSegment}
    </select>
    <select id="calendarIn" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT
            DATE_FORMAT(DATE_ADD(p.complete_time, INTERVAL -#{hour} HOUR), #{field}) AS field,
              (IFNULL(SUM(IFNULL(t.amount, 0)), 0)
            - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)
            - IFNULL(SUM(IFNULL(t.change_amount, 0)), 0))                                   amount,
               (IFNULL(SUM(IFNULL(t.amount, 0)), 0)
                   - SUM(if(t.is_prepaid is not null and t.is_prepaid = 1, IFNULL(t.refund_amount, 0),
                            0))
                   - IFNULL(SUM(IFNULL(t.change_amount, 0)), 0))                            payment,
               SUM(ROUND(IF(bpt.fee_rate is null, 0,
                            (IFNULL(t.amount, 0) - ifnull(t.refund_amount, 0) - IFNULL(t.change_amount, 0)) *
                            bpt.fee_rate /
                            100), 2))                                                       feePayment,
               min(t.pay_name) as                                                           name,
               IFNULL(SUM(IF(IFNULL(t.refund_amount, 0) > 0 and IFNULL(t.refund_amount, 0) > IFNULL(t.gift_amount, 0),
                             0, IFNULL(t.gift_amount, 0) - IFNULL(t.refund_amount, 0))), 0) giftAmount,
               IFNULL(SUM(IF(IFNULL(t.refund_amount, 0) > 0 and IFNULL(t.refund_amount, 0) > IFNULL(t.gift_amount, 0),
                             IFNULL(t.recharge_amount, 0) - (IFNULL(t.refund_amount, 0) - IFNULL(t.gift_amount, 0)),
                             IFNULL(t.recharge_amount, 0))), 0)
                                                                                            rechargeAmount,
               IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)                                   refundAmount,
               IFNULL(count(t.pay_type_id), 0)                                              num,
               IFNULL(SUM(IFNULL(t.change_amount, 0)), 0)                                   changeAmount
        FROM pos_cash_payment t
                 JOIN pos_cash p ON p.id = t.cash_id
                 LEFT JOIN base_payment_type bpt on t.pay_type_id = bpt.id
            ${ew.customSqlSegment}
        GROUP BY field
    </select>
    <select id="calendarInList" resultType="top.kx.kxss.app.vo.result.cash.AmountResultVO">
        SELECT
            t.pay_type_id   AS                                                           field,
            t.pay_name   AS                                                           name,
            p.id   AS                                                           sourceId,
            p.complete_time AS                                                           completeTime,
            t.created_time AS                                                           createdTime,
            p.complete_emp AS                                                           employeeId,
            (IFNULL(SUM(IFNULL(t.amount, 0)), 0)
                - IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)
                - IFNULL(SUM(IFNULL(t.change_amount, 0)), 0))                                   amount,
            (IFNULL(SUM(IFNULL(t.amount, 0)), 0)
                - SUM(if(t.is_prepaid is not null and t.is_prepaid = 1, IFNULL(t.refund_amount, 0),
                         0))
                - IFNULL(SUM(IFNULL(t.change_amount, 0)), 0))                            payment,
            SUM(ROUND(IF(bpt.fee_rate is null, 0,
                         (IFNULL(t.amount, 0) - ifnull(t.refund_amount, 0) - IFNULL(t.change_amount, 0)) *
                         bpt.fee_rate /
                         100), 2))                                                       feePayment,
            IFNULL(SUM(IF(IFNULL(t.refund_amount, 0) > 0 and IFNULL(t.refund_amount, 0) > IFNULL(t.gift_amount, 0),
                          0, IFNULL(t.gift_amount, 0) - IFNULL(t.refund_amount, 0))), 0) giftAmount,
            IFNULL(SUM(IF(IFNULL(t.refund_amount, 0) > 0 and IFNULL(t.refund_amount, 0) > IFNULL(t.gift_amount, 0),
                          IFNULL(t.recharge_amount, 0) - (IFNULL(t.refund_amount, 0) - IFNULL(t.gift_amount, 0)),
                          IFNULL(t.recharge_amount, 0))), 0)
                rechargeAmount,
            IFNULL(SUM(IFNULL(t.refund_amount, 0)), 0)                                   refundAmount,
            IFNULL(count(t.pay_type_id), 0)                                              num,
            IFNULL(SUM(IFNULL(t.change_amount, 0)), 0)                                   changeAmount
        FROM pos_cash_payment t
                 JOIN pos_cash p ON p.id = t.cash_id
                 LEFT JOIN base_payment_type bpt on t.pay_type_id = bpt.id
            ${ew.customSqlSegment}
        GROUP BY t.id
    </select>

</mapper>
