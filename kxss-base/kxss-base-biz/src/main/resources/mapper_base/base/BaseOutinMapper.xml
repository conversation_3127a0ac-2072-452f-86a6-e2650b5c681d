<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="top.kx.kxss.base.mapper.outin.BaseOutinMapper">
<!--
    代码生成器 by 2023-04-06 14:51:59
    自定义sql建议在base文件夹同级新建ext文件夹，并新建同名且同namespace的xml进行编写。方便修改字段时，重新生成此文件。
-->

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="top.kx.kxss.base.entity.outin.BaseOutin">
        <id column="id" property="id" />
        <result column="type_" property="type" />
        <result column="source_type" property="sourceType" />
        <result column="store_id" property="storeId" />
        <result column="code" property="code" />
        <result column="bill_date" property="billDate" />
        <result column="bill_state" property="billState" />
        <result column="org_id" property="orgId" />
        <result column="employee_id" property="employeeId" />
        <result column="supplier_id" property="supplierId" />
        <result column="customer_id" property="customerId" />
        <result column="amount" property="amount" />
        <result column="discount_amount" property="discountAmount" />
        <result column="payment" property="payment" />
        <result column="pay_type" property="payType" />
        <result column="remarks" property="remarks" />
        <result column="created_time" property="createdTime" />
        <result column="created_by" property="createdBy" />
        <result column="updated_time" property="updatedTime" />
        <result column="updated_by" property="updatedBy" />
        <result column="created_org_id" property="createdOrgId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type_, source_type, store_id, code, bill_date, 
        bill_state, org_id, employee_id, supplier_id, customer_id, amount, 
        discount_amount, payment, pay_type, remarks, created_time, created_by, 
        updated_time, updated_by, created_org_id
    </sql>

    <select id="pageList" resultType="top.kx.kxss.base.vo.result.outin.BaseOutinItemResultVO">
        select a.*
        from (
            -- 采购入库/退货
            select bo.id as id,
                   bo.code as code,
                   bo.created_time as createdTime,
                   bo.employee_id as employeeId,
                   bo.warehouse_id as warehouseId,
                   0 as inWarehouseId,
                   0 as outWarehouseId,
                   bo.state as state,
                   sum(bop.num) as num,
                   sum(bop.num) as outinProductNum,
                   bo.remarks as remarks,
                   bo.type_ as type,
                   CASE bo.type_
                     WHEN '0' THEN '采购入库'
                     WHEN '7' THEN '采购退货'
                     WHEN '4' THEN '其他出库'
                     WHEN '11' THEN '其他入库'
                     ELSE '其他'
                   END as typeName
            from base_outin bo
            left join base_outin_product bop on bo.id = bop.outin_id and bop.delete_flag = 0
            where bo.delete_flag = 0
              and bo.type_ in ('0', '7', '4', '11')
              <if test="model.state != null">
                  and bo.state = #{model.state}
              </if>
                <if test="model.code != null and model.code != ''">
                    and bo.code = #{model.code}
                </if>
              <if test="model.createdOrgId != null">
                  and bo.created_org_id = #{model.createdOrgId}
              </if>
              <if test="model.startDate != null and model.startDate != ''">
                  and bo.created_time >= #{model.startDate}
              </if>
              <if test="model.endDate != null and model.endDate != ''">
                  and bo.created_time &lt;= #{model.endDate}
              </if>
            group by bo.id

            union all

            -- 库存盘点
            select bos.id as id,
                   bos.code as code,
                   bos.created_time as createdTime,
                   bos.employee_id as employeeId,
                   bos.warehouse_id as warehouseId,
                   0 as inWarehouseId,
                   0 as outWarehouseId,
                   bos.state as state,
                   null as num,
                   null as outinProductNum,
                   bos.remarks as remarks,
                   '1' as type,
                   '库存盘点' as typeName
            from base_outin_stocktaking bos
            where bos.delete_flag = 0
              <if test="model.state != null">
                  and bos.state = #{model.state}
              </if>
                <if test="model.code != null and model.code != ''">
                    and bos.code = #{model.code}
                </if>
              <if test="model.createdOrgId != null">
                  and bos.created_org_id = #{model.createdOrgId}
              </if>
              <if test="model.startDate != null and model.startDate != ''">
                  and bos.created_time >= #{model.startDate}
              </if>
              <if test="model.endDate != null and model.endDate != ''">
                  and bos.created_time &lt;= #{model.endDate}
              </if>
            group by bos.id

            union all

            -- 商品调库
            select boa.id as id,
                   boa.code as code,
                   boa.created_time as createdTime,
                   boa.employee_id as employeeId,
                   0 as warehouseId,
                   boa.in_warehouse_id as inWarehouseId,
                   boa.out_warehouse_id as outWarehouseId,
                   boa.state as state,
                   null as num,
                   null as outinProductNum,
                   boa.remarks as remarks,
                   '9' as type,
                   '商品调库' as typeName
            from base_outin_adjustment boa
            where boa.delete_flag = 0
              <if test="model.state != null">
                  and boa.state = #{model.state}
              </if>
                <if test="model.code != null and model.code != ''">
                    and boa.code = #{model.code}
                </if>
              <if test="model.createdOrgId != null">
                  and boa.created_org_id = #{model.createdOrgId}
              </if>
              <if test="model.startDate != null and model.startDate != ''">
                  and boa.created_time >= #{model.startDate}
              </if>
              <if test="model.endDate != null and model.endDate != ''">
                  and boa.created_time &lt;= #{model.endDate}
              </if>
            group by boa.id
        ) a
        <where>
            <if test="model.type != null and model.type != ''">
                and a.type = #{model.type}
            </if>
        </where>
        order by a.createdTime desc
    </select>
    <select id="sellPage" resultType="top.kx.kxss.base.entity.outin.SellOutinResultVO">
        select p.id                  as id,
               p.code                as code,
               DATE(p.complete_time) as completeDate,
               p.complete_time       as completeTime,
               p.product_amount      as amount,
               pcp.remarks             as remarks,
               p.complete_emp        as completeEmp,
               p.created_time        as createdTime
        from pos_cash_product pcp
                 inner join pos_cash p on p.id = pcp.cash_id
            ${ew.customSqlSegment}
    </select>
    <select id="sellDetailList" resultType="top.kx.kxss.base.entity.outin.SellOutinDetailsResultVO">
        select pro.created_time                                                    AS createdTime,
               p.complete_time                                                     AS completeTime,
               d.category_id                                                       AS categoryId,
               IF(d.delete_flag = 1, concat(d.`NAME`, '(已删除)'), d.`NAME`)       AS name,
               d.measuring_unit                                                    AS measuringUnit,
               pro.num - ifnull(pro.refund_num, 0)                                 AS num,
               pro.price                                                           AS price,
               ROUND((pro.orgin_price - pro.discount_amount - pro.assessed_amount
                   - ifNULL(pro.refund_amount, 0)), 2)                             AS totalPrice,
               pro.cost_price                                                      AS costPrice,
               ((pro.num - ifnull(pro.refund_num, 0)) * IFNULL(pro.cost_price, 0)) AS totalCostPrice,
               IFNULL(pro.profit_price, 0)                                         AS profitPrice,
               com.name                                                            AS beneficiaryEmp,
               p.created_emp                                                        AS createdEmp,
               p.code                                                              AS code,
               pro.warehouse_id                                                    AS warehouseId
        from pos_cash_product pro
                 INNER JOIN pos_cash p ON p.id = pro.cash_id
                 LEFT JOIN base_outin o ON o.`code` = p.`code` and o.delete_flag = 0 AND o.state = 1
                 LEFT JOIN base_product d ON d.id = pro.product_id
                 left join (select c.source_id as id, c.cash_id as cashId, GROUP_CONCAT(e.real_name SEPARATOR ',') as name
                            from pos_cash_commenter c
                                     LEFT JOIN base_employee e on e.id = c.employee_id
                            where c.delete_flag = 0
                              and c.type_ = '2'
                            GROUP BY c.cash_id, c.source_id) as com on com.id = pro.id and pro.cash_id = com.cashId
                 LEFT JOIN base_product_category cate ON cate.id = d.category_id
        where pro.delete_flag = 0
          AND p.delete_flag = 0
          AND p.bill_state IN ('2', '5')
          AND p.bill_type NOT IN ('1', '2')
          AND p.id = #{id}
    </select>
    <select id="sellOne" resultType="top.kx.kxss.base.entity.outin.SellOutinResultVO">
        select p.id                  as id,
               p.code                as code,
               DATE(p.complete_time) as completeDate,
               p.complete_time       as completeTime,
               p.product_amount      as amount,
               p.remarks             as remarks,
               p.complete_emp        as completeEmp,
               p.created_time        as createdTime
        from pos_cash_product pcp
                 inner join pos_cash p on p.id = pcp.cash_id
            ${ew.customSqlSegment}
    </select>

</mapper>
