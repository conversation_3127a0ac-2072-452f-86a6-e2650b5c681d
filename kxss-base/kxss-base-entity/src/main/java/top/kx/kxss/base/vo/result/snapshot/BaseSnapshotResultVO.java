package top.kx.kxss.base.vo.result.snapshot;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.enumeration.base.BizLogOperationTypeEnum;
import top.kx.kxss.model.enumeration.base.SnapshotBizModuleEnum;

import java.io.Serializable;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 业务镜像日志
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-27 11:46:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "BaseSnapshotResultVO", description = "业务镜像日志")
public class BaseSnapshotResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
    * 操作模块
    */
    @Echo(api = Echo.ENUM_API)
    @ApiModelProperty(value = "操作模块")
    private SnapshotBizModuleEnum bizModule;
    /**
    * 操作类型：新增、修改、删除
    */
    @Echo(api = Echo.ENUM_API)
    @ApiModelProperty(value = "操作类型：新增、修改、删除")
    private BizLogOperationTypeEnum operationType;
    /**
    * 来源
    */
    @ApiModelProperty(value = "来源")
    private String source;
    /**
    * 操作时的数据快照（字符串）
    */
    @ApiModelProperty(value = "操作时的数据快照（字符串）")
    private String dataSnapshot;


    /**
     * 原数据（JSON格式）
     */
    @ApiModelProperty(value = "原数据（JSON格式）")
    private String oldData;

    /**
     * 新数据（JSON格式）
     */
    @ApiModelProperty(value = "新数据（JSON格式）")
    private String newData;

    /**
     * 变更字段
     */
    @ApiModelProperty(value = "变更字段")
    private String changedFields;

    /**
    * 操作员工ID
    */
    @Echo(api = EchoApi.EMPLOYEE_CONTAIN_REMOVE_CLASS)
    @ApiModelProperty(value = "操作员工ID")
    private Long employeeId;
    /**
    * 操作描述
    */
    @ApiModelProperty(value = "操作描述")
    private String description;
    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    @Echo(api = EchoApi.ORG_ID_CLASS)
    private Long createdOrgId;



}
