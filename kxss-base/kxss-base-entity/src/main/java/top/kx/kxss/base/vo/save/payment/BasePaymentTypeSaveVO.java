package top.kx.kxss.base.vo.save.payment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.kxss.base.vo.BizTypeVO;

import javax.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 表单保存方法VO
 * 支付类型
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-19 14:55:43
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BasePaymentTypeSaveVO", description = "支付类型")
public class BasePaymentTypeSaveVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @NotEmpty(message = "请填写名称")
    @Size(max = 200, message = "名称长度不能超过{max}")
    private String name;
    /**
     * 是否启用
     */
    @ApiModelProperty(value = "是否启用")
    @NotNull(message = "请填写是否启用")
    private Boolean state;
    /**
     * 类型
     */
    @ApiModelProperty(value = "类型")
    @Size(max = 5, message = "类型长度不能超过{max}")
    private String type;
    /**
     * 自定义图标图片ID
     */
    @ApiModelProperty(value = "自定义图标图片ID")
    private Long iconId;
    /**
     * 图标颜色
     */
    @ApiModelProperty(value = "图标颜色")
    @Size(max = 100, message = "图标颜色长度不能超过{max}")
    private String customColor;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 255, message = "备注长度不能超过{max}")
    private String remarks;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;
    /**
     * 业务类型 0 直接支付 1 账户支付 2聚合支付
     */
    @ApiModelProperty(value = "业务类型 0 直接支付 1 账户支付 2聚合支付")
    private String bizType;

    /**
     * 是否小程序使用
     */
    @NotNull(message = "请填写是否小程序使用")
    @ApiModelProperty(value = "是否小程序使用")
    private Boolean isApplet;

    /**
     * 可用范围 使用4位二进制表述 台费 商品 服务 权益卡
     */
    @ApiModelProperty(value = "可用范围 使用4位二进制表述 台费 商品 服务 权益卡")
    private List<String> usableRange;

    /**
     * 排除项目列表
     */
    @ApiModelProperty(value = "排除项目列表")
    private List<BizTypeVO> excludeList;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排除项目列表")
    @NotNull(message = "请填写排序")
    private Integer sortValue;

    /**
     * 支付渠道
     */
    @ApiModelProperty(value = "支付渠道")
    private String payChannel;

    @ApiModelProperty(value = "支付费率")
    @DecimalMin(value = "0", message = "支付费率必须大于0")
    @DecimalMax(value = "100", message = "支付费率必须小于100")
    private BigDecimal feeRate;

    /**
     * 收入规则 1 计入收入 2 计入优惠
     */
    @ApiModelProperty(value = "收入规则 1 计入收入 2 计入优惠")
    private Integer incomeFlag;

    /**
     * 赠金收入规则 1 计入收入 2 计入优惠
     */
    @ApiModelProperty(value = "赠金收入规则 1 计入收入 2 计入优惠")
    private Integer giftIncomeFlag;


}
