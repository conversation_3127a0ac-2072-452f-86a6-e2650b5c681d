package top.kx.kxss.base.vo.save.outin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDate;
import java.time.LocalDateTime;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import java.io.Serializable;

/**
 * <p>
 * 表单保存方法VO
 * 商品盘点单
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-22 14:29:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseOutinStocktakingSaveVO", description = "商品盘点单")
public class BaseOutinStocktakingSaveVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 单据来源渠道;[0-web 1-pos  2app]
     */
    @ApiModelProperty(value = "单据来源渠道")
    @Size(max = 5, message = "单据来源渠道长度不能超过{max}")
    private String sourceType;
    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;
    /**
     * 单据号
     */
    @ApiModelProperty(value = "单据号")
    @Size(max = 255, message = "单据号长度不能超过{max}")
    private String code;
    /**
     * 单据日期yyyy-mm-dd
     */
    @ApiModelProperty(value = "单据日期yyyy-mm-dd")
    private LocalDate billDate;
    /**
     * 单据状态   0正常结算  1挂单
     */
    @ApiModelProperty(value = "单据状态   0正常结算  1挂单")
    private Integer billState;

    @ApiModelProperty(value = "审核状态 0-待审核, 1-已审核, 2-作废")
    private Integer state;
    /**
     * 所属门店ID
     */
    @ApiModelProperty(value = "所属门店ID")
    private Long orgId;
    /**
     * 员工id，用于记录和提成相关业务员信息
     */
    @ApiModelProperty(value = "员工id，用于记录和提成相关业务员信息")
    private Long employeeId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 255, message = "备注长度不能超过{max}")
    private String remarks;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;
    /**
     * 删除标识 0 未删除 1 已删除
     */
    @ApiModelProperty(value = "删除标识 0 未删除 1 已删除")
    @NotNull(message = "请填写删除标识 0 未删除 1 已删除")
    private Integer deleteFlag;



}
