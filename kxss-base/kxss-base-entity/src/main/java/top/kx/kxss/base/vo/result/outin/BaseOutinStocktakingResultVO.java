package top.kx.kxss.base.vo.result.outin;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.base.entity.Entity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.constant.EchoDictType;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 商品盘点单
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-22 14:29:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "BaseOutinStocktakingResultVO", description = "商品盘点单")
public class BaseOutinStocktakingResultVO extends Entity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "ID")
    private Long id;

    /**
    * 单据来源渠道;[0-web 1-pos  2app]
    */
    @ApiModelProperty(value = "单据来源渠道")
    private String sourceType;
    /**
    * 仓库id
    */
    @ApiModelProperty(value = "仓库id")
    @Echo(api = EchoApi.WAREHOUSE_CLASS)
    private Long warehouseId;

    /**
     * 类型;[0-入库 2-出库]
     */
    @ApiModelProperty(value = "类型")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS,dictType = EchoDictType.Base.OUTIN_TYPE)
    private String type;
    /**
    * 单据号
    */
    @ApiModelProperty(value = "单据号")
    private String code;
    /**
    * 单据日期yyyy-mm-dd
    */
    @ApiModelProperty(value = "单据日期yyyy-mm-dd")
    private LocalDate billDate;
    /**
    * 单据状态   0正常结算  1挂单
    */
    @ApiModelProperty(value = "单据状态   0正常结算  1挂单")
    private Integer billState;

    @ApiModelProperty(value = "审核状态 0-待审核, 1-已审核, 2-作废")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS,dictType = EchoDictType.Base.BASE_OUTIN_STATE)
    private Integer state;
    /**
    * 所属门店ID
    */
    @ApiModelProperty(value = "所属门店ID")
    @Echo(api = EchoApi.ORG_ID_CLASS)
    private Long orgId;
    /**
    * 员工id，用于记录和提成相关业务员信息
    */
    @ApiModelProperty(value = "员工id，用于记录和提成相关业务员信息")
    @Echo(api = EchoApi.EMPLOYEE_CONTAIN_REMOVE_CLASS)
    private Long employeeId;
    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remarks;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime approvalTime;


    @ApiModelProperty(value = "审核列表")
    private List<BaseOutinApprovalResultVO> approvalList;

    @ApiModelProperty(value = "出入库商品明细")
    private List<BaseOutinProductResultVO> outinProductList;



}
