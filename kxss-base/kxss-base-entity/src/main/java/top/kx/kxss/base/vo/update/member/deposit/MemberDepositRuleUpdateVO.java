package top.kx.kxss.base.vo.update.member.deposit;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.kxss.base.vo.save.member.deposit.MemberDepositCardSaveVO;
import top.kx.kxss.base.vo.save.member.deposit.MemberDepositCouponSaveVO;
import top.kx.kxss.base.vo.save.member.deposit.MemberDepositProductSaveVO;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 表单修改方法VO
 * 储值规则子表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-14 09:59:09
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "MemberDepositRuleUpdateVO", description = "储值规则子表")
public class MemberDepositRuleUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    @NotNull(message = "请选择需要更新的内容")
    private Long id;
    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    @NotBlank(message = "请填写规则名称")
    private String name;

    /**
     * 储值表ID
     */
    @ApiModelProperty(value = "储值表ID")
    @NotNull(message = "请填写储值表ID")
    private Long depositId;
    /**
     * 充值金额
     */
    @ApiModelProperty(value = "充值金额")
    private BigDecimal rechargeAmount;
    /**
     * 赠送金额
     */
    @ApiModelProperty(value = "赠送金额")
    private BigDecimal giftAmount;

    @ApiModelProperty(value = "排序字段")
    private Integer sortValue;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 255, message = "备注长度不能超过{max}")
    private String remarks;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织",hidden = true)
    @JsonIgnore
    private Long createdOrgId;
    /**
     * 储值编号
     */
    @ApiModelProperty(value = "储值编号")
    private String code;

    /**
     * 免打小时
     */
    @ApiModelProperty(value = "免打小时")
    private Integer freeHours;
    /**
     * 发放总量
     */
    @ApiModelProperty(value = "发放总量")
    private Integer issueNum;
    /**
     * 限购数量
     */
    @ApiModelProperty(value = "限购数量")
    private Integer limitedNum;
    /**
     * 储值编号
     */
    @ApiModelProperty(value = "已领数量")
    private Integer claimedNum;

    /**
     * 优惠券信息
     */
    @ApiModelProperty(value = "优惠券信息", hidden = true)
    private List<MemberDepositCouponSaveVO> couponList;

    /**
     * 卡信息
     */
    @ApiModelProperty(value = "卡信息", hidden = true)
    private List<MemberDepositCardSaveVO> cardList;
    /**
     * 商品信息
     */
    @ApiModelProperty(value = "商品信息")
    private List<MemberDepositProductSaveVO> productList;

    /**
     * 会员等级
     */
    @ApiModelProperty(value = "会员等级")
    private List<String> gradeIds;


}
