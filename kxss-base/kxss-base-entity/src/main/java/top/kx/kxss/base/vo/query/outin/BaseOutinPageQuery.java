package top.kx.kxss.base.vo.query.outin;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;


/**
 * <p>
 * 表单查询条件VO
 * 商品出入库主表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-06 14:51:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseOutinPageQuery", description = "商品出入库主表")
public class BaseOutinPageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    private Long id;

    /**
     * 类型;[0-入库 2-出库]
     */
    @ApiModelProperty(value = "类型")
    private String type;


    /**
     * 库存类型
     */
    @ApiModelProperty(value = "库存类型")
    private String outinType;
    /**
     * 单据来源渠道;[0-web 1-pos  2-app]
     */
    @ApiModelProperty(value = "单据来源渠道")
    private String sourceType;
    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;

    @ApiModelProperty(value = "仓库id")
    private List<Long> warehouseIds;
    /**
     * 单据号
     */
    @ApiModelProperty(value = "单据号")
    private String code;
    /**
     * 单据日期yyyy-mm-dd
     */
    @ApiModelProperty(value = "单据日期yyyy-mm-dd")
    private LocalDate billDate;
    /**
     * 单据状态   0正常结算  1挂单
     */
    @ApiModelProperty(value = "单据状态   0正常结算  1挂单")
    private Integer billState;

    @ApiModelProperty(value = "审核状态 0-待审核, 1-已审核, 2-作废")
    private Integer state;

    /**
     * 所属门店ID
     */
    @ApiModelProperty(value = "所属门店ID")
    private Long orgId;
    /**
     * 员工id，用于记录和提成相关业务员信息
     */
    @ApiModelProperty(value = "员工id，用于记录和提成相关业务员信息")
    private Long employeeId;
    /**
     * 供应商id
     */
    @ApiModelProperty(value = "供应商id")
    private Integer supplierId;
    /**
     * 客户id  在customer表建立一个名为散客的记录，如果是散客即默认此id
     */
    @ApiModelProperty(value = "客户id  在customer表建立一个名为散客的记录，如果是散客即默认此id")
    private Integer customerId;
    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private Double amount;
    /**
     * 折扣金额
     */
    @ApiModelProperty(value = "折扣金额")
    private Double discountAmount;
    /**
     * 收款金额
     */
    @ApiModelProperty(value = "收款金额")
    private Integer payment;
    /**
     * 收款或支付类型
     */
    @ApiModelProperty(value = "收款或支付类型")
    private String payType;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织", hidden = true)
    @JsonIgnore
    private Long createdOrgId;
    /**
     * 入库日期查询类型（1 - 今天 2 - 近七天 3 - 本月）
     */
    @ApiModelProperty(value = "入库日期查询类型")
    private Integer billDateType;
    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String updateBy;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 业务ID(例如:红冲单关联主订单)
     */
    @ApiModelProperty(value = "业务ID(例如:红冲单关联主订单)")
    private Long sourceId;


}
