package top.kx.kxss.base.vo.query.outin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import java.io.Serializable;


/**
 * <p>
 * 表单查询条件VO
 * 商品调库主表
 * </p>
 *
 * <AUTHOR>
 * @date 2024-09-05 10:29:44
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseOutinAdjustmentPageQuery", description = "商品调库主表")
public class BaseOutinAdjustmentPageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    private Long id;

    /**
    * 单据来源渠道;[0-web 1-pos  2app]
    */
    @ApiModelProperty(value = "单据来源渠道")
    private String sourceType;
    /**
    * 调入仓库id
    */
    @ApiModelProperty(value = "调入仓库id")
    private Long inWarehouseId;
    /**
    * 调出仓库id
    */
    @ApiModelProperty(value = "调出仓库id")
    private Long outWarehouseId;
    /**
    * 单据号
    */
    @ApiModelProperty(value = "单据号")
    private String code;
    /**
    * 单据日期yyyy-mm-dd
    */
    @ApiModelProperty(value = "单据日期yyyy-mm-dd")
    private LocalDate billDate;


    @ApiModelProperty(value = "审核状态 0-待审核, 1-已审核, 2-作废")
    private Integer state;
    /**
    * 员工id
    */
    @ApiModelProperty(value = "员工id")
    private Long employeeId;
    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;



}
