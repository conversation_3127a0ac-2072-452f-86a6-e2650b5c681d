package top.kx.kxss.base.vo.query.snapshot;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;


/**
 * <p>
 * 表单查询条件VO
 * 业务镜像日志
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-27 11:46:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseSnapshotPageQuery", description = "业务镜像日志")
public class BaseSnapshotPageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
    * 操作模块
    */
    @ApiModelProperty(value = "操作模块")
    private String bizModule;
    /**
    * 操作类型：新增、修改、删除
    */
    @ApiModelProperty(value = "操作类型：新增、修改、删除")
    private String operationType;
    /**
    * 来源
    */
    @ApiModelProperty(value = "来源")
    private String source;
    /**
    * 操作时的数据快照（字符串）
    */
    @ApiModelProperty(value = "操作时的数据快照（字符串）")
    private String dataSnapshot;
    /**
    * 操作员工ID
    */
    @ApiModelProperty(value = "操作员工ID")
    private Long employeeId;

    @ApiModelProperty(value = "操作员工")
    private String employee;

    @ApiModelProperty(value = "创建时间开始")
    private LocalDateTime startCreatedTime;

    @ApiModelProperty(value = "创建时间结束")
    private LocalDateTime endCreatedTime;
    /**
    * 操作描述
    */
    @ApiModelProperty(value = "操作描述")
    private String description;
    /**
    * 备注
    */
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
    * 创建人组织
    */
    @ApiModelProperty(value = "创建人组织")
    private Long createdOrgId;



}
