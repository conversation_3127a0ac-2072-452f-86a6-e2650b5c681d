package top.kx.kxss.base.entity.member.card;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 会员权益卡
 * </p>
 *
 * <AUTHOR>
 * @date 2024-03-21 17:34:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("member_card")
public class MemberCard extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 会员ID
     */
    @TableField(value = "member_id", condition = EQUAL)
    private Long memberId;
    /**
     * 名称
     */
    @TableField(value = "name", condition = LIKE)
    private String name;
    /**
     * 卡类型 DISCOUNT 折扣卡 DURATION 时长卡  TERM 期限卡 METERING 计次卡 STORED 储值卡
     */
    @TableField(value = "type_", condition = LIKE)
    private String type;
    /**
     * 销售价格
     */
    @TableField(value = "price", condition = EQUAL)
    private BigDecimal price;
    /**
     * 状态 NO_ACTIVATE 未激活  IN_USE 使用中 EXPIRED 已过期
     */
    @TableField(value = "status", condition = LIKE)
    private String status;
    /**
     * 有效期类型: 0永久有效 1某个日期前有效 2购买后某天后有效 3首次使用后某天有效
     */
    @TableField(value = "validity_type", condition = LIKE)
    private String validityType;
    /**
     * 有效日期
     */
    @TableField(value = "validity_date", condition = EQUAL)
    private LocalDate validityDate;
    /**
     * 购买后有效天数
     */
    @TableField(value = "buy_validity_day", condition = EQUAL)
    private Integer buyValidityDay;
    /**
     * 首次使用后有效天数
     */
    @TableField(value = "first_validity_day", condition = EQUAL)
    private Integer firstValidityDay;
    /**
     * 卡颜色
     */
    @TableField(value = "background_color", condition = LIKE)
    private String backgroundColor;
    /**
     * 卡图片
     */
    @TableField(value = "background_image", condition = LIKE)
    private String backgroundImage;
    /**
     * 是否设置颜色(0 自定义图片 1 背景色)
     */
    @TableField(value = "is_custom_background", condition = EQUAL)
    private Boolean isCustomBackground;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 每日消费上限(储值卡除外)
     */
    @TableField(value = "daily_usage_limit", condition = EQUAL)
    private Integer dailyUsageLimit;
    /**
     * 星期
     */
    @TableField(value = "weeks", condition = LIKE)
    private String weeks;
    /**
     * 面值(储值卡)
     */
    @TableField(value = "amount", condition = EQUAL)
    private BigDecimal amount;
    /**
     * 使用天数(期限卡). 使用次数(计次卡). 抵扣值(计次卡). 折扣值(折扣卡)
     */
    @TableField(value = "value", condition = EQUAL)
    private Integer value;
    /**
     * 抵扣值(计次卡)
     */
    @TableField(value = "deduct_value", condition = EQUAL)
    private Integer deductValue;
    /**
     * 是否节假日可用
     */
    @TableField(value = "is_holiday", condition = EQUAL)
    private Boolean isHoliday;
    /**
     * 是否叠加会员等级权益
     */
    @TableField(value = "is_superpose", condition = EQUAL)
    private Boolean isSuperpose;
    /**
     * 原价
     */
    @TableField(value = "original_amount", condition = EQUAL)
    private BigDecimal originalAmount;
    /**
     * 卡使用次数
     */
    @TableField(value = "usage_times", condition = EQUAL)
    private Integer usageTimes;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;
    /**
     * 排序
     */
    @TableField(value = "sort_value", condition = EQUAL)
    private Integer sortValue;

    /**
     * 卡ID
     */
    @TableField(value = "card_id", condition = EQUAL)
    private Long cardId;

    /**
     * 会员卡编号
     */
    @TableField(value = "code", condition = EQUAL)
    private String code;

    @TableField(exist = false)
    private Long cashCardId;
    /**
     * 过期时间
     */
    @TableField(value = "expiration_time", condition = EQUAL)
    private LocalDateTime expirationTime;
    /**
     * 卡已使用使用值
     */
    @TableField(value = "usage_value", condition = EQUAL)
    private Integer usageValue;
    /**
     * 旧的面值
     */
    @TableField(value = "old_amount", condition = EQUAL)
    private BigDecimal oldAmount;

    /**
     * 旧的使用天数(期限卡). 使用次数(计次卡). 抵扣值(计次卡). 折扣值(折扣卡)
     */
    @TableField(value = "old_value", condition = EQUAL)
    private Integer oldValue;

    /**
     * 剩余金额
     */
    @TableField(value = "remaining_price", condition = EQUAL)
    private BigDecimal remainingPrice;


    /**
     * 是否禁用
     */
    @TableField(value = "is_disable", condition = EQUAL)
    private Boolean isDisable;

    /**
     * 单笔抵扣值
     */
    @TableField(value = "single_deduct_value", condition = EQUAL)
    private Integer singleDeductValue;

    /**
     * 业务 ID
     */
    @TableField(value = "biz_id", condition = EQUAL)
    private Long bizId;

    /**
     * 来源 ID
     */
    @TableField(value = "source_id", condition = EQUAL)
    private Long sourceId;

    /**
     * 来源类型
     */
    @TableField(value = "source_type", condition = EQUAL)
    private String sourceType;


}
