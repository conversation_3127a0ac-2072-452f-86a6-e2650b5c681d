package top.kx.kxss.base.vo.query.payment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;


/**
 * <p>
 * 表单查询条件VO
 * 支付类型
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-19 14:55:43
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BasePaymentTypePageQuery", description = "支付类型")
public class BasePaymentTypePageQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
    * 名称
    */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
    * 是否启用
    */
    @ApiModelProperty(value = "是否启用")
    private Boolean state;
    /**
    * 类型
    */
    @ApiModelProperty(value = "类型")
    private String type;
    /**
     * 类型
     */
    @ApiModelProperty(value = "业务类型")
    private String  payChannel;
    /**
    * 修改时间
    */
    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updatedTime;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;
    /**
    * 业务类型 0 直接支付 1 账户支付 2聚合支付
    */
    @ApiModelProperty(value = "业务类型 0 直接支付 1 账户支付 2聚合支付")
    private String bizType;

    private Long createdOrgId;

    private List<Long> orgIdList;



}
