package top.kx.kxss.base.vo.update.outin;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.kxss.base.vo.save.outin.BaseOutinProductSaveVO;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 表单修改方法VO
 * 商品出入库主表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-06 14:51:59
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseOutinUpdateVO", description = "商品出入库主表")
public class BaseOutinUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @NotNull(message = "请填写ID", groups = SuperEntity.Update.class)
    private Long id;

    /**
     * 仓库id
     */
    @ApiModelProperty(value = "仓库id")
    private Long warehouseId;
    /**
     * 单据号
     */
    @ApiModelProperty(value = "单据号")
    @Size(max = 255, message = "单据号长度不能超过{max}")
    private String code;
    /**
     * 单据日期yyyy-mm-dd
     */
    @ApiModelProperty(value = "单据日期yyyy-mm-dd")
    private LocalDate billDate;
    /**
     * 单据状态   0正常结算  1挂单
     */
    @ApiModelProperty(value = "单据状态   0正常结算  1挂单")
    private Integer billState;

    @ApiModelProperty(value = "审核状态 0-待审核, 1-已审核, 2-作废")
    private Integer state;

    /**
     * 所属门店ID
     */
    @ApiModelProperty(value = "所属门店ID")
    private Long orgId;
    /**
     * 员工id，用于记录和提成相关业务员信息
     */
    @ApiModelProperty(value = "员工id，用于记录和提成相关业务员信息")
    private Long employeeId;
    /**
     * 供应商id
     */
    @ApiModelProperty(value = "供应商id")
    private Long supplierId;
    /**
     * 客户id  在customer表建立一个名为散客的记录，如果是散客即默认此id
     */
    @ApiModelProperty(value = "客户id  在customer表建立一个名为散客的记录，如果是散客即默认此id")
    private Long customerId;
    /**
     * 金额
     */
    @ApiModelProperty(value = "金额")
    private Double amount;
    /**
     * 折扣金额
     */
    @ApiModelProperty(value = "折扣金额")
    private Double discountAmount;
    /**
     * 收款金额
     */
    @ApiModelProperty(value = "收款金额")
    private Integer payment;
    /**
     * 收款或支付类型
     */
    @ApiModelProperty(value = "收款或支付类型")
    @Size(max = 50, message = "收款或支付类型长度不能超过{max}")
    private String payType;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Size(max = 255, message = "备注长度不能超过{max}")
    private String remarks;
    /**
     * 创建人组织
     */
    @ApiModelProperty(value = "创建人组织",hidden = true)
    @JsonIgnore
    private Long createdOrgId;

    /**
     * 出入库商品明细
     */
    @ApiModelProperty(value = "出入库商品明细")
    @NotEmpty(message = "请选择商品")
    private List<BaseOutinProductSaveVO> outinProductList;

    /**
     * 业务ID(例如:红冲单关联主订单)
     */
    @ApiModelProperty(value = "业务ID(例如:红冲单关联主订单)")
    private Long sourceId;


}
