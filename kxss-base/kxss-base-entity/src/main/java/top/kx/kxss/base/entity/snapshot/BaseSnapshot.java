package top.kx.kxss.base.entity.snapshot;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 业务镜像日志
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-27 11:46:01
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_snapshot")
public class BaseSnapshot extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 操作模块
     */
    @TableField(value = "biz_module", condition = EQUAL)
    private String bizModule;
    /**
     * 操作类型：新增、修改、删除
     */
    @TableField(value = "operation_type", condition = EQUAL)
    private String operationType;
    /**
     * 来源
     */
    @TableField(value = "source", condition = LIKE)
    private String source;
    /**
     * 操作时的数据快照（字符串）
     */
    @TableField(value = "data_snapshot", condition = LIKE)
    private String dataSnapshot;

    /**
     * 原数据（JSON格式）
     */
    @TableField(value = "old_data", condition = LIKE)
    private String oldData;

    /**
     * 新数据（JSON格式）
     */
    @TableField(value = "new_data", condition = LIKE)
    private String newData;

    /**
     * 变动字段（JSON格式，格式：{"fieldName": {"oldValue": "xxx", "newValue": "xxx", "fieldDesc": "xxx"}}）
     */
    @TableField(value = "changed_fields", condition = LIKE)
    private String changedFields;

    /**
     * 操作员工ID
     */
    @TableField(value = "employee_id", condition = EQUAL)
    private Long employeeId;
    /**
     * 操作描述
     */
    @TableField(value = "description", condition = LIKE)
    private String description;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;



}
