package top.kx.kxss.base.entity.outin;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;
import top.kx.basic.base.entity.Entity;


/**
 * <p>
 * 实体类
 * 商品盘点单
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-22 14:29:30
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_outin_stocktaking")
public class BaseOutinStocktaking extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 单据来源渠道;[0-web 1-pos  2app]
     */
    @TableField(value = "source_type", condition = LIKE)
    private String sourceType;
    /**
     * 仓库id
     */
    @TableField(value = "warehouse_id", condition = EQUAL)
    private Long warehouseId;
    /**
     * 单据号
     */
    @TableField(value = "code", condition = LIKE)
    private String code;
    /**
     * 单据日期yyyy-mm-dd
     */
    @TableField(value = "bill_date", condition = EQUAL)
    private LocalDate billDate;
    /**
     * 单据状态   0正常结算  1挂单
     */
    @TableField(value = "bill_state", condition = EQUAL)
    private Integer billState;
    /**
     * 审核状态 0-待审核, 1-已审核, 2-作废
     */
    @TableField(value = "state", condition = EQUAL)
    private Integer state;
    /**
     * 所属门店ID
     */
    @TableField(value = "org_id", condition = EQUAL)
    private Long orgId;
    /**
     * 员工id，用于记录和提成相关业务员信息
     */
    @TableField(value = "employee_id", condition = EQUAL)
    private Long employeeId;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;
    /**
     * 删除标识 0 未删除 1 已删除
     */
    @TableField(value = "delete_flag", condition = EQUAL)
    private Integer deleteFlag;



}
