package top.kx.kxss.base.vo.query.outin;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.kxss.model.constant.EchoApi;

import java.io.Serializable;


/**
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "CashProductOutinQuery", description = "")
public class SellOutinQuery /*extends DataOverviewQuery */implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "模糊查询")
    private String keyword;

    @ApiModelProperty(value = "结账时间")
    private String completeTime_ed;

    @ApiModelProperty(value = "结账时间")
    private String completeTime_st;


    @ApiModelProperty(value = "操作时间/出库时间")
    private String createdTime_ed;

    @ApiModelProperty(value = "操作时间/出库时间")
    private String createdTime_st;

    @ApiModelProperty(value = "员工")
    private String updateBy;


}
