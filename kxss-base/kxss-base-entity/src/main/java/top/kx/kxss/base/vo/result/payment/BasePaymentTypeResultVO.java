package top.kx.kxss.base.vo.result.payment;

import cn.hutool.core.map.MapUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.annotation.echo.Echo;
import top.kx.basic.annotation.log.FieldChangeLog;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.interfaces.echo.EchoVO;
import top.kx.kxss.base.vo.BizTypeVO;
import top.kx.kxss.model.constant.EchoApi;
import top.kx.kxss.model.constant.EchoDictType;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 表单查询方法返回值VO
 * 支付类型
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-19 14:55:43
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@ApiModel(value = "BasePaymentTypeResultVO", description = "支付类型")
public class BasePaymentTypeResultVO extends SuperEntity<Long> implements Serializable, EchoVO {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> echoMap = MapUtil.newHashMap();

    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 名称
     */
    @FieldChangeLog("名称")
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 是否启用
     */
    @FieldChangeLog(value = "是否启用", enumMapping = "true-启用,false-停用,_null-未知")
    @ApiModelProperty(value = "是否启用")
    private Boolean state;
    /**
     * 是否小程序启用
     */
    @FieldChangeLog(value = "是否小程序启用", enumMapping = "true-启用,false-停用,_null-未知")
    @ApiModelProperty(value = "是否小程序启用")
    private Boolean isApplet;
    /**
     * 类型
     */
    @FieldChangeLog("类型")
    @ApiModelProperty(value = "类型")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.PAYMENT_TYPE)
    private String type;
    /**
     * 自定义图标图片ID
     */
    @ApiModelProperty(value = "自定义图标图片ID")
    private String icon;
    /**
     * 图标颜色
     */
    @ApiModelProperty(value = "图标颜色")
    private String customColor;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;
    /**
     * 业务类型 0 直接支付 1 账户支付 2聚合支付
     */
    @FieldChangeLog("支付类型")
    @ApiModelProperty(value = "业务类型 0 直接支付 1 账户支付 2聚合支付")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.PAYMENT_BIZ_TYPE)
    private String bizType;

    /**
     * 可用范围 使用4位二进制表述 台费 商品 服务 权益卡
     */
    @FieldChangeLog("支付场景")
    @ApiModelProperty(value = "可用范围 使用4位二进制表述 台费 商品 服务 权益卡")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.USABLE_RANGE)
    private List<String> usableRange;

    /**
     * 支付渠道
     */
    @FieldChangeLog("支付渠道")
    @ApiModelProperty(value = "可用范围 使用4位二进制表述 台费 商品 服务 权益卡")
    @Echo(api = EchoApi.DICTIONARY_ITEM_FEIGN_CLASS, dictType = EchoDictType.Base.PAY_CHANNEL)
    private String payChannel;

    /**
     * 排除项目列表
     */
    @ApiModelProperty(value = "排除项目列表")
    private List<BizTypeVO> excludeList;

    /**
     * 排序
     */
    @FieldChangeLog("排序")
    @ApiModelProperty(value = "排除项目列表")
    private Integer sortValue;

    @FieldChangeLog("支付费率")
    @ApiModelProperty(value = "支付费率")
    private BigDecimal feeRate;

    /**
     * 收入规则 1 计入收入 2 计入优惠
     */
    @FieldChangeLog(value = "收入规则", enumMapping = "1-计入收入,2-计入优惠,_null-未设置")
    @ApiModelProperty(value = "收入规则 1 计入收入 2 计入优惠")
    private Integer incomeFlag;

    /**
     * 赠金收入规则 1 计入收入 2 计入优惠
     */
    @FieldChangeLog(value = "赠金收入规则", enumMapping = "1-计入收入,2-计入优惠,_null-未设置")
    @ApiModelProperty(value = "赠金收入规则 1 计入收入 2 计入优惠")
    private Integer giftIncomeFlag;

    @ApiModelProperty(value = "删除")
    private Integer deleteFlag;

}
