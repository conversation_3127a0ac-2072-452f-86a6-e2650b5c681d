package top.kx.kxss.base.entity.payment;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.Entity;

import java.math.BigDecimal;
import java.time.LocalDate;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 银行卡信息
 * </p>
 *
 * <AUTHOR>
 * @date 2025-07-04 15:36:19
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_bank_card_info")
public class BaseBankCardInfo extends Entity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 进件商户编号
     */

    @TableField(value = "merchant_code", condition = LIKE)
    private String merchantCode;
    /**
     * 平台应用ID
     */
    @TableField(value = "platform_app_id", condition = LIKE)
    private String platformAppId;
    /**
     * 法人/商户
     */
    @TableField(value = "merchant_name", condition = LIKE)
    private String merchantName;
    /**
     * 收款人姓名
     */
    @TableField(value = "payee_name", condition = LIKE)
    private String payeeName;
    /**
     * 收款类型: 对私/对公
     */
    @TableField(value = "payee_type", condition = LIKE)
    private String payeeType;
    /**
     * 银行卡支行名称
     */
    @TableField(value = "bank_name", condition = LIKE)
    private String bankName;
    /**
     * 银行卡号
     */
    @TableField(value = "card_number", condition = LIKE)
    private String cardNumber;
    /**
     * 支付平台: 天网支付等
     */
    @TableField(value = "pay_channel", condition = LIKE)
    private String payChannel;
    /**
     * 状态: 0-禁用, 1-启用
     */
    @TableField(value = "status", condition = EQUAL)
    private Boolean status;
    /**
     * 手续费率
     */
    @TableField(value = "fee_rate", condition = EQUAL)
    private BigDecimal feeRate;
    /**
     * 生效日期
     */
    @TableField(value = "effective_date", condition = EQUAL)
    private LocalDate effectiveDate;
    /**
     * 备注信息
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;

    /**
     * 支付平台
     */
    @TableField(value = "payment_platform", condition = EQUAL)
    private String paymentPlatform;



}
