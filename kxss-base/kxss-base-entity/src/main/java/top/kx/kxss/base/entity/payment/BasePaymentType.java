package top.kx.kxss.base.entity.payment;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;
import top.kx.basic.base.entity.SuperEntity;

import java.math.BigDecimal;

import static com.baomidou.mybatisplus.annotation.SqlCondition.EQUAL;
import static top.kx.kxss.model.constant.Condition.LIKE;


/**
 * <p>
 * 实体类
 * 支付类型
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-19 14:55:43
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Builder
@TableName("base_payment_type")
public class BasePaymentType extends SuperEntity<Long> {
    private static final long serialVersionUID = 1L;

    /**
     * 名称
     */
    @TableField(value = "name", condition = LIKE)
    private String name;
    /**
     * 是否启用
     */
    @TableField(value = "state", condition = EQUAL)
    private Boolean state;
    /**
     * 是否小程序使用
     */
    @TableField(value = "is_applet", condition = EQUAL)
    private Boolean isApplet;
    /**
     * 类型
     */
    @TableField(value = "type", condition = LIKE)
    private String type;
    /**
     * 自定义图标图片ID
     */
    @TableField(value = "icon", condition = EQUAL)
    private String icon;
    /**
     * 图标颜色
     */
    @TableField(value = "custom_color", condition = LIKE)
    private String customColor;
    /**
     * 备注
     */
    @TableField(value = "remarks", condition = LIKE)
    private String remarks;
    /**
     * 创建人组织
     */
    @TableField(value = "created_org_id", condition = EQUAL)
    private Long createdOrgId;
    /**
     * 删除标识 0 未删除 1 已删除
     */
    @TableField(value = "delete_flag", condition = EQUAL)
    private Integer deleteFlag;
    /**
     * 业务类型 0 直接支付 1 账户支付 2聚合支付
     */
    @TableField(value = "biz_type", condition = LIKE)
    private String bizType;

    /**
     * 可用范围 使用4位二进制表述 台费 商品 服务 权益卡
     */
    @TableField(value = "usable_range", condition = LIKE)
    private String usableRange;

    /**
     * 排序
     */
    @TableField(value = "sort_value", condition = LIKE)
    private Integer sortValue;
    /**
     * 支付渠道
     */
    @TableField(value = "pay_channel", condition = LIKE)
    private String payChannel;
    /**
     * 支付费率
     */
    @TableField(value = "fee_rate", condition = LIKE)
    private BigDecimal feeRate;
    /**
     * 收入规则 1 计入收入 2 计入优惠
     */
    @TableField(value = "income_flag", condition = LIKE)
    private Integer incomeFlag;

    /**
     * 赠金收入规则 1 计入收入 2 计入优惠
     */
    @TableField(value = "gift_income_flag", condition = LIKE)
    private Integer giftIncomeFlag;


}
