package top.kx.kxss.base.vo.update.ad;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import lombok.experimental.Accessors;
import org.springframework.web.multipart.MultipartFile;
import top.kx.basic.base.entity.SuperEntity;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 表单修改方法VO
 * 广告
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-14 17:02:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
@Accessors(chain = true)
@EqualsAndHashCode
@Builder
@ApiModel(value = "BaseAdSortValueUpdateVO", description = "广告")
public class BaseAdSortValueUpdateVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @NotNull(message = "请填写ID", groups = SuperEntity.Update.class)
    private Long id;


    /**
     * 排序
     */
    @ApiModelProperty(value = "排序")
    @NotNull(message = "请填写排序")
    private Integer sortValue;


}
