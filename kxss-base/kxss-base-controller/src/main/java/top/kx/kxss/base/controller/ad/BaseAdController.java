package top.kx.kxss.base.controller.ad;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.ad.BaseAd;
import top.kx.kxss.base.entity.ad.BaseAdTable;
import top.kx.kxss.base.service.ad.BaseAdService;
import top.kx.kxss.base.service.ad.BaseAdTableService;
import top.kx.kxss.base.vo.query.ad.BaseAdPageQuery;
import top.kx.kxss.base.vo.result.ad.BaseAdResultVO;
import top.kx.kxss.base.vo.save.ad.BaseAdSaveVO;
import top.kx.kxss.base.vo.update.ad.BaseAdSortValueUpdateVO;
import top.kx.kxss.base.vo.update.ad.BaseAdUpdateVO;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 广告
 * </p>
 *
 * <AUTHOR>
 * @date 2025-03-14 17:02:36
 * @create [2025-03-14 17:02:36] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseAd")
@Api(value = "BaseAd", tags = "广告")
public class BaseAdController extends SuperController<BaseAdService, Long, BaseAd, BaseAdSaveVO,
        BaseAdUpdateVO, BaseAdPageQuery, BaseAdResultVO> {
    private final EchoService echoService;
    private final BaseAdTableService baseAdTableService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }


    @Override
    public R<IPage<BaseAdResultVO>> page(PageParams<BaseAdPageQuery> params) {
        params.setSort("");
        params.setOrder("");
        return super.page(params);
    }

    @Override
    public IPage<BaseAd> query(PageParams<BaseAdPageQuery> params) {
        params.setSort("");
        params.setOrder("");
        return super.query(params);
    }

    @Override
    public QueryWrap<BaseAd> handlerWrapper(BaseAd model, PageParams<BaseAdPageQuery> params) {
        QueryWrap<BaseAd> wrap = super.handlerWrapper(model, params);
        wrap.lambda().eq(BaseAd::getCreatedOrgId, ContextUtil.getCurrentCompanyId());
        wrap.lambda().orderByAsc(BaseAd::getSortValue).orderByAsc(SuperEntity::getId);
        return wrap;
    }

    @Override
    public void handlerResult(IPage<BaseAdResultVO> page) {
        if (CollUtil.isNotEmpty(page.getRecords())) {
            List<Long> ids = page.getRecords().stream().map(BaseAdResultVO::getId).distinct().collect(Collectors.toList());
            List<BaseAdTable> baseAdTableList = baseAdTableService.list(Wraps.<BaseAdTable>lbQ().in(BaseAdTable::getAdId, ids).eq(BaseAdTable::getCreatedOrgId, ContextUtil.getCurrentCompanyId()));
            if (CollUtil.isNotEmpty(baseAdTableList)) {
                Map<Long, List<Long>> tableMap = baseAdTableList.stream().collect(Collectors.groupingBy(BaseAdTable::getAdId, Collectors.mapping(BaseAdTable::getTableId, Collectors.toList())));
                page.getRecords().forEach(item -> {
                    if (CollUtil.isNotEmpty(tableMap.get(item.getId()))) {
                        item.setTableIds(tableMap.get(item.getId()));
                    }
                });
            }
        }
        super.handlerResult(page);
    }

    /**
     * 批量修改sortValue
     */
    @ApiOperation(value = "批量修改sortValue", notes = "批量修改sortValue")
    @PostMapping("/batchUpdateSortValue")
    public R<Boolean> batchUpdateSortValue(@RequestBody List<BaseAdSortValueUpdateVO> model) {
        return R.success(superService.batchUpdateSortValue(model));
    }

    /**
     * 根据台桌id查询广告
     * 1. 返回所有未绑定过台桌的广告
     * 2. 返回绑定过台桌的广告，但只返回绑定到指定台桌的广告
     *
     * @param tableId 台桌ID
     * @return 广告列表
     */
    @ApiOperation(value = "根据台桌id查询广告", notes = "查询列表")
    @GetMapping("/queryList/{tableId}")
    public R<List<BaseAdResultVO>> queryList(@PathVariable Long tableId) {
        // 使用一次查询，通过OR条件组合未绑定和已绑定的条件
        List<BaseAd> ads = superService.list(Wraps.<BaseAd>lbQ()
                .eq(BaseAd::getState, true)
                        .eq(BaseAd::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .and(wrapper -> wrapper
                    .eq(BaseAd::getIsBindTable, false)
                    .or()
                    .nested(w -> w
                        .eq(BaseAd::getIsBindTable, true)
                        .exists("SELECT 1 FROM base_ad_table t WHERE t.ad_id = base_ad.id AND t.table_id = {0} AND t.delete_flag = 0", tableId)
                    )
                ).orderByAsc(BaseAd::getSortValue).orderByAsc(SuperEntity::getId));

        // 转换为VO并返回
        return R.success(BeanPlusUtil.toBeanList(ads, BaseAdResultVO.class));
    }


    @Override
    public R<BaseAdResultVO> getDetail(Long aLong) {
        BaseAd baseAd = superService.getById(aLong);
        if (Objects.isNull(baseAd)) {
            return R.success(null);
        }
        BaseAdResultVO baseAdResultVO = BeanPlusUtil.copyProperties(baseAd, BaseAdResultVO.class);
        List<BaseAdTable> baseAdTableList = baseAdTableService.list(Wraps.<BaseAdTable>lbQ().eq(BaseAdTable::getAdId, baseAdResultVO.getId()));
        if (CollUtil.isNotEmpty(baseAdTableList)) {
            baseAdResultVO.setTableIds(baseAdTableList.stream().map(BaseAdTable::getTableId).collect(Collectors.toList()));
        }
        return R.success(baseAdResultVO);
    }
}


