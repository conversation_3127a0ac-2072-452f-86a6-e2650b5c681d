package top.kx.kxss.base.controller.system;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperCacheController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.annotation.OperationLog;
import top.kx.kxss.base.biz.system.BaseRoleBiz;
import top.kx.kxss.base.entity.system.BaseRole;
import top.kx.kxss.base.entity.user.BaseOrg;
import top.kx.kxss.base.service.system.BaseRoleService;
import top.kx.kxss.base.service.user.BaseOrgService;
import top.kx.kxss.base.vo.query.system.BaseRolePageQuery;
import top.kx.kxss.base.vo.result.system.BaseRoleResultVO;
import top.kx.kxss.base.vo.save.system.BaseRoleResourceRelSaveVO;
import top.kx.kxss.base.vo.save.system.BaseRoleSaveVO;
import top.kx.kxss.base.vo.save.system.RoleEmployeeSaveVO;
import top.kx.kxss.base.vo.update.system.BaseRoleUpdateVO;
import top.kx.kxss.common.constant.BizConstant;
import top.kx.kxss.common.constant.RoleConstant;
import top.kx.kxss.model.enumeration.base.BizLogOperationTypeEnum;
import top.kx.kxss.model.enumeration.base.RoleCategoryEnum;
import top.kx.kxss.model.enumeration.base.SnapshotBizModuleEnum;

import java.util.*;
import java.util.stream.Collectors;


/**
 * <p>
 * 前端控制器
 * 角色
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/baseRole")
@Api(value = "BaseRole", tags = "角色")
public class BaseRoleController extends SuperCacheController<BaseRoleService, Long, BaseRole, BaseRoleSaveVO, BaseRoleUpdateVO, BaseRolePageQuery, BaseRoleResultVO> {

    private final EchoService echoService;
    private final BaseRoleBiz baseRoleBiz;
    private final BaseOrgService baseOrgService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Override
    public QueryWrap<BaseRole> handlerWrapper(BaseRole model, PageParams<BaseRolePageQuery> params) {
        QueryWrap<BaseRole> wrap = Wraps.q(null, params.getExtra(), getEntityClass());
        // category = ? and state = ? and (code like ? or name like ? or remarks like ?)
        wrap.lambda()
//                .in(BaseRole::getCreatedOrgId, Arrays.asList(1L, ContextUtil.getCurrentCompanyId()))
                .and(wrapper -> wrapper.eq(BaseRole::getCode, "TENANT_ADMIN")
                        .or().eq(BaseRole::getCreatedOrgId, ContextUtil.getCurrentCompanyId()))
                .notIn(BaseRole::getCode, ListUtil.toList("SELECT_ORG", "ORG_ADMIN"))
                .eq(BaseRole::getCategory, model.getCategory())
//                .eq(BaseRole::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(BaseRole::getState, model.getState())
                .and(StrUtil.isNotEmpty(model.getName()), w ->
                        w.like(BaseRole::getCode, model.getName()).or().like(BaseRole::getName, model.getName())
                                .or().like(BaseRole::getRemarks, model.getRemarks())
                );
        return wrap;
    }

    @ApiOperation(value = "分页查询员工的角色", notes = "分页查询员工的角色")
    @PostMapping("/pageMyRole")
    @WebLog(value = "'分页查询员工的角色:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<BaseRoleResultVO>> pageMyRole(@RequestBody @Validated PageParams<BaseRolePageQuery> params) {
        IPage<BaseRole> page = params.buildPage(BaseRole.class);
        BaseRolePageQuery query = params.getModel();
        BaseRole model = BeanUtil.toBean(query, BaseRole.class);
        LbQueryWrap<BaseRole> wraps = Wraps.lbq(model, params.getExtra(), BaseRole.class);
        if (ContextUtil.getCurrentCompanyId() != 1) {
            wraps.and(wrapper -> wrapper.eq(BaseRole::getCode, "TENANT_ADMIN")
                    .or().eq(BaseRole::getCreatedOrgId, ContextUtil.getCurrentCompanyId()));
        }
        if (ObjectUtil.isNotNull(query.getOrgId())) {
            wraps.and(wrapper -> wrapper.eq(BaseRole::getCode, "TENANT_ADMIN")
                    .or().eq(BaseRole::getCreatedOrgId, query.getOrgId()));
        }
        if (StrUtil.equals(query.getScopeType(), BizConstant.SCOPE_TYPE_EMPLOYEE)) {
            if (StrUtil.equalsAny(query.getScope(), BizConstant.SCOPE_BIND, BizConstant.SCOPE_UN_BIND) && query.getEmployeeId() != null) {
                String sql = "select 1 from base_employee_role_rel er where  er.role_id = base_role.id and er.employee_id = {0}";
                if (BizConstant.SCOPE_BIND.equals(query.getScope())) {
                    wraps.exists(sql, query.getEmployeeId());
                } else {
                    wraps.notExists(sql, query.getEmployeeId());
                }
            }
        } else if (StrUtil.equals(query.getScopeType(), BizConstant.SCOPE_TYPE_ORG)) {
            if (StrUtil.equalsAny(query.getScope(), BizConstant.SCOPE_BIND, BizConstant.SCOPE_UN_BIND) && query.getOrgId() != null) {
                String sql = "select 1 from base_org_role_rel er where  er.role_id = base_role.id and er.org_id = {0}";
                if (BizConstant.SCOPE_BIND.equals(query.getScope())) {
                    wraps.exists(sql, query.getOrgId());
                } else {
                    wraps.notExists(sql, query.getOrgId());
                }
            }
        }

        superService.page(page, wraps);
        IPage<BaseRoleResultVO> pageVO = BeanPlusUtil.toBeanPage(page, BaseRoleResultVO.class);
        echoService.action(pageVO);
        return R.success(pageVO);
    }

    @ApiOperation(value = "查询员工的角色", notes = "查询员工的角色")
    @PostMapping("/myRoleList")
    public R<List<BaseRoleResultVO>> myRoleList(@RequestBody @Validated BaseRolePageQuery query) {
        LbQueryWrap<BaseRole> wraps = Wraps.<BaseRole>lbQ();
        wraps.eq(BaseRole::getDeleteFlag, 0);
        if (ContextUtil.getCurrentCompanyId() != 1) {
            wraps.eq(BaseRole::getCreatedOrgId, ContextUtil.getCurrentCompanyId());
        }
        if (ObjectUtil.isNotNull(query.getOrgId())) {
            wraps.eq(BaseRole::getCreatedOrgId, query.getOrgId());
        }
        wraps.like(StrUtil.isNotBlank(query.getCode()), BaseRole::getCode, query.getCode());
        wraps.like(StrUtil.isNotBlank(query.getName()), BaseRole::getName, query.getName());
        if (StrUtil.equals(query.getScopeType(), BizConstant.SCOPE_TYPE_EMPLOYEE)) {
            if (StrUtil.equalsAny(query.getScope(), BizConstant.SCOPE_BIND, BizConstant.SCOPE_UN_BIND) && query.getEmployeeId() != null) {
                String sql = "select 1 from base_employee_role_rel er where  er.role_id = base_role.id and er.employee_id = {0}";
                if (BizConstant.SCOPE_BIND.equals(query.getScope())) {
                    wraps.exists(sql, query.getEmployeeId());
                } else {
                    wraps.notExists(sql, query.getEmployeeId());
                }
            }
        } else if (StrUtil.equals(query.getScopeType(), BizConstant.SCOPE_TYPE_ORG)) {
            if (StrUtil.equalsAny(query.getScope(), BizConstant.SCOPE_BIND, BizConstant.SCOPE_UN_BIND) && query.getOrgId() != null) {
                String sql = "select 1 from base_org_role_rel er where  er.role_id = base_role.id and er.org_id = {0}";
                if (BizConstant.SCOPE_BIND.equals(query.getScope())) {
                    wraps.exists(sql, query.getOrgId());
                } else {
                    wraps.notExists(sql, query.getOrgId());
                }
            }
        }
        List<BaseRole> list = Optional.ofNullable(superService.list(wraps)).orElse(Lists.newArrayList());
        List<BaseRoleResultVO> baseRoleResultVOList = BeanUtil.copyToList(list, BaseRoleResultVO.class);
        BaseRole baseRole = superService.getOne(Wraps.<BaseRole>lbQ().eq(BaseRole::getDeleteFlag, 0)
                .eq(BaseRole::getCode, RoleConstant.TENANT_ADMIN));
        if (baseRole == null) {
            echoService.action(baseRoleResultVOList);
            return R.success(baseRoleResultVOList);
        }
        LbQueryWrap<BaseOrg> eq = Wraps.<BaseOrg>lbQ().eq(BaseOrg::getDeleteFlag, 0);
        if (ContextUtil.getCurrentCompanyId() != 1) {
            eq.eq(BaseOrg::getId, ContextUtil.getCurrentCompanyId());
        }
        if (ObjectUtil.isNotNull(query.getOrgId())) {
            eq.eq(BaseOrg::getId, query.getOrgId());
        }
        List<BaseOrg> baseOrgList = baseOrgService.list(eq);
        Map<Long, List<BaseRoleResultVO>> baseRoleMap = baseRoleResultVOList.stream().collect(Collectors.groupingBy(BaseRoleResultVO::getCreatedOrgId));
        for (BaseOrg baseOrg : baseOrgList) {
            List<BaseRoleResultVO> resultVO = Optional.ofNullable(baseRoleMap.get(baseOrg.getId())).orElse(Lists.newArrayList());
            if (resultVO.stream().noneMatch(e -> e.getCode().equals(RoleConstant.TENANT_ADMIN))) {
                BaseRoleResultVO baseRoleResultVO = BeanUtil.copyProperties(baseRole, BaseRoleResultVO.class);
                baseRoleResultVO.setCreatedOrgId(baseOrg.getId());
                baseRoleResultVOList.add(baseRoleResultVO);
            }
        }

        baseRoleResultVOList.forEach(e -> {
            e.setKey(String.valueOf(e.getId()).concat("_")
                    .concat(String.valueOf(e.getCreatedOrgId())));
        });
        baseRoleResultVOList.sort(Comparator.comparing(BaseRoleResultVO::getCreatedOrgId).reversed()
                .thenComparing(BaseRoleResultVO::getCreatedTime).reversed());
        echoService.action(baseRoleResultVOList);
        return R.success(baseRoleResultVOList);
    }


    @Override
    public void handlerResult(IPage<BaseRoleResultVO> page) {
        List<BaseRoleResultVO> records = page.getRecords();
        if (CollUtil.isNotEmpty(records)) {
            List<BaseRoleResultVO> baseRoleResultVOList = superService.findEmployeeNum(new ArrayList<>(), ContextUtil.getCurrentCompanyId());
            Map<Long, BaseRoleResultVO> baseRoleResultVOMap = baseRoleResultVOList.stream().collect(Collectors.toMap(BaseRoleResultVO::getId, e -> e));
            records.forEach(e -> {
                BaseRoleResultVO baseRoleResultVO = baseRoleResultVOMap.get(e.getId());
                if (Objects.nonNull(baseRoleResultVO)) {
                    e.setEmployeeNum(baseRoleResultVO.getEmployeeNum());
                } else {
                    e.setEmployeeNum(0L);
                }
            });
        }
        super.handlerResult(page);
    }

    @ApiOperation(value = "检测角色编码")
    @GetMapping("/check")
    @WebLog("检测角色编码")
    public R<Boolean> check(@RequestParam String code, @RequestParam(required = false) Long id) {
        return success(superService.check(code, id));
    }


    /**
     * 给角色分配员工
     *
     * @param roleEmployeeSaveVO 给角色分配员工参数
     * @return 新增结果
     */
    @ApiOperation(value = "给角色分配员工", notes = "给角色分配员工")
    @PostMapping("/roleEmployee")
    @WebLog("给角色分配用户")
    public R<List<Long>> saveRoleEmployee(@RequestBody RoleEmployeeSaveVO roleEmployeeSaveVO) {
        return success(superService.saveRoleEmployee(roleEmployeeSaveVO));
    }

    /**
     * 给角色配置资源
     *
     * @param saveVO 角色权限授权对象
     * @return 新增结果
     */
    @OperationLog(
            module = SnapshotBizModuleEnum.BASE_ROLE_PERMISSION,
            type = BizLogOperationTypeEnum.UPDATE,
            source = {"#saveVO.roleId"},
            descSplitField = "roleName"
    )
    @ApiOperation(value = "给角色配置资源")
    @PostMapping("/roleResource")
    @WebLog("给角色配置权限")
    public R<Boolean> roleResource(@RequestBody BaseRoleResourceRelSaveVO saveVO) {
        return success(superService.roleResource(saveVO));
    }

    /**
     * 给角色配置资源
     *
     * @param saveVO 角色权限授权对象
     * @return 新增结果
     */
    @ApiOperation(value = "给角色配置资源")
    @PostMapping("/saveRoleResource")
    @WebLog("给角色配置权限")
    public R<Boolean> saveRoleResource(@RequestBody BaseRoleResourceRelSaveVO saveVO) {
        return success(superService.saveRoleResource(saveVO));
    }


    /**
     * 查询角色绑定的员工
     *
     * @param roleId 角色id
     * @return 新增结果
     */
    @ApiOperation(value = "查询角色绑定的员工")
    @GetMapping("/employeeList")
    @WebLog("查询角色的用户")
    public R<List<Long>> findEmployeeIdByRoleId(@RequestParam Long roleId) {
        return success(superService.findEmployeeIdByRoleId(roleId));
    }


    /**
     * 查询角色拥有的资源id
     *
     * @param roleId 角色id
     * @return 新增结果
     */
    @ApiOperation(value = "查询角色拥有的资源id集合")
    @GetMapping("/resourceList")
    @WebLog("查询角色拥有的资源")
    public R<Map<Long, Collection<Long>>> findResourceIdByRoleId(@RequestParam Long roleId) {
        return success(baseRoleBiz.findResourceIdByRoleId(roleId));
    }

    @OperationLog(
            module = SnapshotBizModuleEnum.BASE_ROLE,
            type = BizLogOperationTypeEnum.CREATE,
            descSplitField = "name"
    )
    @Override
    public R<BaseRole> save(BaseRoleSaveVO baseRoleSaveVO) {
        return super.save(baseRoleSaveVO);
    }

    @OperationLog(
            module = SnapshotBizModuleEnum.BASE_ROLE,
            type = BizLogOperationTypeEnum.UPDATE,
            source = {"#baseRoleUpdateVO.id"},
            descSplitField = "name"
    )
    @Override
    public R<BaseRole> update(BaseRoleUpdateVO baseRoleUpdateVO) {
        return super.update(baseRoleUpdateVO);
    }

    @OperationLog(
            module = SnapshotBizModuleEnum.BASE_ROLE,
            type = BizLogOperationTypeEnum.DELETE,
            source = {"#longs"},
            descSplitField = "name"
    )
    @Override
    public R<Boolean> delete(List<Long> longs) {
        List<BaseRoleResultVO> baseRoleResultVOList = superService.findEmployeeNum(longs, null);
        ArgumentAssert.isFalse(baseRoleResultVOList.stream().anyMatch(e -> e.getEmployeeNum() > 0), "角色已经绑定用户, 不允许删除");
        return super.delete(longs);
    }


    @Override
    public R<Boolean> handlerDelete(List<Long> longs) {
        return super.handlerDelete(longs);
    }

    /**
     * 查询角色拥有的数据权限ID
     *
     * @param roleId 角色id
     * @return 新增结果
     */
    @ApiOperation(value = "查询角色拥有的数据权限ID")
    @GetMapping("/findResourceDataScopeIdByRoleId")
    @WebLog("查询角色拥有的数据权限")
    public R<Map<Long, Collection<Long>>> findResourceDataScopeIdByRoleId(@RequestParam Long roleId) {
        return success(superService.findResourceIdByRoleId(roleId, RoleCategoryEnum.DATA_SCOPE));
    }


    @ApiOperation(value = "查询员工拥有的角色编码")
    @GetMapping("/findRoleCodeByEmployeeId")
    public R<List<String>> findRoleCodeByEmployeeId(@RequestParam(value = "employeeId") Long employeeId) {
        return success(superService.findRoleCodeByEmployeeId(employeeId));
    }


    @Override
    public R<List<BaseRoleResultVO>> query(BaseRolePageQuery data) {
        BaseRole entity = BeanPlusUtil.toBean(data, BaseRole.class);
        QueryWrap<BaseRole> queryWrap = Wraps.q(entity);
        queryWrap.lambda().notIn(BaseRole::getCode, ListUtil.toList("SELECT_ORG", "ORG_ADMIN"));
//        wrapper.lambda().in(BaseRole::getCreatedOrgId, Arrays.asList(1L, ContextUtil.getCurrentCompanyId()));
//        wrapper.lambda().eq(BaseRole::getCreatedOrgId, ContextUtil.getCurrentCompanyId());
        queryWrap.lambda().and(wrapper -> wrapper.eq(BaseRole::getCode, "TENANT_ADMIN")
                .or().eq(BaseRole::getCreatedOrgId, ContextUtil.getCurrentCompanyId()));
        List<BaseRole> list = superService.list(queryWrap);
        List<Long> roleIds = list.stream().map(BaseRole::getId).collect(Collectors.toList());
        List<BaseRoleResultVO> baseRoleResultVOList = superService.findEmployeeNum(roleIds, ContextUtil.getCurrentCompanyId());
        Map<Long, BaseRoleResultVO> baseRoleResultVOMap = baseRoleResultVOList.stream().collect(Collectors.toMap(BaseRoleResultVO::getId, e -> e));
        List<BaseRoleResultVO> baseRoleResultVOS = BeanUtil.copyToList(list, BaseRoleResultVO.class);
        baseRoleResultVOS.forEach(e -> {
            BaseRoleResultVO baseRoleResultVO = baseRoleResultVOMap.get(e.getId());
            if (Objects.nonNull(baseRoleResultVO)) {
                e.setEmployeeNum(baseRoleResultVO.getEmployeeNum());
            } else {
                e.setEmployeeNum(0L);
            }
        });
        echoService.action(baseRoleResultVOS);
        return R.success(baseRoleResultVOS);
    }
}
