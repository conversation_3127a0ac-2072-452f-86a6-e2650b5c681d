package top.kx.kxss.base.controller.meituan;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.kxss.base.entity.northstar.NorthStartPrepare;
import top.kx.kxss.base.service.northstar.MtService;
import top.kx.kxss.base.vo.northstar.query.MtAuthQuery;

import java.util.Map;

/**
 * 前端控制器
 * 美团-相关API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/mt")
@Api(value = "/base/mt", tags = "美团-验劵相关API")
public class MtController {

    @Autowired
    private MtService mtService;

    @ApiOperation(value = "授权并绑定", notes = "授权并绑定")
    @GetMapping("/auth")
    @WebLog("授权并绑定")
    public String auth(MtAuthQuery query) {
        log.info("美团团购授权并绑定：{}", JSONUtil.toJsonStr(query));
        ArgumentAssert.notBlank(query.getCode(), "缺少【授权码】参数");
        ArgumentAssert.notBlank(query.getState(), "缺少【自定义字段回传】参数");
        return mtService.auth(query);
    }

    @ApiOperation(value = "获取绑定状态", notes = "获取绑定状态")
    @GetMapping("/isBind")
    @WebLog("获取绑定状态")
    public R<Boolean> isBind(@RequestParam Long storeId) {
        return R.success(mtService.isBind(storeId));
    }

    @ApiOperation(value = "绑定门店", notes = "绑定门店")
    @PostMapping("/bind")
    @WebLog("绑定门店")
    public R<Boolean> bind(@RequestParam String openShopUuid) {
        return R.success(mtService.bind(openShopUuid));
    }

    @ApiOperation(value = "查询团购劵码信息", notes = "查询团购劵码信息")
    @PostMapping("/prepare")
    @WebLog("校验团购劵码")
    public R<NorthStartPrepare> prepare(@RequestParam String securitiesNumber) {
        return R.success(mtService.prepare(securitiesNumber));
    }

    @ApiOperation(value = "验劵", notes = "验劵")
    @PostMapping("/consume")
    @WebLog("验劵")
    public R<NorthStartPrepare> consume(@RequestParam String securitiesNumber) {
        return R.success(mtService.consume(securitiesNumber, null, null));
    }

    @ApiOperation(value = "撤销验劵", notes = "撤销验劵")
    @PostMapping("/reverseConsume")
    @WebLog("撤销验劵")
    public R<Boolean> reverseConsume(@RequestParam String securitiesNumber) {
        return R.success(mtService.reverseConsume(securitiesNumber));
    }

    @ApiOperation(value = "获取授权链接", notes = "获取授权链接")
    @GetMapping("/authUrl")
    @WebLog("获取授权链接")
    public R<String> authUrl(@RequestParam Long storeId) {
        return R.success(mtService.authUrl(storeId));
    }

    @ApiOperation(value = "接收session转换token", notes = "接收session转换token")
    @PostMapping(path = "/session_token")
    @WebLog("接收session转换token")
    public Map<String, Object> sessionToken(@RequestParam Map<String, Object> params) {
        log.info("接收session转换token：{}", params);
        return mtService.sessionToken(params);
    }

    @ApiOperation(value = "迁移原北极星session", notes = "迁移原北极星session")
    @GetMapping(path = "/migrateSession")
    @WebLog("迁移原北极星session")
    public R<Boolean> migrateSession() {
        mtService.syncSessionToToken();
        return R.success(true);
    }

    @ApiOperation(value = "查询已迁移session明细", notes = "查询已迁移session明细")
    @GetMapping(path = "/migrationTaskDetail/{session}")
    @WebLog("查询已迁移session明细")
    public R<Boolean> migrationTaskDetail(@PathVariable String session) {
        log.info("查询已迁移session明细：{}", session);
        mtService.syncSession(session);
        return R.success(true);
    }
}


