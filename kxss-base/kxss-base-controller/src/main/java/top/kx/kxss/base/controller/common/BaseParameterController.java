package top.kx.kxss.base.controller.common;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.seata.common.util.StringUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.base.annotation.OperationLog;
import top.kx.kxss.base.entity.common.BaseParameter;
import top.kx.kxss.base.service.common.BaseParameterService;
import top.kx.kxss.base.vo.query.common.BaseParameterPageQuery;
import top.kx.kxss.base.vo.result.common.BaseParameterResultVO;
import top.kx.kxss.base.vo.save.common.BaseParameterSaveVO;
import top.kx.kxss.base.vo.update.common.BaseParameterUpdateVO;
import top.kx.kxss.common.constant.ParameterKey;
import top.kx.kxss.model.enumeration.base.BizLogOperationTypeEnum;
import top.kx.kxss.model.enumeration.base.ServiceStaffTimeEnum;
import top.kx.kxss.model.enumeration.base.SnapshotBizModuleEnum;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * 个性参数
 * </p>
 *
 * <AUTHOR>
 * @date 2021-11-08
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/baseParameter")
@Api(value = "BaseParameter", tags = "个性参数")
public class BaseParameterController extends SuperController<BaseParameterService, Long, BaseParameter, BaseParameterSaveVO, BaseParameterUpdateVO, BaseParameterPageQuery, BaseParameterResultVO> {

    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Override
    public QueryWrap<BaseParameter> handlerWrapper(BaseParameter model, PageParams<BaseParameterPageQuery> params) {
        params.setSort("");
        params.setOrder("");
        params.getModel().setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        QueryWrap<BaseParameter> wrap = super.handlerWrapper(model, params);
        wrap.lambda().eq(BaseParameter::getCreatedOrgId, ContextUtil.getCurrentCompanyId());
        if (StringUtils.isNotBlank(params.getModel().getQueryType())) {
            if (StringUtils.equals(params.getModel().getQueryType(), "PARAMETER")) {
                wrap.lambda().ne(BaseParameter::getKey, ParameterKey.CHANGE_TABLE_AUTH);
            }
            if (StringUtils.equals(params.getModel().getQueryType(), "OTHER")) {
                wrap.lambda().eq(BaseParameter::getKey, ParameterKey.CHANGE_TABLE_AUTH);
            }
        }
        wrap.lambda().orderByAsc(BaseParameter::getCreatedTime);
        return wrap;
    }

    @Override
    public R<List<BaseParameterResultVO>> query(BaseParameterPageQuery data) {
        data.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return super.query(data);
    }

    @Override
    public void handlerResult(IPage<BaseParameterResultVO> page) {
        for (BaseParameterResultVO record : page.getRecords()) {
            if (StrUtil.isNotBlank(record.getValue()) && ObjectUtil.equal(record.getValue(), "-1")) {
                record.setValue("");
            }
        }
        super.handlerResult(page);
    }

    @OperationLog(
            module = SnapshotBizModuleEnum.PARAMETERS,
            type = BizLogOperationTypeEnum.UPDATE,
            source = {"#baseParameterUpdateVO.id"},
            descSplitField = "name"
    )
    @Override
    public R<BaseParameter> update(BaseParameterUpdateVO baseParameterUpdateVO) {
        return super.update(baseParameterUpdateVO);
    }

    @ApiOperation(value = "校验是否需要语音播报", notes = "校验是否需要语音播报")
    @GetMapping("/checkVoice")
    public R<Boolean> checkVoice(@RequestParam String key) {
        return success(superService.checkVoice(key));
    }


    @ApiOperation(value = "获取助教的时长展示方式", notes = "获取助教的时长展示方式")
    @GetMapping("/getServiceStaffTime")
    public R<ServiceStaffTimeEnum> getServiceStaffTime() {
        return success(superService.getDefaultServiceStaffTime());
    }
}
