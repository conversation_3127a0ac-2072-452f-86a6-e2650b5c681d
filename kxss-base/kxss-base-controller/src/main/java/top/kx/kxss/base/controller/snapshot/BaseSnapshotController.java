package top.kx.kxss.base.controller.snapshot;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.base.controller.SuperController;
import top.kx.kxss.base.service.snapshot.BaseSnapshotService;
import top.kx.kxss.base.entity.snapshot.BaseSnapshot;
import top.kx.kxss.base.vo.save.snapshot.BaseSnapshotSaveVO;
import top.kx.kxss.base.vo.update.snapshot.BaseSnapshotUpdateVO;
import top.kx.kxss.base.vo.result.snapshot.BaseSnapshotResultVO;
import top.kx.kxss.base.vo.query.snapshot.BaseSnapshotPageQuery;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * <p>
 * 前端控制器
 * 业务镜像日志
 * </p>
 *
 * <AUTHOR>
 * @date 2025-05-27 11:46:01
 * @create [2025-05-27 11:46:01] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseSnapshot")
@Api(value = "BaseSnapshot", tags = "业务镜像日志")
public class BaseSnapshotController extends SuperController<BaseSnapshotService, Long, BaseSnapshot, BaseSnapshotSaveVO,
    BaseSnapshotUpdateVO, BaseSnapshotPageQuery, BaseSnapshotResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }


    @Override
    public QueryWrap<BaseSnapshot> handlerWrapper(BaseSnapshot model, PageParams<BaseSnapshotPageQuery> params) {
        QueryWrap<BaseSnapshot> wrap = super.handlerWrapper(model, params);
        if (Objects.nonNull(params.getModel().getEmployee())) {
            wrap.lambda().inSql(BaseSnapshot::getEmployeeId, "select id from base_employee where delete_flag = 0 and (real_name like '%" + params.getModel().getEmployee() + "%' or name like '%" + params.getModel().getEmployee() + "%')");
        }
        if (Objects.nonNull(params.getModel().getStartCreatedTime()) && Objects.nonNull(params.getModel().getEndCreatedTime())) {
            wrap.lambda().between(BaseSnapshot::getCreatedTime, params.getModel().getStartCreatedTime(), params.getModel().getEndCreatedTime());
        } else if (Objects.nonNull(params.getModel().getStartCreatedTime())) {
            wrap.lambda().ge(BaseSnapshot::getCreatedTime, params.getModel().getStartCreatedTime());
        } else if (Objects.nonNull(params.getModel().getEndCreatedTime())) {
            wrap.lambda().le(BaseSnapshot::getCreatedTime, params.getModel().getEndCreatedTime());
        }
        wrap.lambda().eq(BaseSnapshot::getCreatedOrgId, ContextUtil.getCurrentCompanyId());
        return wrap;
    }
}


