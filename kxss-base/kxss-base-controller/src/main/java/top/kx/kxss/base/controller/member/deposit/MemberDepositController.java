package top.kx.kxss.base.controller.member.deposit;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.member.deposit.*;
import top.kx.kxss.base.service.member.deposit.*;
import top.kx.kxss.base.vo.query.member.deposit.MemberDepositPageQuery;
import top.kx.kxss.base.vo.query.member.deposit.MemberDepositRulePageQuery;
import top.kx.kxss.base.vo.result.member.deposit.*;
import top.kx.kxss.base.vo.save.member.deposit.MemberDepositRuleSaveVO;
import top.kx.kxss.base.vo.save.member.deposit.MemberDepositSaveVO;
import top.kx.kxss.base.vo.update.member.deposit.MemberDepositRuleUpdateVO;
import top.kx.kxss.base.vo.update.member.deposit.MemberDepositUpdateVO;
import top.kx.kxss.iot.entity.IotDevice;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 储值信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-14 09:59:09
 * @create [2023-04-14 09:59:09] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/memberDeposit")
@Api(value = "MemberDeposit", tags = "储值信息")
public class MemberDepositController extends SuperController<MemberDepositService, Long, MemberDeposit, MemberDepositSaveVO,
        MemberDepositUpdateVO, MemberDepositPageQuery, MemberDepositResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Autowired
    private MemberDepositRuleService depositRuleService;
    @Autowired
    private MemberDepositCouponService couponService;
    @Autowired
    private MemberDepositProductService productService;
    @Autowired
    private MemberDepositCardService cardService;

    @ApiOperation(
            value = "查询当前储值规则",
            notes = "查询当前储值规则"
    )
    @GetMapping("/getInfo")
    @WebLog("查询当前储值规则")
    public R<MemberDepositResultVO> getInfo(@RequestParam(value = "storeId", required = false) Long storeId) {
        storeId = ObjectUtil.isNull(storeId) ? ContextUtil.getCurrentCompanyId() : storeId;
        MemberDeposit memberDeposit = null;
        List<MemberDeposit> list = superService.list(Wraps.<MemberDeposit>lbQ().eq(MemberDeposit::getCreatedOrgId, storeId).orderByAsc(MemberDeposit::getCreatedTime).last("limit 1"));
        if (CollUtil.isNotEmpty(list)) {
            memberDeposit = list.get(0);
        }
        if (ObjectUtil.isNull(memberDeposit)) {
            return super.success(null);
        }
        MemberDepositResultVO memberDepositResultVO = BeanUtil.copyProperties(memberDeposit, MemberDepositResultVO.class);
        List<MemberDepositRuleResultVO> depositRules = depositRuleService.list(Wraps.<MemberDepositRule>lbQ().eq(MemberDepositRule::getDepositId, memberDepositResultVO.getId()))
                .stream().map(v -> BeanUtil.copyProperties(v, MemberDepositRuleResultVO.class))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(depositRules)) {
            //规则所绑定的商品
            List<Long> depositRuleIds = depositRules.stream().map(MemberDepositRuleResultVO::getId).collect(Collectors.toList());
            Map<Long, List<MemberDepositProductResultVO>> productMap = productService.list(Wraps.<MemberDepositProduct>lbQ().in(MemberDepositProduct::getDepositRuleId, depositRuleIds))
                    .stream().map(v -> BeanUtil.copyProperties(v, MemberDepositProductResultVO.class)).collect(Collectors.toList())
                    .stream().collect(Collectors.groupingBy(MemberDepositProductResultVO::getDepositRuleId));
            Map<Long, List<MemberDepositCouponResultVO>> couponMap = couponService.list(Wraps.<MemberDepositCoupon>lbQ().in(MemberDepositCoupon::getDepositRuleId, depositRuleIds))
                    .stream().map(v -> BeanUtil.copyProperties(v, MemberDepositCouponResultVO.class)).collect(Collectors.toList())
                    .stream().collect(Collectors.groupingBy(MemberDepositCouponResultVO::getDepositRuleId));
            Map<Long, List<MemberDepositCardResultVO>> cardMap = cardService.list(Wraps.<MemberDepositCard>lbQ().in(MemberDepositCard::getDepositRuleId, depositRuleIds))
                    .stream().map(v -> BeanUtil.copyProperties(v, MemberDepositCardResultVO.class)).collect(Collectors.toList())
                    .stream().collect(Collectors.groupingBy(MemberDepositCardResultVO::getDepositRuleId));

            for (MemberDepositRuleResultVO depositRuleResultVO : depositRules) {
                depositRuleResultVO.setCouponList(couponMap.get(depositRuleResultVO.getId()));
                depositRuleResultVO.setProductList(productMap.get(depositRuleResultVO.getId()));
                depositRuleResultVO.setCardList(cardMap.get(depositRuleResultVO.getId()));
            }
        }
        echoService.action(depositRules);
        memberDepositResultVO.setDepositRuleList(depositRules);
        return super.success(memberDepositResultVO);
    }

    @Override
    public R<MemberDepositResultVO> getDetail(Long aLong) {
        MemberDeposit memberDeposit = superService.getById(aLong);
        if (ObjectUtil.isNull(memberDeposit)) {
            return super.success(null);
        }
        MemberDepositResultVO memberDepositResultVO = BeanUtil.copyProperties(memberDeposit, MemberDepositResultVO.class);
        List<MemberDepositRuleResultVO> depositRules = depositRuleService.list(Wraps.<MemberDepositRule>lbQ().eq(MemberDepositRule::getDepositId, memberDepositResultVO.getId()).orderByAsc(MemberDepositRule::getSortValue))
                .stream().map(v -> BeanUtil.copyProperties(v, MemberDepositRuleResultVO.class)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(depositRules)) {
            //规则所绑定的商品
            List<Long> depositRuleIds = depositRules.stream().map(MemberDepositRuleResultVO::getId).collect(Collectors.toList());
            Map<Long, List<MemberDepositProductResultVO>> productMap = productService.list(Wraps.<MemberDepositProduct>lbQ().in(MemberDepositProduct::getDepositRuleId, depositRuleIds))
                    .stream().map(v -> BeanUtil.copyProperties(v, MemberDepositProductResultVO.class)).collect(Collectors.toList())
                    .stream().collect(Collectors.groupingBy(MemberDepositProductResultVO::getDepositRuleId));
            Map<Long, List<MemberDepositCouponResultVO>> couponMap = couponService.list(Wraps.<MemberDepositCoupon>lbQ().in(MemberDepositCoupon::getDepositRuleId, depositRuleIds))
                    .stream().map(v -> BeanUtil.copyProperties(v, MemberDepositCouponResultVO.class)).collect(Collectors.toList())
                    .stream().collect(Collectors.groupingBy(MemberDepositCouponResultVO::getDepositRuleId));
            Map<Long, List<MemberDepositCardResultVO>> cardMap = cardService.list(Wraps.<MemberDepositCard>lbQ()
                            .in(MemberDepositCard::getDepositRuleId, depositRuleIds))
                    .stream().map(v -> BeanUtil.copyProperties(v, MemberDepositCardResultVO.class)).collect(Collectors.toList())
                    .stream().collect(Collectors.groupingBy(MemberDepositCardResultVO::getDepositRuleId));
            for (MemberDepositRuleResultVO depositRuleResultVO : depositRules) {
                depositRuleResultVO.setCouponList(couponMap.get(depositRuleResultVO.getId()));
                depositRuleResultVO.setProductList(productMap.get(depositRuleResultVO.getId()));
                depositRuleResultVO.setCardList(cardMap.get(depositRuleResultVO.getId()));
            }
        }
        memberDepositResultVO.setDepositRuleList(depositRules);
        return super.success(memberDepositResultVO);
    }

    @ApiOperation(value = "修改储值规则基础信息", notes = "修改储值规则基础信息")
    @PostMapping("/updateById")
    public R<MemberDeposit> updateById(@RequestBody MemberDepositUpdateVO memberDepositUpdateVO) {
        return R.success(superService.updateById(memberDepositUpdateVO));
    }

    @ApiOperation(value = "新增规则", notes = "新增规则")
    @PostMapping("/addDepositRule")
    public R<MemberDepositRule> addDepositRule(@RequestBody MemberDepositRuleSaveVO depositRuleSaveVO) {
        return R.success(superService.addDepositRule(depositRuleSaveVO));
    }

    @ApiOperation(value = "修改规则", notes = "修改规则")
    @PostMapping("/updateDepositRule")
    public R<MemberDepositRule> updateDepositRule(@RequestBody @Validated MemberDepositRuleUpdateVO depositRuleUpdateVO) {
        return R.success(superService.updateDepositRule(depositRuleUpdateVO));
    }

    @ApiOperation(value = "删除规则", notes = "删除规则")
    @DeleteMapping("/removeDepositRule")
    public R<Boolean> removeDepositRule(@RequestBody List<Long> ids) {
        return R.success(superService.removeDepositRule(ids));
    }

    @ApiOperation(value = "规则列表", notes = "规则列表")
    @PostMapping("/depositRulePage")
    public R<IPage<MemberDepositRuleResultVO>> depositRulePage(@RequestBody PageParams<MemberDepositRulePageQuery> params) {
        LbQueryWrap<MemberDepositRule> queryWrap = Wraps.<MemberDepositRule>lbQ().eq(SuperEntity::getDeleteFlag, 0).orderByAsc(MemberDepositRule::getSortValue);
        if (ObjectUtil.isNull(params.getModel().getDepositId())) {
            // 默认只查一个基础的储值规则, 并且一个门店只有一个
            List<MemberDeposit> depositList = superService.list(Wraps.<MemberDeposit>lbQ().eq(SuperEntity::getDeleteFlag, 0)
                    .eq(MemberDeposit::getCreatedOrgId, params.getModel().getCreatedOrgId()));
            if (CollUtil.isNotEmpty(depositList)) {
                queryWrap.eq(MemberDepositRule::getDepositId, depositList.get(0).getId());
            }
        }
        IPage<MemberDepositRule> page = depositRuleService.page(params.buildPage(MemberDepositRule.class), queryWrap);
        IPage<MemberDepositRuleResultVO> resultVOIPage = BeanPlusUtil.toBeanPage(page, MemberDepositRuleResultVO.class);
        return R.success(resultVOIPage);
    }


    @Override
    public R<MemberDeposit> handlerSave(MemberDepositSaveVO model) {
        return super.success(superService.saveDeposit(model));
    }

    @Override
    public R<MemberDeposit> handlerUpdate(MemberDepositUpdateVO memberDepositUpdateVO) {
        return super.success(superService.updateDeposit(memberDepositUpdateVO));
    }

}


