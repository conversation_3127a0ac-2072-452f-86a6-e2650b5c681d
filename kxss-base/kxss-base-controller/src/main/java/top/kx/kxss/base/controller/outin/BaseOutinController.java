package top.kx.kxss.base.controller.outin;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.jackson.JsonUtil;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.base.entity.outin.BaseOutin;
import top.kx.kxss.base.entity.outin.BaseOutinApproval;
import top.kx.kxss.base.entity.outin.BaseOutinProduct;
import top.kx.kxss.base.entity.outin.BaseOutinStocktaking;
import top.kx.kxss.base.entity.warehouse.BaseWarehouse;
import top.kx.kxss.base.service.outin.BaseOutinApprovalService;
import top.kx.kxss.base.service.outin.BaseOutinProductService;
import top.kx.kxss.base.service.outin.BaseOutinService;
import top.kx.kxss.base.service.warehouse.BaseWarehouseService;
import top.kx.kxss.base.vo.query.outin.BaseOutinPageQuery;
import top.kx.kxss.base.vo.result.outin.*;
import top.kx.kxss.base.vo.save.outin.*;
import top.kx.kxss.base.vo.update.outin.BaseOutinUpdateVO;
import top.kx.kxss.common.api.HelperApi;
import top.kx.kxss.common.constant.ParameterKey;
import top.kx.kxss.datascope.DataScopeHelper;
import top.kx.kxss.model.enumeration.base.OutinTypeEnum;
import top.kx.kxss.model.enumeration.base.SourceTypeEnum;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.service.tenant.DefUserService;
import top.kx.kxss.base.vo.query.outin.SellOutinQuery;
import top.kx.kxss.base.entity.outin.SellOutinDetailsResultVO;
import top.kx.kxss.base.entity.outin.SellOutinResultVO;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 商品出入库主表
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-06 14:51:59
 * @create [2023-04-06 14:51:59] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseOutin")
@Api(value = "BaseOutin", tags = "商品出入库主表")
public class BaseOutinController extends SuperController<BaseOutinService, Long, BaseOutin, BaseOutinSaveVO,
        BaseOutinUpdateVO, BaseOutinPageQuery, BaseOutinResultVO> {
    @Autowired
    private final EchoService echoService;

    @Autowired
    private DefUserService userService;
    @Autowired
    private BaseOutinProductService baseOutinProductService;
    @Autowired
    private BaseWarehouseService baseWarehouseService;
    @Autowired
    private HelperApi helperApi;
    @Autowired
    private BaseOutinApprovalService baseOutinApprovalService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<BaseOutin> handlerSave(BaseOutinSaveVO model) {
        if (StrUtil.isBlank(model.getSourceType())) {
            model.setSourceType(SourceTypeEnum.POS.getCode());
        }
        BaseOutinStockSaveVO baseOutinStockSaveVO =
                BeanUtil.copyProperties(model, BaseOutinStockSaveVO.class);
        if (ObjectUtil.isNull(baseOutinStockSaveVO.getWarehouseId())) {
            BaseWarehouse baseWarehouse = baseWarehouseService.getOneBySn();
            ArgumentAssert.notNull(baseWarehouse, "未绑定销售仓库,请绑定后重试");
            baseOutinStockSaveVO.setWarehouseId(baseWarehouse.getId());
        }
        if (StringUtils.equals(baseOutinStockSaveVO.getType(), OutinTypeEnum.PURCHASE_IN.getCode())) {
            ArgumentAssert.isTrue(Objects.nonNull(model.getAmount()), "采购单总金额不能为空");
            ArgumentAssert.isTrue(model.getOutinProductList().stream().allMatch(s -> Objects.nonNull(s.getAmount())), "采购单商品进货单价不能为空");
        }

        if (ObjectUtil.isNull(baseOutinStockSaveVO.getState())) {
            String parameterKey = null;
            // 判断类型
            if (StringUtils.equals(baseOutinStockSaveVO.getType(), OutinTypeEnum.PURCHASE_IN.getCode())) {
                parameterKey = ParameterKey.OUTIN_IN_STOCK_AUDIT;
            } else if (StringUtils.equals(baseOutinStockSaveVO.getType(), OutinTypeEnum.PURCHASE_OUT.getCode())) {
                parameterKey = ParameterKey.OUTIN_RETURN_STOCK_AUDIT;
            } else {
                baseOutinStockSaveVO.setState(1);
            }
            if (ObjectUtil.isNull(baseOutinStockSaveVO.getState())) {
                setStatus(parameterKey, baseOutinStockSaveVO);
            }
        }
        return R.success(superService.saveStock(baseOutinStockSaveVO));
    }

    @ApiOperation(value = "盘点盈亏", notes = "盘点盈亏")
    @PostMapping("/stocktaking")
    @WebLog(value = "盘点盈亏")
    public R<BaseOutinStocktaking> stocktaking(@RequestBody @Validated BaseOutinStocktakingVO model) {
        if (StrUtil.isBlank(model.getSourceType())) {
            model.setSourceType(SourceTypeEnum.POS.getCode());
        }
        model.setId(null);
        if (ObjectUtil.isNull(model.getWarehouseId())) {
            BaseWarehouse baseWarehouse = baseWarehouseService.getOneBySn();
            ArgumentAssert.notNull(baseWarehouse, "未选择盘点仓库,请选择后重试");
            model.setWarehouseId(baseWarehouse.getId());
        }
        if (ObjectUtil.isNull(model.getState())) {
            Map<String, String> voiceParams = helperApi.findParams(Collections.singletonList(ParameterKey.OUTIN_STOCKTAKING_AUDIT)).getData();
            log.info("个性参数：{}", JsonUtil.toJson(voiceParams));
            if (CollUtil.isNotEmpty(voiceParams) && StrUtil.isNotBlank(voiceParams.get(ParameterKey.OUTIN_STOCKTAKING_AUDIT))
                    && StringUtils.equals(voiceParams.get(ParameterKey.OUTIN_STOCKTAKING_AUDIT), "1")) {
                model.setState(0);
            } else {
                model.setState(1);
            }
        }
        return R.success(superService.stocktaking(model));
    }


    @ApiOperation(value = "盘点盈亏", notes = "盘点盈亏")
    @PostMapping("/updateStocktaking")
    @WebLog(value = "盘点盈亏")
    public R<BaseOutinStocktaking> updateStocktaking(@RequestBody @Validated BaseOutinStocktakingVO model) {
        ArgumentAssert.notNull(model.getId(), "请选择需要修改的单据");
        return R.success(superService.updateStocktaking(model));
    }

    @ApiOperation(value = "采购退货", notes = "采购退货")
    @PostMapping("/purchaseOut")
    @WebLog(value = "采购退货")
    public R<BaseOutin> purchaseOut(@RequestBody @Validated BaseOutinStockSaveVO model) {
        if (StrUtil.isBlank(model.getSourceType())) {
            model.setSourceType(SourceTypeEnum.POS.getCode());
        }
        if (ObjectUtil.isNull(model.getWarehouseId())) {
            BaseWarehouse baseWarehouse = baseWarehouseService.getOneBySn();
            ArgumentAssert.notNull(baseWarehouse, "未绑定销售仓库,请绑定后重试");
            model.setWarehouseId(baseWarehouse.getId());
        }
        setStatus(ParameterKey.OUTIN_RETURN_STOCK_AUDIT, model);
        return R.success(superService.purchaseOut(model));
    }


    @ApiOperation(value = "采购退货", notes = "采购退货")
    @PutMapping("/purchaseOut")
    @WebLog(value = "采购退货")
    public R<BaseOutin> purchaseOutUpdate(@RequestBody BaseOutinUpdateVO model) {
        return R.success(superService.updateOutin(model));
    }

    @ApiOperation(value = "销售出库", notes = "销售出库")
    @PostMapping("/sell")
    @WebLog(value = "销售出库")
    public R<IPage<SellOutinResultVO>> sell(@RequestBody PageParams<SellOutinQuery> params) {
        return R.success(superService.sell(params));
    }

    @ApiOperation(value = "销售出库明细", notes = "销售出库明细")
    @GetMapping("/sell/detail")
    @WebLog(value = "销售出库")
    public R<SellOutinResultVO> sellDetail(@RequestParam Long id) {
        return R.success(superService.sellDetail(id));
    }


    @ApiOperation(value = "采购入库-导出", notes = "采购入库-导出")
    @RequestMapping(value = "/purchaseIn/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void purchaseInExport(@RequestParam Long id, HttpServletResponse response) {
        List<BaseOutinPurchaseInExportResultVO> list = superService.purchaseInExport(id);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=PURCHASE_IN.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, BaseOutinPurchaseInExportResultVO.class)
                    .sheet("采购退")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }


    @ApiOperation(value = "采购退货-导出", notes = "采购退货-导出")
    @RequestMapping(value = "/purchaseOut/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void purchaseOutExport(@RequestParam Long id, HttpServletResponse response) {
        List<BaseOutinPurchaseOutExportResultVO> list = superService.purchaseOutExport(id);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=PURCHASE_OUT.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, BaseOutinPurchaseOutExportResultVO.class)
                    .sheet("采购退")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "商品调库", notes = "商品调库")
    @PostMapping("/adjustmentStock")
    @WebLog(value = "商品调库")
    public R<Boolean> adjustmentStock(@RequestBody @Validated BaseOutinStockAdjustmentVO model) {
        if (StrUtil.isBlank(model.getSourceType())) {
            model.setSourceType(SourceTypeEnum.WX_APP.getCode());
        }
        if (ObjectUtil.isNull(model.getState())) {
            Map<String, String> voiceParams = helperApi.findParams(Collections.singletonList(ParameterKey.OUTIN_ADJUSTMENT_AUDIT)).getData();
            log.info("个性参数：{}", JsonUtil.toJson(voiceParams));
            if (CollUtil.isNotEmpty(voiceParams) && StrUtil.isNotBlank(voiceParams.get(ParameterKey.OUTIN_ADJUSTMENT_AUDIT))
                    && StringUtils.equals(voiceParams.get(ParameterKey.OUTIN_ADJUSTMENT_AUDIT), "1")) {
                model.setState(0);
            } else {
                model.setState(1);
            }
        }
        return R.success(superService.adjustmentStock(model));
    }

    @ApiOperation(value = "商品调库", notes = "商品调库")
    @PostMapping("/updateAdjustmentStock")
    @WebLog(value = "商品调库")
    public R<Boolean> updateAdjustmentStock(@RequestBody @Validated BaseOutinStockAdjustmentVO model) {
        return R.success(superService.updateAdjustmentStock(model));
    }


    /**
     * 其他出库
     * @param model
     * @return
     */
    @ApiOperation(value = "其他出库", notes = "其他出库")
    @PostMapping("/otherOut")
    @WebLog(value = "其他出库")
    public R<BaseOutin> otherOut(@RequestBody @Validated BaseOutinStockSaveVO model) {
        if (StrUtil.isBlank(model.getSourceType())) {
            model.setSourceType(SourceTypeEnum.POS.getCode());
        }
        ArgumentAssert.isTrue(Objects.nonNull(model.getWarehouseId()), "请指定仓库后重试");
        setStatus(ParameterKey.OUTIN_OTHER_OUT_STOCK_AUDIT, model);
        return R.success(superService.otherOut(model));
    }



    @ApiOperation(value = "其他出库", notes = "其他出库")
    @PutMapping("/otherOut")
    @WebLog(value = "其他出库")
    public R<BaseOutin> otherOutUpdate(@RequestBody BaseOutinUpdateVO model) {
        return R.success(superService.updateOutin(model));
    }

    /**
     * 其他入库
     * @param model
     * @return
     */
    @ApiOperation(value = "其他入库", notes = "其他入库")
    @PostMapping("/otherIn")
    @WebLog(value = "其他入库")
    public R<BaseOutin> otherIn(@RequestBody @Validated BaseOutinStockSaveVO model) {
        if (StrUtil.isBlank(model.getSourceType())) {
            model.setSourceType(SourceTypeEnum.POS.getCode());
        }
        ArgumentAssert.isTrue(Objects.nonNull(model.getWarehouseId()), "请指定仓库后重试");
        setStatus(ParameterKey.OUTIN_OTHER_IN_STOCK_AUDIT, model);
        return R.success(superService.otherIn(model));
    }




    @ApiOperation(value = "其他入库", notes = "其他入库")
    @PutMapping("/otherIn")
    @WebLog(value = "其他入库")
    public R<BaseOutin> otherInUpdate(@RequestBody BaseOutinUpdateVO model) {
        return R.success(superService.updateOutin(model));
    }


    @ApiOperation(value = "审核状态", notes = "审核状态")
    @PostMapping("/updateState")
    @WebLog(value = "审核状态")
    public R<Boolean> updateState(@RequestBody @Validated BaseOutinStateVO model) {
        return R.success(superService.updateState(model));
    }


    @ApiOperation(value = "红冲", notes = "红冲-暂时只有采购入库和采购退货支持红冲")
    @PostMapping("/reversalEntry")
    @WebLog(value = "红冲")
    public R<Boolean> reversalEntry(@RequestBody @Validated BaseOutinReversalEntryVO model) {
        return R.success(superService.reversalEntry(model));
    }

//    @ApiOperation(value = "审核通过后更新", notes = "只允许更新采购入和采购退货，并且只能修改单价总价和备注")
//    @PostMapping("/updateOutinPrice")
//    @WebLog(value = "红冲")
//    public R<Boolean> updateOutinPrice(@RequestBody @Validated BaseOutinPriceUpdateVO model) {
//        return R.success(superService.updateOutinPrice(model));
//    }


    @ApiOperation(value = "查询列表", notes = "查询列表")
    @PostMapping("/pageList")
    @WebLog(value = "查询列表")
    public R<IPage<BaseOutinItemResultVO>> pageList(@RequestBody @Validated PageParams<BaseOutinPageQuery> params) {
        return R.success(superService.pageList(params));
    }

    @Override
    public R<BaseOutin> handlerUpdate(BaseOutinUpdateVO model) {
        return R.success(superService.updateOutin(model));
    }

    @Override
    public QueryWrap<BaseOutin> handlerWrapper(BaseOutin model, PageParams<BaseOutinPageQuery> params) {
        params.setSort("");
        params.setOrder("");
        Integer dateType = params.getModel().getBillDateType();
        DateUtils.extraDate("billDate", dateType, params.getExtra());
        String type = model.getType();
        model.setType(null);
        params.getModel().setType(null);
        QueryWrap<BaseOutin> queryWrap = super.handlerWrapper(model, params);
        if (StrUtil.isNotBlank(params.getModel().getUpdateBy())) {
            queryWrap.inSql("updated_by", "select o.user_id from base_employee o where (real_name like CONCAT('%','"
                    + params.getModel().getUpdateBy() + "','%') or o.user_id = '" + params.getModel().getUpdateBy() + "')");
        }
        if (StrUtil.isNotBlank(type) && ObjectUtil.equal(type, 2) && StrUtil.isBlank(params.getModel().getOutinType())) {
            queryWrap.in("type_", Arrays.asList(OutinTypeEnum.INVENTORY_LOSS_OUT.getCode(), OutinTypeEnum.SELL_OUT.getCode(),
                    OutinTypeEnum.REFUND_IN.getCode()));
        } else if (StrUtil.isNotBlank(type) && ObjectUtil.equal(type, 1) && StrUtil.isBlank(params.getModel().getOutinType())) {
            queryWrap.in("type_", Arrays.asList(OutinTypeEnum.PURCHASE_IN.getCode(), OutinTypeEnum.INVENTORY_PROFIT_IN.getCode()
                    , OutinTypeEnum.REFUND_IN.getCode()));
        } else {
            if (StrUtil.isNotBlank(params.getModel().getOutinType())) {
                queryWrap.eq("type_", params.getModel().getOutinType());
            }
            if (StrUtil.isNotBlank(type)) {
                queryWrap.eq("type_", type);
            }
        }
        queryWrap.lambda().in(CollUtil.isNotEmpty(params.getModel().getWarehouseIds()), BaseOutin::getWarehouseId, params.getModel().getWarehouseIds());
        queryWrap.eq("delete_flag", 0).eq("created_org_id", ContextUtil.getCurrentCompanyId());
        if (ObjectUtil.isNotNull(params.getModel().getState())) {
            queryWrap.eq("state", params.getModel().getState());
        }
        DataScopeHelper.startDataScope("base_outin");
        queryWrap.orderByDesc("created_time");
        return queryWrap;
    }


    @Override
    public IPage<BaseOutin> query(PageParams<BaseOutinPageQuery> params) {
        params.setSort("");
        params.setOrder("");
        return super.query(params);
    }

    @Override
    public void handlerQueryParams(PageParams<BaseOutinPageQuery> params) {
        params.setSort("");
        params.setOrder("");
        super.handlerQueryParams(params);
    }

    @Override
    public void handlerResult(IPage<BaseOutinResultVO> page) {
        List<BaseOutinResultVO> records = page.getRecords();
        List<Long> updateByIds = records.stream().map(BaseOutinResultVO::getUpdatedBy).collect(Collectors.toList());
        //获取商品信息
        Map<Long, DefUser> updateByMap = CollUtil.isNotEmpty(updateByIds) ? userService.list(Wraps.<DefUser>lbQ().in(DefUser::getId, updateByIds))
                .stream().collect(Collectors.toMap(DefUser::getId, k -> k)) : MapUtil.newHashMap();

        //获取商品信息
        List<Long> outinIds = records.stream().map(BaseOutinResultVO::getId).collect(Collectors.toList());
        Map<Long, List<BaseOutinProduct>> outinProductMap = CollUtil.isNotEmpty(outinIds) ? baseOutinProductService.list(Wraps.<BaseOutinProduct>lbQ()
                        .in(BaseOutinProduct::getOutinId, outinIds))
                .stream().collect(Collectors.groupingBy(BaseOutinProduct::getOutinId)) : MapUtil.newHashMap();

        // 将数据填充到VO中
        Map<Long, List<BaseOutinApprovalResultVO>> approvals = new HashMap<>();
        if (CollUtil.isNotEmpty(outinIds)) {
            List<BaseOutinApproval> approvalList = baseOutinApprovalService.list(Wraps.<BaseOutinApproval>lbQ()
                    .in(BaseOutinApproval::getBizId, outinIds));
            List<BaseOutinApprovalResultVO> beanList = BeanPlusUtil.toBeanList(approvalList, BaseOutinApprovalResultVO.class);
            approvals = beanList.stream().collect(Collectors.groupingBy(BaseOutinApprovalResultVO::getBizId));
        }


        for (BaseOutinResultVO record : records) {
            record.setOutinProductNum(0);
            DefUser user = updateByMap.get(record.getUpdatedBy());
            record.getEchoMap().put("updatedBy", user != null ? user.getNickName() : "");
            if (CollUtil.isNotEmpty(outinProductMap)
                    && CollUtil.isNotEmpty(outinProductMap.get(record.getId()))) {
                record.setOutinProductNum(outinProductMap.get(record.getId()).stream().mapToInt(BaseOutinProduct::getNum).sum());
            }
            if (CollUtil.isNotEmpty(approvals)) {
                record.setApprovalList(approvals.get(record.getId()));
                BaseOutinApprovalResultVO baseOutinApprovalResultVO = approvals.containsKey(record.getId()) ? approvals.get(record.getId()).stream().findFirst().orElse(null) : null;
                if (Objects.nonNull(baseOutinApprovalResultVO)) {
                    record.setApprovalTime(baseOutinApprovalResultVO.getCreatedTime());
                }
            }
        }
        echoService.action(page);
        super.handlerResult(page);
    }

    /**
     * 强制修改价格单价和总价
     * @param updateVO
     * @return
     */
    @ApiOperation(value = "强制修改价格", notes = "强制修改价格")
    @PostMapping("/forceUpdatePrice")
    @WebLog(value = "强制修改价格")
    public R<Boolean> forceUpdatePrice(@RequestBody @Validated BaseOutinForceUpdateVO updateVO) {
        return success(superService.forceUpdatePrice(updateVO));
    }


    @Override
    public R<BaseOutinResultVO> getDetail(Long aLong) {
        return success(superService.getDetail(aLong));
    }

    private void setStatus(String outinOtherInStockAudit, BaseOutinStockSaveVO model) {
        Map<String, String> voiceParams = helperApi.findParams(Collections.singletonList(outinOtherInStockAudit)).getData();
        log.info("个性参数：{}", JsonUtil.toJson(voiceParams));
        if (CollUtil.isNotEmpty(voiceParams) && StrUtil.isNotBlank(voiceParams.get(outinOtherInStockAudit))
                && StringUtils.equals(voiceParams.get(outinOtherInStockAudit), "1")) {
            model.setState(0);
        } else {
            model.setState(1);
        }
    }


    @Override
    public R<Boolean> delete(List<Long> longs) {
        List<BaseOutin> list = superService.list(Wraps.<BaseOutin>lbQ().in(BaseOutin::getId, longs));
        if (CollUtil.isEmpty(list)) {
            return fail("数据不存在, 无需审核");
        }
        ArgumentAssert.isTrue(list.stream().allMatch(s-> Objects.equals(s.getState(), 0)), "只有待审核的单据才能删除");
        return super.delete(longs);
    }
}


