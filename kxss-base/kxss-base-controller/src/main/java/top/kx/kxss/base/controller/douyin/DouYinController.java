package top.kx.kxss.base.controller.douyin;

import cn.hutool.json.JSONUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.kxss.base.entity.douyin.DouyinPrepare;
import top.kx.kxss.base.entity.groupBuy.BaseGroupBuy;
import top.kx.kxss.base.service.douyin.DouyinService;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 前端控制器
 * 美团-北极星相关API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/tiktok")
@Api(value = "/base/tiktok", tags = "抖音相关API")
public class DouYinController {

    @Autowired
    private DouyinService douyinService;

    @ApiOperation(value = "获取授权链接", notes = "获取授权链接")
    @GetMapping("/authUrl")
    @WebLog("获取授权链接")
    public R<String> authUrl() {
        return R.success(douyinService.authUrl());
    }

    @ApiOperation(value = "订阅通知", notes = "订阅通知事件")
    @PostMapping("/subscribe")
    @WebLog("授权并绑定")
    public void subscribeEvent(@RequestBody Map<String, Object> map, HttpServletRequest request, HttpServletResponse response) {
        log.info("订阅消息：{}", JSONUtil.toJsonStr(map));
        douyinService.subscribeEvent(map, request, response);
    }


    @ApiOperation(value = "获取绑定状态", notes = "获取绑定状态")
    @GetMapping("/isBind")
    @WebLog("获取绑定状态")
    public R<Boolean> isBind() {
        return R.success(douyinService.isBind());
    }

    @ApiOperation(value = "查询团购券码信息", notes = "查询团购券码信息,先查库里面,再查询抖音接口")
    @PostMapping("/prepare")
    @WebLog("校验团购券码")
    public R<DouyinPrepare> prepare(@RequestParam String securitiesNumber) {
        return R.success(douyinService.prepare(securitiesNumber));
    }

    // 核销券
    @ApiOperation(value = "验券", notes = "核销券")
    @PostMapping("/consume")
    @WebLog("验券")
    public R<DouyinPrepare> consume(@RequestParam String securitiesNumber, @RequestParam(required = false) String dealGroupId) {
        return R.success(douyinService.consume(securitiesNumber, dealGroupId));
    }

    // 核销券
    @ApiOperation(value = "开台验券", notes = "开台验券")
    @PostMapping("/open/consume")
    @WebLog("开台验券")
    public R<DouyinPrepare> openConsume(@RequestBody DouyinPrepare prepare) {
        return R.success(douyinService.consume(prepare, prepare.getReceiptCode()));
    }


    // 取消核销券

    @ApiOperation(value = "撤销验券", notes = "撤销验券")
    @PostMapping("/reverseConsume")
    @WebLog("撤销验券")
    public R<Boolean> reverseConsume(@RequestParam String securitiesNumber) {
        return R.success(douyinService.reverseConsume(securitiesNumber));
    }


    @ApiOperation(value = "团购列表", notes = "团购列表")
    @GetMapping("/groupBuyList")
    @WebLog("团购列表")
    public R<List<BaseGroupBuy>> groupBuyList() {
        return R.success(douyinService.groupBuyList());
    }

}


