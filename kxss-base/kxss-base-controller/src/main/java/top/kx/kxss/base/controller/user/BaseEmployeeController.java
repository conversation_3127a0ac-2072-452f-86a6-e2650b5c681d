package top.kx.kxss.base.controller.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperCacheController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.app.query.BizCacheConsumeQuery;
import top.kx.kxss.app.query.TenantOrgUserConsumeQuery;
import top.kx.kxss.base.annotation.OperationLog;
import top.kx.kxss.base.biz.user.BaseEmployeeBiz;
import top.kx.kxss.base.entity.system.BaseRole;
import top.kx.kxss.base.entity.user.BaseEmployee;
import top.kx.kxss.base.entity.user.BaseEmployeeOrgRel;
import top.kx.kxss.base.entity.user.BaseEmployeeRoleRel;
import top.kx.kxss.base.service.system.BaseRoleService;
import top.kx.kxss.base.service.user.BaseEmployeeOrgRelService;
import top.kx.kxss.base.service.user.BaseEmployeeRoleRelService;
import top.kx.kxss.base.service.user.BaseEmployeeService;
import top.kx.kxss.base.vo.query.user.BaseEmployeePageQuery;
import top.kx.kxss.base.vo.result.user.BaseEmployeeResultVO;
import top.kx.kxss.base.vo.save.user.*;
import top.kx.kxss.base.vo.update.user.BaseEmployeeUpdateVO;
import top.kx.kxss.common.constant.RabbitMqConstant;
import top.kx.kxss.file.vo.param.FileParamVO;
import top.kx.kxss.model.enumeration.BizCacheEnum;
import top.kx.kxss.model.enumeration.base.BizLogOperationTypeEnum;
import top.kx.kxss.model.enumeration.base.PostionStatusEnum;
import top.kx.kxss.model.enumeration.base.SnapshotBizModuleEnum;
import top.kx.kxss.system.entity.tenant.DefUser;
import top.kx.kxss.system.service.tenant.DefUserService;
import top.kx.kxss.system.vo.save.tenant.DefTenantBindEmployeeVO;
import top.kx.kxss.system.vo.save.tenant.DefTenantBindUserVO;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <p>
 * 前端控制器
 * 员工
 * </p>
 *
 * <AUTHOR>
 * @date 2021-10-18
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/baseEmployee")
@Api(value = "BaseEmployee", tags = "员工")
public class BaseEmployeeController extends SuperCacheController<BaseEmployeeService, Long, BaseEmployee, BaseEmployeeSaveVO, BaseEmployeeUpdateVO,
        BaseEmployeePageQuery, BaseEmployeeResultVO> {

    private final EchoService echoService;

    private final BaseEmployeeBiz baseEmployeeBiz;
    private final BaseEmployeeOrgRelService orgRelService;

    private final DefUserService defUserService;
    private final BaseRoleService baseRoleService;
    private final BaseEmployeeRoleRelService baseEmployeeRoleRelService;

    @Autowired
    private RabbitTemplate template;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Override
    public QueryWrap<BaseEmployee> handlerWrapper(BaseEmployee model, PageParams<BaseEmployeePageQuery> params) {
        QueryWrap<BaseEmployee> wrap = super.handlerWrapper(model, params);
        String keyword = params.getModel().getKeyword();
        if (StrUtil.isNotBlank(keyword)) {
            List<Long> list = defUserService.list(Wraps.<DefUser>lbQ()
                            .and(StrUtil.isNotBlank(keyword), wrapper -> wrapper
                                    .like(DefUser::getNickName, keyword).or()
                                    .like(DefUser::getMobile, keyword)))
                    .stream().map(DefUser::getId).collect(Collectors.toList());
            wrap.lambda().and(StrUtil.isNotBlank(keyword), wrapper -> wrapper
                    .like(BaseEmployee::getRealName, keyword)
                    .or().in(BaseEmployee::getUserId, list));
        }
        return wrap;
    }

    @Override
    public R<IPage<BaseEmployeeResultVO>> page(@RequestBody @Validated PageParams<BaseEmployeePageQuery> params) {
        params.getModel().setScope("1");
        IPage<BaseEmployeeResultVO> page = baseEmployeeBiz.findPageResultVO(params);
        handlerResult(page);
        return R.success(page);
    }


    @ApiOperation(value = "列表(分页)", notes = "列表(分页)")
    @PostMapping("/pageList")
    @WebLog("列表(分页)")
    public R<IPage<BaseEmployeeResultVO>> pageList(@RequestBody PageParams<BaseEmployeePageQuery> params) {
        params.getModel().setScope("1");
        params.getModel().setIsWebAdmin(true);
        IPage<BaseEmployeeResultVO> page = baseEmployeeBiz.findPageResultVO(params);
        handlerResult(page);
        return R.success(page);
    }




    @Override
    public R<BaseEmployeeResultVO> get(Long id) {
        return success(baseEmployeeBiz.getEmployeeUserById(id));
    }


    @Override
    public R<BaseEmployeeResultVO> getDetail(Long aLong) {
        return success(baseEmployeeBiz.getEmployeeUserById(aLong));
    }

    /**
     * 给员工分配角色
     *
     * @param employeeRoleSaveVO 参数
     * @return 新增结果
     */
    @ApiOperation(value = "给员工分配角色", notes = "给员工分配角色")
    @PostMapping("/employeeRole")
    @WebLog("给员工分配角色")
    public R<List<Long>> saveEmployeeRole(@RequestBody BaseEmployeeRoleRelSaveVO employeeRoleSaveVO) {
        return success(superService.saveEmployeeRole(employeeRoleSaveVO));
    }

    /**
     * 给员工分配角色
     *
     * @param employeeRoleSaveVO 参数
     * @return 新增结果
     */
    @ApiOperation(value = "给员工分配角色", notes = "给员工分配角色")
    @PostMapping("/employeeRoleByAdmin")
    @WebLog("给员工分配角色")
    public R<List<String>> saveEmployeeRoleByAdmin(@RequestBody BaseEmployeeNRoleRelSaveVO employeeRoleSaveVO) {
        return success(superService.saveEmployeeRoleByAdmin(employeeRoleSaveVO));
    }

    /**
     * 给员工分配组别
     *
     * @return
     */
    @ApiOperation(value = "给员工分配组别", notes = "给员工分配组别")
    @PostMapping("/batchSaveEmployeeGroup")
    @WebLog("给员工分配组别")
    public R<Boolean> batchSaveEmployeeGroup(@RequestBody @Validated EmployeeBatchGroupSaveVO batchGroupSaveVO) {
        return success(superService.batchSaveEmployeeGroup(batchGroupSaveVO));
    }


    @ApiOperation(value = "查询员工列表", notes = "查询员工列表")
    @PostMapping("/findListByUserIds")
    @WebLog("查询员工列表")
    public R<List<BaseEmployeeResultVO>> findListByUserIds(@RequestParam("userIds") List<Long> userIds) {
        List<BaseEmployee> baseEmployeeList = superService.list(Wraps.<BaseEmployee>lbQ().in(BaseEmployee::getUserId, userIds));
        return success(BeanPlusUtil.toBeanList(baseEmployeeList, BaseEmployeeResultVO.class));
    }

    @ApiOperation(value = "查询员工列表", notes = "查询员工列表")
    @PostMapping("/findListByIds")
    @WebLog("查询员工列表")
    public R<List<BaseEmployeeResultVO>> findListByIds(@RequestParam("ids") List<Long> ids) {
        List<BaseEmployee> baseEmployeeList = superService.listByIds(ids);
        return success(BeanPlusUtil.toBeanList(baseEmployeeList, BaseEmployeeResultVO.class));
    }

    @ApiOperation(value = "查询所有员工", notes = "查询所有员工")
    @GetMapping("/findAllEmployee")
    @WebLog("查询所有员工")
    public R<List<BaseEmployeeResultVO>> findAllEmployee() {
        List<BaseEmployee> baseEmployeeList = superService.list(Wraps.<BaseEmployee>lbQ());
        return success(BeanPlusUtil.toBeanList(baseEmployeeList, BaseEmployeeResultVO.class));
    }

    @ApiOperation(value = "查询经办人-助教排到最后", notes = "查询经办人-助教排到最后")
    @GetMapping("/queryOperatorList")
    @WebLog("查询经办人-助教排到最后")
    public R<List<BaseEmployeeResultVO>> queryOperatorList() {
        List<BaseEmployee> baseEmployeeList = superService.list(Wraps.<BaseEmployee>lbQ());
        BaseRole serviceRole = baseRoleService.getByKey("SERVICE_STAFF");
        if (Objects.isNull(serviceRole)) {
            return success(BeanPlusUtil.toBeanList(baseEmployeeList, BaseEmployeeResultVO.class));
        }
        // 服务角色的所有员工
        List<BaseEmployeeRoleRel> baseEmployeeRoleRelList = baseEmployeeRoleRelService.list(Wraps.<BaseEmployeeRoleRel>lbQ().eq(BaseEmployeeRoleRel::getRoleId, serviceRole.getId()));
        if (CollectionUtil.isEmpty(baseEmployeeRoleRelList)) {
            return success(BeanPlusUtil.toBeanList(baseEmployeeList, BaseEmployeeResultVO.class));
        }
        List<Long> serviceEmployeeIds = baseEmployeeRoleRelList.stream().map(BaseEmployeeRoleRel::getEmployeeId).distinct().collect(Collectors.toList());
        baseEmployeeList = baseEmployeeList.stream().sorted(Comparator.comparingInt(o -> serviceEmployeeIds.contains(o.getId()) ? 1 : 0)).collect(Collectors.toList());
        return success(BeanPlusUtil.toBeanList(baseEmployeeList, BaseEmployeeResultVO.class));
    }

    @GetMapping("/getState")
    public R<Boolean> getState(@RequestParam("employeeId") String employeeId) {
        BaseEmployee baseEmployee = superService.getById(Long.parseLong(employeeId));
        if (baseEmployee == null) {
            return success(false);
        }
        if (baseEmployee.getPositionStatus() == null
                || baseEmployee.getPositionStatus().equals(PostionStatusEnum.NOT_POS.getCode())) {
            return success(false);
        }
        return success(baseEmployee.getState());
    }

    @PostMapping("/updateUserId")
    public R<Boolean> updateUserId(@RequestParam Long sourceUserId, @RequestParam Long targetUserId) {
        return success(superService.updateUserId(sourceUserId, targetUserId));
    }

    /**
     * 查询员工的角色
     *
     * @param employeeId 员工id
     * @return 新增结果
     */
    @ApiOperation(value = "查询员工的角色")
    @GetMapping("/findEmployeeRoleByEmployeeId")
    @WebLog("查询员工的角色")
    public R<List<Long>> findEmployeeRoleByEmployeeId(@RequestParam Long employeeId) {
        return success(superService.findEmployeeRoleByEmployeeId(employeeId));
    }

    /**
     * 查询员工的角色
     *
     * @param employeeId 员工id
     * @return 新增结果
     */
    @ApiOperation(value = "查询员工的角色(新)")
    @GetMapping("/findEmployeeRoleNByEmployeeId")
    @WebLog("查询员工的角色(新)")
    public R<List<String>> findEmployeeRoleNByEmployeeId(@RequestParam Long employeeId) {
        return success(superService.findEmployeeRoleNByEmployeeId(employeeId));
    }

    @Override
    public R<BaseEmployee> handlerSave(BaseEmployeeSaveVO model) {
        FileParamVO photoIdFile = model.getPhotoIdFile();
        if (ObjectUtil.isNotNull(photoIdFile) && ObjectUtil.isNull(model.getPhotoId())) {
            model.setPhotoId(photoIdFile.getId());
        }
        if (StrUtil.isBlank(model.getUsername())) {
            model.setUsername(defUserService.genLoginAccount());
        }
        FileParamVO healthCodeIdFile = model.getHealthCodeIdFile();
        if (ObjectUtil.isNotNull(healthCodeIdFile)) {
            model.setHealthCodeId(healthCodeIdFile.getId());
        }
        if (model.getIsDiscount() == null) {
            model.setIsDiscount(false);
        }
        if (model.getIsReward() == null) {
            model.setIsReward(false);
        }
        model.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return R.success(baseEmployeeBiz.save(model));
    }

    /**
     * 自定义更新
     *
     * @param baseEmployeeUpdateVO 修改VO
     * @return 返回SUCCESS_RESPONSE, 调用默认更新, 返回其他不调用默认更新
     */
    @Override
    public R<BaseEmployee> handlerUpdate(BaseEmployeeUpdateVO baseEmployeeUpdateVO) {
        BaseEmployee byId = superService.getById(baseEmployeeUpdateVO.getId());
        ArgumentAssert.notNull(byId, "员工不存在");
        if (StrUtil.isBlank(byId.getName())) {
            baseEmployeeUpdateVO.setName(baseEmployeeUpdateVO.getRealName());
        }
        FileParamVO photoIdFile = baseEmployeeUpdateVO.getPhotoIdFile();
        if (ObjectUtil.isNotNull(photoIdFile) && ObjectUtil.isNull(baseEmployeeUpdateVO.getPhotoId())) {
            baseEmployeeUpdateVO.setPhotoId(photoIdFile.getId());
        }
        FileParamVO healthCodeIdFile = baseEmployeeUpdateVO.getHealthCodeIdFile();
        if (ObjectUtil.isNotNull(healthCodeIdFile) && ObjectUtil.isNull(baseEmployeeUpdateVO.getHealthCodeId())) {
            baseEmployeeUpdateVO.setHealthCodeId(healthCodeIdFile.getId());
        }
        if (ObjectUtil.isNotNull(byId.getUserId())) {
            defUserService.updateOne(Wraps.<DefUser>lbU()
                    .set(DefUser::getSex, baseEmployeeUpdateVO.getSex())
                    .set(DefUser::getNickName, baseEmployeeUpdateVO.getRealName())
                    .set(DefUser::getNation, baseEmployeeUpdateVO.getNation())
                    .set(DefUser::getEducation, baseEmployeeUpdateVO.getEducation())
                    .eq(DefUser::getId, byId.getUserId()));
        }
        return super.handlerUpdate(baseEmployeeUpdateVO);
    }


    @Override
    public R<Boolean> handlerDelete(List<Long> ids) {
        return R.success(baseEmployeeBiz.delete(ids));
    }

    @ApiOperation(value = "运营者将非该企业的用户，添加进企业使其成为员工并设置为租户管理员")
    @PostMapping(value = "/bindUser")
    @WebLog("运营者将非该企业的用户，添加进企业使其成为员工并设置为租户管理员")
    public R<Boolean> bindUser(@RequestBody @Validated DefTenantBindUserVO param) {
        return R.success(param.getIsBind() != null && param.getIsBind() ? baseEmployeeBiz.bindUser(param) : baseEmployeeBiz.unBindUser(param));
    }


    @ApiOperation(value = "运营者将非该企业的用户，添加进企业使其成为员工并设置为租户管理员")
    @PostMapping(value = "/bindUserByOrgId")
    @WebLog("运营者将非该企业的用户，添加进企业使其成为员工并设置为租户管理员")
    public R<Boolean> bindUserByOrgId(@RequestBody @Validated DefTenantBindEmployeeVO param) {
        return R.success(param.getIsBind() != null && param.getIsBind() ? baseEmployeeBiz.bindUserByOrgId(param) : baseEmployeeBiz.unBindUser(param));
    }


    @ApiOperation(value = "租户管理员邀请用户进入企业")
    @PostMapping(value = "/invitationUser")
    @WebLog("租户管理员邀请用户进入企业")
    public R<Boolean> invitationUser(@RequestBody @Validated DefTenantBindUserVO param) {
        return R.success(param.getIsBind() != null && param.getIsBind() ? baseEmployeeBiz.invitationUser(param) : baseEmployeeBiz.unInvitationUser(param));
    }

    @ApiOperation(value = "运营者将用户设置为某个租户的租户管理员")
    @PostMapping(value = "/bindTenantAdmin")
    @WebLog("运营者将用户设置为某个租户的租户管理员")
    public R<Boolean> bindTenantAdmin(@RequestBody @Validated DefTenantBindUserVO param) {
        return R.success(param.getIsBind() != null && param.getIsBind() ? baseEmployeeBiz.bindTenantAdmin(param) : baseEmployeeBiz.unBindTenantAdmin(param));
    }

    @OperationLog(
            module = SnapshotBizModuleEnum.EMPLOYEE,
            type = BizLogOperationTypeEnum.CREATE,
            descSplitField = "realName"
    )
    @Override
    public R<BaseEmployee> save(BaseEmployeeSaveVO baseEmployeeSaveVO) {
        R<BaseEmployee> save = super.save(baseEmployeeSaveVO);
        if (save.getData() != null) {
            template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.BIZ_CACHE,
                    JSON.toJSONString(BizCacheConsumeQuery.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId())
                            .bizCacheEnum(BizCacheEnum.BASE_EMPLOYEE)
                            .build()));
            template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.TENANT_ORG_USER,
                    JSON.toJSONString(TenantOrgUserConsumeQuery.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .employeeId(save.getData().getId())
                            .userId(save.getData().getUserId())
                            .build()));
        }
        return save;
    }

    @OperationLog(
            module = SnapshotBizModuleEnum.EMPLOYEE,
            type = BizLogOperationTypeEnum.UPDATE,
            source = {"#baseEmployeeUpdateVO.id"},
            descSplitField = "realName"
    )
    @Override
    public R<BaseEmployee> update(BaseEmployeeUpdateVO baseEmployeeUpdateVO) {
        R<BaseEmployee> update = super.update(baseEmployeeUpdateVO);
        if (update.getData() != null) {
            template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.BIZ_CACHE,
                    JSON.toJSONString(BizCacheConsumeQuery.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .orgId(ContextUtil.getCurrentCompanyId())
                            .bizCacheEnum(BizCacheEnum.BASE_EMPLOYEE)
                            .build()));
            BaseEmployee byId = superService.getById(baseEmployeeUpdateVO.getId());
            template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.TENANT_ORG_USER,
                    JSON.toJSONString(TenantOrgUserConsumeQuery.builder()
                            .tenantId(ContextUtil.getTenantId())
                            .employeeId(byId.getId())
                            .userId(byId.getUserId())
                            .build()));
            BaseEmployee data = update.getData();
            if (!data.getState()
                    || (StrUtil.isNotBlank(data.getPositionStatus())
                    && data.getPositionStatus().equals(PostionStatusEnum.NOT_POS.getCode()))) {
                DefUser defUser = defUserService.getById(byId.getUserId());
                if (defUser != null) {
                    //清除账号登录
                    template.convertAndSend(RabbitMqConstant.TOPIC_EXCHANGE, RabbitMqConstant.FORCE_LOGOUT,
                            defUser.getMobile());
                }
            }
        }
        return update;
    }


    @OperationLog(
            module = SnapshotBizModuleEnum.EMPLOYEE,
            type = BizLogOperationTypeEnum.DELETE,
            source = {"#longs"},
            descSplitField = "realName"
    )
    @Override
    public R<Boolean> delete(List<Long> longs) {
        return super.delete(longs);
    }

    @Override
    public R<List<BaseEmployeeResultVO>> query(BaseEmployeePageQuery data) {
        LbQueryWrap<BaseEmployee> wrap = Wraps.<BaseEmployee>lbQ()
                .eq(BaseEmployee::getDeleteFlag, 0)
                .eq(Objects.nonNull(data.getIsDefault()), BaseEmployee::getIsDefault, data.getIsDefault())
                .eq(Objects.nonNull(data.getUserId()), BaseEmployee::getUserId, data.getUserId())
                .in(CollUtil.isNotEmpty(data.getPositionId()), BaseEmployee::getPositionId, data.getPositionId())
                .eq(Objects.nonNull(data.getGroupId()), BaseEmployee::getGroupId, data.getGroupId())
                .inSql(CollUtil.isNotEmpty(data.getOrgIdList()), BaseEmployee::getId, " select eor.employee_id from base_employee_org_rel eor where eor.employee_id = e.id " +
                        "  and eor.delete_flag = 0 and eor.org_id in ( " + StrUtil.join(",", data.getOrgIdList()) + " )  ");
        if (StringUtils.isNotBlank(data.getPositionStatus())) {
            wrap.eq(BaseEmployee::getPositionStatus, data.getPositionStatus());
        } else {
            wrap.eq(BaseEmployee::getPositionStatus, "10");
        }
        if (StringUtils.isNotBlank(data.getActiveStatus())) {
            wrap.eq(BaseEmployee::getActiveStatus, data.getActiveStatus());
        } else {
            wrap.eq(BaseEmployee::getActiveStatus, "20");
        }
        if (StrUtil.isNotBlank(data.getKeyword())) {
            List<Long> list = defUserService.list(Wraps.<DefUser>lbQ()
                            .and(StrUtil.isNotBlank(data.getKeyword()), wrapper -> wrapper
                                    .like(DefUser::getNickName, data.getKeyword()).or()
                                    .like(DefUser::getMobile, data.getKeyword())))
                    .stream().map(DefUser::getId).collect(Collectors.toList());
            wrap.and(StrUtil.isNotBlank(data.getKeyword()), wrapper -> wrapper
                    .like(BaseEmployee::getRealName, data.getKeyword())
                    .or().in(BaseEmployee::getUserId, list));
        }
        // 只查询门店的员工
        wrap.inSql(BaseEmployee::getId,
                " select distinct eor.employee_id from base_employee_org_rel eor where eor.delete_flag = 0 and eor.org_id =  " + ContextUtil.getCurrentCompanyId());
        superService.commissionWrap(data, wrap);
        List<BaseEmployee> employeeList = superService.list(wrap);
        List<BaseEmployeeResultVO> resultVOList = BeanPlusUtil.toBeanList(employeeList, BaseEmployeeResultVO.class);
        if (CollUtil.isEmpty(resultVOList)) {
            return R.success(resultVOList);
        }
        List<Long> collect = orgRelService.list(Wraps.<BaseEmployeeOrgRel>lbQ()
                        .eq(BaseEmployeeOrgRel::getDeleteFlag, 0)
                        .eq(BaseEmployeeOrgRel::getOrgId, ContextUtil.getCurrentCompanyId())
                        .in(BaseEmployeeOrgRel::getEmployeeId,
                                resultVOList.stream().map(BaseEmployeeResultVO::getId).collect(Collectors.toList())))
                .stream().map(BaseEmployeeOrgRel::getEmployeeId).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            resultVOList.removeIf(employeeResultVO -> !collect.contains(employeeResultVO.getId()));
        }
        echoService.action(resultVOList);
        return R.success(resultVOList);
    }

    /**
     * 修改密码
     *
     * @param data 修改实体
     * @return 是否成功
     */
    @ApiOperation(value = "根据手机验证码修改手机号", notes = "根据手机验证码修改手机号")
    @PutMapping("/admin/mobileByCode")
    @WebLog("'根据手机验证码修改手机号:' + #data.id")
    public R<Boolean> adminMobileByCode(@RequestBody @Validated EmployeeUpdateMobileByCodeVO data) {
        ArgumentAssert.notNull(data.getId(), "请选择员工");
        BaseEmployee baseEmployee = superService.getById(data.getId());
        ArgumentAssert.notNull(baseEmployee, "员工不存在");
        return R.success(baseEmployeeBiz.mobileByCode(data, baseEmployee, false));
    }

    /**
     * 修改密码
     *
     * @param data 修改实体
     * @return 是否成功
     */
    @ApiOperation(value = "根据手机验证码修改手机号", notes = "根据手机验证码修改手机号")
    @PutMapping("/mobileByCode")
    @WebLog("'根据手机验证码修改手机号:' + #data.id")
    public R<Boolean> mobileByCode(@RequestBody @Validated EmployeeUpdateMobileByCodeVO data) {
        BaseEmployee baseEmployee = superService.getById(data.getId());
        ArgumentAssert.notNull(baseEmployee, "员工不存在");
        ArgumentAssert.equals(data.getId(), ContextUtil.getEmployeeId(),
                "只能修改自己的手机号");
        return R.success(baseEmployeeBiz.mobileByCode(data, baseEmployee, true));
    }


}
