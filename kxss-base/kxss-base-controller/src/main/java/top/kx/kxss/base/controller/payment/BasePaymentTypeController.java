package top.kx.kxss.base.controller.payment;

import cn.hutool.core.collection.CollUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.payment.BasePaymentType;
import top.kx.kxss.base.service.payment.BasePaymentTypeService;
import top.kx.kxss.base.vo.query.payment.BasePaymentTypePageQuery;
import top.kx.kxss.base.vo.result.payment.BasePaymentTypeResultVO;
import top.kx.kxss.base.vo.save.payment.BasePaymentTypeSaveVO;
import top.kx.kxss.base.vo.update.payment.BasePaymentTypeUpdateVO;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * 支付类型
 * </p>
 *
 * <AUTHOR>
 * @date 2023-09-19 14:43:55
 * @create [2023-09-19 14:43:55] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/basePaymentType")
@Api(value = "BasePaymentType", tags = "支付类型")
public class BasePaymentTypeController extends SuperController<BasePaymentTypeService, Long, BasePaymentType, BasePaymentTypeSaveVO,
        BasePaymentTypeUpdateVO, BasePaymentTypePageQuery, BasePaymentTypeResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Override
    public R<BasePaymentTypeResultVO> getDetail(Long aLong) {
        return success(superService.getDetail(aLong));
    }




    @Override
    public R<List<BasePaymentTypeResultVO>> query(BasePaymentTypePageQuery data) {
        BasePaymentType basePaymentType = BeanPlusUtil.toBean(data, BasePaymentType.class);
        QueryWrap<BasePaymentType> wrapper = Wraps.q(basePaymentType);
        if (CollUtil.isNotEmpty(data.getOrgIdList())) {
            wrapper.lambda().in(BasePaymentType::getCreatedOrgId, data.getOrgIdList());
        } else {
            wrapper.lambda().eq(BasePaymentType::getCreatedOrgId, ContextUtil.getCurrentCompanyId());
        }
        List<BasePaymentType> basePaymentTypeList = superService.list(wrapper);
        List<BasePaymentTypeResultVO> basePaymentTypeResultVOList = BeanPlusUtil.copyToList(basePaymentTypeList, BasePaymentTypeResultVO.class);
        echoService.action(basePaymentTypeResultVOList);
        return R.success(basePaymentTypeResultVOList);
    }

    @Override
    public QueryWrap<BasePaymentType> handlerWrapper(BasePaymentType model, PageParams<BasePaymentTypePageQuery> params) {
        model.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return super.handlerWrapper(model, params);
    }

    /**
     * 修改状态
     *
     * @param id    用户id
     * @param state 用户状态
     * @return 是否成功
     */
    @ApiOperation(value = "修改状态", notes = "修改状态")
    @PutMapping("/updateState")
    @WebLog("'修改状态:id=' + #id + ', state=' + #state")
    public R<Boolean> updateState(
            @NotNull(message = "请选择用户") @RequestParam Long id,
            @NotNull(message = "请设置正确的状态值") @RequestParam Boolean state) {
        return success(superService.updateState(id, state));
    }

    /**
     * 修改状态
     *
     * @param id    用户id
     * @param state 用户状态
     * @return 是否成功
     */
    @ApiOperation(value = "修改小程序启用状态", notes = "修改小程序启用状态")
    @PutMapping("/updateApplet")
    @WebLog("'修改小程序启用状态:id=' + #id + ', state=' + #state")
    public R<Boolean> updateApplet(
            @NotNull(message = "请选择用户") @RequestParam Long id,
            @NotNull(message = "请设置正确的状态值") @RequestParam Boolean state) {
        return success(superService.updateApplet(id, state));
    }

    @ApiOperation(value = "查询聚合相关列表", notes = "查询聚合相关列表")
    @PostMapping("/queryPolymerize")
    public R<List<BasePaymentTypeResultVO>> queryPolymerize() {
        return success(superService.queryPolymerize());
    }
}


