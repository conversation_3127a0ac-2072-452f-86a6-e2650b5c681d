package top.kx.kxss.base.controller.stock;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.entity.SuperEntity;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.LbQueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.kxss.base.entity.product.BaseProduct;
import top.kx.kxss.base.entity.stock.BaseProductStock;
import top.kx.kxss.base.service.product.BaseProductService;
import top.kx.kxss.base.service.stock.BaseProductStockService;
import top.kx.kxss.base.service.warehouse.BaseWarehouseService;
import top.kx.kxss.base.vo.query.product.BaseProductPageQuery;
import top.kx.kxss.base.vo.query.stock.BaseProductStockPageQuery;
import top.kx.kxss.base.vo.query.stock.BaseProductStockQuery;
import top.kx.kxss.base.vo.result.product.BaseProductResultVO;
import top.kx.kxss.base.vo.result.stock.BaseProductStockResultVO;
import top.kx.kxss.base.vo.result.stock.ProductStockResultVO;
import top.kx.kxss.base.vo.save.stock.BaseProductStockSaveVO;
import top.kx.kxss.base.vo.update.stock.BaseProductStockUpdateVO;
import top.kx.kxss.datascope.DataScopeHelper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 商品现存量
 * </p>
 *
 * <AUTHOR>
 * @date 2023-04-10 11:34:30
 * @create [2023-04-10 11:34:30] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseProductStock")
@Api(value = "BaseProductStock", tags = "库存信息")
public class BaseProductStockController extends SuperController<BaseProductStockService, Long, BaseProductStock, BaseProductStockSaveVO,
        BaseProductStockUpdateVO, BaseProductStockPageQuery, BaseProductStockResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Autowired
    private BaseProductService baseProductService;
    @Autowired
    private BaseWarehouseService baseWarehouseService;

    @Override
    public void handlerResult(IPage<BaseProductStockResultVO> page) {
//        List<BaseProductStockResultVO> records = page.getRecords();
//        List<Long> productIds = records.stream().map(BaseProductStockResultVO::getProductId).collect(Collectors.toList());
//        List<Long> warehouseIds = records.stream().map(BaseProductStockResultVO::getWarehouseId).collect(Collectors.toList());
//        Map<Long, BaseProduct> productMap = CollUtil.isNotEmpty(productIds) ? baseProductService.list(Wraps.<BaseProduct>lbQ().in(BaseProduct::getId, productIds))
//                .stream().collect(Collectors.toMap(BaseProduct::getId, k -> k)) : new HashMap<>();
//        Map<Long, BaseWarehouse> warehouseMap = CollUtil.isNotEmpty(warehouseIds) ? baseWarehouseService.list(Wraps.<BaseWarehouse>lbQ().in(BaseWarehouse::getId, warehouseIds))
//                .stream().collect(Collectors.toMap(BaseWarehouse::getId, k -> k)) : new HashMap<>();
//        for (BaseProductStockResultVO record : records) {
//            BaseProduct product = productMap.get(record.getProductId());
//            if (ObjectUtil.isNotNull(product)) {
//                record.setProductVO(BeanUtil.copyProperties(product, BaseProductResultVO.class));
//            }
//            BaseWarehouse baseWarehouse = warehouseMap.get(record.getWarehouseId());
//            if (ObjectUtil.isNotNull(baseWarehouse)) {
//                record.setWarehouseVO(BeanUtil.copyProperties(baseWarehouse, BaseWarehouseResultVO.class));
//            }
//        }
        super.handlerResult(page);
    }


    @ApiOperation(value = "商品库存列表", notes = "商品库存列表")
    @PostMapping("/stockList")
    public R<List<ProductStockResultVO>> productStockList(@RequestBody @Validated BaseProductStockQuery params) {
        List<BaseProductStock> productStockList = superService.list(Wraps.<BaseProductStock>lbQ().eq(SuperEntity::getDeleteFlag, 0)
                .eq(BaseProductStock::getWarehouseId, params.getWarehouseId())
                .in(BaseProductStock::getProductId, params.getProductIds()));
        List<ProductStockResultVO> resultVOS = BeanPlusUtil.toBeanList(productStockList, ProductStockResultVO.class);
        if (CollUtil.isEmpty(resultVOS)) {
            resultVOS = CollUtil.newArrayList();
            for (Long productId : params.getProductIds()) {
                resultVOS.add(ProductStockResultVO.builder().productId(productId).warehouseId(params.getWarehouseId()).num(0).lockNum(0).surplusNum(0).build());
            }
            return R.success(resultVOS);
        }
        // 转 map
        Map<Long, ProductStockResultVO> productStockMap = resultVOS.stream().collect(Collectors.toMap(ProductStockResultVO::getProductId, k -> k));
        // 遍历所有商品id
        for (Long productId : params.getProductIds()) {
            if (Objects.isNull(productStockMap.get(productId))) {
                resultVOS.add(ProductStockResultVO.builder().productId(productId).warehouseId(params.getWarehouseId()).num(0).lockNum(0).surplusNum(0).build());
            }
        }
        resultVOS.forEach(s -> {
            s.setSurplusNum(s.getNum() - (ObjectUtil.isNull(s.getLockNum()) ? 0 : s.getLockNum()));
        });
        return R.success(resultVOS);
    }

    @ApiOperation(value = "分页查询商品库存信息", notes = "分页查询商品库存信息")
    @PostMapping("/pageProductStock")
    @WebLog(value = "'分页查询商品库存信息:第' + #params?.current + '页, 显示' + #params?.size + '行'", response = false)
    public R<IPage<BaseProductStockResultVO>> pageStock(@RequestBody PageParams<BaseProductPageQuery> params) {
        IPage<BaseProduct> page = params.buildPage(BaseProduct.class);
        LbQueryWrap<BaseProduct> wrap = Wraps.<BaseProduct>lbQ().eq(BaseProduct::getCreatedOrgId, ContextUtil.getCurrentCompanyId());
        wrap.eq(ObjectUtil.isNotNull(params.getModel().getCategoryId()), BaseProduct::getCategoryId, params.getModel().getCategoryId());
        wrap.like(StrUtil.isNotBlank(params.getModel().getName()), BaseProduct::getName, params.getModel().getName());
        DataScopeHelper.startDataScope("base_product");
        baseProductService.page(page, wrap);
        IPage<BaseProductResultVO> voPage = BeanPlusUtil.toBeanPage(page, BaseProductResultVO.class);
        echoService.action(voPage);
        if (Objects.isNull(params.getModel()) || params.getModel().getWarehouseId() == null) {
            params.getModel().setWarehouseId(baseWarehouseService.getOneBySn().getId());
        }
        List<BaseProductResultVO> records = voPage.getRecords();
        List<Long> productIds = records.stream().map(BaseProductResultVO::getId).collect(Collectors.toList());
        Map<Long, BaseProductStock> productMap = CollUtil.isNotEmpty(productIds) ? superService.list(Wraps.<BaseProductStock>lbQ()
                        .eq(BaseProductStock::getDeleteFlag, 0)
                        .eq(BaseProductStock::getWarehouseId, params.getModel().getWarehouseId())
                        .in(BaseProductStock::getProductId, productIds))
                .stream().collect(Collectors.toMap(BaseProductStock::getProductId, k -> k, (oldValue, newValue) -> newValue)) : new HashMap<>();

        if (CollUtil.isEmpty(records)) {
            return R.success(new Page<>(params.getCurrent(), params.getSize()));
        }
        IPage<BaseProductStock> stockIPage = params.buildPage(BaseProductStock.class);
        IPage<BaseProductStockResultVO> voiPage = BeanPlusUtil.toBeanPage(stockIPage, BaseProductStockResultVO.class);
        List<BaseProductStockResultVO> resultVOS = records.stream().map(record -> {
            BaseProductStock baseProductStock = productMap.get(record.getId());
            BaseProductStockResultVO vo = BeanUtil.copyProperties(record, BaseProductStockResultVO.class);
            vo.setProductId(record.getId());
            vo.setNum(ObjectUtil.isNotNull(baseProductStock) ? baseProductStock.getNum() : 0);
            vo.setId(ObjectUtil.isNotNull(baseProductStock) ? baseProductStock.getId() : null);
            vo.setBatchCode(ObjectUtil.isNotNull(baseProductStock) ? baseProductStock.getBatchCode() : null);
            vo.setWarehouseId(ObjectUtil.isNotNull(baseProductStock) ? baseProductStock.getWarehouseId() : null);
            return vo;
        }).collect(Collectors.toList());
        voiPage.setRecords(resultVOS);
        super.handlerResult(voiPage);
        return R.success(voiPage);
    }
}


