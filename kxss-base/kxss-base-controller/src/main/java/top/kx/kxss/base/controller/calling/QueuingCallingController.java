package top.kx.kxss.base.controller.calling;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.base.entity.calling.QueuingCalling;
import top.kx.kxss.base.service.calling.QueuingCallingService;
import top.kx.kxss.base.vo.query.calling.QueuingCallingPageQuery;
import top.kx.kxss.base.vo.result.calling.QueuingCallingResultVO;
import top.kx.kxss.base.vo.save.calling.QueuingCallingSaveVO;
import top.kx.kxss.base.vo.update.calling.QueuingCallingUpdateVO;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * 排队叫号
 * </p>
 *
 * <AUTHOR>
 * @date 2024-12-03 19:22:05
 * @create [2024-12-03 19:22:05] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/queuingCalling")
@Api(value = "QueuingCalling", tags = "排队叫号")
public class QueuingCallingController extends SuperController<QueuingCallingService, Long, QueuingCalling, QueuingCallingSaveVO,
        QueuingCallingUpdateVO, QueuingCallingPageQuery, QueuingCallingResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Override
    public QueryWrap<QueuingCalling> handlerWrapper(QueuingCalling model, PageParams<QueuingCallingPageQuery> params) {
        model.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        QueryWrap<QueuingCalling> queryWrap = super.handlerWrapper(model, params);
        queryWrap.eq("delete_flag", 0);
        queryWrap.in(CollUtil.isNotEmpty(params.getModel().getStatusList()),
                "status", params.getModel().getStatusList());
        if (CollUtil.isNotEmpty(params.getModel().getTagList())) {
            StringBuilder sb = new StringBuilder();
            sb.append("JSON_CONTAINS(tags,JSON_ARRAY('");
            for (int i = 0; i < params.getModel().getTagList().size(); i++) {
                if (i != 0) {
                    sb.append("','");
                }
                sb.append(params.getModel().getTagList().get(i));
            }
            sb.append("'))");
            queryWrap.lambda().apply(sb.toString());
        }
        return queryWrap;
    }

    @Override
    public IPage<QueuingCalling> query(PageParams<QueuingCallingPageQuery> params) {
        params.setSort("createdTime");
        params.setOrder("ascending");
        return super.query(params);
    }

    @Override
    public R<List<QueuingCallingResultVO>> query(QueuingCallingPageQuery data) {
        data.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return super.query(data);
    }


    @Override
    public void handlerResult(IPage<QueuingCallingResultVO> page) {
        for (QueuingCallingResultVO record : page.getRecords()) {
            List<String> tags = Lists.newArrayList();
            for (String tag : record.getTags()) {
                tags.add(tag.replaceAll("\"", ""));
            }
            record.setTags(tags);
            superService.waitingDuration(record);
        }
        super.handlerResult(page);
    }

    @Override
    public R<QueuingCalling> handlerSave(QueuingCallingSaveVO model) {
        model.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        model.setStatus("CREATED");
        model.setPrefix("A");
        QueuingCalling queuingCalling = superService.getOne(Wraps.<QueuingCalling>lbQ()
                .eq(QueuingCalling::getDeleteFlag, 0)
                .eq(QueuingCalling::getCreatedOrgId, model.getCreatedOrgId())
                .between(QueuingCalling::getCreatedTime, DateUtils.getStartTime(LocalDate.now().toString()),
                        DateUtils.getEndTime(LocalDate.now().toString()))
                .orderByDesc(QueuingCalling::getCreatedTime).last("limit 1")
        );
        if (queuingCalling != null) {
            int i = Integer.parseInt(queuingCalling.getSuffix());
            if (i == 999) {
                i = 0;
            }
            model.setSuffix(String.format("%03d", i + 1));
        } else {
            model.setSuffix("001");
        }
        model.setSuffix(getSuffix(model.getPrefix(), model.getSuffix()));
        model.setEmployeeId(ContextUtil.getEmployeeId());
        List<String> tags = Lists.newArrayList();
        for (String tag : model.getTags()) {
            tag = "\"" + tag.concat("\"");
            tags.add(tag);
        }
        model.setTags(tags);
        return super.handlerSave(model);
    }

    private String getSuffix(String prefix, String suffix) {
        int i = Integer.parseInt(suffix);
        if (i == 999) {
            i = 0;
        }
        if (superService.checkName(prefix, suffix, null)) {
            return getSuffix(prefix, String.format("%03d", i + 1));
        }
        return suffix;
    }

    @ApiOperation(value = "叫号", notes = "叫号")
    @PostMapping("/calling")
    @WebLog("叫号")
    public R<Boolean> calling(@RequestParam("id") Long id) {
        return R.success(superService.calling(id));
    }

    @ApiOperation(value = "数量", notes = "数量")
    @PostMapping("/count")
    @WebLog("数量")
    public R<Long> count(@RequestParam(required = false) Long id) {
        return R.success(superService.count(id));
    }

    @ApiOperation(value = "查询列表", notes = "查询列表")
    @PostMapping("/selectList")
    @WebLog("查询列表")
    public R<List<QueuingCallingResultVO>> selectList(@RequestBody QueuingCallingPageQuery query) {
        return R.success(superService.selectList(query));
    }

    @ApiOperation(value = "过号", notes = "过号")
    @PostMapping("/passing")
    @WebLog("过号")
    public R<Boolean> passing(@RequestParam("id") Long id) {
        return R.success(superService.passing(id));
    }

    @ApiOperation(value = "入场", notes = "入场")
    @PostMapping("/admission")
    @WebLog("入场")
    public R<Boolean> admission(@RequestParam("id") Long id) {
        return R.success(superService.admission(id));
    }

    @ApiOperation(value = "标签数量", notes = "标签数量")
    @PostMapping("/tagCount")
    @WebLog("标签数量")
    public R<JSONObject> tagCount() {
        return R.success(superService.tagCount());
    }
}


