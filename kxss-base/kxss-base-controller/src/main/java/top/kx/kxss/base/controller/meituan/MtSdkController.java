package top.kx.kxss.base.controller.meituan;

import com.meituan.sdk.MeituanResponse;
import com.meituan.sdk.model.ddzh.tuangou.tuangouDealQueryshopdeal.TuangouDealQueryshopdealRequest;
import com.meituan.sdk.model.ddzh.tuangou.tuangouDealQueryshopdeal.TuangouDealQueryshopdealResponse;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptConsume.TuangouReceiptConsumeRequest;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptConsume.TuangouReceiptConsumeResponse;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptGetconsumed.TuangouReceiptGetconsumedRequest;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptGetconsumed.TuangouReceiptGetconsumedResponse;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptPrepare.TuangouReceiptPrepareRequest;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptPrepare.TuangouReceiptPrepareResponse;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptReverseconsume.TuangouReceiptReverseconsumeRequest;
import com.meituan.sdk.model.ddzh.tuangou.tuangouReceiptReverseconsume.TuangouReceiptReverseconsumeResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.annotation.log.WebLog;
import top.kx.kxss.base.service.northstar.logical.MtSdkService;
import top.kx.kxss.base.vo.northstar.response.mt.MtResponse;

/**
 * 前端控制器
 * 美团-相关API
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/mt/sdk")
@Api(value = "/base/mt", tags = "美团-验劵相关API")
public class MtSdkController {

    @Autowired
    private MtSdkService mtSdkService;


    @ApiOperation(value = "查询团购劵码信息", notes = "查询团购劵码信息")
    @PostMapping("/prepare/{appAuthToken}")
    @WebLog("校验团购劵码")
    public MeituanResponse<TuangouReceiptPrepareResponse> prepare(@RequestBody TuangouReceiptPrepareRequest request,
                                                                  @PathVariable("appAuthToken") String appAuthToken) {
        return mtSdkService.prepare(request, appAuthToken);
    }

    @ApiOperation(value = "验劵", notes = "验劵")
    @PostMapping("/consume/{appAuthToken}")
    @WebLog("验劵")
    public MeituanResponse<TuangouReceiptConsumeResponse> consume(@RequestBody TuangouReceiptConsumeRequest request,
                                                                  @PathVariable("appAuthToken") String appAuthToken) {
        return mtSdkService.consume(request, appAuthToken);
    }

    @ApiOperation(value = "刷新token", notes = "刷新token")
    @PostMapping("/refreshToken")
    @WebLog("刷新token")
    public MtResponse refreshToken(@RequestParam String refreshToken) {
        return mtSdkService.refreshToken(refreshToken);
    }

    @ApiOperation(value = "撤销验劵", notes = "撤销验劵")
    @PostMapping("/reverseConsume/{appAuthToken}")
    @WebLog("撤销验劵")
    public MeituanResponse<TuangouReceiptReverseconsumeResponse> reverseConsume(@RequestBody TuangouReceiptReverseconsumeRequest request,
                                                                                @PathVariable("appAuthToken") String appAuthToken) {
        return mtSdkService.reverseConsume(request, appAuthToken);
    }

    @ApiOperation(value = "获取验券状态", notes = "获取验券状态")
    @PostMapping("/getConsumed/{appAuthToken}")
    @WebLog("获取验券状态")
    public MeituanResponse<TuangouReceiptGetconsumedResponse> getConsumed(@RequestBody TuangouReceiptGetconsumedRequest request,
                                                                          @PathVariable("appAuthToken") String appAuthToken) {
        return mtSdkService.getConsumed(request, appAuthToken);
    }

    @ApiOperation(value = "授权地址", notes = "授权地址")
    @GetMapping("/authUrl")
    @WebLog("授权地址")
    public String authUrl(Long storeId) {
        return mtSdkService.authUrl(storeId);
    }

    @ApiOperation(value = "获取门店套餐信息", notes = "获取门店套餐信息")
    @PostMapping("/selectGroupList/{appAuthToken}")
    MeituanResponse<TuangouDealQueryshopdealResponse> selectGroupList(@RequestBody TuangouDealQueryshopdealRequest request,
                                                                      @PathVariable("appAuthToken") String appAuthToken) {
        return mtSdkService.selectGroupList(request, appAuthToken);
    }


}


