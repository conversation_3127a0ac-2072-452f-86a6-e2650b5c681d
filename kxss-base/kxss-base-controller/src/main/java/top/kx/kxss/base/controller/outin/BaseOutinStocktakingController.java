package top.kx.kxss.base.controller.outin;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.base.entity.outin.BaseOutinApproval;
import top.kx.kxss.base.entity.outin.BaseOutinStocktaking;
import top.kx.kxss.base.service.outin.BaseOutinApprovalService;
import top.kx.kxss.base.service.outin.BaseOutinService;
import top.kx.kxss.base.service.outin.BaseOutinStocktakingService;
import top.kx.kxss.base.vo.query.outin.BaseOutinStocktakingPageQuery;
import top.kx.kxss.base.vo.result.outin.BaseOutinApprovalResultVO;
import top.kx.kxss.base.vo.result.outin.BaseOutinStocktakingExportResultVO;
import top.kx.kxss.base.vo.result.outin.BaseOutinStocktakingResultVO;
import top.kx.kxss.base.vo.result.product.ProductAttributeResultVO;
import top.kx.kxss.base.vo.save.outin.BaseOutinStocktakingSaveVO;
import top.kx.kxss.base.vo.update.outin.BaseOutinStocktakingUpdateVO;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 商品盘点单
 * </p>
 *
 * <AUTHOR>
 * @date 2024-02-22 14:29:30
 * @create [2024-02-22 14:29:30] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseOutinStocktaking")
@Api(value = "BaseOutinStocktaking", tags = "商品盘点单")
public class BaseOutinStocktakingController extends SuperController<BaseOutinStocktakingService, Long, BaseOutinStocktaking, BaseOutinStocktakingSaveVO,
        BaseOutinStocktakingUpdateVO, BaseOutinStocktakingPageQuery, BaseOutinStocktakingResultVO> {
    private final EchoService echoService;

    @Autowired
    private BaseOutinApprovalService baseOutinApprovalService;

    @Autowired
    private BaseOutinService baseOutinService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Override
    public R<BaseOutinStocktakingResultVO> getDetail(Long aLong) {
        return success(superService.getDetail(aLong));
    }

    @Override
    public QueryWrap<BaseOutinStocktaking> handlerWrapper(BaseOutinStocktaking model, PageParams<BaseOutinStocktakingPageQuery> params) {
        params.setOrder("");
        params.setSort("");
        QueryWrap<BaseOutinStocktaking> queryWrap = new QueryWrap<>();
        queryWrap.lambda().eq(BaseOutinStocktaking::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(BaseOutinStocktaking::getDeleteFlag, 0)
                .eq(Objects.nonNull(params.getModel().getWarehouseId()), BaseOutinStocktaking::getWarehouseId, model.getWarehouseId());
        if (params.getExtra().containsKey("createdTime_st")) {
            String createdTimeEd = params.getExtra().get("createdTime_st").toString();
            queryWrap.lambda().ge(BaseOutinStocktaking::getCreatedTime, DateUtils.getStartTime(createdTimeEd));
        }
        if (params.getExtra().containsKey("billDate_st")) {
            String createdTimeEd = params.getExtra().get("billDate_st").toString();
            queryWrap.lambda().ge(BaseOutinStocktaking::getBillDate, DateUtils.getStartTime(createdTimeEd));
        }
        if (params.getExtra().containsKey("billDate_ed")) {
            String createdTimeEd = params.getExtra().get("billDate_ed").toString();
            queryWrap.lambda().le(BaseOutinStocktaking::getBillDate, DateUtils.getEndTime(createdTimeEd));
        }
        if (params.getExtra().containsKey("createdTime_ed")) {
            String createdTimeEd = params.getExtra().get("createdTime_ed").toString();
            queryWrap.lambda().le(BaseOutinStocktaking::getCreatedTime, DateUtils.getEndTime(createdTimeEd));
        }
        if (Objects.nonNull(params.getModel().getState())) {
            queryWrap.lambda().eq(BaseOutinStocktaking::getState, params.getModel().getState());
        }
        if (params.getExtra().containsKey("state") && Objects.nonNull(params.getExtra().get("state")) && StringUtils.isNotBlank(params.getExtra().get("state").toString())) {
            Integer state = Integer.parseInt(params.getExtra().get("state").toString());
            queryWrap.lambda().eq(BaseOutinStocktaking::getState, state);
        }
        queryWrap.lambda().orderByDesc(BaseOutinStocktaking::getCreatedTime);
        return queryWrap;
    }

    @Override
    public R<IPage<BaseOutinStocktakingResultVO>> page(PageParams<BaseOutinStocktakingPageQuery> params) {
        params.setSort("");
        params.setOrder("");
        return super.page(params);
    }

    @Override
    public R<List<BaseOutinStocktakingResultVO>> query(BaseOutinStocktakingPageQuery data) {
        data.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return super.query(data);
    }


    @ApiOperation(value = "商品盘点-导出", notes = "商品盘点-导出")
    @RequestMapping(value = "/detail/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void export(@RequestParam Long id, HttpServletResponse response) {
        List<BaseOutinStocktakingExportResultVO> list = superService.detailExport(id);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=STOCKTAKING.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, BaseOutinStocktakingExportResultVO.class)
                    .sheet("盘点")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }


    @Override
    public void handlerResult(IPage<BaseOutinStocktakingResultVO> page) {

        List<BaseOutinStocktakingResultVO> records = page.getRecords();

        List<Long> outinIds = records.stream().map(BaseOutinStocktakingResultVO::getId).collect(Collectors.toList());
        // 将数据填充到VO中
        Map<Long, List<BaseOutinApprovalResultVO>> approvals = new HashMap<>();
        if (CollUtil.isNotEmpty(outinIds)) {
            List<BaseOutinApproval> approvalList = baseOutinApprovalService.list(Wraps.<BaseOutinApproval>lbQ()
                    .in(BaseOutinApproval::getBizId, outinIds));
            List<BaseOutinApprovalResultVO> beanList = BeanPlusUtil.toBeanList(approvalList, BaseOutinApprovalResultVO.class);
            approvals = beanList.stream().collect(Collectors.groupingBy(BaseOutinApprovalResultVO::getBizId));
        }
        for (BaseOutinStocktakingResultVO record : records) {
            if (CollUtil.isNotEmpty(approvals)) {
                record.setApprovalList(approvals.get(record.getId()));
                BaseOutinApprovalResultVO baseOutinApprovalResultVO = approvals.containsKey(record.getId()) ? approvals.get(record.getId()).stream().filter(s -> Objects.equals(s.getState(), 1)).findFirst().orElse(null) : null;
                if (Objects.nonNull(baseOutinApprovalResultVO)) {
                    record.setApprovalTime(baseOutinApprovalResultVO.getCreatedTime());
                }
            }
            // 盘点 type 默认传 1
            record.setType("1");
        }
        echoService.action(page);
        super.handlerResult(page);
    }

    @Override
    public R<Boolean> delete(List<Long> longs) {
        List<BaseOutinStocktaking> list = superService.list(Wraps.<BaseOutinStocktaking>lbQ().in(BaseOutinStocktaking::getId, longs));
        if (CollUtil.isEmpty(list)) {
            return R.fail("数据不存在");
        }
        ArgumentAssert.isTrue(list.stream().allMatch(s-> Objects.equals(s.getState(), 0)), "只有待审核的单据才能删除");
        return super.delete(longs);
    }
}


