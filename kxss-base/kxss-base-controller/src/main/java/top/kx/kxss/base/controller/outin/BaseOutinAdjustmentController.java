package top.kx.kxss.base.controller.outin;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.Wraps;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.basic.utils.ArgumentAssert;
import top.kx.basic.utils.BeanPlusUtil;
import top.kx.basic.utils.DateUtils;
import top.kx.kxss.base.entity.outin.BaseOutinAdjustment;
import top.kx.kxss.base.entity.outin.BaseOutinApproval;
import top.kx.kxss.base.service.outin.BaseOutinAdjustmentService;
import top.kx.kxss.base.service.outin.BaseOutinApprovalService;
import top.kx.kxss.base.vo.query.outin.BaseOutinAdjustmentPageQuery;
import top.kx.kxss.base.vo.result.outin.BaseOutinAdjustmentExportResultVO;
import top.kx.kxss.base.vo.result.outin.BaseOutinAdjustmentResultVO;
import top.kx.kxss.base.vo.result.outin.BaseOutinApprovalResultVO;
import top.kx.kxss.base.vo.result.outin.BaseOutinStocktakingExportResultVO;
import top.kx.kxss.base.vo.save.outin.BaseOutinAdjustmentSaveVO;
import top.kx.kxss.base.vo.update.outin.BaseOutinAdjustmentUpdateVO;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * 商品调库主表
 * </p>
 *
 * <AUTHOR>
 * @date 2024-09-05 10:29:44
 * @create [2024-09-05 10:29:44] [yan] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/baseOutinAdjustment")
@Api(value = "BaseOutinAdjustment", tags = "商品调库主表")
public class BaseOutinAdjustmentController extends SuperController<BaseOutinAdjustmentService, Long, BaseOutinAdjustment, BaseOutinAdjustmentSaveVO,
    BaseOutinAdjustmentUpdateVO, BaseOutinAdjustmentPageQuery, BaseOutinAdjustmentResultVO> {
    private final EchoService echoService;
    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Autowired
    private BaseOutinApprovalService baseOutinApprovalService;


    @Override
    public R<BaseOutinAdjustmentResultVO> getDetail(Long aLong) {
        return R.success(superService.getDetail(aLong));
    }

    @Override
    public QueryWrap<BaseOutinAdjustment> handlerWrapper(BaseOutinAdjustment model, PageParams<BaseOutinAdjustmentPageQuery> params) {
        params.setOrder("");
        params.setSort("");
        QueryWrap<BaseOutinAdjustment> queryWrap = new QueryWrap<>();
        queryWrap.lambda().eq(BaseOutinAdjustment::getCreatedOrgId, ContextUtil.getCurrentCompanyId())
                .eq(Objects.nonNull(params.getModel().getOutWarehouseId()), BaseOutinAdjustment::getOutWarehouseId, params.getModel().getOutWarehouseId())
                .eq(Objects.nonNull(params.getModel().getInWarehouseId()), BaseOutinAdjustment::getInWarehouseId, params.getModel().getInWarehouseId())
                .eq(Objects.nonNull(params.getModel().getBillDate()), BaseOutinAdjustment::getBillDate, params.getModel().getBillDate());
        if (params.getExtra().containsKey("createdTime_st")) {
            String createdTimeEd = params.getExtra().get("createdTime_st").toString();
            queryWrap.lambda().ge(BaseOutinAdjustment::getCreatedTime, DateUtils.getStartTime(createdTimeEd));
        }
        if (params.getExtra().containsKey("createdTime_ed")) {
            String createdTimeEd = params.getExtra().get("createdTime_ed").toString();
            queryWrap.lambda().le(BaseOutinAdjustment::getCreatedTime, DateUtils.getEndTime(createdTimeEd));
        }
        if (Objects.nonNull(params.getModel().getState())) {
            queryWrap.lambda().eq(BaseOutinAdjustment::getState, params.getModel().getState());
        }
        if (params.getExtra().containsKey("state") && Objects.nonNull(params.getExtra().get("state")) && StringUtils.isNotBlank(params.getExtra().get("state").toString())) {
            Integer state = Integer.parseInt(params.getExtra().get("state").toString());
            queryWrap.lambda().eq(BaseOutinAdjustment::getState, state);
        }
        queryWrap.lambda().orderByDesc(BaseOutinAdjustment::getCreatedTime);
        return queryWrap;
    }

    @Override
    public R<List<BaseOutinAdjustmentResultVO>> query(BaseOutinAdjustmentPageQuery data) {
        data.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return super.query(data);
    }

    @Override
    public R<IPage<BaseOutinAdjustmentResultVO>> page(PageParams<BaseOutinAdjustmentPageQuery> params) {
        params.setSort("");
        params.setOrder("");
        return super.page(params);
    }



    @ApiOperation(value = "商品调库-导出", notes = "商品调库-导出")
    @RequestMapping(value = "/detail/export", method = RequestMethod.POST, produces = "application/octet-stream")
    public void export(@RequestParam Long id, HttpServletResponse response) {
        List<BaseOutinAdjustmentExportResultVO> list = superService.detailExport(id);
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf8");
        response.setHeader("Content-disposition", "attachment;filename=ADJUSTMENT.xlsx");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            EasyExcel.write(outputStream, BaseOutinAdjustmentExportResultVO.class)
                    .sheet("调库")
                    .doWrite(list);
        } catch (IOException e) {
            log.error("导出失败", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public R<Boolean> delete(List<Long> longs) {
        List<BaseOutinAdjustment> list = superService.list(Wraps.<BaseOutinAdjustment>lbQ().in(BaseOutinAdjustment::getId, longs));
        if (CollUtil.isEmpty(list)) {
            return fail("数据不存在, 无需删除");
        }
        ArgumentAssert.isTrue(list.stream().allMatch(s-> Objects.equals(s.getState(), 0)), "只有待审核的单据才能删除");
        return super.delete(longs);
    }

    @Override
    public void handlerResult(IPage<BaseOutinAdjustmentResultVO> page) {
        List<BaseOutinAdjustmentResultVO> records = page.getRecords();

        List<Long> outinIds = records.stream().map(BaseOutinAdjustmentResultVO::getId).collect(Collectors.toList());
        // 将数据填充到VO中
        Map<Long, List<BaseOutinApprovalResultVO>> approvals = new HashMap<>();
        if (CollUtil.isNotEmpty(outinIds)) {
            List<BaseOutinApproval> approvalList = baseOutinApprovalService.list(Wraps.<BaseOutinApproval>lbQ()
                    .in(BaseOutinApproval::getBizId, outinIds));
            List<BaseOutinApprovalResultVO> beanList = BeanPlusUtil.toBeanList(approvalList, BaseOutinApprovalResultVO.class);
            approvals = beanList.stream().collect(Collectors.groupingBy(BaseOutinApprovalResultVO::getBizId));
        }
        for (BaseOutinAdjustmentResultVO record : records) {
            if (CollUtil.isNotEmpty(approvals)) {
                record.setApprovalList(approvals.get(record.getId()));
                BaseOutinApprovalResultVO baseOutinApprovalResultVO = approvals.containsKey(record.getId()) ? approvals.get(record.getId()).stream().filter(s -> Objects.equals(s.getState(), 1)).findFirst().orElse(null) : null;
                if (Objects.nonNull(baseOutinApprovalResultVO)) {
                    record.setApprovalTime(baseOutinApprovalResultVO.getCreatedTime());
                }
            }
            record.setType("9");
        }
        echoService.action(page);
        super.handlerResult(page);
    }
}


