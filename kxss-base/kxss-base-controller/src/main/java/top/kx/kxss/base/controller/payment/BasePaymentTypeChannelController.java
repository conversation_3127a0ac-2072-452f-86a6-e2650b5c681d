package top.kx.kxss.base.controller.payment;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import top.kx.basic.base.R;
import top.kx.basic.base.controller.SuperController;
import top.kx.basic.base.request.PageParams;
import top.kx.basic.context.ContextUtil;
import top.kx.basic.database.mybatis.conditions.query.QueryWrap;
import top.kx.basic.interfaces.echo.EchoService;
import top.kx.kxss.base.entity.payment.BasePaymentTypeChannel;
import top.kx.kxss.base.service.payment.BasePaymentTypeChannelService;
import top.kx.kxss.base.vo.query.payment.BasePaymentTypeChannelPageQuery;
import top.kx.kxss.base.vo.result.payment.BaseMchAppResultVO;
import top.kx.kxss.base.vo.result.payment.BasePaymentTypeChannelResultVO;
import top.kx.kxss.base.vo.save.payment.BasePaymentTypeChannelSaveVO;
import top.kx.kxss.base.vo.update.payment.BasePaymentTypeChannelUpdateVO;
import top.kx.kxss.pay.vo.query.MchAppPageQuery;

import java.util.List;

/**
 * <p>
 * 前端控制器
 * 支付方式对应渠道
 * </p>
 *
 * <AUTHOR>
 * @date 2024-06-12 10:18:05
 * @create [2024-06-12 10:18:05] [dou] [代码生成器生成]
 */
@Slf4j
@RequiredArgsConstructor
@Validated
@RestController
@RequestMapping("/basePaymentTypeChannel")
@Api(value = "BasePaymentTypeChannel", tags = "支付方式对应渠道")
public class BasePaymentTypeChannelController extends SuperController<BasePaymentTypeChannelService, Long, BasePaymentTypeChannel, BasePaymentTypeChannelSaveVO,
        BasePaymentTypeChannelUpdateVO, BasePaymentTypeChannelPageQuery, BasePaymentTypeChannelResultVO> {
    private final EchoService echoService;

    @Override
    public EchoService getEchoService() {
        return echoService;
    }

    @Override
    public R<List<BasePaymentTypeChannelResultVO>> query(BasePaymentTypeChannelPageQuery data) {
        data.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return super.query(data);
    }

    @Override
    public QueryWrap<BasePaymentTypeChannel> handlerWrapper(BasePaymentTypeChannel model, PageParams<BasePaymentTypeChannelPageQuery> params) {
        model.setCreatedOrgId(ContextUtil.getCurrentCompanyId());
        return super.handlerWrapper(model, params);
    }

    @Override
    public R<BasePaymentTypeChannel> save(BasePaymentTypeChannelSaveVO basePaymentTypeChannelSaveVO) {
        R<BasePaymentTypeChannel> save = super.save(basePaymentTypeChannelSaveVO);
        superService.saveBankCard(save.getData());
        return save;
    }

    @Override
    public R<BasePaymentTypeChannel> update(BasePaymentTypeChannelUpdateVO basePaymentTypeChannelUpdateVO) {
        R<BasePaymentTypeChannel> update = super.update(basePaymentTypeChannelUpdateVO);
        superService.saveBankCard(update.getData());
        return update;
    }

    @ApiOperation(value = "查询商户应用相关列表", notes = "查询商户应用相关列表")
    @PostMapping("/queryMchApp")
    public R<List<BaseMchAppResultVO>> queryMchApp() {
        return success(superService.queryMchApp());
    }

    @ApiOperation(value = "查询商户应用相关列表", notes = "查询商户应用相关列表")
    @PostMapping("/queryMchAppByAppId")
    public R<List<BaseMchAppResultVO>> queryMchApp(@RequestBody MchAppPageQuery query) {
        return success(superService.queryMchApp(query));
    }


}


